import { Injectable } from '@nestjs/common';
import axios from 'axios';

import config from '@common/configs/config';

import { CloudrivesService, ShortsData, ApiError, OpenAPI } from './client';

@Injectable()
export class BlogifyMediaService {
  private api = axios.create({
    baseURL: config().internalApps.blogifyMedia.url,
    headers: {
      'x-api-key': config().internalApps.blogifyMedia.apiKey,
    },
  });

  constructor() {
    OpenAPI.HEADERS = {
      'x-api-key': config().internalApps.blogifyMedia.apiKey,
    };
    OpenAPI.BASE = config().internalApps.blogifyMedia.url;
  }

  async getDuration(url: string): Promise<number> {
    return this.api
      .get<{ duration_in_seconds: number }>(`/url/duration?url=${url}`)
      .then((resp) => resp.data)
      .then((resp) => resp.duration_in_seconds);
  }

  async createShorts(url: string, startTime: number, endTime: number): Promise<string> {
    try {
      const params: ShortsData = {
        url,
        start_time_in_seconds: startTime,
        end_time_in_seconds: endTime,
      };
      const response = await CloudrivesService.shortsMedia({ requestBody: params });
      return response.download_url;
    } catch (error) {
      if (error instanceof ApiError && error.status === 415) {
        throw new Error(error.body.error);
      } else {
        throw error;
      }
    }
  }
}
