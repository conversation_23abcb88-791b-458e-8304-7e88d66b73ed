/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/**
 * This model represents the request body expected by the shorts endpoint.
 * Attributes:
 * url(str): YouTube url to create shorts from.
 * start_time_in_seconds(NonNegativeInt): The start time of the shorts in seconds.
 * end_time_in_seconds(PositiveInt): The end time of the shorts in seconds. Must be greater than start_time_in_seconds.
 */
export type ShortsData = {
    url: string;
    start_time_in_seconds: number;
    end_time_in_seconds: number;
};

