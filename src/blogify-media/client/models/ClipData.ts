/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
/**
 * This model represents the request body expected by the clip endpoint.
 * Attributes:
 * url(HttpUrl): The url of the video or audio to be clipped.
 * start_time_in_seconds(NonNegativeInt): The start time of the clip in seconds.
 * end_time_in_seconds(PositiveInt): The end time of the clip in seconds. Must be greater than start_time_in_seconds.
 */
export type ClipData = {
    url: string;
    start_time_in_seconds: number;
    end_time_in_seconds: number;
};

