/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ClipData } from '../models/ClipData';
import type { ClipSuccess } from '../models/ClipSuccess';
import type { ShortsData } from '../models/ShortsData';
import type { Shortsuccess } from '../models/Shortsuccess';
import type { TranscodeBody } from '../models/TranscodeBody';
import type { TranscodeSuccess } from '../models/TranscodeSuccess';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class CloudrivesService {
    /**
     * Clip a video or audio url and then upload it to cloud
     * Clips a video or audio url from a given start time to an end time.
     * It is intended only to be used for creating clips no longer than a few minutes.
     * @returns ClipSuccess The media was downloaded, cut, and uploaded to cloud
     * @throws ApiError
     */
    public static clipMedia({
        requestBody,
    }: {
        requestBody: ClipData,
    }): CancelablePromise<ClipSuccess> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/url/clip',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                415: `The request could not completed successfully because the input url was not valid for this operation`,
                422: `Validation Error`,
            },
        });
    }
    /**
     * Create shorts from YouTube url then upload it to cloud
     * This endpoint takes a YouTube url and creates shorts from it.
     * @returns Shortsuccess YouTube shorts processed successfully
     * @throws ApiError
     */
    public static shortsMedia({
        requestBody,
    }: {
        requestBody: ShortsData,
    }): CancelablePromise<Shortsuccess> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/url/shorts',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                415: `The request could not completed successfully because the input url was not valid for this operation`,
                422: `Validation Error`,
            },
        });
    }
    /**
     * Convert a media to mp3 and upload to cloud storage.
     * Takes a url as a query paramater and returns a URL to a cloud storage
     * after the file has been transcoded to mp3 format.
     * Note: Only audiovisual files are supported, text based files such as
     * pdf or txt are not allowed.
     * @returns TranscodeSuccess The media was downloaded, converted to mp3 and uploaded to cloud storage successfully
     * @throws ApiError
     */
    public static transcodeMedia({
        requestBody,
    }: {
        requestBody: TranscodeBody,
    }): CancelablePromise<TranscodeSuccess> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/transcode',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                415: `The URL could not be transcoded successfully`,
                422: `Validation Error`,
            },
        });
    }
}
