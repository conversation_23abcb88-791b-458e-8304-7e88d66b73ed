export type MongoSort = Record<string, 1 | -1>;

export type MongoParams = {
  limit: number;
  skip: number;
  sort: MongoSort;
  populate: string[];
  filter: Record<string, any> & {
    $and?: Record<string, string | boolean | number>[];
    $or?: Record<string, string | boolean | number>[];
  };
};

export interface CrudQuery {
  offset: number;
  limit: number;
  page: number;
  s: string;
  sort: string[];
  join: string[];
  filter: MongoParams['filter'];
}

export interface ListResponse<T> {
  data: T[];
  total: number;
}
