import type { ListResponse, MongoParams, MongoSort, Crud<PERSON>uery } from './crud.interface';

import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

@Injectable()
export class CrudService<T> {
  private model: Model<T>;

  constructor(model: Model<T>) {
    this.model = model;
  }

  async create(createDto: any): Promise<T> {
    const createdItem = new this.model(createDto);
    return createdItem.save() as T;
  }

  async findAll(query: CrudQuery): Promise<ListResponse<T>> {
    const { populate, filter, limit, skip, sort } = this.parseQuery(query);
    const data = await this.model
      .find(filter)
      .sort(sort)
      .populate(populate)
      .limit(limit)
      .skip(skip)
      .exec();
    const total = await this.model.count(filter).exec();

    return { data, total };
  }

  async findOneById(id: string, query: CrudQuery): Promise<T> {
    const { populate, filter } = this.parseQuery(query);
    return this.model.findById(id, null, { filter }).populate(populate).exec() as T;
  }

  async update(id: string, updateDto: any): Promise<T> {
    return this.model.findByIdAndUpdate(id, updateDto).exec() as T;
  }

  async delete(id: string): Promise<{ success: boolean }> {
    await this.update(id, { deleted: true });
    return { success: true };
  }

  private parseQuery({
    limit,
    page,
    offset,
    join,
    sort: sortFromQuery = ['createdAt,DESC'],
    filter: customFilter,
    s,
  }: CrudQuery): MongoParams {
    // Sorting
    const sort = sortFromQuery.reduce((_sort: MongoSort, _s: string) => {
      const [field, order] = _s.split(',');
      _sort[field] = order === 'DESC' ? -1 : 1;
      return _sort;
    }, {} as MongoSort);

    // Filtering
    const { $and, $or } = JSON.parse(s || '{}');
    const filter: MongoParams['filter'] = {
      ...($and?.length ? { $and } : {}),
      $or: [{ deleted: false }, { deleted: null }, ...($or || [])],
      ...(customFilter || {}),
    };

    // Populate
    let populate = [];
    if (join && join.length) {
      populate = join;
    }

    // Pagination
    if (page) {
      offset = limit * (page - 1);
    }

    return {
      limit: +limit,
      skip: +offset,
      populate,
      filter,
      sort,
    };
  }
}
