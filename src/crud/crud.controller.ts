import type { ListResponse, CrudQuery } from './crud.interface';

import {
  Controller,
  UseGuards,
  Delete,
  Query,
  Param,
  Patch,
  Body,
  Post,
  Get,
} from '@nestjs/common';

import { RolesGuard, Roles } from '@auth/guards/roles.guard';
import { Auth } from '@auth/guards/auth.guard';

import { CrudService } from './crud.service';

@Controller()
export class CrudController<Resource, CreateDro, UpdateDto> {
  constructor(private readonly service: CrudService<Resource>) {}

  @Get()
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async findAll(@Query() query: CrudQuery, ..._args: any[]): Promise<ListResponse<Resource>> {
    return this.service.findAll(query);
  }

  @Get(':id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async findOne(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    ..._args: any[]
  ): Promise<Resource> {
    return this.service.findOneById(id, query);
  }

  @Post()
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async create(@Body() createDto: CreateDro, ..._args: any[]): Promise<Resource> {
    return this.service.create(createDto);
  }

  @Patch(':id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateDto,
    ..._args: any[]
  ): Promise<Resource> {
    return this.service.update(id, updateDto);
  }

  @Delete(':id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async remove(@Param('id') id: string, ..._args: any[]): Promise<{ success: boolean }> {
    return this.service.delete(id);
  }
}
