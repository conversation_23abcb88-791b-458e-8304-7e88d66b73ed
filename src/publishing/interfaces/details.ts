// بسم الله الرحمن الرحيم

import type { Platform, PlatformDetail } from '@/publishing/interfaces/platforms';

export const details = {
  blogify: {
    kind: 'blog',
    name: 'Blogify',
    website: 'https://blogify.ai/',
    description: 'Websites powered by Blogify.',
    theme: '#F2470D',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/publish%2Flogo%2FTEST_1729960777336_blogify-white.svg',
    drafts: false,
    tutorial: '/dashboard/websites/add',
    display: true,
  },
  wordpressorg: {
    kind: 'blog',
    name: 'WordPress (self-hosted)',
    website: 'https://wordpress.org/',
    description: 'Websites running on WordPress but are not hosted by WordPress.com',
    theme: '#23282D',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719888907043_wordpressorg-white.png',
    drafts: true,
    tutorial: '/dashboard/settings/wordpressorg-connect',
    display: true,
  },
  wordpress: {
    kind: 'blog',
    name: 'WordPress',
    website: 'https://wordpress.com',
    description: 'Websites hosted at WordPress.com',
    theme: '#21759B',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719888907043_wordpressorg-white.png',
    drafts: true,
    display: true,
  },
  shopify: {
    name: 'Shopify',
    website: 'https://shopify.com',
    description: 'Websites hosted at Shopify.com',
    theme: '#7AB55C',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/user%2Favatars%2F1747299045083_shopify_glyph.svg',
    drafts: true,
    kind: 'blog',
    display: false,
  },
  blogger: {
    kind: 'blog',
    name: 'Blogger',
    website: 'https://www.blogger.com',
    description: 'Websites hosted at Blogger.com',
    theme: '#FF6F00',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719889314822_blogger-white.png',
    drafts: true,
    display: true,
  },
  medium: {
    kind: 'blog',
    name: 'Medium',
    website: 'https://medium.com',
    description: 'Websites hosted at Medium.com',
    theme: '#000',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719889492207_medium-white.png',
    drafts: true,
    tutorial: '/dashboard/settings/medium-connect',
    display: false,
  },
  zapier: {
    kind: 'automation',
    name: 'Zapier',
    website: 'https://zapier.com',
    description: 'Publish via Zaps',
    theme: '#FF4F00',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719889751793_zapier-white.png',
    drafts: false,
    tutorial: '/dashboard/settings/zapier-connect',
    display: true,
  },
  mailchimp: {
    kind: 'automation',
    name: 'Mailchimp',
    website: 'https://mailchimp.com',
    description: 'Publish via Mailchimp',
    theme: '#FFE01B',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1719889842378_mailchimp-white.png',
    drafts: true,
    display: true,
  },
  /* linkedin: {
    kind: 'blog',
    name: 'LinkedIn',
    website: 'https://linkedin.com',
    description: 'Publish on LinkedIn',
    theme: '#3077B7',
    logoURL:
      'https://s3.amazonaws.com/blogifyai-images/blog%2Fcovers%2F1729658400060_blog_content-images_1729657559004_linkedin.png',
    drafts: true,
    display: false,
  } */
} as const satisfies Record<Platform, Omit<PlatformDetail, 'connected'>>;
