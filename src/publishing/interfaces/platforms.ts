// بسم الله الرحمن الرحيم

import type { Filter, PrependParameters } from '@/types/utility';
import type mongoose from 'mongoose';

export type PublishResult =
  | {
      kind: 'success';
      link?: URL;
    }
  | {
      kind: 'failure';
      error: Error;
    };

export const Platforms = <const>[
  'blogger',
  'blogify',
  'mailchimp',
  'medium',
  'shopify',
  'wordpress',
  'wordpressorg',
  'zapier',
];
export type Platform = (typeof Platforms)[number];

export type Author = Record<'id' | 'name', string>;
export type Category = Record<'id' | 'name', string>;
export type Site<
  T extends Pick<Affordability, 'author' | 'category'> = { author: false; category: false },
> = Record<'id' | 'name' | 'url', string> & {
  [A in Filter<'author' | 'category', T>]: Array<AffordanceType[A]>;
};

const affordances = {
  blogger: { site: true, author: false, category: false },
  blogify: { site: true, author: true, category: true },
  mailchimp: { site: true, author: false, category: false },
  medium: { site: true, author: false, category: false },
  shopify: { site: true, author: false, category: false },
  wordpress: { site: true, author: true, category: true },
  wordpressorg: { site: true, author: true, category: true },
  zapier: { site: false, author: false, category: false },
} as const satisfies Record<Platform, Affordability>;

type AffordanceType = { site: Site; author: Author; category: Category };
type SitePayload = { site: Site['id']; author: Author['id']; category: Array<Category['id']> };
type Affordance = keyof AffordanceType;
type Affordability = Record<Affordance, boolean>;
export const affords = (
  platform: Platform,
  affordance: Affordance,
  service: unknown,
): service is { fetchSites: (businessID: mongoose.Types.ObjectId) => Promise<Sites> } =>
  affordances[platform][affordance];

export type PlatformDetail = { kind: 'blog' | 'social' | 'automation'; tutorial?: string } & Record<
  'name' | 'website' | 'description' | 'theme' | 'logoURL',
  string
> &
  Record<'display' | 'drafts' | 'connected', boolean> & { sites?: Sites };
export type Sites = Array<
  Record<'id' | 'name' | 'url', string> & Partial<{ author: Author[]; category: Category[] }>
>;
export type PublishPayload = Article & Partial<SitePayload>;

type Article = {
  blogID: mongoose.Types.ObjectId;
  bid: mongoose.Types.ObjectId;
  title: string;
  content: string;
  image?: URL;
  tldr: string;
  draft: boolean;
  keywords: string[];
  metaTags: string[];
  metaDescription: string;
};

interface Connectivity {
  disconnect(bid: mongoose.Types.ObjectId): Promise<void>;
  isConnected(bid: mongoose.Types.ObjectId): Promise<boolean>;
}

export type PlatformService<
  P extends Platform,
  T extends Affordability = (typeof affordances)[P],
> = Connectivity & {
  publish(
    payload: Article & { [A in Filter<Affordance, T>]: SitePayload[A] },
  ): Promise<PublishResult>;
} & {
  [K in T['site'] extends true ? `fetchSites` : never]: (
    businessID: mongoose.Types.ObjectId,
  ) => Promise<Site<T>[]>;
};

export type IntegrationCategory = {
  [K in keyof Connectivity]: PrependParameters<Connectivity[K], [integration: string]>;
} & {
  publish(integration: string, payload: PublishPayload): Promise<PublishResult>;
  details(bid: mongoose.Types.ObjectId): Promise<Record<string, PlatformDetail>>;
};
