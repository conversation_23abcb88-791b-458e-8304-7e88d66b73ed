import { forwardRef, Module } from '@nestjs/common';
import { PublishingController } from './publishing.controller';
import { PublishingService } from './publishing.service';
import { PlatformsModule } from './platforms/platforms.module';
import { BlogPublishProcessor } from '@/publishing/queues/blog-publish.processor';
import { SocialPublishProcessor } from '@/publishing/queues/social-publish.processor';
import { Oauth2Module } from '@/publishing/oauth2/oauth2.module';
import { JobModule } from '@/job/job.module';
import { BlogModule } from '@/blog/blog.module';
import { LinkedinModule } from '@/publishing/platforms/linkedin/linkedin.module';
import { TwitterModule } from '@/publishing/platforms/twitter/twitter.module';

@Module({
  controllers: [PublishingController],
  providers: [PublishingService, BlogPublishProcessor, SocialPublishProcessor],
  imports: [
    PlatformsModule,
    JobModule,
    Oauth2Module,
    forwardRef(() => BlogModule),
    LinkedinModule,
    TwitterModule,
  ],
  exports: [PublishingService],
})
export class PublishingModule {}
