// بسم الله الرحمن الرحيم

import { Auth } from '@/auth/guards/auth.guard';
import { includes } from '@/common/utils/array';
import { Platforms, type PlatformDetail } from '@/publishing/interfaces/platforms';
import { JWTToken } from '@/publishing/oauth2/oauth2-admin.controller';
import { Oauth2Service } from '@/publishing/oauth2/oauth2.service';
import { PlatformsService } from '@/publishing/platforms/platforms.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  UseGuards,
} from '@nestjs/common';
import type { Cache } from 'cache-manager';
import type { RedisStore } from 'cache-manager-redis-yet';
import type mongoose from 'mongoose';

@Controller('integrations')
export class PublishingController {
  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache<RedisStore>,
    private readonly platformService: PlatformsService,
    private readonly oauth2Service: Oauth2Service,
  ) {}

  @Get()
  @UseGuards(Auth)
  async fetchIntegrations(
    @JWTToken('bid') bid: mongoose.Types.ObjectId,
  ): Promise<Record<string, PlatformDetail>> {
    const cachedValue = await this.distributedCache.get<string>(bid.toString() + 'integrations');
    if (cachedValue) return JSON.parse(cachedValue);
    const value = {
      ...(await this.platformService.details(bid)),
      ...(await this.oauth2Service.details(bid)),
    };
    await this.distributedCache.set(
      bid.toString() + 'integrations',
      JSON.stringify(value),
      5 * 1000,
    );
    return value;
  }

  @Get('socials')
  @UseGuards(Auth)
  async fetchSocialPlatform(@JWTToken('bid') bid: mongoose.Types.ObjectId) {
    return this.platformService.fetchSocialPlatforms(bid);
  }

  @Delete(':integration')
  @UseGuards(Auth)
  @HttpCode(HttpStatus.NO_CONTENT)
  async disconnectPlatform(
    @JWTToken('bid') bid: mongoose.Types.ObjectId,
    @Param('integration') integration: string,
  ) {
    if (includes(Platforms, integration)) {
      return this.platformService.disconnect(integration, bid);
    } else return this.oauth2Service.disconnect(integration, bid);
  }
}
