import { OmitType, PartialType } from '@nestjs/swagger';
import * as Permission from '@/publishing/oauth2/entities/PermissionScope';
import type { Oauth2App } from '@/publishing/oauth2/oauth2app.model';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsHexColor,
  IsString,
  IsUUID,
  IsUrl,
  Length,
  Matches,
} from 'class-validator';

const text = /^[\w\s-,!()./]*$/;
const helpText = String.raw`Please use only letters, numbers, spaces, hyphens (-), commas (,), exclamation points (!), parentheses (()), periods (.), forward slashes (/), and underscores (_).`;

export class AppDto implements Omit<Oauth2App, 'business' | 'approved'> {
  @IsUUID()
  secret: string;

  @IsString()
  @Matches(text, { message: helpText })
  @Length(1, 30)
  name: string;

  @IsUrl()
  website: string;

  @IsString()
  @Matches(text, { message: helpText })
  @Length(1, 300)
  description: string;

  @IsHexColor()
  theme: string;

  @IsString()
  @Matches(text, { message: helpText })
  @Length(1, 30)
  companyName: string;

  @IsUrl()
  companyWebsite: string;

  @IsUrl()
  documentationURL: string;

  @IsUrl()
  privacyPolicyURL: string;

  @IsUrl()
  logoURL: string;

  @IsArray()
  @ArrayMinSize(1)
  @IsUrl({}, { each: true })
  redirectUris: string[];

  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(Permission.getScopes(), { each: true })
  permissions: ReturnType<typeof Permission.getScopes>;
}
export class UpdateAppDto extends PartialType(AppDto) {}
export class CreateAppDto extends OmitType(AppDto, ['secret'] as const) {}
