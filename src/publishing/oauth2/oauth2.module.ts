import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { BusinessModule } from '@/business/business.module';

import { Oauth2AppSchema, Oauth2App } from './oauth2app.model';
import { Oauth2ScreenController } from './oauth2-screen.controller';
import { Oauth2PortalController } from './oauth2-admin.controller';
import { Oauth2Controller } from './oauth2.controller';
import { Oauth2Service } from './oauth2.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Oauth2App.name, schema: Oauth2AppSchema }]),
    BusinessModule,
  ],
  controllers: [Oauth2ScreenController, Oauth2PortalController, Oauth2Controller],
  providers: [Oauth2Service, ConfigService, JwtService],
  exports: [Oauth2Service],
})
export class Oauth2Module {}
