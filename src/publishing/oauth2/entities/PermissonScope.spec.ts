// بسم الله الرحمن الرحيم

import { Permission } from './PermissionScope';

describe('Permission Namespace', () => {

    describe('decodeFromString', () => {
        it('should decode valid string to Scope', () => {
            expect(Permission.decodeFromString('CreateBlog')).toBe(Permission.Scope.CreateBlog);
            expect(Permission.decodeFromString('UpdateBlog')).toBe(Permission.Scope.UpdateBlog);
        });

        it('should throw ParseError for invalid string', () => {
            expect(() => Permission.decodeFromString('InvalidScope')).toThrow(Permission.ParseError);
        });
    });

    describe('encodeToString', () => {
        it('should encode valid Scope to string', () => {
            expect(Permission.encodeToString(Permission.Scope.CreateBlog)).toBe('CreateBlog');
            expect(Permission.encodeToString(Permission.Scope.UpdateBlog)).toBe('UpdateBlog');
        });
    });

    describe('hasPermission', () => {
        it('should return true if permission vector includes the scope', () => {
            const permissionVector = Permission.Scope.CreateBlog | Permission.Scope.UpdateBlog;
            expect(Permission.hasPermission(Permission.Scope.CreateBlog, permissionVector)).toBe(true);
            expect(Permission.hasPermission(Permission.Scope.UpdateBlog, permissionVector)).toBe(true);
        });

        it('should return false if permission vector does not include the scope', () => {
            const permissionVector = Permission.Scope.CreateBlog;
            expect(Permission.hasPermission(Permission.Scope.UpdateBlog, permissionVector)).toBe(false);
        });
    });

    describe('verify', () => {
        it('should verify and return combined permission vector for valid scopes', () => {
            const input = ['CreateBlog', 'UpdateBlog'];
            const expected = Permission.Scope.CreateBlog | Permission.Scope.UpdateBlog;
            expect(Permission.verify(input)).toBe(expected);
        });

        it('should throw ParseError for invalid scopes', () => {
            const input = ['CreateBlog', 'InvalidScope'];
            expect(() => Permission.verify(input)).toThrow(Permission.ParseError);
        });
    });

});
