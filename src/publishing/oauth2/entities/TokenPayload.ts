// بسم الله الرحمن الرحيم

export type TokenPayload = {
  iss: string; // authorization server
  aud: string; // resource server
  sub: string; // business ID
  uid: string; // user ID
  jti: number; // token ID (randomly generated each time)
  scope: number; // permissions
  client_id: string; // OAuth2 App Client ID
  redirect_uri?: string; // OAuth2 Redirect URI (present only in Auth Code)
  type: 'authorization_code' | 'refresh_token' | 'access_token';
  iat: number; // issued at
  exp?: number; // expires at
  original: boolean; // true if access token was generated by a refresh token it is set to false otherwise true
  public: boolean; // true for public apps, otherwise false
};
