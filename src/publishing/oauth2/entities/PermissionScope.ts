// بسم الله الرحمن الرحيم

export enum Scope {
  CreateBlog = 1 << 0,
  UpdateBlog = 1 << 1,
  ReadBlog = 1 << 2,
  PublishBlog = 1 << 3,
  SubscribeBlog = 1 << 4,
  DeleteBlog = 1 << 5,
  ReadAnalytics = 1 << 6,
  ReadAffiliate = 1 << 7,
  ReadProfile = 1 << 8,
  ReadSettings = 1 << 9,
}

export function getScopes() {
  return Object.keys(Scope).filter((k) => !parseInt(k)) as Array<keyof typeof Scope>;
}

export class ParseError extends Error {}

export function decodeFromString(text: string): Scope {
  if (Scope[text]) return Scope[text];
  else throw new ParseError('failed to decode as ' + text + ' is not a valid Scope');
}

export function encodeToString(scope: Scope) {
  return Scope[scope] as keyof typeof Scope;
}

export function hasPermission(scope: Scope, permissionVector: number) {
  return (permissionVector & scope) === scope;
}

export function verify(input: Array<string>) {
  return vectorize(input.map((s: string) => decodeFromString(s)));
}

export function vectorize(scopes: Array<Scope>) {
  return scopes.reduce((a, b) => a | b, 0);
}

export class ValueError extends Error {}

export function explainScope(scope: Scope) {
  switch (scope) {
    case Scope.CreateBlog:
      return "Create new blogs by using your account's credits";
    case Scope.UpdateBlog:
      return 'Edit the blogs you have created e.g. changing the contents of the blog ';
    case Scope.ReadBlog:
      return 'See the blogs which you have created and also see associated metadata e.g. tags and categories';
    case Scope.PublishBlog:
      return 'Publish your created blogs on the the supported integrations provided by Blogify';
    case Scope.SubscribeBlog:
      return 'Receive your blog data when you create had a new blog and had instructed us to publish the blog to this application';
    case Scope.DeleteBlog:
      return 'Remove blogs you have created from your account';
    case Scope.ReadAnalytics:
      return 'See the analytics of your blogs';
    case Scope.ReadAffiliate:
      return 'See affiliate information of your blogs';
    case Scope.ReadProfile:
      return 'See profile information such as name, credits remaining and other personal data';
    case Scope.ReadSettings:
      return 'See the settings of your account';

    default:
      throw new ValueError('failed to explain ' + scope + ' is not a valid Scope');
  }
}
