// بسم الله الرحمن الرحيم

import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { randomUUID } from 'crypto';
import mongoose, { HydratedDocument } from 'mongoose';
import * as Permission from './entities/PermissionScope';

@Schema({ collection: 'oauth2-apps', timestamps: true })
export class Oauth2App {
  /** Client Secret */
  @Prop({ unique: true, required: true, default: () => randomUUID() })
  secret: string;

  /** Company Name */
  @Prop({ type: String, required: true })
  companyName: string;

  /** Company Website */
  @Prop({ type: String, required: true })
  companyWebsite: string;

  /** App name */
  @Prop({ type: String, required: true })
  name: string;

  /** App description */
  @Prop({ type: String, required: true })
  description: string;

  /** App website URL */
  @Prop({ type: String, required: true })
  website: string;

  /** App theme color */
  @Prop({ type: String, required: true })
  theme: string;

  /** App documentation link */
  @Prop({ type: String })
  documentationURL?: string;

  /** Privacy policy link */
  @Prop({ type: String, required: true })
  privacyPolicyURL: string;

  /** App logo URL */
  @Prop({ type: String, required: true })
  logoURL: string;

  /** App redirect URIs */
  @Prop({ type: Array<string>, required: true })
  redirectUris: Array<string>;

  /** App's Authoried Scopes */
  @Prop({
    required: true,
    validate: {
      validator: (array: Array<keyof typeof Permission.Scope>) =>
        array.every((v) => Permission.getScopes().includes(v)),
    },
  })
  permissions: Array<keyof typeof Permission.Scope>;

  /** Developer Business of App Creator */
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Business', required: true })
  business: mongoose.Types.ObjectId;

  /** App status */
  @Prop({ type: Boolean, required: true, default: false })
  approved: boolean;
}

export type Oauth2AppDocument = HydratedDocument<Oauth2App>;
export const Oauth2AppSchema = SchemaFactory.createForClass(Oauth2App);
