import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Oauth2AuthorizeDto } from './dto/oauth2authorize.dto';
import { Oauth2App } from './oauth2app.model';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Business } from '@business/business.model';
import { ConfigService } from '@nestjs/config';
import * as Permission from './entities/PermissionScope';
import { ObjectId } from 'mongodb';
import { TokenPayload } from './entities/TokenPayload';
import axios from 'axios';
import type {
  IntegrationCategory,
  PlatformDetail,
  PublishPayload,
  PublishResult,
} from '@/publishing/interfaces/platforms';

export class DatabaseError extends Error {}
export class JWTError extends Error {}
export class CacheError extends Error {}

@Injectable()
export class Oauth2Service implements IntegrationCategory {
  constructor(
    private readonly jwtService: JwtService,
    @InjectModel(Oauth2App.name) readonly oauth2app: Model<Oauth2App>,
    @InjectModel(Business.name) private readonly business: Model<Business>,
    private readonly configService: ConfigService,
  ) {}

  async details(bid: ObjectId): Promise<Record<string, PlatformDetail>> {
    return this.oauth2app
      .find()
      .or([{ approved: true }, /* All public apps*/ { business: bid } /* Private Apps */])
      .exec()
      .then((apps) =>
        apps.map(
          async ({ _id, name, website, theme, logoURL, documentationURL, description }) =>
            <const>[
              _id.toString(),
              {
                kind: 'automation',
                display: true,
                drafts: false,
                name,
                website,
                theme,
                logoURL,
                description,
                tutorial: documentationURL,
                connected: await this.isConnected(_id.toString(), bid),
              },
            ],
        ),
      )
      .then((ps) => Promise.all(ps))
      .then((es) => Object.fromEntries(es));
  }

  isConnected = (appID: string, bid: ObjectId) =>
    this.business
      .findById(bid)
      .then(({ oauth2AppPublishWebhooks }) => oauth2AppPublishWebhooks[appID]?.length > 0);

  disconnect = async (appID: string, bid: ObjectId) => {
    await this.business.findByIdAndUpdate(bid, {
      [`oauth2AppPublishWebhooks.${appID}`]: [],
      [`oauth2AppRefreshTokens.${appID}`]: [],
    });
  };

  publish = async (
    appID: string,
    {
      bid,
      blogID,
      content,
      title,
      keywords,
      metaDescription,
      metaTags,
      tldr,
      image,
    }: PublishPayload,
  ): Promise<PublishResult> => {
    const webhooks = (await this.business.findOne(bid))?.oauth2AppPublishWebhooks[appID];
    if (webhooks?.length > 0) {
      return Promise.all(
        webhooks.map(async (webhook) =>
          axios.post(webhook, {
            blogID,
            title,
            image,
            content,
            keywords,
            metaDescription,
            metaTags,
            tldr,
          }),
        ),
      )
        .then(() => <const>{ kind: 'success' })
        .catch((error) => ({
          error: new Error(error),
          kind: 'failure',
        }));
    }
  };

  getConsentScreenURL = () => {
    return new URL(
      'oauth2-consent',
      this.configService.getOrThrow<string>('DASHBOARD_REDIRECT_URL'),
    );
  };

  fetchBusiness = async (bid: string | ObjectId) => {
    try {
      const business = await this.business.findById(bid);
      if (!business) throw new Error('business not found');
      return business;
    } catch (unknown) {
      throw new DatabaseError('failed to get business', { cause: unknown });
    }
  };

  fetchRefreshTokens = async (bid: string, client_id: string) => {
    const { oauth2AppRefreshTokens } = await this.fetchBusiness(bid);
    const refresh_tokens = oauth2AppRefreshTokens[client_id];
    if (refresh_tokens) return refresh_tokens;
    else throw new Error('refresh token not found');
  };

  saveRefreshToken = async (bid: string, client_id: string, token: string) => {
    try {
      return await this.business.findByIdAndUpdate(
        bid,
        { $addToSet: { [`oauth2AppRefreshTokens.${client_id}`]: token } },
        { new: true },
      );
    } catch (unknown) {
      new DatabaseError('failed to add refresh token to business', { cause: unknown });
    }
  };

  updateRefreshTokens = async (bid: string, client_id: string, tokens: string[]) => {
    try {
      return await this.business.findByIdAndUpdate(
        bid,
        { $set: { [`oauth2AppRefreshTokens.${client_id}`]: tokens } },
        { new: true },
      );
    } catch (unknown) {
      throw new DatabaseError('failed to update refresh tokens of business', { cause: unknown });
    }
  };

  fetchOauth2App = async (client_id: string) => {
    try {
      const app = await this.oauth2app.findById(client_id).exec();
      if (!app) throw new Error('app not found');
      return app;
    } catch (unknown) {
      throw new DatabaseError('failed to get app from database', { cause: unknown });
    }
  };

  fetchAllOauth2Apps = async () => {
    try {
      const apps = await this.oauth2app.find().exec();
      return apps || [];
    } catch (unknown) {
      throw new DatabaseError('failed to get all apps from database', { cause: unknown });
    }
  };

  fetchOauth2Apps = async (bid: ObjectId) => {
    try {
      const apps = await this.oauth2app.find({ business: bid }).exec();
      return apps || [];
    } catch (unknown) {
      throw new DatabaseError('failed to get apps from database', { cause: unknown });
    }
  };

  encodeJWT = async (payload: Omit<TokenPayload, 'iat' | 'exp'>, expiresIn: string | false) => {
    try {
      const secret = this.configService.getOrThrow<string>('JWT_SECRET');
      return await this.jwtService.signAsync(
        payload,
        expiresIn ? { secret, expiresIn } : { secret },
      );
    } catch (unknown) {
      throw new JWTError(`failed to encode JWT: ${unknown} `, { cause: unknown });
    }
  };

  decodeJWT = async (
    token: string,
    options?: Partial<{ subject: string; audience: string; jwtid: string }>,
  ): Promise<TokenPayload> => {
    try {
      const secret = this.configService.getOrThrow<string>('JWT_SECRET');
      return await this.jwtService.verifyAsync(token, { secret, ...options });
    } catch (unknown) {
      throw new JWTError(`failed to decode JWT: ${unknown} `, { cause: unknown });
    }
  };

  generateRandomInt = () => {
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    return array[0];
  };

  generateAuthCode = async (bid: string, uid: string, body: Oauth2AuthorizeDto) => {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      iss: this.configService.getOrThrow<string>('BASE_URL'),
      aud: this.configService.getOrThrow<string>('BASE_URL'),
      sub: bid,
      uid,
      jti: this.generateRandomInt(),
      scope: Permission.verify(body.scope.split(' ')),
      client_id: body.client_id,
      redirect_uri: body.redirect_uri,
      type: 'authorization_code',
      original: true,
      public: (await this.fetchOauth2App(body.client_id)).approved,
    };
    const code = await this.encodeJWT(payload, '30s');
    return code;
  };

  generateAccessToken = async (
    data: Omit<TokenPayload, 'original' | 'type' | 'jti' | 'iat' | 'exp'>,
    is_original: boolean,
    expiresIn: string | false,
  ) => {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      iss: data.iss,
      aud: data.aud,
      sub: data.sub,
      uid: data.uid,
      jti: this.generateRandomInt(),
      scope: data.scope,
      client_id: data.client_id,
      type: 'access_token',
      original: is_original,
      public: data.public,
    };
    const token = await this.encodeJWT(payload, expiresIn);
    return token;
  };

  generateRefreshToken = async (data: Omit<TokenPayload, 'type' | 'jti'>) => {
    const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
      iss: data.iss,
      aud: data.aud,
      sub: data.sub,
      uid: data.uid,
      jti: this.generateRandomInt(),
      scope: data.scope,
      client_id: data.client_id,
      type: 'refresh_token',
      original: true,
      public: data.public,
    };
    const token = await this.encodeJWT(payload, false);
    return token;
  };
}
