import type { Response as ExpressResponse } from 'express';
import type { RedisStore } from 'cache-manager-redis-yet';
import type { Cache } from 'cache-manager';

import {
  Controller,
  UseGuards,
  Header,
  Inject,
  Query,
  Post,
  Body,
  Get,
  Req,
  Res,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { Auth } from '@/auth/guards/auth.guard';

import { ErrorDTO, Oauth2AuthorizeDto } from './dto/oauth2authorize.dto';
import { JWTError, Oauth2Service } from './oauth2.service';
import { OAuth2RefreshRequestDTO } from './dto/oauth2refresh.dto';
import { OAuth2TokenRequestDTO } from './dto/oauth2token.dto';
import { DatabaseError } from './oauth2.service';
import * as Permission from './entities/PermissionScope';

class InvalidRequest extends Error {}
class UnsupportedResponseType extends Error {}
class InvalidScope extends Error {}

class InvalidGrant extends Error {}
class UnsupportedGrantType extends Error {}
class InvalidClient extends Error {}

@Controller('oauth2')
export class Oauth2Controller {
  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache<RedisStore>,
    private readonly oauth2Service: Oauth2Service,
  ) {}

  private readonly accessTokenLifespan = '1h';
  private readonly refreshTokenLifespan = 'infinite';
  private readonly authCodeLifespan = '30s';

  decodeQueryParams = <T extends Record<string, string | number | boolean>>(
    obj: T,
  ): { [K in keyof T]: string } => {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [key, decodeURIComponent(String(value))]),
    ) as { [K in keyof T]: string };
  };

  appendSearchParams = (params: { [key: string]: string }, url: URL) => {
    url.search = new URLSearchParams({
      ...Object.fromEntries(url.searchParams.entries()),
      ...params,
    }).toString();
    return url.toString();
  };

  /**
   * This function validates the query parameters
   * @param queryParams The query parameters to validate
   * @returns The validated query parameters
   */
  validate = async (queryParams: Oauth2AuthorizeDto) => {
    const { client_id, redirect_uri, scope, response_type } = queryParams;
    const app = await this.oauth2Service.fetchOauth2App(client_id);
    if (!app.redirectUris.includes(redirect_uri)) {
      throw new InvalidRequest('unregistered redirect_uri');
    }
    if (response_type !== 'code') {
      throw new UnsupportedResponseType('only code is the valid response type');
    }
    try {
      const permissions = scope.split(' ');
      Permission.verify(permissions);
      if (new Set([...permissions, ...app.permissions]).size !== app.permissions.length) {
        // check if requested permissions are subset of allowed permissions
        throw new InvalidScope(
          permissions
            .filter((p: keyof typeof Permission.Scope) => !app.permissions.includes(p))
            .join(' ') + ' are not authorized',
        );
      }
    } catch (error) {
      if (error instanceof Permission.ParseError) throw new InvalidScope(error.message);
      else throw error;
    }
    return queryParams;
  };

  decodeRequestError = (error): [number, ErrorDTO] => {
    if (error instanceof InvalidRequest) {
      return [400, { error: 'invalid_request', error_description: error.message }];
    }
    if (error instanceof UnsupportedResponseType) {
      return [400, { error: 'unsupported_response_type', error_description: error.message }];
    }
    if (error instanceof DatabaseError) {
      return [400, { error: 'invalid_request', error_description: 'client_id is invalid' }];
    }
    if (error instanceof InvalidScope) {
      return [400, { error: 'invalid_scope', error_description: error.message }];
    }
    throw error;
  };

  /**
   * This endpoint validates the paramters of the OAuth2 request and then redirects to the consent screen
   * @param queryParams The query paramters supplied by the client
   * @returns
   */
  @Get('v1/connect')
  async connect(
    @Query() queryParams: Oauth2AuthorizeDto,
    @Res({ passthrough: true }) res: ExpressResponse,
  ) {
    try {
      const correctParams = await this.validate(this.decodeQueryParams(queryParams));
      return res.redirect(
        this.appendSearchParams(correctParams, this.oauth2Service.getConsentScreenURL()),
      );
    } catch (error) {
      const [status, body] = this.decodeRequestError(error);
      res.status(status).send(body);
    }
  }

  @Get('v1/cancel')
  @UseGuards(QueryTokenGuard, Auth)
  cancel(
    @Query('redirect_uri') redirect_uri: string,
    @Query('state') state: string,
    @Res({ passthrough: true }) res: ExpressResponse,
  ) {
    const params = {
      state,
      error: 'access_denied',
      error_description: 'user has denied your app access to his Blogify Account',
    };
    res.redirect(this.appendSearchParams(params, new URL(redirect_uri)));
  }

  @Get('v1/authorize')
  @UseGuards(QueryTokenGuard, Auth)
  async authorize(
    @Req() request: AuthenticatedRequest,
    @Query() params: Oauth2AuthorizeDto,
    @Res({ passthrough: true }) res: ExpressResponse,
  ) {
    try {
      const correctParams = await this.validate(this.decodeQueryParams(params));
      const code = await this.oauth2Service.generateAuthCode(
        request.bid,
        request.uid,
        correctParams,
      );
      await this.distributedCache.set(code, code);
      res.redirect(
        this.appendSearchParams(
          { code: code, state: params.state },
          new URL(correctParams.redirect_uri),
        ),
      );
    } catch (error) {
      const [status, body] = this.decodeRequestError(error);
      res.status(status).send(body);
    }
  }

  @Post('v1/token')
  @Header('Cache-Control', 'none')
  async token(
    @Body() params: OAuth2TokenRequestDTO,
    @Res({ passthrough: true }) res: ExpressResponse,
  ) {
    try {
      const { grant_type, client_id, client_secret, code, redirect_uri } = params;

      // Check if all query params are present
      Object.entries({ grant_type, client_id, client_secret, code, redirect_uri }).map(
        ([key, value]) => {
          if (value) return value;
          else throw new InvalidRequest(`${key} is required`);
        },
      );

      if (grant_type !== 'authorization_code') {
        throw new UnsupportedGrantType('this grant type is not supported');
      }

      const app = await this.oauth2Service.fetchOauth2App(client_id);

      if (app.secret !== client_secret) {
        throw new InvalidClient('client_secret is invalid');
      }

      const cachedCode = await this.distributedCache.get(code);

      if (code !== cachedCode) {
        throw new InvalidGrant('invalid authorization code');
      }
      await this.distributedCache.del(code);

      const payload = await this.oauth2Service.decodeJWT(code);

      if (payload.client_id !== client_id) {
        throw new InvalidRequest('invalid client_id');
      }

      if (payload.redirect_uri !== redirect_uri) {
        throw new InvalidRequest('invalid redirect_uri');
      }

      // end of validation

      const access_token = await this.oauth2Service.generateAccessToken(
        payload,
        true,
        this.accessTokenLifespan,
      );

      const refresh_token = await this.oauth2Service.generateRefreshToken(payload);

      await this.oauth2Service.saveRefreshToken(payload.sub, client_id, refresh_token);

      return {
        access_token,
        expires_in: 3600,
        refresh_token,
        token_type: 'Bearer',
      };
    } catch (error) {
      const [status, body] = this.decodeError(error);
      res.status(status).send(body);
    }
  }

  decodeError(error): [number, ErrorDTO] {
    if (error instanceof InvalidGrant) {
      return [400, { error: 'invalid_grant', error_description: error.message }];
    }
    if (error instanceof JWTError) {
      return [400, { error: 'invalid_grant', error_description: error.message }];
    }
    if (error instanceof DatabaseError) {
      return [401, { error: 'invalid_client', error_description: 'client_id is invalid' }];
    }
    if (error instanceof InvalidClient) {
      return [401, { error: 'invalid_client', error_description: error.message }];
    }
    if (error instanceof UnsupportedGrantType) {
      return [400, { error: 'unsupported_grant_type', error_description: error.message }];
    }
    if (error instanceof InvalidRequest) {
      return [400, { error: 'invalid_request', error_description: error.message }];
    }
    throw error;
  }

  @Post('v1/refresh')
  async refresh(
    @Body() params: OAuth2RefreshRequestDTO,
    @Res({ passthrough: true }) res: ExpressResponse,
  ) {
    try {
      const { grant_type, client_id, client_secret, refresh_token } = params;

      // Check if all query params are present
      Object.entries({ grant_type, client_id, client_secret, refresh_token }).map(
        ([key, value]) => {
          if (value) return value;
          else throw new InvalidRequest(`${key} is required`);
        },
      );

      const app = await this.oauth2Service.fetchOauth2App(client_id);

      if (app.secret !== client_secret) throw new InvalidClient('client_secret is invalid');

      if (grant_type !== 'refresh_token') {
        throw new UnsupportedGrantType('this grant type is not supported');
      }

      const payload = await this.oauth2Service.decodeJWT(refresh_token);

      if (payload.client_id !== client_id) {
        throw new InvalidRequest('invalid client_id');
      }

      if (payload.type !== 'refresh_token') {
        throw new InvalidGrant('invalid refresh_token');
      }

      const valid_refresh_tokens = await this.oauth2Service.fetchRefreshTokens(
        payload.sub,
        client_id,
      );

      if (!valid_refresh_tokens.includes(refresh_token)) {
        throw new InvalidGrant('invalid refresh_token');
      }

      const access_token = await this.oauth2Service.generateAccessToken(
        payload,
        false,
        this.accessTokenLifespan,
      );

      const fresh_refresh_token = await this.oauth2Service.generateRefreshToken(payload);

      await this.oauth2Service.updateRefreshTokens(
        payload.sub,
        client_id,
        valid_refresh_tokens.filter((token) => token !== refresh_token).concat(fresh_refresh_token),
      );

      return {
        access_token,
        expires_in: 3600,
        refresh_token: fresh_refresh_token,
        token_type: 'Bearer',
      };
    } catch (error) {
      const [status, body] = this.decodeError(error);
      res.status(status).send(body);
    }
  }
}
