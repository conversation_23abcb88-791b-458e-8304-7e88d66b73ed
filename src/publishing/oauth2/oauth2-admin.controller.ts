import { Oauth2Service } from '@/publishing/oauth2/oauth2.service';
import { Auth } from '@auth/guards/auth.guard';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import {
  ArgumentMetadata,
  BadRequestException,
  Body,
  Controller,
  ExecutionContext,
  ForbiddenException,
  Get,
  Param,
  Patch,
  PipeTransform,
  Post,
  UseGuards,
  UsePipes,
  ValidationPipe,
  createParamDecorator,
  mixin,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';
import { HydratedDocument, Model } from 'mongoose';
import { CreateAppDto, UpdateAppDto } from './dto/oauth2App.dto';
import { Oauth2App, Oauth2AppDocument } from './oauth2app.model';

export const DatabaseRecordPipe = <M extends { name: string }>(model: M) => {
  class EntityTransform implements PipeTransform<string, Promise<HydratedDocument<M>>> {
    constructor(@InjectModel(model.name) readonly model_: Model<M>) {}

    async transform(value: string, _metadata: ArgumentMetadata) {
      const record = await this.model_.findById(value);
      if (!record) {
        throw new BadRequestException(`Record ${value} not found in ${model.name}`);
      }
      return record;
    }
  }

  return mixin(EntityTransform);
};
export const JWTToken = createParamDecorator(
  // Requires Auth Guard
  (data: 'bid' | 'uid' | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    switch (data) {
      case 'bid':
        return request.user.business._id;
      case 'uid':
        return request.user._id;
      default:
        return request.user;
    }
  },
);
export const OriginatingFromAdmin = createParamDecorator(
  // Requires Auth Guard
  (_: never, ctx: ExecutionContext): boolean => {
    const request = ctx.switchToHttp().getRequest();
    return request.user.role === 'superadmin' && request.headers['x-app-name'] === 'admin';
  },
);
@Controller('oauth2-apps')
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    exceptionFactory(errors) {
      return new BadRequestException(errors);
    },
  }),
)
export class Oauth2PortalController {
  constructor(private readonly Oauth2Service: Oauth2Service) {}

  private formatAppResponse = async (app: Oauth2AppDocument) => {
    return app.toObject();
  };

  @Get()
  @UseGuards(Auth)
  async fetchApps(
    @JWTToken('bid') bid: ObjectId,
    @OriginatingFromAdmin() originatingFromAdminDashboard: boolean,
  ) {
    const results = originatingFromAdminDashboard
      ? await this.Oauth2Service.fetchAllOauth2Apps()
      : await this.Oauth2Service.fetchOauth2Apps(bid);

    return Promise.all(results.map(this.formatAppResponse));
  }

  @Post()
  @UseGuards(Auth)
  async createApp(@Body() appDTO: CreateAppDto, @JWTToken('bid') bid: ObjectId) {
    const app = new this.Oauth2Service.oauth2app({ ...appDTO, business: bid });
    return this.formatAppResponse(await app.save());
  }

  @Get(':id')
  @UseGuards(Auth)
  async fetchApp(@Param('id', DatabaseRecordPipe(Oauth2App)) app: Oauth2AppDocument) {
    return await this.formatAppResponse(app);
  }

  @Patch(':id')
  @UseGuards(Auth)
  async updateApp(
    @JWTToken('bid') bid: ObjectId,
    @Param('id', DatabaseRecordPipe(Oauth2App)) app: Oauth2AppDocument,
    @Body() appDTO: UpdateAppDto,
    @OriginatingFromAdmin() originatingFromAdminDashboard: boolean,
  ) {
    app.populate('business');

    if (!originatingFromAdminDashboard) {
      if (app.approved) delete appDTO.permissions; // App developers cannot update their own apps permissions after approval
      if (!app.business._id.equals(bid)) {
        // Admin Dashboard can update any developer's app but developers can only update their own apps
        throw new ForbiddenException('You are not authorized to update this app');
      }
    }
    Object.assign(app, appDTO);
    return await this.formatAppResponse(await app.save());
  }

  @Patch('enable/:id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async approveApp(@Param('id', DatabaseRecordPipe(Oauth2App)) app: Oauth2AppDocument) {
    app.approved = true;
    return this.formatAppResponse(await app.save());
  }

  @Patch('disable/:id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async disapproveApp(@Param('id', DatabaseRecordPipe(Oauth2App)) app: Oauth2AppDocument) {
    app.approved = false;
    return this.formatAppResponse(await app.save());
  }
}
