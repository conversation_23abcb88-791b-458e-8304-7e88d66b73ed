import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { Oauth2Service } from './oauth2.service';
import { Auth } from '@auth/guards/auth.guard';
import * as Permission from './entities/PermissionScope';

@Controller('oauth2/screen')
export class Oauth2ScreenController {
  constructor(private readonly oauth2Service: Oauth2Service) {}

  @Get('appdata')
  @UseGuards(Auth)
  async appdata(@Query('client_id') client_id: string) {
    const {
      name: appName,
      description: appDescription,
      documentationURL,
      logoURL,
      companyName,
      companyWebsite,
    } = await this.oauth2Service.fetchOauth2App(client_id);

    return {
      appName,
      appDescription,
      documentationURL,
      logoURL,
      companyName,
      companyWebsite,
    };
  }

  @Get('permissions')
  permissionsData() {
    return Permission.getScopes()
      .map((scope) => ({ [scope]: Permission.explainScope(Permission.decodeFromString(scope)) }))
      .reduce((acc, cur) => Object.assign(acc, cur), {});
  }
}
