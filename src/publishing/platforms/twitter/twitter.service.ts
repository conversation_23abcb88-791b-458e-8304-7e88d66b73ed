import type { RedisStore } from 'cache-manager-redis-yet';
import type { Cache } from 'cache-manager';

import { Inject, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import { TwitterApi } from 'twitter-api-v2';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';

import { BusinessService } from '@/business/business.service';
import { UserService } from '@/user/user.service';
import mongoose from 'mongoose';
import type { ObjectId } from 'mongodb';
import type { PublishResult } from '@/publishing/interfaces/platforms';

@Injectable()
export class TwitterService {
  private readonly logger = new Logger(TwitterService.name);
  private readonly twitterClient: TwitterApi;
  private readonly callbackUrl: string;

  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache<RedisStore>,
    private readonly configService: ConfigService,
    private businessService: BusinessService,
    private userService: UserService,
    private jwtService: JwtService,
  ) {
    this.twitterClient = new TwitterApi({
      clientId: configService.get('TWITTER_CLIENT_ID'),
      clientSecret: configService.get('TWITTER_CLIENT_SECRET'),
    });
    this.callbackUrl = this.configService.get('BASE_URL') + '/twitter/callback';
  }
  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return !!(
      business.twitterAccessToken &&
      business.twitterRefreshToken &&
      business.twitterTokenExpiry > new Date().getTime()
    );
  }
  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.twitterAccessToken = undefined;
    business.twitterRefreshToken = undefined;
    business.twitterTokenExpiry = undefined;
    await business.save();
  }

  async isTokenRenewable(bid: string): Promise<boolean> {
    try {
      await this.getAccessToken(bid, true);

      return true;
    } catch (error) {
      this.logger.error(`Token is not renewable: for ${bid}`, error?.message);
      return false;
    }
  }

  async connect(token: string): Promise<string> {
    const { url, codeVerifier, state } = this.twitterClient.generateOAuth2AuthLink(
      this.callbackUrl,
      {
        scope: ['offline.access', 'tweet.write', 'tweet.read', 'users.read'],
      },
    );

    await this.distributedCache.set(state, codeVerifier);
    await this.distributedCache.set(`${state}_token`, token);

    return url;
  }

  async callback(state: string, code: string): Promise<string> {
    const codeVerifier = await this.distributedCache.get<string>(state);
    const token = await this.distributedCache.get<string>(`${state}_token`);
    const redirectUrl = await this.distributedCache.get<string>(`${state}_redirect`);
    const redirectTo =
      redirectUrl || this.configService.get('DASHBOARD_REDIRECT_URL') + '/dashboard/settings';
    if (!codeVerifier || !state || !code) {
      throw new Error(`You denied the app or tokens didn't match!`);
    }

    try {
      const { accessToken, refreshToken, expiresIn } = await this.twitterClient.loginWithOAuth2({
        code,
        codeVerifier,
        redirectUri: this.callbackUrl,
      });

      const now = new Date().getTime();
      const expiryDate = new Date(now + expiresIn * 1000).getTime();
      this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(token);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }
      await this.businessService.updateBusinessTwitterToken(
        payload.bid,
        accessToken,
        refreshToken,
        expiryDate,
      );
      return `${redirectTo}?success=true`;
    } catch (error) {
      this.logger.error(`Error in Twitter callback: ${error?.message}`);
      return `${redirectTo}?error=${error?.message}`;
    }
  }

  async publish({ bid, content }: { bid: ObjectId; content: string }): Promise<PublishResult> {
    try {
      const accessToken = await this.getAccessToken(bid.toString(), true);

      const tweetParams = {
        text: this.trimTweet(content),
      };

      const tweetResponse = await axios.post('https://api.twitter.com/2/tweets', tweetParams, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      if (tweetResponse.status === 201) {
        return {
          link: new URL(`https://twitter.com/i/web/status/${tweetResponse?.data?.data?.id}`),
          kind: 'success',
        };
      }
    } catch (error) {
      return {
        error: new Error(error),
        kind: 'failure',
      };
    }
  }

  async getAccessToken(bid: string, forceRefreshToken = false): Promise<string> {
    const business = await this.businessService.findOne(bid);

    if (!business) {
      throw new UnauthorizedException('Business not found');
    }

    this.logger.debug(business.twitterTokenExpiry);

    if (business.twitterTokenExpiry > new Date().getTime() && !forceRefreshToken) {
      return business.twitterAccessToken;
    }

    const response = await this.twitterClient.refreshOAuth2Token(business.twitterRefreshToken);

    // const response = await axios.post(
    //   'https://api.twitter.com/2/oauth2/token',
    //   'grant_type=refresh_token&refresh_token=' + business.twitterRefreshToken,
    //   {
    //     headers: {
    //       Authorization:
    //         'Basic ' +
    //         Buffer.from(
    //           `${this.configService.get(
    //             'TWITTER_CLIENT_ID',
    //           )}:${this.configService.get('TWITTER_CLIENT_SECRET')}`,
    //         ).toString('base64'),
    //       'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    //     },
    //   },
    // );

    await this.businessService.updateBusinessTwitterToken(
      bid,
      response?.accessToken,
      response?.refreshToken,
      new Date().getTime() + response?.expiresIn * 1000,
    );

    return response?.accessToken;
  }

  private trimTweet(text: string, link = ''): string {
    const words = text.split(' ');
    let trimmedText = '';

    for (let i = 0; i < words.length; i++) {
      if (trimmedText.length + words[i].length <= 280 - link.length) {
        trimmedText += `${words[i]} `;
      } else {
        break;
      }
    }

    return trimmedText.replace('\n', '').trim();
  }
}
