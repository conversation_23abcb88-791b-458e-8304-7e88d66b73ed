import { TwitterService } from './twitter.service';
import { <PERSON>, Get, Lo<PERSON>, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

@Controller('twitter')
export class TwitterController {
  private readonly logger = new Logger(TwitterController.name);

  constructor(private readonly twitterService: TwitterService) {}

  @Get('connect')
  async connect(@Query('token') token: string, @Res() res: Response) {
    const url = await this.twitterService.connect(token);
    res.redirect(url);
  }

  @Get('callback')
  async callback(
    @Query('state') state: string,
    @Query('code') code: string,
    @Req() req: AuthenticatedRequest,
    @Res() res: Response,
  ) {
    const url = await this.twitterService.callback(state, code);

    res.redirect(url);
  }
}
