import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { BusinessModule } from '@/business/business.module';
import { UserModule } from '@/user/user.module';

import { TwitterController } from './twitter.controller';
import { TwitterService } from './twitter.service';

@Module({
  controllers: [TwitterController],
  imports: [BusinessModule, UserModule],
  providers: [TwitterService, JwtService],
  exports: [TwitterService],
})
export class TwitterModule {}
