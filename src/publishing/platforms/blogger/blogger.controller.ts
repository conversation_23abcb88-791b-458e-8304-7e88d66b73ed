import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { <PERSON>, Get, Query, Req, Res } from '@nestjs/common';
import { BloggerService } from './blogger.service';

@Controller('blogger')
export class BloggerController {
  constructor(private readonly bloggerService: BloggerService) {}

  @Get('connect')
  async authorize(
    @Req() req: AuthenticatedRequest,
    @Res() res,
    @Query('redirectUrl') redirectUrl: string,
  ) {
    await this.bloggerService.authorize(req, res, redirectUrl);
  }

  @Get('callback')
  async callback(@Req() req: AuthenticatedRequest, @Res() res) {
    await this.bloggerService.callback(req, res);
  }
}
