// بسم الله الرحمن الرحيم

import type {
  PlatformService,
  PublishPayload,
  PublishResult,
  Site,
} from '@/publishing/interfaces/platforms';
import { Business } from '@business/business.model';
import { BusinessService } from '@business/business.service';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '@user/user.service';
import { Auth, google } from 'googleapis';
import mongoose from 'mongoose';

@Injectable()
export class BloggerService implements PlatformService<'blogger'> {
  private readonly logger = new Logger(BloggerService.name);
  private readonly oauth2Client: Auth.OAuth2Client;

  constructor(
    private readonly configService: ConfigService,
    private jwtService: JwtService,
    private userService: UserService,
    private businessService: BusinessService,
  ) {
    this.oauth2Client = new google.auth.OAuth2(
      configService.get('BLOGGER_CLIENT_ID'),
      configService.get('BLOGGER_CLIENT_SECRET'),
      configService.get('BASE_URL') + '/blogger/callback',
    );
  }
  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return (
      Object.keys(business.bloggerTokens).length > 0 && (await this.fetchSites(bid)).length > 0
    );
  }
  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.bloggerTokens = {};
    await business.save();
  }

  async fetchSites(bid: mongoose.Types.ObjectId): Promise<Site[]> {
    return Object.values(await this.fetchBlogs(bid.toString())).flatMap(({ blogs }) => blogs);
  }

  async authorize(req, res, redirectUrl: string) {
    const url = this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent',
      scope: ['https://www.googleapis.com/auth/blogger'],
      state: req.query.token, // jwt token state parameter
      redirectUrl,
    });
    this.logger.debug(`Auth URL: ${url}`);
    res.redirect(url);
  }

  async callback(req, res) {
    const { code, state, redirectUrl } = req.query;
    const redirectTo =
      redirectUrl || this.configService.get('DASHBOARD_REDIRECT_URL') + '/dashboard/settings';
    const jwtToken: string = state as string;

    try {
      const { tokens } = await this.oauth2Client.getToken(code);

      const bloggerAccessToken = tokens.access_token;
      const bloggerRefreshToken = tokens.refresh_token;
      const bloggerTokenExpiry = tokens.expiry_date;

      // Save the tokens to your database for future use
      this.jwtService.verify(jwtToken, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(jwtToken);
      this.logger.debug(payload);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      this.logger.log(user, payload);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }
      this.oauth2Client.setCredentials({
        access_token: bloggerAccessToken,
        refresh_token: bloggerRefreshToken,
      });
      const blogger = google.blogger({
        version: 'v3',
        auth: this.oauth2Client,
      });

      const {
        data: { id },
      } = await blogger.users.get({ userId: 'self' });
      await this.businessService.updateBusinessBloggerToken(payload.bid, id, {
        access_token: bloggerAccessToken,
        refresh_token: bloggerRefreshToken,
        expiry_date: bloggerTokenExpiry,
      });
      res.redirect(`${redirectTo}?success=true`);
    } catch (error) {
      this.logger.error(`Error in blogger callback: ${error?.message}`);
      res.redirect(`${redirectTo}?error=${error?.message}`);
    }
  }

  async refreshAccessToken(
    accessToken: string,
    refreshToken: string,
    expiryDate = new Date().getTime(),
  ): Promise<Business['bloggerTokens'][string]> {
    return new Promise((resolve, reject) => {
      this.oauth2Client.setCredentials({
        access_token: accessToken,
        refresh_token: refreshToken,
      });
      this.oauth2Client.refreshAccessToken((err, tokens) => {
        if (err) {
          this.logger.error('Error refreshing access token: expiryDate', expiryDate, err);
          return reject(err);
        }
        this.logger.debug(tokens);

        resolve({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          expiry_date: tokens.expiry_date,
        });
      });
    });
  }

  async fetchBlogs(bid: string) {
    try {
      const business = await this.businessService.findOne(bid);
      return Promise.all(
        Object.entries(business.bloggerTokens).map(async ([userId, tokens]) => {
          const currentDate = new Date().getTime();
          if (currentDate >= tokens.expiry_date) {
            tokens = await this.refreshAccessToken(
              tokens.access_token,
              tokens.refresh_token,
              tokens.expiry_date,
            );
            await this.businessService.updateBusinessBloggerToken(bid, userId, tokens);
          }

          this.oauth2Client.setCredentials({
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
          });
          const blogger = google.blogger({
            version: 'v3',
            auth: this.oauth2Client,
          });
          const response = await blogger.blogs.listByUser({
            userId, // get the list of blogs for the authenticated user
          });

          const blogs =
            response.data?.items?.map((site) => ({
              name: site.name,
              url: site.url,
              id: site.id,
            })) || [];
          return <[string, { tokens: Business['bloggerTokens'][string]; blogs: Site[] }]>[
            userId,
            { tokens, blogs },
          ];
        }),
      ).then((entries) => Object.fromEntries(entries));
    } catch (error) {
      this.logger.error('Error getting blogger blog ID:', error?.message);
      throw error;
    }
  }

  /**
   * Blogger requires the sum of keywords to be at most 120,
   * so this function returns an array of keywords which satisfies
   * this constraint
   *
   */
  trim_keywords(keywords?: string[]): string[] {
    if (!keywords) return [];
    const trimmed_keywords = [];
    for (let i = 0, total_length = 0; i < keywords.length; i++) {
      if (keywords[i].length + total_length < 120) {
        trimmed_keywords.push(keywords[i]);
        total_length += keywords[i].length;
      }
    }
    return trimmed_keywords;
  }

  async publish({
    bid,
    title,
    content,
    draft,
    image,
    keywords,
    tldr,
    site: siteID,
  }: PublishPayload): Promise<PublishResult> {
    const info = await this.fetchBlogs(bid.toString());
    const lookupToken = Object.fromEntries(
      Object.values(info).flatMap(({ tokens, blogs }) =>
        blogs.map(({ id }): [string, typeof tokens] => [id, tokens]),
      ),
    );
    try {
      const tokens = lookupToken[siteID];
      this.oauth2Client.setCredentials({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
      });
      const blogger = google.blogger({
        version: 'v3',
        auth: this.oauth2Client,
      });
      // Docs: https://developers.google.com/blogger/docs/3.0/reference/posts/insert
      const response = await blogger.posts.insert({
        blogId: siteID,
        isDraft: draft,
        requestBody: {
          customMetaData: JSON.stringify({
            metaTags: this.trim_keywords(keywords),
            metaDescription: tldr,
          }),
          title: title,
          content: `<div class="separator" style="clear: both;">
                <a href="${image}" style="display: block; padding: 1em 0px; text-align: center;">
            <img alt="${title}" border="0" height="320" src="${image}" />
                </a>
              </div>
              <div>
                ${content}
              </div>`,
          labels: this.trim_keywords(keywords),
        },
      });
      return { link: new URL(response?.data?.url), kind: 'success' };
    } catch (error) {
      return { error: new Error(error), kind: 'failure' };
    }
  }
}
