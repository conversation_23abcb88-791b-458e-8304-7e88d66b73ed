import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BloggerController } from './blogger.controller';
import { BloggerService } from './blogger.service';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtService } from '@nestjs/jwt';
import { JobModule } from '@job/job.module';
import { UserService } from '@/user/user.service';
import { CreditTransactionModule } from '@/resources/credit-transaction/credit-transaction.module';

@Module({
  controllers: [BloggerController],
  imports: [UserModule, BusinessModule, JobModule, CreditTransactionModule],
  providers: [BloggerService, JwtService, UserService],
  exports: [BloggerService],
})
export class BloggerModule {}
