import type {
  PlatformService,
  PublishPayload,
  PublishResult,
  Site,
} from '@/publishing/interfaces/platforms';
import { BusinessService } from '@business/business.service';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { either } from 'fp-ts';
import mongoose from 'mongoose';

@Injectable()
export class MediumService implements PlatformService<'medium'> {
  constructor(private businessService: BusinessService) {}

  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return (
      Object.keys(business.mediumAccessTokens).length > 0 && (await this.fetchSites(bid)).length > 0
    );
  }

  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.mediumAccessTokens = {};
    await business.save();
  }

  fetchSites(bid: mongoose.Types.ObjectId): Promise<Site[]> {
    return this.getSites(bid.toString());
  }

  async getSites(bid: string) {
    const business = await this.businessService.findOne(bid);
    return Promise.all(Object.values(business.mediumAccessTokens).map(this.getMediumProfile));
  }

  async getMediumProfile(token: string): Promise<{
    id: string;
    username: string;
    name: string;
    url: string;
  }> {
    return axios
      .get(`https://api.medium.com/v1/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((response) => response.data.data);
  }

  async saveMediumIntegrationToken(bid: string, token: string) {
    const { id } = await this.getMediumProfile(token);
    return await this.businessService.updateMediumIntegrationToken(bid, token, id);
  }

  async publish({
    bid,
    title,
    content,
    keywords,
    draft,
    image,
    site: siteID,
  }: PublishPayload): Promise<PublishResult> {
    const business = await this.businessService.findOne(bid.toString());

    if (!business) {
      throw new UnauthorizedException('Business not found');
    }

    if (!business.mediumAccessTokens) {
      throw new UnauthorizedException('Medium tokens are missing have to manually connect again');
    }

    const addImage = (imageLink: string, content: string) => {
      return imageLink ? `<img src="${imageLink}" width="800" />${content}` : content;
    };

    const addTitle = (title: string, content: string) => {
      return `<h1>${title}</h1>${content}`;
    };

    try {
      const response = await axios.post(
        // Docs: https://github.com/Medium/medium-api-docs
        `https://api.medium.com/v1/users/${siteID}/posts`,
        {
          title,
          content: addTitle(title, addImage(image.toString(), content)),
          siteID,
          contentFormat: 'html',
          publishStatus: draft ? 'draft' : 'public',
          tags: keywords,
          notifyFollowers: true,
        },
        {
          headers: {
            Authorization: `Bearer ${business.mediumAccessTokens[siteID]}`,
          },
        },
      );

      return { link: new URL(response.data.data.url), kind: 'success' };
    } catch (error) {
      return { error: error.toString(), kind: 'failure' };
    }
  }

  async validateToken(
    token: string,
  ): Promise<either.Either<string, { username: string; name: string; link: string }>> {
    try {
      const response = await axios.get(`https://api.medium.com/v1/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return either.right({
        username: response.data.data.username,
        name: response.data.data.name,
        link: response.data.data.url,
      });
    } catch (error: any | AxiosError) {
      if (error instanceof AxiosError && error.response.status == 401) {
        return either.left(error.response.data.errors[0].message);
      } else {
        throw error;
      }
    }
  }
}
