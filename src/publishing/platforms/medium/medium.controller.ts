import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { MediumService } from './medium.service';
import { Auth } from '@auth/guards/auth.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { either } from 'fp-ts';
import { ConfigService } from '@nestjs/config';

@Controller('medium')
export class MediumController {
  constructor(
    private readonly mediumService: MediumService,
    private readonly config: ConfigService,
  ) {}

  @Get('/connect')
  @UseGuards(QueryTokenGuard, Auth)
  async setToken(
    @Query('medium-integration-token') mediumToken: string,
    @Req() req: AuthenticatedRequest,
    @Res() res: Response,
  ) {
    const redirectURL = new URL(
      '/dashboard/settings',
      this.config.get<string>('DASHBOARD_REDIRECT_URL'),
    );

    try {
      const { uid, bid } = req;
      if (!uid || !bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      await this.mediumService.saveMediumIntegrationToken(bid, mediumToken);

      redirectURL.searchParams.append('message', 'Successfully connected to Medium');
    } catch (error) {
      redirectURL.searchParams.append('error', error.message);
    }

    res.redirect(redirectURL.toString().replaceAll('+', '%20'));
  }

  /**
   *  Endpoint for checking if user submitted token's are valid or not.
   *
   *
   */
  @Post('validate')
  async validate_token(
    @Body('token') mediumToken: string,
  ): Promise<{ error?: string; username?: string; name?: string; link?: string }> {
    try {
      const answer = await this.mediumService.validateToken(mediumToken);
      const ret = { error: undefined, username: undefined, name: undefined, link: undefined };
      either.match(
        (error: string) => {
          ret.error = error;
        },
        ({ username, name, link }: { username: string; name: string; link: string }) => {
          ret.name = name;
          ret.username = username;
          ret.link = link;
        },
      )(answer);
      return ret;
    } catch (e) {
      return { error: e.message };
    }
  }
}
