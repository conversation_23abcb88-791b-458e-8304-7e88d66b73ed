// بسم الله الرحمن الرجيم

import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ZapierService } from './zapier.service';

@Injectable()
export class ZapierApiGuard implements CanActivate {
  private logger = new Logger(ZapierApiGuard.name);
  constructor(private zapierService: ZapierService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const zapierKey = request.headers['zapier-api-key'];
    if (!zapierKey) throw new ForbiddenException('zapier-api-key not found in request header');
    const business = await this.zapierService.fetchBusinessByZapierKey(zapierKey);
    request.business = business;
    return business ? true : false;
  }
}
