import type {
  PublishPayload,
  PublishResult,
  PlatformService,
} from '@/publishing/interfaces/platforms';
import { Blog } from '@blog/blog.model';
import { Business } from '@business/business.model';
import { BusinessService } from '@business/business.service';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { randomUUID } from 'crypto';
import { ObjectId } from 'mongodb';
import { Model, Types } from 'mongoose';

@Injectable()
export class ZapierService implements PlatformService<'zapier'> {
  constructor(
    private readonly businessService: BusinessService,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
  ) {}

  async disconnect(bid: Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.zapHookUrls = [];
    await business.save();
  }

  async isConnected(bid: Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return !!(business.zapierKey && business.zapHookUrls.length > 0);
  }

  /** Generates a random API Key to be granted to a business */
  readonly generateZapierKey = randomUUID;

  /**
   * Assigns a new API Key to a business
   * @param businessId - The business ID for which a new API key is to be assigned
   * @returns the Zapier API Key
   */
  async assignApiKey(businessId: string) {
    const { zapierKey } = await this.businessService.updateBusinessZapierKey(
      businessId,
      this.generateZapierKey(),
    );
    return zapierKey;
  }

  /**
   * Fetches the business associated with a particular Zapier API Key
   * @param zapierKey - The unique API Key given associated with each business
   * @returns The business with the Zapier Key
   * @throws if the Zapier Key is invalid or if two businesses have the same Zapier Key
   */
  async fetchBusinessByZapierKey(zapierKey: string) {
    const businesses = await this.businessService.findbyZapierKey(zapierKey);
    switch (businesses.length) {
      case 0:
        throw new Error('ZapierKey not found in database');
      case 1:
        return businesses[0];
      default:
        throw new Error('Duplicate ZapierKey found!');
    }
  }

  /**
   * Fetches an array of blogs sorted in reverse chronological order
   * @param businesssId - The unique ID associated wiith each business
   * @returns All successful blogs under this business
   */
  async fetchBlogs(businessId: string) {
    const blogs = await this.blog
      .find({
        bid: businessId,
        status: {
          $in: ['publisher_dispatched', 'blog_published', 'social_published', 'completed'],
        },
      })
      .select({
        _id: 1,
        title: 1,
        content: 1,
        image: 1,
      })
      .sort({ createdAt: -1, updatedAt: -1 })
      .lean();
    return blogs;
  }

  /**
   * Add a Zapier Hook URL to a business.
   * @param businesss - The business to modify
   * @param hookUrl - The target URL provided by Zapier
   * @returns The updated business
   */
  async addHookUrl(business: Business & { _id: ObjectId }, hookUrl: string) {
    const hookUrls = new Set(business.zapHookUrls);
    if (hookUrls.has(hookUrl)) throw new Error('Hook URL already exists' + hookUrl);
    hookUrls.add(hookUrl);
    return await this.businessService.updateZapHookUrls(business._id, hookUrls);
  }

  /**
   * Remove a Zapier Hook URL from a business.
   * @param businesss - The business to modify
   * @param hookUrl - The target URL provided by Zapier
   * @returns The updated business
   */
  async removeHookUrl(business: Business & { _id: ObjectId }, hookUrl: string) {
    const hookUrls = new Set(business.zapHookUrls);
    if (!hookUrls.has(hookUrl)) {
      throw new Error('Hook URL not found');
    }
    hookUrls.delete(hookUrl);
    return await this.businessService.updateZapHookUrls(business._id, hookUrls);
  }

  /**
   * Disconnects Zaps connected to Blogify by sending a DELETE request.
   * @param businessId - The unique ID assicuated with each business
   */
  async disconnectHookUrls(businessId: string) {
    const { zapHookUrls } = await this.businessService.findOne(businessId);
    for (const hookUrl of zapHookUrls) {
      await axios.delete(hookUrl);
    }
  }

  async publish({ bid, title, content, image, keywords }: PublishPayload): Promise<PublishResult> {
    try {
      const { zapHookUrls } = await this.businessService.findOne(bid.toString());
      for (const hookUrl of zapHookUrls) {
        await axios.post(hookUrl, {
          id: bid,
          title,
          content,
          image,
          keywords,
        });
      }
      return { kind: 'success' };
    } catch (error) {
      return { error: error, kind: 'failure' };
    }
  }
}
