import {
  BadRequestException,
  Controller,
  Delete,
  Get,
  HttpCode,
  Logger,
  Post,
  Redirect,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ZapierService } from './zapier.service';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { Auth } from '@auth/guards/auth.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { ZapierApiGuard } from './zapier-api-key.guard';
import { BusinessService } from '@business/business.service';

type BlogData = {
  content: string;
  title: string;
  keywords: string[];
  image: string;
};
@Controller('zapier')
export class ZapierController {
  private logger = new Logger(ZapierController.name);
  constructor(
    private readonly zapierService: ZapierService,
    private readonly businessService: BusinessService,
  ) {}

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth)
  @Redirect(process.env.ZAPPIER_CONNECT_URL)
  redirecttoAppInstall() {
    // redirect to App Install Page
  }

  @Get('key')
  @UseGuards(Auth)
  async generateApiKey(@Req() request: AuthenticatedRequest) {
    const { bid } = request;
    const business = await this.businessService.findOne(bid);
    if (!business) throw new BadRequestException('Business not found');
    const zapierKey = business.zapierKey ?? (await this.zapierService.assignApiKey(bid));
    return {
      key: zapierKey,
    };
  }

  @Get('key/validate')
  async validateApiKey(@Req() request) {
    const zapierKey = request.headers['zapier-api-key'];
    if (!zapierKey) throw new BadRequestException('Please include Zapier Key in Header');
    try {
      const { name, website } = await this.zapierService.fetchBusinessByZapierKey(zapierKey);
      return {
        name,
        website,
      };
    } catch (e) {
      throw new BadRequestException(`Key is not valid ${e?.message}`);
    }
  }

  @HttpCode(201)
  @Post('/subscribe')
  @UseGuards(ZapierApiGuard)
  async subscribe(@Req() request) {
    const { hookUrl } = request.body;
    await this.zapierService.addHookUrl(request.business, hookUrl);
    return {
      id: hookUrl,
    };
  }

  @HttpCode(204)
  @Delete('/subscribe')
  @UseGuards(ZapierApiGuard)
  async unsubscribe(@Req() request) {
    const { hookUrl } = request.body;
    await this.zapierService.removeHookUrl(request.business, hookUrl);
  }

  @Get('/blogs')
  @UseGuards(ZapierApiGuard)
  async fetchBlogsbyZapierKey(@Req() request): Promise<Array<BlogData & { id: string }>> {
    const blogs: Array<BlogData & { _id: string }> = await this.zapierService.fetchBlogs(
      request.business._id,
    );
    return blogs.map((blog) => {
      const { _id, ...rest } = blog;
      return {
        id: _id,
        ...rest,
      };
    });
  }
}
