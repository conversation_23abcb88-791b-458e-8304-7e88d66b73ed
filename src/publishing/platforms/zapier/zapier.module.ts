import { forwardRef, Module } from '@nestjs/common';
import { ZapierController } from './zapier.controller';
import { ZapierService } from './zapier.service';
import { BusinessModule } from '@business/business.module';
import { ScheduleModule } from '@nestjs/schedule';
import { BlogModule } from '@blog/blog.module';
import { ZapierApiGuard } from './zapier-api-key.guard';

@Module({
  imports: [BusinessModule, ScheduleModule.forRoot(), forwardRef(() => BlogModule)],
  controllers: [ZapierController],
  providers: [ZapierService, ZapierApiGuard],
  exports: [ZapierService],
})
export class ZapierModule {}
