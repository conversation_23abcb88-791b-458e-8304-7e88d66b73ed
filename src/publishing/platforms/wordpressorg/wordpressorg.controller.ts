// بسم الله الرحمن الرحيم

import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { WordpressorgService } from './wordpressorg.service';
import { Response } from 'express';
import { Auth } from '@auth/guards/auth.guard';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Oauth2Service } from '@/publishing/oauth2/oauth2.service';
import * as Permission from '@/publishing/oauth2/entities/PermissionScope';
import { TokenPayload } from '@/publishing/oauth2/entities/TokenPayload';

@Controller('wordpressorg')
export class WordpressorgController {
  private readonly logger = new Logger(WordpressorgController.name);
  constructor(
    private wordpressorgService: WordpressorgService,
    private readonly config: ConfigService,
    private readonly jwtService: JwtService,
    private readonly oauth2service: Oauth2Service,
  ) {}

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth)
  async storeUrlWithSecret(@Req() request: AuthenticatedRequest, @Res() response: Response) {
    const redirectURL = new URL(
      '/dashboard/settings',
      this.config.getOrThrow<string>('DASHBOARD_REDIRECT_URL'),
    );

    try {
      const { bid } = request;
      if (!bid) {
        throw new Error('Business not found for the user');
      }

      const { url } = request.query;
      if (!url) {
        throw new Error('Please add query parameter called url');
      }

      const urlsecret = new URL(url as string);
      if (!urlsecret.searchParams.has('secret')) {
        throw new Error('Please make sure the query parameter url includes the secret');
      }
      // save url with secret
      const b = await this.wordpressorgService.saveUrlWithSecret(bid, urlsecret.toString());
      this.logger.log(b);
      redirectURL.searchParams.append(
        'message',
        `Successfully connected to WordPress: ${urlsecret.origin}`,
      );
    } catch (error) {
      redirectURL.searchParams.append('error', error.message);
    }

    response.redirect(redirectURL.toString().replaceAll('+', '%20'));
  }

  @Delete('subscribe')
  @HttpCode(HttpStatus.NO_CONTENT)
  async unsubscribe(@Headers('Authorization') auth: string, @Body('webhook') webhook: string) {
    let token: TokenPayload = null;
    try {
      token = await this.jwtService.verifyAsync(auth.split(' ')[1], {
        secret: this.config.getOrThrow<string>('JWT_SECRET'),
      });
      if (token.client_id !== 'wordpressorg') {
        throw new Error('Client ID is invalid');
      }
    } catch (error) {
      throw new UnauthorizedException('Invalid token: ' + error.message);
    }

    let urlsecret: URL;
    try {
      urlsecret = new URL(webhook);
      if (!urlsecret.searchParams.has('secret')) {
        throw new Error('Please make sure the webhook includes the secret');
      }
    } catch (error) {
      throw new BadRequestException('Invalid webhook: ' + error.message);
    }

    await this.wordpressorgService.deleteUrlWithSecret(token.sub, urlsecret.toString());
  }

  @Post('subscribe')
  @HttpCode(HttpStatus.NO_CONTENT)
  async subscribe(@Headers('Authorization') auth: string, @Body('webhook') webhook: string) {
    let token: TokenPayload = null;
    try {
      token = await this.jwtService.verifyAsync(auth.split(' ')[1], {
        secret: this.config.getOrThrow<string>('JWT_SECRET'),
      });
      if (token.client_id !== 'wordpressorg') {
        throw new Error('Client ID is invalid');
      }
    } catch (error) {
      throw new UnauthorizedException('Invalid token: ' + error.message);
    }

    let urlsecret: URL;
    try {
      urlsecret = new URL(webhook);
      if (!urlsecret.searchParams.has('secret')) {
        throw new Error('Please make sure the webhook includes the secret');
      }
    } catch (error) {
      throw new BadRequestException('Invalid webhook: ' + error.message);
    }

    await this.wordpressorgService.saveUrlWithSecret(token.sub, urlsecret.toString());
  }

  @Get('token/generate')
  @UseGuards(Auth)
  async generateToken(@Req() request: AuthenticatedRequest) {
    const { bid, uid } = request;
    const token = await this.oauth2service.generateAccessToken(
      {
        uid,
        iss: this.config.getOrThrow<string>('BASE_URL'),
        aud: this.config.getOrThrow<string>('BASE_URL'),
        sub: bid,
        scope: Permission.vectorize(
          // Permission.Scope.SubscribeBlog is forbidden for wordpressorg
          [
            Permission.Scope.CreateBlog,
            Permission.Scope.ReadBlog,
            Permission.Scope.UpdateBlog,
            Permission.Scope.DeleteBlog,
            Permission.Scope.ReadProfile,
            Permission.Scope.ReadSettings,
          ],
        ),
        client_id: 'wordpressorg',
        public: true, // Must be public
      },
      true,
      false,
    );

    return { token };
  }

  @Get('token/validate')
  async validateToken(@Headers('Authorization') authorization: string) {
    try {
      await this.oauth2service.decodeJWT(authorization.split(' ')[1]);
      return {
        status: 'valid',
      };
    } catch (error) {
      throw new BadRequestException('Invalid key: ' + error.message);
    }
  }
}
