// بسم الله الرحمن الرحيم

import { BusinessModule } from '@business/business.module';
import { JobModule } from '@job/job.module';
import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserModule } from '@user/user.module';
import { WordpressorgController } from './wordpressorg.controller';
import { WordpressorgService } from './wordpressorg.service';
import { Oauth2Module } from '@/publishing/oauth2/oauth2.module';

@Module({
  controllers: [WordpressorgController],
  imports: [UserModule, BusinessModule, JobModule, Oauth2Module],
  providers: [JwtService, WordpressorgService],
  exports: [WordpressorgService],
})
export class WordpressorgModule {}
