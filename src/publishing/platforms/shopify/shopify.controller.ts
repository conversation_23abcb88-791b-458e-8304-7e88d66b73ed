// بسم الله الرحمن الرحيم

import { Auth } from '@/auth/guards/auth.guard';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { ShopifyService } from '@/publishing/platforms/shopify/shopify.service';
import { extractShop, type ShopDomain } from '@/publishing/platforms/shopify/utils';
import { Controller, Get, Query, Res, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import type { Response } from 'express';
import { ObjectId } from 'mongodb';

@Controller('shopify')
export class ShopifyController {
  constructor(
    private readonly config: ConfigService,
    private readonly shopifyService: ShopifyService,
  ) {}

  @Get('auth')
  @UseGuards(QueryTokenGuard, Auth)
  async auth(
    @Query('shop') shopDomain: ShopDomain,
    @Req() { bid }: AuthenticatedRequest,
    @Res() response: Response,
  ) {
    const redirectURL = new URL('/admin/oauth/authorize', `https://${shopDomain}`);
    redirectURL.searchParams.append(
      'client_id',
      this.config.getOrThrow<string>('SHOPIFY_CLIENT_ID'),
    );
    redirectURL.searchParams.append('scope', 'read_content,write_content');
    redirectURL.searchParams.append(
      'redirect_uri',
      new URL('/shopify/auth/callback', this.config.getOrThrow<string>('BASE_URL')).toString(),
    );
    redirectURL.searchParams.append('state', bid);
    response.redirect(redirectURL.toString());
  }

  @Get('auth/callback')
  async callback(
    @Query('shop') shopDomain: ShopDomain,
    @Query('state') businessID: string,
    @Query('code') code: string,
    @Res() response: Response,
  ) {
    const shop = extractShop(shopDomain);
    const {
      data: { access_token },
    } = await axios.post<Record<'access_token' | 'scope', string>>(
      new URL('/admin/oauth/access_token', `https://${shop}.myshopify.com`).toString(),
      {
        client_id: this.config.getOrThrow<string>('SHOPIFY_CLIENT_ID'),
        client_secret: this.config.getOrThrow<string>('SHOPIFY_CLIENT_SECRET'),
        code,
      },
    );
    await this.shopifyService.saveToken(new ObjectId(businessID), shop, access_token);
    response.redirect(
      new URL(
        '/dashboard/settings',
        this.config.getOrThrow<string>('DASHBOARD_REDIRECT_URL'),
      ).toString(),
    );
  }
}
