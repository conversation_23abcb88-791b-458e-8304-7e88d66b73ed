// بسم الله الرحمن الرحيم

export type StoreName = string;
export type ShopDomain = `${StoreName}.myshopify.com`;
export type ShopHostname = `https://${ShopDomain}`;

/**
 * Extracts the shop name from a Shopify domain.
 * @param shopDomain - The full Shopify domain in the format `<shop>.myshopify.com`
 * @returns The shop name without the domain suffix
 * @example
 * ```ts
 * extractShop('mystore.myshopify.com') // returns 'mystore'
 * ```
 */
export const extractShop = (shopDomain: ShopDomain) => shopDomain.split('.')[0];

export const endpoint = (shop: StoreName) =>
  `https://${shop}.myshopify.com/admin/api/2025-04/graphql.json`;
export const blogSite = (shop: StoreName, blogHandle: string) =>
  `https://${shop}.myshopify.com/blogs/${blogHandle}`;
export const articlePage = ({
  shop,
  blogHandle,
  articleHandle,
}: {
  shop: StoreName;
  blogHandle: string;
  articleHandle: string;
}) => `https://${shop}.myshopify.com/blogs/${blogHandle}/${articleHandle}`;
