import { JwtService } from '@nestjs/jwt';
import { forwardRef, Module } from '@nestjs/common';

import { BusinessModule } from '@business/business.module';
import { UserModule } from '@user/user.module';

import { LinkedinController } from './linkedin.controller';
import { LinkedinService } from './linkedin.service';
import { ScraperService } from '@/blog/scraper.service';
import { BlogGenerationModule } from '@/blog-generation/blog-generation.module';
import { BlogModule } from '@/blog/blog.module';

@Module({
  controllers: [LinkedinController],
  providers: [LinkedinService, JwtService, ScraperService],
  imports: [UserModule, BusinessModule, BlogGenerationModule, forwardRef(() => BlogModule)],
  exports: [LinkedinService],
})
export class LinkedinModule {}
