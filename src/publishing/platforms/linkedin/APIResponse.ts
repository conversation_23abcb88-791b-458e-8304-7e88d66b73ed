export type OrganizationResponse = {
  results: {
    [id: string]: {
      vanityName: string;
      localizedName: string;
      created: {
        actor: string;
        time: number;
      };
      groups: Array<any>;
      versionTag: string;
      organizationType: string;
      defaultLocale: {
        country: string;
        language: string;
      };
      alternativeNames: Array<any>;
      specialties: Array<any>;
      staffCountRange: string;
      localizedSpecialties: Array<any>;
      name: {
        localized: Record<string, string>;
        preferredLocale: {
          country: string;
          language: string;
        };
      };
      primaryOrganizationType: string;
      locations: Array<any>;
      lastModified: {
        actor: string;
        time: number;
      };
      id: number;
      autoCreated: boolean;
    };
  };
  statuses: Record<string, number>;
  errors: {
    [id: string]: {
      message: string;
      status: number;
    };
  }; // Docs: https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-lookup-api?view=li-lms-2025-02&tabs=http#batch-get-administered-organizations
};
