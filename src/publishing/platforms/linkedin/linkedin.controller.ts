import { Controller, Get, Query, Redirect, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { LinkedinService } from './linkedin.service';

@Controller('linkedin')
export class LinkedinController {
  constructor(private readonly linkedinService: LinkedinService) {}

  @Get('connect')
  @Redirect()
  getAuthUrl(@Query('token') token: string, @Query('redirectUrl') redirectUrl: string) {
    const authUrl = this.linkedinService.getAuthUrl(token, redirectUrl);
    return { url: authUrl };
  }

  @Get('callback')
  async callback(@Query('code') code: string, @Req() req: Request, @Res() res: Response) {
    const accessToken = await this.linkedinService.getAccessToken(code);
    await this.linkedinService.getProfile(accessToken);
    const token = req.query?.state as string;

    const url = await this.linkedinService.saveLinkedinToken(token, accessToken);

    res.redirect(url);
  }
}
