import { UnauthorizedException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import qs from 'querystring';

import { BusinessService } from '@/business/business.service';
import { UserService } from '@/user/user.service';
import { Blog } from '@/blog/blog.model';
import { load } from 'cheerio';
import { extract } from '@/common/utils/parse';
import mongoose, { Model } from 'mongoose';
import { BlogGenerationService } from '@/blog-generation/blog-generation.service';
import { InjectModel } from '@nestjs/mongoose';
import { OrganizationResponse } from './APIResponse';
import { BlogService } from '@/blog/blog.service';
import { getPlanModel, TASK } from '@/llm/llm.models';
import { BlogSize } from '@/blog/blog.enums';
import type { PublishResult, Site } from '@/publishing/interfaces/platforms';

@Injectable()
export class LinkedinService {
  private readonly logger = new Logger(LinkedinService.name);
  private clientId: string;
  private clientSecret: string;
  private redirectUri: string;

  constructor(
    private configService: ConfigService,
    private businessService: BusinessService,
    private userService: UserService,
    private jwtService: JwtService,
    private blogService: BlogService,
    private blogGenerationService: BlogGenerationService,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
  ) {
    this.clientId = this.configService.get<string>('LINKEDIN_CLIENT_ID');
    this.clientSecret = this.configService.get<string>('LINKEDIN_CLIENT_SECRET');
    this.redirectUri = this.configService.get('BASE_URL') + '/linkedin/callback';
  }
  async fetchPersonalTimeline(bid: mongoose.Types.ObjectId): Promise<Site> {
    const { linkedinAccessToken } = await this.businessService.findOne(bid.toString());
    const profile = await this.getProfile(linkedinAccessToken);
    return {
      name: `Personal Timeline(${profile.vanityName})`,
      id: `urn:li:person:${profile.id}`,
      url: `https://linkedin.com/in/${profile.vanityName}`,
    } as const;
  }
  async fetchSites(bid: mongoose.Types.ObjectId): Promise<Site[]> {
    const { linkedinAccessToken } = await this.businessService.findOne(bid.toString());
    const profile = await this.getProfile(linkedinAccessToken);
    const organizationIds = (await this.getOrganizationUrns(linkedinAccessToken)).map((urn) =>
      urn.split(':').at(-1),
    );
    const { data } = await axios.get<OrganizationResponse>(
      `https://api.linkedin.com/rest/organizations?ids=List(${organizationIds.join(',')})`,
      {
        headers: {
          Authorization: `Bearer ${linkedinAccessToken}`,
          'LinkedIn-Version': '202410',
          'X-Restli-Protocol-Version': '2.0.0',
        },
      },
    );
    return Object.entries(data.results)
      .map(([id, { vanityName }]) => ({
        name: vanityName,
        id: `urn:li:organization:${id}`,
        url: `https://linkedin.com/company/${id}`,
      }))
      .concat([
        {
          name: `Personal Timeline(${profile.vanityName})`,
          id: `urn:li:person:${profile.id}`,
          url: `https://linkedin.com/in/${profile.vanityName}`,
        },
      ]);
  }

  async getOrganizationUrns(access_token: string) {
    // Docs: https://learn.microsoft.com/en-us/linkedin/marketing/community-management/organizations/organization-lookup-api?view=li-lms-2025-02&tabs=http#batch-get-administered-organizations
    const url = 'https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee';
    const headers = {
      Authorization: `Bearer ${access_token}`,
      'LinkedIn-Version': '202410',
      'X-Restli-Protocol-Version': '2.0.0',
    };

    try {
      const response = await axios.get<{
        paging: {
          start: number;
          count: number;
          links: Array<any>;
        };
        elements: Array<{
          roleAssignee: string;
          state: string;
          role: string;
          organizationalTarget: string;
        }>;
      }>(url, { headers });
      return response.data.elements.map(({ organizationalTarget }) => organizationalTarget);
    } catch (error) {
      this.logger.error(`Error fetching company pages: ${error.message}`);
      throw new Error('Failed to fetch company pages', { cause: error });
    }
  }

  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return !!(
      business.linkedinAccessToken &&
      business.linkedinTokenExpiry > new Date().getTime() &&
      (await this.fetchSites(bid)).length > 0
    );
  }
  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.linkedinAccessToken = undefined;
    business.linkedinTokenExpiry = undefined;
    await business.save();
  }
  async isTokenRenewable(bid: string): Promise<boolean> {
    try {
      const business = await this.businessService.findOne(bid);

      if (business.linkedinTokenExpiry < new Date().getTime()) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Token is not renewable: for ${bid}`, error?.message);
      return false;
    }
  }

  getAuthUrl(state: string, redirectTo?: string): string {
    const scopes = [
      'r_emailaddress',
      'r_liteprofile',
      'w_member_social',
      'r_ads_reporting',
      'r_organization_social',
      'rw_organization_admin',
      'r_ads',
      'w_organization_social',
      'rw_ads',
      'r_basicprofile',
      'r_organization_admin',
      'r_1st_connections_size',
    ];
    const scope = scopes.join('%20');
    return `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
      this.clientId
    }&redirect_uri=${encodeURIComponent(
      redirectTo || this.redirectUri,
    )}&state=${state}&scope=${scope}`;
  }

  async getAccessToken(code: string): Promise<string> {
    const tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    const data = {
      grant_type: 'authorization_code',
      code,
      redirect_uri: this.redirectUri,
      client_id: this.clientId,
      client_secret: this.clientSecret,
    };

    const response = await axios.post(tokenUrl, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    return response.data.access_token;
  }

  async getProfile(accessToken: string) {
    const profileUrl = 'https://api.linkedin.com/v2/me';
    const response = await axios.get<{
      localizedLastName: string;
      profilePicture: {
        displayImage: string;
      };
      firstName: {
        localized: Record<string, string>;
        preferredLocale: {
          country: string;
          language: string;
        };
      };
      vanityName: string;
      lastName: {
        localized: Record<string, string>;
        preferredLocale: {
          country: string;
          language: string;
        };
      };
      localizedMaidenName: string;
      maidenName: {
        localized: Record<string, string>;
        preferredLocale: {
          country: string;
          language: string;
        };
      };
      localizedHeadline: string;
      id: string;
      headline: {
        localized: Record<string, string>;
        preferredLocale: {
          country: string;
          language: string;
        };
      };
      localizedFirstName: string;
    }>(profileUrl, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    return response.data;
  }

  async saveLinkedinToken(token: string, accessToken: string): Promise<string> {
    if (!accessToken || !token) {
      throw new Error(`You denied the app or tokens didn't match!`);
    }

    try {
      const now = new Date().getTime();
      const expiryDate = new Date(now + 60 * 24 * 60 * 60 * 1000).getTime();
      this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(token);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }
      await this.businessService.updateBusinessLinkedinToken(payload.bid, accessToken, expiryDate);
      return this.configService.get('DASHBOARD_REDIRECT_URL') + '/dashboard/settings?success=true';
    } catch (error) {
      this.logger.error(`Error while saving linkedin token: ${error?.message}`);
      return (
        this.configService.get('DASHBOARD_REDIRECT_URL') +
        `/dashboard/settings?error=${error?.message}`
      );
    }
  }
  async summarisePost(blogId: string, bid: string, text: string) {
    const business = await this.businessService.findOne(bid);
    const { blogLanguage, blogTone, pov, sourceType, sourceName, inputLanguage } =
      await this.blogService.findById(bid, blogId);
    const model = getPlanModel(business.subscriptionPlan, TASK.SUMMARY);
    const options = {
      model,
      context: text,
      language: blogLanguage,
      tone: blogTone,
      pov,
      sourceType,
      sourceName,
      blogId,
      transcription: text,
      inputLanguage,
      blogLanguage,
      blogSize: BlogSize.mini,
    };

    return await this.blogGenerationService.generateSummary(options);
  }
  async convertToLinkedinPost(blogId: string, bid: string, html: string) {
    const text = extract.tagsText(load(html), ['p']);
    const response = await this.summarisePost(blogId, bid, text);
    const summaryText = response.content;
    const truncatedText = summaryText.slice(0, 3000);
    return truncatedText.slice(0, truncatedText.lastIndexOf('\n'));
  }

  async publish({
    bid,
    draft,
    content,
    siteID,
  }: {
    bid: mongoose.Types.ObjectId;
    draft: boolean;
    content: string;
    siteID?: string;
  }): Promise<PublishResult> {
    const business = await this.businessService.findOne(bid.toString());
    if (!business) {
      throw new UnauthorizedException('Business not found');
    }
    const text =
      content.includes('<') && content.includes('>')
        ? extract.tagsText(load(content), ['p'])
        : content;
    if (text.length > 3000) {
      throw new Error('LinkedIn API does not allow publishing content exceeding 3000 characters');
    }
    const { linkedinAccessToken: token } = business;
    const authorURN = siteID;
    try {
      const response = await axios.post(
        'https://api.linkedin.com/rest/posts', // Docs: https://learn.microsoft.com/en-us/linkedin/marketing/community-management/shares/posts-api?view=li-lms-2025-02&tabs=http#text-only-post-creation-sample-request
        {
          author: authorURN,
          lifecycleState: draft ? 'DRAFT' : 'PUBLISHED',
          commentary: text,
          visibility: 'PUBLIC',
          distribution: {
            feedDistribution: 'MAIN_FEED',
            targetEntities: [],
            thirdPartyDistributionChannels: [],
          },
          isReshareDisabledByAuthor: false,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'LinkedIn-Version': '202410',
            'X-Restli-Protocol-Version': '2.0.0',
            'Content-Type': 'application/json',
          },
        },
      );
      return {
        link: new URL(`https://www.linkedin.com/feed/update/${response?.headers?.['x-restli-id']}`),
        kind: 'success',
      };
    } catch (error) {
      const message =
        axios.isAxiosError(error) && error.response?.data?.message
          ? error.response.data.message
          : error.message;

      this.logger.error(`Error while publishing linkedin post` + message);
      return {
        error: new Error(message),
        kind: 'failure',
      };
    }
  }
}
