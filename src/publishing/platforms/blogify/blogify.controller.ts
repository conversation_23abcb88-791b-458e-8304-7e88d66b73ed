import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import {
  Val<PERSON><PERSON>P<PERSON><PERSON>,
  Controller,
  UseGuards,
  UsePipes,
  Delete,
  Param,
  Query,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import mongoose from 'mongoose';

import { BusinessGuard } from '@/auth/guards/business.guard';
import { ApiKeyGuard } from '@/auth/guards/api-key.guard';
import { RolesGuard } from '@/auth/guards/roles.guard';
import { includes } from '@/common/utils/array';
import { Auth } from '@/auth/guards/auth.guard';
import { BlogifyService } from './blogify.service';

class TimeQueryParams {
  @IsDate()
  @Type(() => Date)
  start: Date;

  @IsDate()
  @Type(() => Date)
  end: Date;
}

@Controller('blogify')
export class BlogifyController {
  constructor(private readonly blogifyService: BlogifyService) {}

  @Get('blogs')
  @UseGuards(Auth)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async fetchBlogs(@Req() { bid }: AuthenticatedRequest, @Query() { start, end }: TimeQueryParams) {
    return this.blogifyService.getPublishedBlogs(new mongoose.Types.ObjectId(bid), start, end);
  }

  @Delete(':siteId/blogs/:blogId')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async unpublishBlog(
    @Req() { bid }: AuthenticatedRequest,
    @Param('siteId') siteId: string,
    @Param('blogId') blogId: string,
  ) {
    return this.blogifyService.unpublishPost(bid, siteId, blogId);
  }

  @Post(':siteId/blogs/:blogId/toggle-visibility')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async toggleBlogVisibility(
    @Req() { bid }: AuthenticatedRequest,
    @Param('siteId') siteId: string,
    @Param('blogId') blogId: string,
  ) {
    return this.blogifyService.toggleBlogVisibility(bid, siteId, blogId);
  }

  @Post('increment-rating/:id')
  @UseGuards(ApiKeyGuard)
  async addRating(@Param('id') blogID: string, @Body('rating') rating: string) {
    if (includes(['bad', 'ok', 'nice', 'great', 'awesome'] as const, rating)) {
      await this.blogifyService.incrementRating(blogID, rating);
    }

    return { success: true };
  }
}
