import { Auth } from '@auth/guards/auth.guard';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { BusinessService } from '@business/business.service';
import {
  Controller,
  Get,
  Logger,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '@user/user.service';
import axios from 'axios';
import { Request, Response } from 'express';
import { WordpressService } from './wordpress.service';

@Controller('wordpress')
export class WordpressController {
  private readonly logger = new Logger(WordpressController.name);
  private readonly redirect_uri: string;

  constructor(
    private wordpressService: WordpressService,
    private businessService: BusinessService,
    private userService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    this.redirect_uri = this.configService.get<string>('BASE_URL') + '/wordpress/wp-connect';
  }

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth)
  async wordpressConnect(@Query('token') token: string, @Res() res: Response) {
    const redirectURL = new URL('https://public-api.wordpress.com/oauth2/authorize');

    const searchParams = new URLSearchParams({
      client_id: this.configService.get<string>('WORDPRESS_CLIENT_ID'),
      redirect_uri: this.redirect_uri,
      scope: 'global',
      response_type: 'code',
      state: token,
    });

    redirectURL.search = searchParams.toString();
    res.redirect(redirectURL.toString());
  }

  @Get('wp-connect')
  async callback(@Req() req: Request, @Res() res: Response, @Query('code') code: string) {
    let redirectURL: URL;

    try {
      redirectURL = new URL(
        '/dashboard/settings',
        this.configService.get<string>('DASHBOARD_REDIRECT_URL'),
      );
    } catch (error) {
      this.logger.error(error);
      return res.status(500).json('Could not construct redirect URL');
    }

    const jwtToken: string = req.query?.state as string;

    try {
      this.jwtService.verify(jwtToken, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(jwtToken);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }

      const { data } = await axios.post(
        'https://public-api.wordpress.com/oauth2/token',
        {
          grant_type: 'authorization_code',
          code: code,
          client_id: this.configService.get<string>('WORDPRESS_CLIENT_ID'),
          client_secret: this.configService.get<string>('WORDPRESS_CLIENT_SECRET'),
          redirect_uri: this.redirect_uri,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      const { ID } = await this.wordpressService.info(data.access_token);
      await this.businessService.updateBusinessWordpressToken(
        payload.bid,
        ID.toString(),
        data.access_token,
      );
      res.redirect(`${redirectURL}?message=Successfully connected to Wordpress.com`);
    } catch (error) {
      this.logger.error(`Wordpress callback failed: ${error?.message}`);
      res.redirect(`${redirectURL}?error=${error?.message}`);
    }
  }

  @Get('verify-wp-token')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async verifyToken(@Req() req: AuthenticatedRequest, @Res() res: Response) {
    const { bid } = req;

    try {
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }

      const data = await this.wordpressService.info(bid);
      return res.status(200).send({ valid: data && Object.keys(data).length > 0 });
    } catch (error) {
      this.logger.error(`Wordpress verification failed ${error?.message}`);
      res.status(400).send(`Wordpress verification failed ${error?.message}`);
    }
  }
}
