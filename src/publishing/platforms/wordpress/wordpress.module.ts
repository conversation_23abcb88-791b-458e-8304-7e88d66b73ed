import { <PERSON>du<PERSON> } from '@nestjs/common';
import { WordpressController } from './wordpress.controller';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtService } from '@nestjs/jwt';
import { WordpressService } from './wordpress.service';
import { JobModule } from '@job/job.module';

@Module({
  controllers: [WordpressController],
  imports: [UserModule, BusinessModule, JobModule],
  providers: [JwtService, WordpressService],
  exports: [WordpressService],
})
export class WordpressModule {}
