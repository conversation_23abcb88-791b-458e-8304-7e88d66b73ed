import { Auth } from '@/auth/guards/auth.guard';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { BusinessService } from '@/business/business.service';
import { UserService } from '@/user/user.service';
import {
  Controller,
  Get,
  Logger,
  Query,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import type { Response } from 'express';
import { MailchimpService } from './mailchimp.service';

@Controller('mailchimp')
export class MailchimpController {
  /** redirect_uri used in OAuth2 */
  private readonly redirect_uri: string;
  private readonly logger = new Logger(MailchimpController.name);

  constructor(
    private readonly mailchimpService: MailchimpService,
    private readonly configService: ConfigService,
    private businessService: BusinessService,
    private userService: UserService,
    private jwtService: JwtService,
  ) {
    try {
      this.redirect_uri = this.configService.getOrThrow<string>('BASE_URL') + '/mailchimp/callback';
    } catch (err) {
      this.logger.error(err.message);
    }
  }

  /* This endpoint is called directly by the front-end when user wishes to connect
   to his/her mailchimp account. */
  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth)
  async mailchimpConnect(@Query('token') token: string, @Res() res: Response) {
    try {
      const redirectURL = new URL('https://login.mailchimp.com/oauth2/authorize');

      const params = new URLSearchParams({
        client_id: this.configService.getOrThrow<string>('MAILCHIMP_CLIENT_ID'),
        response_type: 'code',
        redirect_uri: this.redirect_uri.toString(),
        state: token,
      });

      redirectURL.search = params.toString();
      this.logger.log(
        'Redirecting to ' + redirectURL.toString() + ' to connect to Mailchimp via OAuth2',
      );
      res.redirect(redirectURL.toString());
    } catch (err) {
      this.logger.error(err.message);
    }
  }

  // This endpoint is used as the callback for OAuth login
  @Get('callback')
  async mailchimpCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    const redirectURL = new URL(
      '/dashboard/settings',
      this.configService.getOrThrow<string>('DASHBOARD_REDIRECT_URL'),
    );

    try {
      this.jwtService.verify(state as string, {
        secret: this.configService.getOrThrow<string>('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(state as string);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }

      const response = await axios.post<{ access_token: string }>(
        'https://login.mailchimp.com/oauth2/token',
        {
          grant_type: 'authorization_code',
          code: code,
          client_id: this.configService.getOrThrow<string>('MAILCHIMP_CLIENT_ID'),
          client_secret: this.configService.getOrThrow<string>('MAILCHIMP_CLIENT_SECRET'),
          redirect_uri: this.redirect_uri,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      const { access_token: accessToken } = response.data;
      const {
        dc: mailchimpServerPrefix,
        login: { email: mailchimpUserEmail },
      } = await this.mailchimpService.getUserMetadata(accessToken);

      this.businessService.updateBusinessMailchimpToken(
        payload.bid,
        accessToken,
        mailchimpServerPrefix,
        mailchimpUserEmail,
      );
      this.logger.log(
        'Successfully saved Mailchimp user data:' + accessToken + ' for business: ' + payload.bid,
      );
      redirectURL.searchParams.append('message', 'Successfully connected to Mailchimp');
    } catch (error) {
      redirectURL.searchParams.append('error', error.message);
    }

    res.redirect(redirectURL.toString().replaceAll('+', '%20'));
  }
}
