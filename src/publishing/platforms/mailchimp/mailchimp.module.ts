import { Module } from '@nestjs/common';
import { MailchimpService } from './mailchimp.service';
import { MailchimpController } from './mailchimp.controller';
import { JwtService } from '@nestjs/jwt';
import { BusinessModule } from '@business/business.module';
import { UserModule } from '@user/user.module';

/**
 * Represents the Mailchimp Integration for Blogify. It is used to publish blogs via email using the
 * MailChimp Marketing API on behalf of users.
 * Required ENVIRONMENT variables:
 * - MAILCHIMP_CLIENT_SECRET
 * - MAILCHIMP_CLIENT_ID
 */
@Module({
  providers: [MailchimpService, JwtService],
  controllers: [MailchimpController],
  imports: [UserModule, BusinessModule],
  exports: [MailchimpService],
})
export class MailchimpModule {}
