import type { ErrorResponse } from '@mailchimp/mailchimp_marketing';
import mailchimp from '@mailchimp/mailchimp_marketing';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { BusinessService } from '@/business/business.service';
import type {
  PlatformService,
  PublishPayload,
  PublishResult,
  Site,
} from '@/publishing/interfaces/platforms';
import mongoose from 'mongoose';

const isError = (p: object): p is ErrorResponse =>
  Object.prototype.hasOwnProperty.call(p, 'status') &&
  Object.prototype.hasOwnProperty.call(p, 'title') &&
  Object.prototype.hasOwnProperty.call(p, 'type') &&
  Object.prototype.hasOwnProperty.call(p, 'detail') &&
  Object.prototype.hasOwnProperty.call(p, 'instance');

export interface FailedToFetchUserMetadata {
  error: string;
  error_description: string;
}
export interface UserMetadata {
  /** The API endpoint to call */
  api_endpoint: string;
  accountname: string;
  /** The server prefix for the user */
  dc: string;
  role: string;
  user_id: number;
  login_url: string;
  login: {
    email: string;
    avatar: any;
    login_id: number;
    login_name: string;
    login_email: string;
  };
}
/**
 * Nest Service Class for the Mailchimp Integration. It houses various functions
 * which are used to publish blogs from Mailchimp on behalf of the users.
 *
 * @remarks
 * This class is for the Mailchimp integration which publishes generated blogs.
 * It is using OAuth2 verification along with an App to operate.
 * **Not to be confused with {@link MailChimpService} (with a capital 'C' instead of a small 'C')
 * from the {@link MailModule} which relies upon the MailChimp Transctional API using Blogify's own API key to send email
 * on behalf of the Blogify website itself as opposed to as one of its users.**
 *
 */
@Injectable()
export class MailchimpService implements PlatformService<'mailchimp'> {
  private readonly logger = new Logger(MailchimpService.name);

  constructor(private readonly businessService: BusinessService) {}

  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.businessService.findOne(bid.toString());
    return (
      !!(
        business.mailchimpAccessToken &&
        business.mailchimpServerPrefix &&
        business.mailchimpUserEmail
      ) && (await this.fetchSites(bid)).length > 0
    );
  }

  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    const business = await this.businessService.findOne(bid.toString());
    business.mailchimpAccessToken = undefined;
    business.mailchimpServerPrefix = undefined;
    business.mailchimpUserEmail = undefined;
    await business.save();
  }

  /** The MailChimp Campaign folder where the campaigns through which blogs are published are stored */
  readonly campaignFolderName = 'Blogify';
  /** MailChimp API Version */
  readonly apiVersion = '3.0';
  /** MailChimp API Base URL */
  readonly getBaseUrl = (serverPrefix: string) =>
    `https://${serverPrefix}.api.mailchimp.com/${this.apiVersion}`;
  /** MailChimp URL for campaigns */
  readonly getcampaignURL = (server_prefix: string, campaign_web_id: string) =>
    `https://${server_prefix}.admin.mailchimp.com/campaigns/show/?id=${campaign_web_id}`;
  /** MailChimp URL for campaign reports */
  readonly getcampaignreportURL = (server_prefix: string, campaign_web_id: string) =>
    `https://${server_prefix}.admin.mailchimp.com/reports/summary?id=${campaign_web_id}`;

  /**
   * Fetches data about the user identified by the access token
   * @param mailchimpAccessToken - The authorization token for a user
   * @returns The details of the user
   *
   * @remarks
   * Please note that the login endpoint can only be used immediately after the user issues
   * the Access Token, it cannot be used afterwards as the Access Token might
   * expire for this endpoint, but still be valid for the Marketing API.
   */
  async getUserMetadata(mailchimpAccessToken: string): Promise<UserMetadata> {
    try {
      const response = await axios.get<UserMetadata | FailedToFetchUserMetadata>(
        'https://login.mailchimp.com/oauth2/metadata',
        {
          headers: {
            Authorization: `OAuth ${mailchimpAccessToken}`,
          },
        },
      );
      if ('error' in response.data) {
        throw new Error(response.data.error + ' ' + response.data.error_description);
      } else {
        return response.data;
      }
    } catch (error) {
      this.logger.error(
        `Error fetching user metadata for ${mailchimpAccessToken}`,
        JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Fetches the list of audiences present in a user's mailchimp account
   * @returns A list of audiences
   */
  async getAudiences() {
    try {
      const response = await mailchimp.lists.getAllLists({});
      if (isError(response)) throw new Error(JSON.stringify(response));
      else return response.lists;
    } catch (error) {
      this.logger.error(`Error fetching audiences`, JSON.stringify(error));
      throw error;
    }
  }

  async fetchSites(bid: mongoose.Types.ObjectId): Promise<Site[]> {
    const business = await this.businessService.findOne(bid.toString());
    mailchimp.setConfig({
      accessToken: business.mailchimpAccessToken,
      server: business.mailchimpServerPrefix,
    });
    const audiences = await this.getAudiences();
    const details: Site[] = audiences.map((audience) => ({
      id: audience.id,
      name: audience.name,
      url: `https://${business.mailchimpServerPrefix}.admin.mailchimp.com/lists/members/?id=${audience.web_id}`,
    }));
    return details;
  }

  /**
   * Creates a campaign folder
   * @param folderName - The name of the folder
   * @returns A list of audiences
   */
  async createCampaignFolder(folderName: string) {
    try {
      const response = await mailchimp.campaignFolders.create({ name: folderName });
      if (isError(response)) throw new Error(JSON.stringify(response));
      else return response;
    } catch (error) {
      this.logger.error(`Error creating folder ${folderName}`, JSON.stringify(error));
      throw error;
    }
  }

  /**
   * Fetches the Blogify Folder from the user's mailchimp account
   * @param client - An initialised client with BASE_URL and TOKEN set according to the user on whose behalf the request is being made
   * @returns the __Blogify folder__ where campaigns generated by Blogify are stored
   *
   * @remarks
   * If the folder does not exist, it is created.
   */
  async fetchBlogifyFolder() {
    try {
      const response = await mailchimp.campaignFolders.list();
      if (isError(response)) throw new Error(JSON.stringify(response));
      const folder = response.folders.find((folder) => folder.name === this.campaignFolderName);
      if (folder) return folder;
      else return await this.createCampaignFolder(this.campaignFolderName);
    } catch (error) {
      this.logger.error(`Error fetching 'Blogify' folder`, JSON.stringify(error));
      throw error;
    }
  }

  /**
   * Creates a new campaign in the Blogify folder of the user's account
   * @param CampaignInformation - An object containing all the required information for creating the campaign
   * @returns The newly created campaign
   */
  async createCampaign({
    audienceID,
    replyEmail,
    subject,
    campaignName,
    senderName,
    recipientName,
    folder_id,
  }: {
    audienceID: string;
    /** The email to which the recipients will reply to */
    replyEmail: string;
    /** The subject of the email */
    subject: string;
    /** The name of the campaign */
    campaignName: string;
    /** The name of the sender */
    senderName: string;
    /* The name of the recipient */
    recipientName: string;
    /** The id of the folder in which the campaign will be stored */
    folder_id: string;
  }) {
    try {
      const response = await mailchimp.campaigns.create({
        type: 'regular',
        recipients: {
          list_id: audienceID,
        },
        settings: {
          reply_to: replyEmail,
          title: `[Blogify] ${campaignName}`,
          folder_id,
          subject_line: subject,
          from_name: senderName,
          to_name: recipientName,
        },
      });

      if (isError(response)) throw new Error(JSON.stringify(response));
      return response;
    } catch (error) {
      this.logger.error(
        `Error creating campaign for audience ${audienceID}`,
        JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Sets a blog as the html content of a given campaign
   * @param campaignId - The ID of the campaign to be updated
   * @param title - The title of the blog
   * @param content - The html content of the blog
   * @param image - The url of the image to be displayed in the blog
   */
  async setCampaignContent(campaignId: string, title: string, content: string, image?: string) {
    try {
      const html = `<html>
                            <head>
                                <title>${title}</title>
                            </head>
                            <body>
                                <p>${image ? `<img src='${image}' width='800' />` : ''}<p>
                                <h1>${title}</h1>
                                ${content}
                            <body>
                        </html>`;
      const response = await mailchimp.campaigns.setContent(campaignId, { html });
      if (isError(response)) throw new Error(JSON.stringify(response));
      else return response;
    } catch (error) {
      this.logger.error(
        `Error setting campaign content for campaign ${campaignId}`,
        JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Fetches the checklist which details whether the campaign can be sent
   * @param campaignId - The ID of the campaign to be checked
   * @returns A checklist detailing whether the campaign is ready to be sent
   */
  async checkSendChecklist(campaignId: string) {
    try {
      const response = await mailchimp.campaigns.getSendChecklist(campaignId);
      if (isError(response)) throw new Error(JSON.stringify(response));
      return response;
    } catch (error) {
      this.logger.log(
        `Error checking send checklist for campaign ${campaignId}`,
        JSON.stringify(error),
      );
      throw error;
    }
  }

  /**
   * Sends a campaign to the audience specified in the campaign immediately
   * @param campaignId - The ID of the campaign to be sent
   */
  async sendCampaign(campaignId: string) {
    try {
      const response = await mailchimp.campaigns.send(campaignId);
      // Success case returns {} i.e. null
      if (response) throw new Error(JSON.stringify(response));
    } catch (error) {
      this.logger.error(`Error sending campaign ${campaignId}`, JSON.stringify(error));
      throw error;
    }
  }

  async publish({
    bid,
    title,
    content,
    draft,
    image,
    site: siteID,
  }: PublishPayload): Promise<PublishResult> {
    const business = await this.businessService.findOne(bid.toString());
    if (!business) throw new Error(`Business with ID ${bid} not found`);

    mailchimp.setConfig({
      accessToken: business.mailchimpAccessToken,
      server: business.mailchimpServerPrefix,
    });

    const audienceID = siteID;

    try {
      const folder = await this.fetchBlogifyFolder();
      const campaign = await this.createCampaign({
        audienceID,
        replyEmail: business.mailchimpUserEmail,
        subject: title,
        campaignName: title,
        senderName: 'Blogify',
        recipientName: 'Dear Reader',
        folder_id: folder.id,
      });
      await this.setCampaignContent(campaign.id, title, content, image.toString());
      if (!draft) await this.sendCampaign(campaign.id);
      return {
        link: new URL(this.getcampaignURL(business.mailchimpServerPrefix, campaign.web_id)),
        kind: 'success',
      };
    } catch (error) {
      return { error: new Error(error), kind: 'failure' };
    }
  }
}
