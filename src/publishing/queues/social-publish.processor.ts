// بسم الله الرحمن الرحيم

import { JOB_QUEUES } from '@/common/constants';
import { BaseProcessor } from '@/common/queue/base.processor';
import type { QueuePayloads } from '@/job/payloads';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { LinkedinService } from '@/publishing/platforms/linkedin/linkedin.service';
import { TwitterService } from '@/publishing/platforms/twitter/twitter.service';
import { Blog } from '@blog/blog.model';
import { InjectQueue, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import type { Job } from 'bull';
import { Queue } from 'bull';
import { type Model } from 'mongoose';

@Processor(JOB_QUEUES.PUBLISH_SOCIAL)
export class SocialPublishProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.PUBLISH_SOCIAL;
  protected readonly logger = new Logger(SocialPublishProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.PUBLISH_SOCIAL)
    publishSocialQueue: Queue<QueuePayloads['PUBLISH_SOCIAL']>,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    private readonly gatewayService: GatewayService,
    private readonly linkedinService: LinkedinService,
    private readonly twitterService: TwitterService,
  ) {
    super(publishSocialQueue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<QueuePayloads['PUBLISH_SOCIAL']>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { blogID, bid, uid, content, platform, timestamp } = job.data;

      this.logger.debug({ platform, bid, uid, timestamp, blogID }, 'Publishing to social media');
      this.gatewayService.sendBlogStatusUpdate(uid.toString(), {
        _id: blogID.toString(),
        status: 'social_publishing',
      });

      const result = await (async () => {
        switch (platform) {
          case 'x':
            return await this.twitterService.publish({
              bid,
              content,
            });
          case 'linkedin':
            return await this.linkedinService.publish({
              bid,
              draft: false,
              content,
              siteID: await this.linkedinService.fetchPersonalTimeline(bid).then(({ id }) => id),
            });
          default:
            throw new Error(`Unsupported platform: ${platform}`);
        }
      })();

      const blog = await this.blog.findById(blogID);
      const index = blog.socials.findIndex(
        (social) =>
          social.platform === platform &&
          social.timestamp.toString() === timestamp.toString() &&
          social.text === content,
      );

      blog.socials[index].outcome =
        result.kind === 'failure'
          ? { time: new Date().toUTCString(), error: result.error.message }
          : { time: new Date().toUTCString(), link: result.link.toString() };

      blog.markModified(`socials.${index}.outcome`);
      await blog.save();

      if (result.kind === 'failure') {
        throw result.error;
      }

      this.gatewayService.sendBlogStatusUpdate(uid.toString(), {
        _id: blogID.toString(),
        status: 'social_published',
      });

      this.logger.debug({ platform, bid, uid, blogID }, 'Successfully published to social media');
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(
    job: Job<QueuePayloads['PUBLISH_SOCIAL']>,
    error: Error,
  ): Promise<void> {
    const { blogID, uid } = job.data;
    this.logger.error({ err: error, blogID }, 'Social publish failed');
    this.gatewayService.sendBlogStatusUpdate(uid.toString(), {
      _id: blogID.toString(),
      status: 'social_publication_failed',
    });
  }
}
