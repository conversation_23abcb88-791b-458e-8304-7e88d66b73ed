// بسم الله الرحمن الرحيم

import { BlogPublishStatus } from '@/blog/blog.enums';
import { JOB_QUEUES } from '@/common/constants';
import { includes } from '@/common/utils/array';
import { EmbeddingSupportedBlogSources, getEmbedCode } from '@/common/utils/url';
import type { QueuePayloads } from '@/job/payloads';
import { GatewayService } from '@/modules/gateway/gateway.service';
import {
  Platforms,
  type PublishPayload,
  type PublishResult,
} from '@/publishing/interfaces/platforms';
import { Oauth2Service } from '@/publishing/oauth2/oauth2.service';
import { PlatformsService } from '@/publishing/platforms/platforms.service';
import { Blog } from '@blog/blog.model';
import { BaseProcessor } from '@common/queue/base.processor';
import { InjectQueue, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import type { Job, Queue } from 'bull';
import { load } from 'cheerio';
import { ObjectId } from 'mongodb';
import mongoose, { Model } from 'mongoose';

@Processor(JOB_QUEUES.PUBLISH_BLOG)
export class BlogPublishProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.PUBLISH_BLOG;
  protected readonly logger = new Logger(BlogPublishProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.PUBLISH_BLOG) publishBlogQueue: Queue,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    private readonly gatewayService: GatewayService,
    private readonly oauth2Service: Oauth2Service,
    private readonly platformService: PlatformsService,
  ) {
    super(publishBlogQueue);
  }

  private async embedSource(platform: string, html: string, blogID: mongoose.Types.ObjectId) {
    const blog = await this.blog.findById(blogID).exec();
    if (!includes(EmbeddingSupportedBlogSources, blog.sourceName)) {
      return html;
    }
    const $ = load(html);
    if (['wordpress', 'wordpressorg'].includes(platform)) {
      $('body').prepend(blog.url);
    } else {
      const embedCode = await getEmbedCode(blog.url, blog.sourceName);
      $('body').prepend(embedCode);
    }
    return $.html();
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<QueuePayloads['PUBLISH_BLOG']>): Promise<void> {
    return this.processWithLock(
      job,
      async ({ data: { blogID, platform, draft, author, category, site } }) => {
        const blog = await this.blog.findById(blogID).exec();
        await this.blog.updateOne({ _id: blogID }, { status: 'blog_publishing' });
        this.gatewayService.sendBlogStatusUpdate(blog.uid.toString(), {
          _id: blogID.toString(),
          status: 'blog_publishing',
        });
        const content = blog.embedSource
          ? await this.embedSource(platform, blog.content, blogID)
          : blog.content;

        const payload: PublishPayload = {
          blogID,
          bid: new ObjectId(blog.bid),
          title: blog.title,
          ...(blog.image && { image: new URL(blog.image) }),
          content,
          draft,
          site,
          author,
          category,
          keywords: blog.keywords,
          metaDescription: blog.blogOutline.metaDescription,
          metaTags: blog.blogOutline.metaTags,
          tldr: blog.blogOutline.tldr,
        };

        let promise: Promise<PublishResult>;
        if (includes(Platforms, platform)) {
          promise = this.platformService.publish(platform, payload);
        } else if (ObjectId.isValid(platform)) {
          promise = this.oauth2Service.publish(platform, payload);
        }
        const result = await promise;

        let publishResult: Blog['publishResult'][number];
        switch (result.kind) {
          case 'failure':
            publishResult = {
              integration: platform,
              siteID: site,
              outcome: { time: new Date().toUTCString(), error: result.error.message },
            };
            await this.blog.updateOne({ _id: blogID }, { $push: { publishResult: publishResult } });
            throw result.error;
          case 'success':
            publishResult = {
              integration: platform,
              siteID: site,
              outcome: { time: new Date().toUTCString(), link: result.link.toString() },
            };
            await this.blog.updateOne({ _id: blogID }, { $push: { publishResult: publishResult } });
        }

        if (result.kind === 'success' && includes(Platforms, platform)) {
          await this.blog.updateOne(
            { _id: blogID },
            {
              [`${platform}Link`]: result.link.toString(),
              [`${platform}PublishTime`]: new Date().toUTCString(),
            },
          );
        }
      },
    );
  }

  @OnQueueCompleted()
  protected async handleCompleted({ data: { blogID, draft } }: Job<QueuePayloads['PUBLISH_BLOG']>) {
    const blog = await this.blog.findById(blogID).exec();
    if (!blog) {
      return this.logger.log(`Blog with id ${blogID} not found`);
    }
    await this.blog.updateOne(
      { _id: blogID.toString() },
      {
        publishStatus: draft ? BlogPublishStatus.draft : BlogPublishStatus.published,
        status: 'blog_published',
      },
    );
    this.gatewayService.sendBlogStatusUpdate(blog.uid.toString(), {
      _id: blogID.toString(),
      status: 'blog_published',
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(
    { data: { platform, blogID } }: Job<QueuePayloads['PUBLISH_BLOG']>,
    error: Error,
  ) {
    this.logger.error({ err: error, platform }, 'Blog publish failed');
    const blog = await this.blog.findById(blogID).exec();
    if (!blog) {
      return this.logger.log(`Blog with id ${blogID} not found`);
    }
    await this.blog.updateOne({ _id: blogID }, { status: 'blog_publication_failed' });
    this.gatewayService.sendBlogStatusUpdate(blog.uid, {
      _id: blogID.toString(),
      status: 'blog_publication_failed',
    });
  }
}
