// بسم الله الرحمن الرحيم

import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import * as Permission from '@/publishing/oauth2/entities/PermissionScope';
import {
  OAuth2Guard,
  OAuth2JWTPayload,
  RequiredPermission,
} from 'src/public-api/guards/oauth2-guard';
import { PublicApiService } from './public-api.service';
import type { TokenPayload } from '@/publishing/oauth2/entities/TokenPayload';

@Controller('public-api/v1')
@UseGuards(OAuth2Guard)
export class PublicApiController {
  constructor(private readonly publicApiService: PublicApiService) {}

  @Get('me')
  @RequiredPermission(Permission.Scope.ReadProfile)
  async fetchMe(@OAuth2JWTPayload() payload: TokenPayload) {
    const user = await this.publicApiService.fetchUser(payload.uid);
    const business = await this.publicApiService.fetchBusiness(payload.sub);
    return { ...user, ...business };
  }

  @HttpCode(201)
  @Post('subscribe')
  @RequiredPermission(Permission.Scope.SubscribeBlog)
  async subscribe(
    @OAuth2JWTPayload('sub') bid: string,
    @OAuth2JWTPayload('client_id') client_id: string,
    @Body('hookUrl') webhook: string,
  ) {
    await this.publicApiService.subscribe({ bid, client_id, webhook });
    return {
      id: webhook,
      message: 'Subscribed successfully',
    };
  }

  @HttpCode(204)
  @Delete('subscribe')
  @RequiredPermission(Permission.Scope.SubscribeBlog)
  async unsubscribe(
    @OAuth2JWTPayload('sub') bid: string,
    @OAuth2JWTPayload('client_id') client_id: string,
    @Body('hookUrl') webhook: string,
  ) {
    await this.publicApiService.unsubscribe({ bid, client_id, webhook });
  }

  @Get('blogs')
  @RequiredPermission(Permission.Scope.ReadBlog)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async fetchBlogs(
    @OAuth2JWTPayload() payload: TokenPayload,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 20,
    @Query('offset', new ParseIntPipe({ optional: true })) offset = 0,
  ) {
    const blogs = await this.publicApiService.fetchBlogs(payload.sub, limit, offset);
    return {
      total: (await this.publicApiService.countBlogs(payload.sub)).total,
      data: blogs,
    };
  }

  @Get('blogs/:id')
  @RequiredPermission(Permission.Scope.ReadBlog)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async fetchBlog(@OAuth2JWTPayload('sub') bid: string, @Param('id') blog_id: string) {
    return this.publicApiService.fetchBlog(bid, blog_id);
  }
}
