// بسم الله الرحمن الرحيم

import * as Permission from '@/publishing/oauth2/entities/PermissionScope';
import type { TokenPayload } from '@/publishing/oauth2/entities/TokenPayload';
import { Oauth2Service, JWTError } from '@/publishing/oauth2/oauth2.service';
import type { CanActivate, ExecutionContext } from '@nestjs/common';
import {
  ForbiddenException,
  Injectable,
  SetMetadata,
  UnauthorizedException,
  createParamDecorator,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';

@Injectable()
export class OAuth2Guard implements CanActivate {
  constructor(
    private readonly oauth2Service: Oauth2Service,
    private readonly configService: ConfigService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    if (!request.headers['authorization']?.startsWith('Bearer ')) {
      throw new UnauthorizedException('Bearer token required in authorization header');
    }

    const token = request.headers['authorization'].split(' ')[1];

    try {
      const payload = await this.oauth2Service.decodeJWT(token);
      if (
        payload.type !== 'access_token' ||
        payload.aud !== this.configService.getOrThrow<string>('BASE_URL')
      ) {
        throw new UnauthorizedException('Invalid access token');
      }

      const requiredPermission = this.reflector.getAllAndOverride<Permission.Scope>('SCOPE', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (!Permission.hasPermission(requiredPermission, payload.scope)) {
        throw new ForbiddenException(
          `Use of this endpoint requires scope: ${Permission.encodeToString(requiredPermission)}`,
        );
      }

      if (
        !payload.public &&
        (await this.oauth2Service.fetchOauth2App(payload.client_id)).business?._id.toString() !==
          payload.sub
      ) {
        throw new ForbiddenException('This app is private and only app developers can access it');
      }

      request.oauth2JWTPayload = payload;
      return true;
    } catch (e) {
      if (e instanceof JWTError) {
        throw new UnauthorizedException(e.message);
      } else {
        throw e;
      }
    }
  }
}

export const RequiredPermission = (permission: Permission.Scope) =>
  SetMetadata('SCOPE', permission);

export const OAuth2JWTPayload = createParamDecorator(
  (data: keyof TokenPayload, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return data ? request.oauth2JWTPayload?.[data] : request.oauth2JWTPayload;
  },
);
