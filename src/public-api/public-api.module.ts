import { Module } from '@nestjs/common';
import { PublicApiService } from './public-api.service';
import { PublicApiController } from './public-api.controller';
import { BlogModule } from '@blog/blog.module';
import { BusinessModule } from '@business/business.module';
import { UserModule } from '@user/user.module';
import { Oauth2Module } from '@/publishing/oauth2/oauth2.module';

@Module({
  controllers: [PublicApiController],
  providers: [PublicApiService],
  imports: [BusinessModule, BlogModule, UserModule, Oauth2Module],
})
export class PublicApiModule {}
