import { Blog } from '@blog/blog.model';
import { BlogService } from '@blog/blog.service';
import { Business } from '@business/business.model';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from '@user/user.model';
import { Model } from 'mongoose';

@Injectable()
export class PublicApiService {
  constructor(
    @InjectModel(Business.name) private readonly business: Model<Business>,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    private readonly blogService: BlogService,
    @InjectModel(User.name) private readonly user: Model<User>,
  ) {}

  async fetchUser(id: string) {
    const user = await this.user
      .findById(id, {
        name: 1,
        email: 1,
        role: 1,
        status: 1,
        city: 1,
        country: 1,
        profilePicture: 1,
      })
      .lean();

    if (!user) {
      throw new Error('user not found');
    }

    const data = { ...user, userName: user.name };
    delete data._id;
    delete data.name;
    return data;
  }

  async fetchBusiness(id: string) {
    const business = await this.business
      .findById(id, {
        name: 1,
        website: 1,
        interests: 1,
        subscriptionPlan: 1,
        subscriptionStatus: 1,
        credits: 1,
        monthlyCredits: 1,
      })
      .lean();

    if (!business) {
      throw new Error('business not found');
    }

    const data = { ...business, businessName: business.name, currentCredits: business.credits };
    delete data._id;
    delete data.credits;
    delete data.name;
    return data;
  }

  async fetchBlog(bid: string, blog_id: string) {
    return this.blogService.findById(bid, blog_id);
  }

  async countBlogs(bid: string) {
    return this.blogService.counts(bid);
  }

  async fetchBlogs(bid: string, limit?: number, offset?: number) {
    return this.blog
      .find({ bid })
      .select({
        title: 1,
        content: 1,
        image: 1,
      })
      .skip(offset)
      .limit(limit)
      .sort({
        createdAt: 'descending',
      });
  }

  async subscribe({
    bid,
    client_id,
    webhook,
  }: {
    bid: string;
    client_id: string;
    webhook: string;
  }) {
    return await this.business.findByIdAndUpdate(
      bid,
      { $addToSet: { [`oauth2AppPublishWebhooks.${client_id}`]: webhook } },
      { new: true },
    );
  }

  async unsubscribe({
    bid,
    client_id,
    webhook,
  }: {
    bid: string;
    client_id: string;
    webhook: string;
  }) {
    return await this.business.findByIdAndUpdate(
      bid,
      { $pull: { [`oauth2AppPublishWebhooks.${client_id}`]: webhook } },
      { new: true },
    );
  }
}
