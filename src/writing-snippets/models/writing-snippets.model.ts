import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ collection: 'writing-snippets', timestamps: true })
export class WritingSnippetModel {
  @Prop({ required: true, unique: true })
  genId: string;

  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  uid: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  category: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: false })
  language: string;

  @Prop({ required: false })
  inputLanguage: string;

  @Prop({ required: false })
  tone: string;

  @Prop({ required: false })
  perspective: string;
}

export type WritingSnippetDocument = WritingSnippetModel & Document;

export const WritingSnippetSchema = SchemaFactory.createForClass(WritingSnippetModel);
