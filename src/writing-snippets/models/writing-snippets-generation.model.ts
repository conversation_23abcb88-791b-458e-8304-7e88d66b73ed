import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ collection: 'writing-snippets-generation' })
export class WritingSnippetGenerationHistory {
  @Prop({ required: true, unique: true })
  genId: string;

  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  uid: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  model: string;

  @Prop({ required: false })
  category: string;

  @Prop({ required: false })
  language: string;

  @Prop({ required: false })
  inputLanguage: string;

  @Prop({ required: false })
  perspective: string;

  @Prop({ required: false })
  tone: string;

  @Prop({ required: true })
  status: string;

  @Prop({ required: true })
  usage_token: number;

  @Prop({ required: true })
  usage_cost: number;

  @Prop({ default: Date.now })
  generationTime: Date;

  @Prop({ required: false })
  failReason: string;
}

export type WritingSnippetGenerationHistoryDocument = WritingSnippetGenerationHistory & Document;

export const WritingSnippetGenerationHistorySchema = SchemaFactory.createForClass(
  WritingSnippetGenerationHistory,
);
