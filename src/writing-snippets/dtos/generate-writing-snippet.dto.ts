import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class GenerateWritingSnippetDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public title: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public model: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public category: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public prompt: string;

  @ApiProperty()
  @IsString()
  public tone: string;

  @ApiProperty()
  @IsString()
  public language: string;

  @ApiProperty()
  @IsString()
  public inputLanguage: string;

  @ApiProperty()
  @IsString()
  public perspective: string;
}
