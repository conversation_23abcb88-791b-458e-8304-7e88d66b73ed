import type { AxiosInstance } from 'axios';

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import pRetry from 'p-retry';
import axios from 'axios';

import { generateId } from '@/common/utils';

import { WritingSnippetGenerationHistory } from './models/writing-snippets-generation.model';
import { CategoryDocument, Category } from '@resources/category/category.model';
import { GenerateWritingSnippetDto } from './dtos/generate-writing-snippet.dto';
import { WritingSnippetModel } from './models/writing-snippets.model';
import { CreateSnippetDto } from './dtos/create-snippet.dto';
import { UpdateSnippetDto } from './dtos/update-snippet.dto';
import { ListSnippetsDto } from './dtos/list-snippet.dto';

interface WritingSnippet extends WritingSnippetModel {
  categoryIcon?: string;
  categoryColor?: string;
}

@Injectable()
export class WritingSnippetsService {
  private readonly logger = new Logger(WritingSnippetsService.name);
  private blogifyML: AxiosInstance;
  private categories: CategoryDocument[];

  constructor(
    @InjectModel(WritingSnippetModel.name)
    private writingSnippetModel: Model<WritingSnippetModel>,
    @InjectModel(WritingSnippetGenerationHistory.name)
    private writingSnippetGenerationHistoryModel: Model<WritingSnippetGenerationHistory>,
    @InjectModel(Category.name) private readonly category: Model<CategoryDocument>,
    private readonly configService: ConfigService,
  ) {
    this.blogifyML = axios.create({
      baseURL: this.configService.get<string>('BLOGIFY_ML_SERVICE_URL'),
      headers: {
        'x-api-key': this.configService.get<string>('BLOGIFY_ML_API_KEY'),
      },
    });
  }

  async generate(data: GenerateWritingSnippetDto, bid: string, uid: string) {
    const generateContent = async () => {
      const response = await this.blogifyML.post('/generate-content', data);
      return response.data;
    };

    try {
      const result = await pRetry(generateContent, {
        retries: 3,
        onFailedAttempt: (error) => {
          this.logger.error(
            `writing-snippets failed to generate ${data.title} using model: ${data.model}. for bid: ${bid} and uid: ${uid}.\
            Attempt ${error.attemptNumber} failed.\
            There are ${error.retriesLeft} retries left.\
            Reason: ${error.message}`,
          );
        },
      });

      const { output, usage } = result;
      this.logger.log({ bid, usage });
      const genId = generateId();
      new this.writingSnippetGenerationHistoryModel({
        genId,
        bid,
        uid,
        status: 'success',
        usage_token: usage.total_tokens || 0,
        usage_cost: usage.total_cost,
        ...data,
      }).save();

      return {
        output,
        genId,
      };
    } catch (e) {
      this.logger.error(e.message);
      new this.writingSnippetGenerationHistoryModel({
        genId: generateId(),
        bid,
        uid,
        status: 'failed',
        usage_token: 0,
        usage_cost: 0,
        failReason: e.message,
        ...data,
      }).save();
      throw e;
    }
  }

  async createSnippet(
    createSnippetDto: CreateSnippetDto,
    uid: string,
    bid: string,
  ): Promise<WritingSnippetModel> {
    const snippetInput = await this.writingSnippetGenerationHistoryModel.findOne({
      genId: createSnippetDto.genId,
      bid,
    });
    if (!snippetInput) {
      throw new NotFoundException(`Snippet with genId ${createSnippetDto.genId} not found`);
    }

    const snippet = await this.writingSnippetModel.findOne({ genId: createSnippetDto.genId, bid });

    if (snippet) {
      return this.updateSnippet(createSnippetDto.genId, bid, {
        name: createSnippetDto.name,
        content: createSnippetDto.content,
      });
    }

    const newSnippet = new this.writingSnippetModel({
      ...createSnippetDto,
      uid,
      bid,
      ...snippetInput.toJSON(),
    });

    return newSnippet.save();
  }

  async getSnippet(genId: string, bid: string): Promise<WritingSnippet> {
    const snippet = await this.writingSnippetModel.findOne({ genId, bid });
    const snippetInput = await this.writingSnippetGenerationHistoryModel.findOne({
      genId,
      bid,
    });
    await this.initializeCategories();

    if (!snippet) {
      throw new NotFoundException(`Snippet with genId ${genId} not found`);
    }
    if (!snippetInput) {
      throw new NotFoundException(`Snippet inputs with genId ${genId} not found`);
    }
    const { categoryIcon, categoryColor } = this.getCategoryStyle(snippetInput.category);
    return {
      ...snippet.toJSON(),
      ...snippetInput.toJSON(),
      categoryIcon,
      categoryColor,
    };
  }

  async updateSnippet(
    genId: string,
    bid: string,
    updateSnippetDto: UpdateSnippetDto,
  ): Promise<WritingSnippet> {
    const snippetInput = await this.writingSnippetGenerationHistoryModel.findOne({ genId, bid });
    const snippet = await this.writingSnippetModel.findOne({ genId, bid });
    if (!snippetInput) {
      throw new NotFoundException(`Snippet inputs with genId ${genId} not found`);
    }
    if (!snippet) {
      throw new NotFoundException(`Snippet with genId ${genId} not found`);
    }

    await this.writingSnippetModel.updateOne({ genId }, updateSnippetDto);
    await this.initializeCategories();
    const { categoryIcon, categoryColor } = this.getCategoryStyle(snippetInput.category);

    return {
      genId,
      name: updateSnippetDto.name || snippet.name,
      content: updateSnippetDto.content || snippet.content,
      category: snippetInput.category,
      language: snippetInput.language,
      inputLanguage: snippetInput.inputLanguage,
      tone: snippetInput.tone,
      perspective: snippetInput.perspective,
      bid,
      uid: snippetInput.uid,
      categoryIcon,
      categoryColor,
    };
  }

  async deleteSnippet(genId: string, bid: string): Promise<void> {
    const result = await this.writingSnippetModel.deleteOne({ genId, bid });
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Snippet with genId ${genId} not found`);
    }
  }

  async listSnippets(
    listSnippetsDto: ListSnippetsDto,
    bid: string,
  ): Promise<{ snippets: WritingSnippet[]; total: number }> {
    const { page, limit, category, search } = listSnippetsDto;
    const query: any = { bid };
    if (category) {
      query.category = { $in: category.split(',') };
    }
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }
    const snippets = await this.writingSnippetModel
      .find(query)
      .limit(limit)
      .skip((page - 1) * limit)
      .exec();

    await this.initializeCategories();

    const total = await this.writingSnippetModel.countDocuments(query).exec();

    return {
      snippets: snippets.map((snippet) => {
        const { categoryIcon, categoryColor } = this.getCategoryStyle(snippet.category);

        return snippet.category
          ? {
              ...snippet.toJSON(),
              categoryIcon,
              categoryColor,
            }
          : snippet.toJSON();
      }),
      total,
    };
  }

  private async initializeCategories() {
    this.categories = await this.category
      .find({ $or: [{ deleted: false }, { deleted: null }] })
      .select({ _id: 0, __v: 0 });
  }

  private getCategoryStyle(category: string) {
    const categoryObj = this.categories.find((c) => c.name === category);
    return {
      categoryIcon: categoryObj?.iconUrl,
      categoryColor: categoryObj?.colorScheme,
    };
  }
}
