import { Modu<PERSON> } from '@nestjs/common';

import { WritingSnippetsController } from './writing-snippets.controller';
import { WritingSnippetsService } from './writing-snippets.service';
import {
  WritingSnippetGenerationHistory,
  WritingSnippetGenerationHistorySchema,
} from './models/writing-snippets-generation.model';
import { MongooseModule } from '@nestjs/mongoose';
import { WritingSnippetModel, WritingSnippetSchema } from './models/writing-snippets.model';
import { CategoryModule } from '@resources/category/category.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: WritingSnippetGenerationHistory.name,
        schema: WritingSnippetGenerationHistorySchema,
      },
      {
        name: WritingSnippetModel.name,
        schema: WritingSnippetSchema,
      },
    ]),
    CategoryModule,
  ],
  controllers: [WritingSnippetsController],
  providers: [WritingSnippetsService],
  exports: [WritingSnippetsService],
})
export class WritingSnippetsModule {}
