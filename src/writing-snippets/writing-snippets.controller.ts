import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

import {
  DefaultValuePipe,
  ValidationPipe,
  HttpException,
  ParseIntPipe,
  HttpStatus,
  Controller,
  UseGuards,
  UsePipes,
  Logger,
  Delete,
  Param,
  Query,
  Body,
  Post,
  Get,
  Put,
  Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { ValidationException } from '@common/exceptions/validationException';
import { AddonAccessGuard } from '@auth/guards/addon-access.guard';
import { Addon } from '@resources/product/product.model';
import { Auth } from '@auth/guards/auth.guard';

import { GenerateWritingSnippetDto } from './dtos/generate-writing-snippet.dto';
import { WritingSnippetsService } from './writing-snippets.service';
import { WritingSnippetModel } from './models/writing-snippets.model';
import { CreateSnippetDto } from './dtos/create-snippet.dto';
import { UpdateSnippetDto } from './dtos/update-snippet.dto';
import { ListSnippetsDto } from './dtos/list-snippet.dto';

@ApiTags('writing-snippets')
@Controller('writing-snippets')
@UsePipes(
  new ValidationPipe({
    disableErrorMessages: false,
    stopAtFirstError: true,
    exceptionFactory: (errors) => {
      const errorResponses = errors.map((error) => {
        const constraints = Object.keys(error.constraints).map((key) => error.constraints[key]);
        return { property: error.property, message: constraints.join(', ') };
      });

      return new ValidationException(errorResponses);
    },
  }),
)
export class WritingSnippetsController {
  private readonly logger = new Logger(WritingSnippetsController.name);

  constructor(private readonly writingSnippetsService: WritingSnippetsService) {}

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Post('generate')
  async generate(@Req() req: AuthenticatedRequest, @Body() dto: GenerateWritingSnippetDto) {
    const toSnakeCase = (str: string) =>
      str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);

    const dtoKeys = Object.keys(dto);
    const customProperties = Object.fromEntries(
      Object.entries(req.body)
        .filter(([key]) => !dtoKeys.includes(key))
        .map(([key, value]) => [toSnakeCase(key), value]),
    );

    try {
      const data = await this.writingSnippetsService.generate(
        {
          ...dto,
          ...customProperties,
        },
        req.bid,
        req.uid,
      );

      return data;
    } catch (e) {
      throw new HttpException(
        {
          error: `Failed to generate ${dto.title} for the inputs`,
          details: e.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Post()
  async createSnippet(
    @Req() req: AuthenticatedRequest,
    @Body() createSnippetDto: CreateSnippetDto,
  ): Promise<WritingSnippetModel> {
    try {
      return await this.writingSnippetsService.createSnippet(createSnippetDto, req.uid, req.bid);
    } catch (e) {
      throw new HttpException(
        { message: 'Failed to create snippet', details: e.message },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Get(':genId')
  async getSnippet(
    @Req() req: AuthenticatedRequest,
    @Param('genId') genId: string,
  ): Promise<WritingSnippetModel> {
    try {
      return await this.writingSnippetsService.getSnippet(genId, req.bid);
    } catch (e) {
      throw new HttpException(
        { message: 'Snippet not found', details: e.message },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Put(':genId')
  async updateSnippet(
    @Req() req: AuthenticatedRequest,
    @Param('genId') genId: string,
    @Body() updateSnippetDto: UpdateSnippetDto,
  ): Promise<WritingSnippetModel> {
    try {
      return await this.writingSnippetsService.updateSnippet(genId, req.bid, updateSnippetDto);
    } catch (e) {
      throw new HttpException(
        { message: 'Failed to update snippet', details: e.message },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Delete(':genId')
  async deleteSnippet(
    @Req() req: AuthenticatedRequest,
    @Param('genId') genId: string,
  ): Promise<void> {
    try {
      await this.writingSnippetsService.deleteSnippet(genId, req.bid);
    } catch (e) {
      throw new HttpException(
        { message: 'Failed to delete snippet', details: e.message },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(Auth, AddonAccessGuard(Addon.Snippet))
  @Get()
  async listSnippets(
    @Req() req: AuthenticatedRequest,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('category') category?: string,
    @Query('search') search?: string,
  ): Promise<{ snippets: WritingSnippetModel[]; total: number }> {
    try {
      const listSnippetsDto: ListSnippetsDto = { page, limit, category, search };
      return await this.writingSnippetsService.listSnippets(listSnippetsDto, req.bid);
    } catch (e) {
      throw new HttpException(
        { message: 'Failed to get snippets list', details: e.message },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
