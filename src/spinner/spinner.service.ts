import { Injectable } from '@nestjs/common';
import Stripe from 'stripe';
import { SpinnerItem, SpinResult } from './spinner.interface';
import config from '@/common/configs/config';

@Injectable()
export class SpinnerService {
  private stripe: Stripe;

  private readonly items: SpinnerItem[] = [
    { lgText: '10%', smText: 'Off' },
    { lgText: '20%', smText: 'Off' },
    { lgText: '30%', smText: 'Off' },
    { lgText: '40%', smText: 'Off' },
    { lgText: '50%', smText: 'Off' },
    { lgText: '60%', smText: 'Off' },
    { lgText: '70%', smText: 'Off' },
    { lgText: '80%', smText: 'Off' },
  ];
  private readonly probabilityMap = [
    { value: '10%', weight: 0.824, degrees: { start: 0, end: 296.64 } }, // 82.4% → 296.64°
    { value: '20%', weight: 0.1, degrees: { start: 296.64, end: 332.64 } }, // 10% → 36°
    { value: '30%', weight: 0.05, degrees: { start: 332.64, end: 350.64 } }, // 5% → 18°
    { value: '40%', weight: 0.01, degrees: { start: 350.64, end: 354.24 } }, // 1% → 3.6°
    { value: '50%', weight: 0.0075, degrees: { start: 354.24, end: 356.94 } }, // 0.75% → 2.7°
    { value: '60%', weight: 0.005, degrees: { start: 356.94, end: 358.74 } }, // 0.50% → 1.8°
    { value: '70%', weight: 0.0025, degrees: { start: 358.74, end: 359.64 } }, // 0.25% → 0.9°
    { value: '80%', weight: 0.001, degrees: { start: 359.64, end: 360 } }, // 0.10% → 0.36°
  ];

  constructor() {
    this.stripe = new Stripe(config().stripe.secreteKey, { apiVersion: '2022-11-15' });
  }

  private getRandomRotation(): number {
    const random = Math.random();

    let cumulativeProbability = 0;
    let selectedSegment = this.probabilityMap[0];

    for (const segment of this.probabilityMap) {
      cumulativeProbability += segment.weight;
      if (random <= cumulativeProbability) {
        selectedSegment = segment;
        break;
      }
    }

    const segmentStart = selectedSegment.degrees.start;
    const segmentEnd = selectedSegment.degrees.end;
    const randomAngle = segmentStart + Math.random() * (segmentEnd - segmentStart);

    const fullRotations = (5 + Math.floor(Math.random() * 5)) * 360;
    return fullRotations + randomAngle;
  }

  private getSelectedItem(finalRotation: number): SpinnerItem {
    const normalizedRotation = finalRotation % 360;
    let segmentIndex = Math.floor((360 - normalizedRotation) / 45) % this.items.length;
    if (segmentIndex < 0) segmentIndex += this.items.length;
    return this.items[segmentIndex];
  }

  private async generateCoupon(discountPercentage: number): Promise<{ promotionCode: string }> {
    try {
      const couponCode = `SPIN${discountPercentage}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      const expiresAt = Math.floor(Date.now() / 1000) + 24 * 60 * 60;

      await this.stripe.coupons.create({
        name: couponCode,
        id: couponCode,
        percent_off: discountPercentage,
        duration: 'once',
        max_redemptions: 1,
        redeem_by: expiresAt,
      });

      const promotionCode = await this.stripe.promotionCodes.create({
        coupon: couponCode,
        code: couponCode.replace(/[^a-zA-Z0-9-]/g, ''),
        restrictions: {
          first_time_transaction: true,
        },
      });

      return {
        promotionCode: promotionCode.code,
      };
    } catch (error) {
      console.error('Error generating coupon:', error);
      throw new Error('Failed to generate coupon');
    }
  }

  async spin(): Promise<SpinResult> {
    const rotation = this.getRandomRotation();
    const selectedItem = this.getSelectedItem(rotation);
    const discountPercentage = parseInt(selectedItem.lgText);
    const coupon = await this.generateCoupon(discountPercentage);

    return {
      rotation,
      selectedItem,
      coupon,
    };
  }
}

// private getRandomRotation(): number {
//   const totalRotations = 10;
//   return Math.floor(Math.random() * 360) + totalRotations * 360;
// }
