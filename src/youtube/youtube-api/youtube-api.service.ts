import type {
  YoutubePlaylistItemsQueryParams,
  YoutubePlaylistsQueryParams,
  YoutubeChannelsQueryParams,
  YoutubeSearchQueryParams,
  YoutubeVideosQueryParams,
  YoutubeErrorResponse,
  YoutubeQueryParams,
  YoutubeResponse,
  YoutubePlaylist,
  YoutubeChannel,
  YoutubeVideo,
} from './youtube-api.type';
import type { AxiosResponse, AxiosError } from 'axios';

import { GetListByKeyword, NextPage } from 'youtube-search-api';
import { Injectable } from '@nestjs/common';
import querystring from 'querystring';
import pRetry, { AbortError } from 'p-retry';
import axios from 'axios';

import { mapYoutubeSearchApiResponse } from '../youtube.utils';
import config from '@common/configs/config';

type Tokens = { accessToken: string; refreshToken: string };

const formatQueryParams = ({ maxResults, pageToken }: YoutubeQueryParams) => ({
  maxResults: Math.min(parseInt(maxResults as string, 10) || 50, 50),
  pageToken,
});

@Injectable()
export class YoutubeAPIService {
  private baseURL = 'https://www.googleapis.com/youtube/v3/';
  private api = axios.create({ baseURL: this.baseURL });

  async search(q: string, queryParams: YoutubeQueryParams): Promise<YoutubeResponse<YoutubeVideo>> {
    const params: YoutubeSearchQueryParams = {
      part: 'snippet',
      type: 'video',
      q,
      ...formatQueryParams(queryParams),
    };

    // const query = querystring.stringify(params as Record<string, any>);
    // return this.youtubeAPI(`search?${query}`, tokens).catch(this.handleError);
    if (params.pageToken) {
      const nextPage = JSON.parse(Buffer.from(params.pageToken, 'base64').toString());
      return mapYoutubeSearchApiResponse(await NextPage(nextPage, false, params.maxResults));
    }
    return mapYoutubeSearchApiResponse(
      await GetListByKeyword(params.q, params.maxResults, false, [{ type: 'video' }]),
    );
  }

  async getChannels(tokens: Tokens): Promise<YoutubeResponse<YoutubeChannel>> {
    const params: YoutubeChannelsQueryParams = {
      part: ['snippet', 'statistics', 'brandingSettings', 'contentDetails'],
      mine: true,
    };

    const query = querystring.stringify(params as Record<string, any>);
    return this.youtubeAPI(`channels?${query}`, tokens).catch(this.handleError);
  }

  async getPlaylists(
    channelId: string,
    queryParams: YoutubeQueryParams,
    tokens: Tokens,
  ): Promise<YoutubeResponse<YoutubePlaylist>> {
    const params: YoutubePlaylistsQueryParams = {
      part: ['snippet', 'contentDetails'],
      channelId,
      ...formatQueryParams(queryParams),
    };

    const query = querystring.stringify(params as Record<string, any>);
    return this.youtubeAPI(`playlists?${query}`, tokens).catch(this.handleError);
  }

  async getPlaylistItems(
    playlistId: string,
    queryParams: YoutubeQueryParams,
    tokens: Tokens,
  ): Promise<YoutubeResponse<any>> {
    const params: YoutubePlaylistItemsQueryParams = {
      part: ['contentDetails'],
      playlistId,
      ...formatQueryParams(queryParams),
    };

    const query = querystring.stringify(params as Record<string, any>);
    return this.youtubeAPI(`playlistItems?${query}`, tokens).catch(this.handleError);
  }

  async getVideos(
    videoIds: string[],
    queryParams: YoutubeQueryParams,
    tokens: Tokens,
  ): Promise<YoutubeResponse<YoutubeVideo>> {
    const params: YoutubeVideosQueryParams = {
      part: ['contentDetails', 'statistics', 'snippet', 'status'],
      ...(videoIds.length ? { id: `${videoIds.join(',')}` } : {}),
      ...formatQueryParams(queryParams),
    };

    const query = querystring.stringify(params as Record<string, any>);
    return this.youtubeAPI(`videos?${query}`, tokens).catch(this.handleError);
  }

  async getVideo(videoId: string, tokens: Tokens) {
    const params: YoutubeVideosQueryParams = {
      part: ['contentDetails', 'statistics', 'snippet', 'status'],
      id: videoId,
    };

    const query = querystring.stringify(params as Record<string, any>);
    return this.youtubeAPI(`videos?${query}`, tokens).catch(this.handleError);
  }

  private youtubeAPI = async (
    url: string,
    { accessToken, refreshToken }: Tokens,
    { method, body }: { method?: 'get' | 'post'; body?: any } = { method: 'get' },
  ) => {
    let newAccessToken: string;
    const getAccessToken = () => newAccessToken || accessToken;

    const request = () =>
      this.api({
        url,
        method: method,
        data: body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getAccessToken()}`,
        },
      }).then((r: AxiosResponse) => {
        if (newAccessToken) {
          return { ...r.data, newAccessToken };
        }
        return r.data;
      });

    return pRetry(
      async () => {
        try {
          const resp = await request();
          return resp;
        } catch (error) {
          if (error.response?.status === 401) {
            newAccessToken = await this.renewAccessToken(refreshToken);
            throw error;
          } else {
            throw new AbortError(error);
          }
        }
      },
      { retries: 2 },
    );
  };

  private async renewAccessToken(refreshToken: string): Promise<string> {
    return axios
      .post('https://oauth2.googleapis.com/token', {
        client_id: config().google.clientId,
        client_secret: config().google.clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      })
      .then((resp) => resp.data.access_token);
  }

  private formatError(axiosError: AxiosError<YoutubeErrorResponse>) {
    const error = axiosError.response?.data?.error;
    const message = error?.message;
    // console.log('YouTube Error:', JSON.stringify(error, null, 2));
    // console.log('message', message);
    return message || 'Something went wrong with Youtube API!';
  }

  private handleError = (error: AxiosError<YoutubeErrorResponse>) => {
    const message = this.formatError(error);
    throw new Error(message);
  };
}
