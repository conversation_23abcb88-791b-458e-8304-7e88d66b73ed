import type { YoutubeDocument } from '../youtube-data/youtube.model';
import type { YouTubeChapter } from '../youtube-content/youtube-content.interface';

type YoutubeVideoThumbnail = { url: string; width: number; height: number };

interface YoutubeBase {
  id: string;
  kind: string;
  etag: string;
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      default: YoutubeVideoThumbnail;
      standard?: YoutubeVideoThumbnail;
      medium?: YoutubeVideoThumbnail;
      high?: YoutubeVideoThumbnail;
      maxres?: YoutubeVideoThumbnail;
    };
    publishedAt: string;
    localized: { title: string; description: string };
  };
}

export interface YoutubeChannel extends YoutubeBase {
  snippet: YoutubeBase['snippet'] & { customUrl: string };
  contentDetails: {
    relatedPlaylists: {
      uploads: string;
      likes: string;
    };
  };
  statistics: {
    hiddenSubscriberCount: boolean;
    subscriberCount: string;
    videoCount: string;
    viewCount: string;
  };
}

export interface YoutubePlaylist extends YoutubeBase {
  snippet: YoutubeBase['snippet'] & {
    channelId: string;
    channelTitle: string;
  };
}

export interface YoutubeVideo extends YoutubePlaylist {
  snippet: YoutubePlaylist['snippet'] & {
    tags: string[];
    categoryId: string;
    liveBroadcastContent: string;
    defaultLanguage: string;
    defaultAudioLanguage: 'en' | string;
  };
  statistics?: {
    viewCount: string;
    likeCount: string;
    favoriteCount: string;
    commentCount: string;
  };
  contentDetails?: {
    caption?: 'true' | 'false';
    contentRating?: Record<any, any>;
    definition?: 'hd' | string;
    dimension?: '2d' | string;
    duration: string;
    licensedContent?: boolean;
    projection?: 'rectangular' | string;
  };

  // Custom: Not Provided by Youtube
  data?: YoutubeDocument & { chapters?: YouTubeChapter[] };
}

export interface YoutubeQueryParams {
  pageToken?: string;
  maxResults?: string | number;
}

export interface YoutubeSearchQueryParams extends YoutubeQueryParams {
  type: 'video' | 'playlist' | 'channel';
  part: 'snippet';
  q: string;
}

type YoutubeChannelPart =
  | 'auditDetails'
  | 'brandingSettings'
  | 'contentDetails'
  | 'contentOwnerDetails'
  | 'id'
  | 'localizations'
  | 'snippet'
  | 'statistics'
  | 'status'
  | 'topicDetails';
export interface YoutubeChannelsQueryParams extends YoutubeQueryParams {
  part: YoutubeChannelPart[];
  mine: boolean;
}

type YoutubePlaylistPart =
  | 'contentDetails'
  | 'id'
  | 'localizations'
  | 'player'
  | 'snippet'
  | 'status';
export interface YoutubePlaylistsQueryParams extends YoutubeQueryParams {
  part: YoutubePlaylistPart[];
  channelId?: string;
  mine?: boolean;
}

type YoutubePlaylistItemPart = 'contentDetails' | 'id' | 'snippet' | 'status';
export interface YoutubePlaylistItemsQueryParams extends YoutubeQueryParams {
  part: YoutubePlaylistItemPart[];
  playlistId: string;
}

type YoutubeVideoPart =
  | 'contentDetails'
  | 'fileDetails'
  | 'id'
  | 'liveStreamingDetails'
  | 'localizations'
  | 'player'
  | 'processingDetails'
  | 'recordingDetails'
  | 'snippet'
  | 'statistics'
  | 'status'
  | 'suggestions'
  | 'topicDetails';
export interface YoutubeVideosQueryParams {
  part: YoutubeVideoPart[];
  id?: string;
}

export interface YoutubeResponse<T> {
  kind: string;
  etag: string;
  nextPageToken: string;
  regionCode: 'BD' | string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: T[];

  // Custom: Not provided by Youtube;
  newAccessToken?: string;
}

export interface YoutubeErrorResponse {
  error: {
    code: number;
    message: string;
    status: 'UNAUTHENTICATED';
    errors: {
      message: string;
      domain: string;
      reason: string;
      location: string;
      locationType: string;
    }[];
  };
}

interface YoutubeSearchAPIVideo {
  id: string;
  type: 'video' | 'playlist';
  thumbnail: {
    thumbnails: YoutubeVideoThumbnail[];
  };
  title: string;
  channelTitle: string;
  shortBylineText: {
    runs: [
      {
        text: string;
        navigationEndpoint: {
          clickTrackingParams: string;
          commandMetadata: {
            webCommandMetadata: {
              url: string;
              webPageType: string;
              rootVe: number;
              apiUrl: string;
            };
          };
          browseEndpoint: {
            browseId: string;
            canonicalBaseUrl: string;
          };
        };
      },
    ];
  };
  length: {
    accessibility: {
      accessibilityData: {
        label: string;
      };
    };
    simpleText: string;
  };
  isLive: false;
}

export interface YoutubeSearchAPIResponse {
  items: YoutubeSearchAPIVideo[];
  nextPage: {
    nextPageContext: any;
    nextPageToken: any;
  };
}
