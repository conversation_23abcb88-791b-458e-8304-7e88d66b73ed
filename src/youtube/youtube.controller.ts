import type { GoogleProfile } from '@integrations/socials/google/google.type';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Controller,
  UseGuards,
  UsePipes,
  Redirect,
  Logger,
  Param,
  Query,
  Req,
  Get,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { AddonAccessGuard } from '@auth/guards/addon-access.guard';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { BusinessService } from '@business/business.service';
import { BusinessGuard } from '@auth/guards/business.guard';
import { Addon } from '@resources/product/product.model';
import { Auth } from '@auth/guards/auth.guard';
import config from '@common/configs/config';

import { YoutubeService } from './youtube.service';
import axios, { AxiosInstance } from 'axios';

@ApiBearerAuth()
@ApiTags('Youtube')
@Controller('youtube')
@UsePipes(new ValidationPipe())
export class YoutubeController {
  private readonly logger = new Logger(YoutubeController.name);
  private blogifyMedia: AxiosInstance;
  constructor(
    private readonly businessService: BusinessService,
    private readonly youtubeService: YoutubeService,
  ) {
    this.blogifyMedia = axios.create({
      baseURL: config().internalApps.blogifyMedia.url,
      headers: {
        'x-api-key': config().internalApps.blogifyMedia.apiKey,
      },
    });
  }

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth, AddonAccessGuard(Addon.YouTube), AuthGuard('google-youtube'))
  async connect() {
    return { success: true };
  }

  @Get('disconnect')
  @UseGuards(QueryTokenGuard, Auth)
  @Redirect()
  async disconnect(@Req() { bid }: AuthenticatedRequest) {
    await this.businessService.deleteTokenForPlatform(bid, 'youtube');
    return { url: `${config().internalApps.blogifyClient.url}/dashboard/youtube` };
  }

  @Get('callback')
  @UseGuards(QueryTokenGuard, Auth, AuthGuard('google-youtube'))
  @Redirect()
  async youtubeConnectCallback(@Req() { bid, user }: AuthenticatedRequest) {
    let url = `${config().internalApps.blogifyClient.url}/dashboard/youtube`;
    try {
      await this.youtubeService.youtubeConnectCallback(bid, user as unknown as GoogleProfile);
      url = `${url}?success=true`;
    } catch (error) {
      url = `${url}?error=${error?.message}`;
    }
    return { url };
  }

  @Get('search/:q')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async search(
    @Req() { bid }: AuthenticatedRequest,
    @Param('q') q: string,
    @Query('maxResults') maxResults: string,
    @Query('pageToken') pageToken: string,
  ) {
    return this.youtubeService.search(bid, q, { maxResults, pageToken });
  }

  @Get('channels')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getChannels(@Req() { bid }: AuthenticatedRequest) {
    return this.youtubeService.getChannels(bid);
  }

  @Get('channels/:channelId/playlists')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getPlaylists(
    @Req() { bid }: AuthenticatedRequest,
    @Param('channelId') channelId: string,
    @Query('maxResults') maxResults: string,
    @Query('pageToken') pageToken: string,
  ) {
    return this.youtubeService.getPlaylists(bid, channelId, { maxResults, pageToken });
  }

  @Get('playlists/:playlistId/videos')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getVideos(
    @Req() { bid }: AuthenticatedRequest,
    @Param('playlistId') playlistId: string,
    @Query('maxResults') maxResults: string,
    @Query('pageToken') pageToken: string,
  ) {
    return this.youtubeService.getVideos(bid, playlistId, { maxResults, pageToken });
  }

  @Get('videos/:videoId')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getVideo(@Req() { bid }: AuthenticatedRequest, @Param('videoId') videoId: string) {
    return this.youtubeService.getVideo(bid, videoId);
  }

  @Get('videos/:videoId/is-short')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async isShortVideo(@Param('videoId') videoId: string) {
    try {
      const response = await axios.head(`https://www.youtube.com/shorts/${videoId}`, {
        maxRedirects: 0,
      });
      return { isShort: response.status === 200 };
    } catch (e) {
      this.logger.error('Is Short Video Error', e);
      return { isShort: false };
    }
  }

  @Get('trim')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async trimVideo(
    @Query('start_time_in_seconds') start_time_in_seconds: number,
    @Query('end_time_in_seconds') end_time_in_seconds: number,
    @Query('url') url: string,
  ): Promise<{ download_url?: string; error?: string }> {
    try {
      const response = await this.blogifyMedia.post<{ download_url?: string; error?: string }>(
        '/url/clip',
        {
          url,
          start_time_in_seconds,
          end_time_in_seconds,
        },
      );

      if (response?.data?.error) {
        return { error: response.data.error };
      } else if (response?.data?.download_url) {
        return { download_url: response.data.download_url };
      } else {
        throw new Error('Something went wrong');
      }
    } catch (error) {
      this.logger.error('Trim Video Error', error);
      throw new Error('Something went wrong');
    }
  }
}
