import type {
  YoutubeSearchAPIResponse,
  YoutubeResponse,
  YoutubeVideo,
} from './youtube-api/youtube-api.type';

export const getYoutubeUrlFromId = (id: string) => `https://youtube.com/watch?v=${id}`;

export const mapYoutubeSearchApiResponse = (
  response: YoutubeSearchAPIResponse,
): YoutubeResponse<YoutubeVideo> => {
  return {
    kind: 'youtube#searchAPIResponse',
    etag: '',
    nextPageToken: Buffer.from(JSON.stringify(response.nextPage)).toString('base64'),
    regionCode: '',
    pageInfo: {
      totalResults: 0,
      resultsPerPage: 0,
    },
    items: response.items.map((item) => ({
      id: item.id,
      kind: '',
      etag: '',
      snippet: {
        title: item.title,
        description: '',
        channelId: '',
        channelTitle: item.channelTitle,
        thumbnails: {
          standard: item.thumbnail.thumbnails[0],
          medium: item.thumbnail.thumbnails[1],
          default: item.thumbnail.thumbnails[1] || item.thumbnail.thumbnails[0],
        },
        publishedAt: '',
        localized: { title: '', description: '' },
        tags: [],
        categoryId: '',
        liveBroadcastContent: '',
        defaultLanguage: '',
        defaultAudioLanguage: '',
      },
      contentDetails: {
        duration: item.length.simpleText,
      },
    })),
  };
};
