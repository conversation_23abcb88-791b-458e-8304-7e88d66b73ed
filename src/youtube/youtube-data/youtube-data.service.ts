import type { YoutubeContentDocument } from '../youtube-content/youtube-content.model';
import type { YouTubeChapter } from '../youtube-content/youtube-content.interface';
import type { YoutubeVideo } from '../youtube-api/youtube-api.type';
import type { CrudQuery } from 'src/crud/crud.interface';
import type { Model } from 'mongoose';

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { BusinessService } from '@business/business.service';
import { CrudService } from 'src/crud/crud.service';
import { groupByKey } from '@common/utils';
import { Business } from '@business/business.model';

import { YoutubeContent, CONTENT_TYPE } from '../youtube-content/youtube-content.model';
import { YoutubeAPIService } from '../youtube-api/youtube-api.service';
import { YoutubeModel } from './youtube.model';

@Injectable()
export class YoutubeDataService extends CrudService<YoutubeModel> {
  constructor(
    @InjectModel(YoutubeContent.name) private youtubeContent: Model<YoutubeContent>,
    @InjectModel(YoutubeModel.name) private youtubeModel: Model<YoutubeModel>,
    private readonly youtubeAPIService: YoutubeAPIService,
    private readonly businessService: BusinessService,
  ) {
    super(youtubeModel);
  }

  async getRecent(bid: string, query: CrudQuery) {
    const business = await this.businessService.findOne(bid);
    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again');
    }

    const youtubeData = await this.findAll(query);
    if (youtubeData.total === 0) {
      return { items: [], pageInfo: { resultsPerPage: query.limit, totalResults: 0 } };
    }

    const videoIds = youtubeData.data.map((d) => d.videoId);
    const result = await this.youtubeAPIService.getVideos(videoIds, {}, this.getTokens(business));

    if (result.newAccessToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, result.newAccessToken);
      delete result.newAccessToken;
    }

    const youtubeChapters = await this.youtubeContent
      .find({ bid, contentType: CONTENT_TYPE.CHAPTERS, videoId: { $in: videoIds } })
      .select({ contentData: 1, videoId: 1 })
      .then((data: YoutubeContentDocument[]) => groupByKey(data, 'videoId'));

    const youtubeDataByVideoId = groupByKey(youtubeData.data, 'videoId');

    result.items = result.items.map((v) => {
      v.data = youtubeDataByVideoId[v.id] as YoutubeVideo['data'];
      v.data.chapters = youtubeChapters[v.id]
        ? (youtubeChapters[v.id].contentData as YouTubeChapter[])
        : [];
      return v;
    });

    return result;
  }

  private getTokens = (business: Business) => ({
    accessToken: business.youtubeAccessToken,
    refreshToken: business.youtubeRefreshToken,
  });
}
