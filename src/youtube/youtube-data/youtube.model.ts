import type { YouTubeContentTypes } from '../youtube-content/youtube-content.model';
import type { YouTubeTranscript } from '../youtube-content/youtube-content.interface';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import {
  CONTENT_TYPE_OTHERS,
  CONTENT_TYPE_EXTRA,
  CONTENT_TYPE_POST,
  CONTENT_TYPE,
} from '../youtube-content/youtube-content.model';

const GenerateTypes = Object.values({
  ...CONTENT_TYPE_OTHERS,
  ...CONTENT_TYPE_EXTRA,
  ...CONTENT_TYPE_POST,
  ...CONTENT_TYPE,
});

@Schema({ collection: 'youtube', timestamps: true })
export class YoutubeModel {
  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  uid: string;

  @Prop({ required: true, unique: false })
  url: string;

  @Prop({ required: true, unique: false })
  videoId: string;

  @Prop({ required: true, type: Object })
  transcript: YouTubeTranscript[];

  @Prop({ required: false, default: false })
  transcriptByAsr: boolean;

  @Prop({ required: false })
  summary: string;

  @Prop({ required: false, default: 'Global English' })
  language: string;

  @Prop({ required: false })
  usage_token: number;

  @Prop({ required: false })
  usage_cost: number;

  @Prop({
    type: [String],
    required: false,
    enum: GenerateTypes,
  })
  generateTypes: YouTubeContentTypes[];

  @Prop({
    type: [String],
    required: false,
    enum: GenerateTypes,
  })
  generateTypesInProgress: YouTubeContentTypes[];
}

export type YoutubeDocument = YoutubeModel & Document;

export const YoutubeSchema = SchemaFactory.createForClass(YoutubeModel);
