import type { CrudQ<PERSON>y } from 'src/crud/crud.interface';

import { Controller, UseGuards, Headers, Query, Req, Get } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { Auth } from '@auth/guards/auth.guard';

import { YoutubeDataService } from './youtube-data.service';
import { CrudController } from 'src/crud/crud.controller';
import { YoutubeModel } from './youtube.model';

@ApiBearerAuth()
@ApiTags('Youtube')
@Controller('youtube/data')
export class YoutubeDataController extends CrudController<YoutubeModel, any, any> {
  constructor(private readonly youtubeDataService: YoutubeDataService) {
    super(youtubeDataService);
  }

  @Get('recent')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async getRecent(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ) {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid };
      query.sort = ['updatedAt,DESC'];
      query.limit = query.limit || 50;
    }
    return this.youtubeDataService.getRecent(bid, query);
  }
}
