import type {
  GenerateSocialPostResponse,
  GenerateSocialPostRequest,
  GenerateThumbnailRequest,
  GenerateContentsResponse,
  GenerateContentsRequest,
  GenerateSummaryResponse,
  GenerateSummaryRequest,
  GenerateShortsRequest,
  GenerateShortsResponse,
  YouTubeMLErrorResponse,
} from './youtube-ml.interface';
import type { AxiosResponse, AxiosError } from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import pRetry from 'p-retry';
import axios from 'axios';

import config from '@common/configs/config';

@Injectable()
export class YoutubeMLService {
  private readonly logger = new Logger(YoutubeMLService.name);
  private api = axios.create({
    baseURL: config().internalApps.blogifyML.url,
    headers: {
      'x-api-key': config().internalApps.blogifyML.apiKey,
    },
  });

  async generateContents(body: GenerateContentsRequest): Promise<GenerateContentsResponse> {
    return this.blogifyML('/generate-youtube-data', { body, type: 'content' }).catch(
      this.handleError,
    );
  }

  async generateSummary(body: GenerateSummaryRequest): Promise<GenerateSummaryResponse> {
    return this.blogifyML('/generate-summary', { body, type: 'summary' }).catch(this.handleError);
  }

  async generateThumbnail(body: GenerateThumbnailRequest): Promise<string> {
    return this.blogifyML('/generate-image', { body, type: 'thumbnail' }).catch(this.handleError);
  }

  async generateSocialPosts({
    includeLink,
    ...body
  }: GenerateSocialPostRequest): Promise<GenerateSocialPostResponse> {
    body.platform = body.platform.split('_')[2] as GenerateSocialPostRequest['platform'];
    body.url = includeLink ? body.url : '';
    return this.blogifyML('/generate-social-post', { body, type: 'post' }).catch(this.handleError);
  }

  async generateShorts(body: GenerateShortsRequest): Promise<GenerateShortsResponse> {
    return this.blogifyML('/generate-shorts', { body, type: 'shorts' }).catch(this.handleError);
  }

  private blogifyML = async (
    url: string,
    {
      body,
      type,
    }: {
      body?: any;
      type?: 'content' | 'summary' | 'post' | 'thumbnail' | 'shorts';
    },
  ) => {
    const request = () =>
      this.api({ url, method: 'post', data: body }).then((r: AxiosResponse) => r.data);

    return pRetry(request, {
      retries: 3,
      onFailedAttempt: (e: { attemptNumber: number; retriesLeft: number }) => {
        this.logger.error(
          `Attempt ${e.attemptNumber} failed to generate ${type}. There are ${e.retriesLeft} retries left.`,
        );
      },
    });
  };

  private formatError(axiosError: AxiosError<YouTubeMLErrorResponse>) {
    const error = axiosError.response?.data;
    const detail = error?.detail;
    let message = '';
    if (Array.isArray(detail)) {
      detail.forEach((d) => (message += [d.msg, ...d.loc].join(',')));
    } else if (typeof detail === 'string') {
      message = detail;
    } else {
      message = JSON.stringify(error);
    }
    return message || 'Something went wrong with Blogify ML API!';
  }

  private handleError = (error: AxiosError<YouTubeMLErrorResponse>) => {
    // console.log('Blogify ML Error:', error);
    const message = this.formatError(error);
    throw new Error(message);
  };
}
