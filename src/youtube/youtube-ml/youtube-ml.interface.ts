import type {
  YouTubeTranscript,
  YouTubeChapter,
  YouTubeShorts,
} from '../youtube-content/youtube-content.interface';
import type {
  YouTubeContentTypes,
  CONTENT_TYPE_POST,
} from '../youtube-content/youtube-content.model';

interface Usage {
  successful_requests: number;
  total_cost: number;
  total_tokens: number;
  prompt_tokens: number;
  completion_tokens: number;
}

export interface GenerateContentsRequest {
  types: YouTubeContentTypes[];
  summary: string;
  transcript: YouTubeTranscript[];
  language: string;
  tone?: string;
}
export interface GenerateContentsResponse {
  data: {
    title: string[];
    description: string;
    chapters: YouTubeChapter[];
    tag: string[];
    hashtag: string[];
  };
  usage: Usage;
}

export interface GenerateSummaryRequest {
  title: string;
  transcript: YouTubeTranscript[];
}
export interface GenerateSummaryResponse {
  summary: string;
  usage: {
    usage_cost: number;
    usage_tokens: number;
  };
}

export interface GenerateThumbnailRequest {
  prompt: string;
  style: string;
}

export interface GenerateSocialPostRequest {
  url: string;
  includeLink: boolean;
  platform: CONTENT_TYPE_POST;
  summary: string;
  language: string;
  tone: string;
}
export interface GenerateSocialPostResponse {
  output: string;
  usage: Usage;
}

export interface GenerateShortsResponse {
  shorts: YouTubeShorts[];
  usage: Usage;
}

export interface GenerateShortsRequest {
  transcript: YouTubeTranscript[];
  language: string;
}

export interface YouTubeMLErrorResponse {
  detail?:
    | string
    | {
        type: 'missing' | string;
        url: string;
        msg: string;
        input: [];
        loc: [];
      }[];
}
