import type {
  YouTube<PERSON>ontentOverview,
  YouTubeTranscript,
  YouTubeContents,
} from './youtube-content.interface';
import type { YoutubeContent } from './youtube-content.model';
import type { Request } from '@auth/interfaces/authenticated-request.interface';

import {
  UnprocessableEntityException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Logger,
  Delete,
  Param,
  Post,
  Body,
  Req,
  Get,
  Put,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { RolesGuard, Roles } from '@auth/guards/roles.guard';
import { AddonAccessGuard } from '@auth/guards/addon-access.guard';
import { BusinessGuard } from '@auth/guards/business.guard';
import { Addon } from '@resources/product/product.model';
import { Auth } from '@auth/guards/auth.guard';

import {
  GenerateMoreContentsDto,
  GenerateSocialPostsDto,
  GenerateTranscriptDto,
  GenerateThumbnailDto,
  GenerateContentsDto,
  GenerateShortsDto,
} from './dto/youtube-dto';
import { REGENERABLE_CONTENT_TYPES } from './youtube-content.constants';
import { YoutubeContentService } from './youtube-content.service';
import { CONTENT_TYPE_OTHERS } from './youtube-content.model';
import { getYoutubeUrlFromId } from '../youtube.utils';

@ApiBearerAuth()
@ApiTags('Youtube Contents')
@Controller('youtube/:videoId/contents')
@UsePipes(new ValidationPipe())
export class YoutubeContentController {
  private readonly logger = new Logger(YoutubeContentController.name);

  constructor(private readonly youtubeContentService: YoutubeContentService) {}

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getContents(
    @Req() { bid }: Request,
    @Param('videoId') videoId: string,
  ): Promise<YouTubeContents> {
    const data = await this.youtubeContentService.getContents(bid, videoId);
    return data;
  }

  @Get('overview')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getContentsOverview(
    @Req() { bid }: Request,
    @Param('videoId') videoId: string,
  ): Promise<YouTubeContentOverview> {
    return this.youtubeContentService.getContentsOverview(bid, videoId);
  }

  @Get(':type')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async getContentsByType(
    @Req() { bid }: Request,
    @Param('videoId') videoId: string,
    @Param('type') type: CONTENT_TYPE_OTHERS,
  ): Promise<YoutubeContent[]> {
    if (!Object.values(CONTENT_TYPE_OTHERS).includes(type)) {
      throw new UnprocessableEntityException('Invalid content type provided.');
    }

    return this.youtubeContentService.getContentsByType(bid, videoId, type);
  }

  @Post()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async generateContents(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() body: GenerateContentsDto,
  ): Promise<{ success: boolean }> {
    const data = await this.youtubeContentService.generateContents(req, videoId, body);
    return data;
  }

  @Post('generate-more')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async generateMore(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() { contentType }: GenerateMoreContentsDto,
  ): Promise<{ success: boolean }> {
    if (!REGENERABLE_CONTENT_TYPES.includes(contentType)) {
      throw new UnprocessableEntityException('Can not regenerate the provided content type.');
    }

    const data = await this.youtubeContentService.generateMore(req, videoId, contentType);
    return data;
  }

  @Put(':id/favorite')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async setFavorite(
    @Req() { bid }: Request,
    @Param('videoId') videoId: string,
    @Param('id') id: string,
  ): Promise<{ success: boolean }> {
    const url = getYoutubeUrlFromId(videoId);
    await this.youtubeContentService.setFavorite(bid, url, id);
    return { success: true };
  }

  @Delete(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async deleteContent(
    @Req() { bid }: Request,
    // @Param('videoId') videoId: string,
    @Param('id') id: string,
  ): Promise<{ success: boolean }> {
    await this.youtubeContentService.deleteContent(bid, id);
    return { success: true };
  }

  @Post('transcribe')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async generateTranscript(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() generateTranscriptDto: GenerateTranscriptDto,
  ): Promise<YouTubeTranscript[]> {
    const url = getYoutubeUrlFromId(videoId);
    const { language } = generateTranscriptDto;
    try {
      const transcript = await this.youtubeContentService.getTranscript(req, url, language);
      return transcript;
    } catch (e) {
      this.logger.error(`Failed to generate transcript for url: ${url} bid: ${req.bid}`, e);
      throw e;
    }
  }

  @Post('thumbnails')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async generateThumbnails(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() dto: GenerateThumbnailDto,
  ): Promise<{ success: boolean }> {
    return this.youtubeContentService.generateThumbnails(req, videoId, dto);
  }

  @Post('posts')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTube))
  async generatePosts(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() body: GenerateSocialPostsDto,
  ): Promise<{ success: boolean }> {
    return this.youtubeContentService.generatePosts(req, videoId, body);
  }

  @Post('shorts')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async generateShorts(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Body() body: GenerateShortsDto,
  ): Promise<{ success: boolean }> {
    return this.youtubeContentService.generateShorts(req, videoId, body);
  }

  @Put('shorts/:id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, AddonAccessGuard(Addon.YouTubePro))
  async processShorts(
    @Req() req: Request,
    @Param('videoId') videoId: string,
    @Param('id') id: string,
  ): Promise<{ success: boolean }> {
    try {
      const data = await this.youtubeContentService.processShorts(req, videoId, id);
      this.logger.log(`Processed and generated download link for youtube shorts: ${videoId}`);
      return data;
    } catch (e) {
      this.logger.error(
        `Failed to process and generate download link for youtube shorts: ${videoId} bid: ${req.bid}`,
        e,
      );
      throw e;
    }
  }
}
