import type { <PERSON><PERSON>hapter, YouTubeShorts } from './youtube-content.interface';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

// Part of Generate Content API
export enum CONTENT_TYPE {
  TITLE = 'title',
  DESCRIPTION = 'description',
  TAG = 'tag',
  HASHTAG = 'hashtag',
  CHAPTERS = 'chapters',
}

// Part of Youtube Model
export enum CONTENT_TYPE_EXTRA {
  SUMMARY = 'summary',
  TRANSCRIPT = 'transcript',
}

// Part of Individual Generation API
export enum CONTENT_TYPE_OTHERS {
  SOCIAL_POST = 'social-post',
  THUMBNAIL = 'thumbnail',
  SHORT = 'short',
}

// Part of Generate Post API
export enum CONTENT_TYPE_POST {
  SOCIAL_POST_FACEBOOK = 'social_post_facebook',
  SOCIAL_POST_TWITTER = 'social_post_twitter',
  SOCIAL_POST_LINKEDIN = 'social_post_linkedin',
}

export type YouTubeContentTypes =
  | CONTENT_TYPE_OTHERS
  | CONTENT_TYPE_EXTRA
  | CONTENT_TYPE_POST
  | CONTENT_TYPE;

@Schema({ collection: 'youtube-contents', timestamps: true })
export class YoutubeContent {
  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  url: string;

  @Prop({ type: String, required: true, unique: false })
  videoId: string;

  @Prop({
    type: String,
    required: true,
    enum: [...Object.values({ ...CONTENT_TYPE, ...CONTENT_TYPE_POST, ...CONTENT_TYPE_OTHERS })],
  })
  contentType: CONTENT_TYPE | CONTENT_TYPE_OTHERS | CONTENT_TYPE_POST;

  @Prop({ type: SchemaTypes.Mixed, default: '' })
  contentData?: string | YouTubeShorts | YouTubeChapter[];

  @Prop({
    type: String,
    default: 'generated',
    enum: ['generating', 'generated', 'generation_failed'],
  })
  status?: 'generating' | 'generated' | 'generation_failed';

  @Prop({ type: Boolean })
  isFavorite?: boolean;

  @Prop({ type: Boolean })
  isDeleted?: boolean;

  @Prop({ type: String })
  failReason?: string;

  @Prop({ type: String })
  prompt?: string;

  /* Content Specific Properties */

  // Thumbnail
  @Prop({ type: String })
  style?: string;

  // Social Posts
  @Prop({ type: String })
  language?: string;

  @Prop({ type: String })
  tone?: string;

  @Prop({ type: Boolean })
  includeLink?: boolean;
}

export type YoutubeContentDocument = YoutubeContent & Document;

export const YoutubeContentSchema = SchemaFactory.createForClass(YoutubeContent);
