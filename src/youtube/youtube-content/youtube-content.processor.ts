import type {
  GenerateSocialPostsDto,
  GenerateThumbnailDto,
  GenerateShortsDto,
} from './dto/youtube-dto';
import type { YouTubeTranscript, YouTubeShorts } from './youtube-content.interface';
import type { Model } from 'mongoose';
import type { Job } from 'bull';

import { OnQueueCompleted, OnQueueFailed, Processor, Process } from '@nestjs/bull';
import { InjectModel } from '@nestjs/mongoose';
import { isURL } from 'class-validator';

import { BlogifyMediaService } from 'src/blogify-media/blogify-media.service';
import { NotificationService } from '@resources/notification/notification.service';
import { convertToSeconds } from '@common/helpers';
import { GatewayService } from '@modules/gateway/gateway.service';
import { JOB_QUEUES } from '@common/constants';
import { capitalize } from '@common/utils';

import { CONTENT_TYPE_OTHERS, YoutubeContent } from './youtube-content.model';
import { YoutubeContentService } from './youtube-content.service';
import { getYoutubeUrlFromId } from '../youtube.utils';
import { YoutubeMLService } from '../youtube-ml/youtube-ml.service';
import { CONTENT_PAGES } from './youtube-content.constants';
import { YoutubeModel } from '../youtube-data/youtube.model';
import { Logger } from '@nestjs/common';

interface YouTubeContentQueuePayload {
  bid: string;
  uid: string;
  videoId: string;
  contentId: string;
  content: YoutubeContent;
}

interface YouTubeContentQueueReturnValue {
  contentData: string | YouTubeShorts | YouTubeShorts[];
  usage_cost?: number;
  usage_token?: number;
  notification?: {
    title?: string;
    description?: string;
  };
}

const titles = {
  social_post_facebook: 'Facebook Post',
  social_post_twitter: 'Twitter Post',
  social_post_linkedin: 'LinkedIn Post',
};

@Processor(JOB_QUEUES.YOUTUBE_CONTENT)
export class YouTubeContentProcessor {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    @InjectModel(YoutubeContent.name) private youtubeContent: Model<YoutubeContent>,
    @InjectModel(YoutubeModel.name) private youtubeModel: Model<YoutubeModel>,
    private readonly youtubeContentService: YoutubeContentService,
    private readonly blogifyMediaService: BlogifyMediaService,
    private readonly notificationService: NotificationService,
    private readonly youtubeMLService: YoutubeMLService,
    private readonly gatewayService: GatewayService,
  ) {}

  @Process({ name: CONTENT_TYPE_OTHERS.THUMBNAIL, concurrency: 10 })
  async generateYouTubeThumbnail(
    job: Job<YouTubeContentQueuePayload & GenerateThumbnailDto>,
  ): Promise<YouTubeContentQueueReturnValue> {
    const { prompt, style } = job.data;

    const imageUrl = await this.youtubeMLService.generateThumbnail({ prompt, style });

    if (!imageUrl || !isURL(imageUrl)) {
      throw new Error('Invalid image url generated please try again');
    }

    return { contentData: imageUrl };
  }

  @Process({ name: CONTENT_TYPE_OTHERS.SHORT, concurrency: 10 })
  async generateYouTubeShort(
    job: Job<YouTubeContentQueuePayload & GenerateShortsDto>,
  ): Promise<YouTubeContentQueueReturnValue> {
    const { clipTimeSpan, language, videoId, bid, uid } = job.data;
    const userIds = { bid, uid };
    const url = getYoutubeUrlFromId(videoId);

    let transcript: YouTubeTranscript[];
    try {
      transcript = await this.youtubeContentService.getTranscript(userIds, url, language);
    } catch (e) {
      this.logger.error('Transcript generation failed.', e);
      throw new Error('Transcript generation failed.');
    }

    if (!transcript || !transcript.length) {
      throw new Error(`Couldn't find transcription for this video.`);
    }

    // Filter transcript by clipTimeSpan based on startTime and endTime
    const [startTime, endTime] = (clipTimeSpan || '').split(':');
    if (startTime && endTime) {
      transcript = transcript.filter(
        (c) =>
          convertToSeconds(c.startTime) >= Number(startTime) &&
          convertToSeconds(c.endTime) <= Number(endTime),
      );
    }

    const response = await this.youtubeMLService.generateShorts({ language, transcript });

    if (!response) {
      throw new Error(`Invalid youtube shorts generated please try again.`);
    }

    return {
      contentData: response.shorts,
      usage_cost: response.usage?.total_cost || 0,
      usage_token: response.usage?.total_tokens || 0,
    };
  }

  @Process({ name: `prepare-short`, concurrency: 10 })
  async prepareYouTubeShort(
    job: Job<YouTubeContentQueuePayload & GenerateShortsDto>,
  ): Promise<YouTubeContentQueueReturnValue> {
    const { videoId, content } = job.data;
    const { startTime, endTime } = content.contentData as YouTubeShorts;

    // Start and end time is in minute:second format
    const start = convertToSeconds(startTime, ':');
    const end = convertToSeconds(endTime, ':');
    const url = getYoutubeUrlFromId(videoId);
    const shortsUrl = await this.blogifyMediaService.createShorts(url, start, end);

    if (!shortsUrl) {
      throw new Error(`Failed to process shorts and create downloadable url.`);
    }

    return {
      contentData: {
        ...(content.contentData as YouTubeShorts),
        downloadUrl: shortsUrl,
        downloadUrlExpiry: Date.now() + 6 * 24 * 60 * 60 * 1000,
      },
      notification: {
        title: 'YouTube Shorts',
        description: 'Your YouTube Short is ready to download.',
      },
    };
  }

  @Process({ name: CONTENT_TYPE_OTHERS.SOCIAL_POST, concurrency: 10 })
  async generateYouTubePosts(
    job: Job<YouTubeContentQueuePayload & GenerateSocialPostsDto>,
  ): Promise<YouTubeContentQueueReturnValue> {
    const { includeLink, language, platform, videoId, prompt, tone, bid, uid } = job.data;
    const userIds = { bid, uid };
    const url = getYoutubeUrlFromId(videoId);

    let transcript: YouTubeTranscript[];
    try {
      transcript = await this.youtubeContentService.getTranscript(userIds, url, language);
    } catch (e) {
      this.logger.error('Transcript generation failed.', e);
      throw new Error('Transcript generation failed.');
    }

    let summary: string;
    try {
      summary = await this.youtubeContentService.getSummary(userIds, url, transcript);
    } catch (e) {
      this.logger.error('Summary generation failed.', e);
      throw new Error('Summary generation failed.');
    }

    const payload = {
      includeLink,
      platform,
      language,
      summary,
      prompt,
      tone,
      url: `https://youtu.be/${videoId}`,
    };
    const response = await this.youtubeMLService.generateSocialPosts(payload);

    if (!response) {
      throw new Error(`Invalid ${platform} post generated please try again`);
    }

    const { output: contentData, usage } = response;

    return {
      contentData,
      usage_cost: usage?.total_cost || 0,
      usage_token: usage?.total_tokens || 0,
    };
  }

  @OnQueueCompleted()
  async onComplete(job: Job<YouTubeContentQueuePayload>) {
    const { contentData, usage_token, usage_cost } = job.returnvalue;
    const { bid, uid, videoId, contentId } = job.data;
    const contentType = job.name;

    let content = { contentType } as YoutubeContent;
    if (job.name === 'short') {
      const shorts = contentData as YouTubeShorts[];
      const bulkContent: YoutubeContent[] = [];

      if (shorts && shorts.length) {
        shorts.forEach((short) => {
          bulkContent.push({
            contentType: CONTENT_TYPE_OTHERS.SHORT,
            url: getYoutubeUrlFromId(videoId),
            contentData: short,
            videoId,
            bid,
          });
        });

        await this.youtubeContent.insertMany(bulkContent);
      }
    } else {
      content = await this.youtubeContent.findByIdAndUpdate(contentId, {
        status: 'generated',
        contentData,
      });
    }

    if (contentType !== 'prepare-short') {
      await this.youtubeModel.findOneAndUpdate(
        { bid, videoId },
        {
          $pull: { generateTypesInProgress: contentType },
          $addToSet: { generateTypes: contentType },
          $inc: {
            usage_token: usage_token || 0,
            usage_cost: usage_cost || 0,
          },
        },
      );
    }

    const { notification: jobNotification } = job.returnvalue as YouTubeContentQueueReturnValue;
    const notification = {
      title: jobNotification?.title || `${capitalize(titles[contentType] || contentType)}`,
      description:
        jobNotification?.description ||
        `${capitalize(titles[contentType] || contentType)} generated successfully.`,
      actionTitle: 'View',
      actionLink: `/dashboard/youtube/videos/${videoId}/${CONTENT_PAGES[contentType]}`,
      business: bid,
      user: uid,
    };

    return this.notificationService.sendYouTubeContentUpdate({ bid, uid }, content, notification);
  }

  @OnQueueFailed()
  async onFailed(job: Job<any>, error: Error) {
    const { bid, uid, videoId, contentId } = job.data;
    const contentType = job.name;

    if (contentType !== 'prepare-short') {
      await this.youtubeModel.findOneAndUpdate(
        { bid, videoId },
        { $pull: { generateTypesInProgress: contentType } },
      );
    }

    let content = { contentType } as YoutubeContent;

    if (contentId) {
      content = await this.youtubeContent.findByIdAndUpdate(contentId, {
        status: 'generation_failed',
        failReason: error.message,
      });
    }

    return this.gatewayService.sendYouTubeContentUpdate(uid, content);
  }
}
