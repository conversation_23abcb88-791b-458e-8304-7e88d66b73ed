import type { YoutubeContent } from './youtube-content.model';

export interface YouTubeTranscript {
  startTime: number;
  endTime: number;
  text: string;
  speaker?: string;
}

export interface YouTubeChapter {
  title: string;
  timestamp: string;
}

export interface YouTubeShorts {
  startTime: number;
  endTime: number;
  title: string;
  description: string;
  downloadUrl?: string;
  downloadUrlExpiry?: number;
}

export interface YouTubeContentOverview {
  thumbnail: string;
  title: string;
  description: string;
  chapters: YouTubeChapter[];
  tags: string[];
  hashtags: string[];
}

export interface YouTubeContents {
  titles?: YoutubeContent[];
  descriptions?: YoutubeContent[];
  chapters?: YouTubeChapter[];
  tags?: YoutubeContent[];
  hashtags?: YoutubeContent[];

  // TODO: Move to separate API
  summary?: string;
  transcript?: YouTubeTranscript[];
  thumbnails?: YoutubeContent[];
  socialPosts?: YoutubeContent[];
  shorts?: YoutubeContent[];
}
