import type {
  YouTubeContentOverview,
  YouTubeTranscript,
  YouTubeContents,
} from './youtube-content.interface';
import type { GenerateContentsResponse } from '../youtube-ml/youtube-ml.interface';
import type { YouTubeChapter } from './youtube-content.interface';
import type { Queue } from 'bull';

import {
  UnprocessableEntityException,
  UnauthorizedException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { getURLVideoID } from '@distube/ytdl-core';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { ObjectId } from 'mongodb';
import { Model } from 'mongoose';

import { BlogifyMediaService } from 'src/blogify-media/blogify-media.service';
import { TranscribeService } from '@transcribe/transcribe.service';
import { BusinessService } from '@business/business.service';
import { mongoSelect } from '@common/utils/db';
import { JOB_QUEUES } from '@common/constants';

import {
  CONTENT_TYPE_OTHERS,
  CONTENT_TYPE_EXTRA,
  CONTENT_TYPE_POST,
  YoutubeContent,
  CONTENT_TYPE,
} from './youtube-content.model';
import {
  GenerateSocialPostsDto,
  GenerateThumbnailDto,
  GenerateContentsDto,
  GenerateShortsDto,
} from './dto/youtube-dto';
import { CONTENT_COUNT_MAX_PER_TYPE } from './youtube-content.constants';
import { getYoutubeUrlFromId } from '../youtube.utils';
import { YoutubeMLService } from '../youtube-ml/youtube-ml.service';
import { YoutubeModel } from '../youtube-data/youtube.model';

type UserIds = { bid: string; uid: string };

// Helper Functions
const isContentType = (type: CONTENT_TYPE | CONTENT_TYPE_OTHERS) => (content: YoutubeContent) =>
  type === content.contentType;

@Injectable()
export class YoutubeContentService {
  private readonly logger = new Logger(YoutubeContentService.name);

  constructor(
    @InjectModel(YoutubeContent.name) private youtubeContent: Model<YoutubeContent>,
    @InjectModel(YoutubeModel.name) private youtubeModel: Model<YoutubeModel>,
    @InjectQueue(JOB_QUEUES.YOUTUBE_CONTENT) private youtubeTasksQueue: Queue,
    private readonly blogifyMediaService: BlogifyMediaService,
    private readonly transcribeService: TranscribeService,
    private readonly youtubeMLService: YoutubeMLService,
    private readonly businessService: BusinessService,
  ) {}

  async getContents(bid: string, videoId: string): Promise<YouTubeContents> {
    const contents = await this.youtubeContent.find({
      bid,
      videoId,
      contentType: { $in: Object.values(CONTENT_TYPE) },
    });
    // if (!contents.length) return {};

    const titles = contents.filter(isContentType(CONTENT_TYPE.TITLE));
    const descriptions = contents.filter(isContentType(CONTENT_TYPE.DESCRIPTION));
    const tags = contents.filter(isContentType(CONTENT_TYPE.TAG));
    const hashtags = contents.filter(isContentType(CONTENT_TYPE.HASHTAG));

    const chapters = (contents.find((content) => content.contentType === CONTENT_TYPE.CHAPTERS)
      ?.contentData || []) as YouTubeChapter[];

    // TODO: Move to separate API
    const entry = await this.youtubeModel.findOne({ bid, videoId });
    const summary = entry?.summary || '';

    let transcript: YouTubeTranscript[] = [];
    if (entry?.generateTypes.includes(CONTENT_TYPE_EXTRA.TRANSCRIPT)) {
      transcript = entry.transcript
        ? entry.transcript
        : (await this.youtubeModel.findOne({ videoId }))?.transcript;
    }

    return {
      titles,
      descriptions,
      chapters,
      tags,
      hashtags,
      summary: entry?.generateTypes.includes(CONTENT_TYPE_EXTRA.SUMMARY) ? summary : '',
      transcript,
    };
  }

  async getContentsOverview(bid: string, videoId: string): Promise<YouTubeContentOverview> {
    const contents = await this.youtubeContent
      .find({
        bid,
        videoId,
        isDeleted: { $in: [false, null] },
        isFavorite: { $in: [true, null] },
        contentType: { $in: [...Object.values(CONTENT_TYPE), CONTENT_TYPE_OTHERS.THUMBNAIL] },
      })
      .select(mongoSelect(['contentType', 'contentData']));

    const title = (contents.find(isContentType(CONTENT_TYPE.TITLE))?.contentData || '') as string;
    const description = (contents.find(isContentType(CONTENT_TYPE.DESCRIPTION))?.contentData ||
      '') as string;
    const tags = contents
      .filter(isContentType(CONTENT_TYPE.TAG))
      .map((c) => c.contentData) as string[];
    const hashtags = contents
      .filter(isContentType(CONTENT_TYPE.HASHTAG))
      .map((c) => c.contentData) as string[];

    const chapters = (contents.find((content) => content.contentType === CONTENT_TYPE.CHAPTERS)
      ?.contentData || []) as YouTubeChapter[];

    const thumbnail = (contents.find(isContentType(CONTENT_TYPE_OTHERS.THUMBNAIL))?.contentData ||
      '') as string;

    return { title, description, tags, hashtags, chapters, thumbnail };
  }

  async getContentsByType(
    bid: string,
    videoId: string,
    type: CONTENT_TYPE_OTHERS,
  ): Promise<YoutubeContent[]> {
    const contentTypes =
      type === CONTENT_TYPE_OTHERS.SOCIAL_POST ? Object.values(CONTENT_TYPE_POST) : [type];
    const contents = await this.youtubeContent
      .find({ bid, videoId, contentType: { $in: contentTypes } })
      .select(
        mongoSelect(['_id', 'status', 'isFavorite', 'failReason', 'contentType', 'contentData']),
      )
      .sort({ createdAt: -1 });

    return contents;
  }

  async generateContents(
    userIds: UserIds,
    videoId: string,
    { generateTypes, language, tone }: GenerateContentsDto,
    isGenerateMore = false,
  ) {
    const url = getYoutubeUrlFromId(videoId);
    let toGenerateTypes = generateTypes;
    try {
      if (!isGenerateMore) {
        const youtubeData = await this.youtubeModel.findOne({ bid: userIds.bid, videoId });
        if (youtubeData) {
          toGenerateTypes = generateTypes.filter((gt) => !youtubeData.generateTypes.includes(gt));
          if (!toGenerateTypes.length) {
            throw new UnprocessableEntityException('All types of content have been generated..');
          }
        }
      }

      const transcript = await this.getTranscript(userIds, url, language);
      // TODO: Use the generate transcript API
      if (toGenerateTypes.length === 1 && toGenerateTypes[0] === CONTENT_TYPE_EXTRA.TRANSCRIPT) {
        await this.saveYouTubeData(userIds, url, {
          $addToSet: { generateTypes: { $each: toGenerateTypes } },
        });
        return { success: true };
      }

      const summary = await this.getSummary(userIds, url, transcript);
      // TODO: Add generate summary API
      if (toGenerateTypes.length === 1 && toGenerateTypes[0] === CONTENT_TYPE_EXTRA.SUMMARY) {
        await this.saveYouTubeData(userIds, url, {
          $addToSet: { generateTypes: { $each: toGenerateTypes } },
        });
        return { success: true };
      }

      const req = { transcript, summary, language, tone, types: toGenerateTypes };
      const { data, usage } = await this.youtubeMLService.generateContents(req);

      const successfulGenerateTypes = Object.keys(data).filter(
        (type) =>
          (data[type] && !Array.isArray(data[type])) ||
          (Array.isArray(data[type]) && data[type].length),
      );

      await this.saveAllContents(userIds.bid, url, data);
      await this.saveYouTubeData(userIds, url, {
        $addToSet: {
          generateTypes: { $each: successfulGenerateTypes },
        },
        $inc: {
          usage_cost: usage?.total_cost || 0,
          usage_token: usage?.total_tokens || 0,
        },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Failed to generate data for youtube url: ${url} bid: ${userIds.bid}`,
        error,
      );
      throw error;
    }
  }

  async generateMore({ bid, uid }: UserIds, videoId: string, contentType: CONTENT_TYPE) {
    const entry = await this.youtubeModel.findOne({ bid, videoId });
    if (!entry) {
      throw new UnprocessableEntityException('No content has been generated yet.');
    }

    const contentCount = await this.youtubeContent.count({ bid, videoId, contentType });
    if (contentCount === 0) {
      throw new UnprocessableEntityException('No content has been generated yet.');
    }
    if (contentCount >= CONTENT_COUNT_MAX_PER_TYPE[contentType]) {
      throw new UnprocessableEntityException(
        `You've reached the maximum limit for generating more content of this type.`,
      );
    }

    const data = { language: entry.language, generateTypes: [contentType] };
    await this.generateContents({ bid, uid }, videoId, data, true);

    return { success: true };
  }

  async setFavorite(bid: string, url: string, id: string) {
    try {
      const _id = new ObjectId(id);

      const content = await this.youtubeContent.findOne({
        bid,
        _id,
      });
      if (!content) {
        throw new Error(`content not found for id ${id}`);
      }

      await this.youtubeContent.updateOne({ bid, url, _id }, { $set: { isFavorite: true } });

      await this.youtubeContent.updateMany(
        { bid, url, _id: { $ne: _id }, contentType: content.contentType },
        { $set: { isFavorite: false } },
      );
    } catch (e) {
      this.logger.error(
        `Failed to set favorite content for YouTube url: ${url} bid: ${bid} id: ${id}`,
        e,
      );
      throw e;
    }
  }

  async deleteContent(bid: string, id: string) {
    try {
      const _id = new ObjectId(id);

      await this.youtubeContent.findOneAndUpdate({ bid, _id }, [
        { $set: { isDeleted: { $not: '$isDeleted' } } },
      ]);
    } catch (e) {
      this.logger.error(`Failed to delete content for YouTube bid: ${bid} id: ${id}`, e);
      throw e;
    }
  }

  async generateThumbnails(
    { bid, uid }: UserIds,
    videoId: string,
    dto: GenerateThumbnailDto,
  ): Promise<{ success: boolean }> {
    const contentCount = await this.youtubeContent.count({
      bid,
      videoId,
      status: { $nin: ['generation_failed'] },
      contentType: CONTENT_TYPE_OTHERS.THUMBNAIL,
    });
    if (contentCount >= CONTENT_COUNT_MAX_PER_TYPE['thumbnail']) {
      throw new UnprocessableEntityException(
        `You've reached the maximum limit for generating more thumbnail for this video.`,
      );
    }

    const body = {
      contentType: CONTENT_TYPE_OTHERS.THUMBNAIL,
      url: getYoutubeUrlFromId(videoId),
      status: 'generating',
      videoId,
      bid,
      ...dto,
    };
    const { _id: contentId } = await new this.youtubeContent(body).save();

    await this.saveYouTubeData({ bid, uid }, getYoutubeUrlFromId(videoId), {
      $addToSet: {
        generateTypesInProgress: CONTENT_TYPE_OTHERS.THUMBNAIL,
      },
    });

    this.youtubeTasksQueue.add(
      CONTENT_TYPE_OTHERS.THUMBNAIL,
      { bid, uid, videoId, contentId, ...dto },
      {},
    );

    return { success: true };
  }

  async generatePosts(
    { bid, uid }: UserIds,
    videoId: string,
    { platform, ...dto }: GenerateSocialPostsDto,
  ): Promise<{ success: boolean }> {
    const socialPostCounts = await this.youtubeContent.count({
      bid,
      videoId,
      contentType: platform,
      status: { $nin: ['generation_failed'] },
    });
    if (socialPostCounts >= CONTENT_COUNT_MAX_PER_TYPE[platform]) {
      throw new UnprocessableEntityException(
        `You've reached the maximum limit for generating more content of this type.`,
      );
    }

    const body = {
      url: getYoutubeUrlFromId(videoId),
      contentType: platform,
      status: 'generating',
      videoId,
      bid,
      ...dto,
    };
    const { _id: contentId } = await new this.youtubeContent(body).save();

    await this.saveYouTubeData({ bid, uid }, getYoutubeUrlFromId(videoId), {
      $addToSet: {
        generateTypesInProgress: platform,
      },
    });

    const data = { bid, uid, videoId, contentId, platform, ...dto };
    this.youtubeTasksQueue.add(CONTENT_TYPE_OTHERS.SOCIAL_POST, data);

    return { success: true };
  }

  async generateShorts(
    { bid, uid }: UserIds,
    videoId: string,
    dto: GenerateShortsDto,
  ): Promise<{ success: boolean }> {
    const shortsCounts = await this.youtubeContent.count({
      bid,
      videoId,
      contentType: CONTENT_TYPE_OTHERS.SHORT,
    });
    if (shortsCounts >= CONTENT_COUNT_MAX_PER_TYPE[CONTENT_TYPE_OTHERS.SHORT]) {
      throw new UnprocessableEntityException(
        `You've reached the maximum limit for generating shorts for this video.`,
      );
    }

    await this.saveYouTubeData({ bid, uid }, getYoutubeUrlFromId(videoId), {
      $addToSet: {
        generateTypesInProgress: CONTENT_TYPE_OTHERS.SHORT,
      },
    });

    this.youtubeTasksQueue.add(CONTENT_TYPE_OTHERS.SHORT, { bid, uid, videoId, ...dto }, {});

    return { success: true };
  }

  async processShorts({ bid, uid }: UserIds, videoId: string, id: string) {
    const content = await this.youtubeContent.findById(new ObjectId(id));
    if (!content) {
      throw new Error(`Content not found for id ${id}.`);
    }

    await this.youtubeContent.findByIdAndUpdate(content._id, { status: 'generating' });
    const data = { bid, uid, videoId, content, contentId: content._id };
    this.youtubeTasksQueue.add('prepare-short', data);

    return { success: true };
  }

  async getTranscript(
    { bid, uid }: UserIds,
    url: string,
    language: string,
  ): Promise<YouTubeTranscript[]> {
    const entry = await this.youtubeModel.findOne({ url, language });
    if (entry?.transcript && entry.transcript.length > 1 && entry.transcript[0].startTime) {
      return entry.transcript as YouTubeTranscript[];
    }

    this.logger.debug(
      `Transcript not generated before, generating caption for url: ${url} bid: ${bid}`,
    );

    const { transcript, transcriptByAsr } = await this.generateTranscript(bid, url, language);
    await this.saveYouTubeData({ bid, uid }, url, { transcript, transcriptByAsr, language });

    return transcript;
  }

  private async generateTranscript(
    bid: string,
    youtubeUrl: string,
    language: string,
  ): Promise<{ transcript: YouTubeTranscript[]; transcriptByAsr: boolean }> {
    const business = await this.businessService.findOne(bid);

    let transcript: YouTubeTranscript[];

    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access YouTube. Please connect again.');
    }

    try {
      transcript = (await this.transcribeService.getYouTubeTranscript(
        youtubeUrl,
        false,
      )) as YouTubeTranscript[];

      // check if at least 5 sentences got by default caption
      if (transcript.length > 5 && transcript[0].endTime) {
        return { transcript, transcriptByAsr: false };
      }
    } catch (error) {
      this.logger.error(
        `Failed to get transcript directly from YouTube for url: ${youtubeUrl} bid: ${bid} ${error?.message}`,
      );
    }

    // Transcribe using ASR
    try {
      this.logger.debug(`Transcribing using ASR url: ${youtubeUrl} bid: ${bid}`);
      transcript = await this.transcribeService.getYouTubeTranscriptUsingASR(
        youtubeUrl,
        bid,
        language,
      );
      return { transcript, transcriptByAsr: true };
    } catch (error) {
      this.logger.error(
        `Failed to get transcript using transcription service for youtube url: ${youtubeUrl} bid: ${bid} ${error?.message}`,
      );
      throw error;
    }
  }

  async getSummary(
    { bid, uid }: UserIds,
    url: string,
    transcript: YouTubeTranscript[],
  ): Promise<string> {
    try {
      const entry = await this.youtubeModel.findOne({ bid, url });
      if (entry?.summary) {
        return entry.summary;
      }

      let title = '';
      try {
        const youtubeInfo = await this.transcribeService.getYouTubeInfo(url);
        if (youtubeInfo) {
          title = youtubeInfo.title;
        }
      } catch (error) {
        this.logger.warn(`Failed to get title for youtube url: ${url} bid: ${bid}`, error);
      }

      console.log({ title, transcript });
      const response = await this.youtubeMLService.generateSummary({ title, transcript });
      if (!response || !response.summary) {
        throw new Error('Failed to generate summary');
      }

      const { summary, usage } = response;
      await this.saveYouTubeData({ bid, uid }, url, {
        summary,
        $inc: {
          usage_cost: usage?.usage_cost || 0,
          usage_token: usage?.usage_tokens || 0,
        },
      });

      return summary;
    } catch (error) {
      this.logger.error(`Failed to generate summary for youtube url: ${url} bid: ${bid}`, error);
      throw error;
    }
  }

  private async saveYouTubeData({ bid, uid }: UserIds, url: string, data: any) {
    try {
      this.logger.log(`Saving contents YouTube url: ${url} bid: ${bid} uid: ${uid}`);
      const filter = { bid, url };
      const d = { bid, uid, url, videoId: getURLVideoID(url), ...data };
      const options = { upsert: true, new: true };

      await this.youtubeModel.findOneAndUpdate(filter, d, options);
    } catch (e) {
      this.logger.error(`Failed to save data for YouTube url: ${url} bid: ${bid} uid: ${uid}`, e);
      throw e;
    }
  }

  private async saveAllContents(
    bid: string,
    url: string,
    contents: GenerateContentsResponse['data'],
  ) {
    try {
      this.logger.log(`Saving all contents for YouTube url: ${url} bid: ${bid}`);

      const { title = [], description = '', tag = [], hashtag = [], chapters } = contents;
      const videoId = getURLVideoID(url);
      const commonData = { bid, url, videoId };

      const bulkContent: YoutubeContent[] = [
        ...title.map((contentData, i) => ({
          ...commonData,
          contentType: CONTENT_TYPE.TITLE,
          isFavorite: i === 0,
          contentData,
        })),
        ...tag.map((contentData) => ({
          ...commonData,
          contentType: CONTENT_TYPE.TAG,
          contentData,
        })),
        ...hashtag.map((contentData) => ({
          ...commonData,
          contentType: CONTENT_TYPE.HASHTAG,
          contentData,
        })),
      ];

      if (description) {
        bulkContent.push({
          ...commonData,
          contentType: CONTENT_TYPE.DESCRIPTION,
          contentData: description,
          isFavorite: true,
        });
      }

      if (chapters) {
        bulkContent.push({
          ...commonData,
          contentType: CONTENT_TYPE.CHAPTERS,
          contentData: chapters,
        });
      }

      await this.youtubeContent.insertMany(bulkContent);
      this.logger.log(`Successfully saved all contents for YouTube url: ${url} bid: ${bid}`);
    } catch (e) {
      this.logger.error(`Failed to save data for YouTube url: ${url} bid: ${bid}`, e);
      throw e;
    }
  }
}
