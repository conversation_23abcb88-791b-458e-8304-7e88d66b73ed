// import type { YouTubeContentTypes } from '../youtube-content.model';

import { IsBoolean, IsEnum, IsOptional, IsString, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { CONTENT_TYPE_EXTRA, CONTENT_TYPE_POST, CONTENT_TYPE } from '../youtube-content.model';

export class GenerateContentsDto {
  @ApiProperty({
    isArray: true,
    enum: CONTENT_TYPE,
    // TODO: Keep only CONTENT_TYPE
    example: Object.values({ ...CONTENT_TYPE, ...CONTENT_TYPE_EXTRA }),
  })
  @IsEnum({ ...CONTENT_TYPE, ...CONTENT_TYPE_EXTRA }, { each: true })
  public generateTypes: (CONTENT_TYPE | CONTENT_TYPE_EXTRA)[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  public language: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  public tone?: string;
}

export class GenerateMoreContentsDto {
  @ApiProperty({ enum: CONTENT_TYPE, example: Object.values(CONTENT_TYPE) })
  @IsEnum(CONTENT_TYPE)
  public contentType: CONTENT_TYPE;
}

export class GenerateTranscriptDto {
  @ApiProperty()
  @IsUrl()
  public url: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  public language?: string;
}

export class GenerateThumbnailDto {
  @ApiProperty()
  @IsString()
  public prompt: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  public style?: string;
}

export class GenerateSocialPostsDto {
  @ApiProperty({ enum: CONTENT_TYPE_POST, example: CONTENT_TYPE_POST })
  @IsEnum(CONTENT_TYPE_POST)
  public platform: CONTENT_TYPE_POST;

  @ApiProperty()
  @IsString()
  @IsOptional()
  public prompt: string;

  @ApiProperty()
  @IsString()
  public language: string;

  @ApiProperty()
  @IsString()
  public tone: string;

  @ApiProperty()
  @IsBoolean()
  public includeLink: boolean;
}

export class GenerateShortsDto {
  @ApiProperty()
  @IsString()
  public language: string;

  @ApiPropertyOptional({
    description: 'YouTube or other media Start & End time',
    example: '10:140 (10 seconds to 140 seconds)',
  })
  @IsString()
  @IsOptional()
  readonly clipTimeSpan?: string;
}
