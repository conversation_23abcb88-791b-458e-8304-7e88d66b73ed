import type { YouTubeContentTypes } from './youtube-content.model';
import { CONTENT_TYPE } from './youtube-content.model';

export const CONTENT_COUNT_MAX_PER_TYPE: Record<YouTubeContentTypes, number> = {
  title: 10,
  description: 5,
  tag: 20,
  hashtag: 20,
  chapters: 1,
  thumbnail: 6,
  short: 30,
  summary: 1,
  transcript: 1,
  'social-post': 15,
  social_post_facebook: 5,
  social_post_twitter: 5,
  social_post_linkedin: 5,
};

export const REGENERABLE_CONTENT_TYPES: CONTENT_TYPE[] = [
  CONTENT_TYPE.TITLE,
  CONTENT_TYPE.DESCRIPTION,
  CONTENT_TYPE.TAG,
  CONTENT_TYPE.HASHTAG,
];

export const CONTENT_PAGES: Record<YouTubeContentTypes | 'prepare-short', string> = {
  title: 'title',
  description: 'description',
  chapters: 'chapters',
  tag: 'tags',
  hashtag: 'tags',
  thumbnail: 'thumbnails',
  short: 'shorts',
  'prepare-short': 'shorts',

  summary: 'summary',
  transcript: 'transcript',

  'social-post': 'post',
  social_post_facebook: 'posts',
  social_post_twitter: 'posts',
  social_post_linkedin: 'posts',
};
