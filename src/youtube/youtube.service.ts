import type {
  YoutubeQueryParams,
  YoutubeResponse,
  YoutubeVideo,
} from './youtube-api/youtube-api.type';
import type { YoutubeContentDocument } from './youtube-content/youtube-content.model';
import type { YoutubeDocument } from './youtube-data/youtube.model';
import type { YouTubeChapter } from './youtube-content/youtube-content.interface';
import type { GoogleProfile } from '@integrations/socials/google/google.type';
import type { Business } from '@business/business.model';
import type { Model } from 'mongoose';

import { UnauthorizedException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { BusinessService } from '@business/business.service';
import { mongoSelect } from '@common/utils/db';
import { groupByKey } from '@common/utils';

import { YoutubeContent, CONTENT_TYPE } from './youtube-content/youtube-content.model';
import { YoutubeAPIService } from './youtube-api/youtube-api.service';
import { YoutubeModel } from './youtube-data/youtube.model';

const youtubeDataSelectFields = [
  'videoId',
  'generateTypes',
  'generateTypesInProgress',
  'language',
  'summary',
];

@Injectable()
export class YoutubeService {
  private readonly logger = new Logger(YoutubeService.name);

  constructor(
    @InjectModel(YoutubeContent.name) private youtubeContent: Model<YoutubeContent>,
    @InjectModel(YoutubeModel.name) private youtubeModel: Model<YoutubeModel>,
    private readonly youtubeAPIService: YoutubeAPIService,
    private readonly businessService: BusinessService,
  ) {}

  async youtubeConnectCallback(bid: string, googleUser: GoogleProfile) {
    try {
      const { email, name, picture, accessToken, refreshToken } = googleUser;
      const youtubeProfile = { name, picture, email };

      await this.businessService.updateBusinessYoutubeToken(
        bid,
        accessToken,
        refreshToken,
        youtubeProfile,
      );

      return { success: true };
    } catch (error) {
      throw new Error(
        error.code === 11000 ? 'Email Already exist.' : `Connect failed ${error?.message}`,
      );
    }
  }

  async search(bid: string, q: string, params: YoutubeQueryParams) {
    const business = await this.businessService.findOne(bid);
    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again.');
    }

    const result = await this.youtubeAPIService.search(q, params);

    if (result.newAccessToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, result.newAccessToken);
      delete result.newAccessToken;
    }

    return result;
  }

  async getChannels(bid: string) {
    const business = await this.businessService.findOne(bid);

    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again');
    }

    const result = await this.youtubeAPIService.getChannels(this.getTokens(business));

    if (result.newAccessToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, result.newAccessToken);
      delete result.newAccessToken;
    }

    return result;
  }

  async getPlaylists(bid: string, channelId: string, params: YoutubeQueryParams) {
    const business = await this.businessService.findOne(bid);

    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again');
    }

    const tokens = this.getTokens(business);
    const result = await this.youtubeAPIService.getPlaylists(channelId, params, tokens);

    if (result.newAccessToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, result.newAccessToken);
      delete result.newAccessToken;
    }

    return result;
  }

  async getVideos(bid: string, playlistId: string, params: YoutubeQueryParams) {
    const business = await this.businessService.findOne(bid);
    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again');
    }

    const tokens = this.getTokens(business);
    const playlistItems = await this.youtubeAPIService.getPlaylistItems(playlistId, params, tokens);
    const videoIds = playlistItems.items.map((item) => item.contentDetails.videoId);

    if (!videoIds.length) return { items: [], pageInfo: {} };

    const _params = { maxResults: params.maxResults };
    const result = await this.youtubeAPIService.getVideos(videoIds, _params, tokens);

    const newToken = playlistItems.newAccessToken || result.newAccessToken;
    if (newToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, newToken);
      delete playlistItems.newAccessToken;
      delete result.newAccessToken;
    }

    await this.populateYoutubeData(bid, videoIds, result);

    return { ...playlistItems, items: result.items };
  }

  async getVideo(bid: string, videoId: string) {
    const business = await this.businessService.findOne(bid);
    if (!business || !business.youtubeAccessToken) {
      throw new UnauthorizedException('Unauthorized to access youtube. Please connect again');
    }

    const tokens = this.getTokens(business);

    const result = await this.youtubeAPIService.getVideo(videoId, tokens);

    const newToken = result.newAccessToken;
    if (result.newAccessToken) {
      await this.businessService.updateBusinessYoutubeToken(bid, newToken);
      delete result.newAccessToken;
    }

    await this.populateYoutubeData(bid, [videoId], result);

    return result?.items?.[0] || {};
  }

  private getTokens = (business: Business) => ({
    accessToken: business.youtubeAccessToken,
    refreshToken: business.youtubeRefreshToken,
  });

  private async populateYoutubeData(
    bid: string,
    videoIds: string[],
    result: YoutubeResponse<YoutubeVideo>,
  ) {
    try {
      const youtubeData = await this.youtubeModel
        .find({ bid, videoId: { $in: videoIds } })
        .select(mongoSelect(youtubeDataSelectFields))
        .then((data: YoutubeDocument[]) => groupByKey(data, 'videoId'));

      const youtubeChapters = await this.youtubeContent
        .find({ bid, contentType: CONTENT_TYPE.CHAPTERS, videoId: { $in: videoIds } })
        .select({ contentData: 1, videoId: 1 })
        .then((data: YoutubeContentDocument[]) => groupByKey(data, 'videoId'));

      result.items = result.items.map((v) => {
        if (youtubeData[v.id]) {
          v.data = youtubeData[v.id].toObject();
          v.data.chapters = youtubeChapters[v.id]
            ? (youtubeChapters[v.id].contentData as YouTubeChapter[])
            : [];
        }
        return v;
      });
    } catch (e) {
      this.logger.log(e);
    }
  }
}
