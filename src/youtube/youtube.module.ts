import { MongooseModule } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';

import { JOB_OPTIONS, JOB_QUEUES, QueueOpts } from '@common/constants';
import { NotificationModule } from '@resources/notification/notification.module';
import { TranscribeModule } from '@transcribe/transcribe.module';
import { BusinessModule } from '@business/business.module';
import { PaymentsModule } from 'src/payments/payments.module';
import { UserModule } from '@user/user.module';

import { YoutubeContentSchema, YoutubeContent } from './youtube-content/youtube-content.model';
import { YoutubeSchema, YoutubeModel } from './youtube-data/youtube.model';
import { YoutubeContentController } from './youtube-content/youtube-content.controller';
import { YouTubeContentProcessor } from './youtube-content/youtube-content.processor';
import { YoutubeContentService } from './youtube-content/youtube-content.service';
import { YoutubeDataController } from './youtube-data/youtube-data.controller';
import { YoutubeDataService } from './youtube-data/youtube-data.service';
import { YoutubeAPIService } from './youtube-api/youtube-api.service';
import { YoutubeController } from './youtube.controller';
import { YoutubeMLService } from './youtube-ml/youtube-ml.service';
import { YoutubeService } from './youtube.service';
import { BlogifyMediaService } from 'src/blogify-media/blogify-media.service';
import { SlackService } from '@common/services/slack.service';
import { SignupAttempt, SignupAttemptSchema } from '@/auth/signup-attempt.model';
import { AuthModule } from '@/auth/auth.module';

@Module({
  controllers: [YoutubeContentController, YoutubeDataController, YoutubeController],
  imports: [
    BullModule.registerQueue({
      name: JOB_QUEUES.YOUTUBE_CONTENT,
      defaultJobOptions: JOB_OPTIONS,
      limiter: { max: 1, duration: 1000 },
      settings: QueueOpts,
    }),
    MongooseModule.forFeature([{ name: SignupAttempt.name, schema: SignupAttemptSchema }]),
    MongooseModule.forFeature([
      { name: YoutubeContent.name, schema: YoutubeContentSchema },
      { name: YoutubeModel.name, schema: YoutubeSchema },
    ]),
    NotificationModule,
    TranscribeModule,
    BusinessModule,
    PaymentsModule,
    UserModule,
    AuthModule,
  ],
  providers: [
    YouTubeContentProcessor,
    YoutubeContentService,
    YoutubeDataService,
    YoutubeAPIService,
    YoutubeMLService,
    YoutubeService,
    JwtService,
    BlogifyMediaService,
    SlackService,
  ],
  exports: [YoutubeService],
})
export class YoutubeModule {}
