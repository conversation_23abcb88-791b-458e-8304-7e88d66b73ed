import { IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { EventType } from '../event.enums';

export class TrackEventDto {
  @ApiProperty({ description: 'The URL of the page where the event triggered from.' })
  @IsString()
  ref: string;

  @ApiProperty({ description: 'Type of the event', default: EventType.View })
  @IsEnum(EventType)
  type: EventType;

  // Relations
  @ApiProperty({ description: 'Business ID' })
  @IsString()
  business?: string;

  @ApiProperty({ description: 'Website ID' })
  @IsString()
  website?: string;

  @ApiProperty({ description: 'Blog ID' })
  @IsString()
  blog?: string;
}
