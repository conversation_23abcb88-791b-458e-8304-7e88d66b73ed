import type { UserAgentInfo, GeoInfo } from './event.types';
import type { BusinessDocument } from '@/business/business.model';
import type { WebsiteDocument } from '@/resources/website/website.model';
import type { BlogDocument } from '@/blog/blog.model';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { SchemaTypes, Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { EventType } from './event.enums';

@Schema({ timestamps: { updatedAt: false }, versionKey: false })
export class Event extends Document {
  @Prop({ type: String, required: true })
  ref: string;

  @Prop({ type: String, enum: Object.values(EventType), default: EventType.View, required: true })
  type: EventType;

  @Prop({ type: String, required: true })
  ip: string;

  @Prop({ type: String, required: true })
  userAgent: string;

  @Prop({ type: SchemaTypes.Mixed, default: {} })
  userAgentInfo: UserAgentInfo;

  @Prop({ type: SchemaTypes.Mixed, default: {} })
  geoInfo: GeoInfo;

  // Relations
  @Prop({ type: ObjectId, ref: 'Business' })
  business?: ObjectId | BusinessDocument;

  @Prop({ type: ObjectId, ref: 'Website' })
  website?: ObjectId | WebsiteDocument;

  @Prop({ type: ObjectId, ref: 'Blog' })
  blog?: ObjectId | BlogDocument;
}

const EventSchema = SchemaFactory.createForClass(Event);

EventSchema.index({ ref: 1 });
EventSchema.index({ type: 1 });
EventSchema.index({ business: 1 });
EventSchema.index({ website: 1 });

export { EventSchema };
