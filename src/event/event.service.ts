import type { UserAgentInfo, GeoInfo } from './event.types';
import type { Model } from 'mongoose';

import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { ObjectId } from 'mongodb';
import bowser from 'bowser';
import geoip from 'geoip-lite';

import { CrudService } from '@/crud/crud.service';

import { TrackEventDto } from './dto/track-event.dto';
import { Event } from './event.model';

@Injectable()
export class EventService extends CrudService<Event> {
  constructor(@InjectModel(Event.name) private readonly event: Model<Event>) {
    super(event);
  }

  async trackEvent(
    { ref, type, ...trackEventDto }: TrackEventDto,
    userAgent: string,
    ip: string,
  ): Promise<[userAgentInfo: UserAgentInfo, geoInfo: GeoInfo]> {
    const userAgentInfo = this.getUserAgentInfo(userAgent);
    const geoInfo = this.getGeoInfo(ip);

    const body: Partial<Event> = {
      type,
      ref,
      ip,
      userAgent,

      userAgentInfo,
      geoInfo,
    };

    ['business', 'website', 'blog'].forEach((r) => {
      if (trackEventDto[r]) {
        body[r] = new ObjectId(trackEventDto[r]);
      }
    });

    await this.event.create(body);

    return [userAgentInfo, geoInfo];
  }

  private getUserAgentInfo(userAgent: string): UserAgentInfo {
    const ua = bowser.getParser(userAgent);

    return {
      platform: ua.getPlatformType(),
      browser: ua.getBrowserName(),
      vendor: ua.getPlatform().vendor,
      os: ua.getOSName(),
    };
  }

  private getGeoInfo(ip: string): GeoInfo {
    const geo = geoip.lookup(ip);

    return {
      city: geo?.city,
      country: geo?.country,
      timezone: geo?.timezone,
      coordinates: [geo?.ll[1], geo?.ll[0]],
    };
  }
}
