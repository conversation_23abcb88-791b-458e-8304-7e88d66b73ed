import type { Request } from 'express';

import { Controller, Logger, Query, Get, Req } from '@nestjs/common';
import { CrudController } from '@/crud/crud.controller';

import { TrackEventDto } from './dto/track-event.dto';
import { EventService } from './event.service';
import { Event } from './event.model';

@Controller('events')
export class EventController extends CrudController<Event, null, null> {
  constructor(
    private readonly eventService: EventService,
    private readonly logger: Logger,
  ) {
    super(eventService);
  }

  @Get('track')
  async trackEvent(@Req() req: Request, @Query() query: TrackEventDto) {
    try {
      // Parse URL using BASE_URL as the base
      const url = new URL(req.protocol + '://' + req.get('host') + req.originalUrl);
      const refIndex = url.search.indexOf('ref=');
      if (refIndex !== -1) {
        const refParams = url.search.substring(refIndex + 4);
        query.ref = decodeURIComponent(refParams);
      }

      const userAgent = req.headers['user-agent'];
      const ipAddress = req.headers['x-forwarded-for'] || req.ip || req.connection.remoteAddress;
      const ip = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;
      await this.eventService.trackEvent(query, userAgent, ip);

      return { success: true };
    } catch (error) {
      this.logger.error('Error Tracking Event:', error?.message);
    }
  }
}
