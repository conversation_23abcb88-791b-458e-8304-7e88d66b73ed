import { Injectable, Logger } from '@nestjs/common';
import * as Sentry from '@sentry/node';

import { ErrorLoggerService } from '../error-logger.service';

@Injectable()
export class SentryService extends Logger implements ErrorLoggerService {
  requestHandler() {
    return Sentry.Handlers.requestHandler();
  }

  error(...args: [string]): void {
    Sentry.captureException(args);
    return super.error(...args);
  }

  setContext(name: string) {
    this.context = name;
  }
}
