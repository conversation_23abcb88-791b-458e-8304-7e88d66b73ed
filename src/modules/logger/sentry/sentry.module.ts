import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { Module } from '@nestjs/common';
import * as Sentry from '@sentry/node';

import config from '@/common/configs/config';

import { SentryInterceptor } from './sentry.interceptor';
import { SentryService } from './sentry.service';

const SENTRY_OPTIONS = {
  dsn: config().logger.sentry,
  environment: config().environment,
  tracesSampleRate: 1.0,
  profilesSampleRate: 1,
  integrations: [
    nodeProfilingIntegration(),
    ...Sentry.autoDiscoverNodePerformanceMonitoringIntegrations(),
  ],
  ignoreErrors: [
    // https://gist.github.com/pioug/b006c983538538066ea871d299d8e8bc
    /^No error$/,
    /__show__deepen/,
    /Access is denied/,
    /anonymous function: captureException/,
    /Blocked a frame with origin/,
    /console is not defined/,
    /cordova/,
    /DataCloneError/,
    /Error: AccessDeny/,
    /event is not defined/,
    /feedConf/,
    /ibFindAllVideos/,
    /myGloFrameList/,
    /SecurityError/,
    /MyIPhoneApp/,
    /snapchat.com/,
    /vid_mate_check is not defined/,
    /win\.document\.body/,
    /window\._sharedData\.entry_data/,
    /ztePageScrollModule/,

    /** ** */
    /Token has expired/,
    /Token expired/,
    /Wrong Email or Password/,
    /Item not found/,
  ],
};

@Module({ providers: [SentryInterceptor, SentryService] })
export class SentryModule {
  static forRoot() {
    Sentry.init(SENTRY_OPTIONS);

    return {
      module: SentryModule,
      providers: [SentryInterceptor, SentryService],
      exports: [SentryInterceptor, SentryService],
    };
  }
}
