import { ExecutionContext, Injectable, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { ErrorInterceptor } from '../error.interceptor';
import { SentryService } from './sentry.service';

@Injectable()
export class SentryInterceptor implements ErrorInterceptor {
  constructor(private sentryService: SentryService) {}

  intercept(_: ExecutionContext, next: CallHandler): Observable<unknown> {
    return next.handle().pipe(
      tap({
        error: (e) => this.sentryService.error(e),
      }),
    );
  }
}
