import { Lo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Scope } from '@nestjs/common';
import { INQUIRER } from '@nestjs/core';

import { SentryInterceptor } from './sentry/sentry.interceptor';
import { ErrorInterceptor } from './error.interceptor';
import { SentryService } from './sentry/sentry.service';
import { SentryModule } from './sentry/sentry.module';

@Global()
@Module({
  imports: [SentryModule.forRoot()],
  providers: [
    { provide: ErrorInterceptor, useClass: SentryInterceptor },
    {
      provide: Logger,
      useFactory: (instance) => new SentryService(instance.constructor.name),
      scope: Scope.TRANSIENT,
      inject: [INQUIRER],
    },
  ],
  exports: [ErrorInterceptor, Logger],
})
export class LoggerModule {}
