import type { Notification } from '@resources/notification/notification.model';
import type { YoutubeContent } from 'src/youtube/youtube-content/youtube-content.model';
import type { Blog } from '@blog/blog.model';

import { Injectable } from '@nestjs/common';

import { SocketIOGateService } from './socket-io/socket-io.service';

@Injectable()
export class GatewayService extends SocketIOGateService {
  sendNotification(userId: string, notification: Notification): void {
    this.send('NOTIFICATION', notification, userId);
  }

  sendBlogStatusUpdate(userId: string, blog: Partial<Blog & { percentComplete?: number }>): void {
    this.send('BLOG_STATUS_UPDATE', blog, userId);
  }

  sendBlogSeoAnalysisStatusUpdate(
    userId: string,
    blog: Partial<Blog & { percentComplete?: number }>,
  ): void {
    this.send('BLOG_SEO_ANALYSIS_STATUS_UPDATE', blog, userId);
  }

  sendBlogAffiliateLinkGenerationStatusUpdate(
    userId: string,
    blog: Partial<Blog & { percentComplete?: number }>,
  ): void {
    this.send('BLOG_AFFILIATE_LINK_GENERATION_STATUS_UPDATE', blog, userId);
  }

  sendYouTubeContentUpdate(userId: string, youtubeContent: Partial<YoutubeContent>): void {
    this.send('YOUTUBE_CONTENT_UPDATE', youtubeContent, userId);
  }
}
