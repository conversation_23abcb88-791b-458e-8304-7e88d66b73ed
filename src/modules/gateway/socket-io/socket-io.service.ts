import type { OnGatewayConnection, OnGatewayInit } from '@nestjs/websockets';
import type { Server, Socket } from 'socket.io';
import type { JwtPayload } from '@auth/interfaces/jwt-payload.interface';

import { WebSocketGateway, WebSocketServer, WsException } from '@nestjs/websockets';
import { JwtService } from '@nestjs/jwt';
import { Logger } from '@nestjs/common';

import { EVENTS } from '../gateway.constants';

@WebSocketGateway()
export class SocketIOGateService implements OnGatewayInit, OnGatewayConnection {
  private readonly logger = new Logger('SocketIO');

  @WebSocketServer()
  private readonly server: Server;

  constructor(private readonly jwtService: JwtService) {}

  afterInit(client: Socket) {
    try {
      client.use((req, next) => {
        try {
          this.authenticate(req as unknown as Socket);
        } catch (e) {
          return next(e);
        }
        return next();
      });
    } catch (e) {
      this.logger.log(e?.message || e);
    }
  }

  handleConnection(client: Socket) {
    try {
      const user = this.authenticate(client);
      client.join(user.sub);
    } catch (e) {
      this.logger.log(e?.message || e);
    }
  }

  protected send(eventName: keyof typeof EVENTS, message: unknown, roomId?: string): void {
    try {
      if (roomId) {
        this.server.to(roomId).emit(eventName, message);
      } else {
        this.server.emit(eventName, message);
      }
    } catch (e) {
      this.logger.log(e?.message || e);
    }
  }

  private authenticate(client: Socket): JwtPayload {
    try {
      const authorization = client.handshake.auth?.token || client.handshake.headers.authorization;

      if (!authorization) {
        throw new WsException('No authorization header provided');
      }

      const token = authorization.split(' ')[1];
      if (!token) {
        throw new WsException('Invalid token provided');
      }

      const decoded = this.jwtService.decode(token) as JwtPayload;
      if (!decoded || !decoded.sub) {
        throw new WsException('Invalid token provided');
      }

      return decoded;
    } catch (e) {
      this.logger.log(e?.message || e);
    }
  }
}
