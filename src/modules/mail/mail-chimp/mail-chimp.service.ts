import type {
  ApiClient as MailChimpClient,
  MessagesSendResponse,
} from '@mailchimp/mailchimp_transactional';
import type { MailPayload } from '../mail.type';

import { Injectable, Logger } from '@nestjs/common';
import MailChimp from '@mailchimp/mailchimp_transactional';

import config from '@/common/configs/config';

import { getEmailData } from '../mail.utils';
import { MailService } from '../mail.service';

@Injectable()
export class MailChimpService implements MailService {
  private mailChimpClient: MailChimpClient;
  private readonly logger: Logger = new Logger(MailChimpService.name);

  constructor() {
    this.mailChimpClient = MailChimp(config().mail.mailChimp.apiKey);
  }

  async send(mailPayload: MailPayload): Promise<void> {
    try {
      const { html, subject } = await getEmailData(mailPayload);
      const message = {
        from_name: 'Blogify AI',
        from_email: mailPayload.from || config().mail.support,
        to: [{ email: mailPayload.to, name: mailPayload.name }],
        subject,
        html,
      };

      await this.mailChimpClient.messages.send({ message }).then((resp: MessagesSendResponse[]) => {
        if (resp[0].status === 'sent') {
          this.logger.log(`Email Sent: ${mailPayload.to} - ${mailPayload.subject}`);
        }
      });
    } catch (error) {
      this.logger.log('Email Error:', error);
    }
  }
}
