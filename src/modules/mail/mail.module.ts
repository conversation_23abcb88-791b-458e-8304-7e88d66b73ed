import { Global, Module } from '@nestjs/common';

import config from '@common/configs/config';

import { MailPreviewService } from './mail-preview.service';
import { MailChimpService } from './mail-chimp/mail-chimp.service';
// import { BrevoSMTPService } from './brevo/brevo-smtp.service';
import { MailController } from './mail.controller';
// import { BrevoService } from './brevo/brevo.service';
import { MailService } from './mail.service';

@Global()
@Module({
  controllers: [MailController],
  providers: [
    {
      provide: MailService,
      useClass: config().isDev ? MailPreviewService : MailChimpService,
    },
    MailChimpService,
    // BrevoSMTPService,
    // BrevoService,
  ],
  exports: [MailService],
})
export class MailModule {}
