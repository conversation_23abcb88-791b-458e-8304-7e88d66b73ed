import type { MailPayload } from '../mail.type';

import {
  TransactionalEmailsApiApiKeys,
  TransactionalEmailsApi,
  SendSmtpEmail,
} from '@getbrevo/brevo';
import { Injectable, Logger } from '@nestjs/common';

import config from '@common/configs/config';

import { getEmailData } from '../mail.utils';
import { MailService } from '../mail.service';

@Injectable()
export class BrevoService implements MailService {
  private brevoClient: TransactionalEmailsApi;

  private readonly logger = new Logger(this.constructor.name);
  constructor() {
    this.brevoClient = new TransactionalEmailsApi();
    this.brevoClient.setApiKey(TransactionalEmailsApiApiKeys.apiKey, config().mail.brevo.apiKey);
  }

  async send(mailPayload: MailPayload): Promise<void> {
    try {
      const { html, subject } = await getEmailData(mailPayload);

      const email = new SendSmtpEmail();

      email.subject = subject;
      email.htmlContent = html;
      email.sender = {
        name: 'Team Blogify',
        email: mailPayload.from || config().mail.support,
      };
      email.to = [{ email: mailPayload.to, name: mailPayload.name }];

      await this.brevoClient.sendTransacEmail(email).then(
        (data) => {
          if (!data.response['body'].messageId) {
            throw new Error('Something went wrong with Brevo.');
          }
          this.logger.log(`Email Sent: ${mailPayload.to} - ${mailPayload.subject}`);
        },
        (error) => {
          throw error;
        },
      );
    } catch (error) {
      this.logger.log('Email Error:', error);
    }
  }
}
