// Unused but could be useful later
import type { MailPayload } from '../mail.type';

import { Injectable, Logger } from '@nestjs/common';
import nodemailer from 'nodemailer';

import config from '@/common/configs/config';

import { getEmailData } from '../mail.utils';
import { MailService } from '../mail.service';

@Injectable()
export class BrevoSMTPService implements MailService {
  private smtpClient: nodemailer.Transporter;
  private readonly logger = new Logger(this.constructor.name);
  constructor() {
    this.smtpClient = nodemailer.createTransport({
      host: 'smtp-relay.brevo.com',
      port: 587,
      secure: false,
      auth: {
        user: '',
        pass: '',
      },
    });
  }

  async send(mailPayload: MailPayload): Promise<void> {
    try {
      const { html, subject } = await getEmailData(mailPayload);

      const mailOptions = {
        from: `"Team Blogify" <${mailPayload.from || config().mail.support}>`,
        to: `"${mailPayload.name}" <${mailPayload.to}>`,
        subject,
        html,
      };

      this.smtpClient.sendMail(mailOptions, (error) => {
        if (error) throw error;
        this.logger.log(`Email Sent: ${mailPayload.to} - ${mailPayload.subject}`);
      });
    } catch (error) {
      this.logger.log('Email Error:', error);
    }
  }
}
