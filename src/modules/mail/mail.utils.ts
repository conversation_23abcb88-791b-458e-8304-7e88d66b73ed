import type { MailPayload } from './mail.type';

import { readdirSync, lstatSync } from 'fs';
import { resolve, join } from 'path';
import Email from 'email-templates';
import sass from 'sass';

import config from '@common/configs/config';

const isDirectory = (source: string) => lstatSync(source).isDirectory();

const templatesDir = resolve(__dirname, '../../../src/components/email-templates');
const TEMPLATES = [];
const renderedCSS = {};

const getDirectories = (source: string): string[] =>
  readdirSync(source)
    .map((name) => join(source, name))
    .filter(isDirectory);

const renderSCSS = (firstRun?: boolean) => {
  getDirectories(templatesDir).forEach((dir) => {
    const template = dir.split('/').pop();
    if (template.startsWith('_')) return;
    if (firstRun) TEMPLATES.push(template);

    renderedCSS[template] = sass
      .compile(
        `${process.platform === 'win32' ? template : join(templatesDir, template)}/style.scss`,
      )
      .css.toString();
  });
};
renderSCSS(true);

export async function getEmailData({ template, ...mailPayload }: MailPayload) {
  if (config().isDev) renderSCSS();

  const email = new Email({
    views: {
      root: templatesDir,
      options: {
        extension: 'nunjucks',
      },
    },
    juice: true,
    juiceResources: {
      insertPreservedExtraCss: true,
      preserveImportant: true,
      extraCss: renderedCSS[template],
      webResources: {
        relativeTo: join(templatesDir, template),
      },
    },
  });

  mailPayload.context = {
    ...mailPayload.context,
    blogifyClientUrl: config().internalApps.blogifyClient.url,
    blogifyImagesUrl: 'https://images.blogify.ai/',
    firstName: mailPayload.name?.split(' ')[0],
  };

  return email.renderAll(template, mailPayload.context).then((res) => {
    const mailData: Record<string, any> = {
      from: mailPayload.from || 'BlogifyAI <<EMAIL>>',
      sender: mailPayload.from || `BlogifyAI <${config().mail.support}>`,
      to: mailPayload.to,
      subject: `${!config().isProd ? '[TEST] ' : ''}${mailPayload.subject}`,
      html: res.html,
    };
    if (mailPayload.attachments) {
      mailData.attachments = mailPayload.attachments;
    }
    if (config().isProd && mailPayload.bcc) {
      mailData.bcc = mailPayload.bcc;
    }

    return mailData;
  });
}

export { TEMPLATES };
