import type { MailPayload } from './mail.type';

import { Injectable } from '@nestjs/common';
import previewMail from 'preview-email';
import { getEmailData } from './mail.utils';
import { MailService } from './mail.service';

@Injectable()
export class MailPreviewService implements MailService {
  async send(mailPayload: MailPayload): Promise<any> {
    return getEmailData(mailPayload).then((mailData) => {
      return previewMail(mailData, { openSimulator: false });
    });
  }
}
