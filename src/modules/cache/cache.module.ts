import type { RedisStore } from 'cache-manager-redis-yet';
import type { <PERSON><PERSON> } from 'cache-manager';

import { OnModuleDestroy, OnModuleInit, Inject, Global, Module } from '@nestjs/common';
import { CacheModule as NestCacheModule, CACHE_MANAGER } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-yet';

import config from '@common/configs/config';

import { LocalCacheService } from './local-cache.service';
import { NodeCacheService } from './node-cache/node-cache.service';

@Global()
@Module({
  providers: [NodeCacheService, { provide: LocalCacheService, useExisting: NodeCacheService }],
  exports: [LocalCacheService],
})
export class CacheModule implements OnModuleInit, OnModuleDestroy {
  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache<RedisStore>) {}

  static forRoot() {
    return {
      module: CacheModule,
      imports: [
        NestCacheModule.registerAsync({
          isGlobal: true,
          useFactory: async () => ({
            ttl: config().isProd ? 15 * 60 * 1000 : 1,
            store: await redisStore({
              ttl: config().isProd ? 15 * 60 * 1000 : 1,
              url: process.env.REDIS_URL,
              socket: {
                host: config().cache.redis.host,
                port: config().cache.redis.port,
                passphrase: config().cache.redis.password,
                ...(config().isProd ? { rejectUnauthorized: false, requestCert: true } : {}),
              },
            }),
          }),
        }),
      ],
    };
  }

  onModuleInit() {
    this.cacheManager.store.client.on('error', (error) => console.log(error));
  }

  onModuleDestroy() {
    this.cacheManager.store.client.quit();
  }
}
