import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { BusinessDocument } from '@/business/business.model';
import type { UserDocument } from '@/user/user.model';

import {
  InternalServerErrorException,
  BadRequestException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Stripe } from 'stripe';
import { Model } from 'mongoose';

import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { ProductService } from '@/resources/product/product.service';
import { PACKAGE_LIMITS } from '@/common/constants';
import { StripeService } from '@/integrations/payment/stipe/stripe.service';
import { SlackService } from '@/common/services/slack.service';
import { UserService } from '@/user/user.service';
import { Settings } from '@/resources/settings/settings.model';
import { Business } from '@/business/business.model';
import { Addon } from '@/resources/product/product.model';
import { User } from '@/user/user.model';

import { CreateSubscriptionDto } from './dto/create-subscription.dto';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    @InjectModel(Business.name) private business: Model<Business>,
    @InjectModel(Settings.name) private settings: Model<Settings>,
    @InjectModel(User.name) private user: Model<User>,
    private readonly creditTransactionService: CreditTransactionService,
    // private readonly businessService: BusinessService,
    private readonly productService: ProductService,
    // private readonly configService: ConfigService,
    private readonly stripeService: StripeService,
    private readonly slackService: SlackService,
    private readonly userService: UserService,
  ) {}

  async createSubscription(
    { business }: AuthenticatedRequest,
    { priceId, coupon, period, plan }: CreateSubscriptionDto,
  ): Promise<{ clientSecret: string; subscriptionRef: string }> {
    let subscriptionRef: string;
    try {
      // Verify Package
      const pkg = await this.stripeService.getPrice(priceId);
      if (
        !(pkg.lookup_key.toLowerCase().endsWith(plan) && period.startsWith(pkg.recurring.interval))
      ) {
        throw new BadRequestException('Plan or period doesn`t match with the package.');
      }

      // Verify Coupon
      if (coupon) {
        await this.stripeService.verifyCoupon(coupon, pkg.product as string, pkg.unit_amount);
      }

      const trialPeriod =
        (await this.settings.findOne({ key: 'SUBSCRIPTION_TRIAL_PERIOD', deleted: null }))?.value ||
        0;
      const isFreePlan = pkg.lookup_key === 'FREE';
      const isTrial = !!trialPeriod || isFreePlan;
      const customerId = business.stripeId;

      const subscription = await this.stripeService.createSubscription({
        customerId,
        priceId,
        trialPeriod,
        coupon,
      });
      subscriptionRef = subscription.id;

      let clientSecret: string;
      let intent: Stripe.PaymentIntent | Stripe.SetupIntent;
      if (isTrial) {
        intent = subscription.pending_setup_intent as Stripe.SetupIntent;
        clientSecret = intent?.client_secret;
      } else {
        const invoice = subscription.latest_invoice as Stripe.Invoice;
        intent = invoice.payment_intent as Stripe.PaymentIntent;
        clientSecret = intent?.client_secret;
      }
      if (!clientSecret) {
        throw new Error('Error creating subscription. Please contact support');
      }

      return { clientSecret, subscriptionRef };
    } catch (error) {
      try {
        await this.stripeService.cancelSubscription(subscriptionRef);
      } catch (e) {
        this.logger.error('Subscription Cancel Error', e);
      }
      this.logger.error('Subscription Create Error:', error);
      throw new InternalServerErrorException(error?.message);
    }
  }

  async confirmSubscription(
    { user, business }: AuthenticatedRequest,
    { intentId, subscriptionRef }: { intentId: string; subscriptionRef: string },
    { coupon }: { coupon?: string },
  ): Promise<{ price?: number; redirect?: string; plan?: string }> {
    try {
      const isTrial = !!intentId?.startsWith('seti');
      const intent = await this.stripeService.getIntent(intentId, isTrial);
      if (!intent) {
        throw new BadRequestException('Invalid payment intent.');
      }

      const subscription = await this.stripeService.getSubscription(subscriptionRef);
      if (!subscription) {
        throw new BadRequestException('Subscription not found.');
      }

      const plan = subscription.items?.data?.[0]?.plan;
      if (!plan) {
        throw new BadRequestException('Subscription plan not found.');
      }

      if (
        !['succeeded', 'requires_action', 'requires_confirmation'].includes(intent.status) &&
        plan.nickname !== 'FREE'
      ) {
        throw new BadRequestException('Payment was not successful.');
      }

      if (intent.next_action?.type === 'redirect_to_url') {
        return { redirect: intent.next_action.redirect_to_url.url };
      }

      await this.confirmMainSubscription(
        { user, business },
        {
          plan: plan.nickname,
          subscription,
          isTrial,
          coupon,
        },
      );

      return { price: plan.amount || 0, plan: plan.nickname };
    } catch (error) {
      try {
        await this.stripeService.cancelSubscription(subscriptionRef);
      } catch (e) {
        this.logger.error('Subscription Cancel Error', e);
      }
      this.logger.error('Subscription Confirm Error', error);
      throw new InternalServerErrorException(error?.message);
    }
  }

  private async confirmMainSubscription(
    { user, business }: { user: UserDocument; business: BusinessDocument },
    {
      subscription,
      isTrial,
      coupon,
      plan,
    }: {
      subscription: Stripe.Subscription;
      isTrial: boolean;
      coupon?: string;
      plan: string;
    },
  ): Promise<void> {
    const { _id: bid } = business;
    const { _id: uid } = user;

    try {
      // Update Business
      await this.business.findByIdAndUpdate(
        bid,
        {
          subscriptionStatus: isTrial && plan !== 'FREE' ? 'trial' : 'active',
          subscriptionId: subscription.id,
          subscriptionPlan: plan,
        },
        { new: true },
      );

      // Add Credits
      if (PACKAGE_LIMITS[plan].CREDITS) {
        this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS[plan].CREDITS);
      }

      // Attach Addons
      try {
        if (!plan.startsWith('LIFETIME_')) {
          if (['PREMIUM', 'BUSINESS', 'ENTERPRISE', 'UNLIMITED'].some((p) => plan.endsWith(p))) {
            await this.productService.attachAddonWithSubscription(bid, Addon.Snippet, plan);
          }
          if (plan.endsWith('BUSINESS')) {
            await this.productService.attachAddonWithSubscription(bid, Addon.YouTube, plan);
          }
          if (['ENTERPRISE', 'UNLIMITED'].some((p) => plan.endsWith(p))) {
            await this.productService.attachAddonWithSubscription(bid, Addon.YouTubePro, plan);
          }
        } else if (plan === 'LIFETIME_ENTERPRISE') {
          await this.productService.attachAddonWithSubscription(bid, Addon.YouTube, plan);
        }
      } catch (error) {
        this.logger.error(`Couldn't attach add with the subscription`, error);
      }

      // Update User
      const isFreePlan = plan === 'FREE';
      const shouldVerifyFreeUsers =
        (await this.settings.findOne({ key: 'VERIFY_FREE_USERS', deleted: null }))?.value || false;

      await this.user.findByIdAndUpdate(uid, {
        verified: isFreePlan && shouldVerifyFreeUsers ? false : true,
      });

      if (isFreePlan) {
        if (shouldVerifyFreeUsers) {
          await this.userService.sendVerificationMail(user.email);
        } else {
          const args: [string, number, string] = [business.id, 15, 'Free Plan Credits'];
          await this.creditTransactionService.addAdditionalCredits(...args);
        }
      }

      // Send Slack Notification
      const params = {
        name: user.name,
        email: user.email,
        businessName: business.name,
        coupon,
        plan,
      };
      if (isTrial && plan !== 'FREE') {
        await this.slackService.sendFreeTrialNotification(params);
      } else if (plan !== 'FREE') {
        await this.slackService.sendPaidPlanNotification(params);
      }
    } catch (e) {
      this.logger.error(e);
      throw new BadRequestException(`Couldn't confirm subscription.`);
    }
  }
}
