import type { Request } from '@auth/interfaces/authenticated-request.interface';
import type { Model } from 'mongoose';

import { BadRequestException, ExecutionContext, CanActivate, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Business } from '@/business/business.model';

@Injectable()
export class SubscriptionCreateGuard implements CanActivate {
  constructor(@InjectModel(Business.name) private business: Model<Business>) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest() as Request;
    const { bid } = request;

    const business = await this.business.findById(bid);

    if (!business || !business.stripeId) {
      throw new BadRequestException('Business does not have stripe customer id.');
    }

    if (business.subscriptionStatus === 'trial') {
      throw new BadRequestException(
        `Trial already in progress for ${business.subscriptionPlan} plan.`,
      );
    }

    if (business.subscriptionStatus === 'active') {
      throw new BadRequestException(
        `Subscription already active for ${business.subscriptionPlan} plan.`,
      );
    }

    request.business = business;
    return true;
  }
}
