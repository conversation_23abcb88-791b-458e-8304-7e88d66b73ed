import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';

import {
  BadRequestException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Redirect,
  Query,
  Body,
  Post,
  Req,
  Get,
} from '@nestjs/common';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';

import { StripeService as PaymentService } from '@/payments/stripe.service';
import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';
import config from '@/common/configs/config';

import { CreateSubscriptionDto, SubscriptionPeriod } from './dto/create-subscription.dto';
import { SubscriptionCreateGuard } from './guards/subscription-create.guard';
import { ConfirmSubscriptionDto } from './dto/confirm-subscription.dto';
import { SubscriptionService } from './subscription.service';

@ApiBearerAuth()
@ApiTags('Subscription')
@Controller('subscriptions')
@UsePipes(new ValidationPipe())
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentService: PaymentService,
  ) {}

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, SubscriptionCreateGuard)
  @Post('')
  async createSubscription(@Req() req: AuthenticatedRequest, @Body() body: CreateSubscriptionDto) {
    if (body.period === SubscriptionPeriod.Lifetime) {
      return this.paymentService.createSubscription(req, body.priceId, body.coupon);
    }
    return this.subscriptionService.createSubscription(req, body);
  }

  @Get('confirm')
  @UseGuards(QueryTokenGuard, Auth, BusinessGuard, SubscriptionCreateGuard)
  @Redirect()
  async confirmSubscription(
    @Req() req: AuthenticatedRequest,
    @Query() query: ConfirmSubscriptionDto,
  ): Promise<{ url: string }> {
    const { subscriptionRef, redirectTo, coupon } = query;
    const intentId = query.payment_intent || query.setup_intent;
    const paymentSecret = query.payment_intent_client_secret || query.setup_intent_client_secret;

    if (!(intentId && paymentSecret && query.redirect_status === 'succeeded')) {
      throw new BadRequestException('Payment was not successful');
    }

    const baseRedirect = `${
      config().internalApps.blogifyClient.url
    }/dashboard?transaction=${intentId}&success=`;

    try {
      const { plan, price, redirect } = await this.subscriptionService.confirmSubscription(
        req,
        { intentId, subscriptionRef },
        { coupon },
      );

      if (redirect) {
        return { url: redirect };
      }

      return {
        url: `${baseRedirect}subscription-success&coupon=${coupon}&price=${price}&plan=${plan}${
          redirectTo ? `&redirectTo=${encodeURIComponent(redirectTo)}` : ''
        }`,
      };
    } catch (error) {
      return {
        url: `${
          config().internalApps.blogifyClient.url
        }/payment?transaction=${intentId}&success=false&error=${error.message}`,
      };
    }
  }
}
