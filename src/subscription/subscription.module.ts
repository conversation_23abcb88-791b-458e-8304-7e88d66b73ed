import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { CreditTransactionModule } from '@resources/credit-transaction/credit-transaction.module';
import { SettingsModule } from '@resources/settings/settings.module';
import { BusinessModule } from '@business/business.module';
import { StripeService } from '@integrations/payment/stipe/stripe.service';
import { ProductModule } from '@resources/product/product.module';
import { SlackService } from '@common/services/slack.service';
import { EventModule } from 'src/event/event.module';
import { UserModule } from '@user/user.module';

import { SubscriptionCreateGuard } from './guards/subscription-create.guard';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import { PaymentsModule } from '../payments/payments.module';

@Module({
  imports: [
    CreditTransactionModule,
    BusinessModule,
    SettingsModule,
    ProductModule,
    PaymentsModule,
    EventModule,
    UserModule,
  ],
  controllers: [SubscriptionController],
  providers: [
    SubscriptionCreateGuard,
    SubscriptionService,
    StripeService,
    SlackService,
    JwtService,
  ],
  exports: [],
})
export class SubscriptionModule {}
