import { IsOptional, ValidateIf, IsString } from 'class-validator';

export class ConfirmSubscriptionDto {
  @IsString()
  subscriptionRef: string;

  @IsOptional()
  @IsString()
  coupon?: string;

  @IsOptional()
  @IsString()
  redirectTo?: string;

  // From Stripe
  @IsString()
  @ValidateIf((dto) => !dto.setup_intent)
  payment_intent?: string;

  @IsOptional()
  @IsString()
  @ValidateIf((dto) => !dto.setup_intent_client_secret)
  payment_intent_client_secret?: string;

  @IsOptional()
  @IsString()
  @ValidateIf((dto) => !dto.payment_intent)
  setup_intent?: string;

  @IsOptional()
  @IsString()
  @ValidateIf((dto) => !dto.payment_intent_client_secret)
  setup_intent_client_secret?: string;

  @IsString()
  redirect_status: string;
}
