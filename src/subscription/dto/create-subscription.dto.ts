import { IsOptional, IsString, IsEnum } from 'class-validator';

export enum SubscriptionPlan {
  Free = 'free',
  Lite = 'lite',
  Basic = 'basic',
  Premium = 'premium',
  Business = 'business',
  Enterprise = 'enterprise',
  UNLIMITED = 'unlimited',
}

export enum SubscriptionPeriod {
  Monthly = 'monthly',
  Yearly = 'yearly',
  Lifetime = 'lifetime',
}

export class CreateSubscriptionDto {
  @IsString()
  priceId: string;

  @IsEnum(SubscriptionPlan)
  plan: SubscriptionPlan;

  @IsEnum(SubscriptionPeriod)
  period: SubscriptionPeriod;

  @IsOptional()
  @IsString()
  coupon: string;
}
