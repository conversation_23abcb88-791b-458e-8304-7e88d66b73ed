import { Logger } from '@nestjs/common';

import { SlackService } from '@/integrations/internal/slack/slack.service';

export function setupProcessErrorHandlers(slackService: SlackService) {
  const logger = new Logger('ProcessErrorHandler');

  process.on('uncaughtException', async (error) => {
    logger.error('Uncaught Exception', error.stack);
    await sendCrashToSlack('Uncaught Exception', error, slackService);
    process.exit(1);
  });

  process.on('unhandledRejection', async (reason: any) => {
    logger.error('Unhandled Rejection', reason?.stack || reason);
    await sendCrashToSlack('Unhandled Rejection', reason, slackService);
    process.exit(1);
  });

  process.on('exit', (code) => {
    logger.warn(`Process exiting with code ${code}`);
  });
}

async function sendCrashToSlack(title: string, error: any, slackService: SlackService) {
  try {
    await slackService.sendMessage({
      channel: 'blogify-alerts-dev',
      message: `${title}: ${error?.message || error}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*${title}*\n\`\`\`${error?.stack || JSON.stringify(error)}\`\`\``,
          },
        },
      ],
    });
  } catch (err) {
    console.error('Failed to send Slack message:', err);
  }
}
