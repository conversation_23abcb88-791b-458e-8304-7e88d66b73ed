//  بسم الله الرحمن الرحيم
// Instructions:
// 1. npm install -g bun
// 2. bun path/to/script.ts
import { MongoClient } from 'mongodb';

const dbUrl =
  'mongodb+srv://blogify-local:<EMAIL>/?retryWrites=true&w=majority';

const productionDbName = 'blogify';
const collectionName = 'blogs';

const mongoClient = new MongoClient(dbUrl);

async function migrateData() {
  try {
    await mongoClient.connect();
    console.log('Successfully established connection to database');
    const db = mongoClient.db(productionDbName);
    const dbCollection = db.collection(collectionName);
    // Retrieve all documents from database
    const documents = await dbCollection.find({ content: { $regex: /```html/g } }).toArray();
    console.log(`Retrieved ${documents.length} documents from database`);
    // Update documents
    const updatePromises = documents.map((doc) => {
      const updatedDoc = {
        ...doc,
        content: doc.content.replace(/```html/g, '').replace(/```/g, ''),
      };
      return dbCollection.updateOne({ _id: doc._id }, { $set: updatedDoc });
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    console.log('Data migration completed successfully');
  } catch (err) {
    console.error('An error occurred during migration:', err);
  } finally {
    // Ensure clients are closed in the end
    await mongoClient.close();
  }
}

migrateData();
