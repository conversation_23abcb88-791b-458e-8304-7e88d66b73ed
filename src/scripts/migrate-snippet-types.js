/* eslint-disable */
const { MongoClient } = require('mongodb');

const stagingConnectionString = 'staging database url';
const productionConnectionString = 'production database url';

const stagingDbName = 'blogify-staging';
const productionDbName = 'blogify';
const collectionName = 'snippet-types';

const stagingClient = new MongoClient(stagingConnectionString);
const productionClient = new MongoClient(productionConnectionString);

const chunkSize = 20;

async function migrateData() {
  try {
    // Connect to staging and production databases
    await stagingClient.connect();
    await productionClient.connect();

    console.log('successfully established connection to both databases');

    const stagingDb = stagingClient.db(stagingDbName);
    const productionDb = productionClient.db(productionDbName);

    const stagingCollection = stagingDb.collection(collectionName);
    const productionCollection = productionDb.collection(collectionName);

    // Fetch data count from staging
    const totalDocuments = await stagingCollection.countDocuments();
    let processed = 0;

    while (processed < totalDocuments) {
      // Fetch chunk of data from staging
      const documents = await stagingCollection.find({}).skip(processed).limit(chunkSize).toArray();

      // Insert chunk of data into production
      await productionCollection.insertMany(documents);

      processed += documents.length;
      console.log(`Migrated ${processed}/${totalDocuments} documents`);
    }

    console.log('Data migration completed successfully');
  } catch (err) {
    console.error('An error occurred during migration:', err);
  } finally {
    // Ensure clients are closed in the end
    await stagingClient.close();
    await productionClient.close();
  }
}

migrateData();
