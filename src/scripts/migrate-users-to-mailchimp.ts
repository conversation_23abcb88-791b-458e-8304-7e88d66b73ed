import * as dotenv from 'dotenv';
import { MongoClient } from 'mongodb';
import * as crypto from 'crypto';
import mailchimp from '@mailchimp/mailchimp_marketing';

dotenv.config();

// Configure Mailchimp client
mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY,
  server: process.env.MAILCHIMP_API_KEY?.split('-')[1],
});

export async function migrateUsersToMailchimp(dryRun = true, usersToMigrate = []) {
  console.log(`Starting ${dryRun ? 'dry run' : 'actual'} migration...`);

  const mongoClient = await MongoClient.connect(process.env.MONGO_URL);
  const db = mongoClient.db(process.env.DB_NAME);

  const batchSize = 100;
  let currentPage = 1;

  while (true) {
    const subscribers = await fetchSubscribersFromMongoDB(
      db,
      currentPage,
      batchSize,
      usersToMigrate,
    );

    if (subscribers.length === 0) {
      break;
    }

    for (const subscriber of subscribers) {
      try {
        const mailchimpContact = createMailchimpContact(subscriber);
        if (!dryRun) {
          const result = await updateMailchimpContact(mailchimpContact);
          if (result === null) {
            console.log(`Skipped processing user: ${subscriber.email}`);
            continue;
          }
        }
        console.log(`${dryRun ? '[DRY RUN] Would update' : 'Updated'} user: ${subscriber.email}`);
      } catch (error) {
        console.error(`Error processing user ${subscriber.email}:`, error);
      }
    }

    currentPage++;
  }

  await mongoClient.close();
}

function createMailchimpContact(subscriber) {
  const contact = {
    email_address: subscriber.email,
    merge_fields: {
      CREDITS: subscriber.business?.credits || 0,
      MONTHLY_CREDITS: subscriber.business?.monthlyCredits || 0,
    },
    tags: getSubscriptionTags(subscriber),
  };

  console.log('Created contact:', JSON.stringify(contact, null, 2));
  return contact;
}

function getSubscriptionTags(subscriber) {
  const tags = [];

  // Add tag for subscription status
  if (subscriber.business && subscriber.business.subscriptionStatus) {
    tags.push(`STATUS: ${subscriber.business.subscriptionStatus}`);
  }

  // Add tag for subscription plan
  if (subscriber.business && subscriber.business.subscriptionPlan) {
    tags.push(`PLAN: ${subscriber.business.subscriptionPlan}`);
  }

  // Add tags for active addons
  if (subscriber.business && subscriber.business.addons) {
    Object.values(subscriber.business.addons).forEach((addon: any) => {
      if (addon?.status === 'active') {
        tags.push(`ADDONS: ${addon.name}`);
      }
    });
  }

  return tags;
}

async function fetchSubscribersFromMongoDB(db, page, batchSize, usersToMigrate = []) {
  const skip = (page - 1) * batchSize;
  const usersCollection = db.collection('users');
  const businessesCollection = db.collection('businesses');

  const query = usersToMigrate.length > 0 ? { email: { $in: usersToMigrate } } : {};
  const users = await usersCollection.find(query).skip(skip).limit(batchSize).toArray();

  const userIds = users.map((user) => user.business);
  const businesses = await businessesCollection.find({ _id: { $in: userIds } }).toArray();

  return users.map((user) => {
    const business = businesses.find((b) => b._id.equals(user.business));
    return { ...user, business };
  });
}

async function updateMailchimpContact(contact) {
  const audienceId = process.env.MAILCHIMP_AUDIENCE_ID;

  if (!audienceId) {
    throw new Error('MAILCHIMP_AUDIENCE_ID is not defined in the environment variables');
  }

  const subscriberHash = crypto
    .createHash('md5')
    .update(contact.email_address.toLowerCase())
    .digest('hex');

  try {
    // First, try to get the member
    await mailchimp.lists.getListMember(audienceId, subscriberHash);

    // If the member exists, update them
    const response = await mailchimp.lists.updateListMember(audienceId, subscriberHash, {
      email_address: contact.email_address,
      merge_fields: contact.merge_fields,
    });

    // Update tags separately
    if (contact.tags && contact.tags.length > 0) {
      await mailchimp.lists.updateListMemberTags(audienceId, subscriberHash, {
        tags: contact.tags.map((tag) => ({ name: tag, status: 'active' })),
      });
    }

    console.log(`Successfully updated contact: ${contact.email_address}`);
    return response;
  } catch (error) {
    if (error.status === 404) {
      // If the member doesn't exist, add them
      try {
        const addResponse = await mailchimp.lists.addListMember(audienceId, {
          email_address: contact.email_address,
          status: 'subscribed',
          merge_fields: contact.merge_fields,
          tags: contact.tags,
        });
        console.log(`Successfully added new contact: ${contact.email_address}`);
        return addResponse;
      } catch (addError) {
        console.error(`Error adding new contact ${contact.email_address}:`, addError);
        throw addError;
      }
    } else {
      console.error(`Error updating contact ${contact.email_address}:`, error);
      throw error;
    }
  }
}

// Uncomment the line below to run the migration script for all users
// migrateUsersToMailchimp(false).catch(console.error);
