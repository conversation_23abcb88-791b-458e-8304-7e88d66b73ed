import { MongooseModule } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { BusinessSchema, Business } from './business.model';
import { BusinessController } from './business.controller';
import { BusinessService } from './business.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: Business.name, schema: BusinessSchema }])],
  providers: [BusinessService, JwtService],
  controllers: [BusinessController],
  exports: [BusinessService, MongooseModule],
})
export class BusinessModule {}
