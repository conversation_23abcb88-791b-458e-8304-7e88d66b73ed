import type { TransactionDocument } from '@resources/transaction/transaction.model';
import type { ProductDocument } from '@resources/product/product.model';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { ALL_SUBSCRIPTION_PLANS } from '@common/constants';
import { BaseModel } from '@/resources/base/base.model';

export enum Interests {
  ANIMALS_AND_PET = 'Animals & Pet',
  ARCHITECTURE = 'Architecture',
  ARTS_AND_CRAFT = 'Arts & Craft',
  AUTOMOTIVE = 'Automotive',
  BAKING = 'Baking',
  BEAUTY = 'Beauty',
  BOOKS = 'Books',
  BUSINESS_AND_FINANCE = 'Business & Finance',
  ENVIRONMENT = 'Environment',
  FAMILY = 'Family',
  FASHION = 'Fashion',
  FOOD_AND_DRINK = 'Food & Drink',
  HEALTH_AND_FITNESS = 'Health & Fitness',
  HOME_INTERIOR = 'Home Interior',
  MUSIC = 'Music',
  ORGANIC_FOOD = 'Organic Food',
  PHOTOGRAPHY = 'Photography',
  SKINCARE = 'Skincare',
  SPORT = 'Sport',
  SUSTAINABILITY = 'Sustainability',
  TECHNOLOGY = 'Technology',
  TRAVEL = 'Travel',
  WEDDING = 'Wedding',
}

export enum SUBSCRIPTION_STATUS {
  TRIAL = 'trial',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
}

export type BusinessDocument = Business & Document;

export type BusinessAddon = {
  status: 'active' | 'deactivated' | 'expired';
  transaction: TransactionDocument;
  addon: ProductDocument;
  expirationDate: Date;
  name: string;
  features?: object;
};

export type FBPage = {
  id: string;
  name: string;
  accessToken: string;
  category: string;
};
@Schema({
  timestamps: true,
})
export class Business extends BaseModel {
  @Prop({ required: true })
  name: string;

  @Prop()
  website?: string;

  @Prop({
    type: [{ type: String, enum: Object.values(Interests) }],
    default: [],
  })
  interests: string[];

  @Prop({ type: Object, default: {} })
  wordpressAccessTokens: Record<string, string>;

  @Prop({ type: Array<string>, default: [] })
  wordpressorgUrlWithSecret?: string[];

  @Prop()
  mailchimpAccessToken?: string;

  @Prop()
  mailchimpServerPrefix?: string;

  @Prop()
  mailchimpUserEmail?: string;

  @Prop({ type: Object, default: {} })
  bloggerTokens: Record<
    string,
    { access_token: string; refresh_token: string; expiry_date: number }
  >;

  @Prop()
  twitterAccessToken?: string;

  @Prop()
  twitterRefreshToken?: string;

  @Prop()
  twitterTokenExpiry?: number;

  @Prop({
    type: Array<{
      id: string;
      name: string;
      accessToken: string;
      category: string;
    }>,
  })
  fbPages?: FBPage[];

  @Prop()
  facebookAccessToken?: string;

  @Prop()
  facebookTokenExpiry?: number;

  @Prop()
  linkedinAccessToken?: string;

  @Prop()
  linkedinTokenExpiry?: number;

  @Prop()
  youtubeAccessToken?: string;

  @Prop()
  youtubeRefreshToken?: string;

  @Prop({ type: Object })
  youtubeProfile?: object;

  @Prop()
  wixAccessToken?: string;

  @Prop()
  wixRefreshToken?: string;

  @Prop()
  wixId?: string;

  @Prop({ type: Object, default: {} })
  mediumAccessTokens: Record<string, string>;

  @Prop({ type: Object, default: {} })
  shopifyAccessTokens: { [shop: string]: string };

  @Prop()
  zapierKey: string;

  @Prop({ type: Array<string>, default: [] })
  zapHookUrls: Array<string>;

  @Prop({ type: Object, default: {} })
  oauth2AppRefreshTokens: { [client_id: string]: Array<string> };

  @Prop({ type: Object, default: {} })
  oauth2AppPublishWebhooks: { [client_id: string]: Array<string> };

  @Prop({ type: String })
  stripeId: string;

  @Prop({ type: String })
  subscriptionId: string;

  @Prop({ type: String, default: ALL_SUBSCRIPTION_PLANS.NOPLAN })
  subscriptionPlan: string;

  @Prop({ type: String })
  customPlan?: string;

  @Prop({
    type: String,
    enum: Object.values(SUBSCRIPTION_STATUS),
    default: SUBSCRIPTION_STATUS.INACTIVE,
  })
  subscriptionStatus: string;

  @Prop({ type: Number, default: 0, min: 0 })
  credits: number;

  @Prop({ type: Number, default: 0, min: 0 })
  monthlyCredits: number;

  @Prop({ type: Date })
  blogCreditLastUpdate: Date;

  @Prop({
    type: Boolean,
    default: false,
  })
  isShopifyUser: boolean;

  @Prop({ type: Boolean, default: false })
  acceptedAffiliateTerms: boolean;

  @Prop({
    type: Object,
    schema: {
      _: {
        status: { type: String, enum: ['active', 'deactivated', 'expired'] },
        transaction: { type: ObjectId, ref: 'Transactions' },
        addon: { type: ObjectId, ref: 'Products' },
        name: String,
        expirationDate: Date,
      },
    },
  })
  addons: Record<string, BusinessAddon>;
}

export const BusinessSchema = SchemaFactory.createForClass(Business);
