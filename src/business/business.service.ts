import { NotFoundException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { BusinessDocument, Interests, Business } from './business.model';
import { ObjectId } from 'mongodb';
import axios from 'axios';

@Injectable()
export class BusinessService extends CrudService<Business> {
  private readonly logger = new Logger(BusinessService.name);

  constructor(@InjectModel(Business.name) private readonly businessModel: Model<Business>) {
    super(businessModel);
  }

  async create(name: string): Promise<Business> {
    const business = new this.businessModel({ name });
    return business.save();
  }

  async countAll(): Promise<number> {
    return this.businessModel.count({}).exec();
  }

  // async findAll(): Promise<Business[]> {
  //   return this.businessModel.find().exec();
  // }

  async findOne(id: string) {
    return this.businessModel.findById(id).exec();
  }

  async findByStripeId(stripeId: string): Promise<BusinessDocument> {
    return this.businessModel.findOne({ stripeId }).exec();
  }

  /**
   * Finds businesses with a specific Zapier API Key
   * @param zapierKey - The API Key specific to each user generated by Blogify
   * @returns An array of businesses which has the API Key
   *
   * @remarks
   * The Zapier API Key is unique for each business. Thus the usual return value ought to be a singleton.
   */
  async findbyZapierKey(zapierKey: string) {
    const businesses = await this.businessModel.find({ zapierKey }).exec();
    return businesses.filter((business) => business.zapierKey === zapierKey);
  }

  /**
   * Updates the ZapHookUrls for a particular business
   * @param businessId - The business ID of the business to be updated
   * @param zapHookUrls - An Array of URLs which containt the hookURLs for this business
   * @returns the updated business
   */
  async updateZapHookUrls(businessId: string | ObjectId, zapHookUrls: Set<string>) {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { zapHookUrls: Array.from(zapHookUrls) },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessInterests(businessId: string, interests: Interests[]): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { interests },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async deleteTokenForPlatform(businessId: string, platform: string): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(businessId, {
      $unset: {
        [`${platform}AccessToken`]: '',
        [`${platform}RefreshToken`]: '',
        [`${platform}TokenExpiry`]: '',
      },
    });

    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessWordpressToken(
    businessId: string,
    userId: string,
    wordpressAccessToken: string,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { $set: { [`wordpressAccessTokens.${userId}`]: wordpressAccessToken } },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  /**
   * Updates the mailchimpAccessToken for the given business.
   * Access tokens from mailchimp are perpetual hence needs not be refreshed.
   * @param businessId - The business ID of the business to be updated
   * @param mailchimpAccessToken - The new access token for mailchimp
   * @returns The updated business
   */
  async updateBusinessMailchimpToken(
    businessId: string,
    mailchimpAccessToken: string,
    mailchimpServerPrefix: string,
    mailchimpUserEmail: string,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { mailchimpAccessToken, mailchimpServerPrefix, mailchimpUserEmail },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessZapierKey(businessId: string, zapierKey: string): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { zapierKey },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  /**
   * Updates the ZapHookUrls for a particular business
   * @param businessId - The business ID of the business to be updated
   * @param wordpressorgUrlWithSecrets - An Set of URLs of the wordpress sites with secrets for this business
   * @returns the updated business
   */
  async updateBusinessWordpressorgUrlWithSecret(
    businessId: string,
    wordpressorgUrlWithSecrets: Set<string>,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { wordpressorgUrlWithSecret: Array.from(wordpressorgUrlWithSecrets) },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessBloggerToken(
    businessId: string,
    bloggerId: string,
    tokens: Business['bloggerTokens'][string],
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { [`bloggerTokens.${bloggerId}`]: tokens },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessTwitterToken(
    businessId: string,
    twitterAccessToken: string,
    twitterRefreshToken: string,
    twitterTokenExpiry?: number,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { twitterAccessToken, twitterRefreshToken, twitterTokenExpiry },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessFacebookToken(
    businessId: string,
    fbPages: object,
    facebookAccessToken: string,
    facebookTokenExpiry?: number,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { fbPages, facebookAccessToken, facebookTokenExpiry },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessLinkedinToken(
    businessId: string,
    linkedinAccessToken: string,
    linkedinTokenExpiry?: number,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { linkedinAccessToken, linkedinTokenExpiry },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessWixToken(
    businessId: string,
    wixAccessToken: string,
    wixRefreshToken: string,
    wixId?: string,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { wixAccessToken, wixRefreshToken, wixId },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateMediumIntegrationToken(
    businessId: string,
    mediumAccessToken: string,
    mediumUserId: string,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      { $set: { [`mediumAccessTokens.${mediumUserId}`]: mediumAccessToken } },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateBusinessYoutubeToken(
    businessId: string,
    youtubeAccessToken: string,
    youtubeRefreshToken?: string,
    youtubeProfile?: object,
  ): Promise<Business> {
    const business = await this.businessModel.findByIdAndUpdate(
      businessId,
      {
        youtubeAccessToken,
        youtubeRefreshToken,
        youtubeProfile,
      },
      { new: true },
    );
    if (!business) {
      throw new NotFoundException(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  async updateSubscriptions(
    businessId: string,
    stripeId: string,
    subscriptionId: string,
    subscriptionPlan: string,
    subscriptionStatus: string,
  ) {
    try {
      const business = await this.businessModel.findByIdAndUpdate(
        businessId,
        {
          stripeId,
          subscriptionId,
          subscriptionPlan,
          subscriptionStatus,
        },
        { new: true },
      );

      this.logger.log({
        stripeId,
        subscriptionId,
        subscriptionPlan,
        subscriptionStatus,
      });

      this.logger.log(business);
      if (!business) {
        throw new NotFoundException(`Business with ID ${businessId} not found`);
      }
      return business;
    } catch (error) {
      this.logger.error(`Error updating subscriptions ${error?.message}`);
      throw error;
    }
  }

  /**
   *
   * Deletes token for a given platform from database
   */
  async deletePlatformConnect(bid: string, platform: string) {
    switch (platform) {
      case 'wordpressorg':
        return await this.businessModel.findByIdAndUpdate(
          bid,
          {
            $unset: {
              [`wordpressorgUrlWithSecret`]: [],
            },
          },
          { new: true },
        );
      case 'mailchimp':
        return await this.businessModel.findByIdAndUpdate(
          bid,
          {
            $unset: {
              [`mailchimpAccessToken`]: '',
              [`mailchimpServerPrefix`]: '',
              [`mailchimpUserEmail`]: '',
            },
          },
          { new: true },
        );
      case 'medium':
        return await this.businessModel.findByIdAndUpdate(
          bid,
          {
            $unset: {
              ['mediumUserId']: '',
              ['mediumIntegrationToken']: '',
            },
          },
          { new: true },
        );
      case 'zapier':
        const { zapHookUrls } = await this.findOne(bid);
        for (const hookUrl of zapHookUrls) await axios.delete(hookUrl);
        const business = await this.businessModel.findByIdAndUpdate(
          bid,
          {
            $unset: {
              ['zapierKey']: '',
              ['zapHookUrls']: [],
            },
          },
          { new: true },
        );
        return business;
      default:
        return await this.businessModel.findByIdAndUpdate(
          bid,
          {
            $unset: {
              [`${platform}AccessToken`]: '',
              [`${platform}RefreshToken`]: '',
              [`${platform}TokenExpiry`]: '',
            },
          },
          { new: true },
        );
    }
  }

  async addCustomPlan(businessId: string, customPlan: string) {
    try {
      const business = await this.businessModel.findByIdAndUpdate(
        businessId,
        {
          customPlan,
        },
        { new: true },
      );
      if (!business) {
        throw new NotFoundException(`Business with ID ${businessId} not found`);
      }
      return business;
    } catch (error) {
      this.logger.error(`Error updating custom plan ${error?.message}`);
      throw error;
    }
  }

  async hasBlogCredit(bid: string, requireCredit = 0): Promise<boolean> {
    const business = await this.businessModel.findById(bid);
    return business.credits >= requireCredit;
  }

  async acceptAffiliateTNC(bid: string) {
    try {
      const business = await this.businessModel.findByIdAndUpdate(
        bid,
        { acceptedAffiliateTerms: true },
        { new: true },
      );
      if (!business) {
        throw new NotFoundException(`Business with ID ${bid} not found`);
      }
      return business;
    } catch (error) {
      this.logger.error(`Error updating custom plan ${error?.message}`);
      throw error;
    }
  }
}
