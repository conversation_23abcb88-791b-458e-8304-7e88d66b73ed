import type { Response } from 'express';

import {
  InternalServerErrorException,
  UnauthorizedException,
  Controller,
  UseGuards,
  Logger,
  Param,
  <PERSON>,
  Query,
  Body,
  Get,
  Req,
  Res,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';

import { Interests, Business } from './business.model';
import { BusinessService } from './business.service';

@Controller('business')
export class BusinessController extends CrudController<Business, any, any> {
  private readonly logger = new Logger(BusinessController.name);

  constructor(
    private readonly businessService: BusinessService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {
    super(businessService);
  }

  @Patch('accept-affiliate-tnc')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async acceptAffiliateTNC(@Req() { bid }: AuthenticatedRequest) {
    try {
      return await this.businessService.acceptAffiliateTNC(bid);
    } catch (error) {
      this.logger.error(`Error updating interests for ${bid}`, error?.message);
      throw new InternalServerErrorException(error?.message);
    }
  }

  @Patch(':businessId/interests')
  async updateBusinessInterests(
    @Param('businessId') businessId: string,
    @Body('interests') interests: Interests[],
  ) {
    try {
      await this.businessService.updateBusinessInterests(businessId, interests);
      return { message: 'Interests updated' };
    } catch (error) {
      this.logger.error(`Error updating interests for ${businessId}`, error?.message);
      throw new InternalServerErrorException(error?.message);
    }
  }

  @Get('disconnect/:platform')
  async disconnectPlatform(
    @Param('platform') platform: string,
    @Query('token') token: string,
    @Res() res: Response,
  ) {
    try {
      if (!token) {
        throw new UnauthorizedException('Token not found');
      }
      const decoded: any = this.jwtService.decode(token, { json: true });
      const { bid } = decoded;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      await this.businessService.deletePlatformConnect(bid, platform);
      return res.redirect(
        this.configService.get('DASHBOARD_REDIRECT_URL') +
          '/dashboard/settings?message=Successfully disconnected from ' +
          platform,
      );
    } catch (error) {
      this.logger.error(`Error disconnecting ${platform}`, error?.message);
      return res.redirect(
        this.configService.get('DASHBOARD_REDIRECT_URL') +
          `/dashboard/settings?error=Unsuccessful attempt to disconnect from  ${platform}: ${error}`,
      );
    }
  }
}
