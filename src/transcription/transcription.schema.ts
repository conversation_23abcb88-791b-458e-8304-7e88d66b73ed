import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { generateId } from '../common/utils';

@Schema()
export class Transcription {
  @Prop({ unique: true, default: generateId() })
  id?: string;

  @Prop()
  summary: string;

  @Prop({ type: Object })
  transcript: object;

  @Prop({ type: Object })
  caption: object;
}

export const TranscriptionSchema = SchemaFactory.createForClass(Transcription);
