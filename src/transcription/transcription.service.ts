import { Injectable } from '@nestjs/common';
import { Transcription } from './transcription.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateTranscriptionDto } from './transcription.dto';

@Injectable()
export class TranscriptionService {
  constructor(
    @InjectModel(Transcription.name)
    private readonly transcriptionModel: Model<Transcription>,
  ) {}

  async create(createTranscriptionDto: CreateTranscriptionDto): Promise<Transcription> {
    const transcription = new this.transcriptionModel();
    return transcription.updateOne(createTranscriptionDto, {
      upsert: true,
    });
  }

  async findAll(): Promise<Transcription[]> {
    return this.transcriptionModel.find().exec();
  }

  async findOne(id: string) {
    return this.transcriptionModel.findById(id);
  }

  async remove(id: string) {
    return this.transcriptionModel.findByIdAndRemove(id, { lean: true });
  }
}
