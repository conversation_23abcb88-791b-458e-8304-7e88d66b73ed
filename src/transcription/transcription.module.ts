import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { CreditTransactionModule } from '@resources/credit-transaction/credit-transaction.module';

import { TranscriptionSchema, Transcription } from './transcription.schema';
import { TranscriptionService } from './transcription.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Transcription.name, schema: TranscriptionSchema }]),
    CreditTransactionModule,
  ],
  providers: [TranscriptionService],
  exports: [TranscriptionService],
})
export class TranscriptionModule {}
