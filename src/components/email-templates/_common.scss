/* Base Styles */
$bgColor: #eef2f5;
$iconBg: #7a8eb2;
$primary: #d44211;
$black: #111827;
$white: #ffffff;
$text: #3c4057;

html,
body {
  font-family: 'Helvetica Neue', Helvetica, sans-serif, Arial;
  margin: 0px auto !important;
  padding: 0px !important;
  height: 100% !important;
  width: 100% !important;
}

* {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  color: $text;
}

div[style*='margin: 16px 0'] {
  margin: 0 !important;
}

table,
td {
  mso-table-lspace: 0pt !important;
  mso-table-rspace: 0pt !important;
}

table {
  border-collapse: collapse !important;
  table-layout: fixed !important;
  border-spacing: 0 !important;
  margin: 0 auto !important;

  table table {
    table-layout: auto;
  }
}

img {
  -ms-interpolation-mode: bicubic;
}

a[x-apple-data-detectors] {
  text-decoration: underline !important;
  color: inherit !important;
}

p {
  line-height: 1.5em;
}
/* End Base Styles */

/* Typography */
.font {
  &.bold {
    font-weight: 600;
  }
}
.fs-14 {
  font-size: 14px;
}

.text {
  &.center {
    text-align: center;
  }
  &.left {
    text-align: left;
  }
  &.right {
    text-align: right;
  }
}
/* End Typography */

/* Spacing */
.mr-12 {
  margin-right: 12px;
}

.mx-12 {
  margin-right: 8px;
  margin-left: 8px;
}

.mt-24 {
  margin-top: 24px;
}
.mt-32 {
  margin-top: 32px;
}

.p-4 {
  padding: 4px;
}

.py-8 {
  padding-bottom: 8px;
  padding-top: 8px;
}
/* End Spacing */

/* Colors */
.bg {
  &.light {
    background-color: $bgColor;
  }
}
.tc-2 {
  color: $iconBg;
}
/* End Colors */

/* Button */
.button {
  &.cta {
    background-color: $primary;
    text-decoration: none;
    border-radius: 4px;
    padding: 0px 24px;
    line-height: 56px;
    display: block;
    color: white;
    height: 56px;
  }
}

/* E-Mail Body */
body.emailer {
  background-color: $bgColor;
  margin: 0;

  center {
    padding: 24px;
    margin: 0px;
  }

  .mail-container {
    max-width: 600px;
    margin: 0px auto;
  }

  .max-width-table {
    max-width: 6000px;
  }

  .mail-content {
    box-shadow: 0 2px 4px 0 rgba(122, 142, 178, 0.16);
    background-color: $white;
  }

  .mail-header {
    height: 120px;
    .main-logo-container {
      justify-content: center;
      align-items: center;
      display: flex;
      gap: 12px;
    }
    .main-logo {
      height: 36px;
    }
    .main-logo-text {
      font-weight: bold;
      font-size: 24px;
      color: $black;
    }
  }

  .mail-body {
    padding: 0px 24px;
    font-size: 16px;
  }

  .mail-sign-off {
    padding: 0px 24px 8px;
  }

  .mail-footer {
    .social-icons {
      a img {
        background-color: $iconBg;
        border-radius: 4px;
      }
    }
  }
}

/* Media Query */
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) {
  body.emailer center .main-container {
    max-width: 350px;
  }
}
/* Transactional Mails Overrides */
