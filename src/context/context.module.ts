import { Modu<PERSON> } from '@nestjs/common';

import { SnippetTypeModule } from '@resources/snippet-type/snippet-type.module';
import { CategoryModule } from '@resources/category/category.module';
import { SettingsModule } from '@resources/settings/settings.module';
import { UrlService } from '@common/services/url.service';

import { BlogifyMediaModule } from '../blogify-media/blogify-media.module';
import { ContextController } from './context.controller';
import { ContextService } from './context.service';

@Module({
  imports: [BlogifyMediaModule, SnippetTypeModule, CategoryModule, SettingsModule],
  controllers: [ContextController],
  providers: [ContextService, UrlService],
  exports: [ContextService],
})
export class ContextModule {}
