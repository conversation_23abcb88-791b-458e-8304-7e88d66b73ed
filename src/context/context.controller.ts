import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import type { Response } from 'express';

import {
  InternalServerErrorException,
  Controller,
  UseGuards,
  Query,
  Body,
  Post,
  Get,
  Req,
  Res,
  Logger,
} from '@nestjs/common';
import axios from 'axios';

import { UrlService } from '@common/services/url.service';
import { Auth } from '@auth/guards/auth.guard';

import { ContextService } from './context.service';
import { FileUploadDto } from './dto/file-upload.dto';
import { UrlMetaDto } from './dto/url-meta.dto';

@Controller('context')
export class ContextController {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    private readonly contextService: ContextService,
    private readonly urlService: UrlService,
  ) {}

  @Get()
  getContext() {
    return this.contextService.getContext();
  }

  @UseGuards(Auth)
  @Get('app')
  getAppContext() {
    return this.contextService.getAppContext();
  }

  @UseGuards(Auth)
  @Post('upload')
  getPresignedPost(@Req() { user }: AuthenticatedRequest, @Body() fileUploadDto: FileUploadDto) {
    return this.contextService.getPresignedPost(fileUploadDto, user);
  }

  @UseGuards(Auth)
  @Post('url-meta')
  getUrlMeta(@Body() { url, sourceName }: UrlMetaDto) {
    return this.urlService.getUrlMeta(url, sourceName);
  }

  @Get('image-proxy')
  async proxyImage(@Query('url') imageUrl: string, @Res() res: Response) {
    try {
      const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });

      res.setHeader('Content-Type', response.headers['content-type']);
      res.send(response.data);
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException();
    }
  }
}
