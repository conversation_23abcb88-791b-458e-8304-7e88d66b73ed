import type { FileUploadDto } from './dto/file-upload.dto';
import type { UserDocument } from '@user/user.model';

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import moment from 'moment';
import aws from 'aws-sdk';

import { SnippetTypeDocument, SnippetType } from '@resources/snippet-type/snippet-type.model';
import { CategoryDocument, Category } from '@resources/category/category.model';
import { SettingsDocument, Settings } from '@resources/settings/settings.model';
import config from '@common/configs/config';

const ONE_MB = 1024 * 1024;
const FIFTEEN_MB = 15 * ONE_MB;

const s3 = new aws.S3({
  secretAccessKey: config().aws.secret,
  accessKeyId: config().aws.access,
  region: config().aws.region,
  signatureVersion: 'v4',
});

@Injectable()
export class ContextService {
  private readonly logger = new Logger('Context');

  constructor(
    @InjectModel(SnippetType.name) private readonly snippetType: Model<SnippetTypeDocument>,
    @InjectModel(Category.name) private readonly category: Model<CategoryDocument>,
    @InjectModel(Settings.name) private readonly settings: Model<SettingsDocument>,
  ) {}

  async getContext() {
    const settings = await this.settings.find({ $or: [{ deleted: false }, { deleted: null }] });
    return { settings };
  }

  async getAppContext() {
    const snippetQuery = { $or: [{ deleted: false }, { deleted: null }] };
    if (config().isProd) {
      snippetQuery['$and'] = [{ visibility: true }];
    }

    const snippetTypes = await this.snippetType
      .find(snippetQuery)
      .select({ _id: 0, __v: 0 })
      .sort({ title: 1 })
      .populate('category', { _id: 0, __v: 0 });

    const categories = await this.category
      .find({ $or: [{ deleted: false }, { deleted: null }] })
      .select({ _id: 0, __v: 0 });

    return {
      snippetTypes,
      categories,
    };
  }

  async getPresignedPost(
    {
      folderName,
      fileName,
      userRef,
      fileType = 'image/',
      minFileSize = 0,
      maxFileSize = FIFTEEN_MB,
    }: FileUploadDto & {
      minFileSize?: number;
      maxFileSize?: number;
    },
    user: UserDocument,
  ) {
    if (!fileType.startsWith('images/')) {
      const userSubscription = user.subscriptionPlan.toLowerCase();
      maxFileSize =
        userSubscription.includes('enterprise') || userSubscription.includes('unlimited')
          ? 200 * ONE_MB // 200 MB
          : userSubscription.includes('business')
            ? 150 * ONE_MB // 150 MB
            : userSubscription.includes('premium')
              ? 100 * ONE_MB // 100 MB
              : userSubscription.includes('basic')
                ? 50 * ONE_MB // 50 MB
                : 30 * ONE_MB; // 30 MB for Lite or Free Plan Users
    }

    const Bucket = config().aws[fileType.startsWith('image/') ? 'imageBucket' : 'bucket'];
    const _fileName = [
      config().isProd ? '' : 'TEST',
      userRef ? `user_${userRef}` : '',
      new Date().getTime(),
      fileName,
    ]
      .filter((k) => !!k)
      .join('_');

    const key = `${folderName}/${_fileName}`;

    const params = {
      Conditions: [
        ['content-length-range', minFileSize, maxFileSize],
        ['starts-with', '$Content-Type', fileType],
      ],
      expiration: moment().add(5, 'minute').toISOString(),
      Fields: {
        ACL: 'public-read',
        key,
      },
      Bucket,
    };

    return new Promise((resolve, reject) => {
      s3.createPresignedPost(params, (err, data) => {
        if (err) {
          this.logger.log('S3 Post Generation Failed', err);
          return reject(new Error('S3 Post Generation Failed'));
        }
        return resolve(data);
      });
    });
  }
}
