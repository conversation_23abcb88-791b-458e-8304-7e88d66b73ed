import type { PushSubscription } from 'web-push';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';
import bcrypt from 'bcrypt';

import { BusinessDocument } from '@/business/business.model';

export type UserDocument = User &
  Document & {
    interests?: string[];
    stripeId?: string;
    subscriptionId?: string;
    subscriptionPlan?: string;
    subscriptionStatus?: string;
    youtubeConnect?: boolean;
    credits?: number;
    isShopifyUser?: boolean;
    acceptedAffiliateTerms?: boolean;
  };

export enum UserRole {
  superadmin = 'superadmin',
  admin = 'admin',
  editor = 'editor',
  writer = 'writer',
}

export enum UserStatus {
  active = 'active',
  invited = 'invited',
  inactive = 'inactive',
  deactivated = 'deactivated',
}

@Schema({
  timestamps: true,
})
export class User {
  // User Info
  @Prop()
  profilePicture?: string;

  @Prop()
  name: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop()
  occupation?: string;

  @Prop()
  bio?: string;

  @Prop()
  city?: string;

  @Prop()
  country?: string;

  @Prop({ type: Object, default: {} })
  socials: Record<string, string>;

  // Auth Info
  @Prop({ type: String, enum: Object.values(UserStatus), default: 'active' })
  status: UserStatus;

  @Prop({ type: String, enum: Object.values(UserRole), default: 'admin' })
  role: UserRole;

  @Prop({ required: false })
  password: string;

  @Prop({ type: String, default: '' })
  googleId: string;

  @Prop({ type: String, default: '' })
  googleAccessToken: string;

  @Prop({ type: String, default: '' })
  googleRefreshToken: string;

  @Prop({ type: Boolean, default: false })
  googleAuthEnabled: boolean;

  @Prop({ type: Object })
  platforms?: Record<string, boolean>;

  // User Settings
  @Prop()
  preferredInputLanguage?: string;

  @Prop()
  preferredOutputLanguage?: string;

  // Internal Info
  @Prop({ type: ObjectId, ref: 'Business' })
  business: BusinessDocument;

  @Prop()
  foundUsFrom?: string;

  @Prop({ type: Boolean, default: true })
  verified?: boolean;

  @Prop()
  ip?: string;

  @Prop({ type: Array, default: [] })
  pushNotificationSubscriptions?: PushSubscription[];

  @Prop()
  deleted: boolean;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.pre('save', async function (next) {
  const user = this as UserDocument;
  if (user.isModified('password')) {
    try {
      user.password = await bcrypt.hash(user.password, 10);
      next();
    } catch (error) {
      next(error);
    }
  }
});
