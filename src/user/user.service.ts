import type { RedisStore } from 'cache-manager-redis-yet';
import type { Cache } from 'cache-manager';

import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectModel } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { ObjectId } from 'mongodb';
import { Model } from 'mongoose';
import * as uuid from 'uuid';

import { ALL_SUBSCRIPTION_PLANS, CUSTOM_PLAN_LIMITS, PACKAGE_LIMITS } from '@/common/constants';
import { UserDocument, UserStatus, User } from '@/user/user.model';
import { BusinessDocument, Business } from '@/business/business.model';
import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { MailService } from '@/modules/mail/mail.service';
import { CrudService } from '@/crud/crud.service';
import { Blog } from '@/blog/blog.model';
import config from '@/common/configs/config';

import { BusinessUserResendInviteDto, BusinessUserInviteDto } from './dto/business-user-invite.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import bcrypt from 'bcrypt';
import { ConfigService } from '@nestjs/config';
import { createHmac } from 'crypto';

@Injectable()
export class UserService extends CrudService<User> {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache<RedisStore>,
    @InjectModel(Business.name) private businessModel: Model<Business>,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
    @InjectModel(User.name) private userModel: Model<User>,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly mailService: MailService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    super(userModel);
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ email }).populate('business');
  }

  async findById(userId: string): Promise<UserDocument> {
    const user = await this.userModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException(`User with id ${userId} not found`);
    }
    const userObject: UserDocument = user.toObject();
    delete userObject.password;
    delete userObject.googleAccessToken;
    delete userObject.googleRefreshToken;
    delete userObject['__v'];
    const business: BusinessDocument = await this.businessModel.findById(userObject.business);

    userObject.interests = business.interests;

    userObject.stripeId = business.stripeId;
    userObject.subscriptionId = business.subscriptionId;
    userObject.subscriptionPlan = business.subscriptionPlan;
    userObject.subscriptionStatus = business.subscriptionStatus;
    userObject.acceptedAffiliateTerms = business.acceptedAffiliateTerms;

    userObject.credits = business.credits;

    userObject.youtubeConnect = !!business.youtubeAccessToken;

    userObject['addons'] = business.addons;
    userObject.isShopifyUser = business.isShopifyUser;

    const { subscriptionPlan, customPlan } = business;
    let packageLimits = PACKAGE_LIMITS[subscriptionPlan];
    try {
      if (customPlan) {
        const planPeriod = subscriptionPlan.split('_')[1];
        if (CUSTOM_PLAN_LIMITS[customPlan] && CUSTOM_PLAN_LIMITS[customPlan][planPeriod]) {
          packageLimits = CUSTOM_PLAN_LIMITS[customPlan][planPeriod];
        }
      }
    } catch (e) {
      this.logger.error(e);
    }
    userObject['monthlyCredits'] = packageLimits?.CREDITS || 0;

    userObject['crispEmailDigest'] = this.generateCrispEmailDigest(user.email);

    return userObject;
  }

  async findOneByGoogleId(googleId: string): Promise<UserDocument> {
    return this.userModel.findOne({ googleId }).exec();
  }

  async getUserByBusinessId(businessId: string): Promise<UserDocument | null> {
    const user = await this.userModel
      .findOne({ business: new ObjectId(businessId) })
      .populate('business')
      .exec();
    this.logger.log('user', user);
    return user;
  }

  async create(createUserDto: Partial<CreateUserDto>): Promise<UserDocument> {
    const { businessName } = createUserDto;

    const user = new this.userModel(createUserDto);

    if (businessName) {
      const { isShopifyUser, isFreeUser } = createUserDto;
      const business = new this.businessModel({ name: businessName });
      user.business = business;
      business.stripeId = createUserDto.stripeId;
      // Set subscription status and FREE plan for free & shopify users
      if (isFreeUser || isShopifyUser) {
        business.subscriptionStatus = 'active';
        business.subscriptionPlan = ALL_SUBSCRIPTION_PLANS.FREE;
        business.isShopifyUser = isShopifyUser;
      }

      const b = await business.save();

      if (isFreeUser || isShopifyUser) {
        await this.creditTransactionService.addMonthlyCredits(
          String(b._id),
          PACKAGE_LIMITS.FREE.CREDITS,
        );
      }
    }

    return user.save();
  }

  async createGoogleUser(profile: any): Promise<UserDocument> {
    const googleUser = new this.userModel({
      email: profile.emails[0].value,
      name: profile.displayName,
      googleId: profile.id,
    });
    return googleUser.save();
  }

  async validateCurrentPassword(userId: string, currentPassword: string): Promise<void> {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.password) return;

    if (!currentPassword) {
      throw new BadRequestException('Current password is required');
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      throw new ForbiddenException('Current password is incorrect');
    }
  }

  async updateProfile(userId: string, updateData: UpdateUserDto) {
    const user = await this.userModel.findById(userId);

    [
      'name',
      'city',
      'email',
      'status',
      'country',
      'password',
      'profilePicture',
      'preferredInputLanguage',
      'preferredOutputLanguage',
    ].forEach((field) => {
      if (updateData[field]) {
        user[field] = updateData[field];
      }
    });

    await user.save();

    return user;
  }

  async forceUpdatePassword(userId: string, newPassword: string) {
    const user = await this.userModel.findById(userId);
    user.password = newPassword;
    await user.save();
    return user;
  }

  async assignBlogsToAdmin(uid: string, bid: string): Promise<void> {
    const adminFilter = {
      role: { $in: ['admin', 'superadmin'] },
      status: UserStatus.active,
      deleted: { $ne: true },
      business: bid,
    };
    const admin = await this.userModel.findOne(adminFilter).sort({ createdAt: 1 }).exec();

    if (!admin) {
      throw new Error('No admin found in the business');
    }

    await this.blogModel.updateMany({ uid, bid }, { uid: admin._id });
  }

  async deletePermanently(id: string): Promise<{ success: boolean }> {
    await this.userModel.findByIdAndDelete(id);
    return { success: true };
  }

  // TODO: Refactor later
  async impersonate(id: string): Promise<{ url: string }> {
    const user = await this.userModel.findById(id);

    if (!user) {
      throw new Error('User not found');
    }

    const payload = {
      sub: user._id,
      email: user.email,
      name: user.name,
      impersonation: true,
    };
    if (user.business) {
      Object.assign(payload, {
        bid: user.business._id,
        bname: user.business.name,
      });
    }
    const token = await this.jwtService.signAsync(payload, {
      expiresIn: '3h',
      secret: config().security.secret,
    });

    const url = `${config().internalApps.blogifyClient.url}/dashboard?token=${token}`;

    return { url };
  }

  async deactivate(id: string): Promise<{ success: boolean }> {
    const user = await this.userModel.findById(id).populate('business');

    if (!user) {
      throw new Error('User not found');
    }

    if (
      user.business.subscriptionStatus === 'inactive' &&
      ['FREE', 'NOPLAN'].includes(user.business.subscriptionPlan)
    ) {
      throw new Error(`User doesn't have any subscription.`);
    }

    const businessUpdates = {
      subscriptionStatus: 'inactive',
      subscriptionPlan: 'FREE',
    };

    await this.businessModel.findByIdAndUpdate(user.business._id, businessUpdates).exec();
    await this.creditTransactionService.deductUnusedMonthlyCredits(
      { bid: String(user.business._id) },
      user.business.monthlyCredits,
    );

    return { success: true };
  }

  async sendVerificationMail(email: string) {
    const user = await this.userModel.findOne({ email, verified: false });
    if (!user) {
      throw new NotFoundException('User not found.');
    }

    const token = uuid.v4();
    await this.distributedCache.set(token, user._id, 48 * 60 * 60 * 1000);
    await this.mailService.send({
      name: user.name,
      to: user.email,
      subject: `Email Verification`,
      template: 'verify-email',
      context: {
        ctaUrl: `${config().internalApps.blogifyAPI.url}/users/verify/${token}`,
      },
    });
  }

  async verifyMail(token: string) {
    const userId: string = await this.distributedCache.get(token);
    if (!userId) {
      throw new NotFoundException('Invalid or expired verification link.');
    }
    this.distributedCache.del(token);

    const user = await this.userModel.findOne({ _id: userId, verified: false });
    if (!user) {
      throw new NotFoundException('User not found.');
    }

    await this.update(user.id, { verified: true });
    await this.creditTransactionService.addAdditionalCredits(
      String(user.business),
      15,
      'Email Verification Reward',
    );
  }

  async inviteUserToBusiness(
    bid: string,
    user: UserDocument,
    dto: BusinessUserInviteDto | null,
    _newUser?: UserDocument,
  ): Promise<{ success: boolean }> {
    const userCount = await this.userModel.count({ business: bid });
    if (userCount >= (PACKAGE_LIMITS[user.subscriptionPlan]?.MAX_USERS || 0)) {
      throw new ForbiddenException(
        `You've reached the maximum number of users for your subscription plan. ${
          userCount < 50 ? 'Please upgrade to add more users.' : ''
        }`,
      );
    }

    let newUser = _newUser;
    if (!newUser && dto) {
      const { name, email, role } = dto;
      newUser = new this.userModel({ name, email, role, status: 'invited', business: bid });
      await newUser.save();
    }
    await this.sendInviteUserToBusinessMail(user, newUser);

    return { success: true };
  }

  async resendInviteUserToBusiness(
    user: UserDocument,
    { email }: BusinessUserResendInviteDto,
  ): Promise<{ success: boolean }> {
    const invitedUser = await this.userModel.findOne({
      email,
      status: { $in: [UserStatus.invited, UserStatus.inactive] },
    });
    if (!user) {
      throw new NotFoundException('User not found.');
    }
    await this.sendInviteUserToBusinessMail(user, invitedUser);
    invitedUser.status = UserStatus.invited;
    await invitedUser.save();
    return { success: true };
  }

  async toggleActiveStatus(userId: string) {
    const user = await this.userModel.findById(userId);
    user.status =
      user.status === UserStatus.deactivated ? UserStatus.active : UserStatus.deactivated;
    await user.save();
  }

  private async sendInviteUserToBusinessMail(sender: UserDocument, user: UserDocument) {
    const invitationId = uuid.v4();
    await this.distributedCache.set(invitationId, user._id, 48 * 60 * 60 * 1000);
    await this.mailService.send({
      name: user.name,
      to: user.email,
      subject: `You've been invited to join ${sender.name}'s team on Blogify AI`,
      template: 'user-invitation',
      context: {
        ctaUrl: `${config().internalApps.blogifyClient.url}/accept-invitation/${invitationId}`,
        inviter: sender.name,
      },
    });
  }

  generateCrispEmailDigest(email: string): string {
    return createHmac('sha256', this.configService.get<string>('CRISP_SECRET_KEY'))
      .update(email)
      .digest('hex');
  }
}
