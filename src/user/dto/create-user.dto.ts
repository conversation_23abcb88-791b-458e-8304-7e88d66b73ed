import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';

export class CreateUserDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;

  @IsString()
  @IsOptional()
  businessName?: string;

  @IsString()
  @IsOptional()
  stripeId?: string;

  @IsString()
  @IsOptional()
  profilePicture?: string;

  @IsString()
  foundUsFrom?: string;

  @IsBoolean()
  @IsOptional()
  isShopifyUser?: boolean;

  @IsBoolean()
  @IsOptional()
  isFreeUser?: boolean;

  @IsString()
  @IsOptional()
  ip?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  business!: string;

  @IsString()
  @IsOptional()
  status!: string;

  @IsString()
  @IsOptional()
  role!: string;

  @IsBoolean()
  @IsOptional()
  sendInvite?: boolean;
}
