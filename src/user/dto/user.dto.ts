import { ApiProperty } from '@nestjs/swagger';

export class UserDto {
  @ApiProperty()
  readonly name: string;

  @ApiProperty()
  readonly email: string;

  @ApiProperty()
  readonly role: string;

  @ApiProperty()
  readonly googleId: string;

  @ApiProperty()
  readonly googleAccessToken: string;

  @ApiProperty()
  readonly googleRefreshToken: string;

  @ApiProperty()
  readonly googleAuthEnabled: boolean;

  @ApiProperty()
  readonly city?: string;

  @ApiProperty()
  readonly country?: string;

  @ApiProperty()
  readonly profilePicture?: string;
}
