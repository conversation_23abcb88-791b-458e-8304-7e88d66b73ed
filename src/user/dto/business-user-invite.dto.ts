import { IsS<PERSON>, Is<PERSON><PERSON>, IsE<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '@user/user.model';

export class BusinessUserInviteDto {
  @ApiProperty()
  @IsString()
  readonly name!: string;

  @ApiProperty()
  @IsEmail()
  readonly email!: string;

  @ApiProperty({ enum: UserRole })
  @IsEnum(UserRole)
  readonly role: UserRole;
}

export class BusinessUserResendInviteDto {
  @ApiProperty()
  @IsEmail()
  readonly email!: string;
}
