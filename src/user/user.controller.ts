import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { UserDocument, User } from '@/user/user.model';

import {
  UnprocessableEntityException,
  BadRequestException,
  NotFoundException,
  HttpException,
  Controller,
  UseGuards,
  HttpCode,
  Redirect,
  Headers,
  Delete,
  Logger,
  Param,
  Patch,
  Query,
  Post,
  Body,
  Put,
  Get,
  Req,
} from '@nestjs/common';

import { UserStatus, UserRole } from '@/user/user.model';
import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';
import config from '@/common/configs/config';

import { BusinessUserResendInviteDto, BusinessUserInviteDto } from './dto/business-user-invite.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserService } from './user.service';

@Controller('users')
export class UserController extends CrudController<User, CreateUserDto, UpdateUserDto> {
  private readonly logger = new Logger(UserController.name);
  constructor(private readonly userService: UserService) {
    super(userService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<User>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: bid };
    }
    return this.userService.findAll(query);
  }

  @Post()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async create(
    @Body() body: CreateUserDto,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<User> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.business = bid;
    }

    body.status = body.sendInvite ? UserStatus.invited : UserStatus.inactive;

    const newUser = await this.userService.create(body);
    if (body.sendInvite) {
      await this.userService.inviteUserToBusiness(bid, user, null, newUser);
    }

    return newUser;
  }

  @Patch(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async update(
    @Param('id') id: string,
    @Body() body: UpdateUserDto,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<User> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    query.filter = { _id: id };
    query.limit = 0;
    if (!isRequestFromAdmin) {
      query.filter.business = bid;
    }
    const u = await this.userService.findOneById(id, query);
    if (!u) {
      throw new NotFoundException('User not found');
    }

    return await this.userService.update(id, body);
  }

  @Delete(':id')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async remove(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<{ success: boolean }> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: bid };
    }

    const u = (await this.userService.findOneById(id, query)) as UserDocument;
    if (!u) {
      throw new NotFoundException('User not found.');
    }

    if (u._id.toString() === user._id.toString()) {
      throw new NotFoundException('You cannot delete yourself.');
    }

    await this.userService.assignBlogsToAdmin(id, bid);

    if ([UserStatus.inactive, UserStatus.invited].includes(u.status)) {
      return this.userService.deletePermanently(id);
    }

    const email = `${u.email}_deleted_${Date.now()}`;
    await this.userService.update(id, { email });
    return super.remove(id);
  }

  @Get('me')
  @UseGuards(Auth)
  async getMe(@Req() req: AuthenticatedRequest) {
    try {
      const user = await this.userService.findById(req.user._id);
      return user;
    } catch (error) {
      this.logger.error(`Failed to get user ${req.user.email}`, error?.message);
      return new BadRequestException(error?.message);
    }
  }

  @Put('me')
  @UseGuards(Auth)
  async updateProfile(
    @Req() { uid, user }: AuthenticatedRequest,
    @Body() updateDto: UpdateUserDto,
  ) {
    try {
      if (updateDto.password) {
        await this.userService.validateCurrentPassword(uid, updateDto.currentPassword);
      }

      const user = await this.userService.updateProfile(uid, updateDto);
      return user;
    } catch (error) {
      this.logger.error(`Failed to update user ${user.email}`, error?.message);
      if (error instanceof HttpException) {
        throw error;
      }
      return new BadRequestException(error?.message);
    }
  }

  @Get(':id/impersonate')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async impersonate(@Param('id') id: string) {
    try {
      const resp = await this.userService.impersonate(id);
      return resp;
    } catch (e) {
      if (e.message.includes('not found')) {
        throw new NotFoundException(e.message);
      }
      throw new UnprocessableEntityException(e.message);
    }
  }

  @Put(':id/deactivate')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async deactivate(@Param('id') id: string) {
    try {
      const resp = await this.userService.deactivate(id);
      return resp;
    } catch (e) {
      this.logger.log(e);
      if (e.message.includes('not found')) {
        throw new NotFoundException(e.message);
      }
      throw new UnprocessableEntityException(e.message);
    }
  }

  @Get('verify/:token')
  @Redirect()
  async verifyMail(@Param('token') token: string) {
    let url = `${config().internalApps.blogifyClient.url}/dashboard`;
    try {
      await this.userService.verifyMail(token);
    } catch (error) {
      url = `${url}?error=${error?.message}`;
    }
    return { url };
  }

  @Post('verify/resend')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async resendVerificationEmail(@Req() { user }: AuthenticatedRequest) {
    return this.userService.sendVerificationMail(user.email);
  }

  @Post('invite')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async inviteUserToBusiness(
    @Req() { bid, ...req }: AuthenticatedRequest,
    @Body() body: BusinessUserInviteDto,
  ) {
    return this.userService.inviteUserToBusiness(bid, req.user, body);
  }

  @Post('invite/resend')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async resendInviteUserToBusiness(
    @Req() req: AuthenticatedRequest,
    @Body() body: BusinessUserResendInviteDto,
  ) {
    return this.userService.resendInviteUserToBusiness(req.user, body);
  }

  @Post(':id/toggle-active-status')
  @Roles(UserRole.admin)
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async toggleActiveStatus(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { user, bid }: AuthenticatedRequest,
  ): Promise<{ success: boolean }> {
    query.filter = { business: bid };
    const u = (await this.userService.findOneById(id, query)) as UserDocument;
    if (!u) {
      throw new NotFoundException('User not found.');
    }

    if (u._id.toString() === user._id.toString()) {
      throw new NotFoundException('You cannot disable yourself.');
    }

    await this.userService.toggleActiveStatus(id);
    return { success: true };
  }

  @Post('change-role')
  @Roles(UserRole.admin)
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @HttpCode(204)
  async updateUser(@Body('email') email: string, @Body('role') role: UserRole) {
    const user = await this.userService.findByEmail(email);
    user.role = role;
    await user.save();
  }

  @Delete(':id/delete')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async deletePermanently(@Param('id') id: string): Promise<{ success: boolean }> {
    return this.userService.deletePermanently(id);
  }
}
