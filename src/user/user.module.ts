import { MongooseModule } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { CreditTransactionModule } from '@/resources/credit-transaction/credit-transaction.module';
import { BlogSchema, Blog } from '@/blog/blog.model';
import { BusinessModule } from '@/business/business.module';
import { S3Service } from '@/common/services/s3.service';

import { UserSchema, User } from './user.model';
import { UserController } from './user.controller';
import { UserService } from './user.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Blog.name, schema: BlogSchema }]),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    CreditTransactionModule,
    BusinessModule,
  ],
  providers: [UserService, JwtService, S3Service],
  exports: [MongooseModule, UserService],
  controllers: [UserController],
})
export class UserModule {}
