import { Controller, Get, Inject } from '@nestjs/common';
import { HealthCheck, HealthCheckService, MongooseHealthIndicator } from '@nestjs/terminus';
import Redis from 'ioredis';

@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly mongoose: MongooseHealthIndicator,
    @Inject('REDIS_CLIENT') private readonly redisClient: Redis,
  ) {}

  @Get('readiness')
  @HealthCheck()
  readiness() {
    return this.health.check([
      () => this.mongoose.pingCheck('mongodb', { timeout: 300 }),
      () => ({
        redis: {
          status: this.redisClient.status === 'ready' ? 'up' : 'down',
        },
      }),
    ]);
  }

  @Get('liveness')
  liveness() {
    return { status: 'ok' };
  }
}
