import type { Response } from 'express';

import { json } from 'body-parser';

import RequestWithRawBody from './rawBody.interface';

function rawBodyMiddleware(): ReturnType<typeof json> {
  return json({
    verify: (request: RequestWithRawBody, response: Response, buffer: Buffer) => {
      if (request.url === '/payments/stripe-webhook' && Buffer.isBuffer(buffer)) {
        request.rawBody = Buffer.from(buffer);
      }
      return true;
    },
  });
}

export default rawBodyMiddleware;
