export interface Config {
  nest: NestConfig;
  cors: CorsConfig;
  security: SecurityConfig;

  environment: 'production' | 'staging' | 'development' | 'test';
  isProd: boolean;
  isStaging: boolean;
  isTest: boolean;
  isDev: boolean;

  stripeProductId: string;

  internalApps: InternalApps;

  logger: LoggerConfig;
  swagger: SwaggerConfig;
  cache: CacheConfig;
  aws: AWS;
  mail: Mail;

  // Internal Integrations
  openAI: { apiKey: string };
  assemblyAI: {
    serviceUrl: string;
    token: string;
  };
  deepgram: {
    token: string;
  };
  slack: {
    signingSecret: string;
    botToken: string;
    token: string;
    channels: {
      signup: string;
      paidSignup: string;
      addonSubscriptions: string;
    };
  };
  github: {
    authToken: string;
  };
  vercel: {
    apiToken: string;
  };
  vapid: {
    privateKey: string;
    publicKey: string;
  };
  // Payment Integration
  stripe: {
    secreteKey: string;
    webhookSecret: string;
    lifetimeToken: string;
  };
  // Affiliate Network Integration
  impact: {
    accountSid: string;
    authToken: string;
  };
  shareASale: {
    merchantId: string;
    apiSecret: string;
    apiToken: string;
  };
  // Blog & Site Integrations
  wordpress: Integration;
  mailchimp: Integration;
  blogger: Integration;
  wix: Integration;
  spotify: Integration;
  // Social Integrations
  google: Integration;
  facebook: Integration;
  twitter: TwitterIntegration;
  linkedIn: Integration;

  // Security
  blockedCountries: string;
  blockedIpAddresses: string;

  // Feature Flags
  featureFlags: {
    isReviewRequired: boolean;
  };
}

interface NestConfig {
  port: number;
}

interface CorsConfig {
  enabled: boolean;
}

interface LoggerConfig {
  sentry: string;
}

interface SwaggerConfig {
  enabled: boolean;
  favIcon: string;
  docs: {
    title: string;
    description: string;
    version: string;
    path: string;
    pageTitle: string;
    pathNameFilter?: string;
    username: string;
    password: string;
  }[];
}

interface CacheConfig {
  redis: {
    host: string;
    port: number;
    password?: string;
  };
}

interface SecurityConfig {
  secret: string;
  expiresIn: string;
  refreshIn: string;
  bcryptSaltOrRound: string | number;
}

interface InternalApp {
  url: string;
  apiKey?: string;
}

interface InternalApps {
  blogifyAPI: InternalApp;
  blogifyAdmin: InternalApp;
  blogifyClient: InternalApp;
  blogifyMedia: InternalApp;
  blogifyML: InternalApp;
}

interface Mail {
  mailChimp: { apiKey: string };
  brevo: { apiKey: string };
  registration: string;
  support: string;
  notify: string;
  supportEmail: string;
}

interface AWS {
  access: string;
  secret: string;
  region: string;
  bucket: string;
  imageBucket: string;
}

interface Integration {
  clientId: string;
  clientSecret: string;
}

interface TwitterIntegration extends Integration {
  appId: number;
  apiKey: string;
  apiSecret: string;
  token: string;
}
