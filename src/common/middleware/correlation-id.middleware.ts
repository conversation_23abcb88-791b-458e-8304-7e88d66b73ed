import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Middleware to ensure each request has a correlation ID
 * It either uses an existing correlation ID from the request headers
 * or generates a new one if none exists.
 */
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Get existing correlation ID or generate a new one
    const correlationId = req.headers['x-correlation-id'] || uuidv4();

    // Set correlation ID in request headers if it doesn't exist
    req.headers['x-correlation-id'] = correlationId;

    // Add correlation ID to response headers to allow client tracking
    res.setHeader('x-correlation-id', correlationId);

    next();
  }
}
