export class AnswerError extends Error {
  constructor(humanReadableMessage: string) {
    super(humanReadableMessage);
    this.name = 'ExpectedError';
  }
}

export class UnauthenticatedAnswerError extends AnswerError {
  constructor() {
    super('Authentication is required to access this resource');
  }
}

export class ForbiddenAnswerError extends AnswerError {
  constructor() {
    super('Permission denied to access this resource');
  }
}
