import { appendFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { createLogger } from './logger';

/**
 * AgentLogger class for logging outputs from all blog generation agents to a single file
 */
export class AgentLogger {
  private static instance: AgentLogger;
  private readonly logger = createLogger('AgentLogger');
  private readonly logDirectory: string;
  private readonly logFilePath: string;
  private sessionId: string;
  private readonly isDebugMode: boolean;

  private constructor() {
    // Check if we're in debug mode
    this.isDebugMode = process.env.NODE_ENV === 'debug';

    // Define log directory relative to project root
    this.logDirectory = join(process.cwd(), 'logs', 'agents');

    // Create logs directory if it doesn't exist and we're in debug mode
    if (this.isDebugMode) {
      this.ensureLogDirectory();

      // Generate default log filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.logFilePath = join(this.logDirectory, `agent-logs-${timestamp}.log`);

      // Initialize with a random session ID
      this.sessionId = this.generateSessionId();

      this.logger.debug(`AgentLogger initialized with log file: ${this.logFilePath}`);
    } else {
      // Set dummy values when not in debug mode
      this.logFilePath = '';
      this.sessionId = '';
    }
  }

  /**
   * Get singleton instance of AgentLogger
   */
  public static getInstance(): AgentLogger {
    if (!AgentLogger.instance) {
      AgentLogger.instance = new AgentLogger();
    }
    return AgentLogger.instance;
  }

  /**
   * Set a session ID for grouping related agent logs
   * @param sessionId - The session ID (e.g., blog ID or unique request ID)
   */
  public setSessionId(sessionId: string): void {
    if (!this.isDebugMode) return;

    this.sessionId = sessionId;
    this.logMessage('SESSION', `Started new session: ${sessionId}`);
  }

  /**
   * Log agent input message
   * @param agentName - Name of the agent
   * @param input - The input provided to the agent
   */
  public logInput(agentName: string, input: any): void {
    if (!this.isDebugMode) return;

    const inputStr = typeof input === 'object' ? JSON.stringify(input, null, 2) : input.toString();
    this.logMessage(agentName, `INPUT: ${inputStr}`);
  }

  /**
   * Log agent output message
   * @param agentName - Name of the agent
   * @param output - The output from the agent
   */
  public logOutput(agentName: string, output: any): void {
    if (!this.isDebugMode) return;

    const outputStr =
      typeof output === 'object' ? JSON.stringify(output, null, 2) : output.toString();
    this.logMessage(agentName, `OUTPUT: ${outputStr}`);
  }

  /**
   * Log agent error
   * @param agentName - Name of the agent
   * @param error - Error that occurred
   */
  public logError(agentName: string, error: any): void {
    if (!this.isDebugMode) return;

    const errorMessage =
      error instanceof Error ? `${error.message}\n${error.stack}` : error.toString();
    this.logMessage(agentName, `ERROR: ${errorMessage}`);
  }

  /**
   * Log state change or important event
   * @param agentName - Name of the agent
   * @param event - Event description
   * @param data - Additional data for the event
   */
  public logEvent(agentName: string, event: string, data?: any): void {
    if (!this.isDebugMode) return;

    let message = `EVENT: ${event}`;
    if (data) {
      const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data.toString();
      message += `\nDATA: ${dataStr}`;
    }
    this.logMessage(agentName, message);
  }

  /**
   * Ensure the log directory exists
   * Creates the directory structure if it doesn't exist
   */
  private ensureLogDirectory(): void {
    if (!existsSync(this.logDirectory)) {
      try {
        mkdirSync(this.logDirectory, { recursive: true });
        this.logger.debug(`Created log directory: ${this.logDirectory}`);
      } catch (error) {
        this.logger.error(`Failed to create log directory: ${error}`);
      }
    }
  }

  /**
   * Generate a random session ID
   */
  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  /**
   * Write message to the log file
   * @param agentName - Name of the agent
   * @param message - Message to log
   */
  private logMessage(agentName: string, message: string): void {
    if (!this.isDebugMode) return;

    try {
      const timestamp = new Date().toISOString();
      const formattedMessage = `[${timestamp}] [${this.sessionId}] [${agentName}] ${message}\n${'='.repeat(80)}\n`;

      // Write to file synchronously to ensure proper sequence
      appendFileSync(this.logFilePath, formattedMessage);
    } catch (error) {
      this.logger.error(`Failed to write agent log: ${error}`);
    }
  }
}

// Export a singleton instance
export const agentLogger = AgentLogger.getInstance();
