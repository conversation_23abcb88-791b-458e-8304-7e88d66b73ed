import { Injectable, LoggerService, Optional, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import pino from 'pino';
import { createWriteStream } from 'pino-stackdriver';

@Injectable()
export class CustomPinoLogger implements LoggerService {
  private readonly logger: pino.Logger;
  private context?: string;
  private correlationId?: string;

  constructor(@Optional() @Inject('LOGGER_CONTEXT') context?: string) {
    this.context = context;

    // Base logger options
    const loggerOptions: pino.LoggerOptions = {
      level: process.env.LOG_LEVEL || process.env.NODE_ENV === 'debug' ? 'debug' : 'info',
      redact: ['password', 'passwordConfirmation', 'authorization', 'cookie'],
    };

    // Only add pretty printing transport in non-production environments
    if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'staging') {
      loggerOptions.transport = {
        target: 'pino-pretty',
        options: {
          colorize: true,
          levelFirst: true,
          translateTime: 'yyyy-mm-dd HH:MM:ss.l',
          messageFormat: '{context}: {msg}',
          ignore: 'pid,hostname,context',
          singleLine: true,
        },
      };
    }

    this.logger = pino(loggerOptions);
  }

  createChildLogger(context?: string, correlationId?: string): CustomPinoLogger {
    const childLogger = new CustomPinoLogger(context || this.context);
    if (correlationId) {
      childLogger.setCorrelationId(correlationId);
    }
    return childLogger;
  }

  setCorrelationId(correlationId: string): void {
    this.correlationId = correlationId;
  }

  getCorrelationId(): string {
    if (!this.correlationId) {
      this.correlationId = uuidv4();
    }
    return this.correlationId;
  }

  private getBaseLogObject(): Record<string, any> {
    const logObj: Record<string, any> = {};

    if (this.context) {
      logObj.context = this.context;
    }

    if (this.correlationId) {
      logObj.correlation_id = this.correlationId;
    }

    return logObj;
  }

  error(message: any, stack?: string, context?: string): void {
    const logObj = this.getBaseLogObject();
    logObj.message = typeof message === 'object' ? JSON.stringify(message) : message;
    logObj.msg = logObj.message;

    if (stack) {
      logObj.stack = stack;
    }

    if (context && !this.context) {
      logObj.context = context;
    }

    this.logger.error(logObj);
  }

  warn(message: any, context?: string): void {
    const logObj = this.getBaseLogObject();
    logObj.message = typeof message === 'object' ? JSON.stringify(message) : message;
    logObj.msg = logObj.message;

    if (context && !this.context) {
      logObj.context = context;
    }

    this.logger.warn(logObj);
  }

  log(message: any, context?: string): void {
    const logObj = this.getBaseLogObject();
    logObj.message = typeof message === 'object' ? JSON.stringify(message) : message;
    logObj.msg = logObj.message;

    if (context && !this.context) {
      logObj.context = context;
    }

    this.logger.info(logObj);
  }

  debug(message: any, context?: string): void {
    const logObj = this.getBaseLogObject();
    logObj.message = typeof message === 'object' ? JSON.stringify(message) : message;
    logObj.msg = logObj.message;

    if (context && !this.context) {
      logObj.context = context;
    }

    this.logger.debug(logObj);
  }

  verbose(message: any, context?: string): void {
    const logObj = this.getBaseLogObject();
    logObj.message = typeof message === 'object' ? JSON.stringify(message) : message;
    logObj.msg = logObj.message;

    if (context && !this.context) {
      logObj.context = context;
    }

    this.logger.trace(logObj);
  }
}

export const createLogger = (context?: string, correlationId?: string) => {
  const logger = new CustomPinoLogger(context);
  if (correlationId) {
    logger.setCorrelationId(correlationId);
  }
  return logger;
};

export const defaultLogger = createLogger();

export const createLoggerWithCorrelationId = (context?: string) => {
  const logger = createLogger(context);
  logger.setCorrelationId(uuidv4());
  return logger;
};

export const setupCloudLogging = () => {
  if (process.env.NODE_ENV !== 'production') {
    return undefined;
  }

  // Check for required GCP project ID
  if (!process.env.GOOGLE_CLOUD_PROJECT) {
    console.warn('GOOGLE_CLOUD_PROJECT environment variable is missing, cloud logging disabled');
    return undefined;
  }

  const config = {
    projectId: process.env.GOOGLE_CLOUD_PROJECT,
    logName: 'blogify_logs',
    resource: {
      type: 'k8s_container',
      labels: {
        namespace_name: process.env.K8S_NAMESPACE,
        pod_name: process.env.K8S_POD_NAME,
        service_name: process.env.K8S_SERVICE_NAME,
      },
    },
  };

  try {
    return {
      stream: createWriteStream(config),
    };
  } catch (error) {
    console.error('Failed to setup cloud logging:', error);
    return undefined;
  }
};
