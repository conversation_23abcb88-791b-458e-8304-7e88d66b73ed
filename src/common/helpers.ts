import { encode } from 'gpt-3-encoder';
import { WordTokenizer } from 'natural';

export const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

export const countTokens = (text: string): number => {
  return encode(text).length;
};

export const calculateCost = (input: string | string[], model) => {
  if (!Array.isArray(input)) {
    input = [input];
  }
  const unit = input.reduce((acc, inp) => countTokens(inp) / 1000 + acc, 0);
  const price = model.startsWith('text-davinci-') ? 0.02 : 0.002;
  return unit * price;
};

/**
 * Get the array of input to the GPT-3 model. An input is concatenated from caption sentences and its total
 * tokens is avoided to exceed the GPT-3 token request limit
 * @category GPT3 - Helpers
 * @param algoCaptions Captions of the meeting
 * @param maxTokens The max number of tokens that can be in any GPT3 input
 * @param inputs the inputs accumulator
 * @param start the index of captions where the new input begins
 */
export const getInputs = (
  algoCaptions: any[],
  maxTokens: number,
  inputs = [] as string[],
  start = 0,
): string[] => {
  console.debug(`helpers:getInputs:debug:start`, {
    algoCaptions: algoCaptions.length,
    maxTokens,
    inputs: inputs.length,
  });

  if (start === algoCaptions.length) {
    return inputs;
  }

  /** we assume the output of encode of gpt-3-encoder is linear according the passed strings */
  let tokens = 0;

  const sentences: string[] = [];

  for (let i = start; i < algoCaptions.length; i++) {
    const sentence = algoCaptions[i].sentence;

    if (!tokens) {
      tokens += countTokens(sentence);
    } else {
      tokens += countTokens(' ' + sentence);
    }

    const isAppendable = tokens <= maxTokens;
    start++;
    if (!isAppendable) {
      break;
    }
    sentences.push(sentence);
  }

  if (sentences.length) {
    inputs.push(sentences.join(' '));
  }

  console.debug(`helpers:getInputs:debug:end`, {
    algoCaptions: algoCaptions.length,
    maxTokens,
    inputs: inputs.length,
  });

  return getInputs(algoCaptions, maxTokens, inputs, start);
};

export const getDurationMinutes = (durationInSeconds?: number) => {
  if ((!durationInSeconds && durationInSeconds !== 0) || Number.isNaN(durationInSeconds)) {
    return null;
  }
  const durationInMinutes = Number(Math.ceil(durationInSeconds / 60));
  durationInMinutes.toFixed(2);
  return durationInMinutes;
};

export const isLastLetterPunctuation = (text: string) => {
  if (!text) return false;
  const punctuations = ['.', '?', '!'];
  return punctuations.some((p) => text.endsWith(p));
};

export const toHourMinuteSecond = (seconds: number) => {
  if ((!seconds && seconds !== 0) || Number.isNaN(seconds)) return null;
  const date = new Date(seconds * 1000);
  return date.toISOString()?.slice(14, 19);
};

export const verifyBullCredentials = (username: string, pass: string): boolean => {
  const { BULL_USERNAME, BULL_PASSWORD } = process.env;
  return username === BULL_USERNAME && pass === BULL_PASSWORD;
};

export const parseArrayString = (input) => {
  // Check if the input is a string
  if (typeof input !== 'string') {
    return [];
  }

  // Remove outer brackets if present
  input = input.trim();
  if (input.startsWith('[') && input.endsWith(']')) {
    input = input?.slice(1, -1);
  }

  // Remove leading and trailing whitespace
  input = input.trim();

  // Check if the input is empty
  if (input.length === 0) {
    return [];
  }

  // Split the input by commas
  const parts = input.split(',').map((part) => part.trim());

  // Remove surrounding quotes if present
  const result = parts.map((part) => {
    if (
      (part.startsWith('"') && part.endsWith('"')) ||
      (part.startsWith("'") && part.endsWith("'"))
    ) {
      return part?.slice(1, -1);
    }
    return part;
  });

  return result;
};

export const createChunks = (text, tokenLimit) => {
  const tokenizer = new WordTokenizer();
  const words = tokenizer.tokenize(text);

  const chunks = [];
  let currentChunk = '';

  for (const word of words) {
    if (currentChunk.length + word.length <= tokenLimit * 3) {
      currentChunk += ' ' + word;
    } else {
      chunks.push(currentChunk.trim());
      currentChunk = word;
    }
  }

  if (currentChunk.trim() !== '') {
    chunks.push(currentChunk.trim());
  }

  return chunks;
};

export const getVideoIdFromYouTubeUrl = (url: string): string => {
  const youtubeUrl = url.replace(/^(?:https?:\/\/)?(?:www\.)?/i, '');

  const patterns = [
    /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([\w-]{11})/i,
    /youtube\.com\/live\/([\w-]{11})/i,
  ];

  const match = patterns.map((pattern) => youtubeUrl.match(pattern)).find(Boolean);
  return match ? match[1] : '';
};

export const getImageFromYouTubeUrl = (url: string) => {
  const videoId = getVideoIdFromYouTubeUrl(url);
  const imageUrl = videoId ? `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg` : '';
  return imageUrl;
};

export const isYouTubeURL = (url: string): boolean => {
  // Regular expressions to match different YouTube URL formats
  const youtubeUrlPatterns = [
    /https?:\/\/(?:www\.|m\.)?youtube\.com\/(?:watch\?(?:[^&]*&)?v=|embed\/|shorts\/|v\/|embed\/videoseries\?list=)([a-zA-Z0-9_-]+)/,
    /https?:\/\/(?:www\.|m\.)?youtu\.be\/([a-zA-Z0-9_-]+)/,
  ];

  // Check if the URL matches any of the patterns
  for (const pattern of youtubeUrlPatterns) {
    if (pattern.test(url)) {
      return true;
    }
  }

  return false;
};

export const convertMillisecondsToMinutes = (milliseconds: number): number => {
  // Convert milliseconds to seconds
  const seconds = Math.floor(milliseconds / 1000);

  // Convert seconds to minutes
  const minutes = Math.floor(seconds / 60);

  // Calculate the remaining seconds
  const remainingSeconds = seconds % 60;

  // Combine minutes and seconds as a float string
  const result =
    minutes + '.' + (remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds);

  // Convert the string to a float
  return parseFloat(result);
};

export const convertToSeconds = (time, delimiter = '.') => {
  const parts = time.toString().split(delimiter);
  const minutes = parseInt(parts[0], 10);
  const seconds = parseInt(parts[1], 10);
  return minutes * 60 + seconds;
};

export const convertToMs = (text: string) => {
  const float = parseFloat(text.split('=')[1].replace(/"/g, '')) * 1000;
  return Math.round(float);
};

type PlaceholderValues = {
  [key: string]: string;
};

export const replacePlaceholders = (template: string, values: PlaceholderValues): string => {
  return template.replace(/{([^}]+)}/g, (_, key) => values[key] || '');
};
