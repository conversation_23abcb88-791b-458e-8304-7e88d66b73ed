//  بسم الله الرحمن الرحيم

/**
 * Custom error class representing internal errors
 * @see Error
 */
class BlogifyError extends Error {
  /**
   * Constructs the custom error class
   * @param message The error message
   * @param options The cause of the error
   * @param details The user details for whom the error occurred
   *
   * @remarks This class is used as a wrapper around built in Errors
   *
   * Example:
   * ```ts
   * throw new BlogifyError('Some Messsage', {cause: new Error('Background Error')}, {businessID: '999', userID: '100'});
   * ```
   */
  constructor(
    message: string,
    options?: { /** The original wrapped Error*/ cause: Error },
    public readonly details?: {
      /** The business ID of the user*/ businessID: string;
      /** The user ID of the user*/ userID: string;
    },
  ) {
    super(message, options);
    this.name = this.constructor.name;
  }
}

export { BlogifyError };
