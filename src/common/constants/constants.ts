import { AdvancedSettings, JobOptions } from 'bull';

export const LATEST_SUBSCRIPTION_PLANS = {
  FREE: 'FREE',
  MONTHLY_LITE: 'MONTHLY_LITE',
  MONTHLY_BASIC: 'MONTHLY_BASIC',
  MON<PERSON><PERSON>Y_PREMIUM: 'MONTHLY_PREMIUM',
  MONTHLY_BUSINESS: 'MONTHLY_BUSINESS',
  MONTHLY_ENTERPRISE: 'MONTHLY_ENTERPRISE',
  MONTHLY_UNLIMITED: 'MONTHLY_UNLIMITED',
  YEARLY_LITE: 'YEARLY_LITE',
  YEARLY_BASIC: 'YEARLY_BASIC',
  YEARLY_PREMIUM: 'YEARLY_PREMIUM',
  YEARLY_BUSINESS: 'YEARLY_BUSINESS',
  YEARLY_ENTERPRISE: 'YEARLY_ENTERPRISE',
  YEARLY_UNLIMITED: 'YEARLY_UNLIMITED',
};

export const ALL_SUBSCRIPTION_PLANS = {
  NOPLAN: 'NOPLAN',
  FREE: 'FREE',
  MON<PERSON><PERSON>Y_STARTER: 'MONTHLY_STARTER',
  MONTHLY_LITE: 'MONTHLY_LITE',
  MONTHLY_BASIC: 'MONTHLY_BASIC',
  MONTHLY_PREMIUM: 'MONTHLY_PREMIUM',
  MONTHLY_BUSINESS: 'MONTHLY_BUSINESS',
  MONTHLY_ENTERPRISE: 'MONTHLY_ENTERPRISE',
  MONTHLY_UNLIMITED: 'MONTHLY_UNLIMITED',
  YEARLY_STARTER: 'YEARLY_STARTER',
  YEARLY_LITE: 'YEARLY_LITE',
  YEARLY_BASIC: 'YEARLY_BASIC',
  YEARLY_PREMIUM: 'YEARLY_PREMIUM',
  YEARLY_BUSINESS: 'YEARLY_BUSINESS',
  YEARLY_ENTERPRISE: 'YEARLY_ENTERPRISE',
  YEARLY_UNLIMITED: 'YEARLY_UNLIMITED',
  LIFETIME_MINI: 'LIFETIME_MINI',
  LIFETIME_STARTER: 'LIFETIME_STARTER',
  LIFETIME_LITE: 'LIFETIME_LITE',
  LIFETIME_BASIC: 'LIFETIME_BASIC',
  LIFETIME_PREMIUM: 'LIFETIME_PREMIUM',
  LIFETIME_BUSINESS: 'LIFETIME_BUSINESS',
  LIFETIME_ENTERPRISE: 'LIFETIME_ENTERPRISE',
  LIFETIME_UNLIMITED: 'LIFETIME_UNLIMITED',
};

export const CUSTOM_PLANS = [
  { plan: 'DEALMIRROR', couponPrefix: 'BLOGIFY_DEALMIRROR' },
  { plan: 'STACKCOMMERCE', couponPrefix: 'BLOGIFY_STACKCOMMERCE' },
  { plan: 'DEALIFY', couponPrefix: 'BLOGIFY_DEALIFY' },
  { plan: 'DEALFUEL', couponPrefix: 'BLOGIFY_DEALFUEL' },
  { plan: 'PITCHGROUND', couponPrefix: 'PG-' },
  { plan: 'APPSUMO', couponPrefix: 'BLOGIFY_APPSUMO' },
  { plan: 'VIEDEDINGUE', couponPrefix: 'BLOGIFY_VIEDEDINGUE' },
] as const;

const ONE_MB = 1000 ** 2;

type PackageLimit = {
  CREDITS: number;
  CREDIT_PRICE: number;
  MAX_USERS?: number;
  MAX_DURATION_IN_SECONDS?: number;
  MAX_UPLOAD_SIZE_IN_BYTES?: Record<'DOCUMENT' | 'AUDIO' | 'VIDEO', number>;
};

const FREE: PackageLimit = {
  CREDITS: 0,
  CREDIT_PRICE: 0.08,
  MAX_USERS: 1,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 5 * ONE_MB,
    AUDIO: 75 * ONE_MB,
    VIDEO: 400 * ONE_MB,
  },
  MAX_DURATION_IN_SECONDS: 30 * 60,
};
const STARTER: PackageLimit = {
  CREDITS: 30,
  CREDIT_PRICE: 0.08,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 5 * ONE_MB,
    AUDIO: 75 * ONE_MB,
    VIDEO: 400 * ONE_MB,
  },
  MAX_DURATION_IN_SECONDS: 30 * 60,
};
const LITE: PackageLimit = {
  CREDITS: 50,
  CREDIT_PRICE: 0.08,
  MAX_USERS: 1,
  MAX_DURATION_IN_SECONDS: 45 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 5 * ONE_MB,
    AUDIO: 75 * ONE_MB,
    VIDEO: 400 * ONE_MB,
  },
};
const BASIC: PackageLimit = {
  CREDITS: 150,
  CREDIT_PRICE: 0.07,
  MAX_USERS: 3,
  MAX_DURATION_IN_SECONDS: 60 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 8 * ONE_MB,
    AUDIO: 100 * ONE_MB,
    VIDEO: 550 * ONE_MB,
  },
};
const PREMIUM: PackageLimit = {
  CREDITS: 300,
  CREDIT_PRICE: 0.06,
  MAX_USERS: 10,
  MAX_DURATION_IN_SECONDS: 90 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 12 * ONE_MB,
    AUDIO: 150 * ONE_MB,
    VIDEO: 800 * ONE_MB,
  },
};
const BUSINESS: PackageLimit = {
  CREDITS: 500,
  CREDIT_PRICE: 0.05,
  MAX_USERS: 50,
  MAX_DURATION_IN_SECONDS: 180 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 20 * ONE_MB,
    AUDIO: 300 * ONE_MB,
    VIDEO: 1600 * ONE_MB,
  },
};
const ENTERPRISE: PackageLimit = {
  CREDITS: 1000,
  CREDIT_PRICE: 0.04,
  MAX_USERS: 200,
  MAX_DURATION_IN_SECONDS: 240 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 30 * ONE_MB,
    AUDIO: 450 * ONE_MB,
    VIDEO: 2400 * ONE_MB,
  },
};
const UNLIMITED: PackageLimit = {
  CREDITS: 5000,
  CREDIT_PRICE: 0.04,
  MAX_USERS: 1000,
  MAX_DURATION_IN_SECONDS: 240 * 60,
  MAX_UPLOAD_SIZE_IN_BYTES: {
    DOCUMENT: 30 * ONE_MB,
    AUDIO: 450 * ONE_MB,
    VIDEO: 2400 * ONE_MB,
  },
};

type PackageDuration = 'MONTHLY' | 'YEARLY' | 'LIFETIME';
type PackageTier =
  | 'STARTER'
  | 'LITE'
  | 'BASIC'
  | 'PREMIUM'
  | 'BUSINESS'
  | 'ENTERPRISE'
  | 'UNLIMITED';
export const PACKAGE_LIMITS: Record<'FREE' | `${PackageDuration}_${PackageTier}`, PackageLimit> = {
  FREE: FREE,
  MONTHLY_STARTER: STARTER,
  MONTHLY_LITE: LITE,
  MONTHLY_BASIC: BASIC,
  MONTHLY_PREMIUM: PREMIUM,
  MONTHLY_BUSINESS: BUSINESS,
  MONTHLY_ENTERPRISE: ENTERPRISE,
  MONTHLY_UNLIMITED: UNLIMITED,
  YEARLY_STARTER: STARTER,
  YEARLY_LITE: LITE,
  YEARLY_BASIC: BASIC,
  YEARLY_PREMIUM: PREMIUM,
  YEARLY_BUSINESS: BUSINESS,
  YEARLY_ENTERPRISE: ENTERPRISE,
  YEARLY_UNLIMITED: UNLIMITED,
  LIFETIME_STARTER: STARTER,
  LIFETIME_LITE: LITE,
  LIFETIME_BASIC: BASIC,
  LIFETIME_PREMIUM: PREMIUM,
  LIFETIME_BUSINESS: BUSINESS,
  LIFETIME_ENTERPRISE: ENTERPRISE,
  LIFETIME_UNLIMITED: UNLIMITED,
} as const;

export const BUFFER_TOKEN_LIMIT = 700;
export const JOB_OPTIONS: JobOptions = {
  attempts: 5,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 50,
  removeOnFail: 100,
  timeout: 300000,
};

export const QueueOpts: AdvancedSettings = {
  retryProcessDelay: 2000,
  lockDuration: 300000,
  lockRenewTime: 30000,
  stalledInterval: 30000,
  maxStalledCount: 3,
  drainDelay: 1000,
};

export const isLastLetterPunctuation = (text: string) => {
  if (!text) return false;
  const punctuations = ['.', '?', '!'];
  return punctuations.some((p) => text.endsWith(p));
};
export const KEYWORDS_EXTRACTION_PROMPT = `We know you are an AI language model from your knowledge find out the most commonly used words which can be used as keywords to place affiliate links.
Step 1: Read the blog carefully from the earlier message context
Step 2: Find the most commonly used words or word combination which can be used to place affiliate links from the existing knowledge you have maintain keywords density of 2%.
Step 3: Return the output as a Javascript Array of String or an empty array if no keywords are found. Please do not add any reason or explanation to the output. Example of output: ["best laptop", "travel"]`;

export const CUSTOM_PLAN_LIMITS: Record<string, Record<string, PackageLimit>> = {
  DEALMIRROR: {
    MINI: {
      CREDITS: 45,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 150,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 375,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 750,
      CREDIT_PRICE: 0.05,
    },
    UNLIMITED: {
      CREDITS: 5000,
      CREDIT_PRICE: 0.04,
    },
  },
  STACKCOMMERCE: {
    STARTER: {
      CREDITS: 30,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 150,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 375,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 750,
      CREDIT_PRICE: 0.05,
    },
    UNLIMITED: {
      CREDITS: 5000,
      CREDIT_PRICE: 0.04,
    },
  },
  DEALIFY: {
    STARTER: {
      CREDITS: 45,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 150,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 375,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 750,
      CREDIT_PRICE: 0.05,
    },
  },
  DEALFUEL: {
    STARTER: {
      CREDITS: 30,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 150,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 375,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 525,
      CREDIT_PRICE: 0.05,
    },
  },
  PITCHGROUND: {
    STARTER: {
      CREDITS: 75,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 300,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 525,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 750,
      CREDIT_PRICE: 0.05,
    },
  },
  APPSUMO: {
    STARTER: {
      CREDITS: 20,
      CREDIT_PRICE: 0.08,
    },
    LITE: {
      CREDITS: 150,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 375,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 750,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 1125,
      CREDIT_PRICE: 0.05,
    },
    ENTERPRISE: {
      CREDITS: 1500,
      CREDIT_PRICE: 0.04,
    },
    UNLIMITED: {
      CREDITS: 5000,
      CREDIT_PRICE: 0.04,
    },
  },
  VIEDEDINGUE: {
    STARTER: {
      CREDITS: 30,
      CREDIT_PRICE: 0.08,
    },
    BASIC: {
      CREDITS: 150,
      CREDIT_PRICE: 0.07,
    },
    PREMIUM: {
      CREDITS: 375,
      CREDIT_PRICE: 0.06,
    },
    BUSINESS: {
      CREDITS: 750,
      CREDIT_PRICE: 0.05,
    },
  },
};
