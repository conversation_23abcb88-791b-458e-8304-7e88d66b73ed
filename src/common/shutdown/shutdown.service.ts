import { Injectable, OnM<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gger } from '@nestjs/common';
import { Queue } from 'bull';
import { ModuleRef } from '@nestjs/core';

/**
 * shutdown service to handle graceful application shutdown
 */
@Injectable()
export class ShutdownService implements OnModuleD<PERSON>roy {
  private readonly logger = new Logger(ShutdownService.name);
  private queues: Queue[] = [];

  constructor(private moduleRef: ModuleRef) {
    // listening termination signals
    this.registerShutdownHooks();
  }

  registerQueue(queue: Queue): void {
    this.queues.push(queue);
  }

  private registerShutdownHooks(): void {
    process.on('SIGTERM', async () => {
      this.logger.log('SIGTERM received, initiating graceful shutdown...');
      await this.gracefulShutdown();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      this.logger.log('SIGINT received, initiating graceful shutdown...');
      await this.gracefulShutdown();
      process.exit(0);
    });
  }

  async onModuleDestroy(): Promise<void> {
    await this.gracefulShutdown();
  }

  private async gracefulShutdown(): Promise<void> {
    this.logger.log('Starting graceful shutdown sequence...');

    await this.pauseAllQueues();
    await this.waitForActiveJobs();
    this.logger.log('Shutdown sequence completed successfully.');
  }

  private async pauseAllQueues(): Promise<void> {
    this.logger.log(`Pausing ${this.queues.length} queue(s)...`);
    const pausePromises = this.queues.map(async (queue) => {
      try {
        this.logger.log(`Pausing queue: ${queue.name}`);
        await queue.pause();
      } catch (error) {
        this.logger.error(`Failed to pause queue ${queue.name}:`, error);
      }
    });

    await Promise.all(pausePromises);
    this.logger.log('All queues paused successfully');
  }

  private async waitForActiveJobs(): Promise<void> {
    this.logger.log('Waiting for active jobs to complete...');

    const MAX_WAIT_TIME = 30000; // 30 seconds max wait time
    const startTime = Date.now();

    // Check for active jobs in all queues
    while (Date.now() - startTime < MAX_WAIT_TIME) {
      let activeJobsCount = 0;

      for (const queue of this.queues) {
        try {
          const activeJobs = await queue.getActive();
          activeJobsCount += activeJobs.length;

          if (activeJobs.length > 0) {
            this.logger.log(`Queue ${queue.name} has ${activeJobs.length} active jobs`);
          }
        } catch (error) {
          this.logger.error(`Error checking active jobs for queue ${queue.name}:`, error);
        }
      }

      if (activeJobsCount === 0) {
        this.logger.log('No active jobs remaining, continuing shutdown');
        break;
      }

      this.logger.log(`Waiting for ${activeJobsCount} active jobs to complete...`);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    if (Date.now() - startTime >= MAX_WAIT_TIME) {
      this.logger.warn('Timeout reached while waiting for active jobs to complete');
    }
  }
}
