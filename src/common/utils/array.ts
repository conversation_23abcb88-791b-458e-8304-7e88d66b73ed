// بشم الله الرحمن الرحيم

export function includes<T extends U, U>(array: ReadonlyArray<T>, item: U): item is T {
  return array.includes(item as T);
}

/**
 * Counts the freqeuncy of array elements
 * @param arr - the input array
 * @returns A Map of array elements as keys and their corresponsing frequency in the array as value
 * @remarks Taken from https://stackoverflow.com/a/57028486
 */
export const countFrequency = <T>(arr: T[]) =>
  arr.reduce((acc, e) => acc.set(e, (acc.get(e) || 0) + 1), new Map<T, number>());
