// بسم الله الرحمن الرحيم
/**
 * The purpose of this module is to house various HTML parsing functions.
 */

import { load } from 'cheerio';
import { range } from 'lodash';

/**
 * The parsed tree's Root
 */
type Root = cheerio.Root;

/**
 * Removes unnecessary whitespace from text
 * @param text - The text to be cleaned
 * @returns The cleaned text
 */
export const pruneWhiteSpace = (text: string) => text.replace(/\s{2,}/g, ' ');

export const containsTag = ($: cheerio.Root, tag: string) => $(tag).length > 0;

export const makeRoot = (html: string) => load(html);
/**
 * Container for various functions to extract text from parsed HTML tree
 */
export const extract = {
  pageTitle: ($: Root) => $('title').text(),

  metaDescription: ($: Root) => $('meta[name="description"]').attr('content') || '',

  bodyText: function parse($: Root) {
    return $(
      'html body *' +
        range(1, 7)
          .map((x) => `:not(h${x})`)
          .join(''),
    )
      .contents()
      .filter(function () {
        return this.type === 'text';
      })
      .filter(function () {
        return !['script', 'style', 'noscript'].includes(this.parent.name);
      })
      .map(function () {
        return $(this).text().trim();
      })
      .get()
      .filter((text) => text.length)
      .join(' ');
  },
  tagText: function ($: cheerio.Root, tag: string): string[] {
    return $(tag)
      .contents()
      .map(function () {
        return $(this).text().trim();
      })
      .get()
      .filter((text) => text.length);
  },
  tagsText: (
    $: Root,
    tags: string[] = [
      'article',
      'main',
      'section',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'p',
      'table',
      'ol',
      'ul',
    ],
  ) => {
    let importantText = '';
    tags.forEach((tag) => {
      $(tag).each((_, elem) => {
        importantText += $(elem).text().trim() + ' ';
      });
    });
    return importantText;
  },
} as const;
