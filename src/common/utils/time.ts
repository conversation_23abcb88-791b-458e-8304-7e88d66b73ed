export const getOffsetTime = (date?: any, offset = 6) => {
  const local = date || new Date();
  const UTC = local.getTime() + local.getTimezoneOffset() * 60000;
  return new Date(UTC + 3600000 * offset);
};

export function toHHMMSS(secs: number, printFull = false) {
  const sec_num = Math.ceil(secs);
  const hours = Math.floor(sec_num / 3600) % 24;

  const minutes = Math.floor(sec_num / 60) % 60;
  const seconds = sec_num % 60;
  const timestamp = [hours, minutes, seconds]
    .map((v) => (v < 10 ? `0${v}` : v))
    .filter((v, i) => printFull || v !== '00' || i > 0)
    .join(':');
  return timestamp;
}

export function toSecFromHHMMSS(hhmmss: string) {
  const [seconds, minutes, hours] = hhmmss
    .split(':')
    .reverse()
    .map((v) => (v ? parseInt(v) : 0));
  return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
}
