import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { OnQueueStalled, OnQueueFailed } from '@nestjs/bull';
import { ShutdownService } from '../shutdown/shutdown.service';

export abstract class BaseProcessor {
  protected readonly logger: Logger;
  protected abstract readonly queueName: string;
  private readonly LOCK_RENEWAL_INTERVAL = 10000; // Reduce to 10 seconds for more frequent renewals
  private readonly HEARTBEAT_INTERVAL = 5000; // More frequent heartbeats
  private readonly JOB_TIMEOUT = 600000; // Increase to 10 minutes
  protected readonly concurrency = 10;

  protected abstract process(job: Job): Promise<any>;

  constructor(
    protected readonly queue: Queue,
    private readonly MAX_RETRY_ATTEMPTS = 5,
    private readonly shutdownService?: ShutdownService,
  ) {
    this.logger = new Logger(this.constructor.name);
    this.initializeQueue();

    // Register this queue with the shutdown service if available
    if (this.shutdownService) {
      this.shutdownService.registerQueue(this.queue);
    }
  }

  private async initializeQueue() {
    try {
      // Only clean stuck jobs on startup
      await this.queue.clean(24 * 3600 * 1000, 'wait'); // Clean waiting jobs
      await this.queue.clean(24 * 3600 * 1000, 'active'); // Clean active jobs
      await this.queue.clean(24 * 3600 * 1000, 'delayed'); // Clean delayed jobs

      // Resume any paused queues
      const isPaused = await this.queue.isPaused();
      if (isPaused) {
        await this.queue.resume();
      }

      this.logger.log(`Queue ${this.queueName} initialized successfully`);

      // Monitor queue metrics every 5 mins
      setInterval(async () => {
        const [waiting, active, completed, failed] = await Promise.all([
          this.queue.getWaitingCount(),
          this.queue.getActiveCount(),
          this.queue.getCompletedCount(),
          this.queue.getFailedCount(),
        ]);
        this.logger.debug(
          `Queue ${this.queueName} metrics - Waiting: ${waiting}, Active: ${active}, Completed: ${completed}, Failed: ${failed}`,
        );
      }, 300000);
    } catch (error) {
      this.logger.error(`Failed to initialize queue ${this.queueName}:`, error);
      throw error;
    }
  }

  protected async processWithLock<T>(
    job: Job<T>,
    processor: (job: Job<T>) => Promise<any>,
  ): Promise<any> {
    let heartbeatInterval: NodeJS.Timeout;
    let lockRenewalInterval: NodeJS.Timeout;
    const processingTimeout = setTimeout(() => {
      this.logger.error(`Job ${job.id} timed out after ${this.JOB_TIMEOUT}ms`);
      this.cleanupJob(job, heartbeatInterval, lockRenewalInterval, processingTimeout);
      throw new Error('Job timeout');
    }, this.JOB_TIMEOUT);

    try {
      await job.progress(1);
      await this.randomDelay();

      lockRenewalInterval = setInterval(async () => {
        try {
          const renewed = await job.takeLock();
          if (!renewed) throw new Error('Lock renewal returned false');
        } catch (error) {
          this.logger.warn(`Lock renewal failed for job ${job.id}:`, error);
          this.cleanupJob(job, heartbeatInterval, lockRenewalInterval, processingTimeout);
        }
      }, this.LOCK_RENEWAL_INTERVAL);

      heartbeatInterval = setInterval(async () => {
        try {
          const currentProgress = await job.progress();
          if (currentProgress < 100) {
            await job.progress(currentProgress || 1);
          }
          const jobState = await job.getState();
          if (!['active', 'waiting'].includes(jobState)) {
            throw new Error(`Job ${job.id} is in invalid state: ${jobState}`);
          }
        } catch (error) {
          this.logger.warn(`Heartbeat failed for job ${job.id}`, error);
        }
      }, this.HEARTBEAT_INTERVAL);

      const result = await processor(job);
      await job.progress(100);
      return result;
    } catch (error) {
      this.logger.error(`Error processing job ${job.id} in queue ${this.queueName}:`, error.stack);
      await this.handleFailedJob(job, error);
      throw error;
    } finally {
      this.cleanupJob(job, heartbeatInterval, lockRenewalInterval, processingTimeout);
    }
  }

  private cleanupJob(
    job: Job,
    heartbeatInterval?: NodeJS.Timeout,
    lockRenewalInterval?: NodeJS.Timeout,
    processingTimeout?: NodeJS.Timeout,
  ) {
    if (heartbeatInterval) clearInterval(heartbeatInterval);
    if (lockRenewalInterval) clearInterval(lockRenewalInterval);
    if (processingTimeout) clearTimeout(processingTimeout);
  }

  @OnQueueStalled()
  protected async handleStalledJob(job: Job): Promise<void> {
    try {
      this.logger.warn(
        `Job ${job.id} has stalled. Attempt ${job.attemptsMade} of ${this.MAX_RETRY_ATTEMPTS}`,
      );

      if (job.attemptsMade >= this.MAX_RETRY_ATTEMPTS) {
        throw new Error('Max retry attempts reached for stalled job');
      }

      const backoff = Math.min(1000 * Math.pow(2, job.attemptsMade), 30000);
      const jobOptions = {
        delay: backoff,
        attempts: this.MAX_RETRY_ATTEMPTS - job.attemptsMade,
        jobId: job.id,
        removeOnComplete: true,
        removeOnFail: false,
        timeout: this.JOB_TIMEOUT,
        priority: job.opts.priority || 0,
      };

      await this.queue.add(job.data, jobOptions);
      await job.remove();
    } catch (error) {
      this.logger.error(`Error handling stalled job ${job.id}`, error);
      try {
        job.moveToFailed({ message: 'Error handling stalled job' }, true);
      } catch (e) {
        this.logger.error(e);
      }
    }
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job, error: Error): Promise<void> {
    try {
      if (job.attemptsMade < this.MAX_RETRY_ATTEMPTS) {
        const backoff = Math.min(1000 * Math.pow(2, job.attemptsMade), 30000);
        const jobOptions = {
          delay: backoff,
          attempts: this.MAX_RETRY_ATTEMPTS - job.attemptsMade,
          jobId: job.id,
          removeOnComplete: true,
          removeOnFail: false,
          timeout: this.JOB_TIMEOUT,
          priority: job.opts.priority || 0,
        };

        await this.queue.add(job.data, jobOptions);
        await job.remove();

        this.logger.warn(
          `Retrying failed job ${job.id}. Attempt ${job.attemptsMade} of ${this.MAX_RETRY_ATTEMPTS}`,
        );
      } else {
        this.logger.error(
          `Job ${job.id} failed permanently after ${this.MAX_RETRY_ATTEMPTS} attempts`,
        );
        throw new Error(error.message);
      }
    } catch (retryError) {
      this.logger.error(`Error retrying failed job ${job.id}`, retryError);
    }
  }

  private async randomDelay(): Promise<void> {
    const delay = Math.floor(Math.random() * 1000);
    return new Promise((resolve) => setTimeout(resolve, delay));
  }
}
