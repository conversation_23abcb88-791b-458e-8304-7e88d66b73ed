import { ExceptionFilter, ArgumentsHost, HttpException, HttpStatus, Catch } from '@nestjs/common';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(error: <PERSON>rro<PERSON>, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorResponse: object | string = error.message;

    if (error instanceof HttpException) {
      status = error.getStatus();
      const httpResponse = error.getResponse();

      if (typeof httpResponse === 'object') {
        errorResponse = httpResponse;
      }
    }

    response.status(status).json({
      statusCode: status,
      ...(error['code'] ? { code: error['code'] } : {}),
      ...(typeof errorResponse === 'object' ? errorResponse : { message: errorResponse }),
    });
  }
}
