import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3 } from 'aws-sdk';
import sharp from 'sharp';

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private s3: S3;

  constructor(private readonly configService: ConfigService) {
    this.s3 = new S3({
      accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      region: this.configService.get('AWS_REGION'),
    });
  }

  async uploadFile(
    file: Buffer,
    fileName: string,
    folderName: string,
    MimeType?: string,
    acl = 'public-read',
  ): Promise<string> {
    const params: S3.Types.PutObjectRequest = {
      Bucket: this.configService.get('AWS_BUCKET_NAME'),
      Key: `${folderName}/${fileName}`,
      Body: file,
      ACL: acl,
      ContentType: MimeType,
    };

    try {
      const response = await this.s3.upload(params).promise();
      this.logger.debug(`uploaded file to s3: ${response.Location}`);
      return response.Location;
    } catch (error) {
      this.logger.error(`error uploading file to s3: ${error?.message}`);
      throw error;
    }
  }

  async deleteFile(key: string): Promise<void> {
    const params: S3.Types.DeleteObjectRequest = {
      Bucket: this.configService.get('AWS_BUCKET_NAME'),
      Key: key,
    };

    try {
      await this.s3.deleteObject(params).promise();
      this.logger.debug(`deleted file from s3: ${key}`);
    } catch (error) {
      this.logger.error(`error deleting file from s3: ${error?.message}`);
      throw error;
    }
  }

  async resizeImage(
    file: Buffer,
    width: number,
    height?: number,
    format: 'jpeg' | 'png' = 'png',
  ): Promise<Buffer> {
    try {
      const resizedImage = await sharp(file)
        .resize(height ? { width, height } : { width })
        .toFormat(format)
        .toBuffer();

      return resizedImage;
    } catch (error) {
      this.logger.error(`error resizing image: ${error?.message}`);
      throw error;
    }
  }
}
