import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { normalizeUrl } from '../utils/url';

export enum UrlScope {
  DOMAIN = 'domain',
  SUBDOMAIN = 'subdomain',
  SUBFOLDER = 'subfolder',
  URL = 'url',
}
@Injectable()
export class MozApiService {
  private readonly apiUrl = 'https://api.moz.com/jsonrpc';
  private readonly apiToken: string;

  constructor(private configService: ConfigService) {
    this.apiToken = this.configService.get<string>('MOZ_API_TOKEN');
    if (!this.apiToken) {
      throw new Error('MOZ_API_TOKEN is not defined in environment');
    }
  }

  async fetchSiteMetrics(url: string, scope: UrlScope) {
    try {
      const response = await axios.post(
        this.apiUrl,
        {
          jsonrpc: '2.0',
          id: uuidv4(),
          method: 'data.site.metrics.fetch',
          params: {
            data: {
              site_query: {
                query: url,
                scope,
              },
            },
          },
        },
        { headers: this.getApiHeaders() },
      );
      return response.data.result.site_metrics;
    } catch (error) {
      console.error('Error fetching site metrics:', error);
      throw new Error('Failed to fetch site metrics');
    }
  }

  async fetchTopRankingKeywords(url: string, limit: number, locale: string, scope: UrlScope) {
    const hostname = new URL(url).hostname;

    try {
      const response = await axios.post(
        this.apiUrl,
        {
          jsonrpc: '2.0',
          id: uuidv4(),
          method: 'data.site.ranking-keyword.list',
          params: {
            data: {
              target_query: {
                query: hostname,
                scope,
                locale,
              },
              options: {
                sort: 'rank',
              },
              page: {
                n: 0,
                limit,
              },
            },
          },
        },
        { headers: this.getApiHeaders() },
      );

      return response.data.result.ranking_keywords;
    } catch (error) {
      console.error('Error fetching top ranking keywords:', error);
      throw new Error('Failed to fetch top ranking keywords');
    }
  }

  async fetchKeywordSuggestions(keyword: string, limit: number, locale: string) {
    try {
      const response = await axios.post(
        this.apiUrl,
        {
          jsonrpc: '2.0',
          id: uuidv4(),
          method: 'data.keyword.suggestions.list',
          params: {
            data: {
              serp_query: {
                keyword,
                locale,
                device: 'desktop',
                engine: 'google',
              },
              page: {
                n: 0,
                limit,
              },
            },
          },
        },
        { headers: this.getApiHeaders() },
      );

      return response.data.result.suggestions;
    } catch (error) {
      console.error('Error fetching keyword suggestions:', error);
      throw new Error('Failed to fetch keyword suggestions');
    }
  }

  determineScope(url: string): UrlScope {
    try {
      // Normalize the URL
      const normalizedUrl = normalizeUrl(url);

      const urlObj = new URL(normalizedUrl);
      const hostname = urlObj.hostname;
      const pathname = urlObj.pathname;

      // Extract domain parts
      const domainParts = hostname.split('.');

      // Check for subdomains
      // Special case: treat 'www' as part of the root domain
      if (domainParts.length > 2) {
        const potentialSubdomain = domainParts[0];
        if (potentialSubdomain !== 'www') {
          return UrlScope.SUBDOMAIN;
        }
      }

      // If pathname is empty or just '/', it's a root domain request
      if (pathname === '/' || pathname === '') {
        return UrlScope.DOMAIN;
      }

      // Clean the pathname (remove trailing slash)
      const cleanPathname = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname;
      const pathSegments = cleanPathname.split('/').filter(Boolean);

      // Check for URL vs subfolder
      // URLs typically have extensions or query parameters
      const hasExtension =
        pathSegments.length > 0 && this.hasFileExtension(pathSegments[pathSegments.length - 1]);
      const hasQueryParams = urlObj.search !== '';
      const hasHashFragment = urlObj.hash !== '';

      if (hasExtension || hasQueryParams || hasHashFragment) {
        return UrlScope.URL;
      }

      // If we have a path but no file extension or query parameters, it's a subfolder
      if (pathSegments.length > 0) {
        return UrlScope.SUBFOLDER;
      }

      // Default to domain if nothing else matches
      return UrlScope.DOMAIN;
    } catch (_) {
      return UrlScope.DOMAIN;
    }
  }

  private hasFileExtension(segment: string): boolean {
    const commonExtensions = [
      '.html',
      '.htm',
      '.php',
      '.asp',
      '.aspx',
      '.jsp',
      '.pdf',
      '.xml',
      '.json',
      '.js',
      '.css',
      '.txt',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.svg',
      '.webp',
    ];

    if (segment.includes('.') && /\.[a-zA-Z0-9]+$/.test(segment)) {
      return true;
    }

    return commonExtensions.some((ext) => segment.toLowerCase().endsWith(ext));
  }

  private getApiHeaders() {
    return {
      'x-moz-token': this.apiToken,
      'Content-Type': 'application/json',
    };
  }
}
