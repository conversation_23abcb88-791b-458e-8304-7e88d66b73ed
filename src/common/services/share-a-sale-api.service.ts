import type { AxiosResponse, AxiosError } from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import moment from 'moment';
import axios from 'axios';

import config from '../configs/config';

@Injectable()
export class ShareASaleAPIService {
  private baseURL = 'https://api.shareasale.com/w.cfm';
  private api = axios.create({ baseURL: this.baseURL });

  private readonly logger = new Logger(this.constructor.name);

  async editTransaction({ bid, amount }: { bid: string; amount: number }) {
    const query = {
      action: 'edit',
      version: '3.0',
      date: moment().format('DD/MM/YYYY'),
      merchantId: config().shareASale.merchantId,
      token: config().shareASale.apiToken,
      ordernumber: bid,
      newamount: `${amount}`,
    };

    const searchParams = new URLSearchParams(query);
    return this.shareASaleAPI(`?${searchParams.toString()}`);
  }

  private shareASaleAPI = async (
    url: string,
    { method, body }: { method?: 'get' | 'post' | 'delete'; body?: any } = { method: 'get' },
  ) => {
    return this.api({
      url,
      method: method,
      data: body,
      headers: { 'Content-Type': 'application/json' },
    })
      .then((r: AxiosResponse) => r.data)
      .catch((r: AxiosError<ShareASaleError>) => {
        const errorMessage = r.response.data?.error?.message;
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      });
  };
}

type ShareASaleError = {
  error: {
    code: string;
    message: string;
  };
};
