import type { AxiosResponse, AxiosError } from 'axios';

import { Injectable } from '@nestjs/common';
import axios from 'axios';

import config from '../configs/config';

@Injectable()
export class VercelAPIService {
  private baseURL = 'https://api.vercel.com/';
  private api = axios.create({ baseURL: this.baseURL });

  async addDomainToProject({ domain }: { domain: string }) {
    return this.vercelAPI('v10/projects/blogify-publish/domains?slug=blogifyai', {
      method: 'post',
      body: {
        name: domain,
        gitBranch: !config().isProd ? 'develop' : null,
      },
    });
  }

  async removeDomainFromProject({ domain }: { domain: string }) {
    return this.vercelAPI(`v9/projects/blogify-publish/domains/${domain}?slug=blogifyai`, {
      method: 'delete',
    });
  }

  async getDomainInfo({
    domain,
  }: {
    domain: string;
  }): Promise<VercelDomainConfig & VercelProjectDomainInfo> {
    return Promise.allSettled([
      this.getDomainConfig({ domain }),
      this.getProjectDomainInfo({ domain }),
    ]).then(([domainConfig, domainInfo]) => {
      let obj = {};
      if (domainConfig.status === 'fulfilled') {
        obj = { ...obj, ...domainConfig.value };
      }
      if (domainInfo.status === 'fulfilled') {
        obj = { ...obj, ...domainInfo.value };
      }
      return obj as VercelDomainConfig & VercelProjectDomainInfo;
    });
  }

  private getDomainConfig({ domain }: { domain: string }): Promise<VercelDomainConfig> {
    return this.vercelAPI(`v6/domains/${domain}/config?slug=blogifyai`);
  }

  private getProjectDomainInfo({ domain }: { domain: string }): Promise<VercelProjectDomainInfo> {
    return this.vercelAPI(`v9/projects/blogify-publish/domains/${domain}?slug=blogifyai`);
  }

  private vercelAPI = async (
    url: string,
    { method, body }: { method?: 'get' | 'post' | 'delete'; body?: any } = { method: 'get' },
  ) => {
    return this.api({
      url,
      method: method,
      data: body,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config().vercel.apiToken}`,
      },
    })
      .then((r: AxiosResponse) => r.data)
      .catch((r: AxiosError<VercelError>) => {
        const errorMessage = r.response.data?.error?.message;
        // this.logger.error(errorMessage);
        throw new Error(errorMessage);
      });
  };
}

type VercelError = {
  error: {
    code: string;
    message: string;
  };
};

type VercelDomainConfig = {
  configuredBy: 'http' | 'CNAME' | 'A' | 'dns-01';
  nameservers: string[];
  serviceType: 'external' | string;
  cnames: string[];
  aValues: string[];
  conflicts: string[];
  acceptedChallenges: string[];
  misconfigured: boolean;
};

type VercelProjectDomainInfo = {
  name: string;
  apexName: string;
  projectId: string;
  redirect: string | null;
  redirectStatusCode: 307 | 301 | 302 | 308 | null;
  gitBranch: string | null;
  customEnvironmentId: string | null;
  updatedAt: number;
  createdAt: number;
  verified: boolean;
  verification: { type: 'TXT'; domain: string; value: string; reason: string }[];
};
