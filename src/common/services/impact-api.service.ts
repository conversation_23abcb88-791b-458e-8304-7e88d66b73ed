import type { AxiosResponse, AxiosError } from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import moment from 'moment';
import axios from 'axios';

import config from '../configs/config';

@Injectable()
export class ImpactAPIService {
  private baseURL = `https://api.impact.com/Advertisers/${config().impact.accountSid}`;
  private api = axios.create({ baseURL: this.baseURL });
  private token = Buffer.from(
    `${config().impact.accountSid}:${config().impact.authToken}`,
  ).toString('base64');

  private readonly logger = new Logger(this.constructor.name);

  async trackConversion({ bid, amount }: { bid: string; amount: number }) {
    const body = {
      CampaignId: 22875,
      ActionTrackerId: 53851,
      EventDate: moment().toISOString(),
      // OrderId: bid,
      CustomerId: bid,
      CustomerStatus: 'Returning',
      CurrencyCode: 'USD',
      ItemSubTotal1: amount,
    };

    return this.impactAPI('/Conversions', { method: 'post', body });
  }

  private impactAPI = async (
    url: string,
    { method, body }: { method?: 'get' | 'post' | 'delete'; body?: any } = { method: 'get' },
  ) => {
    return this.api({
      url,
      method: method,
      data: body,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${this.token}`,
      },
    })
      .then((r: AxiosResponse) => r.data)
      .catch((r: AxiosError<ImpactError>) => {
        const errorMessage = r.response.data?.error?.message;
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      });
  };
}

type ImpactError = {
  error: {
    code: string;
    message: string;
  };
};
