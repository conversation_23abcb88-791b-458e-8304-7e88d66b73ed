import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

interface DomainMetrics {
  pos_1: number;
  pos_2_3: number;
  pos_4_10: number;
  pos_11_20: number;
  pos_21_30: number;
  pos_31_40: number;
  pos_41_50: number;
  pos_51_60: number;
  pos_61_70: number;
  pos_71_80: number;
  pos_81_90: number;
  pos_91_100: number;
  etv: number;
  count: number;
  estimated_paid_traffic_cost: number;
}

interface BacklinksInfo {
  referring_domains: number;
  referring_main_domains: number;
  referring_pages: number;
  dofollow: number;
  backlinks: number;
  time_update: string;
}

export interface DomainAnalytics {
  domain: string;
  created_datetime: string | null;
  changed_datetime: string;
  expiration_datetime: string | null;
  updated_datetime: string;
  first_seen: string;
  epp_status_codes: string[];
  tld: string;
  registered: boolean;
  registrar: string;
  metrics: {
    organic: DomainMetrics;
    paid: DomainMetrics;
  };
  backlinks_info: BacklinksInfo;
}

@Injectable()
export class DataForSeoApiService {
  private readonly apiUrl = 'https://api.dataforseo.com/v3/domain_analytics/whois/overview/live';
  private readonly apiLogin: string;
  private readonly apiPassword: string;

  constructor(private configService: ConfigService) {
    this.apiLogin = this.configService.get<string>('DATAFORSEO_API_LOGIN');
    this.apiPassword = this.configService.get<string>('DATAFORSEO_API_PASSWORD');

    if (!this.apiLogin || !this.apiPassword) {
      throw new Error(
        'DATAFORSEO_API_LOGIN or DATAFORSEO_API_PASSWORD is not defined in environment',
      );
    }
  }

  private getAuthConfig() {
    return {
      auth: {
        username: this.apiLogin,
        password: this.apiPassword,
      },
    };
  }

  async fetchDomainAnalytics(domain: string): Promise<DomainAnalytics> {
    try {
      const postData = [
        {
          filters: [['domain', '=', domain]],
        },
      ];

      const response = await axios({
        method: 'post',
        url: this.apiUrl,
        data: postData,
        headers: {
          'content-type': 'application/json',
        },
        ...this.getAuthConfig(),
      });

      return response.data.tasks[0].result[0].items[0];
    } catch (error) {
      console.error('Error fetching domain analytics:', error);
      throw new Error('Failed to fetch domain analytics');
    }
  }
}
