import type { AxiosError } from 'axios';

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import textract from 'textract';
import mammoth from 'mammoth';
import axios from 'axios';
import pdf from 'pdf-parse';

import {
  FileNotDownloadableException,
  InaccessibleUrlException,
  UnsupportedFileException,
  NoTextContentException,
} from '@blog/blog.errors';

@Injectable()
export class FileConversionService {
  private readonly logger = new Logger(FileConversionService.name);

  async downloadAndConvert(url: string, fileExtension?: string) {
    const buffer = await axios
      .get(url, { responseType: 'arraybuffer' })
      .then((r) => r.data)
      .catch((e: AxiosError) => {
        if (e.response?.status === 401) {
          throw new InaccessibleUrlException();
        }
        throw new BadRequestException(e.message);
      });

    const extension = fileExtension ?? this.extractFileExtension(url);

    if (!['pdf', 'docx', 'doc', 'txt'].includes(extension)) {
      throw new FileNotDownloadableException(
        'File is not directly downloadable or file format is not supported.',
      );
    }

    let content = '';

    switch (extension) {
      case 'pdf':
        content = await this.extractFromPDF(buffer);
        break;
      case 'docx':
        content = await this.extractFromDocx(buffer);
        break;
      case 'doc':
        content = await this.extractFromDoc(buffer);
        break;
      case 'txt':
        content = buffer.toString('utf-8');
        break;
    }

    if (!content) {
      throw new UnsupportedFileException(
        'Error extracting text from file or file is not supported',
      );
    }

    if (content.length > 0 && content.length < 50) {
      throw new NoTextContentException(`The ${extension} file doesn't have any text content.`);
    }

    return content;
  }

  private extractFileExtension(url: string): string | null {
    const fileName = url.split('/').pop()?.split('?')[0];
    if (!fileName) return null;
    const extension = fileName.split('.').pop();
    return extension?.toLowerCase() || null;
  }

  private async extractFromPDF(buffer: Buffer): Promise<string> {
    const data = await pdf(buffer);
    if (data.text) {
      const formattedText = data.text.replace(/\t/g, ' ').replace(/\n/g, '\n');
      return formattedText;
    } else {
      throw new Error(`Error extracting text from PDF`);
    }
  }

  private async extractFromDocx(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  private extractFromDoc(buffer: Buffer): Promise<string> {
    return new Promise((resolve, reject) => {
      textract.fromBufferWithMime('application/msword', buffer, (error, text) => {
        if (error) reject(error);
        else resolve(text);
      });
    });
  }
}
