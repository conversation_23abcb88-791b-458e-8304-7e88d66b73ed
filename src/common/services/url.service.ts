import type { AxiosError } from 'axios';
import type { Blog } from '@blog/blog.model';

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { load } from 'cheerio';
import axios from 'axios';

import {
  DurationLimitExceedException,
  InaccessibleUrlException,
  InvalidUrlException,
} from '@/blog/blog.errors';
import { BlogifyMediaService } from '@/blogify-media/blogify-media.service';
import { BlogSourceName } from '@/blog/blog.enums';

type UrlMeta = {
  image: string;
  title: string;
  description: string;
  duration: string;
};

type Params = {
  sourceType: Blog['sourceType'];
  sourceName: Blog['sourceName'];
  maxDuration: number;
};

@Injectable()
export class UrlService {
  private readonly logger = new Logger(this.constructor.name);
  constructor(private readonly blogifyMediaService: BlogifyMediaService) {}

  async getUrlMeta(url: string, sourceName: Blog['sourceName']): Promise<UrlMeta> {
    if (sourceName === 'TikTok') {
      return await this.getTikTokMeta(url);
    } else {
      const meta = await this.getAnyUrlMeta(url);
      if (meta.title === ' - YouTube') {
        meta.title = '';
      }
      return meta;
    }
  }

  async validateUrl(url: string, params: Params): Promise<boolean> {
    const { sourceType } = params;

    if (!URL.canParse(url)) {
      throw new InvalidUrlException(`The provided URL is invalid.`);
    }

    // await this.isLinkAccessible(url, params);

    switch (sourceType) {
      case 'video':
      case 'audio': {
        await this.validateMediaUrl(url, params);
        break;
      }
      case 'document': {
        await this.validateDocumentUrl(url, params);
        break;
      }
      case 'webLink': {
        // await this.validateWebLinkUrl(url, params);
        break;
      }
    }

    return true;
  }

  private async isLinkAccessible(url: string, { sourceName }: Params) {
    if (['YouTube', 'Vimeo', 'TikTok', 'Google Docs'].includes(sourceName)) {
      await this.getUrlMeta(url, sourceName).then((meta) => {
        if (!meta?.title) {
          throw new InaccessibleUrlException();
        }
      });
    } else if (
      !['Rumble', BlogSourceName.Etsy, BlogSourceName.Walmart, BlogSourceName.FlipCart].includes(
        sourceName,
      )
    ) {
      await axios.get(url).catch((e: AxiosError) => {
        const status = e.response?.status || e.status;
        if ([401, 403].includes(status)) {
          throw new InaccessibleUrlException();
        }
        if (status === 404) {
          throw new InaccessibleUrlException(`The provide URL doesn't exist or private.`);
        }
        if (e.code === 'ENOTFOUND') {
          throw new InvalidUrlException(`The provide URL doesn't exist.`);
        }
        throw new BadRequestException(e);
      });
    }
  }

  private async validateMediaUrl(url: string, { maxDuration }: Params) {
    const duration = await this.blogifyMediaService.getDuration(url).catch((e: AxiosError) => {
      let message = e.response.data['error'];

      if (message?.toLowerCase().includes('unauthorized')) {
        message = message.replace(/(:\s)?unauthorized/gi, '');
      }

      if (message?.includes('Sign in to confirm your age')) {
        message = 'The content is age restricted.';
      }

      if (message.length > 100) {
        this.logger.error(message);
        message = 'The content is not accessible.';
      }

      throw new InaccessibleUrlException(message);
    });
    if (duration > maxDuration) {
      throw new DurationLimitExceedException(
        `Duration exceeds the limit allowed by subscription plan i.e. ${maxDuration / 60} minutes.`,
      );
    }
  }

  private async validateDocumentUrl(url: string, { sourceName }: Params) {
    switch (sourceName) {
      case 'Google Docs': {
        const potentialDownloadUrl = `https://docs.google.com/document/d/${
          url.split('/')[5]
        }/export?format=txt`;

        await axios
          .head(potentialDownloadUrl)
          .then((resp) => {
            if (resp.status !== 200) {
              throw new InaccessibleUrlException();
            }
          })
          .catch(() => {
            throw new InaccessibleUrlException();
          });
      }
    }
  }

  // TODO: Add Web Link Validation Later
  // private validateWebLinkUrl(url: string, { sourceName }: Params) {
  // }

  private async getAnyUrlMeta(url: string): Promise<UrlMeta> {
    let image = '',
      title = '',
      description = '',
      duration = '';

    try {
      const response = await axios.get(url, { timeout: 3000 });
      const html = response.data;

      const $ = load(html);

      const titleOGMeta = $('meta[property="og:title"]').first().attr('content');
      const titleTag = $('title').first().text();
      title = titleOGMeta || titleTag || '';

      const descriptionOGMeta = $('meta[property="og:description"]').first().attr('content');
      const descriptionMeta = $('meta[name="description"]').first().attr('content');
      description = descriptionOGMeta || descriptionMeta || '';

      const imageOGMeta = $('meta[property="og:image"]').first().attr('content');
      image = imageOGMeta || '';

      const durationMeta = $('meta[itemprop="duration"]').first().attr('content');
      if (durationMeta) {
        duration = durationMeta || '';
      } else {
        try {
          const jsonLDString = $('script[type="application/ld+json"]').first().text();
          if (jsonLDString) {
            const jsonLD = JSON.parse(jsonLDString);
            duration = jsonLD?.duration || jsonLD?.[0]?.duration;
          }
        } catch (e) {
          this.logger.log('Error with fetching/parsing JSON LD:', e);
        }
      }
    } catch (error) {
      this.logger.log('Error Fetching URL Meta:', error);
    }

    return { image, title, description, duration };
  }

  private async getTikTokMeta(url: string): Promise<UrlMeta> {
    type TikTokMeta = { title: string; thumbnail_url: string; author_name: string };
    try {
      const endPoint = 'https://www.tiktok.com/oembed';
      const response: TikTokMeta = await axios.get(`${endPoint}?url=${url}`).then((r) => r.data);

      return {
        title: `Video by ${response.author_name}`,
        image: response.thumbnail_url,
        description: response.title,
        duration: '',
      };
    } catch (e) {
      this.logger.log('Error Fetching TikTok Meta:', e);
    }

    return { image: '', title: '', description: '', duration: '' };
  }
}
