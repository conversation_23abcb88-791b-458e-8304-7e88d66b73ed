import { Module } from '@nestjs/common';

import { BusinessModule } from '@/business/business.module';
import { WebsiteModule } from '@/resources/website/website.module';
import { WalletModule } from '@/resources/wallet/wallet.module';
import { BlogModule } from '@/blog/blog.module';

import { MeController } from './me.controller';
import { MeService } from './me.service';

@Module({
  imports: [BusinessModule, WebsiteModule, WalletModule, BlogModule],
  controllers: [MeController],
  providers: [MeService],
  exports: [MeService],
})
export class MeModule {}
