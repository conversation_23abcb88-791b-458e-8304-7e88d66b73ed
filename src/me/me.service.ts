import type { BusinessDocument } from '@/business/business.model';
import type { BlogDocument } from '@/blog/blog.model';
import type { UserDocument } from '@/user/user.model';

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import moment from 'moment';

import { getPackageLimits } from '@/payments/blog-credit.processor';
import { WebsiteService } from '@/resources/website/website.service';
import { WalletService } from '@/resources/wallet/wallet.service';
import { Business } from '@/business/business.model';
import { Blog } from '@/blog/blog.model';

@Injectable()
export class MeService {
  private readonly logger = new Logger(MeService.name);

  constructor(
    @InjectModel(Business.name) private readonly business: Model<BusinessDocument>,
    @InjectModel(Blog.name) private readonly blog: Model<BlogDocument>,
    private readonly websiteService: WebsiteService,
    private readonly walletService: WalletService,
  ) {}

  async getStats(user: UserDocument) {
    try {
      const uid = user._id;
      const bid = user.business?._id || user.business;

      // Blog Counts
      const firstDayOfThisMonth = moment().startOf('month');
      const lastDateOfThisMonth = moment().endOf('month');
      const blogCountLifeTime = await this.blog.countDocuments({ uid, bid });
      const blogCountThisMonth = await this.blog.countDocuments({
        uid,
        bid,
        createdAt: {
          $gte: firstDayOfThisMonth,
          $lte: lastDateOfThisMonth,
        },
      });

      // Website
      const websiteCount = await this.websiteService.count(bid);
      const websiteBlogCount = await this.websiteService.countBlogsByBusiness(bid);

      // Credits;
      const business = await this.business.findById(bid);
      const packageLimits = getPackageLimits(business.subscriptionPlan, business.customPlan);

      // Wallet
      const { balance, currency } = await this.walletService.getBalance(bid);
      const { debit: lastWithdraw = 0 } = (await this.walletService.getLastPayout(bid)) || {};

      return {
        blogCount: {
          thisMonth: blogCountThisMonth,
          lifeTime: blogCountLifeTime,
        },
        website: {
          count: websiteCount,
          blogCount: websiteBlogCount,
        },
        credits: {
          remaining: business.credits,
          total: packageLimits.CREDITS,
        },
        wallet: {
          currency,
          balance,
          lastWithdraw,
        },
      };
    } catch (e) {
      this.logger.log(e);
      throw e;
    }
  }
}
