import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

import { <PERSON><PERSON><PERSON><PERSON>, Controller, Lo<PERSON>, Req, Get } from '@nestjs/common';

import { Auth } from '@auth/guards/auth.guard';

import { MeService } from './me.service';

@Controller('me')
export class MeController {
  private readonly logger = new Logger(MeController.name);

  constructor(private readonly meService: MeService) {}

  @UseGuards(Auth)
  @Get('stats')
  async getStats(@Req() { user }: AuthenticatedRequest) {
    return this.meService.getStats(user);
  }
}
