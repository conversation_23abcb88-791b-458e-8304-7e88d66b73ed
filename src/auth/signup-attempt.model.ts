import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type SignupAttemptDocument = SignupAttempt & Document;

@Schema()
export class SignupAttempt {
  @Prop({ required: true })
  ip: string;

  @Prop()
  country: string;

  @Prop({ default: 1 })
  count: number;

  @Prop({ default: Date.now })
  lastAttempt: Date;
}

export const SignupAttemptSchema = SchemaFactory.createForClass(SignupAttempt);
