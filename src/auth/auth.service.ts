import type { RedisStore } from 'cache-manager-redis-yet';
import type { Cache } from 'cache-manager';

import { Injectable, Inject, Logger, ForbiddenException } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import { UserService } from '@user/user.service';
import { JwtService } from '@nestjs/jwt';
import { compare } from 'bcrypt';
import * as uuid from 'uuid';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as geoip from 'geoip-lite';
import { SignupAttempt, SignupAttemptDocument } from './signup-attempt.model';
import { SignupDto } from './dto/signup.dto';

import { User, UserDocument, UserStatus } from '@user/user.model';
import { StripeService } from 'src/payments/stripe.service';
import { MailService } from '@modules/mail/mail.service';
import config from '@common/configs/config';

import { SlackService } from '@common/services/slack.service';
import { google } from '@google-cloud/recaptcha-enterprise/build/protos/protos';
import axios from 'axios';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    @Inject(CACHE_MANAGER) private distributedCache: Cache<RedisStore>,
    private readonly configService: ConfigService,
    private readonly slackService: SlackService,
    private readonly stripeService: StripeService,
    private readonly mailService: MailService,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    @InjectModel(SignupAttempt.name) private signupAttemptModel: Model<SignupAttemptDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  private async generateJwtToken(user: UserDocument) {
    const payload = {
      sub: user._id,
      email: user.email,
      name: user.name,
    };
    if (user.business) {
      Object.assign(payload, {
        bid: user.business._id,
        bname: user.business.name,
      });
    }
    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: '6h',
      secret: this.configService.get('JWT_SECRET'),
    });
    this.logger.debug({ uid: user._id }, 'JWT Token Created');
    return accessToken;
  }

  private async generateRefreshToken(user: UserDocument) {
    const payload = {
      sub: user._id,
      type: 'refresh',
    };

    const refreshToken = await this.jwtService.signAsync(payload, {
      expiresIn: '7d',
      secret: this.configService.get('JWT_REFRESH_SECRET'),
    });

    this.logger.debug({ uid: user._id }, 'Refresh Token Created');
    return refreshToken;
  }

  async refreshTokens(
    refreshToken: string,
  ): Promise<{ access_token: string; refresh_token: string }> {
    try {
      const payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      if (payload.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      const user = await this.userModel.findById(payload.sub);

      if (!user) {
        throw new Error('User not found');
      }

      const newAccessToken = await this.generateJwtToken(user);
      const newRefreshToken = await this.generateRefreshToken(user);

      return {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
      };
    } catch (error) {
      this.logger.error('Token refresh failed', error?.message);
      throw new Error('Token refresh failed: ' + error?.message);
    }
  }

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.userService.findByEmail(email);
    if (user && (await compare(password, user.password))) {
      delete user.password;
      return user;
    }
    return null;
  }

  async login(user: UserDocument): Promise<{ access_token: string; refresh_token: string }> {
    const accessToken = await this.generateJwtToken(user);
    const refreshToken = await this.generateRefreshToken(user);

    return { access_token: accessToken, refresh_token: refreshToken };
  }

  async signUp(signupDto: SignupDto, ip?: string, country?: string): Promise<UserDocument> {
    if (ip && country) {
      await this.checkSignupAttempts(ip, signupDto.email);
      await this.trackSignupAttempt(ip);
    }

    const { email, password, name, businessName, foundUsFrom, isShopifyUser, isFreeUser } =
      signupDto;
    const stripeCustomer = await this.stripeService.createCustomer(email, name);

    const user = await this.userService.create({
      email: email.toLowerCase(),
      password,
      name,
      businessName,
      foundUsFrom,
      stripeId: stripeCustomer.id,
      isShopifyUser,
      isFreeUser,
      ip,
      country: geoip.lookup(ip)?.country || 'Unknown',
    });

    // Shopify users starts with Free plan
    if (isShopifyUser || isFreeUser) {
      await this.stripeService.createFreeSubscription(user.business._id, stripeCustomer.id);
    }

    await this.slackService.sendSignupNotification({
      email,
      name,
      businessName,
      foundUsFrom,
      isShopifyUser,
    });

    delete user.password;

    return user;
  }

  async forgotPassword(email: string) {
    const user = await this.userService.findByEmail(email);

    if (!user) {
      return { notFound: true };
    }

    const resetId = uuid.v4();
    await this.distributedCache.set(resetId, user.id, 6 * 60 * 60 * 1000);
    await this.mailService.send({
      name: user.name,
      to: user.email,
      subject: 'Password Reset',
      template: 'password-reset',
      context: {
        ctaUrl: `${config().internalApps.blogifyClient.url}/reset-password/${resetId}`,
      },
    });
  }

  async resetPassword(password: string, token: string) {
    const userId: string = await this.distributedCache.get(token);
    if (!userId) {
      return { notFound: true };
    }
    this.distributedCache.del(token);

    const user = await this.userService.updateProfile(userId, { password });

    if (!user) {
      return { notFound: true };
    }

    await this.mailService.send({
      name: user.name,
      to: user.email,
      subject: 'Password Reset Successful.',
      template: 'password-reset-success',
    });
  }

  async acceptInvitation(password: string, token: string) {
    const userId: string = await this.distributedCache.get(token);
    if (!userId) {
      return { notFound: true };
    }
    this.distributedCache.del(token);

    const user = await this.userService.updateProfile(userId, {
      password,
      status: UserStatus.active,
    });

    if (!user) {
      return { notFound: true };
    }
  }

  private async trackSignupAttempt(ip: string): Promise<void> {
    const geo = geoip.lookup(ip);
    const country = geo ? geo.country : 'Unknown';

    const attempt = await this.signupAttemptModel.findOne({ ip });
    if (attempt) {
      attempt.count += 1;
      attempt.lastAttempt = new Date();
      await attempt.save();
    } else {
      await this.signupAttemptModel.create({ ip, country });
    }
  }

  private async checkSignupAttempts(ip: string, email: string): Promise<void> {
    const whitelistedTesters = this.configService.get('SIGNUP_TESTERS')?.split(',');
    if (whitelistedTesters?.some((substring) => email.includes(substring))) {
      return;
    }

    const attempt = await this.signupAttemptModel.findOne({ ip });
    if (attempt && attempt.count >= 10) {
      this.logger.warn(`Blocked IP ${ip} Email ${email} from signing up`);
      throw new ForbiddenException('Maximum signup attempts reached for this IP');
    }
  }

  /**
   * Create an assessment to analyze the risk of a UI action.
   *
   * @param token- The generated token obtained from the client.
   * @param recaptchaAction- Action name corresponding to the token.
   * @return The probability of being human between 0 and 1
   */
  async validateRepatcha({ token, recaptchaAction }: { token: string; recaptchaAction: 'SIGNUP' }) {
    try {
      const { data: response } = await axios.post<google.cloud.recaptchaenterprise.v1.IAssessment>(
        `https://recaptchaenterprise.googleapis.com/v1/projects/${this.configService.getOrThrow<string>(
          'GOOGLE_CLOUD_RECAPTCHA_PROJECT_ID',
        )}/assessments?key=${this.configService.getOrThrow<string>('RECAPTCHA_SERVER_API_KEY')}`,
        {
          event: {
            token: token,
            expectedAction: recaptchaAction,
            siteKey: this.configService.getOrThrow<string>('RECAPTCHA_SITEKEY'),
          },
        },
      );

      // Check if the token is valid.
      if (!response.tokenProperties.valid) {
        this.logger.error(
          `The CreateAssessment call failed because the token was: ${response.tokenProperties.invalidReason}`,
        );
      }

      // Check if the expected action was executed.
      // The `action` property is set by user client in the grecaptcha.enterprise.execute() method.
      if (response.tokenProperties.action === recaptchaAction) {
        // Get the risk score and the reason(s).
        // For more information on interpreting the assessment, see:
        // https://cloud.google.com/recaptcha-enterprise/docs/interpret-assessment
        this.logger.log(`The reCAPTCHA score is: ${response.riskAnalysis.score}`);
        response.riskAnalysis.reasons.forEach((reason) => {
          this.logger.log(reason);
        });

        // return response.riskAnalysis.score;
        return 1;
      } else {
        this.logger.error(
          'The action attribute in your reCAPTCHA tag does not match the action you are expecting to score',
        );
      }
    } catch (error) {
      this.logger.error(error);
      return 1;
    }
  }
}
