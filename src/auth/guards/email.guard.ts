import { ForbiddenException, ExecutionContext, CanActivate, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';

const EMAIL_DOMAIN_BLACKLIST = [
  '@bikerider.com',
  '@counsellor.com',
  '@gardener.com',
  '@engineer.com',
  '@doglover.com',
  '@saxpads.xyz',
  '@artlover.com',
  '@comic.com',
  '@hilarious.com',
  '@minister.com',
  '@politician.com',
  '@mail.com',
  '@gmx.com',
];

@Injectable()
export class EmailGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const { body } = request;

    if (EMAIL_DOMAIN_BLACKLIST.some((e) => body.email.endsWith(e))) {
      throw new ForbiddenException(
        'The email domain is blacklisted. Please use a different email address.',
      );
    }

    return true;
  }
}
