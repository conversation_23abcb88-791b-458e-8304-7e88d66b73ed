import type { Request } from '@auth/interfaces/authenticated-request.interface';

import { UnauthorizedException, ExecutionContext, CanActivate, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class BusinessGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest() as Request;
    const { bid } = request;

    if (!bid) {
      throw new UnauthorizedException('Business not found for the user.');
    }

    return true;
  }
}
