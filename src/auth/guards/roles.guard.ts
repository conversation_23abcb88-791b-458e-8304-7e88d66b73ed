import { ExecutionContext, CanActivate, SetMetadata, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { UserRole, User } from '@user/user.model';

@Injectable()
export class RolesGuard implements CanActivate {
  private privilegeMap: Record<UserRole, number>;
  constructor(private readonly reflector: Reflector) {
    this.privilegeMap = {
      superadmin: 3,
      admin: 2,
      editor: 2,
      writer: 2,
    };
  }

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<string[]>('roles', context.getHandler());
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const user = context.switchToHttp().getRequest().user as User;
    if (!user || !user.role || !user.business) {
      return false;
    }

    const userPrivilege = this.privilegeMap[user.role];
    const requiredPrivilege = Math.max(...requiredRoles.map((role) => this.privilegeMap[role]));
    return userPrivilege >= requiredPrivilege;
  }
}

export const Roles = (...roles: string[]) => SetMetadata('roles', roles);
