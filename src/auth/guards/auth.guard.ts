import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserService } from '@user/user.service';

@Injectable()
export class Auth extends AuthGuard('jwt') {}

@Injectable()
// This should only be used where we need the response whether the user is authenticated or not
// i.e. in the case of the pricing plan /payment/plans endpoint
export class AuthInsecure implements CanActivate {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<any> {
    const req = context.switchToHttp().getRequest();
    const { authorization = '' }: any = req.headers;

    const authToken = authorization.replace(/bearer/gim, '').trim();
    let resp = { sub: '' };
    try {
      resp = this.jwtService.verify(authToken, {
        secret: this.configService.get('JWT_SECRET'),
      });
    } catch (error) {
      this.logger.error(error);
    }

    const { sub: userId } = resp;
    let user: any = {};
    if (userId) {
      user = await this.userService.findById(userId);
    }
    req.user = user;
    req.uid = user._id;
    req.bid = user.business?._id;

    return { ...user, uid: user._id, bid: user.business?._id };
  }
}
