import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

class CustomUnauthorizedException extends UnauthorizedException {
  constructor() {
    super(
      'Sorry, the provided credentials are incorrect or you are not authorized to access this resource.',
    );
  }
}

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  async canActivate(context: ExecutionContext) {
    if (await super.canActivate(context)) {
      return true;
    } else {
      throw new CustomUnauthorizedException();
    }
  }

  handleRequest(err, user) {
    if (err || !user) {
      throw new CustomUnauthorizedException();
    }
    return user;
  }
}
