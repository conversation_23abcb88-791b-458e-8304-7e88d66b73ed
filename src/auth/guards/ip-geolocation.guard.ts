import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import geoip from 'geoip-lite';
import config from '@/common/configs/config';

@Injectable()
export class IpGeolocationGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const req = context.switchToHttp().getRequest();
    const ipAddress = req.headers['x-forwarded-for'] || req.ip || req.connection.remoteAddress;
    const ip = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;
    const geo = geoip.lookup(ip);

    // Add IP and country to the request object
    req.clientIp = ip;
    req.clientCountry = geo ? geo.country : null;

    const blockedCountries = config().blockedCountries ? config().blockedCountries.split(',') : [];
    if (geo && blockedCountries.includes(geo.country)) {
      return false;
    }

    const blockedIpAddresses = config().blockedIpAddresses
      ? config().blockedIpAddresses.split(',')
      : [];
    if (blockedIpAddresses.includes(ip)) {
      return false;
    }

    return true;
  }
}
