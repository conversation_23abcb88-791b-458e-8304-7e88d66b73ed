import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { BusinessAddon, Business } from '@business/business.model';
import type { Observable } from 'rxjs';
import type { Addon } from '@resources/product/product.model';

import { ForbiddenException, Injectable, mixin } from '@nestjs/common';
import moment from 'moment';

const isAddOnActive = (addon: BusinessAddon): boolean => {
  return !!(
    ['active', 'deactivated'].includes(addon?.status) &&
    moment(addon?.expirationDate).diff(moment(), 'days') >= 0
  );
};

export const AddonAccessGuard = (addonName: Addon) => {
  @Injectable()
  class AddonAccessGuardMixin implements CanActivate {
    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
      const request = context.switchToHttp().getRequest();
      const userAddons = (request.user.addons || {}) as Business['addons'];

      const partialAddonId = Object.keys(userAddons).find(
        (id) => userAddons[id].name.includes(addonName) && userAddons[id].name !== addonName,
      );
      const addonId = Object.keys(userAddons).find((id) => userAddons[id].name === addonName);
      if (!partialAddonId && !addonId) {
        throw new ForbiddenException(`You haven't purchased ${addonName} addon yet.`);
      }

      const partialAddon = userAddons[partialAddonId];
      const addon = userAddons[addonId];
      if (!isAddOnActive(partialAddon) && !isAddOnActive(addon)) {
        throw new ForbiddenException(`Your ${addonName} addon has been expired.`);
      }

      return request;
    }
  }

  const guard = mixin(AddonAccessGuardMixin);
  return guard;
};
