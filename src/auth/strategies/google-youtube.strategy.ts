import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-google-oauth20';
import { AuthService } from '../auth.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleYoutubeStrategy extends PassportStrategy(Strategy, 'google-youtube') {
  private readonly logger = new Logger(GoogleYoutubeStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.get('GOOGLE_CLIENT_ID'),
      clientSecret: configService.get('GOOGLE_CLIENT_SECRET'),
      callbackURL: configService.get('BASE_URL') + '/youtube/callback',
      access_type: 'offline',
      prompt: 'consent',
      scope: ['profile', 'email', 'https://www.googleapis.com/auth/youtube.readonly'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: any): Promise<any> {
    const { email, name, picture, state } = profile._json; // Retrieve name and profile picture

    const user = {
      email,
      state,
      name, // Add user's name to the user object
      picture, // Add profile picture URL to the user object
      accessToken,
      refreshToken,
    };
    done(null, user);
  }

  async authenticate(request, options) {
    this.logger.debug(`Authorizing request: ${request}`);
    // Add custom JWT token as a header in the authorization URL
    const jwtToken = request.query.token;
    options.state = jwtToken;
    options.accessType = 'offline';
    options.prompt = 'consent';

    // Call the parent authorize method to continue the OAuth flow
    return super.authenticate(request, options);
  }
}
