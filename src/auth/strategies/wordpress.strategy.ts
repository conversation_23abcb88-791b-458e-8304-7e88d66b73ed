import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-oauth2';
import { ConfigService } from '@nestjs/config';
import { BASE_URL } from '@common/constants';

// This strategy is not used
@Injectable()
export class WordpressStrategy extends PassportStrategy(Strategy, 'wordpress') {
  private readonly logger = new Logger(WordpressStrategy.name);
  constructor(private readonly configService: ConfigService) {
    super({
      authorizationURL: 'https://public-api.wordpress.com/oauth2/authorize',
      tokenURL: 'https://public-api.wordpress.com/oauth2/token',
      clientID: configService.get('WORDPRESS_CLIENT_ID'),
      clientSecret: configService.get('WORDPRESS_CLIENT_SECRET'),
      callbackURL: `${BASE_URL}/wordpress/wp-connect`,
      responseType: 'code',
      scope: 'global',
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any) {
    this.logger.debug(`Access token: ${accessToken} Profile: ${profile}`);
    return { accessToken, refreshToken, profile };
  }

  async authenticate(request, options) {
    this.logger.debug(`Authorizing request: ${request}`);
    // Add custom JWT token as a header in the authorization URL
    const jwtToken = request.query.token;
    options.state = jwtToken;
    options.accessType = 'offline';
    options.prompt = 'consent';
    options.approvalPrompt = 'force';

    // Call the parent authorize method to continue the OAuth flow
    return super.authenticate(request, options);
  }
}
