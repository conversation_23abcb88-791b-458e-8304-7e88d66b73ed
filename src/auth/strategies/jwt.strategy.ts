import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtPayload } from '@auth/interfaces/jwt-payload.interface';
import { User } from '@user/user.model';
import { ConfigService } from '@nestjs/config';
import { UserService } from '@user/user.service';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get('JWT_SECRET'),
      passReqToCallback: true, // add this line
    });
  }

  async validate(
    req: AuthenticatedRequest, // add req argument
    payload: JwtPayload,
  ): Promise<User & { bid: string; uid: string }> {
    const { sub: userId } = payload;
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new UnauthorizedException();
    }
    req.user = user;
    req.uid = user._id;
    req.bid = user.business?._id;

    return { ...user, uid: user._id, bid: user.business?._id };
  }
}
