import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-google-oauth20';
import { AuthService } from '../auth.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      clientID: configService.get('GOOGLE_CLIENT_ID'),
      clientSecret: configService.get('GOOGLE_CLIENT_SECRET'),
      callbackURL: configService.get('BASE_URL') + '/google/login-callback',
      prompt: 'consent',
      scope: ['profile', 'email'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: any): Promise<any> {
    const { email, name, picture } = profile._json; // Retrieve name and profile picture

    const expiryTime = new Date(new Date().getTime() + parseInt(profile?._json?.expires_in) * 1000);
    const user = {
      email,
      name, // Add user's name to the user object
      picture, // Add profile picture URL to the user object
      accessToken,
      refreshToken,
      expiryTime,
    };
    done(null, user);
  }
}
