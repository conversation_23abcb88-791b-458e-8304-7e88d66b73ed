import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { Strategy } from 'passport-facebook';

import { FacebookService } from '@integrations/socials/facebook/facebook.service';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  private readonly logger = new Logger(FacebookStrategy.name);
  constructor(
    private readonly facebookService: FacebookService,
    private readonly configService: ConfigService,
  ) {
    super({
      clientID: configService.get('FACEBOOK_CLIENT_ID'),
      clientSecret: configService.get('FACEBOOK_CLIENT_SECRET'),
      callbackURL: configService.get('BASE_URL') + '/facebook/callback',
      scope: ['pages_manage_posts'],
      enableProof: true,
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: any): Promise<any> {
    const pagesResponse = await this.facebookService.getPages(accessToken);
    const pages = pagesResponse?.data?.data;

    const { email } = profile._json;
    const expiryTime = new Date(new Date().getTime() + parseInt(profile?._json?.expires_in) * 1000);
    const user = {
      email,
      accessToken,
      refreshToken,
      expiryTime,
      pages,
    };
    done(null, user);
  }

  async authenticate(request, options) {
    this.logger.debug(`Authorizing request: ${request}`);
    // Add custom JWT token as a header in the authorization URL
    const jwtToken = request.query.token;
    options.state = jwtToken;
    options.accessType = 'offline';
    options.prompt = 'consent';
    options.approvalPrompt = 'force';

    // Call the parent authorize method to continue the OAuth flow
    return super.authenticate(request, options);
  }
}
