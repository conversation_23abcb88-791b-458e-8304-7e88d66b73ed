import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-facebook';
import { FacebookService } from '@integrations/socials/facebook/facebook.service';

@Injectable()
export class FacebookLoginStrategy extends PassportStrategy(Strategy, 'facebook-login') {
  private readonly logger = new Logger(FacebookLoginStrategy.name);
  constructor(
    private readonly configService: ConfigService,
    private readonly facebookService: FacebookService,
  ) {
    super({
      clientID: configService.get('FACEBOOK_CLIENT_ID'),
      clientSecret: configService.get('FACEBOOK_CLIENT_SECRET'),
      callbackURL: configService.get('BASE_URL') + '/facebook/login-callback',
      scope: ['email', 'public_profile'],
      profileFields: ['id', 'displayName', 'email', 'photos'],
      enableProof: true,
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: any): Promise<any> {
    const { email, name, picture } = profile._json; // Retrieve name and profile picture

    const expiryTime = new Date(new Date().getTime() + parseInt(profile?._json?.expires_in) * 1000);
    const user = {
      email,
      name, // Add user's name to the user object
      picture: picture?.data?.url, // Add profile picture URL to the user object
      accessToken,
      refreshToken,
      expiryTime,
    };
    done(null, user);
  }

  async authenticate(request, options) {
    this.logger.debug(`Authorizing request: ${request}`);

    // Customize other options if needed
    options.accessType = 'offline';
    options.prompt = 'consent';
    options.approvalPrompt = 'force';

    // Call the parent authorize method to continue the OAuth flow
    return super.authenticate(request, options);
  }
}
