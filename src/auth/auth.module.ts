import { JwtService } from '@nestjs/jwt';
import { forwardRef, Module } from '@nestjs/common';
import { SlackService } from '@common/services/slack.service';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { GoogleStrategy } from './strategies/google.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtStrategy } from './strategies/jwt.strategy';
import { WordpressStrategy } from './strategies/wordpress.strategy';
import { FacebookStrategy } from './strategies/facebook.strategy';
import { FacebookModule } from '@integrations/socials/facebook/facebook.module';
import { PaymentsModule } from 'src/payments/payments.module';
import { FacebookLoginStrategy } from './strategies/facebook-login.strategy';
import { GoogleYoutubeStrategy } from './strategies/google-youtube.strategy';
import { MongooseModule } from '@nestjs/mongoose';
import { SignupAttempt, SignupAttemptSchema } from './signup-attempt.model';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: SignupAttempt.name, schema: SignupAttemptSchema }]),
    PassportModule.register({
      defaultStrategy: 'jwt',
    }),
    PassportModule.register({
      defaultStrategy: 'wordpress',
    }),
    UserModule,
    BusinessModule,
    PaymentsModule,
    forwardRef(() => FacebookModule),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    GoogleStrategy,
    JwtStrategy,
    JwtService,
    WordpressStrategy,
    FacebookStrategy,
    FacebookLoginStrategy,
    GoogleStrategy,
    GoogleYoutubeStrategy,
    SlackService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
