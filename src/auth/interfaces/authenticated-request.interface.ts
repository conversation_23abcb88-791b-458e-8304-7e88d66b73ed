import type { Request as ExpressRequest } from 'express';
import type { BusinessDocument } from '@/business/business.model';
import type { UserDocument } from '@/user/user.model';

export interface AuthenticatedRequest extends ExpressRequest {
  user?: UserDocument;
  business?: BusinessDocument;
  bid?: string;
  uid?: string;
}

export interface Request<RequestBody = any> extends ExpressRequest {
  body: RequestBody;
  user: UserDocument;
  business?: BusinessDocument;
  bid: string;
  uid: string;
}
