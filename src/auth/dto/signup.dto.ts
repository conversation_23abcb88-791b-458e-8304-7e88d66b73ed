import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, MinLength } from 'class-validator';

export class SignupDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @MinLength(6)
  password: string;

  @ApiProperty()
  @IsOptional()
  businessName: string;

  @ApiProperty()
  foundUsFrom: string;

  @ApiProperty({ default: false })
  @IsOptional()
  isShopifyUser: boolean;

  @ApiProperty({ default: false })
  @IsOptional()
  isFreeUser: boolean;
}
