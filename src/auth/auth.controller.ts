import type { Request } from 'express';

import {
  InternalServerErrorException,
  BadRequestException,
  ForbiddenException,
  NotFoundException,
  Controller,
  UseGuards,
  Logger,
  Query,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';
import { ApiTags } from '@nestjs/swagger';

import { UserStatus } from '@user/user.model';

import { AuthenticatedRequest } from './interfaces/authenticated-request.interface';
import { IpGeolocationGuard } from './guards/ip-geolocation.guard';
import { PasswordForgotDto } from './dto/password-forgot.dto';
import { PasswordResetDto } from './dto/password-reset.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { AuthService } from './auth.service';
import { EmailGuard } from './guards/email.guard';
import { SignupDto } from './dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @UseGuards(EmailGuard, LocalAuthGuard)
  async login(@Req() req: AuthenticatedRequest, @Body() loginDto: LoginDto) {
    try {
      const user = req.user;
      if (user.status === UserStatus.deactivated) {
        throw new Error(
          'Your account has been deactivated. Please request your admin to reinvite you.',
        );
      }
      const result = await this.authService.login(user);
      return result;
    } catch (error) {
      this.logger.error(`Login failed for ${loginDto.email}`, error?.message);
      throw new BadRequestException('Login failed: ' + error?.message);
    }
  }

  @Post('refresh')
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    try {
      const { refresh_token } = refreshTokenDto;
      const result = await this.authService.refreshTokens(refresh_token);
      return result;
    } catch (error) {
      this.logger.error(`Token refresh failed`, error?.message);
      throw new BadRequestException('Token refresh failed: ' + error?.message);
    }
  }

  @Post('signup')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
  @UseGuards(EmailGuard, IpGeolocationGuard)
  async signUp(@Req() req: Request, @Body() signupDto: SignupDto) {
    try {
      const clientIp = (req as any).clientIp;
      const clientCountry = (req as any).clientCountry;
      this.logger.debug(`Signup attempt from ${clientIp} - ${clientCountry}`);
      const user = await this.authService.signUp(signupDto, clientIp, clientCountry);
      const result = await this.authService.login(user);
      return result;
    } catch (error) {
      this.logger.error(`Signup failed for ${signupDto.email}`, error?.message);
      if (error.code === 11000) {
        throw new BadRequestException('Email Already exist.');
      } else {
        throw new InternalServerErrorException(`Signup failed ${error?.message}`);
      }
    }
  }

  @Post('password-forgot')
  async forgotPassword(@Body() { email }: PasswordForgotDto) {
    const response = await this.authService.forgotPassword(email.toLowerCase().trim());

    if (response?.notFound) {
      throw new NotFoundException('No account associated with the email found.');
    }

    return { success: true };
  }

  @Post('password-reset')
  async resetPassword(@Body() { password, token }: PasswordResetDto) {
    const response = await this.authService.resetPassword(password, token);

    if (response?.notFound) {
      throw new ForbiddenException('Password change link expired. Please request link again.');
    }

    return { success: true };
  }

  @Post('accept-invitation')
  async acceptInvitation(@Body() { password, token }: PasswordResetDto) {
    const response = await this.authService.acceptInvitation(password, token);

    if (response?.notFound) {
      throw new ForbiddenException('Invitation link expired.');
    }

    return { success: true };
  }

  @Get('recaptcha')
  async calculateRecaptchaScore(
    @Query('token') token: string,
    @Query('action') recaptchaAction: 'SIGNUP',
  ) {
    return {
      score: await this.authService.validateRepatcha({ token, recaptchaAction }),
    };
  }
}
