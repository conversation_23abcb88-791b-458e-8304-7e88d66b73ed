import {
  UnauthorizedException,
  BadRequestException,
  Controller,
  UseGuards,
  Redirect,
  Headers,
  Delete,
  Logger,
  Param,
  Query,
  Patch,
  Body,
  Post,
  Get,
  Req,
  Put,
} from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import Stripe from 'stripe';

import { ProductPurchaseCancelDto } from '@/resources/product/dto/product-purchase-cancel.dto';
import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { AuthInsecure, Auth } from '@/auth/guards/auth.guard';
import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { ImpactAPIService } from '@/common/services/impact-api.service';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { BusinessService } from '@/business/business.service';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { ApiKeyGuard } from '@/auth/guards/api-key.guard';
import RequestWithRawBody from '@/common/middlewares/rawBody.interface';
import config from '@/common/configs/config';

import { StripeService } from './stripe.service';

@Controller('payments')
export class PaymentsController {
  private readonly logger = new Logger(PaymentsController.name);

  constructor(
    private readonly impactAPIService: ImpactAPIService,
    private readonly businessService: BusinessService,
    private readonly configService: ConfigService,
    private readonly stripeService: StripeService,
    private readonly jwtService: JwtService,
  ) {}

  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @Get('get-packages')
  async getPackages() {
    try {
      const packages = await this.stripeService.getPackages();
      return packages;
    } catch (error) {
      this.logger.error('Get packages failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @UseGuards(ApiKeyGuard)
  @Get('packages')
  async packages() {
    try {
      const packages = await this.stripeService.getPackages();
      return packages;
    } catch (error) {
      this.logger.error('Get packages failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(AuthInsecure)
  @Get('plans')
  async plans(@Req() req: AuthenticatedRequest) {
    try {
      const { bid } = req;
      const plans = await this.stripeService.getPlans(bid);
      return plans;
    } catch (error) {
      this.logger.error('Get packages failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @Get('validate-coupon')
  async validateCoupon(@Query('coupon') coupon: string) {
    try {
      const discount = await this.stripeService.validateCoupon(coupon);
      return { discount };
    } catch (error) {
      this.logger.error('Validate coupon failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @Post('create-subscription')
  async createSubscription(
    @Req() req: AuthenticatedRequest,
    @Body('price_id') priceId: string,
    @Body('coupon') coupon: string,
    @Query('is-upgrade') isUpgrade: boolean,
  ) {
    try {
      const cs = await this.stripeService.createSubscription(req, priceId, coupon, isUpgrade);
      return { ...(cs as any), message: 'Subscription Successful' };
    } catch (error) {
      this.logger.error('Create subscription failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @Post('update-subscription')
  async updateSubscription(
    @Req() { bid }: AuthenticatedRequest,
    @Body('price_id') priceId: string,
    @Body('coupon') coupon: string,
  ) {
    try {
      const clientSecret = await this.stripeService.updateSubscription(bid, priceId, coupon);
      return { clientSecret, message: 'Subscription Successful' };
    } catch (error) {
      this.logger.error('Create subscription failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @Delete('cancel-subscription')
  async cancelSubscription(
    @Req() { bid }: AuthenticatedRequest,
    @Query('comment') comment: string,
  ) {
    try {
      await this.stripeService.cancelSubscription(bid, comment);
      return { message: 'Subscription cancelled' };
    } catch (error) {
      this.logger.error('Cancel subscription failed', error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @Post('create-payment-intent')
  async createPaymentIntent(
    @Req() { bid }: AuthenticatedRequest,
    @Body('amount') amount: number,
    @Body('credits') credits: number,
  ): Promise<{ clientSecret: string }> {
    try {
      const paymentIntent = await this.stripeService.createPaymentIntent(bid, amount, credits);
      return { clientSecret: paymentIntent.client_secret };
    } catch (error) {
      this.logger.error('Create payment intent failed', error?.message);
      throw new BadRequestException(`Failed to create payment intent. ${error?.message}`);
    }
  }

  @Get('confirm-purchase-subscription')
  @UseGuards(QueryTokenGuard, Auth, BusinessGuard)
  @Redirect()
  async confirmSubscription(
    @Req() { bid }: AuthenticatedRequest,
    @Query('payment_intent') paymentIntentId: string,
    @Query('plan') plan: string,
    @Query('redirect_status') redirectStatus: string,
    @Query('payment_intent_client_secret') paymentIntentClientSecret: string,
    @Query('setup_intent') setupIntentId: string,
    @Query('setup_intent_client_secret') setupIntentClientSecret: string,
    @Query('coupon') coupon?: string,
    @Query('redirectTo') redirectTo?: string,
  ): Promise<{ url: string }> {
    const paymentId = paymentIntentId || setupIntentId;
    const paymentSecret = paymentIntentClientSecret || setupIntentClientSecret;

    if (!(paymentId && paymentSecret && redirectStatus === 'succeeded')) {
      throw new BadRequestException('Payment was not successful');
    }

    const baseRedirect = `${
      config().internalApps.blogifyClient.url
    }/dashboard?transaction=${paymentId}&success=`;

    try {
      const { price } = await this.stripeService.confirmSubscription(bid, paymentId, plan, coupon);

      return {
        url: `${baseRedirect}subscription-success&plan=${plan}&coupon=${coupon}&price=${price}${
          redirectTo ? `&redirectTo=${encodeURIComponent(redirectTo)}` : ''
        }`,
      };
    } catch (error) {
      return { url: `${baseRedirect}false&error=${error.message}` };
    }
  }

  @Patch('cancel-purchase-subscription')
  @UseGuards(Auth, BusinessGuard)
  async cancelPurchaseAddon(
    @Body() { paymentRef, subscriptionRef }: ProductPurchaseCancelDto,
  ): Promise<{ success: boolean }> {
    return this.stripeService.cancelPurchaseSubscription(paymentRef, subscriptionRef);
  }

  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  @Put('end-trial-and-upgrade')
  async endTrialAndUpgrade(@Req() { bid }: AuthenticatedRequest) {
    return this.stripeService.endTrialAndUpgrade(bid);
  }

  @Get('confirm-purchase-credit')
  @Redirect()
  async confirmPayment(
    @Query('token') token: string,
    @Query('payment_intent') paymentIntent: string,
    @Query('payment_intent_client_secret') paymentIntentClientSecret: string,
    @Query('redirect_status') redirectStatus: string,
  ): Promise<{ url: string }> {
    try {
      if (!token) {
        throw new UnauthorizedException('Token not found');
      }
      this.logger.log({
        paymentIntent,
        paymentIntentClientSecret,
        redirectStatus,
      });
      if (!paymentIntent || !paymentIntentClientSecret || redirectStatus !== 'succeeded') {
        throw new UnauthorizedException('Payment intent not found');
      }
      const decoded: any = this.jwtService.decode(token, { json: true });
      const { bid } = decoded;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      await this.stripeService.confirmPurchaseCredit(bid, paymentIntent);
      return {
        url: `${this.configService.get(
          'DASHBOARD_REDIRECT_URL',
        )}/dashboard?success=purchase-credit-success`,
      };
    } catch (error) {
      return {
        url: `${this.configService.get('DASHBOARD_REDIRECT_URL')}/dashboard?error=${
          error?.message
        }`,
      };
    }
  }

  @UseGuards(ApiKeyGuard)
  @Get('backup-pricing')
  async backupProductPricing() {
    try {
      await this.stripeService.backupProductPricing();
      return { message: 'Backup completed successfully' };
    } catch (error) {
      throw new BadRequestException(`Failed to backup pricing ${error?.message}`);
    }
  }

  @UseGuards(ApiKeyGuard)
  @Get('create-products-from-backup')
  async createProductsFromBackup() {
    try {
      await this.stripeService.createProductsAndPricesFromBackup();
      return { message: 'Products and prices created successfully' };
    } catch (error) {
      throw new BadRequestException(`Failed to create products and prices ${error?.message}`);
    }
  }

  @Post('stripe-webhook')
  async handleWebhookEvent(
    @Headers('stripe-signature') signature: string,
    @Req() req: RequestWithRawBody,
  ) {
    try {
      const event = this.stripeService.constructEvent(req.rawBody, signature);

      type StipeObject = {
        billing_reason: 'subscription_cycle';
        customer: string;
        lines: {
          data: {
            amount: number;
            plan: { nickname: 'YOUTUBE_CHANNELS' | 'YOUTUBE_CONNECT' | 'WRITING_SNIPPETS' };
          }[];
        };
      };

      const object = event.data.object as StipeObject;
      const addons = ['YOUTUBE_CHANNELS', 'YOUTUBE_CONNECT', 'WRITING_SNIPPETS'];
      if (addons.includes(object.lines?.data?.[0]?.plan?.nickname)) {
        // TODO: Handle Addons Subscription Downgrade
        return;
      }

      switch (event.type) {
        case 'customer.subscription.updated':
          const subscription = event.data.object as Stripe.Subscription;
          if (subscription.status === 'active' && subscription.trial_end) {
            const trialEnd = subscription.trial_end;
            const currentTime = Math.floor(Date.now() / 1000);

            if (trialEnd <= currentTime) {
              await this.stripeService.onTrialEnd(object.customer);
            }
          }
          break;

        case 'invoice.payment_succeeded':
          this.logger.debug('Invoice Payment Succeed:', object);
          if (object.billing_reason === 'subscription_cycle') {
            const { _id: bid } = await this.businessService.findByStripeId(object.customer);
            await this.impactAPIService.trackConversion({
              bid,
              amount: object.lines.data?.[0]?.amount || 0,
            });
          }
          break;

        case 'invoice.payment_failed':
          this.logger.debug('Invoice Payment Failed:', object);
          if (object.billing_reason === 'subscription_cycle') {
            this.logger.debug(`Downgrade subscription for ${object.customer}`);
            await this.stripeService.downgradeToFreeSubscription(object.customer);
          }
          break;

        case 'customer.subscription.deleted':
          this.logger.debug('Customer Subscription Failed:', object);
          this.logger.debug(`Downgrade subscription for ${object.customer}`);
          await this.stripeService.downgradeToFreeSubscription(object.customer, true);
          break;

        default:
          this.logger.log(`Unhandled event type ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error('Error handling webhook event:', error);
      throw new BadRequestException(`Webhook Error: ${error?.message}`);
    }
  }

  @Get(`verify/coupon/:coupon`)
  async verifyCoupon(@Param('coupon') coupon: string, @Query('price') price: number) {
    return this.stripeService.verifyCoupon(coupon, price);
  }
}
