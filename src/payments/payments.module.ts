import { JwtService } from '@nestjs/jwt';
import { Module } from '@nestjs/common';

import { CreditTransactionModule } from '@/resources/credit-transaction/credit-transaction.module';
import { ShareASaleAPIService } from '@/common/services/share-a-sale-api.service';
import { ImpactAPIService } from '@/common/services/impact-api.service';
import { SettingsModule } from '@/resources/settings/settings.module';
import { BusinessModule } from '@/business/business.module';
import { ProductModule } from '@/resources/product/product.module';
import { SlackService } from '@/common/services/slack.service';
import { EventModule } from '@/event/event.module';
import { UserModule } from '@/user/user.module';
import { JobModule } from '@/job/job.module';

import { BlogCreditProcessor } from './blog-credit.processor';
import { PaymentsController } from './payments.controller';
import { StripeService } from './stripe.service';

@Module({
  imports: [
    CreditTransactionModule,
    BusinessModule,
    SettingsModule,
    ProductModule,
    EventModule,
    UserModule,
    JobModule,
  ],
  controllers: [PaymentsController],
  providers: [
    ShareASaleAPIService,
    BlogCreditProcessor,
    ImpactAPIService,
    StripeService,
    SlackService,
    JwtService,
  ],
  exports: [StripeService],
})
export class PaymentsModule {}
