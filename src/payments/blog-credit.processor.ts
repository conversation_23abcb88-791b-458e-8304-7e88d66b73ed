import type { BusinessDocument } from '@/business/business.model';
import type { Job } from 'bull';

import { InjectQueue, Processor, Process } from '@nestjs/bull';
import { InjectModel } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { Queue } from 'bull';
import moment from 'moment';

import { CUSTOM_PLAN_LIMITS, PACKAGE_LIMITS, JOB_QUEUES } from '@/common/constants';
import { SUBSCRIPTION_STATUS, Business } from '@/business/business.model';
import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { CONTENT_COST } from '@/resources/credit-transaction/credit-transaction.constant';

export const getPackageLimits = (
  subscriptionPlan: string,
  customPlan: string,
): { CREDITS: number } => {
  let packageLimits = PACKAGE_LIMITS[subscriptionPlan];

  if (customPlan) {
    const planPeriod = subscriptionPlan.split('_')[1];
    if (CUSTOM_PLAN_LIMITS[customPlan] && CUSTOM_PLAN_LIMITS[customPlan][planPeriod]) {
      packageLimits = CUSTOM_PLAN_LIMITS[customPlan][planPeriod];
    }
  }

  return packageLimits;
};

const getPackageAndPeriod = (pkg: string): { plan: string; period: string } => ({
  plan: pkg.split('_')[1]?.toLowerCase() || 'free',
  period: pkg.split('_')[0]?.toLowerCase() || 'lifetime',
});

@Processor(JOB_QUEUES.BLOG_CREDITS)
export class BlogCreditProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.BLOG_CREDITS;

  protected readonly logger = new Logger(this.constructor.name);
  constructor(
    @InjectQueue(JOB_QUEUES.BLOG_CREDITS) private readonly blogCreditsQueue: Queue,
    @InjectModel(Business.name) private readonly businessModel: Model<Business>,
    private readonly creditTransactionService: CreditTransactionService,
  ) {
    super(blogCreditsQueue);
  }

  @Process()
  protected async process(job: Job): Promise<{ businessCount: number }> {
    return this.processWithLock(job, async () => {
      const oneDayAgo = new Date();
      oneDayAgo.setUTCDate(oneDayAgo.getDate() - 1);
      oneDayAgo.setHours(0, 0, 0, 0);

      const filters = {
        $or: [
          { blogCreditLastUpdate: { $lte: oneDayAgo } },
          { blogCreditLastUpdate: { $exists: false } },
        ],
        $and: [
          { subscriptionStatus: { $eq: SUBSCRIPTION_STATUS.ACTIVE } },
          { subscriptionStatus: { $ne: SUBSCRIPTION_STATUS.CANCELLED } },
        ],
      };

      const businesses = await this.businessModel
        .find(filters)
        .select(
          '_id credits monthlyCredits subscriptionPlan customPlan blogCreditLastUpdate addon',
        );

      businesses.forEach((business) => {
        this.blogCreditsQueue.add('RESET_MONTHLY_CREDITS', { business });
        this.blogCreditsQueue.add('RESET_MONTHLY_ADDON_CREDITS', { business });
      });

      return { businessCount: businesses.length };
    });
  }

  @Process({ name: 'RESET_MONTHLY_CREDITS', concurrency: 10 })
  protected async resetMonthlyCredits({
    data: { businessId, ...data },
  }: Job<{ businessId?: string; business?: BusinessDocument }>): Promise<{
    monthlyCreditsDeducted: number;
    monthlyCreditsAdded: number;
  }> {
    if (!businessId && !data.business) {
      throw new Error('Business ID or business document is required.');
    }

    const business = data.business
      ? data.business
      : await this.businessModel
          .findById(businessId)
          .select('_id credits monthlyCredits subscriptionPlan customPlan blogCreditLastUpdate');
    if (!business) {
      throw new Error('Business not found.');
    }

    const { subscriptionPlan, customPlan, monthlyCredits, credits, _id } = business;
    const packageLimits = getPackageLimits(subscriptionPlan, customPlan);
    const bid = String(_id);
    let monthlyCreditsDeducted = 0;
    let monthlyCreditsAdded = 0;

    if (monthlyCredits > 0) {
      monthlyCreditsDeducted = monthlyCredits;
      const deductAmount = credits < monthlyCredits ? credits : monthlyCredits;
      await this.creditTransactionService.deductUnusedMonthlyCredits({ bid }, deductAmount);
    }

    if (packageLimits.CREDITS) {
      const { plan, period } = getPackageAndPeriod(business.subscriptionPlan);
      monthlyCreditsAdded = packageLimits.CREDITS;
      await this.creditTransactionService.addMonthlyCredits(
        bid,
        packageLimits.CREDITS,
        `Your monthly credits have been refilled as per your ${business.customPlan ? `${business.customPlan.toLowerCase()} ` : ''}${period} ${plan} plan.`,
      );
    }

    await this.businessModel.findByIdAndUpdate(bid, { blogCreditLastUpdate: new Date() });

    return { monthlyCreditsDeducted, monthlyCreditsAdded };
  }

  @Process({ name: 'RESET_MONTHLY_ADDON_CREDITS', concurrency: 10 })
  protected async resetMonthlyAddonCredits({
    data: { businessId, ...data },
  }: Job<{ businessId?: string; business?: BusinessDocument }>): Promise<{
    addonCreditsAdded: number;
  }> {
    if (!businessId && !data.business) {
      throw new Error('Business ID or business document is required.');
    }

    const business = data.business
      ? data.business
      : await this.businessModel.findById(businessId).select('_id addons');
    if (!business) {
      throw new Error('Business not found.');
    }

    const { _id, addons } = business;
    const bid = String(_id);
    let addonCreditsAdded = 0;

    for (const [, addon] of Object.entries(addons || {})) {
      const isExpired = moment().isAfter(moment(addon.expirationDate));
      if (addon.status === 'active' && !isExpired) {
        const amount =
          CONTENT_COST['blog-from-media'] * (addon.features?.['mediaCredit'] || 0) +
          CONTENT_COST['blog-from-text'] * (addon.features?.['promptCredit'] || 0);
        if (amount) {
          addonCreditsAdded += amount;
          await this.creditTransactionService.addMonthlyCredits(
            bid,
            amount,
            `${addon.name} Credits`,
          );
        }
      }
    }

    return { addonCreditsAdded };
  }
}
