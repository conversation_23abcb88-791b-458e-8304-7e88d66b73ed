import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Stripe } from 'stripe';
import { Model } from 'mongoose';
import * as fs from 'fs';

import {
  LATEST_SUBSCRIPTION_PLANS,
  ALL_SUBSCRIPTION_PLANS,
  CUSTOM_PLAN_LIMITS,
  PACKAGE_LIMITS,
  CUSTOM_PLANS,
} from '@common/constants';
import { CreditTransactionService } from '@resources/credit-transaction/credit-transaction.service';
import { migrateUsersToMailchimp } from '@/scripts/migrate-users-to-mailchimp';
import { getPriceAfterDiscount } from '@common/utils/price';
import { ShareASaleAPIService } from '@/common/services/share-a-sale-api.service';
import { BusinessService } from '@business/business.service';
import { ProductService } from '@resources/product/product.service';
import { SlackService } from '@common/services/slack.service';
import { UserService } from '@user/user.service';
import { Settings } from '@resources/settings/settings.model';
import { Addon } from '@resources/product/product.model';
import config from '@common/configs/config';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private stripe: Stripe;

  constructor(
    @InjectModel(Settings.name) private settingsModel: Model<Settings>,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly shareASaleAPIService: ShareASaleAPIService,
    private readonly businessService: BusinessService,
    private readonly productService: ProductService,
    private readonly configService: ConfigService,
    private readonly slackService: SlackService,
    private readonly userService: UserService,
  ) {
    this.stripe = new Stripe(configService.get('STRIPE_SECRET_KEY'), {
      apiVersion: '2022-11-15',
    });
  }

  constructEvent(payload: Buffer, signature: string) {
    return this.stripe.webhooks.constructEvent(
      payload,
      signature,
      this.configService.get('STRIPE_WEBHOOK_SECRET'),
    );
  }

  async getPackages() {
    try {
      const prices = await this.stripe.prices.list({
        active: true,
        limit: 100,
        product: config().stripeProductId,
      });
      const order = ['MONTHLY', 'YEARLY', 'LIFETIME'];

      return prices.data
        .sort((p1, p2) => p1.unit_amount - p2.unit_amount)
        .sort(
          (p1, p2) =>
            order.indexOf(p1.nickname?.split('_')[0]) - order.indexOf(p2.nickname?.split('_')[0]),
        )
        .map((price) => ({
          id: price.id,
          name: price.nickname,
          price: price.unit_amount,
          currency: price.currency,
          limit: PACKAGE_LIMITS[price.nickname],
          interval: price.recurring?.interval,
          isLatest: !!(price.lookup_key && LATEST_SUBSCRIPTION_PLANS[price.lookup_key]),
        }));
    } catch (error) {
      throw new Error('Error getting packages:', error);
    }
  }

  async getPlans(bid: string) {
    try {
      let business = {
        subscriptionPlan: ALL_SUBSCRIPTION_PLANS.FREE,
        subscriptionStatus: 'inactive',
      };
      try {
        const businessResult = await this.businessService.findOne(bid);
        if (businessResult) {
          business = businessResult;
        }
      } catch (error) {
        this.logger.error('Failed to get business', error);
      }

      const packages = await this.getPackages();

      return {
        subscriptionStatus: business.subscriptionStatus,
        subscriptionPlan: business.subscriptionPlan,
        subscriptionPrice:
          packages.find((price) => price.name === business.subscriptionPlan)?.price || 0,
        isLatestPlan: !!LATEST_SUBSCRIPTION_PLANS[business.subscriptionPlan],
        packages: packages.map((price) => ({
          ...price,
          isActive: price.name === business.subscriptionPlan,
        })),
      };
    } catch (error) {
      throw new Error('Failed to get pricing plans for user', error);
    }
  }

  async validateCoupon(coupon: string) {
    try {
      const stripeCoupon = await this.stripe.coupons.retrieve(coupon);

      if (!stripeCoupon?.valid) {
        throw new Error('Invalid coupon code.');
      }

      return stripeCoupon.percent_off;
    } catch (error) {
      throw new Error(error?.message);
    }
  }

  async createPaymentIntent(
    bid: string,
    amount: number,
    credits: number,
  ): Promise<{ client_secret: string }> {
    const business = await this.businessService.findOne(bid);
    if (!business || !business.stripeId) {
      throw new Error('Business does not have stripe customer id');
    }
    const customerId = business.stripeId;
    const paymentIntent = await this.stripe.paymentIntents.create({
      customer: customerId,
      amount,
      currency: 'usd',
      setup_future_usage: 'off_session',
      metadata: { credits },
    });

    return paymentIntent;
  }

  async confirmPurchaseCredit(bid: string, paymentIntentId: string) {
    const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
    const { credits } = paymentIntent.metadata;
    await this.creditTransactionService.addAdditionalCredits(bid, parseInt(credits, 10));
  }

  // eslint-disable-next-line complexity
  async confirmSubscription(
    bid: string,
    paymentIntentId: string,
    plan: string,
    coupon?: string,
    subscription?: Stripe.Subscription,
  ) {
    // eslint-disable-next-line no-useless-catch
    try {
      const business = await this.businessService.findOne(bid);
      if (!business) {
        throw new Error('Business not found');
      }

      const isLifeTimeUser = plan.startsWith('LIFETIME_') && this.isLifetimeCoupon(coupon);
      const isShopifyUser = coupon && coupon === this.configService.get('SHOPIFY_COUPON');
      const isTrial = !!paymentIntentId?.startsWith('seti') && !isLifeTimeUser && !isShopifyUser;
      let paymentIntent: Stripe.SetupIntent | Stripe.PaymentIntent;

      if (!isLifeTimeUser && !isShopifyUser) {
        paymentIntent = (await this.stripe[isTrial ? 'setupIntents' : 'paymentIntents'].retrieve(
          paymentIntentId,
          isTrial ? {} : { expand: ['invoice.subscription'] },
        )) as Stripe.SetupIntent | Stripe.PaymentIntent;

        if (!isTrial && (!paymentIntent || paymentIntent.status !== 'succeeded')) {
          throw new Error('Payment was not successful');
        }

        if (
          isTrial &&
          (!paymentIntent ||
            !['succeeded', 'requires_action', 'requires_confirmation'].includes(
              paymentIntent.status,
            ))
        ) {
          throw new Error('Payment failed please contact support');
        }
      }

      const customer = await this.stripe.customers.retrieve(business.stripeId);
      const user = await this.userService.getUserByBusinessId(bid);

      await this.updateBusinessSubscription(
        bid,
        {
          ...subscription,
          plan: { nickname: plan },
          customer: customer.id,
        } as any,
        false,
        !!isTrial,
      );

      if (isLifeTimeUser) {
        const customPlan = this.getCustomPlan(coupon);
        if (customPlan) {
          await this.businessService.addCustomPlan(bid, customPlan);
        }
        await this.updateCustomPackageLimits(bid, plan, customPlan);
      } else {
        await this.updatePackageLimits(bid, customer, plan);
      }

      migrateUsersToMailchimp(false, [user.email]);

      this.logger.log(`Subscription successful for ${bid} for ${plan} plan coupon code: ${coupon}`);

      try {
        if (!plan.startsWith('LIFETIME_')) {
          if (['PREMIUM', 'BUSINESS', 'ENTERPRISE', 'UNLIMITED'].some((p) => plan.endsWith(p))) {
            await this.productService.attachAddonWithSubscription(bid, Addon.Snippet, plan);
          }
          if (plan.endsWith('BUSINESS')) {
            await this.productService.attachAddonWithSubscription(bid, Addon.YouTube, plan);
          }
          if (['ENTERPRISE', 'UNLIMITED'].some((p) => plan.endsWith(p))) {
            await this.productService.attachAddonWithSubscription(bid, Addon.YouTubePro, plan);
          }
        } else if (plan === 'LIFETIME_ENTERPRISE') {
          await this.productService.attachAddonWithSubscription(bid, Addon.YouTube, plan);
        }
      } catch (error) {
        this.logger.error(`Couldn't attach add with the subscription`, error);
      }

      if (isTrial) {
        await this.slackService.sendFreeTrialNotification({
          email: user.email,
          name: user.name,
          businessName: business.name,
          plan,
          coupon,
        });
      } else {
        await this.slackService.sendPaidPlanNotification({
          email: user.email,
          name: user.name,
          businessName: business.name,
          plan,
          coupon,
          isShopifyUser,
        });
      }

      const subscriptions = await this.stripe.subscriptions.list({ customer: customer.id });
      if (subscriptions.data.length === 1) {
        try {
          return { price: subscriptions.data[0].items.data[0].plan.amount };
        } catch (error) {
          this.logger.error('Failed to get price', error);
          return { price: 0 };
        }
      }
      return { price: 0 };
    } catch (error) {
      throw error;
    }
  }

  async createCustomer(email: string, name: string) {
    const existingCustomers = await this.stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      // Customer already exists, return the existing customer
      return existingCustomers.data[0];
    } else {
      // Customer does not exist, create a new customer
      const customer = await this.stripe.customers.create({
        email: email,
        name: name,
      });

      return customer;
    }
  }

  // eslint-disable-next-line complexity
  async createSubscription(
    { uid, bid, user }: AuthenticatedRequest,
    priceId: string,
    couponCode?: string,
    isUpgrade?: boolean,
  ): Promise<{ clientSecret: string; subscriptionRef: string } | string> {
    try {
      let coupon: Stripe.Coupon;
      const couponOpts = couponCode ? { coupon: couponCode } : {};
      const business = await this.businessService.findOne(bid);
      if (!business || !business.stripeId) {
        throw new Error('Business does not have stripe customer id');
      }
      const customerId = business.stripeId;

      const packages = await this.getPackages();

      const plan = packages.find((price) => price.id === priceId);
      const isFreePlan = plan.name === 'FREE';

      if (!plan) {
        throw new Error('Package does not exist');
      }

      this.logger.log(
        `Creating subscription for ${bid} with ${plan.name} plan ${
          couponCode ? 'using coupon' + couponCode : ''
        } for bid: ${bid}`,
      );

      // Check if the user is a lifetime or shopify user
      const isLifeTimeUser = plan.name.startsWith('LIFETIME_') && this.isLifetimeCoupon(couponCode);
      const isShopifyUser = couponCode && couponCode === this.configService.get('SHOPIFY_COUPON');

      if (couponCode) {
        // Validate the coupon code
        coupon = await this.stripe.coupons.retrieve(couponCode);

        // Check if the coupon is valid and active
        if (!coupon || !coupon.valid) {
          this.logger.error(`Invalid coupon code: ${couponCode} for ${bid} for ${plan} plan`);
          throw new Error('Invalid coupon code.');
        }

        // Check if the coupon is valid for this plan
        if (
          !this.isValidCouponForPlan(plan.name, couponCode, business.subscriptionPlan) &&
          !isShopifyUser
        ) {
          this.logger.error(
            `This coupon code is not valid for this plan: ${couponCode} for ${bid} for ${plan} plan`,
          );
          throw new Error(`Coupon ${couponCode} is not valid for ${plan.name} plan`);
        }
      }

      const subscriptionsInSamePlan = await this.listSubscriptions(customerId, priceId);
      if (subscriptionsInSamePlan.data.length > 0) {
        throw new Error(`Subscription with same plan already exists`);
      }
      const subscriptions = await this.listSubscriptions(customerId, priceId);
      if (subscriptions.data.length > 0 && isShopifyUser) {
        throw new Error(`Another active subscription already exists try upgrading instead`);
      }

      const TRIAL_PERIOD =
        (await this.settingsModel.findOne({ key: 'SUBSCRIPTION_TRIAL_PERIOD', deleted: null }))
          ?.value || 0;

      const isTrial =
        !isUpgrade && !!TRIAL_PERIOD && !isLifeTimeUser && !isShopifyUser && !isFreePlan;

      let subscription: Stripe.Subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        off_session: true,
        payment_settings: {
          save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent', 'pending_setup_intent'],
        ...(isTrial ? { trial_period_days: TRIAL_PERIOD } : {}),
        ...couponOpts,
      });

      this.logger.log(`Subscription created for ${bid} for ${plan?.name} plan`);

      let clientSecret: string;
      let paymentIntent: Stripe.PaymentIntent | Stripe.SetupIntent;
      if (isTrial) {
        paymentIntent = subscription.pending_setup_intent as Stripe.SetupIntent;
        clientSecret = paymentIntent?.client_secret;
      } else {
        const invoice = subscription.latest_invoice as Stripe.Invoice;
        paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;
        clientSecret = paymentIntent?.client_secret;
      }

      // confirm subscription if the user is a shopify user because we don't need to collect payment
      if (isShopifyUser) {
        subscription = await this.stripe.subscriptions.update(subscription.id, {
          coupon: this.configService.get('SHOPIFY_COUPON'),
        });
        await this.confirmSubscription(bid, paymentIntent?.id, plan.name, couponCode, subscription);

        return `shopify`;
      }

      // Handle Lifetime Subscription by adding a coupon to the subscription
      if (isLifeTimeUser) {
        subscription = await this.stripe.subscriptions.update(subscription.id, {
          coupon: this.configService.get('STRIPE_LIFETIME_COUPON'),
        });

        this.logger.log(`Added lifetime coupon to subscription for ${bid} for ${plan} plan`);
      }
      if (isLifeTimeUser && !clientSecret) {
        await this.confirmSubscription(bid, paymentIntent?.id, plan.name, couponCode, subscription);

        return `lifetime`;
      }

      await this.businessService.update(bid, {
        subscriptionId: subscription.id,
        ...(isTrial ? { subscriptionStatus: 'trial' } : {}),
        ...(isFreePlan ? { subscriptionPlan: 'FREE', subscriptionStatus: 'active' } : {}),
      });

      migrateUsersToMailchimp(false, [user.email]);

      const shouldVerifyFreeUsers =
        (await this.settingsModel.findOne({ key: 'VERIFY_FREE_USERS', deleted: null }))?.value ||
        false;

      await this.userService.update(uid, {
        verified: isFreePlan && shouldVerifyFreeUsers ? false : true,
      });

      if (isFreePlan) {
        if (shouldVerifyFreeUsers) {
          await this.userService.sendVerificationMail(user.email);
        } else {
          await this.creditTransactionService.addAdditionalCredits(bid, 20, 'Free Plan Credits');
        }
        return;
      }

      if (clientSecret) {
        return { clientSecret, subscriptionRef: subscription.id };
      } else {
        throw new Error('Error creating subscription. Please contact support');
      }
    } catch (error) {
      this.logger.error(error);
      this.logger.error(`Error creating subscription: ${error?.message}`);
      throw new Error(error?.message);
    }
  }

  async cancelPurchaseSubscription(
    paymentRef: string,
    subscriptionRef: string,
  ): Promise<{ success: true }> {
    try {
      const isTrial = paymentRef.startsWith('seti');
      if (isTrial) {
        await this.stripe.setupIntents.cancel(paymentRef);
      } else {
        await this.stripe.paymentIntents.cancel(paymentRef);
      }
      await this.stripe.subscriptions.cancel(subscriptionRef);

      return { success: true };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async handleTrialEnd(bid: string, isManualUpgrade = false) {
    const business = await this.businessService.findOne(bid);

    const user = await this.userService.getUserByBusinessId(bid);

    if (business.subscriptionPlan.includes('LIFETIME')) {
      return;
    }

    try {
      const subscription = await this.stripe.subscriptions.retrieve(business.subscriptionId, {
        expand: ['latest_invoice.payment_intent'],
      });

      const latestInvoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = latestInvoice.payment_intent as Stripe.PaymentIntent;

      if (paymentIntent.status === 'succeeded') {
        // Get the subscription from Stripe to ensure we have the correct plan
        let stripePlan;
        if (business.subscriptionId) {
          const subscriptionItemData = await this.stripe.subscriptions.retrieve(
            business.subscriptionId,
            {
              expand: ['items.data.price'],
            },
          );

          stripePlan = subscriptionItemData.items.data[0]?.price?.nickname;
        }
        const subscriptionPlan =
          business.subscriptionPlan === 'NOPLAN' ? stripePlan : business.subscriptionPlan;
        // Calculate months since subscription started
        const startDate = new Date(subscription?.created * 1000); // Convert Unix timestamp to milliseconds
        const currentPeriodStart = new Date(subscription?.current_period_start * 1000);
        const monthsDuration = Math.floor(
          (currentPeriodStart.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30),
        );

        await this.businessService.update(bid, {
          subscriptionStatus: 'active',
          subscriptionPlan,
        });

        try {
          await this.shareASaleAPIService.editTransaction({
            bid: String(business._id),
            amount: subscription.items?.data?.[0]?.plan?.amount || 0,
          });
        } catch (e) {
          this.logger.error('Error with Share A Sale:', e);
        }

        await this.slackService.sendPaidPlanNotification({
          email: user.email,
          name: business.name,
          businessName: business.name,
          plan: subscriptionPlan,
          customMessage: isManualUpgrade
            ? 'Upgraded to paid plan before trial end :green_heart:'
            : !monthsDuration
              ? 'Trial Ended & Upgraded to Paid Plan :heart:'
              : `Automatically Renewed Subscription for ${monthsDuration} Months :blue_heart:`,
        });
        migrateUsersToMailchimp(false, [user.email]);
        return { success: true };
      } else if (paymentIntent.status === 'requires_action') {
        await this.slackService.sendPaidPlanNotification({
          email: user.email,
          name: business.name,
          businessName: business.name,
          plan: business.subscriptionPlan,
          customMessage: 'Payment requires additional action. @support :rotating_light:',
        });
        throw new Error(
          `Payment requires additional action please check your email for more details`,
        );
      } else {
        await this.businessService.update(bid, {
          subscriptionStatus: 'inactive',
          subscriptionPlan: 'NOPLAN',
        });
        migrateUsersToMailchimp(false, [user.email]);
        throw new Error(`Payment failed or is pending. Status: ${paymentIntent.status}`);
      }
    } catch (e) {
      this.logger.error(e);
      await this.businessService.update(bid, {
        subscriptionStatus: 'inactive',
        subscriptionPlan: 'NOPLAN',
      });
      migrateUsersToMailchimp(false, [user.email]);
      throw new Error(
        isManualUpgrade
          ? `Upgrade failed. Please contact our support.`
          : `Trial end processing failed. Please contact our support.`,
      );
    }
  }

  async endTrialAndUpgrade(bid: string) {
    try {
      const business = await this.businessService.findOne(bid);
      await this.stripe.subscriptions.update(business.subscriptionId, {
        trial_end: 'now',
      });

      return this.handleTrialEnd(bid, true);
    } catch (e) {
      this.logger.error(e);
      throw new Error(`Upgrade failed. Please contact our support.`);
    }
  }

  async onTrialEnd(customerId: string) {
    const business = await this.businessService.findByStripeId(customerId);
    return this.handleTrialEnd(business._id);
  }

  async downgradeToFreeSubscription(customerId: string, isCancelled = false) {
    try {
      const business = await this.businessService.findByStripeId(customerId);
      const isTrial = business.subscriptionStatus === 'trial';

      if (!business) {
        throw new Error('Business does not exist');
      }

      if (business.subscriptionPlan.includes('LIFETIME')) {
        return;
      }

      if (isCancelled) {
        await this.cancelSubscription(business['_id']);
      }
      const subscriptions = await this.listSubscriptions(customerId, null);

      // const packages = await this.getPackages();

      // const freePackage = packages.find((price) => price.name === 'NOPLAN');

      if (subscriptions.data.length === 0) {
        // const subscription: Stripe.Subscription = await this.stripe.subscriptions.create({
        //   customer: customerId,
        //   items: [{ price: freePackage.id }],
        //   expand: ['latest_invoice.payment_intent'],
        // });

        // await this.updateBusinessSubscription(business['_id'], subscription);

        this.businessService.update(business._id, {
          subscriptionId: null,
          subscriptionPlan: 'NOPLAN',
          subscriptionStatus: 'cancelled',
        });

        const user = await this.userService.getUserByBusinessId(business['_id']);

        migrateUsersToMailchimp(false, [user.email]);

        if (isTrial) {
          await this.slackService.sendCancelSubscriptionOnTrialEndNotification({
            name: business.name,
            businessName: business.name,
            plan: business.subscriptionPlan,
          });
        }

        return {};
      }
    } catch (error) {
      this.logger.error(`Error creating Free subscription: ${error?.message}`);
      throw new Error(error?.message);
    }
  }

  async createFreeSubscription(bid, customerId) {
    try {
      const business = await this.businessService.findOne(bid);

      if (!business) {
        throw new Error('Business does not exist');
      }

      const packages = await this.getPackages();

      const freePackage = packages.find((price) => price.name === 'FREE');

      const subscription: Stripe.Subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: freePackage.id }],
        expand: ['latest_invoice.payment_intent'],
      });

      await this.updateBusinessSubscription(
        business['_id'] as unknown as string,
        subscription,
        true,
      );

      return subscription;
    } catch (error) {
      this.logger.error(`Error creating Free subscription: ${error?.message}`);
      throw new Error(error?.message);
    }
  }

  async listSubscriptions(customerId, priceId) {
    const price = priceId ? { price: priceId } : {};
    const subscriptions = await this.stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
      ...price,
    });

    return subscriptions;
  }

  // eslint-disable-next-line complexity
  async updateSubscription(bid: string, priceId: string, couponCode?: string) {
    try {
      const business = await this.businessService.findOne(bid);
      let coupon: Stripe.Coupon;

      if (!business || !business.stripeId) {
        throw new Error('Business does not have stripe customer id');
      }
      if (!priceId) {
        throw new Error('Price id is required');
      }
      const user = await this.userService.getUserByBusinessId(bid);
      const isShopifyUser = couponCode && couponCode === this.configService.get('SHOPIFY_COUPON');
      if (!business.stripeId || (!business.subscriptionId && !isShopifyUser)) {
        throw new Error('Create new subscription instead');
      }
      const hasValidPaymentMethod = await this.hasValidPaymentMethod(business.stripeId);
      if (!hasValidPaymentMethod && !isShopifyUser) {
        throw new Error(`Create new subscription instead`);
      }

      const subscriptionsInSamePlan = await this.listSubscriptions(business.stripeId, priceId);
      if (subscriptionsInSamePlan.data.length > 0) {
        throw new Error(`Subscription with same plan already exists`);
      }

      const existingSubscription = await this.stripe.subscriptions.retrieve(
        business.subscriptionId,
      );

      if (!existingSubscription) {
        throw new Error('No existing subscription exist create new one');
      }

      const subscriptionItemId = existingSubscription?.items?.data[0]?.id;

      if (couponCode) {
        // Validate the coupon code
        coupon = await this.stripe.coupons.retrieve(couponCode);

        // Check if the coupon is valid and active
        if (!coupon || !coupon.valid) {
          this.logger.error(
            `Invalid coupon code: ${couponCode} to updgrade to ${priceId} for ${bid}`,
          );
          throw new Error('Invalid coupon code.');
        } else {
          this.logger.log(`Valid coupon code: ${couponCode} to updgrade to ${priceId} for ${bid}`);
        }
      }

      const subscription = coupon?.valid
        ? await this.stripe.subscriptions.update(existingSubscription.id, {
            items: [
              {
                id: subscriptionItemId,
                price: priceId,
              },
            ],
            coupon: couponCode,
          })
        : await this.stripe.subscriptions.update(existingSubscription.id, {
            items: [
              {
                id: subscriptionItemId,
                price: priceId,
              },
            ],
          });

      // delete other active subscriptions
      const subscriptions = await this.stripe.subscriptions.list({
        customer: business.stripeId,
        expand: ['data.items'],
      });

      for (const sub of subscriptions.data) {
        if (sub.id !== subscription.id) {
          await this.stripe.subscriptions.del(sub.id);
        }
      }

      await this.updateBusinessSubscription(bid, subscription);
      await this.updatePackageLimits(bid, business.stripeId);

      const subscriptionPlan = Object.keys(ALL_SUBSCRIPTION_PLANS).find(
        (key) => ALL_SUBSCRIPTION_PLANS[key] === subscription['plan'].nickname,
      );

      await this.slackService.sendPaidPlanNotification({
        email: user.email,
        name: business.name,
        businessName: business.name,
        plan: subscriptionPlan,
        coupon: couponCode,
        isShopifyUser,
        customMessage: `Subscription updated to ${subscriptionPlan} plan from ${business.subscriptionPlan} plan`,
      });

      migrateUsersToMailchimp(false, [user.email]);

      return subscription['message'] || `Subscripton updated successfully`;
    } catch (error) {
      this.logger.error(`Error upgrading subscription: ${error?.message}`);
      throw new Error(
        `Failed to update subscription: ${error?.message}. We could not charge your card, please use a valid payment method.`,
      );
    }
  }

  async cancelSubscription(bid: string, comment?: string) {
    try {
      const business = await this.businessService.findOne(bid);
      const user = await this.userService.getUserByBusinessId(bid);

      const stripeOptions = {
        cancellation_details: { comment: comment || '' },
      };

      if (business.stripeId) {
        const customer = await this.stripe.customers.retrieve(business.stripeId);
        const subscriptions = await this.stripe.subscriptions.list({
          customer: customer.id,
          expand: ['data.items'],
        });

        // delete all active subscriptions
        let sub: Stripe.Subscription;
        for (const subscription of subscriptions.data) {
          sub = await this.stripe.subscriptions.del(subscription.id, stripeOptions);
        }

        await this.businessService.updateSubscriptions(
          bid,
          sub.customer as string,
          sub.id,
          ALL_SUBSCRIPTION_PLANS.FREE,
          'cancelled',
        );

        await this.slackService.sendCancelSubscriptionNotification({
          email: user.email,
          name: business.name,
          businessName: business.name,
          plan: business.subscriptionPlan,
          customMessage: comment,
        });

        migrateUsersToMailchimp(false, [user.email]);
      }
      if (business.subscriptionId) {
        const subscription = await this.stripe.subscriptions.del(
          business.subscriptionId,
          stripeOptions,
        );
        await this.businessService.updateSubscriptions(
          bid,
          subscription.customer as string,
          subscription.id,
          ALL_SUBSCRIPTION_PLANS.FREE,
          'cancelled',
        );

        migrateUsersToMailchimp(false, [user.email]);
        await this.slackService.sendCancelSubscriptionNotification({
          email: user.email,
          name: business.name,
          businessName: business.name,
          plan: business.subscriptionPlan,
          customMessage: comment,
        });

        return subscription;
      } else {
        await this.slackService.sendCancelSubscriptionNotification({
          email: user.email,
          name: business.name,
          businessName: business.name,
          plan: business.subscriptionPlan,
          customMessage: `Could not cancel. Subscription does not exist :question: User Comment: ${comment}.`,
        });

        throw new Error('Subscription does not exist');
      }
    } catch (error) {
      this.logger.error(`Error canceling subscription: ${error?.message}`);
      await this.slackService.sendCancelSubscriptionNotification({
        bid,
        customMessage: `Error canceling subscription :question:: ${error?.message}. User Comment: ${comment}.`,
      });
      return 'could not cancel subscription probably it was already cancelled';
    }
  }

  async backupProductPricing() {
    try {
      const products = await this.stripe.products.list();
      const prices = await this.stripe.prices.list();

      const backupData = {
        products: products.data,
        prices: prices.data,
      };

      fs.writeFileSync('stripe_backup.json', JSON.stringify(backupData, null, 2));
      this.logger.log('Backup file created successfully.');
    } catch (error) {
      this.logger.error('Error creating backup:', error);
    }
  }

  async createProductsAndPricesFromBackup() {
    try {
      const backupData = JSON.parse(fs.readFileSync('stripe_backup.json', 'utf-8'));

      let createdProduct: Stripe.Product;
      for (const productData of backupData.products) {
        const formattedProductData = this.formatProductData(productData);
        createdProduct = await this.stripe.products.create(formattedProductData);
        this.logger.log(`Product created: ${createdProduct.id}`);
      }

      for (const priceData of backupData.prices) {
        const formattedPriceData = this.formatPriceData(priceData, createdProduct.id);
        const createdPrice = await this.stripe.prices.create(formattedPriceData);
        this.logger.log(`Price created: ${createdPrice.id}`);
      }

      this.logger.log('Products and prices created successfully.');
    } catch (error) {
      this.logger.error('Error creating products and prices:', error);
    }
  }

  private formatProductData(productData: any): any {
    const formattedProductData = {
      name: productData.name,
      type: productData.type,
      description: productData.description,
      images: productData.images || [],
      metadata: productData.metadata || {},
    };

    delete formattedProductData['id'];
    delete formattedProductData['object'];
    delete formattedProductData['active'];
    delete formattedProductData['created'];

    return formattedProductData;
  }

  private formatPriceData(priceData: any, productId: string): any {
    const formattedPriceData = {
      currency: priceData.currency,
      product: productId,
      unit_amount: priceData.unit_amount,
      active: priceData.active !== false,
      metadata: priceData.metadata || {},
      nickname: priceData.nickname || null,
      recurring: priceData.recurring?.interval
        ? {
            interval: priceData.recurring?.interval,
            interval_count: priceData.recurring?.interval_count || 1,
            usage_type: priceData.recurring?.usage_type || 'licensed',
          }
        : {},
    };

    delete formattedPriceData['id'];
    delete formattedPriceData['object'];

    return formattedPriceData;
  }

  async updateBusinessSubscription(
    bid: string,
    subscription: Stripe.Subscription,
    subscriptionWithZeroAmount?: boolean,
    isTrial?: boolean,
  ) {
    try {
      const subscriptionPlan = Object.keys(ALL_SUBSCRIPTION_PLANS).find(
        (key) => ALL_SUBSCRIPTION_PLANS[key] === subscription['plan'].nickname,
      );

      const subscriptionValue = subscriptionWithZeroAmount ? 'active' : subscription.status;
      let subscriptionStatus = 'active';
      try {
        subscriptionStatus = Array.isArray(subscriptionValue) ? subscriptionValue[0] : 'active';
      } catch {
        this.logger.error(`Failed to get subscription status for user ${bid}`);
      }
      if (isTrial) {
        subscriptionStatus = 'trial';
      }

      await this.businessService.updateSubscriptions(
        bid,
        subscription.customer as string,
        subscription.id,
        subscriptionPlan,
        subscriptionStatus,
      );

      const user = await this.userService.getUserByBusinessId(bid);

      setTimeout(() => {
        migrateUsersToMailchimp(false, [user.email]);
      }, 10000);
    } catch (error) {
      this.logger.error(`Error updating business subscription: ${error?.message}`);
      throw new Error(error?.message);
    }
  }

  async updatePackageLimits(bid: string, customerId, subscriptionPlan?) {
    if (!subscriptionPlan) {
      const customer = await this.stripe.customers.retrieve(customerId);

      const activeSubscriptions = await this.stripe.subscriptions.list({
        customer: customer.id,
        status: 'active',
        expand: ['data.items'],
      });

      activeSubscriptions.data.forEach((subscription) => {
        const pricePlan = subscription.items?.data[0]?.price?.nickname;
        const plan = Object.keys(ALL_SUBSCRIPTION_PLANS).find(
          (key) => ALL_SUBSCRIPTION_PLANS[key] === pricePlan,
        );
        if (plan && PACKAGE_LIMITS[plan]) {
          this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS[plan].CREDITS);
        }
      });

      if (activeSubscriptions.data.length === 0) {
        this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS.FREE.CREDITS);
      }
    } else {
      if (PACKAGE_LIMITS[subscriptionPlan]) {
        this.creditTransactionService.addMonthlyCredits(
          bid,
          PACKAGE_LIMITS[subscriptionPlan].CREDITS,
        );
      } else {
        this.logger.debug('No subscription plan found');
        this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS.FREE.CREDITS);
      }
    }
  }

  async updateCustomPackageLimits(bid: string, subscriptionPlan, customPlan?) {
    try {
      if (customPlan && CUSTOM_PLAN_LIMITS[customPlan]) {
        const planPeriod = subscriptionPlan.split('_')[1];
        this.creditTransactionService.addMonthlyCredits(
          bid,
          CUSTOM_PLAN_LIMITS[customPlan][planPeriod].CREDITS,
        );
      } else if (PACKAGE_LIMITS[subscriptionPlan]) {
        this.creditTransactionService.addMonthlyCredits(
          bid,
          PACKAGE_LIMITS[subscriptionPlan].CREDITS,
        );
      } else {
        this.logger.log('No subscription plan found');
        this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS.FREE.CREDITS);
      }
    } catch (error) {
      this.logger.error(error.message);
      this.logger.error(error);
      this.creditTransactionService.addMonthlyCredits(bid, PACKAGE_LIMITS.FREE.CREDITS);
    }
  }

  async hasValidPaymentMethod(customerId: string): Promise<boolean> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);

      if ('invoice_settings' in customer && customer.invoice_settings.default_payment_method) {
        const defaultPaymentMethodId = customer.invoice_settings.default_payment_method as string;

        const paymentMethod = await this.stripe.paymentMethods.retrieve(defaultPaymentMethodId);

        return !!paymentMethod.type;
      }

      return false;
    } catch (error) {
      this.logger.error('Invalid payment method:', error);
      return false;
    }
  }

  private isLifetimeCoupon(couponCode: string) {
    return CUSTOM_PLANS.some((plan) => couponCode.startsWith(plan.couponPrefix));
  }

  private getCustomPlan(couponCode: string) {
    const matchedPlan = CUSTOM_PLANS.find((plan) => couponCode.startsWith(plan.couponPrefix));
    return matchedPlan ? matchedPlan.plan : '';
  }

  private isValidCouponForPlan(
    plan: string, // new plan
    couponCode: string,
    subscriptionPlan?: string, // current plan
  ): boolean {
    const isLifetimeCoupon = this.isLifetimeCoupon(couponCode);
    const couponPlan = couponCode.split('_')[isLifetimeCoupon ? 2 : 1] || '';
    const planCode = plan.split('_')[1];
    const planType = plan.split('_')[0];

    // All monthly and yearly plans valid for all coupon codes
    // as we already verified before and consider safeopt coupon as valid
    if (
      (planType === 'MONTHLY' ||
        planType === 'YEARLY' ||
        couponCode.startsWith('BLOGIFY_SAFEOPT')) &&
      !couponCode.startsWith('BLOGIFY_APPSUMO')
    ) {
      return true;
    }

    // validate appsumo codes for plans
    if (couponCode.startsWith('BLOGIFY_APPSUMO')) {
      if (
        subscriptionPlan === ALL_SUBSCRIPTION_PLANS.LIFETIME_BUSINESS &&
        plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_ENTERPRISE
      ) {
        return true;
      } else if (
        subscriptionPlan === ALL_SUBSCRIPTION_PLANS.LIFETIME_PREMIUM &&
        plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_BUSINESS
      ) {
        return true;
      } else if (
        subscriptionPlan === ALL_SUBSCRIPTION_PLANS.LIFETIME_BASIC &&
        plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_PREMIUM
      ) {
        return true;
      } else if (
        subscriptionPlan === ALL_SUBSCRIPTION_PLANS.LIFETIME_LITE &&
        plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_BASIC
      ) {
        return true;
      } else if (
        (subscriptionPlan === ALL_SUBSCRIPTION_PLANS.FREE ||
          subscriptionPlan === ALL_SUBSCRIPTION_PLANS.NOPLAN) &&
        (plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_LITE ||
          plan === ALL_SUBSCRIPTION_PLANS.LIFETIME_BASIC)
      ) {
        return true;
      } else {
        return false;
      }
    }

    const isExcludedPlan = couponPlan.length === 2;
    if (isExcludedPlan) {
      return planCode !== 'STARTER';
    }
    return couponPlan === planCode;
  }

  async verifyCoupon(coupon: string, price?: number) {
    const stripeCoupon = await this.stripe.coupons.retrieve(coupon);

    if (!stripeCoupon?.valid) {
      throw new Error('Invalid coupon code.');
    }

    const discount = stripeCoupon.amount_off || stripeCoupon.percent_off;
    return {
      ...(price ? { priceAfterDiscount: getPriceAfterDiscount(price, discount) } : {}),
      isPercentDiscount: !!stripeCoupon.percent_off,
      discount,
    };
  }
}
