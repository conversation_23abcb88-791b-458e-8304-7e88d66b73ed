import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { PushSubscription } from 'web-push';
import type { Notification } from './notification.model';

import { Controller, UseGuards, Headers, Query, Body, Post, Get, Req } from '@nestjs/common';

import { NotificationCreateDto } from './dto/notification-create.dto';
import { Roles, RolesGuard } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { Auth } from '@/auth/guards/auth.guard';

import { NotificationService } from './notification.service';

@Controller('notifications')
export class NotificationController extends CrudController<
  Notification,
  NotificationCreateDto,
  any
> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly notificationService: NotificationService) {
    super(notificationService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<Notification>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = {
        $or: [{ business: String(bid) }, { business: null }],
      };
    }
    return this.notificationService.findAll(query);
  }

  @Post('push/subscription')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async savePushNotificationSubscription(
    @Req() { uid }: AuthenticatedRequest,
    @Body() body: PushSubscription,
  ): Promise<{ success: boolean }> {
    await this.notificationService.savePushNotificationSubscription(uid, body);
    return { success: true };
  }
}
