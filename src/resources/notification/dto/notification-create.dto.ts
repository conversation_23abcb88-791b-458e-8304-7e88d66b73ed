import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class NotificationCreateDto {
  @ApiProperty()
  @IsString()
  title!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  actionTitle?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  actionLink?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  business?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  user?: string;
}
