import type { NotificationCreateDto } from './dto/notification-create.dto';
import type { PushSubscription } from 'web-push';
import type { YoutubeContent } from '@/youtube/youtube-content/youtube-content.model';
import type { Request } from '@/auth/interfaces/authenticated-request.interface';

import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { UserDocument, User } from '@/user/user.model';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { CrudService } from '@/crud/crud.service';

import { Notification, NotificationDocument } from './notification.model';
import { PushNotificationService } from './push-notification.service';

@Injectable()
export class NotificationService extends CrudService<Notification> {
  constructor(
    @InjectModel(Notification.name) private readonly notification: Model<NotificationDocument>,
    @InjectModel(User.name) private readonly user: Model<UserDocument>,
    private readonly pushNotificationService: PushNotificationService,
    private readonly gatewayService: GatewayService,
  ) {
    super(notification);
  }

  async create(body: NotificationCreateDto) {
    const createdNotification = await super.create(body);

    this.gatewayService.sendNotification(body.user, createdNotification);
    if (body.user) {
      this.pushNotificationService.sendNotification(body.user, createdNotification);
    } else {
      this.pushNotificationService.sendNotifications(createdNotification);
    }

    return createdNotification;
  }

  async savePushNotificationSubscription(
    _id: string,
    pushNotificationSubscription: PushSubscription,
  ) {
    const user = await this.user.findById(_id).select('pushNotificationSubscriptions');
    const subscriptions = [...user.pushNotificationSubscriptions];
    if (
      user.pushNotificationSubscriptions.findIndex(
        (pns) => pns.endpoint === pushNotificationSubscription.endpoint,
      ) < 0
    ) {
      subscriptions.push(pushNotificationSubscription);
      user.pushNotificationSubscriptions = subscriptions;
      await user.save();
    }
  }

  async sendYouTubeContentUpdate(
    { uid }: Partial<Request>,
    content: YoutubeContent,
    notification: NotificationCreateDto,
  ) {
    const createdNotification = await super.create(notification);

    this.gatewayService.sendNotification(uid, createdNotification);
    this.gatewayService.sendYouTubeContentUpdate(uid, content);

    return createdNotification;
  }
}
