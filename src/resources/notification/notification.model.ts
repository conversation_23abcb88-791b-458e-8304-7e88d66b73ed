import type { BusinessDocument } from '@business/business.model';
import type { UserDocument } from '@user/user.model';
import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

@Schema({ timestamps: { createdAt: true } })
export class Notification {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop()
  actionTitle: string;

  @Prop()
  actionLink: string;

  @Prop({ type: ObjectId, ref: 'Business' })
  business: BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User' })
  user: UserDocument;

  @Prop()
  deleted: boolean;
}

export type NotificationDocument = Notification & Document;

export const NotificationSchema = SchemaFactory.createForClass(Notification);
