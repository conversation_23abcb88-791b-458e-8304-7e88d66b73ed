import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { UserSchema, User } from '@/user/user.model';

import { Notification, NotificationSchema } from './notification.model';
import { WebPushNotificationService } from './web-push-notification.service';
import { PushNotificationService } from './push-notification.service';
import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Notification.name, schema: NotificationSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  controllers: [NotificationController],
  providers: [
    { provide: PushNotificationService, useExisting: WebPushNotificationService },
    WebPushNotificationService,
    NotificationService,
  ],
  exports: [NotificationService, MongooseModule],
})
export class NotificationModule {}
