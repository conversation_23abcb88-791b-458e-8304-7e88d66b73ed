import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import WebPush from 'web-push';

import { UserDocument, User } from '@/user/user.model';
import config from '@/common/configs/config';

import { PushNotificationService } from './push-notification.service';
import { Notification } from './notification.model';

@Injectable()
export class WebPushNotificationService implements PushNotificationService {
  constructor(@InjectModel(User.name) private readonly user: Model<UserDocument>) {
    WebPush.setVapidDetails(
      'mailto:<EMAIL>',
      config().vapid.publicKey,
      config().vapid.privateKey,
    );
  }

  async sendNotification(userId: string, notification: Notification) {
    const user = await this.user.findById(userId).select('pushNotificationSubscriptions');
    await this.sendNotificationToUser(user, notification);
  }

  async sendNotifications(notification: Notification) {
    const users = await this.user
      .find({ pushNotificationSubscriptions: { $exists: true, $not: { $size: 0 } } })
      .select('pushNotificationSubscriptions');

    for (const user of users) {
      await this.sendNotificationToUser(user, notification);
    }
  }

  private async sendNotificationToUser(user: UserDocument, notification: Notification) {
    if (!user.pushNotificationSubscriptions?.length) return;

    const subscriptions = [];
    for (const subscription of user.pushNotificationSubscriptions) {
      try {
        await WebPush.sendNotification(subscription, JSON.stringify(notification));
        subscriptions.push(subscription);
      } catch (e) {
        console.error('Error sending push notification.', e.message);
      }
    }

    user.pushNotificationSubscriptions = subscriptions;
    await user.save();
  }
}
