import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { BlogCategorySchema, BlogCategory } from './blog-category.model';
import { BlogCategoryController } from './blog-category.controller';
import { BlogCategoryService } from './blog-category.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: BlogCategory.name, schema: BlogCategorySchema }])],
  controllers: [BlogCategoryController],
  providers: [BlogCategoryService],
  exports: [BlogCategoryService, MongooseModule],
})
export class BlogCategoryModule {}
