import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongoSchema } from 'mongoose';

import { BaseModel } from '@/resources/base/base.model';
import { Business } from '@/business/business.model';
import { Website } from '@/resources/website/website.model';

@Schema({ collection: 'blog-categories', timestamps: true })
export class BlogCategory extends BaseModel {
  @Prop({ required: true })
  name: string;

  @Prop({ type: MongoSchema.Types.ObjectId, ref: 'Business', required: true })
  business: Relation<Business>;

  @Prop({ type: MongoSchema.Types.ObjectId, ref: 'Website', required: true })
  website: Relation<Website>;
}

export type BlogCategoryDocument = BlogCategory & Document;

export const BlogCategorySchema = SchemaFactory.createForClass(BlogCategory);
