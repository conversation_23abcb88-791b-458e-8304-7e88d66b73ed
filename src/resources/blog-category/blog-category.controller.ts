import type { ControllerOptions } from '@/resources/base/base.types';

import { BaseController } from '@/resources/base/base.controller';

import { CreateBlogCategoryDto } from './dto/create-blog-category.dto';
import { UpdateBlogCategoryDto } from './dto/update-blog-category.dto';
import { BlogCategoryService } from './blog-category.service';
import { BlogCategory } from './blog-category.model';

const options: Partial<ControllerOptions> = {
  show: { enabled: false },
};

export class BlogCategoryController extends BaseController<
  BlogCategory,
  typeof BlogCategoryService,
  CreateBlogCategoryDto,
  UpdateBlogCategoryDto
>({
  name: 'BlogCategory',
  path: 'blog-categories',
  service: BlogCategoryService,
  options,
}) {}
