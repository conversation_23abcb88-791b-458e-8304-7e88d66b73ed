import type { FilterQuery as RootFilterQuery } from 'mongoose';
import type { ControllerOptions, CrudQuery } from './base.types';

import { ObjectId } from 'mongodb';
import { Types } from 'mongoose';

import { DEFAULT_CONTROLLER_OPTIONS } from './base.constants';

const recursivelyCastObjectIds = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(recursivelyCastObjectIds);
  }

  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      const val = obj[key];

      // Special case: operators like $eq, $in, etc.
      if (typeof val === 'object') {
        obj[key] = recursivelyCastObjectIds(val);
      } else if (ObjectId.isValid(val)) {
        obj[key] = new Types.ObjectId(val);
      }
    }
  }

  return obj;
};

const parseFilter = <Resource>(s = ''): RootFilterQuery<Resource> => {
  const { $and, $or } = JSON.parse(s || '{}');

  return {
    ...($and?.length ? { $and: recursivelyCastObjectIds($and) } : {}),
    $or: recursivelyCastObjectIds([{ deleted: false }, { deleted: null }, ...($or || [])]),
  };
};

const parseSort = <Resource>(sorts: string[] = ['createdAt,DESC']): MongoSort<Resource> => {
  return sorts.reduce((_sort: MongoSort<Resource>, _s: string) => {
    const [field, order] = _s.split(',');
    _sort[field] = order === 'DESC' ? -1 : 1;
    return _sort;
  }, {} as MongoSort<Resource>);
};

export const parseQuery = <Resource>(query: CrudQuery): MongoParams<Resource> => {
  const { join, page, s } = query;

  const filter = parseFilter<Resource>(s);
  const sort = parseSort<Resource>(query.sort);

  // Populate
  const populate = join?.length ? join : [];

  // Pagination
  const limit = +query.limit || 10;
  const skip = limit * ((+page || 1) - 1);

  return {
    filter,
    sort,
    populate,
    limit,
    skip,
  };
};

export const getOptions = (options: Partial<ControllerOptions> = {}): ControllerOptions => ({
  ...DEFAULT_CONTROLLER_OPTIONS,
  ...options,
  list: { ...DEFAULT_CONTROLLER_OPTIONS.list, ...(options.list || {}) },
  show: { ...DEFAULT_CONTROLLER_OPTIONS.show, ...(options.show || {}) },
  create: { ...DEFAULT_CONTROLLER_OPTIONS.create, ...(options.create || {}) },
  update: { ...DEFAULT_CONTROLLER_OPTIONS.update, ...(options.update || {}) },
  remove: { ...DEFAULT_CONTROLLER_OPTIONS.remove, ...(options.remove || {}) },
});
