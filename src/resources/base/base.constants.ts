import type { ControllerOptions } from './base.types';

import { RolesGuard } from '@/auth/guards/roles.guard';
import { UserRole } from '@/user/user.model';
import { Auth } from '@/auth/guards/auth.guard';

export const DEFAULT_CONTROLLER_OPTIONS: ControllerOptions = {
  isTeamResource: true,
  list: {
    enabled: true,
    guards: [Auth, RolesGuard],
    roles: [UserRole.admin],
    cacheEnabled: true,
  },
  show: {
    enabled: true,
    guards: [Auth, RolesGuard],
    roles: [UserRole.admin],
    cacheEnabled: true,
  },
  create: {
    enabled: true,
    guards: [Auth, RolesGuard],
    roles: [UserRole.admin],
  },
  update: {
    enabled: true,
    guards: [Auth, RolesGuard],
    roles: [UserRole.admin],
  },
  remove: {
    enabled: true,
    guards: [Auth, RolesGuard],
    roles: [UserRole.admin],
  },
};
