import type { BaseServiceInterface, ControllerOptions } from './base.types';
import type { User as UserType } from '@/user/user.model';
import type { InjectionToken } from '@nestjs/common';
import type { Business } from '@/business/business.model';

import {
  ForbiddenException,
  NotFoundException,
  UseInterceptors,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Headers,
  Inject,
  Delete,
  mixin,
  Param,
  Patch,
  Query,
  Body,
  Post,
  Get,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

import { NoopInterceptor } from '@/common/interceptors/noop.interceptor';
import { Roles } from '@/auth/guards/roles.guard';
import { User } from '@/common/decorators/user.decorator';
import config from '@/common/configs/config';

import { getOptions, parseQuery } from './base.utils';
import { QueryDto } from './dto/query.dto';

export function BaseController<Resource, Service, CreateDto, UpdateDto>({
  name,
  path,
  service,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  CreateResource = null,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  UpdateResource = null,
  options,
}: {
  name: string;
  path: string;
  service: Service;
  CreateResource?: CreateDto;
  UpdateResource?: UpdateDto;
  options?: Partial<ControllerOptions>;
}) {
  const { isTeamResource, list, show, create, update, remove } = getOptions(options);

  @Controller(path)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  class BaseControllerMixin {
    @Inject(service as unknown as InjectionToken) baseService: BaseServiceInterface<Resource>;

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} list${list.enabled ? '' : '; Disabled ❌'}` })
    @Get()
    @CacheTTL(config().isProd ? 15 * 60 * 1000 : 1)
    @UseInterceptors(list.cacheEnabled && !isTeamResource ? CacheInterceptor : NoopInterceptor)
    @Roles(...(list.roles || []))
    @UseGuards(...(list.guards || []))
    async list(
      @Query() query: QueryDto,
      @User() user: UserType,
      @Headers() headers: { 'x-app-name': 'admin' },
    ) {
      if (!list.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const { filter, ...filterOptions } = parseQuery<Resource>(query);

      const isRequestFromAdmin = user?.role === 'superadmin' && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.business) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.business = (user.business as Business)?._id || user.business;
      }

      const [data, total] = await Promise.all([
        this.baseService.findMany(filter, filterOptions),
        this.baseService.count(filter),
      ]);

      return { data, total };
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} show${show.enabled ? '' : '; Disabled ❌'}` })
    @Get(':id')
    @CacheTTL(config().isProd ? 15 * 60 * 1000 : 1)
    @UseInterceptors(show.cacheEnabled && !isTeamResource ? CacheInterceptor : NoopInterceptor)
    @Roles(...(show.roles || []))
    @UseGuards(...(show.guards || []))
    async show(
      @Query() query: QueryDto,
      @Param('id') id: string,
      @User() user: UserType,
      @Headers() headers: { 'x-app-name': 'admin' },
    ) {
      if (!show.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const { filter, populate } = parseQuery<Resource>(query);

      const isRequestFromAdmin = user?.role === 'superadmin' && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.business) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.business = (user.business as Business)?._id || user.business;
      }

      const result = await this.baseService.findById(id, { filter, populate });
      if (!result) {
        throw new NotFoundException('Not Found');
      }

      return result;
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} create${create.enabled ? '' : '; Disabled ❌'}` })
    @Post()
    @Roles(...(create.roles || []))
    @UseGuards(...(create.guards || []))
    create(
      // @ts-expect-error for now
      @Body() data: CreateResource,
      @User() user: UserType,
      @Headers() headers: { 'x-app-name': 'admin' },
    ) {
      if (!create.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const isRequestFromAdmin = user?.role === 'superadmin' && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.business) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        data.business = (user.business as Business)?._id || user.business;
      }

      return this.baseService.create(data);
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} update${update.enabled ? '' : '; Disabled ❌'}` })
    @Patch(':id')
    @Roles(...(update.roles || []))
    @UseGuards(...(update.guards || []))
    update(
      @Param() { id }: { id: string },
      // @ts-expect-error for now
      @Body() data: UpdateResource,
      @User() user: UserType,
      @Headers() headers: { 'x-app-name': 'admin' },
    ) {
      if (!update.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const filter: Record<string, string | unknown> = { _id: id };
      const isRequestFromAdmin = user?.role === 'superadmin' && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.business) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.business = (user.business as Business)?._id || user.business;
      }

      return this.baseService.updateOne(filter, data);
    }

    @ApiBearerAuth()
    @ApiTags(name)
    @ApiOperation({ summary: `${name} delete${remove.enabled ? '' : '; Disabled ❌'}` })
    @Delete(':id')
    @Roles(...(remove.roles || []))
    @UseGuards(...(remove.guards || []))
    delete(
      @Param() { id }: { id: string },
      @User() user: UserType,
      @Headers() headers: { 'x-app-name': 'admin' },
    ) {
      if (!remove.enabled) {
        throw new ForbiddenException('Forbidden Resource');
      }

      const filter: Record<string, string | unknown> = { _id: id };
      const isRequestFromAdmin = user?.role === 'superadmin' && headers['x-app-name'] === 'admin';
      if (!isRequestFromAdmin && isTeamResource) {
        if (!user?.business) {
          throw new ForbiddenException('You do not have access to this resource.');
        }
        filter.business = (user.business as Business)?._id || user.business;
      }

      return this.baseService.deleteOne(filter);
    }
  }

  const controller = mixin(BaseControllerMixin);
  return controller;
}
