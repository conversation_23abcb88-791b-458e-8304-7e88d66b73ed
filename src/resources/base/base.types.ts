import type { FilterQuery as RootFilterQuery } from 'mongoose';
import type { CanActivate } from '@nestjs/common';
import type { UserRole } from '@/user/user.model';

export interface BaseServiceInterface<Resource> {
  findMany(
    filter: RootFilterQuery<Resource>,
    options?: Omit<MongoParams<Resource>, 'filter'>,
  ): Promise<Resource[]>;

  findOne(
    filter: RootFilterQuery<Resource>,
    options?: Pick<MongoParams<Resource>, 'sort' | 'populate'>,
  ): Promise<Resource | null>;

  findById(
    id: string,
    options?: Pick<MongoParams<Resource>, 'filter' | 'populate'>,
  ): Promise<Resource>;

  count(filter: RootFilterQuery<Resource>): Promise<number>;

  create(data: Resource): Promise<Resource>;

  update(id: string, data: Partial<Resource>): Promise<Resource>;
  updateOne(filter: RootFilterQuery<Resource>, data: Partial<Resource>): Promise<Resource>;

  delete(id: string): Promise<{ success: boolean }>;
  deleteOne(filter: RootFilterQuery<Resource>): Promise<{ success: boolean }>;

  destroy(id: string): Promise<{ success: boolean }>;
}

type ControllerRouteOptions = {
  enabled?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  guards?: (CanActivate | Function)[];
  roles?: UserRole[];
};

export type ControllerOptions = {
  isTeamResource?: boolean;
  list: ControllerRouteOptions & { cacheEnabled?: boolean };
  show: ControllerRouteOptions & { cacheEnabled?: boolean };
  create: ControllerRouteOptions;
  update: ControllerRouteOptions;
  remove: ControllerRouteOptions;
};

interface RefineQuery {
  join?: string[];
  sort?: string[];
  limit?: number;
  page?: number;
  s?: string;
}

export type CrudQuery = RefineQuery;
