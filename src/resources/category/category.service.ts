import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { Category, CategoryDocument } from './category.model';

@Injectable()
export class CategoryService extends CrudService<Category> {
  // private readonly logger = new Logger(CategoryService.name);

  constructor(@InjectModel(Category.name) private readonly category: Model<CategoryDocument>) {
    super(category);
  }
}
