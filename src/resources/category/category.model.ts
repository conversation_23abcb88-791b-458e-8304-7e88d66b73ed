import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';

@Schema()
export class Category {
  @Prop({ required: true })
  name: string;

  @Prop({})
  description: string;

  @Prop({ required: true })
  iconUrl: string;

  @Prop({ required: true })
  colorScheme: string;

  @Prop()
  deleted: boolean;
}

export type CategoryDocument = Category & Document;

export const CategorySchema = SchemaFactory.createForClass(Category);
