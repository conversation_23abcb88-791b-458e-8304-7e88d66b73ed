import type { Category } from './category.model';

import { Controller } from '@nestjs/common';

import { CrudController } from 'src/crud/crud.controller';

import { CategoryCreateDto } from './dto/category-create.dto';
import { CategoryService } from './category.service';

@Controller('categories')
export class CategoryController extends CrudController<Category, CategoryCreateDto, any> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly categoryService: CategoryService) {
    super(categoryService);
  }
}
