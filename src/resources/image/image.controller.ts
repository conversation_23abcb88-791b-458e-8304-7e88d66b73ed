import type { ListResponse, CrudQuery } from '@/crud/crud.interface';

import {
  NotFoundException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Headers,
  Delete,
  Query,
  Patch,
  Param,
  Post,
  Body,
  Req,
  Get,
} from '@nestjs/common';

import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { Roles, RolesGuard } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';

import { GenerateImageDto } from './dto/generate-image.dto';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';
import { ImageService } from './image.service';
import { ImageType } from './image.enums';
import { Image } from './image.model';

@Controller('images')
@UsePipes(new ValidationPipe())
export class ImageController extends CrudController<Image, CreateImageDto, UpdateImageDto> {
  constructor(private readonly imageService: ImageService) {
    super(imageService);
  }

  @Get()
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<Image>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid: bid };
    }
    if (query['type']) {
      query.filter['type'] = query['type'];
    }
    if (query['isFavorite']) {
      query.filter['isFavorite'] = true;
    }
    return this.imageService.findAll(query);
  }

  @Get('counts')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async counts(@Req() { bid }: AuthenticatedRequest) {
    return this.imageService.counts(bid);
  }

  @Post()
  @UseGuards(Auth, RolesGuard)
  async create(
    @Body() body: CreateImageDto,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Image> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.bid = bid;
      body.uid = uid;
      body.type = ImageType.Uploaded;
    }
    return super.create(body);
  }

  @Patch(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async update(
    @Param('id') id: string,
    @Body() body: UpdateImageDto,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Image> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.bid = bid;
      body.uid = uid;
    }
    return super.update(id, body);
  }

  @Delete(':id')
  @UseGuards(Auth, RolesGuard)
  async remove(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<{ success: boolean }> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid };
    }
    const image = await this.imageService.findOneById(id, query);
    if (!image) {
      throw new NotFoundException('Image not found.');
    }

    return super.remove(id);
  }

  @Post('generate')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async generateImage(@Req() { uid, bid }: AuthenticatedRequest, @Body() body: GenerateImageDto) {
    console.log(1);
    return await this.imageService.generateAndUploadImage({ uid, bid }, body);
  }
}
