import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Image {
  @Prop({ type: String })
  bid: string;

  @Prop({ type: String })
  uid: string;

  @Prop({ type: String })
  url: string;

  @Prop({ type: String })
  thumbnailUrl?: string;

  @Prop({ type: String })
  downloadUrl?: string;

  @Prop({ type: String, enum: ['generated', 'uploaded'], default: 'generated' })
  type: 'generated' | 'uploaded';

  @Prop({ type: String })
  alt: string;

  @Prop({ type: String })
  prompt: string;

  @Prop({ type: String })
  copyright: string;

  @Prop({ type: Number })
  fileSize: number;

  @Prop({ type: String })
  fileType: string;

  @Prop({ type: String })
  resolution: string;

  @Prop({ type: Boolean, default: false })
  isFavorite: boolean;

  @Prop()
  deleted: boolean;
}

export type ImagesDocument = Image & Document;

export const ImageSchema = SchemaFactory.createForClass(Image);
