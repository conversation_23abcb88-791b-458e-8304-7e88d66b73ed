import type { ImageQuality } from './image.enums';

import { Injectable } from '@nestjs/common';

import { ImageGenerationResponse, BaseImageProvider } from './base.image.provider';
import { ImageProvider, ImageSize } from './image.enums';
import { GenerateImageDto } from './dto/generate-image.dto';
import { AiSdk, ProviderType } from '@/llm/sdk/AiSdk';

@Injectable()
export class DalleImageProvider extends BaseImageProvider {
  constructor(private readonly aiSdk: AiSdk) {
    super(ImageProvider.DALLE);
  }

  async generateImage(input: GenerateImageDto): Promise<ImageGenerationResponse> {
    try {
      const size = this.validateSize(input.size as ImageSize);
      const defaultModel = 'gpt-image-1';
      const noTextPrompt = 'Strictly do not use any text in the image.'; // DALL-E can't generate correct text

      const result = await this.aiSdk.generateImage({
        provider: (input.provider || 'openai') as ProviderType,
        model: input.model || defaultModel,
        prompt: input.prompt + noTextPrompt,
        size,
        quality: (input.quality || 'standard') as ImageQuality,
        style: input.model === 'dall-e-3' ? 'vivid' : undefined,
        n: 1,
      });

      if (!result.images || result.images.length === 0) {
        throw new Error('No images generated');
      }

      return {
        url: result.images[0],
      };
    } catch (error) {
      console.error('Dall-E Error:', error);
      throw new Error(error.message || 'Failed to generate image with DALL-E');
    }
  }

  private validateSize(size?: ImageSize): ImageSize {
    if (Object.values(ImageSize).includes(size)) {
      return size;
    }
    return ImageSize['1024x1024'];
  }
}
