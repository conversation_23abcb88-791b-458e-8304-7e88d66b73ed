import { IsBoolean, IsOptional, IsString, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateImageDto {
  @ApiProperty()
  @IsOptional()
  @IsUrl()
  bid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  uid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  alt!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  copyright!: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isFavorite!: boolean;
}
