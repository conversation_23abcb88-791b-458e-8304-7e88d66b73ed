import type { ImageGenerateParams } from 'openai/resources';

import { IsOptional, IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { ImageProvider, ImageQuality, ImageModel, ImageStyle, ImageSize } from '../image.enums';

export class GenerateImageDto {
  @ApiProperty()
  @IsString()
  prompt: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageStyle)
  style?: ImageStyle | string = 'Anime';

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageProvider)
  provider?: ImageProvider | string = 'dalle';

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageModel)
  model?: ImageModel | string = 'gpt-image-1';

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageSize)
  size?: ImageSize | string = '1024x1024';

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageQuality)
  quality?: ImageGenerateParams['quality'];

  @ApiProperty()
  @IsOptional()
  @IsString()
  purpose?: string;
}
