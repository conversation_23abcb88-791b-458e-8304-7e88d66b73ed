import { IsOptional, IsString, IsEnum, IsInt, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { ImageType } from '../image.enums';

export class CreateImageDto {
  @ApiProperty()
  @IsOptional()
  @IsUrl()
  bid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  uid!: string;

  @ApiProperty()
  @IsUrl()
  url: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(ImageType)
  @IsString()
  type!: ImageType | string;

  @ApiProperty()
  @IsString()
  alt: string;

  @ApiProperty()
  @IsInt()
  fileSize: number;

  @ApiProperty()
  @IsString()
  fileType: string;

  @ApiProperty()
  @IsString()
  resolution: string;
}
