import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import axios from 'axios';

import { ImageGenerationResponse, BaseImageProvider } from './base.image.provider';
import { GenerateImageDto } from './dto/generate-image.dto';
import { ImageProvider } from './image.enums';

@Injectable()
export class StableDiffusionProvider extends BaseImageProvider {
  private readonly endpointId: string;
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(configService: ConfigService) {
    super(ImageProvider.STABLE_DIFFUSION);
    this.endpointId = configService.get<string>('RUNPOD_ENDPOINT_ID');
    this.baseUrl = `https://api.runpod.ai/v2/${this.endpointId}/runsync`;
    this.apiKey = configService.get<string>('RUNPOD_API_KEY');
  }

  async generateImage(input: GenerateImageDto): Promise<ImageGenerationResponse> {
    const { prompt } = input;
    const data = {
      input: {
        prompt,
      },
    };

    const headers = {
      Authorization: `Bearer ${this.apiKey}`,
    };

    const response = await axios.post(this.baseUrl, data, { headers });

    if (response.status != 200) {
      throw new Error(`Failed to generate image: ${response.statusText}`);
    }

    const result = response.data;
    if (!result.output && !result.output.image_url && !result.output.image) {
      throw new Error('No images generated');
    }

    return {
      url: result.output.image || result.output.image_url || result.output[0]?.image,
    };
  }
}
