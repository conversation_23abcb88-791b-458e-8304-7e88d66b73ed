import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { S3Service } from '@/common/services/s3.service';
import { AiSdk } from '@/llm/sdk/AiSdk';

import { CreditTransactionModule } from '../credit-transaction/credit-transaction.module';
import { DalleImageProvider } from './dalle.image.provider';
import { GeminiImageProvider } from './gemini.image.provider';
import { ImageController } from './image.controller';
import { Image, ImageSchema } from './image.model';
import { ImageService } from './image.service';
import { StableDiffusionProvider } from './stablediffusion.provider';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Image.name, schema: ImageSchema }]),
    CreditTransactionModule,
  ],
  providers: [
    {
      provide: AiSdk,
      useFactory: () =>
        new AiSdk({
          provider: 'openai',
          model: 'gpt-4o',
          identifierName: 'image-generation',
          identifierValue: 'default',
        }),
    },
    ImageService,
    DalleImageProvider,
    GeminiImageProvider,
    StableDiffusionProvider,
    S3Service,
  ],
  controllers: [ImageController],
  exports: [ImageService],
})
export class ImageModule {}
