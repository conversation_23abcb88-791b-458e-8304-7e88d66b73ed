import type { Model } from 'mongoose';

import { InternalServerErrorException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { v4 as uuid } from 'uuid';
import { outdent } from 'outdent';
import axios from 'axios';
import sharp from 'sharp';

// import { experimental_generateImage as generateImage } from 'ai';
// import { vertex } from '@ai-sdk/google-vertex';

import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { OpenAiProvider } from '@/llm/index';
import { BaseProvider } from '@/llm/providers/base.provider';
import { CrudService } from '@/crud/crud.service';
import { isValidUrl } from '@/common/utils/url';
import { S3Service } from '@/common/services/s3.service';

import { ImageGenerationResponse, BaseImageProvider } from './base.image.provider';
import { StableDiffusionProvider } from './stablediffusion.provider';
import { ImagesDocument, Image } from './image.model';
import { DalleImageProvider } from './dalle.image.provider';
import { GenerateImageDto } from './dto/generate-image.dto';
import { ImageContent } from './image.enums';

type UserIds = { bid: string; uid: string };

const getImageContentName = ({ model, size, quality }: GenerateImageDto): ImageContent => {
  return `${model}_${size}_${quality}` as ImageContent;
};

@Injectable()
export class ImageService extends CrudService<Image> {
  private readonly logger = new Logger(this.constructor.name);
  private providers: Map<string, BaseImageProvider> = new Map();
  private llmProvider: BaseProvider;

  constructor(
    @InjectModel(Image.name) private readonly imageModel: Model<Image>,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly stableDiffusionProvider: StableDiffusionProvider,
    private readonly dalleProvider: DalleImageProvider,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    super(imageModel);
    this.providers.set(this.dalleProvider.providerName, this.dalleProvider);
    this.providers.set('google', this.dalleProvider);
    this.providers.set('openai', this.dalleProvider);
    this.providers.set(this.stableDiffusionProvider.providerName, this.stableDiffusionProvider);
    this.llmProvider = new OpenAiProvider(this.configService.get('OPENAI_API_KEY'));
  }

  async counts(bid: string) {
    const results = await this.imageModel.aggregate([
      { $match: { bid: String(bid), deleted: { $ne: true } } },
      { $group: { _id: '$type', count: { $sum: 1 } } },
    ]);

    const favorites = await this.imageModel.countDocuments({ bid, isFavorite: true });

    const generated = results.find((r) => r._id === 'generated')?.count || 0;
    const uploaded = results.find((r) => r._id === 'uploaded')?.count || 0;

    return {
      'All Images': generated + uploaded,
      'AI Generated': generated,
      Uploaded: uploaded,
      Favorites: favorites,
    };
  }

  async generateAndUploadImage({ uid, bid }: UserIds, imgRequest: GenerateImageDto) {
    try {
      // console.log('GOOGLE_VERTEX_PROJECT', process.env.GOOGLE_VERTEX_PROJECT);
      // console.log('GOOGLE_VERTEX_LOCATION2', process.env.GOOGLE_VERTEX_LOCATION2);
      // const { image } = await generateImage({
      //   model: vertex.image('imagen-3.0-generate-002'),
      //   prompt: 'A futuristic cityscape at sunset',
      //   aspectRatio: '16:9',
      //   // providerOptions: {  },
      // });
      // console.log('image', image);
      console.log(2);
      const response = await this.generateImage(imgRequest);
      // Upload image to S3
      const [imageUrl, downloadUrl, thumbnailUrl] = await this.uploadImageToS3(response.url);
      // Save image url to DB
      const img = await this.saveImageToDb(
        { bid, uid },
        { ...imgRequest, url: imageUrl, downloadUrl, thumbnailUrl },
      );
      // Charge Credits
      try {
        await this.creditTransactionService.deductContentCost(
          { bid, uid },
          {
            contentType: getImageContentName(imgRequest),
            contentIds: [img._id],
            status: 'confirmed',
          },
        );
      } catch (e) {
        this.logger.error(e);
      }

      // const response = await this.generateImage(imgRequest);
      // // Upload image to S3
      // const [imageUrl, downloadUrl, thumbnailUrl] = await this.uploadImageToS3(response.url);
      // // Save image url to DB
      // const img = await this.saveImageToDb(
      //   { bid, uid },
      //   { ...imgRequest, url: imageUrl, downloadUrl, thumbnailUrl },
      // );
      // // Charge Credits
      // try {
      //   await this.creditTransactionService.deductContentCost(
      //     { bid, uid },
      //     {
      //       contentType: getImageContentName(imgRequest),
      //       contentIds: [img._id],
      //       status: 'confirmed',
      //     },
      //   );
      // } catch (e) {
      //   this.logger.error(e);
      // }

      return { url: '' };
    } catch (error) {
      console.error('Error generating image:', error);
      throw new InternalServerErrorException(error.message || 'Failed to generate image');
    }
  }

  private async generateImage(imgRequest: GenerateImageDto): Promise<ImageGenerationResponse> {
    const { provider, prompt, style } = imgRequest;
    console.log(3);
    const imageProvider = this.providers.get(provider);
    if (!provider) {
      console.log(4);
      throw new Error(`Provider ${provider} not registered`);
    }
    console.log(5);
    const imageGenerationPrompt = await this.generateImageDescriptionPrompt(prompt, style);
    console.log('imageGenerationPrompt', imageGenerationPrompt);
    console.log('imageProvider', imageProvider.providerName);

    return imageProvider.generateImage({ ...imgRequest, prompt: imageGenerationPrompt });
  }

  private async uploadImageToS3(
    dataUrl: string,
  ): Promise<[webpUrl: string, pngUrl: string, thumbnailUrl: string]> {
    let imageData: Buffer;

    const isUrl = isValidUrl(dataUrl);
    // Convert to buffer
    if (isUrl) {
      imageData = await this.fetchImageAsBuffer(dataUrl);
    } else {
      const base64String = dataUrl.startsWith('data:image/png;base64')
        ? dataUrl.split(',')[1]
        : dataUrl;
      imageData = Buffer.from(base64String, 'base64');
    }

    const webpBuffer = await sharp(imageData).webp({ quality: 80 }).toBuffer();
    const thumbnailBuffer = await this.transformToThumbnail(imageData);
    const name = uuid();
    const urls = await Promise.all(<const>[
      this.s3Service.uploadFile(webpBuffer, `${name}.webp`, 'ai'),
      this.s3Service.uploadFile(imageData, `${name}.png`, 'ai'),
      this.s3Service.uploadFile(thumbnailBuffer, `${name}-thumbnail.webp`, 'thumbnails'),
    ]);

    return urls;
  }

  private async saveImageToDb(
    { uid, bid }: UserIds,
    {
      url,
      downloadUrl,
      thumbnailUrl,
      size,
      prompt,
    }: GenerateImageDto & { url: string; downloadUrl: string; thumbnailUrl: string },
  ): Promise<ImagesDocument> {
    return this.imageModel.create({
      bid,
      uid,
      url,
      downloadUrl,
      thumbnailUrl,
      type: 'generated',
      alt: prompt,
      prompt,
      fileSize: '',
      fileType: 'type/webp',
      resolution: size,
    });
  }

  private transformToThumbnail = (imageData: Buffer) =>
    sharp(imageData)
      .resize(600, 600, {
        fit: 'inside',
        withoutEnlargement: true,
      })
      .webp({ quality: 80 })
      .toBuffer();

  convertToThumbnail = (url: URL): Promise<string> =>
    this.fetchImageAsBuffer(url.href)
      .then(this.transformToThumbnail)
      .then((webpBuffer) => this.s3Service.uploadFile(webpBuffer, `${uuid()}.webp`, 'thumbnails'));

  private async fetchImageAsBuffer(url: string): Promise<Buffer> {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const buffer = Buffer.from(response.data, 'binary');
      return buffer;
    } catch (error) {
      console.error('Error fetching the image:', error);
      throw error;
    }
  }

  private async generateImageDescriptionPrompt(prompt: string, style?: string): Promise<string> {
    const systemPrompt = outdent`You are an artist who can describe an image based on the
    user input. You never show front or incomplete face of any
    human. You always use illustration instead of real.
    You never misspell any word and if you do, you correct it or skip using texts.`;

    const stylePrompt = `using the ${style} style.`;
    const userPrompt = outdent`Generate a detailed prompt to generate an image based
    on the following prompt delimited by triple quotes.
    Make sure to analyze the prompt, get up to date information and explain the context in detail.
    Make sure the prompt is detailed, cover up all the context, clear and help to generate stunning image.
    prompt:
    """
    ${prompt}
    """
    ${style ? stylePrompt : ''}

    Return the final prompt without any additional information.`;

    const resp = await this.llmProvider.chatCompletion({
      systemPrompt,
      messages: [{ role: 'user', content: userPrompt }],
      model: 'gpt-4o',
    });

    return resp.message.content;
  }
}
