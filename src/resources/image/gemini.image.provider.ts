import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { BaseImageProvider, ImageGenerationResponse } from './base.image.provider';
import { ImageProvider } from './image.enums';
import { GenerateImageDto } from './dto/generate-image.dto';
import { AiSdk } from '@/llm/sdk/AiSdk';

@Injectable()
export class GeminiImageProvider extends BaseImageProvider {
  constructor(
    private readonly configService: ConfigService,
    private readonly aiSdk: AiSdk,
  ) {
    super(ImageProvider.GEMINI);
  }

  async generateImage(input: GenerateImageDto): Promise<ImageGenerationResponse> {
    try {
      // Convert size to format supported by Gemini
      const size = this.validateSize(input.size);
      const model = input.model || 'gemini-2.0-flash-preview-image-generation';

      const result = await this.aiSdk.generateImage({
        prompt: input.prompt,
        provider: 'google',
        model,
        size,
        n: 1,
      });

      if (!result || !result.images || result.images.length === 0) {
        throw new Error('No images generated');
      }

      return {
        url: result.images[0],
      };
    } catch (error) {
      console.error('Gemini Image Provider Error:', error);
      throw new Error(error.message || 'Failed to generate image with Gemini');
    }
  }

  private validateSize(size?: string): string {
    // Gemini supports specific sizes, default to 1024x1024 if invalid
    const supportedSizes = ['1024x1024', '1024x1792', '1792x1024'];

    if (size && supportedSizes.includes(size)) {
      return size;
    }

    return '1024x1024';
  }
}
