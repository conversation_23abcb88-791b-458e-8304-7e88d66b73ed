import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import type { CrudQuery } from 'src/crud/crud.interface';
import type { Product } from './product.model';

import {
  BadRequestException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Redirect,
  Query,
  Patch,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { CrudController } from 'src/crud/crud.controller';
import { BusinessGuard } from '@auth/guards/business.guard';
import { Auth } from '@auth/guards/auth.guard';
import config from '@common/configs/config';

import { ProductPurchaseCancelDto } from './dto/product-purchase-cancel.dto';
import { ProductAddonPurchaseDto } from './dto/product-addon-purchase.dto';
import { ProductCreateDto } from './dto/product-create.dto';
import { ProductService } from './product.service';

@ApiBearerAuth()
@ApiTags('Products')
@Controller('products')
@UsePipes(new ValidationPipe())
export class ProductController extends CrudController<Product, ProductCreateDto, any> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly productService: ProductService) {
    super(productService);
  }

  @Get('addons')
  @UseGuards(Auth)
  async getAddOns(@Req() { bid }: AuthenticatedRequest, @Query() query: CrudQuery) {
    return this.productService.findAllProductsByType(bid, 'addon', query);
  }

  @Post('addons/purchase')
  @UseGuards(Auth)
  async purchaseAddon(
    @Req() { bid, uid }: AuthenticatedRequest,
    @Body() { id, coupon }: ProductAddonPurchaseDto,
  ): Promise<{ clientSecret: string }> {
    return this.productService.purchaseAddon(id, coupon, { bid, uid });
  }

  @Get('addons/purchase/confirm')
  @UseGuards(QueryTokenGuard, Auth)
  @Redirect()
  async confirmPurchaseAddon(
    @Req() { bid, uid }: AuthenticatedRequest,
    @Query('addonId') id: string,
    @Query('redirect_status') redirectStatus: string,
    @Query('payment_intent') paymentIntentId: string,
    @Query('payment_intent_client_secret') paymentIntentClientSecret: string,
    @Query('setup_intent') setupIntentId: string,
    @Query('setup_intent_client_secret') setupIntentClientSecret: string,
  ): Promise<{ clientSecret: string; subscriptionRef: string } | any> {
    const paymentId = paymentIntentId || setupIntentId;
    const paymentSecret = paymentIntentClientSecret || setupIntentClientSecret;
    if (!(id && paymentId && paymentSecret && redirectStatus === 'succeeded')) {
      // await this.productService.cancelPurchaseAddon(bid, paymentId);
      throw new BadRequestException('Payment was not successful');
    }

    const baseRedirect = `${
      config().internalApps.blogifyClient.url
    }/dashboard/addons?addon=${id}&transaction=${paymentId}&success=`;

    try {
      const productUrl = await this.productService.confirmPurchaseAddon(id, paymentId, {
        bid,
        uid,
      });
      return { url: `${baseRedirect.replace('/dashboard/addons', productUrl)}true` };
    } catch (e) {
      return { url: `${baseRedirect}false&error=${e.message}` };
    }
  }

  @Patch('addons/purchase/cancel')
  @UseGuards(Auth, BusinessGuard)
  async cancelPurchaseAddon(
    @Body() { paymentRef, subscriptionRef }: ProductPurchaseCancelDto,
  ): Promise<{ success: boolean }> {
    return this.productService.cancelPurchaseAddon(paymentRef, subscriptionRef);
  }

  @Post('addons/deactivate')
  @UseGuards(Auth)
  async deactivateAddon(
    @Req() { bid, uid }: AuthenticatedRequest,
    @Body() { id, comment }: ProductAddonPurchaseDto & { comment: string },
  ): Promise<{ success: boolean }> {
    return this.productService.deactivateAddon(id, comment, { bid, uid });
  }

  @Get('packages')
  @UseGuards(Auth)
  async getPackages(@Req() { bid }: AuthenticatedRequest, @Query() query: CrudQuery) {
    return this.productService.findAllProductsByType(bid, 'package', query);
  }
}
