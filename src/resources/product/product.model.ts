import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';

export enum Addon {
  Snippet = 'Writing Snippets',
  YouTube = 'YouTube Connect',
  YouTubePro = 'YouTube Connect Pro',
}

@Schema({ versionKey: false })
export class Product {
  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  group: string;

  @Prop({ type: String })
  imageUrl: string;

  @Prop({ type: String })
  videoUrl: string;

  @Prop({ type: String })
  productUrl: string;

  @Prop({ type: String })
  learnMoreUrl: string;

  @Prop({ type: String, enum: ['USD'], required: true, default: 'USD' })
  currency: 'USD';

  @Prop({ type: Number, required: true, default: 0 })
  price: number;

  @Prop({ type: Number, min: 0, max: 100, default: 0 })
  discount: number;

  @Prop({ type: String, enum: ['month', 'year', 'lifetime'], required: true, default: 'month' })
  interval: 'month' | 'year' | 'lifetime';

  @Prop({ type: String, required: true })
  subscriptionId: string;

  @Prop({ type: Number })
  trialPeriod: number;

  @Prop({
    type: String,
    enum: ['subscription', 'one_time'],
    required: true,
    default: 'subscription',
  })
  paymentMode: 'subscription' | 'one_time';

  @Prop({ type: String, enum: ['package', 'addon'], required: true, default: 'package' })
  type: 'package' | 'addon';

  @Prop({
    type: Object,
    schema: {
      promptCreditPrice: Number,
      mediaCreditPrice: Number,
      promptCredit: Number,
      mediaCredit: Number,
    },
  })
  features: {
    promptCreditPrice: number;
    mediaCreditPrice: number;
    promptCredit: number;
    mediaCredit: number;
  };

  @Prop({
    type: Array,
    schema: [
      {
        label: String,
        value: String,
      },
    ],
  })
  extraFeatures: {
    label: string;
    value: string;
  }[];

  @Prop({ type: Boolean })
  deleted: boolean;
}

export type ProductDocument = Product & Document;

export const ProductSchema = SchemaFactory.createForClass(Product);
