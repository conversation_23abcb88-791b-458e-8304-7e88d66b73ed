import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { CreditTransactionModule } from '@resources/credit-transaction/credit-transaction.module';
import { TransactionModule } from '@resources/transaction/transaction.module';
import { BusinessModule } from '@business/business.module';
import { UserModule } from '@user/user.module';

import { ProductSchema, Product } from './product.model';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Product.name, schema: ProductSchema }]),
    CreditTransactionModule,
    TransactionModule,
    BusinessModule,
    UserModule,
  ],
  controllers: [ProductController],
  providers: [ProductService],
  exports: [ProductService, MongooseModule],
})
export class ProductModule {}
