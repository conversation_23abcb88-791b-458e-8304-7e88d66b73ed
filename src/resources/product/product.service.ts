import type { CrudQuery } from 'src/crud/crud.interface';

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { IncomingWebhook } from '@slack/webhook';
import { Stripe } from 'stripe';
import { Model } from 'mongoose';
import moment from 'moment';

import { TransactionDocument, Transaction } from '@resources/transaction/transaction.model';
import { BusinessDocument, Business } from '@business/business.model';
import { CreditTransactionService } from '@resources/credit-transaction/credit-transaction.service';
import { getPriceAfterDiscount } from '@common/utils/price';
import { UserDocument, User } from '@user/user.model';
import { CONTENT_COST } from '@resources/credit-transaction/credit-transaction.constant';
import { CrudService } from 'src/crud/crud.service';
import { capitalize } from '@common/utils';
import config from '@common/configs/config';

import { ProductDocument, Product, Addon } from './product.model';

const isAddOnActive = (business: BusinessDocument, id: string) => {
  const addon = business.addons?.[id];
  return !!(addon?.status === 'active' && moment(addon.expirationDate).diff(moment(), 'days') >= 0);
};

@Injectable()
export class ProductService extends CrudService<ProductDocument> {
  private readonly logger = new Logger(ProductService.name);
  private readonly slack: IncomingWebhook;
  private stripe: Stripe;

  constructor(
    @InjectModel(Transaction.name) private readonly transaction: Model<TransactionDocument>,
    @InjectModel(Business.name) private readonly business: Model<BusinessDocument>,
    @InjectModel(Product.name) private readonly product: Model<ProductDocument>,
    @InjectModel(User.name) private readonly user: Model<UserDocument>,
    private readonly creditTransactionService: CreditTransactionService,
  ) {
    super(product);
    this.stripe = new Stripe(config().stripe.secreteKey, { apiVersion: '2022-11-15' });
    this.slack = new IncomingWebhook(config().slack.channels.addonSubscriptions);
  }

  async findAllProductsByType(bid: string, type: 'package' | 'addon', q: CrudQuery) {
    const query = {
      sort: ['name,ASC'],
      ...q,
      s: JSON.stringify({ $and: [{ type }] }),
    };

    try {
      const business = await this.business.findById(bid);
      const products = await this.findAll(query);

      products.data = products.data.map((p) => ({
        ...p['_doc'],
        isActive: isAddOnActive(business, p._id),
      }));

      return products;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async purchaseAddon(
    id: string,
    coupon: string,
    { bid, uid }: { bid: string; uid: string },
  ): Promise<{ clientSecret: string; subscriptionRef: string }> {
    try {
      const business = await this.business.findById(bid);
      const addon = await this.product.findById(id);

      if (!business || !business.stripeId) {
        throw new Error('Business does not have stripe customer id');
      }

      if (isAddOnActive(business, id)) {
        throw new Error('Addon already purchased.');
      }

      let stripeCoupon: Stripe.Coupon;
      if (coupon) {
        stripeCoupon = await this.stripe.coupons.retrieve(coupon);

        if (!stripeCoupon || !stripeCoupon.valid) {
          throw new Error('Invalid coupon code.');
        }
      }

      const customerId = business.stripeId;
      const isTrial = !!addon.trialPeriod;
      const subscription: Stripe.Subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: addon.subscriptionId }],
        payment_behavior: 'default_incomplete',
        off_session: true,
        payment_settings: {
          save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent', 'pending_setup_intent'],
        ...(isTrial ? { trial_period_days: addon.trialPeriod } : {}),
        ...(coupon ? { coupon } : {}),
      });

      let clientSecret: string;
      let paymentRef: Stripe.PaymentIntent | Stripe.SetupIntent;
      if (isTrial) {
        paymentRef = subscription.pending_setup_intent as Stripe.SetupIntent;
        clientSecret = paymentRef.client_secret;
      } else {
        const invoice = subscription.latest_invoice as Stripe.Invoice;
        paymentRef = invoice.payment_intent as Stripe.PaymentIntent;
        clientSecret = paymentRef.client_secret;
      }

      if (clientSecret) {
        await new this.transaction({
          amount: getPriceAfterDiscount(addon.price, stripeCoupon?.percent_off || 0),
          description: addon.name,
          status: 'unconfirmed',
          paymentProvider: 'stripe',
          paymentMethod: 'card',
          paymentRef: paymentRef.id,
          subscriptionRef: subscription.id,
          paymentInvoiceUrl: (subscription.latest_invoice as Stripe.Invoice).invoice_pdf,
          coupon,
          product: addon._id,
          business: bid,
          user: uid,
        }).save();
      }

      return { clientSecret, subscriptionRef: subscription.id };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async confirmPurchaseAddon(
    id: string,
    paymentRef: string,
    { bid, uid }: { bid: string; uid: string },
  ): Promise<string> {
    try {
      const business = await this.business.findById(bid);
      if (!business) {
        throw new Error('Business not found.');
      }
      const user = await this.user.findById(uid);

      const isTrial = paymentRef.startsWith('seti');
      const paymentIntent = (await this.stripe[
        isTrial ? 'setupIntents' : 'paymentIntents'
      ].retrieve(paymentRef, isTrial ? {} : { expand: ['invoice.subscription'] })) as
        | Stripe.SetupIntent
        | Stripe.PaymentIntent;

      const transaction = await this.transaction.findOne({
        business: bid,
        paymentRef,
        user: uid,
      });

      const subscription = await this.stripe.subscriptions.retrieve(transaction.subscriptionRef);
      if (!paymentIntent || !subscription || !paymentIntent.customer || !paymentIntent.status) {
        throw new Error('Payment was not successful');
      }

      const addon = await this.product.findById(id);

      const subscriptionExpirationDate = isTrial
        ? moment().add(31, 'days').toISOString()
        : moment(subscription.current_period_end * 1000).toISOString();

      // TODO: Update charge id for trials via stripe webhook later
      await this.transaction.findByIdAndUpdate(transaction._id, {
        status: 'confirmed',
        ...(isTrial ? {} : { paymentRef: (paymentIntent as Stripe.PaymentIntent).latest_charge }),
        subscriptionExpirationDate,
      });

      await this.business.findByIdAndUpdate(bid, {
        addons: {
          ...business.addons,
          [id]: {
            expirationDate:
              transaction.coupon === 'Blogify100X'
                ? moment().add(100, 'years').toISOString()
                : subscriptionExpirationDate,
            transaction: transaction._id,
            status: 'active',
            name: addon.name,
            addon: id,
            features: addon.features, // Addon features credits & prices for displaying in frontend
          },
        },
      } as Business);

      const amount =
        CONTENT_COST['blog-from-media'] * (addon.features?.mediaCredit || 0) +
        CONTENT_COST['blog-from-text'] * (addon.features?.promptCredit || 0);
      await this.creditTransactionService.addMonthlyCredits(bid, amount, `${addon.name} Credits`);

      if (config().isProd) {
        this.slack.send({
          text: `*${user.email}* *${business.name}* has purchased the *${transaction.description}* addon. :tada: :partying_face:`,
          attachments: [
            {
              color: '#00ff00',
              text: `Subscription Fee: $${transaction.amount / 100}`,
            },
          ],
        });
      }

      return addon.productUrl;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async cancelPurchaseAddon(
    paymentRef: string,
    subscriptionRef: string,
  ): Promise<{ success: true }> {
    try {
      const isTrial = paymentRef.startsWith('seti');
      if (isTrial) {
        await this.stripe.setupIntents.cancel(paymentRef);
      } else {
        await this.stripe.paymentIntents.cancel(paymentRef);
      }
      await this.stripe.subscriptions.cancel(subscriptionRef);

      return { success: true };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deactivateAddon(id: string, comment: string, { bid, uid }: { bid: string; uid: string }) {
    try {
      const business = await this.business.findById(bid);
      if (!business) {
        throw new Error('Business not found.');
      }
      const user = await this.user.findById(uid);

      const transaction = await this.transaction.findById(business.addons[id]?.transaction);
      if (!transaction) {
        throw new Error('Subscription not found.');
      }

      const addon = await this.product.findById(id);
      const subscription = await this.stripe.subscriptions.cancel(transaction.subscriptionRef, {
        cancellation_details: { comment },
      } as Stripe.SubscriptionCancelParams);

      let subscriptionExpirationDate = moment(subscription.current_period_end * 1000).toISOString();
      if (addon.trialPeriod) {
        if (
          moment().diff(moment(subscription.current_period_start * 1000), 'days') <
          addon.trialPeriod
        ) {
          subscriptionExpirationDate = moment(subscription.current_period_start * 1000)
            .add(addon.trialPeriod, 'days')
            .toISOString();
        }
      }

      await this.business.findByIdAndUpdate(bid, {
        addons: {
          ...business.addons,
          [id]: {
            ...business.addons[id],
            status: 'deactivated',
            expirationDate: subscriptionExpirationDate,
          },
        },
      });

      if (config().isProd) {
        this.slack.send({
          text: `*${user.email}* *${business.name}* has deactivated the *${transaction.description}* addon. :sob:`,
          attachments: [
            {
              color: '#ff0000',
              text: comment,
            },
          ],
        });
      }

      return { success: true };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async attachAddonWithSubscription(bid: string, addonName: Addon, subscriptionPlan: string) {
    const business = await this.business.findById(bid);
    const addon = await this.product.findOne({ name: addonName });

    const subscriptionPlanName = subscriptionPlan.split('_').map(capitalize).join(' ');
    const transaction = await new this.transaction({
      amount: 0,
      description: `${addon.name} for ${subscriptionPlanName} plan`,
      status: 'confirmed',
      paymentProvider: 'stripe',
      paymentMethod: 'card',
      product: addon._id,
      business: business._id,
    }).save();

    const subscriptionExpirationDate = moment().add(100, 'years').toISOString();
    await this.business.findByIdAndUpdate(business._id, {
      addons: {
        ...business.addons,
        [addon._id]: {
          expirationDate: subscriptionExpirationDate,
          transaction: transaction._id,
          status: 'active',
          addon: addon._id,
          name: addon.name,
          features: addon.features,
        },
      },
    } as Business);
  }
}
