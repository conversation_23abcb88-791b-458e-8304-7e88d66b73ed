import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

import { BusinessDocument } from '@business/business.model';
import { ProductDocument } from '@resources/product/product.model';
import { UserDocument } from '@user/user.model';

@Schema({ collection: 'transactions', timestamps: true })
export class Transaction {
  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: String, required: true })
  status: 'unconfirmed' | 'confirmed';

  @Prop({ type: String, enum: ['stripe'], required: true })
  paymentProvider: 'stripe';

  @Prop({ type: String, enum: ['card'], required: true })
  paymentMethod: 'card';

  @Prop({ type: String })
  paymentRef: string;

  @Prop({ type: String })
  paymentInvoiceUrl: string;

  @Prop({ type: String })
  subscriptionRef: string;

  @Prop({ type: String })
  subscriptionExpirationDate: string;

  @Prop({ type: String })
  coupon: string;

  @Prop({ type: ObjectId, ref: 'Product' })
  product: ProductDocument;

  @Prop({ type: ObjectId, ref: 'Business' })
  business: BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User' })
  user: UserDocument;
}

export type TransactionDocument = Transaction & Document;

export const TransactionSchema = SchemaFactory.createForClass(Transaction);
