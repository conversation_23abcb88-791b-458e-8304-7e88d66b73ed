import type { ListResponse, CrudQuery } from 'src/crud/crud.interface';
import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import type { Transaction } from './transaction.model';

import { Controller, UseGuards, Headers, Query, Get, Req } from '@nestjs/common';

import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { CrudController } from 'src/crud/crud.controller';
import { Auth } from '@auth/guards/auth.guard';

import { TransactionService } from './transaction.service';

@Controller('transactions')
export class TransactionController extends CrudController<Transaction, any, any> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly transactionService: TransactionService) {
    super(transactionService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<Transaction>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: bid };
    }
    return this.transactionService.findAll(query);
  }
}
