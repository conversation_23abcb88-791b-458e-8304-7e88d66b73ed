import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { Transaction, TransactionSchema } from './transaction.model';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: Transaction.name, schema: TransactionSchema }])],
  controllers: [TransactionController],
  providers: [TransactionService],
  exports: [TransactionService, MongooseModule],
})
export class TransactionModule {}
