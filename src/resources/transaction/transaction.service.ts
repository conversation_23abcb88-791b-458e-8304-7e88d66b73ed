import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { Transaction, TransactionDocument } from './transaction.model';

@Injectable()
export class TransactionService extends CrudService<Transaction> {
  // private readonly logger = new Logger(TransactionService.name);

  constructor(
    @InjectModel(Transaction.name) private readonly transaction: Model<TransactionDocument>,
  ) {
    super(transaction);
  }
}
