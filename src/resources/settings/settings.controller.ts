import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { Settings } from './settings.model';

import { Controller, Query, Get } from '@nestjs/common';

import { CrudController } from 'src/crud/crud.controller';

import { SettingsService } from './settings.service';

@Controller('settings')
export class SettingsController extends CrudController<Settings, any, any> {
  constructor(private readonly settingsService: SettingsService) {
    super(settingsService);
  }

  @Get()
  async findAll(@Query() query: CrudQuery): Promise<ListResponse<Settings>> {
    return this.settingsService.findAll(query);
  }
}
