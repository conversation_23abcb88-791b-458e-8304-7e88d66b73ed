import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';

@Schema({ collection: 'settings', versionKey: false })
export class Settings {
  @Prop({ type: String, required: true })
  key: string;

  @Prop({ type: String, required: true })
  type: 'text' | 'number' | 'boolean' | 'date' | 'tag' | 'json';

  @Prop({ type: MongooseSchema.Types.Mixed, required: true })
  value: string | number | boolean | any[] | any;

  @Prop()
  deleted: boolean;
}

export type SettingsDocument = Settings & Document;

export const SettingsSchema = SchemaFactory.createForClass(Settings);
