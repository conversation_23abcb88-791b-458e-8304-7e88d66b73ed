import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { Settings, SettingsDocument } from './settings.model';

@Injectable()
export class SettingsService extends CrudService<Settings> {
  // private readonly logger = new Logger(SettingsService.name);

  constructor(@InjectModel(Settings.name) private readonly settings: Model<SettingsDocument>) {
    super(settings);
  }
}
