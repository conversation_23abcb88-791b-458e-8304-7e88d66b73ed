import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { BlogSourceSchema, BlogSource } from './blog-source.model';
import { BlogSourceController } from './blog-source.controller';
import { BlogSourceService } from './blog-source.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: BlogSource.name, schema: BlogSourceSchema }])],
  controllers: [BlogSourceController],
  providers: [BlogSourceService],
  exports: [MongooseModule, BlogSourceService],
})
export class BlogSourceModule {}
