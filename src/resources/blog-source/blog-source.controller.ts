import type { ControllerOptions } from '@/resources/base/base.types';

import { BaseController } from '@/resources/base/base.controller';
import { UserRole } from '@/user/user.model';

import { BlogSourceService } from './blog-source.service';
import { BlogSource } from './blog-source.model';

const options: Partial<ControllerOptions> = {
  isTeamResource: false,
  list: { roles: [] },
  show: { roles: [UserRole.superadmin] },
  create: { roles: [UserRole.superadmin] },
  update: { roles: [UserRole.superadmin] },
  remove: { roles: [UserRole.superadmin] },
};

export class BlogSourceController extends BaseController<
  BlogSource,
  typeof BlogSourceService,
  null,
  null
>({
  name: 'BlogSource',
  path: 'blog-sources',
  service: BlogSourceService,
  options,
}) {}
