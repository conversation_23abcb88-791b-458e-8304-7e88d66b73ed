import { SchemaF<PERSON>y, Schema, Prop } from '@nestjs/mongoose';

import { BlogSourceName, BlogSourceType } from '@/blog/blog.enums';
import { BaseModel } from '@/resources/base/base.model';

import { BlogSourceStatus } from './blog-source.enums';

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'blog-sources',
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
})
export class BlogSource extends BaseModel {
  @Prop({ type: String, required: true })
  iconUrl: string;

  @Prop({ type: String, enum: Object.values(BlogSourceName), required: true, unique: true })
  name: BlogSourceName;

  @Prop({ type: String, enum: Object.values(BlogSourceType), required: true })
  type: BlogSourceType;

  @Prop({ type: String, enum: ['url', 'textarea', 'upload'], required: true })
  inputType: 'url' | 'textarea' | 'upload';

  @Prop({ type: String, enum: Object.values(BlogSourceStatus), default: 'active' })
  status: BlogSourceStatus;

  @Prop({ type: Number, default: 0 })
  sequence: number;

  // Frontend Text Display
  @Prop({ type: String })
  displayName: BlogSourceName | string;

  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: String })
  footer: string;
  // End Frontend Text Display

  // Flags
  @Prop({ type: Boolean, default: false })
  isEmbeddable?: boolean;

  @Prop({ type: Boolean, default: false })
  availableOnTrial?: boolean;

  @Prop({ type: Boolean, default: false })
  availableOnFree?: boolean;

  // Virtual Flags
  declare isInactive?: boolean;
  // End Flags
}

const BlogSourceSchema = SchemaFactory.createForClass(BlogSource);

BlogSourceSchema.virtual('isInactive').get(function () {
  return this.status === BlogSourceStatus.Inactive;
});
export { BlogSourceSchema };
