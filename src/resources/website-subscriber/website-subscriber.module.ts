import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { WebsiteSubscriber, WebsiteSubscriberSchema } from './website-subscriber.model';
import { WebsiteSubscriberController } from './website-subscriber.controller';
import { WebsiteSubscriberService } from './website-subscriber.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: WebsiteSubscriber.name, schema: WebsiteSubscriberSchema }]),
  ],
  controllers: [WebsiteSubscriberController],
  providers: [WebsiteSubscriberService],
  exports: [WebsiteSubscriberService, MongooseModule],
})
export class WebsiteSubscriberModule {}
