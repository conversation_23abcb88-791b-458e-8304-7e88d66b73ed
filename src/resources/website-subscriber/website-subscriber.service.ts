import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { WebsiteSubscriber, WebsiteSubscriberDocument } from './website-subscriber.model';

@Injectable()
export class WebsiteSubscriberService extends CrudService<WebsiteSubscriber> {
  constructor(
    @InjectModel(WebsiteSubscriber.name)
    private readonly websiteSubscriber: Model<WebsiteSubscriberDocument>,
  ) {
    super(websiteSubscriber);
  }
}
