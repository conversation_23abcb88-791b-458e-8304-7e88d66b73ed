import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { WebsiteSubscriber } from './website-subscriber.model';
import type { Response } from 'express';

import { Controller, UseGuards, Delete, Param, Query, Get, Res } from '@nestjs/common';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';

import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { CrudController } from '@/crud/crud.controller';
import { Auth } from '@/auth/guards/auth.guard';

import { WebsiteSubscriberCreateDto } from './dto/website-subscriber-create.dto';
import { WebsiteSubscriberService } from './website-subscriber.service';

@Controller('websites/:websiteId/subscribers')
export class WebsiteSubscriberController extends CrudController<
  WebsiteSubscriber,
  WebsiteSubscriberCreateDto,
  any
> {
  constructor(private readonly websiteSubscriberService: WebsiteSubscriberService) {
    super(websiteSubscriberService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Param('websiteId') websiteId: string,
  ): Promise<ListResponse<WebsiteSubscriber>> {
    query.filter = { websiteId };
    return this.websiteSubscriberService.findAll(query);
  }

  @Delete(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async remove(@Param('id') id: string): Promise<{ success: boolean }> {
    return this.websiteSubscriberService.delete(id);
  }

  @Get('export')
  @Roles('admin')
  @UseGuards(QueryTokenGuard, Auth, RolesGuard)
  async exportAsCSV(@Query() query: CrudQuery, @Res() res: Response) {
    query.limit = 10000;
    const { data } = await this.websiteSubscriberService.findAll(query);
    const csvData = data.map((d) => {
      return {
        name: d.name,
        email: d.email,
        createdAt: moment(d['createdAt']).format('YYYY-MM-DD HH:mm:ss'),
      };
    });

    const csv = createObjectCsvStringifier({
      header: [
        { id: 'name', title: 'Name' },
        { id: 'email', title: 'Email' },
        { id: 'createdAt', title: 'Joined' },
      ],
    });

    const header = csv.getHeaderString();
    const records = csv.stringifyRecords(csvData);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="subscribers.csv"');

    res.send(header + records);
  }
}
