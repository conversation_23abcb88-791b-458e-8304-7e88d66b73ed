import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';

@Schema({ timestamps: true, collection: 'website-subscribers' })
export class WebsiteSubscriber {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  websiteId: string;

  @Prop()
  deleted: boolean;
}

export type WebsiteSubscriberDocument = WebsiteSubscriber & Document;

const WebsiteSubscriberSchema = SchemaFactory.createForClass(WebsiteSubscriber);
WebsiteSubscriberSchema.index({ websiteId: 1, email: 1 }, { unique: true });

export { WebsiteSubscriberSchema };
