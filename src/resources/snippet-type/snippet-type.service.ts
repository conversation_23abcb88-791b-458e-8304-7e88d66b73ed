import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { CrudService } from 'src/crud/crud.service';

import { SnippetType, SnippetTypeDocument } from './snippet-type.model';

@Injectable()
export class SnippetTypeService extends CrudService<SnippetType> {
  // private readonly logger = new Logger(SnippetTypeService.name);

  constructor(
    @InjectModel(SnippetType.name) private readonly snippetType: Model<SnippetTypeDocument>,
  ) {
    super(snippetType);
  }
}
