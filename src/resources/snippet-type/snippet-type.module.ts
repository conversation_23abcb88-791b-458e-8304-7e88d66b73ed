import { MongooseModule } from '@nestjs/mongoose';
import { Modu<PERSON> } from '@nestjs/common';

import { SnippetType, SnippetTypeSchema } from './snippet-type.model';
import { SnippetTypeController } from './snippet-type.controller';
import { SnippetTypeService } from './snippet-type.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: SnippetType.name, schema: SnippetTypeSchema }])],
  controllers: [SnippetTypeController],
  providers: [SnippetTypeService],
  exports: [SnippetTypeService, MongooseModule],
})
export class SnippetTypeModule {}
