import type { Document } from 'mongoose';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

import { CategoryDocument } from '@resources/category/category.model';

type ModelType =
  | 'academic_researcher'
  | 'blogger'
  | 'ceo'
  | 'content_writer'
  | 'copywriter'
  | 'creative_writer'
  | 'financial_analyst'
  | 'hr'
  | 'journalist'
  | 'legal_expert'
  | 'marketing_manager'
  | 'medical_writer'
  | 'pr_specialist'
  | 'sales_expert'
  | 'scriptwriter'
  | 'seo_expert'
  | 'social_media_expert'
  | 'startup_strategist'
  | 'technical_writer'
  | 'translator_and_linguist';

@Schema({ collection: 'snippet-types' })
export class SnippetType {
  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: ObjectId, ref: 'Category', required: true })
  category: CategoryDocument;

  @Prop({ type: String, required: true })
  model: ModelType;

  @Prop({ type: String })
  iconUrl: string;

  @Prop({ type: String })
  promptHint: string;

  @Prop({ type: Array })
  extraFields: { fieldName: string; placeholder: string }[];

  @Prop({ type: Boolean })
  deleted: boolean;

  @Prop({ type: String, default: 'Prompt' })
  promptHeading: string;

  @Prop({ type: Boolean, default: true })
  visibility: boolean;

  @Prop({ type: Boolean, default: true })
  displayTone: boolean;

  @Prop({ type: Boolean, default: false })
  displayPerspective: boolean;

  @Prop({ type: Boolean, default: false })
  displayOutputLanguage: boolean;

  @Prop({ type: Boolean, default: false })
  displayInputLanguage: boolean;
}

export type SnippetTypeDocument = SnippetType & Document;

export const SnippetTypeSchema = SchemaFactory.createForClass(SnippetType);
