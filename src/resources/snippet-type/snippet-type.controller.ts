import type { SnippetType } from './snippet-type.model';

import { Controller } from '@nestjs/common';

import { CrudController } from 'src/crud/crud.controller';

import { SnippetTypeService } from './snippet-type.service';

@Controller('snippet-types')
export class SnippetTypeController extends CrudController<SnippetType, any, any> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly snippetTypeService: SnippetTypeService) {
    super(snippetTypeService);
  }
}
