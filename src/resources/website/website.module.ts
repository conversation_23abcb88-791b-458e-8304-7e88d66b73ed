import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { VercelAPIService } from '@/common/services/vercel-api.service';
import { EventModule } from '@/event/event.module';
import { BlogModule } from '@/blog/blog.module';

import { WebsiteSchema, Website } from './website.model';
import { WebsiteController } from './website.controller';
import { WebsiteService } from './website.service';
import { SlackModule } from '../../integrations/internal/slack/slack.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Website.name, schema: WebsiteSchema }]),
    SlackModule,
    EventModule,
    BlogModule,
  ],
  controllers: [WebsiteController],
  providers: [WebsiteService, VercelAPIService],
  exports: [WebsiteService, MongooseModule],
})
export class WebsiteModule {}
