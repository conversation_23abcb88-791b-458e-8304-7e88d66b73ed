import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, collection: 'websites' })
export class Website {
  @Prop({ type: String })
  bid: string;

  @Prop({ type: String })
  uid: string;

  @Prop({ type: String })
  url: string;

  @Prop({ type: String })
  name: string;

  @Prop({ type: String, enum: ['checking', 'active', 'error'], default: 'checking' })
  status: 'checking' | 'active' | 'error';

  @Prop({ type: String })
  error: string;

  @Prop({ type: String })
  verificationRecordValue?: string;

  // Settings
  @Prop({ type: String, default: 'en' })
  defaultLanguage: string;

  @Prop({ type: Boolean, default: true })
  ratingsEnabled: boolean;

  @Prop({ type: Boolean, default: true })
  commentsEnabled: boolean;

  @Prop({ type: Boolean, default: true })
  shareEnabled: boolean;

  @Prop({ type: Boolean, default: true })
  tableOfContentsEnabled: boolean;

  // Theming
  @Prop({ type: String, enum: ['default', 'personal', 'news'], default: 'default' })
  theme: 'default' | 'personal' | 'news';

  @Prop({ type: String, default: 'font-inter' })
  font: string;

  @Prop({ type: String })
  logo: string;

  @Prop({ type: String, default: 'https://blogify.ai/favicon.ico' })
  favicon: string;

  @Prop({ type: String, default: '#F2470D' })
  brandColor: string;

  @Prop({
    type: Array,
    default: [
      { title: 'Home', url: '/' },
      { title: 'Blog', url: '/blog' },
    ],
  })
  links: { title: string; url: string }[];

  @Prop({ type: Array, default: [] })
  footerLinks: { title: string; url: string }[];

  @Prop({ type: Boolean, default: true })
  showBlogifyBranding: boolean;

  @Prop({
    type: String,
    default: `© ${new Date().getFullYear()} All rights reserved.`,
  })
  footerText: string;

  @Prop({ type: String })
  privacyPolicyUrl: string;

  // SEO
  @Prop({ type: String })
  metaTitle: string;

  @Prop({ type: String })
  metaDescription: string;

  @Prop({ type: [String], default: [] })
  metaKeywords: string[];

  // Scripts
  @Prop({ type: String })
  googleAnalyticsId: string;

  @Prop({ type: String })
  googleTagManagerId: string;

  @Prop()
  deleted: boolean;
}

export type WebsiteDocument = Website & Document;

export const WebsiteSchema = SchemaFactory.createForClass(Website);
