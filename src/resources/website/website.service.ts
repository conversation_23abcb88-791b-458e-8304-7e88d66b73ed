import type { CrudQ<PERSON>y, ListResponse } from '@/crud/crud.interface';
import type { Model } from 'mongoose';

import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import { ObjectId } from 'mongodb';

import { VercelAPIService } from '@/common/services/vercel-api.service';
import { CrudService } from '@/crud/crud.service';
import { EventType } from '@/event/event.enums';
import { Event } from '@/event/event.model';
import { Blog } from '@/blog/blog.model';

import { WebsiteDocument, Website } from './website.model';
import { AnalyticsRangeParams } from './dto/website-analytics.dto';

@Injectable()
export class WebsiteService extends CrudService<Website> {
  constructor(
    @InjectModel(Website.name) private websiteModel: Model<Website>,
    @InjectModel(Event.name) private eventModel: Model<Event>,
    @InjectModel(Blog.name) private blogModel: Model<Blog>,
    private readonly vercelAPIService: VercelAPIService,
  ) {
    super(websiteModel);
  }

  async getAnalytics(websiteId: string, business: string, { from, to }: AnalyticsRangeParams) {
    const $match = {
      business,
      website: new ObjectId(websiteId),
      createdAt: { $gte: from, $lte: to },
      type: { $in: [EventType.View, EventType.Click, EventType.Action] },
    };

    const $group = {
      _id: { type: '$type' },
      platform: { $push: '$userAgentInfo.platform' },
      vendor: { $push: '$userAgentInfo.vendor' },
      browser: { $push: '$userAgentInfo.browser' },
      os: { $push: '$userAgentInfo.os' },
      country: { $push: '$geoInfo.country' },
      city: { $push: '$geoInfo.city' },
      actions: { $push: '$ref' }, // Collect action event references
    };

    const result = await this.eventModel.aggregate([{ $match }, { $group }]);

    const countOccurrences = (arr: string[]) =>
      arr.reduce((acc: Record<string, number>, key: string) => {
        if (key) acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});

    const format = (type: EventType) => {
      const item = result.find((r) => r._id.type === type) || {};
      return {
        platform: countOccurrences(item.platform || []),
        vendor: countOccurrences(item.vendor || []),
        browser: countOccurrences(item.browser || []),
        os: countOccurrences(item.os || []),
        country: countOccurrences(item.country || []),
        city: countOccurrences(item.city || []),
      };
    };

    return {
      view: format(EventType.View),
      click: format(EventType.Click),
      actions: result.find((r) => r._id.type === EventType.Action)?.actions || [],
    };
  }

  async getActivity(websiteId: string, business: string, { from, to }: AnalyticsRangeParams) {
    const $match = {
      business,
      website: new ObjectId(websiteId),
      createdAt: { $gte: from, $lte: to },
      type: { $in: [EventType.View, EventType.Click, EventType.Action] },
    };

    const $group = {
      _id: {
        date: { $dateToString: { format: '%Y-%m-%dT00:00:00.000Z', date: '$createdAt' } },
        type: '$type',
      },
      count: { $sum: 1 },
      blogs: { $addToSet: '$blog' },
    };

    const result = await this.eventModel.aggregate([{ $match }, { $group }]);

    const format = (type: EventType) =>
      result
        .filter((item) => item._id.type === type && item.count)
        .map((item) => [item._id.date, item.count]);

    const blogIds = result.reduce((ids, r) => {
      ids.push(...r.blogs);
      return ids;
    }, []);

    const blogs = await this.blogModel
      .find({
        _id: { $in: blogIds },
        blogifyPublishTime: { $gte: from, $lte: to },
      })
      .select('title blogifyPublishTime')
      .populate('uid', 'name profilePicture');

    return {
      clicks: format(EventType.Click),
      sales: format(EventType.Action),
      views: format(EventType.View),
      blogs,
    };
  }

  async count(bid: string) {
    return await this.websiteModel.count({ bid, status: 'active', deleted: { $ne: true } }).exec();
  }

  async countBlogsByBusiness(bid: string, query: { category?: string } = {}): Promise<number> {
    const { category } = query;

    const siteIds = await this.websiteModel
      .find({ bid })
      .select({ _id: 1 })
      .then((result) => result.map((r) => String(r._id)));

    const filterQuery: Record<string, any> = {
      bid,
      publishResult: {
        $elemMatch: {
          siteID: { $in: siteIds },
          'outcome.link': { $exists: true, $ne: null },
        },
      },
    };

    if (category) {
      filterQuery.categories = { $in: [category] };
    }

    return await this.blogModel.countDocuments(filterQuery);
  }

  async findBlogs(
    id: string,
    bid: string,
    query: CrudQuery & { category: string },
  ): Promise<ListResponse<Blog>> {
    const { limit = 10, page = 1, category } = query;

    const filterQuery: Record<string, any> = {
      bid,
      publishResult: {
        $elemMatch: {
          siteID: id,
          'outcome.link': { $exists: true, $ne: null },
        },
      },
    };

    if (category) {
      filterQuery.categories = { $in: [category] };
    }

    const [blogs, total] = await Promise.all([
      this.blogModel
        .find(filterQuery)
        .sort({ createdAt: -1 })
        .populate('uid')
        .limit(limit)
        .skip((page - 1) * limit)
        .lean(),
      this.blogModel.countDocuments(filterQuery),
    ]);

    return {
      data: blogs,
      total,
    };
  }

  async checkRecentlyUpdatedDomainStatus() {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const domains = await this.websiteModel
      .find({
        url: { $not: /blogify.ai/ },
        status: { $in: ['checking', 'error'] },
        updatedAt: { $gte: oneHourAgo },
        deleted: { $ne: true },
      })
      .select('_id url status error verificationRecordValue');

    for (const domain of domains) {
      await this.updateDomainStatus(domain);
    }
  }

  async checkDomainStatus() {
    const domains = await this.websiteModel
      .find({ url: { $not: /blogify.ai/ }, deleted: { $ne: true } })
      .select('_id url status error verificationRecordValue');

    for (const domain of domains) {
      await this.updateDomainStatus(domain);
    }
  }

  async updateDomainStatus(website: WebsiteDocument): Promise<Website['status']> {
    const info = await this.vercelAPIService.getDomainInfo({ domain: website.url });
    let status: 'checking' | 'active' | 'error' = 'active';
    let error = null;

    if (!info.projectId) {
      status = 'error';
      error = 'Domain is not added to project. Please contact support.';
    } else if (!info.verified) {
      status = 'error';
      error = 'Domain is pending verification.';
    } else if (info.misconfigured) {
      status = 'error';
      error =
        "Invalid configuration detected. Please review your DNS settings. If you need assistance, don't hesitate to contact support.";
    }

    const updates: Partial<WebsiteDocument> = {
      verificationRecordValue: info.verification?.[0]?.value ? info.verification[0].value : null,
      status,
      error,
    };

    if (
      !(
        status === website.status &&
        error === website.error &&
        updates.verificationRecordValue === website.verificationRecordValue
      )
    ) {
      await this.websiteModel.findByIdAndUpdate(website._id, updates, { timestamps: false });
    }

    return status;
  }
}
