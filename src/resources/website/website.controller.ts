import type { ListResponse, CrudQuery } from '@/crud/crud.interface';

import {
  BadRequestException,
  NotFoundException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Headers,
  Delete,
  Param,
  Patch,
  Query,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { CronExpression, Cron } from '@nestjs/schedule';
// import { promises as dns } from 'dns';

import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { VercelAPIService } from '@/common/services/vercel-api.service';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { Auth } from '@/auth/guards/auth.guard';
import { Blog } from '@/blog/blog.model';

import { WebsiteDocument, Website } from './website.model';
import { AnalyticsRangeParams } from './dto/website-analytics.dto';
import { CreateWebsiteDto } from './dto/create-website.dto';
import { UpdateWebsiteDto } from './dto/update-website.dto';
import { WebsiteService } from './website.service';

@Controller('websites')
@UsePipes(new ValidationPipe({ transform: true }))
export class WebsiteController extends CrudController<Website, CreateWebsiteDto, UpdateWebsiteDto> {
  constructor(
    private readonly vercelAPIService: VercelAPIService,
    private readonly websiteService: WebsiteService,
    private readonly slackService: SlackService,
  ) {
    super(websiteService);
  }

  @Get(':id/analytics')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getAnalytics(
    @Query() query: AnalyticsRangeParams,
    @Req() { bid }: AuthenticatedRequest,
    @Param('id') websiteId: string,
  ): Promise<any> {
    return this.websiteService.getAnalytics(websiteId, bid, query);
  }

  @Get(':id/analytics/activity')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getActivity(
    @Query() query: AnalyticsRangeParams,
    @Req() { bid }: AuthenticatedRequest,
    @Param('id') websiteId: string,
  ): Promise<any> {
    return this.websiteService.getActivity(websiteId, bid, query);
  }

  @Get()
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<Website>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid: bid };
    }
    return this.websiteService.findAll(query);
  }

  @Get(':id')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findOne(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Website> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid: bid };
    }
    return this.websiteService.findOneById(id, query);
  }

  @Get(':id/blogs')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findBlogs(
    @Param('id') id: string,
    @Query() query: CrudQuery & { category: string },
    @Req() { bid }: AuthenticatedRequest,
  ): Promise<ListResponse<Blog>> {
    return this.websiteService.findBlogs(id, bid, query);
  }

  @Post()
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async create(
    @Body() body: CreateWebsiteDto,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Query() query: CrudQuery,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Website> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.bid = bid;
      body.uid = uid;
    }

    await this.checkForExistingActiveDomain(body, query);

    // const records: string[] = await dns.resolveCname(body.url);
    // if (!records.includes('cname.vercel-dns.com')) {
    //   throw new BadRequestException(
    //     'It seems like the required DNS record is not yet added. Please ensure you have added the CNAME record to your DNS settings and allow a few minutes for it to propagate. Once done, try again.',
    //   );
    // }

    await this.vercelAPIService.addDomainToProject({ domain: body.url });
    const website = await super.create(body);
    website.status = await this.websiteService.updateDomainStatus(website as WebsiteDocument);

    await this.slackService.sendMessage({
      message: `New Website Added: https://${body.url} by ${user.email}.`,
      channel: 'dev-slack',
    });

    return website;
  }

  @Patch(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async update(
    @Param('id') id: string,
    @Body() body: UpdateWebsiteDto,
    @Query() query: CrudQuery,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Website> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.bid = bid;
      body.uid = uid;
    }

    if (!isRequestFromAdmin) {
      query.filter = { bid };
    }
    const website = await this.websiteService.findOneById(id, query);
    if (!website) {
      throw new NotFoundException('Website not found.');
    }

    if (body.url && body.url !== website.url) {
      await this.checkForExistingActiveDomain(body, query);

      await this.vercelAPIService.removeDomainFromProject({ domain: website.url });
      await this.vercelAPIService.addDomainToProject({ domain: body.url });

      const status = await this.websiteService.updateDomainStatus(website as WebsiteDocument);
      body.status = status === 'active' ? status : 'checking';
    }

    return super.update(id, body);
  }

  @Delete(':id')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async remove(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<{ success: boolean }> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid };
    }
    const website = await this.websiteService.findOneById(id, query);
    if (!website) {
      throw new NotFoundException('Website not found.');
    }

    await this.vercelAPIService.removeDomainFromProject({ domain: website.url });
    return super.remove(id);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async checkRecentlyUpdatedDomainStatus() {
    await this.websiteService.checkRecentlyUpdatedDomainStatus();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkDomainStatus() {
    await this.websiteService.checkDomainStatus();
  }

  private async checkForExistingActiveDomain(
    body: CreateWebsiteDto | UpdateWebsiteDto,
    query: CrudQuery,
  ) {
    query.filter = { url: body.url, status: 'active', deleted: { $ne: true } };
    query.limit = 0;

    const websites = await this.websiteService.findAll(query);
    if (websites.total) {
      throw new BadRequestException(`The domain you've entered is already in use.`);
    }
  }
}
