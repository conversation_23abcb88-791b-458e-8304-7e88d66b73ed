import { IsOptional, IsBoolean, IsString, IsUrl } from 'class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';

export class UpdateWebsiteDto {
  @ApiProperty()
  @IsOptional()
  @IsUrl()
  bid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  uid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  url!: string;

  @ApiHideProperty()
  @IsOptional()
  status!: 'checking' | 'active' | 'error';

  @ApiProperty()
  @IsOptional()
  @IsString()
  defaultLanguage?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  tableOfContentsEnabled?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  googleAnalyticsId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  googleTagManagerId?: string;
}
