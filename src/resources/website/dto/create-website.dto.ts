import { IsOptional, IsBoolean, IsString, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateWebsiteDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  bid!: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  uid!: string;

  @ApiProperty()
  @IsUrl()
  url: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  defaultLanguage?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  tableOfContentsEnabled?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  googleAnalyticsId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  googleTagManagerId?: string;
}
