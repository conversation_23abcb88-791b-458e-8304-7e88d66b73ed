import type { YouTubeContentTypes } from 'src/youtube/youtube-content/youtube-content.model';
import type { BusinessDocument } from '@business/business.model';
import type { UserDocument } from '@user/user.model';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { ImageContent } from '@/resources/image/image.enums';
import { BlogType } from '@/blog/blog.model';
import * as YouTube from '@/youtube/youtube-content/youtube-content.model';

const CONTENT_TYPES = Object.values({
  ...BlogType,
  ...ImageContent,
  ...YouTube.CONTENT_TYPE,
  ...YouTube.CONTENT_TYPE_EXTRA,
  ...YouTube.CONTENT_TYPE_OTHERS,
  ...YouTube.CONTENT_TYPE_POST,
});

export type ContentType = BlogType | ImageContent | YouTubeContentTypes;

export enum TransactionType {
  // Debits
  ContentGenerationCharge = 'CONTENT_GENERATION_CHARGE',
  MonthlyCreditsExpiration = 'MONTHLY_CREDITS_EXPIRATION',

  // Credits
  ContentGenerationChargeRefund = 'CONTENT_GENERATION_CHARGE_REFUND',
  MonthlyCreditsAllocation = 'MONTHLY_CREDITS_ALLOCATION',
  AdditionalCreditPurchase = 'ADDITIONAL_CREDIT_PURCHASE',
}

@Schema({ collection: 'credit-transactions', timestamps: true })
export class CreditTransaction {
  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: String, required: true, enum: ['debit', 'credit'] })
  mode: 'debit' | 'credit';

  @Prop({ type: String, required: true, enum: Object.values(TransactionType) })
  type: TransactionType;

  @Prop({
    type: String,
    required: true,
    enum: ['unconfirmed', 'confirmed', 'refunded'],
  })
  status: 'unconfirmed' | 'confirmed' | 'refunded';

  @Prop({ type: String })
  description?: string;

  @Prop({ type: String, enum: CONTENT_TYPES })
  contentType?: ContentType;

  @Prop({ type: [String] })
  contentIds?: string[];

  @Prop({ type: Boolean, default: false })
  monthlyCreditsUsed?: boolean;

  @Prop({ type: ObjectId, ref: 'Business' })
  business: BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User' })
  user?: UserDocument;
}

export type CreditTransactionDocument = CreditTransaction & Document;
export const CreditTransactionSchema = SchemaFactory.createForClass(CreditTransaction);
