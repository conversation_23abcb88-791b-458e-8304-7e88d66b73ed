import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { BusinessDocument } from '@business/business.model';
import type { ContentType } from './credit-transaction.model';

import { ForbiddenException, Injectable, mixin } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Business } from '@business/business.model';
import { Model } from 'mongoose';

import { CONTENT_COST } from './credit-transaction.constant';

export const CreditTransactionGuard = (contentType: ContentType) => {
  @Injectable()
  class CreditTransactionGuardMixin implements CanActivate {
    constructor(@InjectModel(Business.name) readonly business: Model<BusinessDocument>) {}
    async canActivate(context: ExecutionContext): Promise<boolean> {
      const request = context.switchToHttp().getRequest();

      const business = await this.business.findById(request.bid);
      if (business.credits < CONTENT_COST[contentType]) {
        throw new ForbiddenException('Insufficient credits to generate the requested content.');
      }

      return request;
    }
  }

  const guard = mixin(CreditTransactionGuardMixin);
  return guard;
};
