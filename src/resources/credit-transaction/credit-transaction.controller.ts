import type { ListResponse, CrudQuery } from 'src/crud/crud.interface';
import type { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import type { CreditTransaction } from './credit-transaction.model';

import { Controller, UseGuards, Headers, Query, Get, Req } from '@nestjs/common';

import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { CrudController } from 'src/crud/crud.controller';
import { Auth } from '@auth/guards/auth.guard';

import { CreditTransactionService } from './credit-transaction.service';

@Controller('credit-transactions')
export class CreditTransactionController extends CrudController<CreditTransaction, any, any> {
  // private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly creditTransactionService: CreditTransactionService) {
    super(creditTransactionService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<CreditTransaction>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: { $in: [bid, String(bid)] } };
    }
    return this.creditTransactionService.findAll(query);
  }
}
