import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { BusinessModule } from '@business/business.module';

import { CreditTransactionSchema, CreditTransaction } from './credit-transaction.model';
import { CreditTransactionController } from './credit-transaction.controller';
import { CreditTransactionService } from './credit-transaction.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: CreditTransaction.name, schema: CreditTransactionSchema }]),
    BusinessModule,
  ],
  controllers: [CreditTransactionController],
  providers: [CreditTransactionService],
  exports: [CreditTransactionService, MongooseModule],
})
export class CreditTransactionModule {}
