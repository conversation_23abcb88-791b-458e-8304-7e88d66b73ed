import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BusinessDocument, Business } from '@business/business.model';
import { CrudService } from 'src/crud/crud.service';

import {
  CreditTransactionDocument,
  CreditTransaction,
  TransactionType,
} from './credit-transaction.model';
import { CONTENT_COST } from './credit-transaction.constant';

type TransactionDataType = Omit<CreditTransaction, 'mode' | 'monthlyCreditsUsed' | 'business'>;
type UserIds = { bid: string; uid?: string };

@Injectable()
export class CreditTransactionService extends CrudService<CreditTransaction> {
  constructor(
    @InjectModel(CreditTransaction.name)
    private readonly creditTransaction: Model<CreditTransactionDocument>,
    @InjectModel(Business.name) private readonly business: Model<BusinessDocument>,
  ) {
    super(creditTransaction);
  }

  async deductContentCost(
    { bid, uid }: UserIds,
    {
      amount,
      status,
      ...data
    }: Pick<CreditTransaction, 'contentType' | 'contentIds'> &
      Partial<Pick<CreditTransaction, 'amount' | 'status'>>,
    description?: string,
  ) {
    return this.debit(
      { bid, uid },
      {
        type: TransactionType.ContentGenerationCharge,
        description: description || `${data.contentType} generation cost`,
        status: status || 'unconfirmed',
        amount: amount || 0, // Amount will be charged automatically based on the contentType when 0 is passed.
        ...data,
      },
    );
  }

  async deductUnusedMonthlyCredits({ bid, uid }: UserIds, amount: number) {
    return this.debit(
      { bid, uid },
      {
        type: TransactionType.MonthlyCreditsExpiration,
        description: 'Your unused monthly credits have expired.',
        status: 'confirmed',
        amount,
      },
    );
  }

  async addMonthlyCredits(bid: string, amount: number, description?: string) {
    return this.credit(
      { bid },
      {
        description: description || 'Your monthly credits have been refilled.',
        type: TransactionType.MonthlyCreditsAllocation,
        status: 'confirmed',
        amount,
      },
      true,
    );
  }

  async addAdditionalCredits(bid: string, amount: number, description?: string) {
    return this.credit(
      { bid },
      {
        description: description || `You have purchased ${amount} credits.`,
        type: TransactionType.AdditionalCreditPurchase,
        status: 'confirmed',
        amount,
      },
    );
  }

  async refund(bid: string, debitId: string) {
    const debit = await this.creditTransaction.findOne({ _id: debitId, status: 'unconfirmed' });
    if (!debit?.amount) return;

    return this.credit(
      { bid },
      {
        amount: debit.amount,
        type: TransactionType.ContentGenerationChargeRefund,
        status: 'confirmed',
        contentType: debit.contentType,
        contentIds: debit.contentIds || [],
        description: `Refund for ${debit.description || debit.contentType}`,
      },
      debit.monthlyCreditsUsed,
    ).then(async (resp) => {
      await this.creditTransaction.updateOne({ _id: debitId }, { status: 'refunded' });
      return resp;
    });
  }

  async confirmTransaction(bid: string, creditTransactionId: string) {
    return this.creditTransaction.findOneAndUpdate(
      { bid, creditTransactionId },
      { status: 'confirmed' },
    );
  }

  private async debit(
    { bid, uid }: UserIds,
    { contentType, ...transactionData }: TransactionDataType,
  ) {
    const transactionAmount = transactionData.amount || CONTENT_COST[contentType];
    if (!transactionAmount || transactionAmount < 0) {
      throw new BadRequestException(`Transaction amount is invalid.`);
    }

    const business = await this.business.findById(bid);
    if (business.credits < transactionAmount) {
      throw new ForbiddenException('Insufficient credits to perform this action.');
    }

    const session = await this.business.db.startSession();
    session.startTransaction();
    try {
      const monthlyCredits =
        business.monthlyCredits - transactionAmount >= 0 ? transactionAmount : 0;
      await this.business.findByIdAndUpdate(
        bid,
        { $inc: { credits: -1 * transactionAmount, monthlyCredits: -1 * monthlyCredits } },
        { session },
      );

      const newCreditTransaction = await this.creditTransaction.create(
        [
          {
            ...transactionData,
            amount: transactionAmount,
            mode: 'debit',
            contentType,
            monthlyCreditsUsed: monthlyCredits > 0,
            business: bid,
            user: uid,
          },
        ],
        { session },
      );

      await session.commitTransaction();
      session.endSession();

      return newCreditTransaction[0];
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }

  private async credit(
    { bid, uid }: UserIds,
    { amount, ...transactionData }: TransactionDataType,
    creditMonthlyCredits?: boolean,
  ) {
    if (!amount || amount < 0) {
      throw new BadRequestException(`Transaction amount is invalid.`);
    }

    const session = await this.business.db.startSession();
    session.startTransaction();
    try {
      await this.business.findByIdAndUpdate(
        bid,
        {
          $inc: {
            credits: amount,
            ...(creditMonthlyCredits ? { monthlyCredits: amount } : {}),
          },
        },
        { session },
      );

      const newCreditTransaction = await this.creditTransaction.create(
        [{ ...transactionData, amount, mode: 'credit', business: bid, user: uid }],
        { session },
      );

      await session.commitTransaction();
      session.endSession();

      return newCreditTransaction[0];
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }
}
