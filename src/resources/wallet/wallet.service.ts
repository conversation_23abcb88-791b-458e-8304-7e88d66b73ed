import type { ClientSession, Model } from 'mongoose';

import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

import { BusinessDocument, Business } from '@/business/business.model';
import { CrudService } from '@/crud/crud.service';

import { WalletTransactionDocument, WalletTransaction } from './wallet-transaction.model';
import { WalletTransactionType } from './wallet.enum';

type TransactionDataType = Omit<WalletTransaction, 'business' | 'previousBalance' | 'newBalance'>;
type UserIds = { bid: string; uid?: string };

@Injectable()
export class WalletService extends CrudService<WalletTransaction> {
  constructor(
    @InjectModel(WalletTransaction.name)
    private readonly walletTransaction: Model<WalletTransactionDocument>,
    @InjectModel(Business.name) private readonly business: Model<BusinessDocument>,
  ) {
    super(walletTransaction);
  }

  async getBalance(bid: string, currency = 'USD'): Promise<{ balance: number; currency: string }> {
    const session = await this.walletTransaction.db.startSession();
    session.startTransaction();

    try {
      const wallet = await this.getLastEntry(bid, currency, session);

      await session.commitTransaction();
      session.endSession();

      return wallet;
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }

  async getLastPayout(business: string, currency = 'USD'): Promise<WalletTransaction> {
    return await this.walletTransaction
      .findOne({ business, currency, type: WalletTransactionType.Withdraw })
      .sort({ createdAt: -1 });
  }

  // Credit Actions
  async creditAffiliateEarning(
    ids: UserIds,
    amount: number,
    affiliateLinkTrackingId: string,
    data: { currency?: string; description?: string } = {},
  ): Promise<WalletTransaction> {
    return await this.credit(ids, {
      credit: amount,
      currency: data.currency || 'USD',
      description: data.description,
      type: WalletTransactionType.AffiliateEarning,
      affiliateLinkTracking: new ObjectId(affiliateLinkTrackingId),
    });
  }

  // Debit Actions
  async withdraw(
    ids: UserIds,
    amount: number,
    payoutId: string,
    data: { currency?: string; description?: string } = {},
  ): Promise<WalletTransaction> {
    return await this.debit(ids, {
      debit: amount,
      currency: data.currency || 'USD',
      description: data.description,
      type: WalletTransactionType.Withdraw,
      payout: new ObjectId(payoutId),
    });
  }

  private async getLastEntry(
    business: string,
    currency: string,
    session: ClientSession,
  ): Promise<{ balance: number; currency: string }> {
    let lastEntry = await this.walletTransaction
      .findOne({ business, currency })
      .select({ newBalance: 1, currency: 1 })
      .sort({ createdAt: -1 })
      .session(session);

    // If no last entry found, create opening entry with 0 credit and 0 balance
    const initialBalance = 10; // TODO: Remove once we stop giving away initial $10
    if (!lastEntry) {
      lastEntry = (
        await this.walletTransaction.create(
          [
            {
              credit: initialBalance,
              previousBalance: 0,
              newBalance: initialBalance,
              currency,
              type: WalletTransactionType.OpeningBalance,
              business: new ObjectId(business),
            } satisfies WalletTransaction,
          ],
          { session },
        )
      )[0];
    }

    return {
      balance: lastEntry?.newBalance || initialBalance,
      currency: lastEntry?.currency || currency,
    };
  }

  private async debit(
    { bid, uid }: UserIds,
    transactionData: TransactionDataType,
  ): Promise<WalletTransaction> {
    if (!transactionData.debit || transactionData.debit < 0) {
      throw new BadRequestException(`Debit amount is invalid.`);
    }

    const session = await this.business.db.startSession();
    session.startTransaction();
    try {
      const wallet = await this.getLastEntry(bid, transactionData.currency, session);
      if (wallet.balance < transactionData.debit) {
        throw new ForbiddenException('Insufficient funds to perform this action.');
      }

      const newWalletTransaction = await this.walletTransaction.create(
        [
          {
            ...transactionData,
            previousBalance: wallet.balance,
            newBalance: wallet.balance - transactionData.debit,
            business: new ObjectId(bid),
            user: uid ? new ObjectId(uid) : null,
          },
        ] satisfies WalletTransaction[],
        { session },
      );

      await session.commitTransaction();
      session.endSession();

      return newWalletTransaction[0];
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }

  private async credit(
    { bid, uid }: UserIds,
    transactionData: TransactionDataType,
  ): Promise<WalletTransaction> {
    if (!transactionData.credit || transactionData.credit < 0) {
      throw new BadRequestException(`Credit amount is invalid.`);
    }

    const session = await this.business.db.startSession();
    session.startTransaction();
    try {
      const wallet = await this.getLastEntry(bid, transactionData.currency, session);

      const newWalletTransaction = await this.walletTransaction.create(
        [
          {
            ...transactionData,
            previousBalance: wallet.balance,
            newBalance: wallet.balance + transactionData.credit,
            business: new ObjectId(bid),
            user: uid ? new ObjectId(uid) : null,
          },
        ],
        { session },
      );

      await session.commitTransaction();
      session.endSession();

      return newWalletTransaction[0];
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }
}
