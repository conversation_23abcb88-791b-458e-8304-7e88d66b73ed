import type { BusinessDocument } from '@/business/business.model';
import type { PayoutDocument } from '@/resources/wallet/payout/payout.model';
import type { UserDocument } from '@/user/user.model';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { AffiliateLinkTrackingDocument } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.model';

import { WalletTransactionType } from './wallet.enum';

@Schema({ collection: 'wallet-transactions', versionKey: false, timestamps: { updatedAt: false } })
export class WalletTransaction {
  @Prop({ type: Number })
  credit?: number;

  @Prop({ type: Number })
  debit?: number;

  @Prop({ type: Number, required: true, default: 0 })
  previousBalance: number;

  @Prop({ type: Number, required: true, default: 0 })
  newBalance: number;

  @Prop({
    type: String,
    required: true,
    minlength: 3,
    maxlength: 3,
    uppercase: true,
    default: 'USD',
  })
  currency: string;

  @Prop({ type: String, required: true, enum: Object.values(WalletTransactionType) })
  type: WalletTransactionType;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: ObjectId, ref: 'Business', required: true })
  business: ObjectId | BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User' })
  user?: ObjectId | UserDocument;

  @Prop({ type: ObjectId, ref: 'AffiliateLinkTracking' })
  affiliateLinkTracking?: ObjectId | AffiliateLinkTrackingDocument;

  @Prop({ type: ObjectId, ref: 'Payout' })
  payout?: ObjectId | PayoutDocument;
}

export type WalletTransactionDocument = WalletTransaction & Document;
export const WalletTransactionSchema = SchemaFactory.createForClass(WalletTransaction);
