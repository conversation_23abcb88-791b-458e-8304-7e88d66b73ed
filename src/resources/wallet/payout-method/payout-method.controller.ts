import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { PayoutMethod } from './payout-method.model';

import {
  NotFoundException,
  Controller,
  UseGuards,
  Headers,
  Query,
  Param,
  Patch,
  Body,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { ObjectId } from 'mongodb';

import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';

import { CreatePayoutMethodDto } from './dto/create-payout-method.dto';
import { UpdatePayoutMethodDto } from './dto/update-payout-method.dto';
import { PayoutMethodService } from './payout-method.service';

@Controller('payout-methods')
export class PayoutMethodController extends CrudController<
  PayoutMethod,
  CreatePayoutMethodDto,
  UpdatePayoutMethodDto
> {
  constructor(private readonly payoutMethodService: PayoutMethodService) {
    super(payoutMethodService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<PayoutMethod>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: new ObjectId(bid) };
    }
    return this.payoutMethodService.findAll(query);
  }

  @Post()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async create(
    @Body() body: CreatePayoutMethodDto,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<PayoutMethod> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.business = new ObjectId(bid);
      body.user = new ObjectId(uid);
    }

    return await super.create(body);
  }

  @Patch(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async update(
    @Param('id') id: string,
    @Body() body: UpdatePayoutMethodDto,
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<PayoutMethod> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: new ObjectId(bid) };
    }
    const payoutMethod = await this.payoutMethodService.findOneById(id, query);
    if (!payoutMethod) {
      throw new NotFoundException('Website not found.');
    }

    return super.update(id, body);
  }
}
