import type { BusinessDocument } from '@/business/business.model';
import type { UserDocument } from '@/user/user.model';
import type { BankInfo } from './payout-method.type';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

@Schema({ collection: 'payout-methods', timestamps: true, versionKey: false })
export class PayoutMethod {
  @Prop({ type: String, enum: ['bank'], required: true })
  type: 'bank';

  @Prop({ type: Object, required: true })
  info: BankInfo;

  @Prop({ type: ObjectId, ref: 'Business', required: true })
  business: ObjectId | BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User', required: true })
  user: ObjectId | UserDocument;
}

export type PayoutMethodDocument = PayoutMethod & Document;
export const PayoutMethodSchema = SchemaFactory.createForClass(PayoutMethod);
