import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { PayoutMethodSchema, PayoutMethod } from './payout-method.model';
import { PayoutMethodController } from './payout-method.controller';
import { PayoutMethodService } from './payout-method.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: PayoutMethod.name, schema: PayoutMethodSchema }])],
  controllers: [PayoutMethodController],
  providers: [PayoutMethodService],
  exports: [MongooseModule, PayoutMethodService],
})
export class PayoutMethodModule {}
