import type { ObjectId } from 'mongodb';

import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePayoutMethodDto {
  @ApiProperty()
  @IsEnum(['bank'])
  type: 'bank';

  @ApiProperty()
  @IsObject()
  info: Record<string, any>;

  @ApiProperty()
  @IsOptional()
  @IsString()
  business!: ObjectId;

  @ApiProperty()
  @IsOptional()
  @IsString()
  user!: ObjectId;
}
