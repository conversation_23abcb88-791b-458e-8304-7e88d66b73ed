import type { Model } from 'mongoose';

import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';

import { CrudService } from '@/crud/crud.service';

import { PayoutMethodDocument, PayoutMethod } from './payout-method.model';

@Injectable()
export class PayoutMethodService extends CrudService<PayoutMethodDocument> {
  constructor(
    @InjectModel(PayoutMethod.name) private readonly payoutMethod: Model<PayoutMethodDocument>,
  ) {
    super(payoutMethod);
  }
}
