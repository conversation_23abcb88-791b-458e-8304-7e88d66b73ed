import { MongooseModule } from '@nestjs/mongoose';
import { RouterModule } from '@nestjs/core';
import { Module } from '@nestjs/common';

import { BusinessModule } from '@/business/business.module';

import { WalletTransactionSchema, WalletTransaction } from './wallet-transaction.model';
import { PayoutMethodModule } from './payout-method/payout-method.module';
import { WalletController } from './wallet.controller';
import { WalletService } from './wallet.service';
import { PayoutModule } from './payout/payout.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: WalletTransaction.name, schema: WalletTransactionSchema }]),
    RouterModule.register([{ path: 'wallet', module: PayoutMethodModule }]),
    RouterModule.register([{ path: 'wallet', module: PayoutModule }]),
    PayoutMethodModule,
    BusinessModule,
    PayoutModule,
  ],
  controllers: [WalletController],
  providers: [WalletService],
  exports: [MongooseModule, WalletService],
})
export class WalletModule {}
