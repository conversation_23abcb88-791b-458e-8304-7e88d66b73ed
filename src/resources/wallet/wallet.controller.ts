import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { WalletTransaction } from './wallet-transaction.model';

import { Controller, UseGuards, Headers, Query, Get, Req } from '@nestjs/common';

import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { Auth } from '@/auth/guards/auth.guard';

import { WalletService } from './wallet.service';

@Controller('wallet/transactions')
export class WalletController extends CrudController<WalletTransaction, any, any> {
  constructor(private readonly walletService: WalletService) {
    super(walletService);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findAll(
    @Query() query: CrudQ<PERSON>y,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<WalletTransaction>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: bid };
    }
    return this.walletService.findAll(query);
  }
}
