import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { SlackModule } from '@/integrations/internal/slack/slack.module';

import { PayoutSchema, Payout } from './payout.model';
import { PayoutMethodModule } from '../payout-method/payout-method.module';
import { PayoutController } from './payout.controller';
import { PayoutService } from './payout.service';
import { WalletModule } from '../wallet.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Payout.name, schema: PayoutSchema }]),
    forwardRef(() => WalletModule),
    PayoutMethodModule,
    SlackModule,
  ],
  controllers: [PayoutController],
  providers: [PayoutService],
  exports: [MongooseModule, PayoutService],
})
export class PayoutModule {}
