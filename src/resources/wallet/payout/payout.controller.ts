import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import type { Payout } from './payout.model';

import {
  BadRequestException,
  NotFoundException,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Headers,
  Delete,
  Param,
  Patch,
  Query,
  Post,
  Body,
  Get,
  Req,
} from '@nestjs/common';
import { ObjectId } from 'mongodb';

import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { Auth } from '@/auth/guards/auth.guard';

import { PayoutMethodService } from '../payout-method/payout-method.service';
import { CreatePayoutDto } from './dto/create-payout.dto';
import { UpdatePayoutDto } from './dto/update-payout.dto';
import { PayoutService } from './payout.service';
import { WalletService } from '../wallet.service';
import { PayoutStatus } from './payout.enum';

const camelToSentenceCase = (input: string): string =>
  input.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/(^\w)/, (match) => match.toUpperCase());

@Controller('payouts')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class PayoutController extends CrudController<Payout, CreatePayoutDto, UpdatePayoutDto> {
  constructor(
    private readonly payoutMethodService: PayoutMethodService,
    private readonly payoutService: PayoutService,
    private readonly walletService: WalletService,
    private readonly slackService: SlackService,
  ) {
    super(payoutService);
  }

  @Get('')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<Payout>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { business: new ObjectId(bid) };
    }
    return this.payoutService.findAll(query);
  }

  @Post()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async create(
    @Body() body: CreatePayoutDto,
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<Payout> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      body.business = new ObjectId(bid);
      body.user = new ObjectId(uid);
    }

    const payoutMethod = await this.payoutMethodService.findOneById(
      body.payoutMethodId,
      {} as CrudQuery,
    );
    if (!payoutMethod) {
      throw new NotFoundException('Payout method not found');
    }

    const wallet = await this.walletService.getBalance(bid);
    if (wallet.balance < body.amount) {
      throw new BadRequestException('Insufficient funds.');
    }

    body.type = payoutMethod.type;
    body.payoutMethodInfo = payoutMethod.info;
    const payout = await super.create(body);

    await this.sendNewPayoutRequestAlert(payout);

    return payout;
  }

  @Patch(':id')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async update(@Param('id') id: string, @Body() updateDto: UpdatePayoutDto): Promise<Payout> {
    return this.payoutService.update(id, updateDto);
  }

  @Post(':id/approve')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async approvePayoutRequest(@Param('id') id: string) {
    const payoutRequest = await this.payoutService.findOneById(id, {} as CrudQuery);
    if (!payoutRequest?._id) {
      throw new NotFoundException('Payout request not found.');
    }

    try {
      await this.walletService.withdraw(
        { bid: String(payoutRequest.business), uid: String(payoutRequest.user) },
        payoutRequest.amount,
        payoutRequest._id,
      );

      await this.payoutService.update(id, {
        status: PayoutStatus.Successful,
      });
    } catch (e) {
      if (e.message.includes('Insufficient funds')) {
        await this.payoutService.update(id, {
          status: PayoutStatus.Failed,
        });
      }
    }

    return { success: true };
  }

  @Delete(':id/reject')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async rejectPayoutRequest(@Param('id') id: string) {
    await this.payoutService.update(id, {
      status: PayoutStatus.Failed,
    });

    return { success: true };
  }

  private async sendNewPayoutRequestAlert(payout: Payout) {
    await this.slackService.sendMessage({
      channel: 'payout-requests',
      message: `Payout request for ${payout.amount} has been made by Business ID: ${payout.business} and user ID: ${payout.user}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: [
              `:money_with_wings: *Payout Request*`,
              `*Business ID:* ${payout.business}`,
              `*User ID:* ${payout.user}`,
              `Payout Method Type: ${payout.type}`,
              `*Amount:* ${payout.amount}`,
              `*Payout Details:*`,
              Object.entries(payout.payoutMethodInfo)
                .map(([key, value]) => `\t• *${camelToSentenceCase(key)}:* ${value}`)
                .join('\n'),
            ].join('\n'),
          },
        },
      ],
    });
  }
}
