import type { ObjectId } from 'mongodb';

import { IsOptional, IsNumber, IsObject, IsString, IsEnum, Min } from 'class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { BankInfo } from '../../payout-method/payout-method.type';

export class CreatePayoutDto {
  @ApiProperty()
  @IsNumber()
  @Min(50)
  amount: number;

  @ApiProperty()
  @IsString()
  payoutMethodId: string;

  @ApiHideProperty()
  @IsOptional()
  @IsEnum(['bank'])
  type!: 'bank';

  @ApiHideProperty()
  @IsOptional()
  @IsObject()
  payoutMethodInfo!: BankInfo;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  business!: ObjectId;

  @ApiHideProperty()
  @IsOptional()
  @IsString()
  user!: ObjectId;
}
