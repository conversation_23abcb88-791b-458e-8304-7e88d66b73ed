import type { Model } from 'mongoose';

import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';

import { CrudService } from '@/crud/crud.service';

import { PayoutDocument, Payout } from './payout.model';

@Injectable()
export class PayoutService extends CrudService<PayoutDocument> {
  constructor(@InjectModel(Payout.name) private readonly payout: Model<PayoutDocument>) {
    super(payout);
  }
}
