import type { BusinessDocument } from '@/business/business.model';
import type { UserDocument } from '@/user/user.model';
import type { BankInfo } from '../payout-method/payout-method.type';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { PayoutStatus } from './payout.enum';

@Schema({ collection: 'payouts', timestamps: true, versionKey: false })
export class Payout {
  @Prop({ type: Number, required: true, min: 50 })
  amount: number;

  @Prop({
    type: String,
    enum: Object.values(PayoutStatus),
    required: true,
    default: PayoutStatus.Initiated,
  })
  status: PayoutStatus;

  @Prop({ type: String })
  transactionId: string;

  @Prop({ type: String, enum: ['bank'], required: true })
  type: 'bank';

  @Prop({ type: Object, required: true })
  payoutMethodInfo: BankInfo;

  @Prop({ type: ObjectId, ref: 'Business', required: true })
  business: ObjectId | BusinessDocument;

  @Prop({ type: ObjectId, ref: 'User', required: true })
  user: ObjectId | UserDocument;
}

export type PayoutDocument = Payout & Document;
export const PayoutSchema = SchemaFactory.createForClass(Payout);
