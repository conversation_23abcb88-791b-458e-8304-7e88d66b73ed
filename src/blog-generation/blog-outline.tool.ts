import { BlogSize, BlogSourceType, BlogTldrPosition } from '@/blog/blog.enums';
import { Type } from '@sinclair/typebox';

export const getSectionSchema = (
  sourceType: BlogSourceType,
  hasTimestampedTranscript: boolean,
  blogSize: BlogSize,
) => {
  const numberOfItems = blogSize === BlogSize.small ? 2 : blogSize === BlogSize.medium ? 4 : 6;
  const isTranscript = sourceType === BlogSourceType.audio || sourceType === BlogSourceType.video;
  const SectionSchema = Type.Object({
    heading: Type.String({
      description: 'The heading of the section',
    }),
    bulletPoints: Type.Array(
      Type.String({
        description: 'All bullet points under the section',
      }),
      {
        minItems: numberOfItems,
      },
    ),
    notes: Type.Array(
      Type.String({
        description:
          'Notes related to the section content (e.g., additional information, references, key topics, all the data, numbers, dates and unique terms/keywords/brands/services mentioned in the original text)',
      }),
      {
        minItems: numberOfItems,
      },
    ),
    data: Type.Array(
      Type.String({
        description:
          'All the data points in the section (e.g., numbers, dates, percentages, statistics, etc.) to generate a data table or chart if available',
      }),
    ),
    quotes: Type.Array(
      Type.String({
        description: isTranscript
          ? 'A quote from the transcript section or from an industry expert with name if available. Do not mention anonymous user or placeholder instead do not mention any name if not available'
          : 'A quote from an industry expert with name. Do not mention anonymous user instead keep the name blank if not available',
      }),
    ),
    relevantContent: Type.String({
      description: `All relevant contents from input text for the section.`,
    }),
    svgPrompt: Type.String({
      description:
        'Write a prompt to generate SVG image like line chart, column chart, donut chart, bar chart, comparison table or mind map from the data and information, If there is no data from which we can generate an SVG image just return an empty string',
    }),
    shouldGenerateTable: Type.Boolean({
      description: 'If the section should generate a table',
    }),
    shouldGenerateChart: Type.Boolean({
      description: 'If the section should generate a chart',
    }),
  });

  if (hasTimestampedTranscript) {
    // Update the schema to include all timestamps from the transcript
    SectionSchema.properties['captionIndexes'] = Type.Array(
      Type.Object({
        startCaptionIndex: Type.Integer({
          description: 'The start index of the caption',
        }),
        endCaptionIndex: Type.Integer({
          description: 'The end index of the caption',
        }),
      }),
      {
        description:
          'Find all the captions in the transcript that are related to this section or cover any topics from the section',
      },
    );
    SectionSchema.required.push('captionIndexes');
  }

  return SectionSchema;
};

export const getBlogOutlineSchema = (
  sourceType: BlogSourceType,
  hasTimestampedTranscript: boolean,
  blogSize: BlogSize,
  tldrPosition?: BlogTldrPosition,
) => {
  const numberOfSections = blogSize === BlogSize.small ? 3 : blogSize === BlogSize.medium ? 5 : 7;
  const SectionSchema = getSectionSchema(sourceType, hasTimestampedTranscript, blogSize);

  const BlogOutlineSchema = Type.Object({
    title: Type.String({
      description: 'The title of the blog post',
    }),
    keywords: Type.Array(Type.String(), {
      description:
        'SEO-optimized keywords including long-tail keywords (2-4 words) related to the blog post. Focus on search intent, relevance, and search volume. Include both broad and specific terms.',
      minItems: 8,
      maxItems: 15,
    }),
    hashtags: Type.Array(Type.String(), {
      description:
        'Social media hashtags relevant to the blog post. Include both popular and niche hashtags. Do not include spaces, start each with #.',
      minItems: 5,
      maxItems: 10,
    }),
    summary: Type.String({
      description: 'The summary of the blog post',
    }),
    targetAudience: Type.String({
      description: 'The target audience for the blog post',
    }),
    introduction: Type.String({
      description: 'The intro of the blog post',
    }),
    ...(tldrPosition !== BlogTldrPosition.none
      ? {
          tldr: Type.String({
            description:
              tldrPosition === BlogTldrPosition.start
                ? 'The TLDR of the blog post, to be placed at the beginning'
                : 'The TLDR of the blog post, to be placed at the end',
          }),
        }
      : {}),
    sections: Type.Array(SectionSchema, {
      description: `Write approximately ${numberOfSections} sections outline or more with headings, bullet points, notes, data, quotes, keywords ${
        hasTimestampedTranscript ? 'and all the caption indexes' : ''
      } `,
      minItems: numberOfSections,
    }),
    metaDescription: Type.String({
      description: 'The meta description for the blog post for SEO optimization',
    }),
    metaTags: Type.Array(Type.String(), {
      description:
        'The meta tags for the blog post for SEO optimization, including key phrases and topics',
    }),
  });

  return BlogOutlineSchema;
};
