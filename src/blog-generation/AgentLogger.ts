import * as fs from 'fs';
import * as path from 'path';
import { logger } from '@/common/logger';

/**
 * Ensure a directory exists, creating it if necessary
 */
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    try {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.debug(`Created directory at ${dirPath}`);
    } catch (error) {
      logger.error(`Failed to create directory: ${error}`);
    }
  }
}

/**
 * Class for logging agent activities, inputs, outputs, and costs
 */
export class AgentLogger {
  private debugDir: string;
  private logsDir: string;
  private isDebugMode: boolean;
  private sessionId: string;
  private currentLogFile: string;

  constructor() {
    this.debugDir = path.join(process.cwd(), 'debug_logs');
    this.logsDir = path.join(process.cwd(), 'logs', 'agents');
    this.isDebugMode = process.env.NODE_ENV === 'debug';
    this.sessionId = `session_${Date.now()}`;
    this.currentLogFile = `agent-logs-${new Date().toISOString().replace(/:/g, '-')}.log`;

    // Create debug directory if in debug mode
    if (this.isDebugMode) {
      ensureDirectoryExists(this.debugDir);
    }
  }

  /**
   * Set session ID for grouping related logs
   */
  setSessionId(id: string): void {
    this.sessionId = id;
  }

  /**
   * Get current session ID
   */
  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Log agent input
   */
  logAgentInput(agentName: string, input: any): void {
    // Debug mode file logging
    if (this.isDebugMode) {
      try {
        const fileName = `${this.sessionId}_${agentName}_input_${Date.now()}.json`;
        const filePath = path.join(this.debugDir, fileName);

        fs.writeFileSync(filePath, JSON.stringify(input, null, 2), { encoding: 'utf8' });

        logger.debug(`Logged ${agentName} input to ${filePath}`);
      } catch (error) {
        logger.error(`Failed to log agent input: ${error}`);
      }
    }

    // Runtime logging
    this.writeAgentLog(
      `[${new Date().toISOString()}] [INPUT] [${agentName}] ${JSON.stringify(input, null, 2)}`,
    );
  }

  /**
   * Log agent output
   */
  logAgentOutput(agentName: string, output: any): void {
    // Debug mode file logging
    if (this.isDebugMode) {
      try {
        const fileName = `${this.sessionId}_${agentName}_output_${Date.now()}.json`;
        const filePath = path.join(this.debugDir, fileName);

        fs.writeFileSync(filePath, JSON.stringify(output, null, 2), { encoding: 'utf8' });

        logger.debug(`Logged ${agentName} output to ${filePath}`);
      } catch (error) {
        logger.error(`Failed to log agent output: ${error}`);
      }
    }

    // Runtime logging
    this.writeAgentLog(
      `[${new Date().toISOString()}] [OUTPUT] [${agentName}] ${JSON.stringify(output, null, 2)}`,
    );
  }

  /**
   * Log tool calls
   */
  logToolCall(agentName: string, toolName: string, params: any, result: any): void {
    // Debug mode file logging
    if (this.isDebugMode) {
      try {
        const fileName = `${this.sessionId}_${agentName}_tool_${toolName}_${Date.now()}.json`;
        const filePath = path.join(this.debugDir, fileName);

        fs.writeFileSync(
          filePath,
          JSON.stringify(
            {
              agent: agentName,
              tool: toolName,
              parameters: params,
              result: result,
            },
            null,
            2,
          ),
          { encoding: 'utf8' },
        );

        logger.debug(`Logged ${agentName} tool call to ${filePath}`);
      } catch (error) {
        logger.error(`Failed to log tool call: ${error}`);
      }
    }

    // Runtime logging
    this.writeAgentLog(
      `[${new Date().toISOString()}] [TOOL] [${agentName}] ${toolName} - Params: ${JSON.stringify(params)} Result: ${JSON.stringify(result)}`,
    );
  }

  /**
   * Log usage and cost information
   */
  logUsageAndCost(agentName: string, modelName: string, usage: any, estimatedCost: number): void {
    // Debug mode file logging
    if (this.isDebugMode) {
      try {
        const fileName = `${this.sessionId}_${agentName}_cost_${Date.now()}.json`;
        const filePath = path.join(this.debugDir, fileName);

        fs.writeFileSync(
          filePath,
          JSON.stringify(
            {
              agent: agentName,
              model: modelName,
              usage: usage,
              estimatedCost: estimatedCost,
              timestamp: new Date().toISOString(),
            },
            null,
            2,
          ),
          { encoding: 'utf8' },
        );

        logger.debug(`Logged ${agentName} usage and cost to ${filePath}`);
      } catch (error) {
        logger.error(`Failed to log usage and cost: ${error}`);
      }
    }

    // Runtime logging
    this.writeAgentLog(
      `[${new Date().toISOString()}] [COST] [${agentName}] Model: ${modelName} ` +
        `Tokens: ${usage?.total_tokens || 0} ` +
        `Cost: $${estimatedCost.toFixed(5)}`,
    );
  }

  /**
   * Write to the agent log file, creating the directory if needed
   */
  private writeAgentLog(message: string): void {
    try {
      // Make sure logs directory exists
      ensureDirectoryExists(this.logsDir);

      // Build log file path
      const logFilePath = path.join(this.logsDir, this.currentLogFile);

      // Append to log file
      fs.appendFileSync(logFilePath, message + '\n', { encoding: 'utf8' });
    } catch (error) {
      logger.error(`Failed to write agent log: ${error}`);
    }
  }

  /**
   * Combine all logs for a session into a single report
   */
  generateSessionReport(): string | null {
    if (!this.isDebugMode) return null;

    try {
      // Find all log files for the current session
      const sessionFiles = fs
        .readdirSync(this.debugDir)
        .filter((file) => file.startsWith(this.sessionId))
        .sort();

      if (sessionFiles.length === 0) {
        return null;
      }

      // Create report data structure
      const report = {
        sessionId: this.sessionId,
        timestamp: new Date().toISOString(),
        steps: [] as any[],
        totalEstimatedCost: 0,
      };

      // Process each file to build the report
      for (const file of sessionFiles) {
        const filePath = path.join(this.debugDir, file);
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const fileData = JSON.parse(fileContent);

        // Extract type from filename (input, output, tool, cost)
        const fileType = file.includes('_input_')
          ? 'input'
          : file.includes('_output_')
            ? 'output'
            : file.includes('_tool_')
              ? 'tool'
              : file.includes('_cost_')
                ? 'cost'
                : 'unknown';

        // Extract agent name from filename
        const agentName = file.split('_')[1];

        // Add to report based on type
        if (fileType === 'cost') {
          report.totalEstimatedCost += fileData.estimatedCost || 0;
        }

        report.steps.push({
          timestamp: new Date(parseInt(file.split('_').pop() || '0')).toISOString(),
          agent: agentName,
          type: fileType,
          data: fileData,
        });
      }

      // Write report to file
      const reportFileName = `${this.sessionId}_report.json`;
      const reportFilePath = path.join(this.debugDir, reportFileName);

      fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), { encoding: 'utf8' });

      logger.debug(`Generated session report at ${reportFilePath}`);
      return reportFilePath;
    } catch (error) {
      logger.error(`Failed to generate session report: ${error}`);
      return null;
    }
  }
}

// Create a singleton instance
export const agentDebugLogger = new AgentLogger();
