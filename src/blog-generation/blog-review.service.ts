import { JOB_QUEUES } from '@/common/constants';
import { agentLogger } from '@/common/logger';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { BlogSize, BlogTone } from '../blog/blog.enums';
import { SentencePair } from './agents/HumanizationAgent';
import { ReviewAgent } from './agents/ReviewAgent';

/**
 * Service to handle blog content review and optimization
 * This service uses Claude 3.7 to make content human-like and undetectable by AI content detectors
 */
@Injectable()
export class BlogReviewService {
  private readonly logger = new Logger(BlogReviewService.name);
  private reviewAgent: ReviewAgent;

  constructor(@InjectQueue(JOB_QUEUES.BLOG_REVIEW) private blogReviewQueue: Queue) {
    // Initialize the ReviewAgent for direct processing if needed
    this.reviewAgent = new ReviewAgent({
      provider: 'anthropic',
      model: 'claude-3-7-sonnet-20250219',
      identifierName: 'blogId',
      identifierValue: 'review-service',
    });
  }

  /**
   * Queue blog content for review and optimization
   * Adds a job to the queue for processing by the BlogReviewProcessor
   */
  async reviewBlogContent(params: {
    blogId: string;
    content: string;
    title: string;
    blogTone: BlogTone;
    language: string;
    perspective?: string;
    seoKeywords?: string[];
    blogSize?: BlogSize;
    wordCountApprox?: number;
    targetAudience?: string;
    sourceType?: string;
    sourceName?: string;
    transcription?: string;
    prompt?: string;
    sentencePairs?: SentencePair[];
  }): Promise<boolean> {
    const {
      blogId,
      content,
      title,
      blogTone,
      language,
      perspective = 'second-person',
      seoKeywords = [],
      blogSize,
      wordCountApprox,
      targetAudience = 'general',
      sourceType,
      sourceName,
      transcription,
      prompt,
      sentencePairs = [],
    } = params;

    if (!content || content.trim() === '') {
      this.logger.error(`Empty content provided for blog review: ${blogId}`);
      return false;
    }

    try {
      this.logger.log(`Queueing blog review job for blog: ${blogId}`);

      // Calculate approximate word count based on blog size
      let targetWordCount = wordCountApprox;
      if (!targetWordCount && blogSize) {
        switch (blogSize.toLowerCase()) {
          case 'very_small':
            targetWordCount = 500;
            break;
          case 'small':
            targetWordCount = 800;
            break;
          case 'medium':
            targetWordCount = 1200;
            break;
          case 'large':
            targetWordCount = 2000;
            break;
          case 'very_large':
            targetWordCount = 3000;
            break;
          default:
            targetWordCount = 1200; // Default to medium
        }
      }

      // Log that we're adding the job to the queue
      agentLogger.logEvent('ReviewService', 'Queueing blog review job', {
        blogId,
        title,
        blogTone,
        language,
        perspective,
        seoKeywordsCount: seoKeywords?.length || 0,
        targetWordCount,
        targetAudience,
        contentLength: content.length,
        hasTranscription: !!transcription,
        hasPrompt: !!prompt,
        hasSentencePairs: sentencePairs.length > 0,
        sentencePairsCount: sentencePairs.length,
        sourceType,
        sourceName,
      });

      // Add the job to the queue
      await this.blogReviewQueue.add(
        'review',
        {
          blogId,
          content,
          title,
          blogTone,
          language,
          perspective,
          seoKeywords,
          wordCountApprox: targetWordCount,
          targetAudience,
          sourceType,
          sourceName,
          transcription,
          prompt,
          sentencePairs,
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: 50, // Keep the last 50 completed jobs
        },
      );

      this.logger.log(`Successfully queued review job for blog: ${blogId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error queueing blog review job for blog ${blogId}: ${error.message}`);
      agentLogger.logError('ReviewService', `Error queueing blog review job: ${error.message}`);
      return false;
    }
  }

  /**
   * Direct review of blog content using the ReviewAgent
   * This can be used for synchronous processing without queuing
   */
  async processReviewDirectly(params: {
    blogId: string;
    content: string;
    title: string;
    blogTone: BlogTone;
    blogLanguage: string;
    perspective?: string;
    seoKeywords?: string[];
    wordCountApprox?: number;
    targetAudience?: string;
    sentencePairs?: SentencePair[];
  }): Promise<string> {
    try {
      this.logger.log(`Directly processing review for blog: ${params.blogId}`);

      // Start the review process
      const startTime = Date.now();
      const reviewedContent = await this.reviewAgent.run({
        content: params.content,
        metadata: {
          title: params.title,
          blogTone: params.blogTone,
          blogLanguage: params.blogLanguage,
          perspective: params.perspective || 'second-person',
          seoKeywords: params.seoKeywords || [],
          wordCountApprox: params.wordCountApprox || 1200,
          targetAudience: params.targetAudience || 'general',
        },
      });
      const endTime = Date.now();

      // Log the completion of the review
      this.logger.log(
        `Completed direct review for blog ${params.blogId} in ${endTime - startTime}ms`,
      );

      return reviewedContent;
    } catch (error) {
      this.logger.error(
        `Error in direct blog review process for blog ${params.blogId}: ${error.message}`,
      );
      return params.content; // Return the original content in case of error
    }
  }
}
