import {
  Controller,
  Post,
  Query,
  Sse,
  Body,
  Logger,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { BlogGenerationService } from './blog-generation.service';
import { Observable, Subject } from 'rxjs';
import { MessageEvent } from '@nestjs/common';
import { ModelResponse } from '@llm/types';
import { Auth } from '@/auth/guards/auth.guard';
import pRetry from 'p-retry';

@Controller('blog-generation')
export class BlogGenerationController {
  private readonly logger = new Logger(BlogGenerationController.name);
  private requestStore = new Map<string, Subject<MessageEvent>>();
  private completedStore = new Map<string, { result: any; timestamp: number }>();

  constructor(private readonly blogGenerationService: BlogGenerationService) {
    // Set up a periodic cleanup for the completed store
    setInterval(() => this.cleanupCompletedStore(), 60 * 1000); // Cleanup every minute
  }

  @Post('rewrite')
  @UseGuards(Auth)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async rewrite(
    @Body('mode') mode: 'extend' | 'rephrase',
    @Body('text') text: string,
    @Body('tone') tone: string,
    @Body('keywords') keywords: string[],
  ) {
    return pRetry(
      () =>
        this.blogGenerationService
          .rewriteBlogSection(mode, text, tone, keywords)
          .then((output) => ({ content: output })),
      {
        retries: 3,
      },
    );
  }

  @Post('rephrase-section')
  async handleRephraseSection(
    @Body('model') model: string,
    @Body('originalText') originalText: string,
    @Body('tone') tone: string,
    @Body('language') language: string,
  ): Promise<{ requestId: string }> {
    const requestId = `${Date.now()}-${Math.random()}`;
    const subject = new Subject<MessageEvent>();
    this.requestStore.set(requestId, subject);

    const stream = this.blogGenerationService.rephraseBlogSection(
      model,
      originalText,
      tone,
      language,
    );

    this.processStream(stream, subject, requestId);

    return { requestId };
  }

  private async processStream(
    stream: AsyncGenerator<any, ModelResponse>,
    subject: Subject<MessageEvent>,
    requestId: string,
  ) {
    console.log('processStream', stream, subject, requestId);
    try {
      let current = await stream.next();
      while (!current.done) {
        if (current.value.delta) {
          subject.next({ data: { delta: current.value?.delta?.content } });
        }
        current = await stream.next();
      }

      // Process the final completion
      const completion = current.value;
      console.log('final completion', current);
      this.logger.debug(
        {
          message: completion.message || {},
          usage: completion.usage || {},
        },
        'got final completion',
      );

      subject.next({ data: { done: true, content: completion?.message?.content } });
      subject.complete();

      // Store the result in the completed store
      this.completedStore.set(requestId, {
        result: completion?.message?.content,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(error, 'Error processing stream');
      subject.error(error);
    } finally {
      this.requestStore.delete(requestId);
    }
  }

  @Sse('rephrase-section-stream')
  rephraseBlogSectionStream(@Query('requestId') requestId: string): Observable<MessageEvent> {
    const subject = this.requestStore.get(requestId);

    if (subject) {
      return subject.asObservable();
    }

    const completed = this.completedStore.get(requestId);
    console.log('CML', completed);
    if (completed) {
      console.log(completed);
      return new Observable<MessageEvent>((observer) => {
        observer.next({ data: completed.result });
        observer.complete();
      });
    }

    return new Observable<MessageEvent>((observer) => {
      observer.next({ data: 'Request ID not found or already processed.' });
      observer.complete();
    });
  }

  private cleanupCompletedStore() {
    const cutoff = Date.now() - 15 * 60 * 1000; // 15 minutes ago
    for (const [key, value] of this.completedStore.entries()) {
      if (value.timestamp < cutoff) {
        this.completedStore.delete(key);
      }
    }
  }
}
