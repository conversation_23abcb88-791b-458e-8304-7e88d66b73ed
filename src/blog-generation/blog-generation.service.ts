import { agentLogger } from '@/common/logger';
import { S3Service } from '@/common/services/s3.service';
import { AnthropicProvider, ModelResponse, OpenAiProvider, PartialModelResponse } from '@/llm';
import { getProviderName, ModelPricing } from '@/llm/llm.models';
import { YouTubeTranscript } from '@/youtube/youtube-content/youtube-content.interface';
import {
  BlogSize,
  BlogSourceName,
  BlogSourceType,
  BlogTldrPosition,
  BlogTone,
} from '@blog/blog.enums';
import { Blog } from '@blog/blog.model';
import { countTokens, replacePlaceholders } from '@common/helpers';
import { BaseProvider } from '@llm/providers/base.provider';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Type } from '@sinclair/typebox';
import { outdent } from 'outdent';
import pRetry from 'p-retry';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { BlogGenerationOrchestrator } from './agents';
import {
  GENERATE_BLOG_OUTLINE_SYSTEM_PROMPT,
  GENERATE_BLOG_OUTLINE_USER_PROMPT,
  GENERATE_CHART_INSTRUCTIONS,
  GENERATE_SUMMARY_SYSTEM_PROMPT,
  GENERATE_SUMMARY_USER_PROMPT,
  GENERATE_TABLE_INSTRUCTIONS,
} from './blog-generation.prompts';
import { getBlogOutlineSchema } from './blog-outline.tool';
import { BlogReviewService } from './blog-review.service';

const predefinedColors = [
  '#D1D8DC',
  '#00E2B1',
  '#CCD7E6',
  '#54D3DA',
  '#A2ACE0',
  '#41B8DD',
  '#FBD0B9',
];

enum LlmOperation {
  GENERATE_SUMMARY = 'generateSummary',
  GENERATE_OUTLINE = 'generateOutline',
  GENERATE_BLOG_SECTION = 'generateBlogSection',
  GENERATE_SVG = 'generateSvg',
  GENERATE_BLOG_TITLE = 'generateBlogTitle',
  GENERATE_FULL_BLOG = 'generateFullBlog',
  REPHRASE_SECTION = 'rephraseSection',
  GENERATE_KUDOS = 'generateKudos',
}

@Injectable()
export class BlogGenerationService {
  private readonly logger = new Logger(BlogGenerationService.name);
  private providers: Map<string, BaseProvider>;
  private readonly maxRetries: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
    private readonly blogReviewService: BlogReviewService,
  ) {
    this.providers = new Map();
    this.providers.set(
      OpenAiProvider.PROVIDER_NAME,
      new OpenAiProvider(this.configService.get('OPENAI_API_KEY')),
    );
    this.providers.set(
      AnthropicProvider.PROVIDER_NAME,
      new AnthropicProvider(this.configService.get('ANTHROPIC_API_KEY')),
    );
    this.maxRetries = 3;
  }

  private async retryLlmCall<T>(
    operation: () => Promise<T>,
    operationType: LlmOperation,
  ): Promise<T> {
    return pRetry(
      async () => {
        try {
          return await operation();
        } catch (error) {
          this.logger.error(`Error in LLM call for ${operationType}`, error);
          throw error;
        }
      },
      {
        retries: this.maxRetries,
        onFailedAttempt: (error) => {
          this.logger.warn(
            `LLM call attempt ${error.attemptNumber} failed for ${operationType}. ${
              this.maxRetries - error.attemptNumber
            } attempts remaining.`,
          );
        },
      },
    );
  }

  async rewriteBlogSection(
    mode: 'extend' | 'rephrase',
    text: string,
    tone: string,
    keywords: string[],
  ): Promise<string> {
    const provider = getProviderName('gpt-4o-mini');
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: gpt-4o-mini`);
    }

    const systemPrompt = `You are an AI assistant specialized in rewriting blog content.
Guidelines:
- ${mode === 'extend' ? 'Expand the content up to double the original length' : 'Keep similar length to original'}
- Write in a ${tone.toLowerCase()} tone
- Each keyword should be used naturally and strategically
- Content should remain:
  * Engaging and readable
  * Well-structured
  * Factually accurate
  * True to the original meaning
- Follow best practices:
  * Maintain natural flow
  * Use original content as base
  * Include key points from original
  * Create smooth transitions`;

    const userPrompt = `${mode === 'extend' ? 'Expand' : 'Rephrase'} the following blog section:

Original Text:
${text}

Requirements:
1. ${mode === 'extend' ? 'Expand content up to 2x length' : 'Keep similar length'}
2. Maintain a ${tone.toLowerCase()} tone
3. Naturally incorporate these keywords: ${keywords.join(', ')}
4. Preserve the main message and key points
5. Make content engaging and valuable to readers

Rewrite the content now.`;
    const ContentSchema = Type.Object({
      content: Type.String(),
    });
    try {
      const response = await llmProvider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'generate_content',
            description: 'Generate SEO-optimized content',
            parameters: ContentSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const result = response.message.toolCalls[0].args as { content: string };
        return result.content;
      } else throw new Error('Failed to generate Content Snippet');
    } catch (error) {
      this.logger.error(
        `Error ${mode === 'extend' ? 'expanding' : 'rephrasing'} blog section:`,
        error,
      );
      throw error;
    }
  }

  async *rephraseBlogSection(
    model: string,
    originalText: string,
    tone: string,
    language: string,
    maxCharacters?: number,
  ): AsyncGenerator<PartialModelResponse | ModelResponse> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const systemPrompt = `You are a content writer for a blog. You have been tasked with rephrasing a part of a blog post. Keep the output size to a minimum of ${maxCharacters ? maxCharacters + 'characters' : 'input text size'}. Keep it human readable use easy words and simple sentences to avoid plagiarism and make it unique.`;
    const message = `Rephrase the following blog section in ${language} with a ${tone} tone:\n\n${originalText}`;

    const token = Math.round(countTokens(originalText) * 1.5);
    const stream = llmProvider.chatCompletionStream({
      messages: [{ role: 'user', content: message }],
      model,
      opts: {
        max_tokens: token > 4000 ? 4000 : token,
        temperature: 0.5,
        top_p: 1,
      },
      systemPrompt,
    });

    let current = await stream.next();

    while (!current.done) {
      yield current.value;
      current = await stream.next();
    }

    const completion = current;

    return completion;
  }

  async generateSummary({
    model,
    context,
    language,
    tone,
    pov,
    sourceType,
    sourceName,
  }: {
    model: string;
    context: string;
    language: string;
    tone: string;
    pov: string;
    sourceType: BlogSourceType;
    sourceName: BlogSourceName;
  }): Promise<{ content: string; metadata?: { estimatedCost: number } }> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const source = this.getSourceAlias(sourceType, sourceName);
    const systemPrompt = replacePlaceholders(GENERATE_SUMMARY_SYSTEM_PROMPT, {
      source,
    });
    const userPrompt = replacePlaceholders(GENERATE_SUMMARY_USER_PROMPT, {
      source,
      language,
      tone,
      pov,
      context,
    });

    const response = {
      content: '',
      metadata: {
        estimatedCost: 0,
      },
    };

    await this.retryLlmCall(async () => {
      const resp = await llmProvider.chatCompletion({
        systemPrompt,
        messages: [{ role: 'user', content: userPrompt }],
        model,
        tools: [],
      });
      response.content = resp.message.content;

      // Calculate and track cost
      if (resp.usage) {
        const { inputTokens, outputTokens } = resp.usage;
        const providerKey = provider.toLowerCase();
        const modelInfo = ModelPricing[providerKey]?.[model];
        if (modelInfo) {
          const inputCost = inputTokens * modelInfo.pricing.inputTokensCost;
          const outputCost = outputTokens * modelInfo.pricing.outputTokensCost;
          response.metadata.estimatedCost = inputCost + outputCost;
          this.logger.debug(
            `Cost for summary generation: $${response.metadata.estimatedCost.toFixed(6)} (${inputTokens} input, ${outputTokens} output tokens)`,
          );
        }
      }
    }, LlmOperation.GENERATE_SUMMARY);

    return response;
  }

  async generateOutline({
    model,
    context,
    language,
    blogTone,
    blogSize,
    pov,
    sourceType,
    sourceName,
    transcript,
    customInstruction,
    tableOption,
    chartOption,
    tldrPosition,
    seoInputKeywords,
    seoOptimizationData,
    title,
    keywords,
  }: {
    model: string;
    context: string;
    language: string;
    blogTone: BlogTone;
    blogSize: BlogSize;
    pov: string;
    sourceType: BlogSourceType;
    sourceName: BlogSourceName;
    transcript?: YouTubeTranscript[];
    customInstruction?: string;
    tableOption?: string;
    chartOption?: string;
    tldrPosition?: BlogTldrPosition;
    seoInputKeywords?: string[];
    seoOptimizationData?: object[];
    title?: string;
    keywords?: string[];
  }): Promise<{ outline: any; metadata?: { estimatedCost: number } }> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const transcriptText = transcript
      ?.map((t, i) => `${i}. ${t.startTime.toFixed(2)} - ${t.endTime.toFixed(2)}: ${t.text}`)
      .join('\n');

    const source = this.getSourceAlias(sourceType, sourceName);
    const systemPrompt = replacePlaceholders(GENERATE_BLOG_OUTLINE_SYSTEM_PROMPT, {
      source,
    });

    const tableGenerationInstruction =
      tableOption === 'No Table'
        ? ''
        : replacePlaceholders(GENERATE_TABLE_INSTRUCTIONS, { tableOption });

    const chartGenerationInstruction =
      chartOption === 'No Chart'
        ? ''
        : replacePlaceholders(GENERATE_CHART_INSTRUCTIONS, { chartOption });

    const seoInstructions = seoOptimizationData?.length
      ? outdent`
        - Review the SEO optimization data: ${JSON.stringify(seoOptimizationData)}
        - From the SEO data, identify and incorporate the frequently used keywords that are relevant to the blog content
        - Focus on proper keywords that naturally fit the blog's context
        - Do not force keywords if they don't match the blog's topic
        `
      : '';
    const maxContextLength = 20000;
    const includeTitle = title ? `Must use the following title: ${title}\n` : '';
    const allKeywords = [...(keywords || []), ...(seoInputKeywords || [])];
    const includeKeywords = allKeywords?.length
      ? `Must use the following keywords: ${allKeywords.join(', ')} \n`
      : '';
    const userPrompt = replacePlaceholders(GENERATE_BLOG_OUTLINE_USER_PROMPT, {
      source,
      language,
      tone: blogTone,
      size: blogSize,
      pov,
      context: (transcriptText || context).slice(0, maxContextLength),
      extra: customInstruction,
      tableGenerationInstruction,
      chartGenerationInstruction,
      includeTldr: tldrPosition !== BlogTldrPosition.none ? ', TL;DR' : '',
      chartOption,
      tableOption,
      seoInstructions,
      includeTitle,
      includeKeywords,
    });

    const BlogOutlineSchema = getBlogOutlineSchema(
      sourceType,
      !!transcriptText,
      blogSize,
      tldrPosition,
    );

    const response = {
      outline: null,
      metadata: {
        estimatedCost: 0,
      },
    };

    await this.retryLlmCall(async () => {
      const resp = await llmProvider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model,
        systemPrompt,
        tools: [
          {
            name: 'generate_blog_outline',
            description: 'Generate outline for the blog post from the given prompt',
            parameters: BlogOutlineSchema,
            strict: true,
          },
        ],
      });

      response.outline = resp.message.toolCalls?.[0]?.args as typeof BlogOutlineSchema;

      // Calculate and track cost
      if (resp.usage) {
        const { inputTokens, outputTokens } = resp.usage;
        const providerKey = provider.toLowerCase();
        const modelInfo = ModelPricing[providerKey]?.[model];
        if (modelInfo) {
          const inputCost = inputTokens * modelInfo.pricing.inputTokensCost;
          const outputCost = outputTokens * modelInfo.pricing.outputTokensCost;
          response.metadata.estimatedCost = inputCost + outputCost;
          this.logger.debug(
            `Cost for outline generation: $${response.metadata.estimatedCost.toFixed(6)} (${inputTokens} input, ${outputTokens} output tokens)`,
          );
        }
      }
    }, LlmOperation.GENERATE_OUTLINE);

    return response;
  }

  async generateBlogSection({
    model,
    blogSectionOutline,
    prompt,
    sourceSimilarity,
    blogTone,
    includeQuotation,
  }: {
    model: string;
    blogSectionOutline: Blog['blogOutline']['sections'][0];
    prompt: string;
    sourceSimilarity?: number;
    blogTone?: BlogTone;
    includeQuotation?: boolean;
  }): Promise<{ content: string; svgInstruction: string; metadata?: { estimatedCost: number } }> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);
    const temperature = sourceSimilarity ? 1 - sourceSimilarity / 100 : 0.3;

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const svgPrompt = blogSectionOutline.svgPrompt;
    delete blogSectionOutline.svgPrompt;

    const response = {
      content: '',
      svgInstruction: '',
      metadata: {
        estimatedCost: 0,
      },
    };

    const toneInstruction = blogTone
      ? `Use a ${blogTone} tone throughout the content.`
      : 'Use a natural tone.';

    const systemPrompt = `You are a helpful assistant and a professional blogger. You have the following blog section outline of a full blog outline in JSON delimeted by """
        """
        ${JSON.stringify(blogSectionOutline, null, 2)}
        """
        You can write SEO friendly content for each blog section individually when user ask for it in the prompt. ${includeQuotation ? 'Include quotes if relevant to the section and contains in the source.' : 'Do not include any quotes in the generated content.'}
        ${
          blogSectionOutline.shouldGenerateTable
            ? `Generate a table from the data options of this section`
            : `should not generate any table for this section`
        }
        ${
          blogSectionOutline.shouldGenerateChart
            ? `Generate a chart from the data options of this section`
            : `should not generate any chart for this section`
        }

      ${toneInstruction} Remember You're helping me create content that reads like it was written by a human; naturally, with a realistic thought process, while maintaining the ${blogTone || 'natural'} tone throughout.
      Here are the key goals:

      - Vary sentence structure. Use a mix of long and short sentences within the ${blogTone || 'natural'} tone. Interrupt smooth flows occasionally, just enough to feel real, not robotic.
      - Add subtle nuances that fit the ${blogTone || 'natural'} tone.
      - Avoid perfect symmetry. Don't balance every argument too easily. Let some thoughts feel unfinished or tangential, like a real person thinking out loud.
      - Format naturally. Break into paragraphs where it feels intuitive. Avoid overly rigid or textbook-like structure.
      - Stay consistent with the ${blogTone || 'natural'} tone throughout all sections of the content.
        `;

    try {
      await this.retryLlmCall(async () => {
        const resp = await llmProvider.chatCompletion({
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          model,
          systemPrompt,
          opts: {
            temperature,
          },
        });
        response.content = resp.message.content;
        // Calculate and track cost for this section generation
        if (resp.usage) {
          const { inputTokens, outputTokens } = resp.usage;
          const providerKey = provider.toLowerCase();
          const modelInfo = ModelPricing[providerKey]?.[model];
          if (modelInfo) {
            const inputCost = inputTokens * modelInfo.pricing.inputTokensCost;
            const outputCost = outputTokens * modelInfo.pricing.outputTokensCost;
            response.metadata.estimatedCost = inputCost + outputCost;
            this.logger.debug(
              `Cost for section ${blogSectionOutline.heading}: $${response.metadata.estimatedCost.toFixed(6)} (${inputTokens} input, ${outputTokens} output tokens)`,
            );
          }
        }
      }, LlmOperation.GENERATE_BLOG_SECTION);
    } catch (error) {
      this.logger.error('Error generating summary', error);
      throw error;
    }

    // Generate SVG Prompt if svgPrompt is not empty
    if (svgPrompt) {
      const svgSystemPrompt = `You are a professional Prompt Engineer and Digital Artist to create section svg image.`;
      const svgUserPrompt = `.
        follow the following instructions:
        """
        ${svgPrompt}
        """
        - Heading:${blogSectionOutline.heading}
        - Data: ${blogSectionOutline.data}
        - Use light sophisticated colors like ${predefinedColors.join(',')}
        - The image instruction should be professional and detailed
        - The image content should fit according to the image size
        Return the prompt
        `;

      try {
        await this.retryLlmCall(async () => {
          const resp = await llmProvider.chatCompletion({
            messages: [{ role: 'user', content: svgUserPrompt }],
            model,
            systemPrompt: svgSystemPrompt,
            opts: {
              temperature,
            },
          });

          response.svgInstruction = resp.message.content;
        }, LlmOperation.GENERATE_SVG);
      } catch {
        response.svgInstruction = '';
      }
    }

    return response;
  }

  async generateSvg({
    svgInstruction,
    model = 'gpt-4o-mini',
  }: {
    svgInstruction: string;
    model?: string;
  }): Promise<string> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const systemPrompt = `You are a professional SVG designer / digital artist. Create a svg image`;

    svgInstruction = `Create a svg image for the following prompt: ${svgInstruction}. Return the svg code in the following format <svg>...</svg>`;

    return this.retryLlmCall(async () => {
      const resp = await llmProvider.chatCompletion({
        messages: [{ role: 'user', content: svgInstruction }],
        model,
        systemPrompt,
      });

      return resp.message.content;
    }, LlmOperation.GENERATE_SVG);
  }

  async processBlogSectionHtml(blogSectionHtml: string): Promise<string> {
    const svgRegex = /<svg[^>]*>[\s\S]*?<\/svg>/gi;
    const svgMatches = blogSectionHtml.match(svgRegex);
    const s3Directory = `svg`;

    if (!svgMatches) {
      return blogSectionHtml;
    }

    let processedHtml = blogSectionHtml;

    for (const svgMatch of svgMatches) {
      try {
        const pngBuffer = await sharp(Buffer.from(svgMatch)).png().toBuffer();

        const fileName = `${uuidv4()}.png`;
        const s3Url = await this.s3Service.uploadFile(
          pngBuffer,
          fileName,
          s3Directory,
          'image/png',
        );

        processedHtml = processedHtml.replace(
          svgMatch,
          `<img src="${s3Url}" alt="Generated image" />`,
        );
      } catch (error) {
        this.logger.error(
          `Error processing SVG for blog section returning original html: ${error.message}`,
        );
      }
    }

    return processedHtml;
  }

  private getSourceAlias(sourceType: BlogSourceType, sourceName: BlogSourceName): string {
    let suffix: string;
    switch (sourceType) {
      case BlogSourceType.audio:
      case BlogSourceType.video:
        suffix = 'transcript';
        break;
      case BlogSourceType.document:
        suffix = 'document';
        break;
      case BlogSourceType.webLink:
        suffix = 'Web Page Content';
        break;
      default:
        suffix = 'text';
    }

    return `${sourceName} ${suffix}`;
  }

  async generateFullBlogContent({
    model,
    blogOutline,
    prompt,
    sourceSimilarity,
  }: {
    model: string;
    blogOutline: Blog['blogOutline'];
    prompt: string;
    sourceSimilarity?: number;
  }): Promise<{ content: string }> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);
    const temperature = sourceSimilarity ? 1 - sourceSimilarity / 100 : 0.3;
    // Prepare sections with optional elements
    const sectionsOutline = blogOutline.sections
      .map((section) => {
        const elements = [];

        if (section.bulletPoints?.length) {
          elements.push(`Key points: ${section.bulletPoints.join(', ')}`);
        }

        if (section.quotes?.length) {
          elements.push(`Available quotes: "${section.quotes[0]}"`);
        }

        if (section.data?.length && (section.shouldGenerateTable || section.shouldGenerateChart)) {
          elements.push(`Available data: ${section.data.join(', ')}`);
        }

        return `- ${section.heading}
${elements.join('\n  ')}`;
      })
      .join('\n\n');

    const enhancedPrompt = `
      ${prompt}

      Sections to cover but do not use more than 2 quotes and more than 1 table in the entire blog and use them if available:
      ${sectionsOutline}

      Writing guidelines:
      - Use <h2> tags for section headings
      - Use <p> tags for paragraphs
      - Keep content concise and focused
      - Use <b> tags for key points
      - Create smooth transitions between sections
      - Include quotes or create tables if they enhance the content naturally
      - Format quotes with <blockquote> tags when used
      - Use proper <table> tags for data presentation when needed

      Write a short, engaging blog post that covers all sections clearly and concisely.
      Use the available quotes and data only if they add value to the content.

      Use supporting elements like quotes and data tables only when they enhance the narrative and do not use more than 2 quotes and more than 1 table.
      `;

    return this.retryLlmCall(async () => {
      const response = await llmProvider.chatCompletion({
        model,
        messages: [
          {
            role: 'user',
            content: enhancedPrompt,
          },
        ],
        systemPrompt: prompt,
        opts: {
          temperature,
        },
      });

      const content = response.message.content;
      if (!content) {
        throw new Error('Failed to generate blog content');
      }

      return { content };
    }, LlmOperation.GENERATE_FULL_BLOG);
  }

  async generateBlogTitle({
    model,
    context,
    keywords,
  }: {
    model: string;
    context: string;
    keywords: string[];
  }): Promise<string> {
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const systemPrompt = `You are a professional content writer specializing in creating engaging, SEO-friendly blog titles.
      Create a concise, compelling title that accurately represents the content while being attention-grabbing and searchable.`;

    const keywordsInstruction = keywords?.length
      ? `Make sure the title contains some of the following keywords targeting for SEO.
        Keywords: ${keywords.join(',')}`
      : '';
    const userPrompt = `Generate a blog title for the following content. The title should be clear, engaging, and under 60 characters:
      Content:
      ${context}

      Every blog title, regardless of your industry or what you are writing about, has to satisfy three main requirements:
      - It contains the keyword you are targeting for SEO.
      - It gives the reader a reason to click (value).
      - It offers a unique take on the topic.

      ${keywordsInstruction}
    The ideal length of a blog title is 50-60 characters or 10-11 words is the ideal length for a blog post to optimize CTR from Google.

    Return only the title text, without quotes or additional formatting.`;

    return this.retryLlmCall(async () => {
      const resp = await llmProvider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model,
        systemPrompt,
        opts: {
          temperature: 0.5, // Slightly creative but not too random
          max_tokens: 100,
        },
      });

      // Clean up any potential quotes or extra whitespace
      return resp.message.content.replace(/["']/g, '').trim();
    }, LlmOperation.GENERATE_BLOG_TITLE);
  }

  async generateCreditSection({
    url,
    authorsToGiveCredit,
    language,
  }: {
    url: string;
    authorsToGiveCredit: string[];
    language: string;
  }): Promise<string> {
    let message;
    const model = `gpt-4o-mini`;
    const provider = getProviderName(model);
    const llmProvider = this.providers.get(provider);

    if (!llmProvider) {
      throw new Error(`Provider not found for model: ${model}`);
    }

    const videoPlatforms = ['youtube', 'vimeo', 'youtu'];
    const showVideoUrl = url && videoPlatforms.some((platform) => url.includes(platform));

    if (showVideoUrl) {
      url = `<a target="_blank" href="${url}">${url}</a>`;
    }

    if (authorsToGiveCredit.length === 1) {
      message = `Kudos to ${authorsToGiveCredit[0]} for the insightful content. ${
        showVideoUrl ? 'Check it out here: ' + url + '.' : ''
      }`;
    } else {
      const selectedAuthors = authorsToGiveCredit
        .slice(0, authorsToGiveCredit.length <= 2 ? 1 : authorsToGiveCredit.length - 2)
        .join(', ');
      const lastAuthor = authorsToGiveCredit[authorsToGiveCredit.length - 1];

      const messages = [
        `Kudos to ${selectedAuthors} and ${lastAuthor} for the insightful content. ${
          showVideoUrl ? 'Check it out here: ' + url + '.' : ''
        }`,
        `We appreciate ${selectedAuthors} and ${lastAuthor}'s contributions. ${
          showVideoUrl ? 'View it here: ' + url + '' : ''
        }`,
        `Thanks to ${selectedAuthors} and ${lastAuthor} for sharing amazing content. ${
          showVideoUrl ? 'Enjoy it here: ' + url + '.' : ''
        } `,
        `Special thanks to ${selectedAuthors} and ${lastAuthor} for their informative content. ${
          showVideoUrl ? 'Access it through this link: ' + url + '.' : ''
        }`,
        `With gratitude, we present ${selectedAuthors} and ${lastAuthor}'s content. ${
          showVideoUrl ? 'Dive in here: ' + url + '.' : ''
        }`,
      ];

      message = messages[Math.floor(Math.random() * messages.length)];
    }

    return this.retryLlmCall(async () => {
      const resp = await llmProvider.chatCompletion({
        systemPrompt: `You can write a kudos`,
        messages: [
          {
            role: 'user',
            content: `Rephrase kudos in ${language} language which should contain this message ${message}`,
          },
        ],
        model,
        tools: [],
      });
      return resp.message.content;
    }, LlmOperation.GENERATE_KUDOS);
  }

  async generateCompleteBlog(options: {
    blogId: string;
    topic: string;
    language: string;
    blogTone: BlogTone;
    blogSize: BlogSize;
    pov: string;
    customInstruction?: string;
    tldrPosition?: BlogTldrPosition;
    seoInputKeywords?: string[];
    sourceContent?: string;
  }): Promise<{
    title: string;
    content: string;
    research: any;
    outline: any;
    generationCost: any;
  }> {
    this.logger.log(
      `Starting complete blog generation process for topic: ${options.topic.substring(0, 100)}`,
    );

    // Log the blog generation request to our agent logger
    agentLogger.setSessionId(`blog_${options.blogId}`);
    agentLogger.logEvent('BlogGenerationService', 'Starting complete blog generation', {
      blogId: options.blogId,
      topic: options.topic,
      language: options.language,
      blogTone: options.blogTone,
      blogSize: options.blogSize,
    });

    try {
      // Create an orchestrator to handle the entire blog generation process
      const orchestrator = new BlogGenerationOrchestrator({
        provider: 'openai',
        model: 'gpt-4o',
        identifierName: 'blogId',
        identifierValue: options.blogId,
      });

      // Run the orchestrated blog generation process
      const result = await orchestrator.generateBlog({
        topic: options.topic,
        language: options.language,
        blogTone: options.blogTone,
        blogSize: options.blogSize,
        pov: options.pov,
        customInstruction: options.customInstruction,
        tldrPosition: options.tldrPosition,
        seoInputKeywords: options.seoInputKeywords,
        sourceContent: options.sourceContent,
      });

      this.logger.log(`Blog generation completed successfully for topic: ${options.topic}`);
      agentLogger.logEvent(
        'BlogGenerationService',
        'Complete blog generation finished successfully',
        {
          blogId: options.blogId,
          title: result.title,
          totalCost: result.generationCost?.totalCost || 0,
        },
      );

      return {
        title: result.title,
        content: result.content,
        research: result.research,
        outline: result.outline,
        generationCost: result.generationCost,
      };
    } catch (error) {
      this.logger.error(`Failed to generate complete blog: ${error.message}`, error.stack);
      agentLogger.logError('BlogGenerationService', `Failed to generate complete blog: ${error}`);
      throw error;
    }
  }

  /**
   * Queue a blog for review
   * @param blogId - The ID of the blog to review
   * @param content - The content to review
   * @param options - Additional blog generation options
   */
  async queueBlogForReview(
    blogId: string,
    content: string,
    options: {
      title: string;
      blogTone: BlogTone;
      blogLanguage?: string;
      language?: string;
      perspective?: string;
      seoKeywords?: string[];
      blogSize?: BlogSize;
      wordCountApprox?: number;
      targetAudience?: string;
      sourceType?: string;
      sourceName?: string;
      transcription?: string;
      prompt?: string;
      sentencePairs?: any[];
    },
  ): Promise<boolean> {
    this.logger.log(`Queueing blog for review: ${blogId}`);

    try {
      // Call blog review service
      const result = await this.blogReviewService.reviewBlogContent({
        blogId,
        content,
        title: options.title,
        blogTone: options.blogTone,
        language: options.language || options.blogLanguage,
        perspective: options.perspective,
        seoKeywords: options.seoKeywords,
        blogSize: options.blogSize,
        wordCountApprox: options.wordCountApprox,
        targetAudience: options.targetAudience || 'general',
        sourceType: options.sourceType || 'text',
        sourceName: options.sourceName || 'blog',
        transcription: options.transcription || '',
        prompt: options.prompt || '',
        sentencePairs: options.sentencePairs || [],
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to queue blog for review: ${error.message}`);
      return false;
    }
  }
}
