import { BlogModule } from '@/blog/blog.module';
import { S3Service } from '@/common/services/s3.service';
import { JobModule } from '@/job/job.module';
import { Module, forwardRef } from '@nestjs/common';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { BlogGenerationController } from './blog-generation.controller';
import { BlogGenerationService } from './blog-generation.service';
import { BlogReviewService } from './blog-review.service';

@Module({
  imports: [JobModule, forwardRef(() => BlogModule)],
  controllers: [BlogGenerationController],
  providers: [BlogGenerationService, BlogReviewService, S3Service],
  exports: [BlogGenerationService, BlogReviewService],
})
export class BlogGenerationModule {
  constructor() {
    // Only create logs directory if in debug mode
    if (process.env.NODE_ENV === 'debug') {
      // Create logs directory for agents if it doesn't exist
      const logsDir = join(process.cwd(), 'logs');
      const agentsLogsDir = join(logsDir, 'agents');

      if (!existsSync(logsDir)) {
        mkdirSync(logsDir, { recursive: true });
      }

      if (!existsSync(agentsLogsDir)) {
        mkdirSync(agentsLogsDir, { recursive: true });
      }
    }
  }
}
