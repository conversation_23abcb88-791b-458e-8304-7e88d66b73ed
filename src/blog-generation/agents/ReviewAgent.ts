import { logger } from '@/common/logger';
import { AiAgent } from '../../llm/sdk/AiAgent';
import { AiAgentOptions } from '../../llm/sdk/types';
import { RelevantSection } from './RelevanceExtractorAgent';

/**
 * Final Review Agent
 *
 * Responsible for:
 * - Applying specified tone, language and perspective consistently
 * - Adjusting content to target word count
 * - Optimizing for SEO by incorporating keywords naturally
 * - Adapting content for the target audience
 * - Making the content undetectable by AI content detectors
 * - Preserving HTML formatting, tables, and charts
 */
export class ReviewAgent extends AiAgent {
  constructor(options: AiAgentOptions) {
    super({
      ...options,
      systemPrompt: `You are an expert blog content reviewer specializing in making AI-generated content appear human-written and undetectable by AI content detectors.
      You're helping me create content that reads like it was written by a human; naturally, casually, and with a realistic thought process.
      Here are the key goals:
      Vary sentence structure. Use a mix of long and short sentences. Interrupt smooth flows occasionally, just enough to feel real, not robotic.
      And subtle imperfections. Slight redundancy hesitations (like 'perhaps' or 'I think'), or overly cautions qualifiers make it feel more human.
      Avoid perfect symmetry. Don't balance every argument too easily. Let some thoughts feel unfinished or tangential, like a real person thinking out loud.
      Use light personalization. Mention reactions, small experiences, or opinions, even invented ones, that a typical person might include.
      Introduce ambiguity or mild contradiction. Humans aren't always consistent, a slight shift in tone or perspective adds realism.
      Skip slang or regionalisms. Keep the language neutral, but still natural. Focus on tone, pacing, and realism.
      Format naturally. Break into paragraphs where it feels intuitive. Avoid overly rigid or textbook-like structure.
      MAKE THE CONTENT AUTHENTICALLY HUMAN:
      - Vary sentence lengths dramatically (30% very short 1-5 word sentences, 50% medium 6-10 words, 18% longer 11-15 words, max 2% very long)
      - Use contractions heavily (don't, can't, you're, I'd, we've, etc.)
      - Add casual phrases and fillers ("actually", "you know", "honestly", "like")
      - Include multiple sentence fragments. Just like this.
      - Break perfectionist AI patterns (parallel structure, balanced sentences, perfect grammar)
      - Add parenthetical thoughts (like this) and conversational asides
      - Include 3-5 rhetorical questions throughout
      - Start some sentences with conjunctions (But, And, So)
      - Use idioms and metaphors naturally ("hit the nail on the head", "cutting corners")
      - Inject light personality with phrases like "I think", "I've found", "in my experience"
      - Create abrupt transitions between some paragraphs
      - Include 1-2 minor grammar peculiarities or self-corrections
      - Vary paragraph lengths dramatically, including single-sentence paragraphs
      Your task is to review and optimize the given content according to the specified blog settings while ensuring it appears entirely human-written.
      Also make sure to keep the same html tags and formatting as the original content. No need to replace sentences which are already human-written.`,
    });
  }

  /**
   * Run the review agent on the provided content
   */
  async run(input: {
    content: string;
    metadata: {
      title: string;
      blogTone: string;
      blogLanguage: string;
      perspective: string;
      seoKeywords?: string[];
      wordCountApprox?: number;
      targetAudience?: string;
      relevantSections?: RelevantSection[];
    };
  }): Promise<string> {
    if (!input || !input.content) {
      logger.error('Missing required content for review');
      return '';
    }

    const defaultMetadata = {
      title: '',
      blogTone: 'conversational',
      blogLanguage: 'English',
      perspective: 'second-person',
      seoKeywords: [],
      wordCountApprox: 1200,
      targetAudience: 'general',
      relevantSections: [],
    };

    return this.review(input.content, input.metadata || defaultMetadata);
  }

  /**
   * Review and improve the blog content
   */
  async review(
    content: string,
    metadata: {
      title: string;
      blogTone: string;
      blogLanguage: string;
      perspective: string;
      seoKeywords?: string[];
      wordCountApprox?: number;
      targetAudience?: string;
      relevantSections?: RelevantSection[];
    },
  ): Promise<string> {
    try {
      // Format relevant sections if available
      let relevantSectionsText = '';
      if (metadata.relevantSections && metadata.relevantSections.length > 0) {
        relevantSectionsText = `\n\nRELEVANT SOURCE MATERIAL:
${metadata.relevantSections
  .map((section) => {
    return `SECTION: ${section.blogSection}
RELEVANT LINES:
${section.relevantLines.map((line) => `- ${line}`).join('\n')}
CONTEXT: ${section.contextSummary}

`;
  })
  .join('\n')}`;
      }

      // Build SEO keywords section
      let seoKeywordsText = '';
      if (metadata.seoKeywords && metadata.seoKeywords.length > 0) {
        seoKeywordsText = `\n\nSEO KEYWORDS: ${metadata.seoKeywords.join(', ')}`;
      }

      // Create blog settings section
      const blogSettingsText = `TITLE: ${metadata.title}
TONE: ${metadata.blogTone}
LANGUAGE: ${metadata.blogLanguage}
PERSPECTIVE: ${metadata.perspective}
TARGET WORD COUNT: ${metadata.wordCountApprox || 1200}
TARGET AUDIENCE: ${metadata.targetAudience || 'general'}${seoKeywordsText}`;

      // Persona guidance
      const personaText = `Write as if you're a ${metadata.targetAudience || 'general'}-focused expert with real experience, sharing knowledge casually. Use phrases like "I've found that..." or "In my experience..." occasionally to add authenticity.`;

      // Build the prompt
      const prompt = `You are an expert blog content reviewer specializing in making AI-generated content appear completely human-written and undetectable by AI content detectors. Your task is to review and transform the given content according to the specified blog settings.

<blog_settings>
${blogSettingsText}
</blog_settings>

<original_content>
${content}
</original_content>

${relevantSectionsText}

CRITICAL TRANSFORMATION INSTRUCTIONS:

1. MAINTAIN VOICE AND EFFECTIVENESS:
   - Apply the ${metadata.blogTone} tone consistently throughout
   - Use ${metadata.perspective} perspective (except for occasional first-person examples)
   - Naturally incorporate all SEO keywords (2-3 times each)
   - Ensure content is appropriate for ${metadata.targetAudience || 'general'} audience
   - Keep word count approximately ${metadata.wordCountApprox || 1200} words
   - ${personaText}

2. AVOID THESE AI PATTERNS:
   - "In today's fast-paced world" or similar clichés
   - "Moreover", "Furthermore", "In conclusion" and other academic transitions
   - Overly perfect parallel structure or balanced sentences
   - Paragraphs where each sentence is the same length
   - Overly formal or technical vocabulary when simpler words will do
   - Passive voice used consistently
   - Perfect punctuation and grammar throughout
   - Long, multi-clause sentences connected with commas
   - Perfect flow between paragraphs (humans are more abrupt)
   - Consistent voice and tone throughout (humans vary slightly)

3. PRESERVE HTML INTEGRITY:
   - Keep ALL HTML tags intact (<p>, <h2>, <ul>, <li>, <table>, etc.)
   - Preserve ALL attributes, classes, IDs and styles exactly as-is
   - Never modify URLs or links in <a href="..."> tags
   - Don't change image tags or their alt text
   - Don't alter data in tables, only improve surrounding text
   - Ensure all opening tags have matching closing tags

HUMANIZATION EXAMPLES:

AI: "Implementation of effective time management strategies can significantly enhance productivity and reduce stress levels in professional environments."
HUMAN: "Using better time management actually helps you get more done. And it cuts down on stress at work too."

AI: "The data indicates a correlation between regular exercise and improved cognitive function across all age demographics."
HUMAN: "The data shows something interesting - people who exercise regularly tend to think better. Doesn't matter how old you are."

AI: "It is imperative to consider multiple perspectives when evaluating complex societal issues to formulate balanced conclusions."
HUMAN: "When looking at complicated social issues, you've gotta consider different viewpoints. Otherwise, you won't reach balanced conclusions."

Focus especially on making the introduction and conclusion sound authentically human, as these are most often flagged by AI detectors.

CRITICAL REQUIREMENTS:
1. Apply the ${metadata.blogTone} tone consistently throughout
2. Use ${metadata.perspective} perspective ONLY
3. Naturally incorporate the SEO keywords throughout (without forcing them)
4. Adjust content to be approximately ${metadata.wordCountApprox || 1200} words
5. Make sure the content is written in ${metadata.blogLanguage} language
6. Make the content impossible to detect as AI-written by varying sentence structure, using conversational elements, and breaking perfectionist patterns

Return the optimized content, with all HTML formatting preserved intact.`;

      // Generate the improved content
      const response = await this.generateText(prompt);

      if (response?.text) {
        return response.text;
      }

      // Fallback to original content if no response
      return content;
    } catch (error) {
      logger.error(`Error in ReviewAgent: ${error}`);
      return content;
    }
  }
}
