import { agentLogger, logger } from '@/common/logger';
import { Injectable } from '@nestjs/common';
import { AiAgent } from '../../llm/sdk/AiAgent';
import { AiSdk } from '../../llm/sdk/AiSdk';
import { AiAgentOptions } from '../../llm/sdk/types';
import { calculateCost } from '../../llm/utils/cost-calculator';
import { agentDebugLogger } from '../AgentLogger';

/**
 * Research data structure interface
 */
export interface ResearchData {
  topic: string;
  keyFindings: string[];
  statistics: Array<{ label: string; value: string; source?: string }>;
  relevantSources: Array<{ title: string; url: string }>;
  contentIdeas: string[];
  keywordSuggestions: string[];
}

/**
 * System prompt for the research agent
 */
const RESEARCH_SYSTEM_PROMPT = `You are a specialized Research Agent for blog content creation with web search capability.
Your primary purpose is to gather accurate, up-to-date information from the web and use it to inform blog content.

Your core responsibilities:
1. Gather information from real-time web searches
2. Find the most recent statistics and data points
3. Identify key facts and trends about the topic
4. Collect real sources with accurate URLs
5. Generate relevant content ideas and keywords for blogs

Always prioritize:
- Accuracy and recency of information (focus on 2023-2025 data)
- Credible sources with proper attribution
- Statistics with specific numbers and dates
- Content ideas that reflect current interests
- Keywords that match current search patterns`;

/**
 * Research Agent with Web Search
 *
 * Uses Perplexity AI for web search capabilities to get up-to-date information
 * for blog research and content planning.
 */
@Injectable()
export class ResearchAgent extends AiAgent {
  private apiKey: string;
  private openaiSdk: AiSdk; // Separate SDK instance for tool calling with GPT-4o-mini
  private perplexitySdk: AiSdk; // Dedicated SDK instance for Perplexity calls

  constructor(options: AiAgentOptions) {
    super({
      ...options,
      // Always force perplexity provider
      provider: 'perplexity',
      model: 'sonar',
      systemPrompt: RESEARCH_SYSTEM_PROMPT,
    });

    this.apiKey = process.env.PERPLEXITY_API_KEY || '';

    // Initialize OpenAI SDK for tool calling with GPT-4o-mini
    this.openaiSdk = new AiSdk({
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'ResearchAgent_ToolCalling',
      identifierValue: 'openai',
    });

    // Initialize dedicated Perplexity SDK
    this.perplexitySdk = new AiSdk({
      provider: 'perplexity',
      model: 'sonar',
      identifierName: 'ResearchAgent_WebSearch',
      identifierValue: 'perplexity',
    });

    // Log initialization status
    if (!this.apiKey) {
      logger.warn('PERPLEXITY_API_KEY not set. ResearchAgent will not be able to search the web.');
    } else {
      logger.log(
        'ResearchAgent initialized with Perplexity web search and OpenAI tool calling capabilities',
      );
    }
  }

  /**
   * Run the research agent with a specific topic
   */
  async run(input: string): Promise<ResearchData> {
    logger.debug(`Starting web research for topic: ${input}`);
    agentLogger.logEvent('ResearchAgent', 'Starting research process', { topic: input });

    // Log input for debugging
    agentDebugLogger.logAgentInput('ResearchAgent', { topic: input });

    try {
      // Record start time for performance tracking
      const startTime = Date.now();

      // Create a research prompt based on the input
      const researchPrompt = this.createResearchPrompt(input);
      agentLogger.logEvent('ResearchAgent', 'Generated research prompt', {
        prompt: researchPrompt,
      });

      // Perform web search using Perplexity
      const searchResults = await this.performWebSearch(researchPrompt);

      if (!searchResults.text) {
        logger.error('No text returned from Perplexity search');
        agentLogger.logError('ResearchAgent', 'No text returned from Perplexity search');
        throw new Error('Failed to get search results from Perplexity');
      }

      // Record end time and calculate duration
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Simulate usage for cost calculation
      const usage = {
        prompt_tokens: researchPrompt.length / 4, // Rough estimate
        completion_tokens: searchResults.text.length / 4, // Rough estimate
        total_tokens: (researchPrompt.length + searchResults.text.length) / 4, // Rough estimate
      };

      // Calculate the estimated cost
      const costResult = calculateCost(usage, 'perplexity', this.options.model, 'ResearchAgent');

      // Log usage and cost
      agentDebugLogger.logUsageAndCost(
        'ResearchAgent',
        this.options.model,
        usage,
        costResult.totalCost,
      );

      logger.debug(
        `ResearchAgent web search completed in ${duration}ms. Estimated cost: $${costResult.totalCost.toFixed(4)}`,
      );

      // Format the sources from search results
      const relevantSources = searchResults.sources.map((source) => ({
        title: source.title || 'Untitled Source',
        url: source.url || '',
      }));

      // Extract structured data using the search results
      const _structuredDataStartTime = Date.now();
      const structuredData = await this.extractStructuredData(searchResults.text);

      // Create the final research data object
      const researchData = {
        topic: input,
        keyFindings: structuredData.keyFindings || [],
        statistics: structuredData.statistics || [],
        relevantSources,
        contentIdeas: structuredData.contentIdeas || [],
        keywordSuggestions: structuredData.keywordSuggestions || [],
      };

      agentLogger.logEvent('ResearchAgent', 'Structured data extracted successfully');

      // Log the final output
      agentDebugLogger.logAgentOutput('ResearchAgent', researchData);

      return researchData;
    } catch (error) {
      logger.error(`Error in research agent: ${error}`);
      agentLogger.logError('ResearchAgent', error);

      // Create default data
      const defaultData = this.createDefaultResearchData();
      return {
        topic: input,
        keyFindings: defaultData.keyFindings || [],
        statistics: defaultData.statistics || [],
        relevantSources: [],
        contentIdeas: defaultData.contentIdeas || [],
        keywordSuggestions: defaultData.keywordSuggestions || [],
      };
    }
  }

  /**
   * Perform web search using Perplexity's AI SDK
   */
  private async performWebSearch(
    query: string,
  ): Promise<{ text: string; sources: Array<{ title: string; url: string }> }> {
    try {
      if (!this.apiKey) {
        throw new Error('PERPLEXITY_API_KEY not set. Cannot perform web search.');
      }

      logger.debug(`Performing Perplexity web search: ${query.substring(0, 100)}...`);

      // Use the dedicated perplexitySdk instance
      const result = await this.perplexitySdk.generateText({
        prompt: query,
        // Set web search mode and source control for Perplexity
        searchMode: 'websearch',
        maxSourceCount: 5,
      });

      // Extract sources from result
      const sources = result.sources || [];

      // Log success if we get sources
      if (sources.length > 0) {
        logger.debug(`Perplexity web search returned ${sources.length} sources`);
      } else {
        logger.warn('Perplexity web search returned no sources');
      }

      return {
        text: result.text,
        sources: sources.map((source) => ({
          title: source.title || 'Untitled Source',
          url: source.url || '',
        })),
      };
    } catch (error) {
      logger.error(`Error in Perplexity web search: ${error.message}`);
      // Instead of silently returning empty data, throw the error to be handled by the caller
      throw new Error(`Perplexity web search failed: ${error.message}`);
    }
  }

  /**
   * Create a research prompt based on the input topic
   */
  private createResearchPrompt(topic: string): string {
    return `I need comprehensive research about "${topic}" for a blog article.

Research these aspects and provide real-time information:
1. Key findings and facts about this topic from current sources (at least 5)
2. Recent statistics or data points with specific numbers from 2023-2025 (at least 3)
3. Current market trends and recent developments
4. Expert opinions and insights from reliable sources
5. Potential content angles that would interest readers today
6. SEO-friendly keywords based on current search trends

Focus on finding accurate, up-to-date information with real sources that can be cited.
Make sure to include actual URLs for sources when available.`;
  }

  /**
   * Extract structured data from research results using direct JSON generation
   */
  private async extractStructuredData(rawText: string): Promise<Partial<ResearchData>> {
    try {
      logger.debug('Extracting structured data from research results');
      agentLogger.logEvent('ResearchAgent', 'Extracting structured data');

      // Create a prompt that asks for a JSON response directly
      const prompt = `Extract and organize the following information from this research data into JSON format:

1. Key findings and important facts (5-7 items)
2. Statistics with specific numbers (3-5 items)
3. Content ideas for blog posts (3-5 items)
4. SEO keyword suggestions (5-7 items)

Format your response as valid JSON with the following structure:
{
  "keyFindings": ["finding 1", "finding 2", ...],
  "statistics": [
    {"label": "stat description 1", "value": "stat value 1", "source": "optional source"},
    {"label": "stat description 2", "value": "stat value 2"}
  ],
  "contentIdeas": ["idea 1", "idea 2", ...],
  "keywordSuggestions": ["keyword 1", "keyword 2", ...]
}

Make sure to return ONLY valid JSON that can be parsed with JSON.parse().

Research data:
${rawText.substring(0, Math.min(rawText.length, 8000))}`;

      // Log the extraction attempt
      agentDebugLogger.logAgentInput('ResearchAgent_extractStructuredData', {
        prompt,
        model: 'gpt-4o-mini',
      });

      // Use direct text generation instead of function calling
      const response = await this.openaiSdk.generateText({
        prompt,
        // Force OpenAI provider and model to ensure compatibility
        provider: 'openai',
        model: 'gpt-4o-mini',
      });

      if (!response?.text) {
        logger.error('No response text received from OpenAI');
        return this.createDefaultResearchData();
      }

      logger.debug(`Raw response: ${response.text.substring(0, 200)}...`);

      // Try to extract JSON from the response
      try {
        // Look for JSON in the response
        const jsonMatch =
          response.text.match(/```json\s*([\s\S]*?)\s*```/) ||
          response.text.match(/```\s*([\s\S]*?)\s*```/) ||
          response.text.match(/{[\s\S]*}/);

        let jsonString;
        if (jsonMatch) {
          jsonString = jsonMatch[0].replace(/```json|```/g, '').trim();
        } else {
          jsonString = response.text;
        }

        // Parse the JSON
        const data = JSON.parse(jsonString);

        // Validate that we have the expected structure
        if (
          !data.keyFindings ||
          !data.statistics ||
          !data.contentIdeas ||
          !data.keywordSuggestions
        ) {
          logger.warn('Extracted data is missing required fields');
          // Add missing fields with empty arrays
          return {
            keyFindings: data.keyFindings || [],
            statistics: data.statistics || [],
            contentIdeas: data.contentIdeas || [],
            keywordSuggestions: data.keywordSuggestions || [],
          };
        }

        logger.debug('Successfully extracted structured data from text response');
        return data;
      } catch (jsonError) {
        logger.error(`Error parsing JSON from response: ${jsonError}`);
        return this.createDefaultResearchData();
      }
    } catch (error) {
      logger.error(`Error in extractStructuredData: ${error}`);
      return this.createDefaultResearchData();
    }
  }

  /**
   * Create default research data when extraction fails
   */
  private createDefaultResearchData(): Partial<ResearchData> {
    return {
      keyFindings: [
        'Information is evolving rapidly in this field',
        'Recent studies show increasing interest in this topic',
        'Multiple applications exist across different industries',
        'Experts predict continued growth in this area',
        'There are various approaches being developed',
      ],
      statistics: [
        { label: 'Growth rate', value: 'Significant' },
        { label: 'Adoption rate', value: 'Increasing' },
        { label: 'Market potential', value: 'High' },
      ],
      contentIdeas: [
        'Overview and introduction to the topic',
        'Benefits and practical applications',
        'Future trends and predictions',
      ],
      keywordSuggestions: ['beginner guide', 'tutorial', 'benefits', 'examples', 'best practices'],
    };
  }
}
