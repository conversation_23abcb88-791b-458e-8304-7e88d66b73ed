import { agentLogger, logger } from '@/common/logger';
import { Injectable } from '@nestjs/common';
import { BlogSize, BlogTldrPosition, BlogTone } from '../../blog/blog.enums';
import { OrchestratorAgent, OrchestratorContext } from '../../llm/sdk/OrchestratorAiAgent';
import { AiAgentOptions } from '../../llm/sdk/types';
import { agentDebugLogger } from '../AgentLogger';
import { BlogWriterAgent } from './BlogWriterAgent';
import { RelevanceExtractorAgent } from './RelevanceExtractorAgent';
import { ResearchAgent } from './ResearchAgent';
import { ReviewAgent } from './ReviewAgent';

/**
 * Blog generation options interface
 */
export interface BlogGenerationOptions {
  language: string;
  blogTone: BlogTone;
  blogSize: BlogSize;
  pov: string;
  customInstruction?: string;
  tldrPosition?: BlogTldrPosition;
  seoInputKeywords?: string[];
  sourceContent?: string; // Source content like transcription or prompt
  draftContent?: string; // Draft content already generated to be improved
  title?: string; // Title for the blog, used with draft content
}

/**
 * Blog generation result interface
 */
export interface BlogGenerationResult {
  title: string;
  content: string;
  research: any;
  outline: any;
  relevantSections?: any; // Relevant sections extracted from source content
  sentencePairs?: any; // Pairs of AI-generated and humanized sentences
  generationCost: any; // Cost tracking information
  context: OrchestratorContext;
}

/**
 * Blog Generation Orchestrator
 *
 * Coordinates the multi-agent workflow for blog content generation:
 * 1. Research & Ideation - Gathers information and ideas
 * 2. Relevance Extraction - Finds relevant information from source content
 * 3. Content Writing - Transforms research into structured blog content
 * 4. Humanization - Ensures the content sounds human-written
 * 5. Final Review - Polishes structure, formatting, and readability
 */
@Injectable()
export class BlogGenerationOrchestrator extends OrchestratorAgent {
  private researchAgent: ResearchAgent;
  private relevanceExtractorAgent: RelevanceExtractorAgent;
  private contentWriterAgent: BlogWriterAgent;
  private reviewAgent: ReviewAgent;

  constructor(options: AiAgentOptions) {
    super({
      ...options,
      orchestrationPrompt: `You are a Blog Generation Orchestrator coordinating specialized agents to create high-quality blog content.`,
    });

    // Initialize the specialized agents
    this.initializeAgents(options);

    // Configure the workflow
    this.configureWorkflow();

    // Set up input/output mappers for each agent
    this.setupMappers();
  }

  /**
   * Initialize specialized agents
   */
  private initializeAgents(options: AiAgentOptions): void {
    // Initialize ResearchAgent with Perplexity for web search
    this.researchAgent = new ResearchAgent({
      provider: 'perplexity',
      model: 'sonar',
      identifierName: options.identifierName,
      identifierValue: options.identifierValue,
    });

    // Initialize RelevanceExtractorAgent for finding relevant information in source content
    this.relevanceExtractorAgent = new RelevanceExtractorAgent({
      provider: 'anthropic',
      model: 'claude-3-7-sonnet-20250219',
      identifierName: options.identifierName,
      identifierValue: options.identifierValue,
    });

    // Initialize ContentWriterAgent with Gemini
    this.contentWriterAgent = new BlogWriterAgent({
      provider: 'google',
      model: 'gemini-2.0-flash-lite',
      identifierName: options.identifierName,
      identifierValue: options.identifierValue,
    });

    // Initialize ReviewAgent with Claude 3.7 for final polishing and AI-detection avoidance
    this.reviewAgent = new ReviewAgent({
      provider: 'anthropic',
      model: 'claude-3-7-sonnet-20250219',
      identifierName: options.identifierName,
      identifierValue: options.identifierValue,
    });
  }

  /**
   * Configure the agent workflow with workers and their relationships
   */
  private configureWorkflow(): void {
    // Add agents as workers to the orchestrator
    this.addWorker({
      id: 'research',
      name: 'Research & Ideation',
      description: 'Gathers information and ideas for the blog content',
      agent: this.researchAgent,
      shouldHandle: (_input, context) => !context.state.research,
    });

    // Add the new relevance extractor worker
    this.addWorker({
      id: 'relevance',
      name: 'Relevance Extraction',
      description: 'Extracts relevant information from source content',
      agent: this.relevanceExtractorAgent,
      shouldHandle: (_input, context) =>
        !!context.state.research &&
        !!context.state.sourceContent &&
        !context.state.relevantSections,
    });

    // Add content writer agent only if there's no draft content
    this.addWorker({
      id: 'content',
      name: 'Content Writing',
      description: 'Transforms research into structured blog content',
      agent: this.contentWriterAgent,
      shouldHandle: (_input, context) =>
        !!context.state.research && !context.state.content && !context.state.draftContent, // Skip if draft content exists
    });

    // Bypass the humanization agent - commented out for reference
    /*
    this.addWorker({
      id: 'humanization',
      name: 'Content Humanization',
      description: 'Identifies complex sentences and words for humanization',
      agent: this.humanizationAgent,
      shouldHandle: (_input, context) =>
        (!!context.state.content || !!context.state.draftContent) && !context.state.humanized,
    });
    */

    // Add review worker - modified to accept content directly without humanization
    this.addWorker({
      id: 'review',
      name: 'Final Review',
      description: 'Polishes structure, formatting, and applies blog settings',
      agent: this.reviewAgent,
      shouldHandle: (_input, context) =>
        // Changed from checking humanized state to checking for content or draft content
        (!!context.state.content || !!context.state.draftContent) && !context.state.reviewed,
    });

    // Set workflow with conditional content writing
    if (this.hasDraftContent()) {
      // Skip content writing if draft content is available
      this.setWorkflow(['research', 'relevance', 'review']);
    } else {
      // Include content writing if no draft content
      this.setWorkflow(['research', 'relevance', 'content', 'review']);
    }
  }

  /**
   * Check if draft content is provided in options
   */
  private hasDraftContent(): boolean {
    // This is checked during runtime when the orchestrator is configured
    return false; // Default to false, will be properly set during runtime
  }

  /**
   * Generate a complete blog post using the orchestrated multi-agent process
   */
  async generateBlog(options: {
    topic: string;
    language: string;
    blogTone: BlogTone;
    blogSize: BlogSize;
    pov: string;
    customInstruction?: string;
    tldrPosition?: BlogTldrPosition;
    seoInputKeywords?: string[];
    sourceContent?: string;
    draftContent?: string;
    title?: string;
  }): Promise<BlogGenerationResult> {
    try {
      // Record start time for the entire process
      const startTime = Date.now();

      // Check if draft content is provided
      const hasDraftContent = !!options.draftContent && options.draftContent.trim().length > 0;

      // Initialize session and logging
      const sessionId = this.initializeSession(options);

      // Reconfigure the workflow if draft content is available
      if (hasDraftContent) {
        this.setWorkflow(['research', 'relevance', 'review']);
        logger.log('Draft content provided - skipping content writing step');
      } else {
        this.setWorkflow(['research', 'relevance', 'content', 'review']);
        logger.log('No draft content - using full workflow with content writing');
      }

      // Run the orchestration process
      const runStartTime = Date.now();
      const { result, context } = await this.executeOrchestration(options);
      const runDuration = Date.now() - runStartTime;

      // Log completion of generation
      this.logCompletionEvent(result, context);

      // Calculate total duration and costs
      const totalDuration = Date.now() - startTime;
      const generationCost = this.calculateCost(context);

      // Log detailed results
      this.logDetailedResults(
        options,
        sessionId,
        result,
        context,
        totalDuration,
        runDuration,
        generationCost,
      );

      // Create the final result
      return this.createResult(result, context, generationCost);
    } catch (error) {
      this.handleError(error, options.topic);
      throw new Error(`Failed to generate blog: ${error.message}`);
    }
  }

  /**
   * Set up input and output mappers for each worker agent
   */
  private setupMappers(): void {
    // Research agent mappers
    this.setInputMapper('research', (input, context) => {
      if (!input || !input.topic) {
        throw new Error('Missing required topic for research');
      }
      const { topic, options } = input;
      context.state.topic = topic;
      context.state.options = options;
      context.state.sourceContent = options.sourceContent || '';
      context.state.draftContent = options.draftContent || '';
      context.progress = { percentCompleted: 0, currentStage: 'Research & Ideation' };

      // If a title is provided with draft content, store it
      if (options.title) {
        context.state.providedTitle = options.title;
        logger.debug(`Using provided title: "${options.title}"`);
      }

      // Log input to research agent
      agentLogger.logInput('ResearchAgent', { topic, options });

      logger.log(`Starting research for topic: ${topic}`);

      return topic;
    });

    this.setOutputMapper('research', (output, context) => {
      context.state.research = output;
      context.progress = { percentCompleted: 15, currentStage: 'Research Complete' };

      // If draft content exists, prepare it for humanization step
      if (context.state.draftContent) {
        // Create a temporary content state to use for humanization
        context.state.content = {
          title: context.state.providedTitle || context.state.topic || '',
          content: context.state.draftContent,
          metadata: {
            fromDraft: true,
          },
        };

        logger.debug(
          `Using draft content (${context.state.draftContent.length} chars) for humanization`,
        );
      }

      // Log research agent output to debug file
      agentDebugLogger.logAgentOutput('ResearchAgent', output);

      // Log output summary to application logs
      agentLogger.logOutput('ResearchAgent', output);
      logger.log(`Research complete for topic: ${context.state.topic}`);

      return output;
    });

    // Relevance extractor agent mappers
    this.setInputMapper('relevance', (_input, context) => {
      // Skip this step if there's no source content
      if (!context.state.sourceContent) {
        logger.debug('No source content available, skipping relevance extraction');
        return null;
      }

      context.progress = { percentCompleted: 20, currentStage: 'Relevance Extraction' };

      // Create sections from research data to find relevant content
      const blogSections = [];

      // Add content ideas from research as sections
      if (context.state.research?.contentIdeas?.length > 0) {
        context.state.research.contentIdeas.forEach((idea) => {
          blogSections.push({ heading: idea });
        });
      }

      // If no content ideas, create a generic section
      if (blogSections.length === 0) {
        blogSections.push({ heading: context.state.topic });
      }

      // Ensure sourceContent is never null
      const sourceContent = context.state.sourceContent || '';

      const relevanceInput = {
        sourceContent: sourceContent,
        blogSections: blogSections,
      };

      // Log input to relevance extractor agent
      agentLogger.logInput('RelevanceExtractorAgent', {
        sourceContentLength: sourceContent.length,
        blogSectionsCount: blogSections.length,
      });

      logger.log(`Starting relevance extraction for topic: ${context.state.topic}`);

      return relevanceInput;
    });

    this.setOutputMapper('relevance', (output, context) => {
      // Skip if no output or if step was skipped
      if (!output) {
        context.progress = { percentCompleted: 25, currentStage: 'Relevance Extraction Skipped' };
        return null;
      }

      context.state.relevantSections = output.relevantSections || [];
      context.progress = { percentCompleted: 25, currentStage: 'Relevance Extraction Complete' };

      // Log relevance extractor output to debug file
      agentDebugLogger.logAgentOutput('RelevanceExtractorAgent', output);

      // Log output summary to application logs
      agentLogger.logOutput('RelevanceExtractorAgent', {
        relevantSectionsCount: output.relevantSections?.length || 0,
        estimatedCost: output.metadata?.estimatedCost || 0,
      });

      logger.log(
        `Relevance extraction complete with ${output.relevantSections?.length || 0} relevant sections`,
      );

      return output.relevantSections;
    });

    // Content writer agent mappers
    this.setInputMapper('content', (_input, context) => {
      context.progress = { percentCompleted: 30, currentStage: 'Content Writing' };

      const contentInput = {
        research: context.state.research,
        options: context.state.options,
        relevantSections: context.state.relevantSections || [],
        draftContent: context.state.draftContent || '', // Pass the draft content if it exists
      };

      // Log input to content writer agent
      agentLogger.logInput('BlogWriterAgent', {
        ...contentInput,
        relevantSectionsCount: contentInput.relevantSections?.length || 0,
        hasDraftContent: !!contentInput.draftContent,
      });
      logger.log(`Starting content writing for topic: ${context.state.topic}`);

      return contentInput;
    });

    this.setOutputMapper('content', (output, context) => {
      context.state.content = output;

      // Set progress to 60% when content is complete (humanization will follow)
      context.progress = { percentCompleted: 60, currentStage: 'Content Writing Complete' };

      // Log content writer agent output to debug file
      agentDebugLogger.logAgentOutput('BlogWriterAgent', output);

      // Log output summary to application logs
      agentLogger.logOutput('BlogWriterAgent', {
        title: output.title,
        contentPreview: output.content.substring(0, 100) + '...',
      });

      logger.log(`Content writing complete for topic: ${context.state.topic}`);

      return {
        title: output.title,
        content: output.content,
      };
    });

    // Review agent mappers - updated to handle content directly instead of through humanization
    this.setInputMapper('review', (_input, context) => {
      context.progress = { percentCompleted: 85, currentStage: 'Final Review' };

      // Calculate approximate word count based on blog size
      let wordCountApprox = 1200; // Default medium
      switch (context.state.options?.blogSize?.toLowerCase()) {
        case 'mini':
          wordCountApprox = 500;
          break;
        case 'small':
          wordCountApprox = 800;
          break;
        case 'medium':
          wordCountApprox = 1200;
          break;
        case 'large':
          wordCountApprox = 2000;
          break;
        case 'x-large':
          wordCountApprox = 3000;
          break;
      }

      // Modified to get content directly from content or draftContent, bypassing humanized
      const reviewInput = {
        content: context.state.content?.content || context.state.draftContent || '',
        metadata: {
          title: context.state.content?.title || '',
          blogTone: context.state.options?.blogTone || 'conversational',
          blogLanguage: context.state.options?.language || 'English',
          perspective: context.state.options?.pov || 'second-person',
          seoKeywords: context.state.options?.seoInputKeywords || [],
          wordCountApprox: wordCountApprox,
          targetAudience: context.state.research?.targetAudience || '',
          relevantSections: context.state.relevantSections || [],
          // If there were sentence pairs from a previous run, pass them along
          sentencePairs: context.state.sentencePairs || [],
        },
      };

      // Log input to review agent
      agentLogger.logInput('ReviewAgent', {
        contentPreview: reviewInput.content.substring(0, 100) + '...',
        metadata: reviewInput.metadata,
      });

      return reviewInput;
    });

    this.setOutputMapper('review', (output, context) => {
      context.state.reviewed = {
        title: context.state.content?.title || '',
        content: output,
      };
      context.progress = { percentCompleted: 100, currentStage: 'Review Complete' };

      // Log review agent output to debug file
      agentDebugLogger.logAgentOutput('ReviewAgent', output);

      // Log output summary to application logs
      agentLogger.logOutput('ReviewAgent', {
        contentPreview: output.substring(0, 100) + '...',
      });

      return {
        title: context.state.content?.title || '',
        content: output,
      };
    });
  }

  /**
   * Initialize the session and set up logging
   */
  private initializeSession(options: { topic: string }): string {
    // Create a unique session ID for this blog generation run
    const sessionId = `blog_${Date.now()}_${options.topic.substring(0, 20).replace(/\s+/g, '_')}`;

    // Set up session tracking in both loggers
    agentLogger.setSessionId(sessionId);
    agentDebugLogger.setSessionId(sessionId);

    // Log start of generation
    agentLogger.logEvent('Orchestrator', 'Started blog generation', options);

    // Log debug environment flag for clarity
    const isDebugMode = process.env.NODE_ENV === 'debug';
    logger.log(`Running in ${isDebugMode ? 'DEBUG' : 'NORMAL'} mode with session ID: ${sessionId}`);

    // Log the complete input options
    agentDebugLogger.logAgentInput('Orchestrator', {
      options,
      sessionId,
      timestamp: new Date().toISOString(),
      mode: isDebugMode ? 'DEBUG' : 'NORMAL',
    });

    return sessionId;
  }

  /**
   * Execute the orchestration process
   */
  private async executeOrchestration(options: {
    topic: string;
    language: string;
    blogTone: BlogTone;
    blogSize: BlogSize;
    pov: string;
    customInstruction?: string;
    tldrPosition?: BlogTldrPosition;
    seoInputKeywords?: string[];
    sourceContent?: string;
    draftContent?: string;
    title?: string;
  }): Promise<{ result: any; context: OrchestratorContext }> {
    // Ensure sourceContent has a default value to prevent null reference exceptions
    const safeSourceContent = options.sourceContent || '';

    return await this.run({
      topic: options.topic,
      options: {
        language: options.language,
        blogTone: options.blogTone,
        blogSize: options.blogSize,
        pov: options.pov,
        customInstruction: options.customInstruction,
        tldrPosition: options.tldrPosition,
        seoInputKeywords: options.seoInputKeywords,
        sourceContent: safeSourceContent,
        draftContent: options.draftContent,
        title: options.title,
      },
    });
  }

  /**
   * Log the completion event
   */
  private logCompletionEvent(result: any, context: OrchestratorContext): void {
    agentLogger.logEvent('Orchestrator', 'Completed blog generation');
    agentLogger.logOutput('Orchestrator', {
      title: result.title,
      contentPreview: result.content.substring(0, 100) + '...',
      context: {
        state: context.state,
        progress: context.progress,
      },
    });
  }

  /**
   * Calculate the generation cost
   */
  private calculateCost(context: OrchestratorContext): {
    totalCost: number;
    breakdown: { step: string; cost: number; model: string; timestamp: Date }[];
  } {
    // Get detailed cost information from each step
    const researchMetadata = context.state.research?.metadata || {};
    const relevanceMetadata = context.state.relevantSections?.metadata || {};
    const contentMetadata = context.state.content?.metadata || {};
    const reviewMetadata = context.state.reviewed?.metadata || {};

    // Create cost breakdown by step
    const breakdown = [
      {
        step: 'research',
        cost: researchMetadata.estimatedCost || 0,
        model: 'sonar',
        timestamp: new Date(),
      },
      {
        step: 'relevance_extraction',
        cost: relevanceMetadata.estimatedCost || 0,
        model: 'claude-3-7-sonnet-20250219',
        timestamp: new Date(),
      },
      {
        step: 'content_writing',
        cost: contentMetadata.estimatedCost || 0,
        model: 'gemini-2.0-flash-lite',
        timestamp: new Date(),
      },
      {
        step: 'review',
        cost: reviewMetadata.estimatedCost || 0,
        model: 'claude-3-7-sonnet-20250219',
        timestamp: new Date(),
      },
    ];

    // Calculate total cost from all steps
    const totalCost = breakdown.reduce((total, item) => total + item.cost, 0);

    return {
      totalCost,
      breakdown,
    };
  }

  /**
   * Log detailed results including full humanization guide
   */
  private logDetailedResults(
    options: { topic: string },
    sessionId: string,
    result: any,
    context: OrchestratorContext,
    totalDuration: number,
    runDuration: number,
    generationCost: { totalCost: number; breakdown: any[] },
  ): void {
    // Compile statistics
    const stats = {
      topic: options.topic,
      sessionId,
      totalDurationMs: totalDuration,
      runDurationMs: runDuration,
      totalEstimatedCost: generationCost.totalCost,
      costBreakdown: generationCost.breakdown,
      contentStats: {
        titleLength: result.title?.length || 0,
        contentLength: result.content?.length || 0,
        contentWordCount: result.content?.split(/\s+/).length || 0,
      },
      researchStats: {
        keyFindingsCount: context.state.research?.keyFindings?.length || 0,
        statisticsCount: context.state.research?.statistics?.length || 0,
        contentIdeasCount: context.state.research?.contentIdeas?.length || 0,
        keywordSuggestionsCount: context.state.research?.keywordSuggestions?.length || 0,
      },
      relevanceStats: {
        relevantSectionsCount: context.state.relevantSections?.length || 0,
        relevantLinesTotal:
          context.state.relevantSections?.reduce(
            (total, section) => total + (section.relevantLines?.length || 0),
            0,
          ) || 0,
      },
    };

    // Log the complete results with statistics
    agentDebugLogger.logAgentOutput('Orchestrator_Final', {
      title: result.title,
      contentPreview: result.content.substring(0, 300) + '...',
      stats,
      context: context,
    });

    // Generate a full session report with all logged data
    const isDebugMode = process.env.NODE_ENV === 'debug';
    if (isDebugMode) {
      const reportPath = agentDebugLogger.generateSessionReport();
      if (reportPath) {
        logger.log(`Generated complete session report at: ${reportPath}`);
      }
    }

    logger.log(
      `Blog generation complete. Topic: "${options.topic}". Duration: ${totalDuration}ms. Estimated cost: $${generationCost.totalCost.toFixed(4)}`,
    );
  }

  /**
   * Create the final result
   */
  private createResult(
    result: any,
    context: OrchestratorContext,
    generationCost: { totalCost: number; breakdown: any[] },
  ): BlogGenerationResult {
    // Determine the final content, prioritizing the reviewed content if available
    const finalContent =
      context.state.reviewed?.content ||
      context.state.content?.content ||
      context.state.draftContent || // Add draftContent as a fallback
      result.content ||
      '';

    // Extract title, prioritizing existing title
    const finalTitle =
      context.state.content?.title ||
      context.state.options?.title || // Check for title in options
      result.title ||
      context.state.topic || // Use topic as a last resort
      '';

    // Save any sentence pairs that were captured during generation
    const sentencePairs = context.state.sentencePairs || [];

    return {
      title: finalTitle,
      content: finalContent,
      research: context.state.research || {},
      outline: context.state.outline || {}, // No real outline in this simplified version
      relevantSections: context.state.relevantSections || [],
      sentencePairs: sentencePairs,
      generationCost,
      context,
    };
  }

  /**
   * Handle errors during blog generation
   */
  private handleError(error: any, topic: string): void {
    agentLogger.logError('Orchestrator', error);
    logger.error(`Error generating blog: ${error}`);

    // Log detailed error information
    agentDebugLogger.logAgentOutput('Orchestrator_Error', {
      error: error.message,
      stack: error.stack,
      topic,
      timestamp: new Date().toISOString(),
    });
  }
}
