import { logger } from '@/common/logger';
import { BlogSize, BlogTone } from '../../blog/blog.enums';
import { AiAgent } from '../../llm/sdk/AiAgent';
import { AiAgentOptions } from '../../llm/sdk/types';
import { calculateCost } from '../../llm/utils/cost-calculator';
import { agentDebugLogger } from '../AgentLogger';
import { GENERATE_PLAGIARISM_FREE_TEXT_SYSTEM_PROMPT } from '../blog-generation.prompts';
import { RelevantSection } from './RelevanceExtractorAgent';
import { ResearchData } from './ResearchAgent';

/**
 * Blog content generation options interface
 */
export interface BlogContentOptions {
  language?: string;
  blogTone?: BlogTone;
  blogSize?: BlogSize;
  pov?: string;
  customInstruction?: string;
  tldrPosition?: string;
  seoInputKeywords?: string[];
}

/**
 * Blog content output interface
 */
export interface BlogContent {
  title: string;
  content: string;
  metadata?: {
    modelUsed?: string;
    usage?: any;
    estimatedCost?: number;
    durationMs?: number;
    error?: string;
    stack?: string;
    extractionMethod?: string;
  };
}

/**
 * Blog generation metrics
 */
interface GenerationMetrics {
  duration: number;
  usage?: any;
  estimatedCost?: number;
}

/**
 * System prompt for blog writer agent, using the same prompts as the original blog-generation service
 */
const BLOG_WRITER_SYSTEM_PROMPT = `You are a professional content writer specialized in creating engaging, well-structured blog posts based on research data.

${GENERATE_PLAGIARISM_FREE_TEXT_SYSTEM_PROMPT}

Your responsibilities:
1. Transform research findings into coherent, informative content
2. Create attention-grabbing titles that accurately represent the content
3. Organize information with a logical flow using proper HTML heading hierarchy
4. Adapt your writing style to match the requested tone and perspective
5. Incorporate statistics and data points naturally within the content
6. Include relevant keywords for SEO optimization
7. When provided with draft content, enhance and improve it rather than starting from scratch
8. Preserve the structure and HTML formatting in any draft content provided

You excel at:
- Creating compelling introductions that hook readers
- Explaining complex topics in an accessible way
- Structuring content with clear sections and transitions
- Writing persuasive conclusions with clear takeaways
- Maintaining consistent tone and voice throughout
- Enhancing existing content while preserving its structure and value

Always format your content using proper HTML tags:
- <h1> for the main title
- <h2> for section headings
- <h3> to <h6> for subheadings
- <p> for paragraphs
- <ul>, <ol>, <li> for lists
- <b>, <i>, <em> for emphasis
- <blockquote> for quotes
- <code> for code snippets
- <br> for line breaks
- <table>, <tr>, <th>, <td> for tables`;

/**
 * Maps blog size enum to word count description
 */
const BLOG_SIZE_MAP: Record<BlogSize, string> = {
  [BlogSize.mini]: 'brief (300-400 words)',
  [BlogSize.small]: 'short (500-700 words)',
  [BlogSize.medium]: 'medium-length (800-1000 words)',
  [BlogSize.large]: 'long (1500-2000 words)',
  [BlogSize['x-large']]: 'comprehensive (2000+ words)',
};

/**
 * Agent responsible for transforming research data into well-structured blog content
 */
export class BlogWriterAgent extends AiAgent {
  constructor(options: AiAgentOptions) {
    super({
      ...options,
      systemPrompt: BLOG_WRITER_SYSTEM_PROMPT,
    });
  }

  /**
   * Implementation of the abstract run method from AiAgent
   */
  async run(input: {
    research: ResearchData;
    options: BlogContentOptions;
    relevantSections?: RelevantSection[];
    draftContent?: string;
  }): Promise<BlogContent> {
    if (!input || !input.research) {
      logger.error('Missing required research data for content generation');
      return { title: 'Error', content: '<p>Missing research data.</p>' };
    }

    // Log the input with research data
    logger.debug(
      `BlogWriterAgent received research data with ${input.research.keyFindings?.length || 0} key findings and ${
        input.research.contentIdeas?.length || 0
      } content ideas`,
    );

    // Log detailed input for debugging
    agentDebugLogger.logAgentInput('BlogWriterAgent', {
      research: input.research,
      options: input.options || {},
      relevantSectionsCount: input.relevantSections?.length || 0,
      hasDraftContent: !!input.draftContent,
    });

    return this.generateContent(
      input.research,
      input.options || {},
      input.relevantSections || [],
      input.draftContent,
    );
  }

  /**
   * Generate blog content from research data
   */
  async generateContent(
    research: ResearchData,
    options: BlogContentOptions,
    relevantSections: RelevantSection[] = [],
    draftContent?: string,
  ): Promise<BlogContent> {
    // Record start time for performance tracking
    const startTime = Date.now();

    try {
      // Create tool and prepare prompt
      const { contentPrompt, blogContentTool } = this.prepareGenerationTools(
        research,
        options,
        relevantSections,
        draftContent,
      );

      // Get response from model
      const response = await this.callContentGenerationModel(contentPrompt, blogContentTool);

      // Calculate metrics from response
      const metrics = this.calculateMetrics(startTime, response);

      // Process the response and return content
      return this.processModelResponse(response, research, metrics);
    } catch (error) {
      const duration = Date.now() - startTime;
      return this.handleGenerationError(error, research, duration);
    }
  }

  /**
   * Prepare generation tools and prompt
   */
  private prepareGenerationTools(
    research: ResearchData,
    options: BlogContentOptions,
    relevantSections: RelevantSection[] = [],
    draftContent?: string,
  ) {
    // Create the blog content generation tool
    const blogContentTool = this.createFunctionTool({
      name: 'generate_blog_content',
      description: 'Generate structured blog content based on research data',
      parameters: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: 'The blog post title',
          },
          content: {
            type: 'string',
            description: 'The complete blog post content in HTML format',
          },
        },
        required: ['title', 'content'],
      },
    });

    // Log the tool definition
    agentDebugLogger.logToolCall('BlogWriterAgent', 'generate_blog_content', blogContentTool, {
      status: 'Preparing tool',
    });

    // Build the content generation prompt
    const contentPrompt = this.buildContentGenerationPrompt(
      research,
      options,
      relevantSections,
      draftContent,
    );

    // Log the prompt for debugging
    agentDebugLogger.logAgentInput('BlogWriterAgent_ContentPrompt', {
      prompt: contentPrompt,
      research_summary: {
        topic: research.topic,
        keyFindings_count: research.keyFindings?.length || 0,
        statistics_count: research.statistics?.length || 0,
        contentIdeas_count: research.contentIdeas?.length || 0,
        keywordSuggestions_count: research.keywordSuggestions?.length || 0,
      },
      hasDraftContent: !!draftContent,
      relevantSectionsCount: relevantSections.length,
    });

    return { contentPrompt, blogContentTool };
  }

  /**
   * Call the model to generate content
   */
  private async callContentGenerationModel(contentPrompt: string, blogContentTool: any) {
    const toolSet = {
      generate_blog_content: blogContentTool,
    };

    logger.debug(
      `Calling content generation model: ${JSON.stringify({
        model: this.options.model,
        provider: this.options.provider,
      })}`,
    );

    return this.generateText(contentPrompt, {
      tools: toolSet,
      tool_choice: { type: 'function', function: { name: 'generate_blog_content' } },
    });
  }

  /**
   * Calculate metrics based on model response
   */
  private calculateMetrics(startTime: number, response: any): GenerationMetrics {
    const duration = Date.now() - startTime;
    const usage = response?.usage || { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };

    // Use the centralized cost calculator
    const costResult = calculateCost(
      usage,
      this.options.provider,
      this.options.model,
      'BlogWriterAgent',
    );

    // Log usage and cost data
    agentDebugLogger.logUsageAndCost(
      'BlogWriterAgent',
      this.options.model,
      usage,
      costResult.totalCost,
    );

    logger.debug(
      `BlogWriterAgent completed in ${duration}ms. Estimated cost: $${costResult.totalCost.toFixed(
        4,
      )}`,
    );

    return { duration, usage, estimatedCost: costResult.totalCost };
  }

  /**
   * Process the model response
   */
  private processModelResponse(
    response: any,
    research: ResearchData,
    metrics: GenerationMetrics,
  ): BlogContent {
    // Try to extract content from tool call
    if (response?.toolCalls && response.toolCalls.length > 0) {
      const result = this.extractFromToolCall(response, research, metrics);
      if (result) return result;
    }

    // If tool call extraction failed, try to extract from text response
    if (response?.text) {
      return this.extractFromTextResponse(response.text, research, metrics);
    }

    // Return error response if all extraction methods fail
    return this.createErrorResponse(research, 'Failed to generate content', metrics);
  }

  /**
   * Extract content from tool call response
   */
  private extractFromToolCall(
    response: any,
    research: ResearchData,
    metrics: GenerationMetrics,
  ): BlogContent | null {
    const toolCall = response.toolCalls[0];
    let args;

    try {
      if (toolCall?.function?.arguments) {
        if (typeof toolCall.function.arguments === 'string') {
          args = JSON.parse(toolCall.function.arguments);
        } else {
          args = toolCall.function.arguments;
        }
      }
    } catch (error) {
      logger.error(`Error parsing tool arguments: ${error}`);
      return null;
    }

    if (args && args.title && args.content) {
      const result: BlogContent = {
        title: args.title,
        content: args.content,
        metadata: {
          modelUsed: this.options.model,
          usage: metrics.usage,
          estimatedCost: metrics.estimatedCost,
          durationMs: metrics.duration,
        },
      };

      // Log the successful result
      agentDebugLogger.logToolCall(
        'BlogWriterAgent',
        'generate_blog_content',
        { research_topic: research.topic },
        {
          title: args.title,
          contentPreview: args.content.substring(0, 300) + '...',
          durationMs: metrics.duration,
        },
      );

      agentDebugLogger.logAgentOutput('BlogWriterAgent', result);

      return result;
    }

    return null;
  }

  /**
   * Extract content from text response (fallback method)
   */
  private extractFromTextResponse(
    text: string,
    research: ResearchData,
    metrics: GenerationMetrics,
  ): BlogContent {
    const result = this.extractContentFromText(text, research.topic);

    const contentWithMetadata: BlogContent = {
      ...result,
      metadata: {
        modelUsed: this.options.model,
        usage: metrics.usage,
        estimatedCost: metrics.estimatedCost,
        durationMs: metrics.duration,
        extractionMethod: 'fallback_text_extraction',
      },
    };

    // Log the fallback extraction
    agentDebugLogger.logAgentOutput('BlogWriterAgent_FallbackExtraction', contentWithMetadata);

    return contentWithMetadata;
  }

  /**
   * Create an error response
   */
  private createErrorResponse(
    research: ResearchData,
    errorMessage: string,
    metrics: GenerationMetrics,
  ): BlogContent {
    logger.warn(`Failed to generate blog content for topic: ${research.topic} - ${errorMessage}`);

    const errorResult: BlogContent = {
      title: research.topic,
      content: `<h1>${research.topic}</h1><p>Content generation failed.</p>`,
      metadata: {
        modelUsed: this.options.model,
        usage: metrics.usage,
        estimatedCost: metrics.estimatedCost,
        durationMs: metrics.duration,
        error: errorMessage,
      },
    };

    // Log the error result
    agentDebugLogger.logAgentOutput('BlogWriterAgent_Error', errorResult);

    return errorResult;
  }

  /**
   * Handle errors during generation
   */
  private handleGenerationError(
    error: Error,
    research: ResearchData,
    duration: number,
  ): BlogContent {
    logger.debug(
      `Tool call failed: ${JSON.stringify({
        agentName: 'BlogWriterAgent',
        errorMessage: error.message,
        durationMs: duration,
      })}`,
    );
    logger.error(`Error generating blog content: ${error}`);

    const errorResult: BlogContent = {
      title: research.topic,
      content: `<h1>${research.topic}</h1><p>An error occurred during content generation.</p>`,
      metadata: {
        modelUsed: this.options.model,
        durationMs: duration,
        error: error.message,
        stack: error.stack,
      },
    };

    // Log the error
    agentDebugLogger.logAgentOutput('BlogWriterAgent_Exception', errorResult);

    return errorResult;
  }

  /**
   * Extract blog title and content from raw text response
   */
  private extractContentFromText(text: string, fallbackTitle: string): BlogContent {
    const lines = text.split('\n');
    let title = fallbackTitle;
    let content = text;

    // Try to extract title from the first line if it looks like a heading
    if (lines.length > 0) {
      if (lines[0].startsWith('<h1>')) {
        title = lines[0].replace(/<h1>(.*?)<\/h1>/, '$1');
        content = lines.slice(1).join('\n').trim();
      } else if (!lines[0].includes(' ') || lines[0].length < 100) {
        // If first line is short and doesn't have spaces, it might be a title
        title = lines[0];
        content = lines.slice(1).join('\n').trim();
      }
    }

    // If the content doesn't have HTML headings, add the title as a heading
    if (!content.includes('<h1>')) {
      content = `<h1>${title}</h1>\n\n${content}`;
    }

    return { title, content };
  }

  /**
   * Build the content generation prompt using the same style as the original blog-generation service
   */
  private buildContentGenerationPrompt(
    research: ResearchData,
    options: BlogContentOptions,
    relevantSections: RelevantSection[] = [],
    draftContent?: string,
  ): string {
    // Format research data sections
    const keyFindings = Array.isArray(research.keyFindings)
      ? research.keyFindings.map((f: string) => `- ${f}`).join('\n')
      : 'No key findings available';

    const statistics = Array.isArray(research.statistics)
      ? research.statistics
          .map(
            (stat: { label: string; value: string; source?: string }) =>
              `- **${stat.label}**: ${stat.value}${stat.source ? ` (Source: ${stat.source})` : ''}`,
          )
          .join('\n')
      : 'No statistics available';

    const contentIdeas = Array.isArray(research.contentIdeas)
      ? research.contentIdeas.map((i: string) => `- ${i}`).join('\n')
      : 'No content ideas available';

    // Combine research keywords with SEO keywords
    const keywordSuggestions = Array.isArray(research.keywordSuggestions)
      ? research.keywordSuggestions.join(', ')
      : '';

    const seoKeywords = options.seoInputKeywords?.join(', ') || '';
    const allKeywords = [keywordSuggestions, seoKeywords].filter(Boolean).join(', ');

    // Determine content length based on blog size
    const contentLength = options.blogSize
      ? BLOG_SIZE_MAP[options.blogSize]
      : BLOG_SIZE_MAP[BlogSize.medium];

    // Format relevant sections if available
    let relevantSectionsText = '';
    if (relevantSections && relevantSections.length > 0) {
      relevantSectionsText = `\n\nRELEVANT SOURCE INFORMATION:
${relevantSections
  .map((section) => {
    return `SECTION: ${section.blogSection}
RELEVANT LINES:
${section.relevantLines.map((line) => `- ${line}`).join('\n')}
CONTEXT: ${section.contextSummary}

`;
  })
  .join('\n')}`;
    }

    // Format draft content section if available
    let draftContentSection = '';
    if (draftContent && draftContent.trim()) {
      draftContentSection = `
EXISTING DRAFT CONTENT TO IMPROVE:
<draft_content>
${draftContent}
</draft_content>

IMPORTANT: This existing draft content should be improved, not replaced. Keep the structure, HTML formatting, tables, and charts intact while enhancing:
- Add missing information from the research
- Incorporate relevant source information
- Ensure all SEO keywords are naturally included
- Maintain HTML formatting including any tables or charts
- Apply the requested tone and perspective
`;
    }

    // Build the prompt using similar structure to GENERATE_BLOG_OUTLINE_USER_PROMPT
    return `Craft a unique, human-like blog post based on the research provided. Your mission: create content that's indistinguishable from one written by a creative human blogger.

Topic: "${research.topic}"

Research Data:
<research_data>
Key Findings:
${keyFindings}

Statistics & Data Points:
${statistics}

Content Ideas:
${contentIdeas}

Keywords to include: ${allKeywords}
</research_data>${relevantSectionsText}${draftContentSection}

Guidelines for a natural, AI-detection-proof blog post:
1. Embrace unpredictability: Mix up your structure, use varied language, and throw in surprising elements.
2. Get personal: Weave in (invented) anecdotes, opinions, or experiences that feel authentically human.
3. Think creatively: Develop unique angles or unconventional connections related to the topic.
4. Be imperfect: Include natural "flaws" like brief tangents or informal asides.
5. Avoid formulaic patterns: Each section should feel distinct and organically developed.
6. Preserve all HTML formatting, tables, and charts from the draft content if available.

Writing Requirements:
- Length: ${contentLength}
- Language: ${options.language || 'English'}
- Tone: ${options.blogTone || 'conversational'}
- Point of view: ${options.pov || 'first person'}
- Format content with proper HTML tags (h1, h2, h3, p, ul, li, blockquote, etc.)
- Create a compelling title as the h1
- Structure the content with clear section headings
- Preserve all HTML tables and charts exactly as they appear in the draft content`;
  }
}
