import { AiAgent } from '../../llm/sdk/AiAgent';
import { AiAgentOptions } from '../../llm/sdk/types';
import { calculateCost } from '../../llm/utils/cost-calculator';
import { agentLogger, logger } from '@/common/logger';
import { agentDebugLogger } from '../AgentLogger';

export interface RelevantSection {
  blogSection: string;
  relevantLines: string[];
  contextSummary: string;
}

/**
 * Relevance Extractor Agent
 *
 * Responsible for:
 * - Analyzing source material (transcription, prompt, etc.)
 * - Finding relevant information for each section of the blog
 * - Extracting specific lines that support each section
 * - Providing contextual summaries for improved content generation
 */
export class RelevanceExtractorAgent extends AiAgent {
  constructor(options: AiAgentOptions) {
    super({
      ...options,
      systemPrompt: `You are an expert content researcher who specializes in extracting the most relevant information from source materials for blog content creation.

Your primary job is to analyze source text (transcriptions, user prompts, or research data) and find the specific lines and passages that are most relevant to each section of a blog outline.

For each blog section, you will:
1. Extract the exact lines/passages from the source that are directly relevant
2. Provide a concise summary of the key information for that section
3. Ensure all critical data points, quotes, and examples are captured

IMPORTANT GUIDELINES:
- Maintain original wording from source materials when extracting direct lines
- Capture complete thoughts rather than partial sentences
- When working with transcriptions, include speaker identifications if present
- Preserve technical terminology exactly as it appears in source materials
- When dealing with numerical data, ensure all figures and statistics are extracted accurately
- Look for context clues that might add depth to each section
- When multiple sources contradict, extract all perspectives and note the differences

Your goal is to ensure that each blog section has the strongest possible foundation of factual information from the source materials.`,
    });
  }

  /**
   * Extract relevant information from source material for blog sections
   */
  async run(input: {
    sourceContent: string;
    blogSections: { heading: string; subheadings?: string[] }[];
  }): Promise<{ relevantSections: RelevantSection[]; metadata?: any }> {
    // If sourceContent is null/undefined, use an empty string instead of throwing an error
    const sourceContent = input.sourceContent || '';
    if (!sourceContent) {
      logger.error('Missing required input for relevance extraction');
      throw new Error('Missing required source content for relevance extraction');
    }

    if (!input.blogSections || input.blogSections.length === 0) {
      logger.error('Missing required input for relevance extraction');
      throw new Error('Missing required blog sections for relevance extraction');
    }

    try {
      // Record start time for cost calculation
      const startTime = Date.now();

      // Log the start of extraction
      logger.debug(`Starting relevance extraction for ${input.blogSections.length} blog sections`);
      agentLogger.logEvent('RelevanceExtractorAgent', 'Starting relevance extraction', {
        sectionCount: input.blogSections.length,
        sourceContentLength: sourceContent.length,
      });

      // Extract relevant information for each blog section
      const relevantSections = await this.extractRelevantInformation(
        sourceContent,
        input.blogSections,
      );

      // Record end time and calculate duration
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Estimate token usage for cost calculation
      const promptTokens = this.estimateTokens(sourceContent) + 500; // Source + instructions
      const completionTokens = this.estimateTokens(JSON.stringify(relevantSections));
      const totalTokens = promptTokens + completionTokens;

      // Calculate the estimated cost
      const usage = {
        prompt_tokens: promptTokens,
        completion_tokens: completionTokens,
        total_tokens: totalTokens,
      };

      const costResult = calculateCost(
        usage,
        this.options.provider,
        this.options.model,
        'RelevanceExtractorAgent',
      );

      // Log results
      logger.debug(
        `Relevance extraction completed in ${duration}ms. Extracted information for ${relevantSections.length} sections.`,
      );

      agentLogger.logEvent('RelevanceExtractorAgent', 'Completed relevance extraction', {
        durationMs: duration,
        sectionCount: relevantSections.length,
        estimatedCost: costResult.totalCost,
      });

      agentDebugLogger.logAgentOutput('RelevanceExtractorAgent', {
        relevantSections,
        duration,
        estimatedCost: costResult.totalCost,
      });

      return {
        relevantSections,
        metadata: {
          durationMs: duration,
          estimatedCost: costResult.totalCost,
        },
      };
    } catch (error) {
      logger.error(`Error in relevance extraction: ${error.message}`);
      agentLogger.logError(
        'RelevanceExtractorAgent',
        `Error in relevance extraction: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Extract relevant information for each blog section
   */
  private async extractRelevantInformation(
    sourceContent: string,
    blogSections: { heading: string; subheadings?: string[] }[],
  ): Promise<RelevantSection[]> {
    // Create a prompt for the relevance extraction
    const prompt = this.createExtractionPrompt(sourceContent, blogSections);

    try {
      // Generate content using the relevance extraction prompt
      const response = await this.generateText(prompt);

      if (!response?.text) {
        logger.error('No response received from AI provider');
        return [];
      }

      // Parse the response to extract the relevant sections
      return this.parseRelevanceResponse(response.text, blogSections);
    } catch (error) {
      logger.error(`Error in extractRelevantInformation: ${error.message}`);
      return [];
    }
  }

  /**
   * Create a prompt for extracting relevant information
   */
  private createExtractionPrompt(
    sourceContent: string,
    blogSections: { heading: string; subheadings?: string[] }[],
  ): string {
    // Create a string of blog section headings and subheadings
    const sectionsFormatted = blogSections
      .map((section, index) => {
        const subheadingsText = section.subheadings?.length
          ? `\n   Subheadings: ${section.subheadings.join(', ')}`
          : '';

        return `${index + 1}. "${section.heading}"${subheadingsText}`;
      })
      .join('\n');

    // Create the extraction prompt
    return `I need you to extract the most relevant information from the following source content for each section of a blog post.

BLOG OUTLINE SECTIONS:
${sectionsFormatted}

SOURCE CONTENT:
${sourceContent}

For each blog section, please:
1. Find the most relevant lines or passages from the source content
2. Provide a brief summary of key information for that section (2-3 sentences)

Format your response as follows for each section:

SECTION: [Section Heading]

RELEVANT LINES:
- [Exact quote/line from source 1]
- [Exact quote/line from source 2]
- [etc.]

CONTEXT SUMMARY:
[Brief 2-3 sentence summary of the key information for this section]

===

Proceed through all sections systematically. If a section has no relevant information in the source content, indicate "No relevant information found" in the RELEVANT LINES section.`;
  }

  /**
   * Parse the AI response to extract the relevant sections
   */
  private parseRelevanceResponse(
    responseText: string,
    _blogSections: { heading: string; subheadings?: string[] }[],
  ): RelevantSection[] {
    const relevantSections: RelevantSection[] = [];

    try {
      // Split the response by section markers
      const sectionMatches = responseText.split(/SECTION:/);

      // Skip the first element if it's just an introduction
      const sectionsToProcess =
        sectionMatches[0].trim().length === 0 ? sectionMatches.slice(1) : sectionMatches;

      for (let i = 0; i < sectionsToProcess.length; i++) {
        const sectionText = sectionsToProcess[i].trim();
        if (!sectionText) continue;

        // Extract section heading (first line)
        const lines = sectionText.split('\n');
        const blogSection = lines[0].trim();

        // Extract relevant lines
        const relevantLinesStart = sectionText.indexOf('RELEVANT LINES:');
        const contextSummaryStart = sectionText.indexOf('CONTEXT SUMMARY:');

        if (relevantLinesStart > -1 && contextSummaryStart > -1) {
          // Extract relevant lines between the markers
          const relevantLinesText = sectionText
            .substring(relevantLinesStart + 'RELEVANT LINES:'.length, contextSummaryStart)
            .trim();

          // Extract context summary
          const contextSummaryText = sectionText
            .substring(contextSummaryStart + 'CONTEXT SUMMARY:'.length)
            .trim();

          // Split relevant lines by bullet points or new lines
          const relevantLines = relevantLinesText
            .split(/\n-|\n•/)
            .map((line) => line.trim())
            .filter((line) => line && line !== 'No relevant information found');

          // Add section to results
          relevantSections.push({
            blogSection,
            relevantLines: relevantLines.length > 0 ? relevantLines : [],
            contextSummary: contextSummaryText,
          });
        }
      }

      return relevantSections;
    } catch (error) {
      logger.error(`Error parsing relevance response: ${error.message}`);
      return [];
    }
  }

  /**
   * Estimate the number of tokens in a text
   */
  private estimateTokens(text: string): number {
    // Rough estimation: ~4 chars per token for English text
    return Math.ceil(text.length / 4);
  }
}
