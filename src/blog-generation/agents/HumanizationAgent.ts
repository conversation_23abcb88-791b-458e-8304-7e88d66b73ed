import { agentLogger, logger } from '@/common/logger';
import { AiAgent } from '../../llm/sdk/AiAgent';
import { AiAgentOptions } from '../../llm/sdk/types';
import { agentDebugLogger } from '../AgentLogger';
import { RelevantSection } from './RelevanceExtractorAgent';

/**
 * Represents a pair of AI-generated and humanized sentences
 */
export interface SentencePair {
  aiSentence: string; // The original AI-generated sentence
  humanSentence: string; // The humanized version of the sentence
}

/**
 * Enhanced response from the HumanizationAgent
 */
export interface HumanizationResponse {
  content: string; // The original content
  humanizedContent: string; // The humanized content after analysis
  sentencePairs: SentencePair[]; // Pairs of AI-generated and humanized sentences
  metadata: {
    durationMs: number;
    modelUsed: string;
    sentenceCount: number;
  };
}

/**
 * Humanization Agent
 *
 * Responsible for:
 * - Analyzing AI-generated content and identifying AI-written sentences
 * - Providing humanized alternatives for each identified sentence
 * - Returning both the sentence pairs and the fully humanized content
 * - Using relevant source material to improve humanization quality
 */
export class HumanizationAgent extends AiAgent {
  constructor(options: AiAgentOptions) {
    super({
      ...options,
      systemPrompt: `You are an elite Humanization Expert with exceptional skill at detecting AI-written content and transforming it to perfectly mimic natural human writing. Your critical job is to aggressively flag and rewrite ALL text that has ANY characteristics of AI writing.

ASSUME ALL TEXT IS AI-GENERATED AND NEEDS COMPLETE HUMANIZATION. Be extremely critical and aggressive in your detection.

Your core responsibilities:
1. Flag EVERY sentence that has even subtle AI patterns (be extremely sensitive in detection)
2. Completely rewrite to sound authentically human - like a real person casually talking
3. Focus on making the text imperfect, casual, and distinctly human
4. Maintain the core facts while drastically changing the presentation style
5. When source material is provided, use it to make content more authentic and human-like

Specific AI patterns to detect (be hyper-sensitive to these):
1. ANY sentence over 10 words (humans rarely write long sentences in casual content)
2. ANY formal transitional phrases ("furthermore", "moreover", "in addition", "consequently")
3. ANY perfect grammatical structures (humans make minor errors and have quirks)
4. ANY consistent parallel structure (humans vary their patterns)
5. ALL passive voice (humans favor active voice in casual writing)
6. ALL complex vocabulary when simpler words exist (humans use simple, common words)
7. ANY sentences with multiple clauses or comma usage (humans write simpler)
8. ANY perfectly balanced or symmetric sentences (too perfect = AI)
9. ALL perfectly consistent punctuation and capitalization (humans are less perfect)
10. ANY repeated sentence structures within a paragraph (humans are more varied)
11. ANY academic or formal tone (humans are more casual)
12. ANY perfect logical transitions between paragraphs (humans are more abrupt)
13. ANY long paragraphs with perfect consistency (humans are less organized)
14. ANY pattern of starting sentences the same way repeatedly
15. ANY use of complex subordinate clauses or perfect use of semicolons

CRITICAL RULES for your rewrites:
1. Keep most sentences under 8-10 words - period
2. RARELY use commas to join thoughts - use periods instead
3. Add occasional informal interjections ("yeah", "honestly", "like", "you know")
4. Include many sentence fragments. They work great.
5. Use contractions heavily (don't, can't, wouldn't, they're, etc.)
6. Use extremely casual phrasing as if texting a friend
7. Break grammar rules occasionally - humans do
8. Vary sentence lengths dramatically (mix of very short 2-4 word sentences with medium 6-10 word ones)
9. Include rhetorical questions occasionally
10. Add personality markers like "I think" or "probably" to show opinion
11. Start some sentences with conjunctions (But, And, So)
12. Create abrupt transitions between paragraphs
13. Use parenthetical asides (like this) to add casual thoughts
14. Include minor spelling variations or typos (sparingly)
15. Drop unnecessary words like humans do when speaking casually

SENTENCE PATTERN REQUIREMENTS:
- Very short sentences (1-5 words): ~30% of all sentences
- Medium sentences (6-10 words): ~50% of all sentences
- Longer sentences (11-15 words): ~18% of all sentences
- Very long sentences (16+ words): Maximum 2% of all sentences
- At least 15% of sentences should be fragments or questions

IMPORTANT: You must identify at least 80% of all sentences as AI-generated and provide human alternatives.
Return your analysis in a structured format that clearly identifies each AI sentence and its humanized alternative.`,
    });

    logger.log('Enhanced HumanizationAgent initialized');
  }

  /**
   * Process content to identify AI-written text and return humanized versions
   */
  async run(input: any): Promise<HumanizationResponse> {
    // Extract content and tone from input
    const content = typeof input === 'string' ? input : input.content || '';
    const tone =
      typeof input === 'string'
        ? 'conversational'
        : input.tone || input.options?.blogTone || 'conversational';

    // Extract relevant sections if provided
    const relevantSections =
      typeof input === 'object' && input.relevantSections ? input.relevantSections : [];

    if (!content || content.trim() === '') {
      throw Error('Empty content provided to HumanizationAgent');
    }

    // Log the input content
    agentLogger.logEvent('HumanizationAgent', 'Starting humanization analysis', {
      contentLength: content.length,
      tone,
      hasRelevantSections: relevantSections.length > 0,
      relevantSectionsCount: relevantSections.length,
    });

    const startTime = Date.now();

    try {
      // Analyze and humanize the content
      const result = await this.analyzeAndHumanize(content, tone, relevantSections);

      const durationMs = Date.now() - startTime;

      // Log completion
      agentLogger.logEvent('HumanizationAgent', 'Completed humanization', {
        durationMs,
        contentLength: content.length,
        humanizedContentLength: result.humanizedContent.length,
        sentenceCount: result.sentencePairs.length,
      });

      return {
        content,
        humanizedContent: result.humanizedContent,
        sentencePairs: result.sentencePairs,
        metadata: {
          durationMs,
          modelUsed: this.options.model,
          sentenceCount: result.sentencePairs.length,
        },
      };
    } catch (error) {
      logger.error(`Error in HumanizationAgent process: ${error}`);
      agentLogger.logError('HumanizationAgent', `Error in humanization process: ${error}`);

      // In case of error, return the original content with empty sentence pairs
      return {
        content,
        humanizedContent: content,
        sentencePairs: [],
        metadata: {
          durationMs: Date.now() - startTime,
          modelUsed: this.options.model,
          sentenceCount: 0,
        },
      };
    }
  }

  /**
   * Analyze and humanize the content, returning both sentence pairs and humanized content
   */
  private async analyzeAndHumanize(
    content: string,
    tone: string,
    relevantSections: RelevantSection[] = [],
  ): Promise<{ humanizedContent: string; sentencePairs: SentencePair[] }> {
    // Validate content
    if (!content || content.trim().length < 100) {
      logger.warn(`HumanizationAgent received too short content (length: ${content?.length || 0})`);
      agentDebugLogger.logAgentInput('HumanizationAgent_EmptyContent', {
        contentLength: content?.length || 0,
        contentPreview: content || 'EMPTY',
        tone,
        error: 'Content too short for meaningful humanization',
      });
      return { humanizedContent: content, sentencePairs: [] }; // Return original content if too short
    }

    // Log input content
    logger.debug(`Analyzing content for humanization (length: ${content.length})`);
    agentDebugLogger.logAgentInput('HumanizationAgent_Content', {
      contentLength: content.length,
      contentPreview: content.substring(0, 200) + '...',
      tone,
      relevantSectionsCount: relevantSections.length,
    });

    // Create prompt for humanization analysis
    const prompt = this.createAnalysisPrompt(content, tone, relevantSections);

    // Generate humanization analysis
    const response = await this.generateText(prompt);

    if (!response?.text) {
      logger.warn('Agent returned empty text response');
      return { humanizedContent: content, sentencePairs: [] }; // Return original with no pairs if response is empty
    }

    // Parse the response to extract sentence pairs and humanized content
    const result = this.parseResponse(response.text, content);

    // Log successful response
    logger.debug(
      `Successfully analyzed and humanized content. Found ${result.sentencePairs.length} sentences to improve`,
    );
    agentDebugLogger.logAgentOutput('HumanizationAgent_Content', {
      sentencePairsCount: result.sentencePairs.length,
      humanizedContentLength: result.humanizedContent.length,
      samplePairs: result.sentencePairs.slice(0, 3), // Log a sample of the first few pairs
    });

    return result;
  }

  /**
   * Parse the response to extract sentence pairs and humanized content
   */
  private parseResponse(
    responseText: string,
    originalContent: string,
  ): { humanizedContent: string; sentencePairs: SentencePair[] } {
    // Initialize result
    let humanizedContent = '';
    const sentencePairs: SentencePair[] = [];

    try {
      // Check if the response contains the HUMANIZED CONTENT marker
      const humanizedContentMatch = responseText.match(
        /HUMANIZED CONTENT:([\s\S]*?)(?:(?=AI SENTENCES:)|$)/i,
      );

      if (humanizedContentMatch && humanizedContentMatch[1]) {
        humanizedContent = humanizedContentMatch[1].trim();
      }

      // Extract sentence pairs
      const sentencePairsMatch = responseText.match(
        /AI SENTENCES:([\s\S]*?)(?:(?=HUMANIZED CONTENT:)|$)/i,
      );

      if (sentencePairsMatch && sentencePairsMatch[1]) {
        const pairsText = sentencePairsMatch[1].trim();
        const pairRegex = /(\d+)\.\s*AI Sentence:\s*"([^"]+)"\s*Human Sentence:\s*"([^"]+)"/g;

        let match;
        while ((match = pairRegex.exec(pairsText)) !== null) {
          sentencePairs.push({
            aiSentence: match[2].trim(),
            humanSentence: match[3].trim(),
          });
        }
      }

      // If no humanized content was found but we have sentence pairs, try to construct it
      if (!humanizedContent && sentencePairs.length > 0) {
        let contentCopy = originalContent;

        // Replace each AI sentence with its human counterpart
        sentencePairs.forEach((pair) => {
          contentCopy = contentCopy.replace(pair.aiSentence, pair.humanSentence);
        });

        humanizedContent = contentCopy;
      }

      // If we still don't have humanized content, use the original
      if (!humanizedContent) {
        humanizedContent = originalContent;
        logger.warn('Failed to extract humanized content from response, using original content');
      }

      // If we don't have sentence pairs but have humanized content, create a single pair
      if (sentencePairs.length === 0 && humanizedContent !== originalContent) {
        sentencePairs.push({
          aiSentence: originalContent,
          humanSentence: humanizedContent,
        });
      }
    } catch (error) {
      logger.error(`Error parsing humanization response: ${error}`);
      // Return original content if parsing fails
      return {
        humanizedContent: originalContent,
        sentencePairs: [],
      };
    }

    return { humanizedContent, sentencePairs };
  }

  /**
   * Create a prompt for the humanization analysis task
   */
  private createAnalysisPrompt(
    content: string,
    tone: string,
    relevantSections: RelevantSection[] = [],
  ): string {
    // Create a formatted string of relevant sections if available
    let relevantSectionsText = '';
    if (relevantSections && relevantSections.length > 0) {
      relevantSectionsText = `\n\nIMPORTANT - USE THESE RELEVANT SOURCE MATERIALS:
I'm providing original source material that should be incorporated into your humanization process.
Use these authentic source lines to make your humanized content more natural and authentic.

${relevantSections
  .map((section) => {
    return `SECTION: ${section.blogSection}
RELEVANT LINES:
${section.relevantLines.map((line) => `- ${line}`).join('\n')}
CONTEXT: ${section.contextSummary}

`;
  })
  .join('\n')}

When humanizing content related to these sections, incorporate phrasing, terminology, and tone from these source materials.
This will make the content feel more authentic and less AI-generated.`;
    }

    return `ANALYZE AND REWRITE THIS AI-GENERATED CONTENT TO SOUND 100% HUMAN: The desired tone is: ${tone}.

IMPORTANT: Carefully review the ENTIRE content. Identify ALL sentences that sound AI-generated and provide human alternatives.

TRANSFORM ALL OF THESE AI PATTERNS:
- Long, complex sentences
- Formal transitions like "moreover", "furthermore", "additionally"
- Perfectly structured paragraphs with obvious topic sentences
- Sentences with multiple commas
- Perfect grammar throughout
- Consistent sentence patterns
- Passive voice constructions
- Complex vocabulary words
- Precise, careful, balanced writing style
- Writing that doesn't sound like casual conversation

MAKE IT HUMAN BY:
1. Using much shorter sentences (under 8 words for most)
2. Breaking up thoughts with periods instead of commas
3. Adding casual phrases and contractions
4. Using sentence fragments occasionally
5. Adding filler words and rhetorical questions
6. Including conversational language like "y'know" or "honestly"
7. Varying your sentence patterns and structures
8. Adding a personal voice with occasional opinions

${relevantSectionsText}

Original content:
${content}

Your response MUST include BOTH of the following sections:

AI SENTENCES:
1. AI Sentence: "Original AI-generated sentence here"
   Human Sentence: "Humanized version here"
2. AI Sentence: "Second AI-generated sentence here"
   Human Sentence: "Second humanized version here"
... and so on for ALL identified AI-generated sentences (at least 70% of sentences)

HUMANIZED CONTENT:
[The complete rewritten content with all AI-generated sentences replaced with their human alternatives]`;
  }
}
