import { Logger, type NestApplicationOptions } from '@nestjs/common';
import type { IServerAdapter } from '@bull-board/api/dist/typings/app';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { urlencoded, json } from 'express';
import { createBullBoard } from '@bull-board/api';
import { ExpressAdapter } from '@bull-board/express';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { NestFactory } from '@nestjs/core';
import { RedisStore } from 'connect-redis';
import passport from 'passport';
import session from 'express-session';
import Redis from 'ioredis';
import auth from 'basic-auth';
import * as path from 'path';
import * as fs from 'fs';

import { APP_PORT, CORS_ALLOWED_HOST, JOB_QUEUES } from '@common/constants';
import { UnauthorizedExceptionFilter } from '@common/filters/unauthorized-exception.filter';
import { GlobalExceptionFilter } from '@common/filters/global-exception.filter';
import { verifyBullCredentials } from '@common/helpers';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import rawBodyMiddleware from '@common/middlewares/rawBody.middleware';
import config from '@/common/configs/config';

import { setupProcessErrorHandlers } from './process-error-handler';
import { AppModule } from './app.module';

const getHttpsOptions = (): NestApplicationOptions => {
  const IS_HTTPS = process.env.HTTPS === 'true';
  if (!IS_HTTPS) return {};

  const projectRoot = path.resolve(process.cwd(), '');
  const certsPath = path.join(projectRoot, 'certs');
  const IS_DEV = process.env.NODE_ENV === 'development';

  const keyPath = path.join(certsPath, 'localhost-key.pem');
  const certPath = path.join(certsPath, 'localhost.pem');

  if (IS_DEV && (!fs.existsSync(keyPath) || !fs.existsSync(certPath))) {
    console.error(
      '\u001b[31m \u274C Certificate files (localhost-key.pem and localhost.pem) not found in the certs directory. Please install the certificate files before running the server in the development environment. \u001b[0m',
    );
    return process.exit(1);
  }

  const httpsOptions = IS_DEV
    ? {
        httpsOptions: {
          key: fs.readFileSync(path.join(certsPath, 'localhost-key.pem')),
          cert: fs.readFileSync(path.join(certsPath, 'localhost.pem')),
        },
      }
    : {};

  return httpsOptions;
};

async function bootstrap() {
  console.log('GOOGLE_VERTEX_PROJECT', process.env.GOOGLE_VERTEX_PROJECT);
  console.log('GOOGLE_VERTEX_LOCATION2', process.env.GOOGLE_VERTEX_LOCATION2);
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: true,
    ...getHttpsOptions(),
  });

  // Trust proxy headers
  app.set('trust proxy', true);

  // Validation
  // app.useGlobalPipes(
  //   new ValidationPipe({ transform: true, whitelist: true, disableErrorMessages: false }),
  // );

  app.setViewEngine('pug');

  const options = new DocumentBuilder()
    .setTitle('Blog API')
    .setDescription('The Blog API description')
    .setVersion('1.0')
    .addTag('blogs')
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api', app, document);

  // Enable CORS
  app.enableCors({
    origin: CORS_ALLOWED_HOST,
    methods: ['GET', 'HEAD', 'PUT', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
  });

  // Configure logger before any other middleware
  app.useLogger(app.get(Logger));

  // Bull Board
  const bullExpressAdapter = new ExpressAdapter();

  const queues = Object.values(JOB_QUEUES).map((queueName) => app.get(`BullQueue_${queueName}`));

  // Increase max listeners for each queue
  queues.forEach((queue) => {
    queue.setMaxListeners(20);
  });

  createBullBoard({
    queues: queues.map((queue) => new BullAdapter(queue)),
    serverAdapter: bullExpressAdapter as unknown as IServerAdapter,
  });

  bullExpressAdapter.setBasePath('/bullboard');

  // Body Parser
  app.use(rawBodyMiddleware());
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  // Passport
  app.use(passport.initialize());
  app.use(
    session({
      store: new RedisStore({ client: new Redis(config().cache.redis) }),
      secret: 'your-secret-key-here',
      saveUninitialized: false,
      resave: false,
      cookie: {
        secure: process.env.HTTPS === 'true' || !config().isDev,
        maxAge: 1000 * 60 * 60 * 24, // 1 day
      },
    }),
  );

  app.use(
    '/bullboard',
    (req, res, next) => {
      const credentials = auth(req);

      if (!credentials || !verifyBullCredentials(credentials.name, credentials.pass)) {
        res.statusCode = 401;
        res.setHeader('WWW-Authenticate', 'Basic realm="example"');
        res.end('Access denied');
      } else {
        next();
      }
    },
    bullExpressAdapter.getRouter(),
  );
  const queue = app.get(`BullQueue_${JOB_QUEUES.BLOG_CREDITS}`);

  // Schedule the job will run midnight on the first day of every month
  await queue.add(null, { repeat: { cron: '0 0 1 * *' } });

  if (process.env.NODE_ENV === 'production') {
    app.enableShutdownHooks();
  }
  app.useGlobalFilters(new GlobalExceptionFilter());
  app.useGlobalFilters(new UnauthorizedExceptionFilter());

  const slackService = app.get(SlackService);
  setupProcessErrorHandlers(slackService);

  await app.listen(APP_PORT, '0.0.0.0');
  console.log(`Server is running on port ${APP_PORT}`);
  app.flushLogs();
}
bootstrap();
