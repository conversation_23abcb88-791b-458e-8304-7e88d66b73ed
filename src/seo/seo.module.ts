import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SeoService } from './seo.service';
import { BlogModule } from '@/blog/blog.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Seo<PERSON>ache, SeoCacheSchema } from './cache/cache.model';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './seo.controller';
import { SearchRelevantContentProcessor } from './processors/searchRelevantContent.processor';
import { KeywordAnalysisProcessor } from './processors/keywordAnalysis.processor';
import { JobModule } from '@/job/job.module';
import { SeoScoringProcessor } from './processors/seoScoring.processor';

@Module({
  imports: [
    JobModule,
    BlogModule,
    MongooseModule.forFeature([{ name: SeoCache.name, schema: SeoCacheSchema }]),
  ],
  providers: [
    SeoService,
    SearchRelevantContentProcessor,
    KeywordAnalysisProcessor,
    SeoScoringProcessor,
  ],
  controllers: [SeoController],
})
export class SeoModule {}
