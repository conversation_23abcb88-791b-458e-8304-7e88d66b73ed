// بسم الله الرحمن الرحيم

import { Blog } from '@/blog/blog.model';
import { union } from 'fp-ts/Array';
import { Eq } from 'fp-ts/string';
import { Trie } from '../match/trie';
import { extract, makeRoot } from '@/common/utils/parse';
import { SeoCache } from '../cache/cache.model';
import { pipe } from 'fp-ts/lib/function';
import { ReturnType } from '@sinclair/typebox';

/** HTML tags for which SEO Keywords are considered */
const Tag = <const>['title', 'meta', 'h1', 'h2', 'h3', 'content'];
export type Tag = (typeof Tag)[number];

/**
 * Converts the Blog's seoKeywords data model to Map
 * @param seoKeywords - seoKeywords from Blog Model
 * @returns a Map of urls and their associated tags' keywords
 */
const deserialise = (seoKeywords: Blog['seoKeywords']) =>
  new Map(
    seoKeywords.map(
      ({ url, ...rest }) =>
        <const>[new URL(url), new Map(Object.entries(rest)) as Map<Tag, string[]>],
    ),
  );

/**
 * Generates set of unique keywords per HTML tag
 * @param keywords - the keywords to combine
 * @returns a Map of tags and their respective unique keywords
 */
const generateKeywordsSet = (keywords: ReturnType<typeof deserialise>) =>
  [...keywords.values()].reduce(
    (accumulator, element) =>
      new Map(
        [...accumulator].map(([tag, words]) => <const>[tag, union(Eq)(words, element.get(tag))]),
      ),
    new Map(Tag.map((tag) => <const>[tag, []])),
  );

/**
 * Given the keywords for HTML tags generates the automata required for {@link https://cp-algorithms.com/string/aho_corasick.html | Aho-Corasick String Search Algorithm}
 * @param keywordsSet - The keywords for HTML tags to be searched for
 * @returns A Map of Tries for every keyword set
 */
const generateTries = (keywordsSet: ReturnType<typeof generateKeywordsSet>) => {
  return new Map(
    [...keywordsSet].map(
      ([tag, keywordSet]) =>
        [
          tag,
          new Trie(keywordSet, {
            caseSensitive: false,
            removeWhiteSpaces: true,
            allowOverlap: false,
            wholeWords: true,
          }),
        ] as const,
    ),
  );
};

/**
 * Counts the frequency of keywords in HTML
 * @param tries - The automata for string search for each HTML tag
 * @param htmls - An array of htmls to be analysed
 * @returns Counts of keywords for each HTMl tag for in the HTML of each supplied URL
 */
const makeHistogram = (
  tries: ReturnType<typeof generateTries>,
  htmls: Array<{ parsedHTML: Record<Tag, string>; url: string }>,
) =>
  new Map(
    htmls
      .map(
        ({ url, parsedHTML }) =>
          <const>[new URL(url), new Map(Object.entries(parsedHTML) as Array<[Tag, string]>)],
      )
      .map(([url, parsedHTML]) => [
        url,
        new Map(
          [...parsedHTML].map(
            ([tag, text]) => <const>[tag, tries.get(tag).getStringOccurrences(text)],
          ),
        ),
      ]),
  );

/**
 * Groups an array of objects by a key
 * @param array Array to group
 * @param key Key to group by
 * @returns Record with grouped items
 */
function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  if (!Array.isArray(array)) {
    throw new Error('Input must be an array');
  }

  return array.reduce(
    (groups, item) => {
      if (!item) return groups;
      const value = String(item[key] ?? '');
      groups[value] = groups[value] || [];
      groups[value].push(item);
      return groups;
    },
    {} as Record<string, T[]>,
  );
}

/**
 * Generates a frequency chart from a histogram.
 *
 * @param histogram - The histogram to process, which is the return type of the `makeHistogram` function.
 * @returns A nested Map structure where the first level is grouped by Tag, the second level is grouped by keyword,
 * and the third level maps URLs to their respective frequencies.
 *
 * The structure of the returned Map is:
 * Map<Tag, Map<string, Map<URL, number>>>
 * - Tag: The tag associated with the keywords.
 * - string: The keyword.
 * - URL: The URL where the keyword was found.
 * - number: The frequency of the keyword in the given URL.
 */
function makeFrequencyChart(
  histogram: ReturnType<typeof makeHistogram>,
): Map<Tag, Map<string, Map<URL, number>>> {
  if (!histogram) {
    throw new Error('Histogram input is required');
  }

  try {
    // Step 1: Flatten the histogram into an array of records
    const flatRecords = Array.from(histogram.entries() || []).flatMap(([url, tagsMap]) => {
      if (!tagsMap) return [];
      return Array.from(tagsMap.entries() || []).flatMap(([tag, keywordsMap]) => {
        if (!keywordsMap) return [];
        return Array.from(keywordsMap.entries() || []).map(([keyword, frequency]) => ({
          url,
          tag,
          keyword,
          frequency: typeof frequency === 'number' ? frequency : 0,
        }));
      });
    });

    // Step 2: Group by tag
    const groupedByTag = groupBy(flatRecords, 'tag');

    // Step 3: Create the final nested Map structure
    return new Map(
      Object.entries(groupedByTag).map(([tag, records]) => {
        if (!records) return [tag as Tag, new Map()];

        // Group keywords for each tag
        const keywordGroups = groupBy(
          records
            .filter((record) => record && typeof record === 'object')
            .map(({ tag: _, ...rest }) => rest),
          'keyword',
        );

        // Create Map of keyword to URL frequencies
        const keywordMap = new Map(
          Object.entries(keywordGroups).map(([keyword, entries]) => {
            if (!entries || !Array.isArray(entries)) return [keyword, new Map()];
            return [
              keyword,
              new Map(
                entries
                  .filter((entry) => entry && entry.url && typeof entry.frequency === 'number')
                  .map(({ url, frequency }) => [url, frequency]),
              ),
            ] as const;
          }),
        );

        return [tag as Tag, keywordMap];
      }),
    );
  } catch (error) {
    console.error('Error in makeFrequencyChart:', error);
    throw new Error(
      `Failed to generate frequency chart: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Counts the minimum & maximum occurences of keywords
 * @param data - The frequency chart data
 * @returns A range chart showcasing the minmax occurrences of associated keyword for each tag
 */
function makeRangeChart(
  data: ReturnType<typeof makeFrequencyChart>,
): Map<Tag, Map<string, [min: number, max: number]>> {
  if (!data) {
    throw new Error('Input data is required for range chart generation');
  }

  try {
    return new Map(
      [...data].map(
        ([tag, keywordsMap]) =>
          <const>[
            tag,
            new Map(
              [...keywordsMap].map(
                ([keyword, urlMap]) =>
                  <const>[keyword, [Math.min(...urlMap.values()), Math.max(...urlMap.values())]],
              ),
            ),
          ],
      ),
    );
  } catch (error) {
    console.error('Error in makeRangeChart:', error);
    throw new Error(
      `Failed to generate range chart: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Parses HTML into text for selected tags
 * @param html - The html to be parsed
 * @returns A Map of tags and their associated text
 */
export const observeHTML = (html: string) => {
  const $ = makeRoot(html);
  return new Map([
    ['title', extract.pageTitle($)],
    ['meta', extract.metaDescription($)],
    ['h1', extract.tagText($, 'h1').join(' ')],
    ['h2', extract.tagText($, 'h2').join(' ')],
    ['h3', extract.tagText($, 'h3').join(' ')],
    ['content', extract.bodyText($)],
  ] as const);
};

/**
 * SEO Rating Algorithm, computes the SEO Scores based on chosen keywords
 * @param seoKeywords - The keywords chosen by LLM
 * @param htmls - The online texts to score
 * @returns The rangeChart of keywords
 */
export function analyseTexts(seoKeywords: Blog['seoKeywords'], htmls: SeoCache[]) {
  if (!seoKeywords || !Array.isArray(seoKeywords)) {
    throw new Error('SEO keywords must be an array');
  }

  if (!htmls || !Array.isArray(htmls)) {
    throw new Error('HTMLs must be an array');
  }

  try {
    return pipe(
      seoKeywords,
      deserialise,
      generateKeywordsSet,
      generateTries,
      (tries) => makeHistogram(tries, htmls),
      makeFrequencyChart,
      (fqc) => {
        if (!fqc) {
          return new Map(); // Return empty map if frequency chart is null
        }
        // Filter content keywords
        const contentKeywords = fqc.get('content');
        if (!contentKeywords) return fqc;

        return fqc.set(
          'content',
          new Map(
            Array.from(contentKeywords.entries()).filter(
              ([keyword, occurences]) =>
                keyword && keyword.length > 3 && occurences && occurences.size >= 2,
            ),
          ),
        );
      },
      makeRangeChart,
    );
  } catch (error) {
    console.error('Error in analyseTexts:', error);
    throw new Error(
      `Failed to analyze texts: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Calculates the counts of keywords within a blog content based on a given range chart.
 *
 * @param blog - The blog content as a string.
 * @param rangeChart - A map where the keys are tags and the values are maps of keywords to their range counts.
 * @returns A map where the keys are keywords and the values are objects containing the count, minimum, and maximum range for each keyword.
 */
export function getCounts(blog: string, rangeChart: ReturnType<typeof makeRangeChart>) {
  if (!blog || !rangeChart) {
    throw new Error('Blog content and range chart are required');
  }

  const localhost = new URL('https://127.0.0.1');
  const keywordsSet = new Map(
    [...rangeChart].map(
      ([tag, keywordRangeCount]) => <const>[tag, Array.from(keywordRangeCount.keys())],
    ),
  );
  const histogram = makeHistogram(
    generateTries(keywordsSet),
    Array.of({
      url: localhost.toString(),
      parsedHTML: Object.fromEntries([...observeHTML(blog)]) as Record<Tag, string>,
    }),
  );

  const counts = [...histogram.values()].at(0);

  return new Map(
    [...rangeChart].map(
      ([tag, keywordsRange]) =>
        <const>[
          tag,
          new Map(
            [...keywordsRange].map(([keyword, range]) => {
              return <const>[
                keyword,
                [counts.get(tag).get?.(keyword) ?? 0, ...range] as [
                  count: number,
                  minimum: number,
                  maximum: number,
                ],
              ];
            }),
          ),
        ],
    ),
  );
}
