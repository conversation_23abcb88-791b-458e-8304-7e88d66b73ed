// بسم الله الرحمن الرحيم

// Type-safe version with proper inference
type MapToRecord<M> =
  M extends Map<infer K, infer V>
    ? K extends string | number | symbol
      ? Record<K, V extends Map<any, any> ? MapToRecord<V> : V>
      : never
    : never;
type UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any
  ? R
  : never;
/**
 * Transforms a nested Map structure into a nested Record structure with full type preservation.
 *
 * @param map - The input Map to transform
 * @returns A Record with the same structure and types as the input Map
 */
export function transformMapToRecord<M extends Map<any, any>>(
  map: M,
): UnionToIntersection<MapToRecord<M>> {
  return Object.fromEntries(
    [...map].map(([key, value]) => {
      // Check if the value is a Map and recursively transform it
      if (value instanceof Map) {
        return [key, transformMapToRecord(value)] as const;
      }
      return [key, value] as const;
    }),
  ) as MapToRecord<M>;
}
