// بسم الله الرحمن الرحيم
import { InjectQueue, Process, Processor, OnQueueFailed } from '@nestjs/bull';
import { BlogQueuePayload } from '@/blog/blog.model';
import { Job, Queue } from 'bull';
import { SeoService } from '../seo.service';
import { ScraperService } from '@/blog/scraper.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BaseProcessor } from '@/common/queue/base.processor';
import { Logger } from '@nestjs/common';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { observeHTML, Tag } from '../functions/keywords';

@Processor(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT)
export class SearchRelevantContentProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT;
  protected readonly logger = new Logger(SearchRelevantContentProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) private readonly generateBlogContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT) queue: Queue,
    private readonly scraperService: ScraperService,
    private readonly seoService: SeoService,
    @InjectQueue(JOB_QUEUES.SEO_ANALYZE_KEYWORD) private readonly keywordAnalysisQueue: Queue,
    private readonly gatewayService: GatewayService,
  ) {
    super(queue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { blogTitle, country, blogId, uid, blogOutline, keywords } = job.data;

      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_search_content_inprogress',
      });

      this.logger.debug(`searching relevant content for blog ${blogId}`);

      const htmls = await this.scraperService
        .search(blogTitle, country, 10)
        .then((urls) =>
          Promise.all(
            urls.map(
              async (url) =>
                <const>[url, await this.scraperService.fetchHTML(url).catch((e: Error) => e)],
            ),
          ),
        )
        .then((data) => new Map(data));

      const outputs = [...htmls]
        .filter((entry): entry is [URL, string] => !(entry[1] instanceof Error))
        .map(([url, html]) => <const>[url, observeHTML(html)])

        .filter(([_, parsedHTML]) => {
          // Check word count
          const words = parsedHTML
            .get('content')
            .split(/\s/)
            .filter((shard) => shard.length);
          return words.length > 500 && words.length < 4000;
        })

        .filter(([_, parsedHTML]) => {
          // Check for existence of h1 AND h2 and content
          return (<const>['h1', 'h2', 'content']).every((tag) => parsedHTML.has(tag));
        })
        .map(
          ([url, parsedHTML]) =>
            <const>[url, Object.fromEntries([...parsedHTML]) as Record<Tag, string>],
        );

      if (outputs.length < 3) {
        const error = new Error('Insufficient number of search outputs for SEO');
        error['jobId'] = job.id;
        error['blogId'] = blogId;
        error['outputCount'] = outputs.length;
        throw error;
      }

      await this.seoService.saveToCache(outputs, blogId);
      await this.keywordAnalysisQueue.add(
        { ...job.data, blogOutline, blogTitle, keywords },
        { ...JOB_OPTIONS, jobId: blogId },
      );
      this.logger.debug(`${outputs.length} relevant results found for blog ${blogId}`);

      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_search_content_completed',
      });
    });
  }

  @OnQueueFailed()
  protected async onQueueFailed(job: Job<BlogQueuePayload>, error: Error) {
    const { blogOutline, blogTitle, keywords, blogId, uid } = job.data;

    this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
      _id: blogId,
      seoAnalysisStatus: 'seo_search_content_failed',
    });

    await this.generateBlogContentQueue.add(
      { ...job.data, blogOutline, blogTitle, keywords },
      {
        ...JOB_OPTIONS,
        jobId: blogId,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );
    this.logger.error(
      `Job ${job.id} failed for blog ${job.data.blogId}: ${error.message}`,
      error.stack,
    );
    await super.handleFailedJob(job, error);
  }
}
