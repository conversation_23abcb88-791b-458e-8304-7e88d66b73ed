// بسم الله الرحمن الرحيم

import { Process, Processor } from '@nestjs/bull';
import { InjectQueue } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import mongoose from 'mongoose';
import { SeoService } from '../seo.service';
import { BlogService } from '@/blog/blog.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BaseProcessor } from '@/common/queue/base.processor';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BlogQueuePayload } from '@/blog/blog.model';

@Processor(JOB_QUEUES.SEO_ANALYZE_KEYWORD)
export class KeywordAnalysisProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.SEO_ANALYZE_KEYWORD;

  constructor(
    @InjectQueue(JOB_QUEUES.SEO_ANALYZE_KEYWORD) queue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) private readonly generateBlogContentQueue: Queue,
    private readonly seoService: SeoService,
    private readonly blogService: BlogService,
    private readonly gatewayService: GatewayService,
  ) {
    super(queue);
    this.logger.log('KeywordAnalysis processor initialized');
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>) {
    return this.processWithLock(job, async (job) => {
      const { blogId, uid, blogOutline, blogTitle, keywords } = job.data;
      const blogID = new mongoose.Types.ObjectId(blogId);
      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_keyword_analysis_inprogress',
      });

      this.logger.debug(`Processing keyword analysis for blog ID: ${blogId}`);

      try {
        const data = await this.seoService.loadFromCache(blogID);
        this.logger.debug(`Loaded cache data for blog ID: ${blogId}`);

        const suggestedKeywordsForUrl = await this.seoService.suggestKeywords(blogOutline, data);

        this.logger.debug(`Generated keywords for blog ID: ${blogId}`);
        await this.blogService.saveSeoKeywords(suggestedKeywordsForUrl, blogID);
        this.logger.debug(`Saved keywords for blog ID: ${blogID}`);

        this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
          _id: blogId,
          seoAnalysisStatus: 'seo_keyword_analysis_completed',
        });
      } catch (error) {
        this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
          _id: blogId,
          seoAnalysisStatus: 'seo_keyword_analysis_failed',
        });

        this.logger.error({ blogId, uid, err: error }, 'Error processing keyword analysis');
        throw error;
      } finally {
        await this.generateBlogContentQueue.add(
          { ...job.data, blogOutline, blogTitle, keywords },
          {
            ...JOB_OPTIONS,
            jobId: blogId,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          },
        );
      }
    });
  }
}
