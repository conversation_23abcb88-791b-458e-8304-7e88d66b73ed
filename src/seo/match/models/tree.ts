import { Node, Segment } from '../models';

/**
 * class Tree
 * Represents a tree structure for managing segments and removing overlapping segments.
 */
export class Tree {
  private root: Node;

  /**
   * Creates a new tree with the specified segments.
   *
   * @param segments - An array of segments to initialize the tree.
   */
  constructor(segments: Array<Segment>) {
    this.root = new Node(segments);
  }

  /**
   * Removes overlapping segments from the given array of segments.
   *
   * @param segments - An array of segments to remove overlaps from.
   * @returns An array of segments without overlapping segments.
   */
  removeOverlappingSegments<T extends Segment>(segments: Array<T>): Array<T> {
    const removed: Array<Segment> = new Array<Segment>();
    const sortedSegments = segments.slice().sort((a, b) => b.size() - a.size());

    for (const segment of sortedSegments) {
      if (!this.hasSegment(removed, segment)) {
        removed.push(
          ...this.root.getOverlappingSegments(segment).filter((o) => !this.hasSegment(removed, o)),
        );
      }
    }

    return segments
      .slice()
      .sort((a, b) => a.start - b.start)
      .filter((s) => !this.hasSegment(removed, s));
  }

  /**
   * Checks if a segment is present in the array of segments.
   *
   * @param segments - An array of segments to check against.
   * @param s - The segment to check for.
   * @returns True if the segment is present, false otherwise.
   */
  private hasSegment(segments: Array<Segment>, s: Segment): boolean {
    return segments.some((n) => n.isEqual(s));
  }
}
