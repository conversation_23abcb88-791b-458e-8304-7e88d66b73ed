/**
 * Represents a state in a finite state machine. A State has the following functions:
 * - success: Successfully transferred to another state.
 * - failure: If it is not possible to jump along the string, jump to a shallower node.
 * - match: Where a pattern string has been matched relative to input.
 *
 * The root node is slightly different. The root node does not have a failure function.
 * Its _failure_ refers to moving to the next state according to the string path.
 * Other nodes do have failure states.
 */
export class State {
  private _fail: State | null = null;
  private readonly _depth: number;
  private readonly _match: string[] = [];
  private readonly _success: Map<string, State> = new Map();

  /**
   * Creates a new State with the specified depth.
   * @param depth - The depth of the state in the state machine hierarchy
   */
  constructor(d: number) {
    this._depth = d;
  }

  /**
   * Gets the failure state associated with this state.
   * @returns The failure state or null if none exists
   */
  get failure(): State | null {
    return this._fail;
  }

  /**
   * Sets the failure state associated with this state.
   * @param state - The failure state to be set
   */
  set failure(s: State | null) {
    this._fail = s;
  }

  /**
   * Gets the depth of the state in the state machine hierarchy.
   * @returns number The depth of the state
   */
  get depth(): number {
    return this._depth;
  }

  /**
   * Gets an array of strings representing keywords associated with this state.
   * @returns Array of keywords
   */
  get match(): string[] {
    return this._match;
  }

  /**
   * Gets an array of next states reachable from this state.
   * @returns Array of next states
   */
  get successes(): State[] {
    return Array.from(this._success.values());
  }

  /**
   * Gets an array of transition characters associated with next states.
   * @returns string[] Array of transition characters
   */
  get transitions(): string[] {
    return Array.from(this._success.keys());
  }

  /**
   * Adds keywords to the list of strings representing keywords associated with this state.
   * @param keywords - Array of strings representing keywords to be added
   */
  addMatch(keywords: string[]): void {
    this._match.push(...keywords);
  }

  /**
   * Gets the next state associated with the specified character.
   * @param char - The transition character
   * @param ignoreRoot - If true, ignore the root state when transitioning
   * @returns The next state, or null if no next state is found
   */
  nextState(char: string, ignoreRoot = false): State | null {
    const nextState = this._success.get(char);
    return nextState !== undefined ? nextState : (!ignoreRoot && this.root) || null;
  }

  /**
   * Adds a new state for the specified character.
   * @param char - The transition character
   * @returns The newly added state
   */
  addState(char: string): State {
    const next = this.nextState(char, true) ?? new State(this.depth + 1);
    this._success.set(char, next);
    return next;
  }

  /**
   * Gets the root state if the depth is 0.
   * @returns This state if depth is 0, otherwise null
   */
  get root(): State | null {
    return this._depth === 0 ? this : null;
  }
}
