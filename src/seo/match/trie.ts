import { defaultTrieOptions } from './config/options';
import { Match, State, Tree } from './models';
import { TrieOptions } from './interfaces';
import { isAlphanumeric, isNonEmptyArray, isNullOrEmptyString } from './utilities/utils';

/**
 * of the following implmentations:
 *  - [Efficient String Matching: An Aid to Bibliographic Search](http://cr.yp.to/bib/1975/aho.pdf)
 *  - [robert-bor/aho-corasick](https://github.com/robert-bor/aho-corasick)
 *  - [hankcs/aho-corasick](https://github.com/hankcs/aho-corasick)
 *  - [tanishiking/aho-corasick-js](https://github.com/tanishiking/aho-corasick-js)
 *
 * This implementation adds several additional features, including:
 *  - Extended case sensitivity & trimming of white spaces.
 */
export class Trie {
  private failureStateConstructed = false;
  private readonly root: State = new State(0);
  private options: TrieOptions;
  public keywords: Array<string> = [];
  /**
   * Trie constructor function.
   *
   * @param keywords - An optional array of keywords to initialize the Trie with.
   * @param options - Optional configuration options for the Trie.
   */
  constructor(keywords?: Array<string>, options?: Partial<TrieOptions>) {
    this.options = { ...defaultTrieOptions, ...options };

    if (keywords && isNonEmptyArray(keywords)) {
      this.initialiseKeywords(keywords);
    }
  }

  /**
   * Adds a keyword to the Trie.
   *
   * @param keyword - The keyword to add to the Trie.
   * @throws Throws an error if the provided keyword is null or empty.
   * @returns void
   */
  addKeyword(keyword: string): void {
    if (isNullOrEmptyString(keyword)) {
      throw new Error('Provided string `keyword` is a null or empty string.');
    }

    let currentState = this.root;
    const kw = this.prepareKeywordForInsertion(keyword);

    for (let i = 0; i < kw.length; i++) {
      const char = kw.charAt(i);
      currentState = currentState.addState(char);
    }

    currentState.addMatch([kw]);
    this.keywords.push(kw);
  }

  /**
   * Parses the input text for keyword matches.
   *
   * @param text - The text to parse for keyword matches.
   * @returns Array of Match objects representing keyword matches.
   */
  getMatches(text: string): Array<Match> {
    this.checkForConstructedFailureStates();
    let currentState: State = this.root;
    const matches: Array<Match> = [];

    if (isNullOrEmptyString(text)) {
      return matches;
    }

    for (let pos = 0; pos <= text.length; pos++) {
      const char = this.options.caseSensitive ? text.charAt(pos) : text.charAt(pos).toLowerCase();
      currentState = this.getState(currentState, char);
      matches.push(...this.toMatches(pos, currentState));
    }

    const filteredMatches = this.applyFilteringOptions(text, matches);
    return filteredMatches;
  }

  /**
   * Retrieves all non-matching substrings in the input text.
   *
   * @param text - The input text.
   * @returns Array of non-matching substrings.
   */
  getNonMatches(text: string): Array<string> {
    this.checkForConstructedFailureStates();
    const matches: Array<Match> = this.getMatches(text);

    matches.sort((a, b) => a.start - b.start);

    const nonMatches: Array<string> = new Array<string>();
    let currentIndex = 0;

    for (const match of matches) {
      if (currentIndex < match.start) {
        nonMatches.push(text.slice(currentIndex, match.start));
      }
      currentIndex = match.end + 1;
    }

    if (currentIndex < text.length) {
      nonMatches.push(text.slice(currentIndex));
    }

    return nonMatches;
  }

  /**
   * Retrieves an array of WordCount objects representing matched words and their total occurrences
   * in the input text.
   *
   * @param text - The input text.
   * @returns A Map where the key is the keyword and the value is the number of occurences.
   */
  getStringOccurrences(text: string) {
    this.checkForConstructedFailureStates();
    const matches: Array<Match> = this.getMatches(text);
    const wordCountMap = new Map<string, number>();

    for (const match of matches) {
      const keyword = match.keyword;
      const count = wordCountMap.get(keyword) || 0;
      wordCountMap.set(keyword, count + 1);
    }

    const initial = this.keywords.map((word) => <const>[word, 0]);
    return new Map([...initial, ...wordCountMap]);
  }

  /**
   * Initializes the Trie with an array of keywords. These keywords are parsed respective
   * to the provided options `caseSensitive` and `removeWhiteSpaces` where strings are
   * flipped to lower case when the former is `true` and white spaces are trimmed when the
   * latter is `true`.
   *
   * @param keywords - An array of keywords to add to the Trie.
   * @returns void
   */
  private initialiseKeywords(keywords: Array<string>): void {
    const caseTransform = this.options.caseSensitive
      ? (x: string) => x
      : (x: string) => x.toLowerCase();

    const trimWhiteSpace = this.options.removeWhiteSpaces
      ? (x: string) => x
      : (x: string) => x.trim();

    keywords.forEach((kw) => this.addKeyword(trimWhiteSpace(caseTransform(kw))));
  }

  /**
   * Prepares a keyword for insertion into the Trie based on the specified options.
   *
   * @param keyword - The original keyword to prepare.
   * @returns The prepared keyword.
   */
  private prepareKeywordForInsertion(keyword: string): string {
    const transformCase = (x: string): string => (this.options.caseSensitive ? x : x.toLowerCase());

    const trimWhitespace = (x: string): string => (this.options.removeWhiteSpaces ? x.trim() : x);

    return trimWhitespace(trimWhitespace(transformCase(keyword)));
  }

  /**
   * Applies filtering options to the list of matches.
   *
   * @param text - The input text.
   * @param matches - The list of matches.
   * @returns The filtered list of matches.
   */
  private applyFilteringOptions(text: string, matches: Array<Match>): Array<Match> {
    const partialMatchesRemoved = this.options.wholeWords
      ? this.removePartialMatches(text, matches)
      : matches;

    const overlapsFiltered = !this.options.allowOverlap
      ? new Tree(partialMatchesRemoved).removeOverlappingSegments(partialMatchesRemoved)
      : partialMatchesRemoved;

    return overlapsFiltered;
  }

  /**
   * Retrieves the next state based on the current state and a character.
   *
   * @param currentState - The current state in the Trie.
   * @param char - The character to transition to the next state.
   * @returns The next state in the Trie.
   */
  private getState(currentState: State, char: string): State {
    let state: State = currentState;
    let newCurrentState: State | null = currentState.nextState(char);

    while (newCurrentState === null) {
      state = state.failure;
      newCurrentState = state.nextState(char);
    }

    return newCurrentState;
  }

  /**
   * Checks if failure states have been constructed and constructs them if necessary.
   *
   * @returns void
   */
  private checkForConstructedFailureStates(): void {
    if (!this.failureStateConstructed) {
      this.constructFailureStates();
    }
  }

  /**
   * Constructs failure states for each state in the Trie.
   *
   * @returns void
   */
  private constructFailureStates(): void {
    const queue: State[] = [];
    this.root.failure = this.root;

    this.root.successes.forEach((depthOneState) => {
      depthOneState.failure = this.root;
      queue.push(depthOneState);
    });

    while (queue.length > 0) {
      const currentState = queue.shift() as State;

      currentState.transitions.forEach((transition) => {
        const targetState = currentState.nextState(transition);
        if (!targetState) return;

        queue.push(targetState);
        let traceFailureState = currentState.failure;

        while (traceFailureState.nextState(transition) === null) {
          traceFailureState = traceFailureState.failure;
        }

        const newFailureState = traceFailureState.nextState(transition);
        if (!newFailureState) return;

        targetState.failure = newFailureState;
        targetState.addMatch(newFailureState.match);
      });
    }

    this.failureStateConstructed = true;
  }

  /**
   * Removes partial matches from the list of emits based on the search text.
   *
   * @param text - The search text.
   * @param matches - The list of emitted matches.
   * @returns The filtered list of matches.
   */
  private removePartialMatches(text: string, matches: Match[]): Match[] {
    const start = text.length;
    return matches.filter((match) => {
      return (
        (match.start === 0 || !isAlphanumeric(text.charAt(match.start - 1))) &&
        (match.end + 1 == start || !isAlphanumeric(text.charAt(match.end + 1)))
      );
    });
  }

  /**
   * Converts Trie matches to Match objects.
   *
   * @param end - The end index of the match.
   * @param state - The current state in the Trie.
   * @returns The list of Match objects.
   */
  private toMatches(end: number, state: State): Match[] {
    const matches: string[] = state.match;
    return matches.map((match) => {
      return new Match(end - match.length + 1, end, match);
    });
  }
}

export { type TrieOptions, defaultTrieOptions, Match };
