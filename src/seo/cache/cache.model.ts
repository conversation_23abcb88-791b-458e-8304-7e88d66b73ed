// بسم الله الرحمن الرحيم
import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { Tag } from '../functions/keywords';

@Schema({
  collection: 'seo-cache',
  timestamps: true,
  timeseries: {
    timeField: 'updatedAt',
    metaField: 'blogID',
    granularity: 'seconds',
  },
  expireAfterSeconds: 24 * 60 ** 2, // 1 day
})
export class SeoCache {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Blog', required: true })
  blogID: mongoose.Types.ObjectId;

  @Prop({ type: Object, required: true })
  parsedHTML: Record<Tag, string>;

  @Prop({ required: true })
  url: string;
}

export type SeoCacheDocument = HydratedDocument<SeoCache>;
export const SeoCacheSchema = SchemaFactory.createForClass(SeoCache);
