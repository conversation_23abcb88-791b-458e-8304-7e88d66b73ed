// بسم الله الرحمن الرحيم
import { Controller, UseGuards, Get, Param, Logger, Post, Body } from '@nestjs/common';
import mongoose from 'mongoose';

import { Auth } from '@/auth/guards/auth.guard';

import { SeoService } from './seo.service';
import { pipe } from 'fp-ts/lib/function';
import { transformMapToRecord } from './functions/utils';
import { BlogTone } from '@/blog/blog.enums';

@Controller('blogs/seo')
export class SeoController {
  private logger = new Logger(this.constructor.name);
  constructor(private readonly seoService: SeoService) {}

  @Post('generate/:id/content')
  @UseGuards(Auth)
  async generateContent(
    @Param('id') blogID: string,
    @Body()
    payload: {
      prompt: string;
      keywords: string[];
      wordCount: number;
      tone: BlogTone;
    },
  ) {
    return this.seoService.synthesizeContent(new mongoose.Types.ObjectId(blogID), payload);
  }
  @Post('generate/:id/headings')
  @UseGuards(Auth)
  async generateHeadings(@Param('id') blogID: string) {
    try {
      return await this.seoService.synthesizeHeadings(new mongoose.Types.ObjectId(blogID));
    } catch (error) {
      this.logger.error(
        { err: error, blogID },
        `Failed to generate SEO headings blog ID ${blogID}`,
      );
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(Auth)
  async fetchSeoData(@Param('id') blogID: string) {
    try {
      return pipe(
        await this.seoService.processBlogSeoData(new mongoose.Types.ObjectId(blogID)),
        (x) => transformMapToRecord(x),
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Failed to fetch SEO data for blog ID ${blogID}`);
      return {
        title: {},
        meta: {},
        h1: {},
        h2: {},
        h3: {},
        content: {},
      };
    }
  }
}
