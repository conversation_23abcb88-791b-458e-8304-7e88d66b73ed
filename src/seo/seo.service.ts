import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { SeoCache } from './cache/cache.model';
import mongoose, { Model } from 'mongoose';
import { BaseProvider } from '@/llm/providers/base.provider';
import { ConfigService } from '@nestjs/config';
import { OpenAiProvider } from '@/llm';
import { Type } from '@sinclair/typebox';
import { Blog } from '@/blog/blog.model';
import { analyseTexts, getCounts, type Tag } from './functions/keywords';
import { BlogTone } from '@/blog/blog.enums';
import pRetry from 'p-retry';

@Injectable()
export class SeoService {
  private providers: Map<string, BaseProvider>;
  private logger = new Logger(this.constructor.name);
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(SeoCache.name) readonly seocache: Model<SeoCache>,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
  ) {
    this.providers = new Map();
    this.providers.set(
      OpenAiProvider.PROVIDER_NAME,
      new OpenAiProvider(this.configService.get('OPENAI_API_KEY')),
    );
  }

  /**
   * Gathers SEO Results for a blog with suggested SEO keywords
   * @param blogID - The ID of the blog
   * @returns - The blog with updated SEO Results
   */
  async gatherSeoResults(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Results`);
      }

      const cachedResults = await this.seocache.find({ blogID });
      if (!cachedResults?.length) {
        throw new Error(`No cached SEO results found for blog: ${blogID}`);
      }

      const rangeChart = analyseTexts(blog.seoKeywords, cachedResults);

      blog.seoResults = {
        rangeChart: Object.fromEntries(
          [...rangeChart].map(
            ([tag, keywordRange]) => <const>[tag, Object.fromEntries([...keywordRange])],
          ),
        ) as Record<Tag, Record<string, [number, number]>>,
      };

      return await blog.save();
    } catch (error) {
      console.error(`Error gathering SEO results for blog ${blogID}:`, error);
      // Returning null instead of throwing error
      // TODO: Implement a better error handling strategy
      return null;
    }
  }

  async synthesizeHeadings(blogID: mongoose.Types.ObjectId) {
    type Heading = `h${1 | 2 | 3}`;
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Headings`);
      }

      const input = new Map<Heading, string[]>(
        (<const>['h1', 'h2', 'h3']).map((tag) => [
          tag,
          Object.keys(blog.seoResults.rangeChart[tag]),
        ]),
      );

      const suggestions = await this.suggestHeadings(input, blog.blogOutline?.summary);
      return Object.fromEntries(
        Object.entries(suggestions).map(
          ([tag, { suggestions }]) =>
            <const>[tag as Heading, { keywords: input.get(tag as Heading), suggestions }],
        ),
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Error synthesizing headings for blog ${blogID}`);
      throw error;
    }
  }

  /**
   * Combines blog's current keyword count with SEO data from DB
   * @param blogID - The ID of the blog
   * @returns The frequency of a keyword in the blog alongside expected range
   */
  async processBlogSeoData(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID}`);
      }

      if (!blog.seoResults?.rangeChart) {
        return null; // Returning null if there is no rangeChart
      }

      const rangeChart = new Map(
        Object.entries(blog.seoResults.rangeChart).map(
          ([tag, keywordRange]) => <const>[tag as Tag, new Map(Object.entries(keywordRange))],
        ),
      );

      // Build the additionalKeywords array from seoInputKeywords and blog keywords.
      const additionalKeywords = [...(blog.seoInputKeywords ?? []), ...(blog.keywords ?? [])].map(
        (keyword) => <const>[keyword, this.contentKeywordRange(blog.content, keyword)],
      );

      rangeChart.set('content', new Map([...additionalKeywords, ...rangeChart.get('content')]));

      return getCounts(blog.content, rangeChart);
    } catch (error) {
      this.logger.error(`Error scoring blog ${blogID}:`, error);
      throw error;
    }
  }

  async saveToCache(
    data: Array<readonly [URL, Record<'title' | 'content' | 'meta' | 'h1' | 'h2' | 'h3', string>]>,
    blogID: string,
  ) {
    return await this.seocache.create(
      <Array<SeoCache>>data.map(([url, parsedHTML]) => ({
        parsedHTML: parsedHTML,
        url: url.toString(),
        blogID: new mongoose.Types.ObjectId(blogID),
      })),
    );
  }

  async loadFromCache(blogID: mongoose.Types.ObjectId) {
    return new Map(
      (await this.seocache.find({ blogID })).map(
        ({ parsedHTML, url }) =>
          <const>[new URL(url), new Map(Object.entries(parsedHTML) as Array<[Tag, string]>)],
      ),
    );
  }

  async suggestContent({
    prompt,
    keywords,
    wordCount,
    summary,
    tone,
  }: {
    prompt: string;
    keywords: string[];
    wordCount: number;
    tone: BlogTone;
    summary: string;
  }): Promise<string> {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    const systemPrompt = `You are an AI assistant specialized in generating SEO-optimized content.
Guidelines:
- Generate content that is exactly ${wordCount} words long
- Write in a ${tone.toLowerCase()} tone
- Each keyword should be used naturally and strategically
- Content should be:
  * Engaging and readable
  * Well-structured
  * SEO-optimized
  * Factually accurate
  * Coherent with the provided summary
- Follow SEO best practices:
  * Use keywords in first 100 words
  * Maintain keyword density without stuffing
  * Include semantic variations of keywords
  * Create natural paragraph transitions`;

    const userPrompt = `Generate SEO-optimized content based on:

Main Prompt: ${prompt}

Background Summary: ${summary}

Required Keywords: ${keywords.join(', ')}

Requirements:
1. Write exactly ${wordCount} words
2. Maintain a ${tone.toLowerCase()} tone throughout
3. Naturally incorporate all provided keywords
4. Ensure content flows logically with the background summary
5. Make content engaging and valuable to readers

Generate the content now.`;

    const ContentSchema = Type.Object({
      content: Type.String(),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'generate_content',
            description: 'Generate SEO-optimized content',
            parameters: ContentSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const result = response.message.toolCalls[0].args as { content: string };
        return result.content;
      } else throw new Error('Failed to generate SEO Content Snippet');
    } catch (error) {
      this.logger.error('Error generating SEO content with specific keywords :', error);
      throw error;
    }
  }

  async synthesizeContent(
    blogID: mongoose.Types.ObjectId,
    payload: {
      prompt: string;
      keywords: string[];
      wordCount: number;
      tone: BlogTone;
    },
  ) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Headings`);
      }
      return pRetry(
        () =>
          this.suggestContent({
            summary: blog.blogOutline?.summary ?? '',
            wordCount: payload.wordCount,
            keywords: payload.keywords,
            tone: payload.tone,
            prompt: payload.prompt,
          }),
        {
          retries: 3,
        },
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Error synthesizing content for blog ${blogID}`);
      throw error;
    }
  }
  /**
   * Uses AI to generate heading suggestions based on keyword inputs
   * @param input - A map of heading levels and their associated keywords
   * @returns An object with heading suggestions and their confidence scores
   */
  async suggestHeadings(
    input: Map<`h${1 | 2 | 3}`, string[]>,
    tldr: string,
  ): Promise<
    Record<
      'h1' | 'h2' | 'h3',
      {
        suggestions: Array<[text: string, score: number]>;
      }
    >
  > {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    // Convert the input map to a more usable structure
    const keywordsInput = {
      h1: input.get('h1') || [],
      h2: input.get('h2') || [],
      h3: input.get('h3') || [],
    };

    const systemPrompt = `You are an AI assistant specialized in generating SEO-optimized headings.
  Guidelines:
  - Generate heading suggestions for H1, H2, and H3 tags based on provided keywords
  - Each heading should:
    * Incorporate the provided keywords naturally
    * Be concise and engaging
    * Follow SEO best practices
    * Be appropriate for its heading level in the content hierarchy
  - H1 headings should:
    * Be the main title (50-60 characters)
    * Include the primary keyword near the beginning
    * Be compelling and descriptive of the overall content
  - H2 headings should:
    * Be section titles (40-60 characters)
    * Include secondary keywords
    * Break down the main topic into logical subtopics
  - H3 headings should:
    * Be subsection titles (30-50 characters)
    * Include tertiary keywords
    * Further elaborate on H2 sections
  - For each heading suggestion:
    * Assign a confidence score (0-1) based on:
      - Keyword integration (how well it incorporates the keywords)
      - SEO potential (how search-friendly it is)
      - Readability and engagement
      - Hierarchical appropriateness
  - Provide 3-5 varied suggestions for each heading level
  `;

    const userPrompt = `Generate SEO-optimized heading suggestions for H1, H2, and H3 tags based on these keywords:
  
  H1 Keywords: ${keywordsInput.h1.join(', ')}
  H2 Keywords: ${keywordsInput.h2.join(', ')}
  H3 Keywords: ${keywordsInput.h3.join(', ')}
  
  For each heading level:
  1. Create 3-5 compelling heading suggestions that naturally incorporate the keywords
  2. Ensure each suggestion is appropriate for its heading level in the content hierarchy
  3. Assign confidence scores (0-1) based on keyword integration, SEO potential, and readability
  
    Summary of the article for which you have to suggest the headings:
    ${tldr}

  Return structured heading suggestions with confidence scores for each heading level.`;

    // Fixed schema structure - using objects instead of tuples
    const HeadingSuggestionsSchema = Type.Object({
      h1: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
      h2: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
      h3: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'generate_heading_suggestions',
            description: 'Generate SEO-optimized heading suggestions with confidence scores',
            parameters: HeadingSuggestionsSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const rawResult = response.message.toolCalls[0].args as Record<
          'h1' | 'h2' | 'h3',
          {
            suggestions: Array<{ text: string; score: number }>;
          }
        >;

        // Convert the object format to the tuple format required by the return type
        const convertToTupleFormat = (section: {
          suggestions: Array<{ text: string; score: number }>;
        }) => {
          return {
            suggestions: section.suggestions
              .map((item) => [item.text, item.score] as [string, number])
              .sort((a, b) => b[1] - a[1]), // Sort by score in descending order
          };
        };

        return {
          h1: convertToTupleFormat(rawResult.h1),
          h2: convertToTupleFormat(rawResult.h2),
          h3: convertToTupleFormat(rawResult.h3),
        };
      }

      // Fallback with empty results if no tool calls found
      return {
        h1: { suggestions: [] },
        h2: { suggestions: [] },
        h3: { suggestions: [] },
      };
    } catch (error) {
      this.logger.error(`Error generating heading suggestions:`, error);
      return {
        h1: { suggestions: [] },
        h2: { suggestions: [] },
        h3: { suggestions: [] },
      };
    }
  }

  /**
   * Uses AI to analyse text and suggest the topmost keywords
   * @param input -  An array of urls and their extracted body text
   * @returns An array of keywords for heading tags and content
   */
  async suggestKeywords(
    { introduction, conclusion, summary, title }: Blog['blogOutline'],
    input: Map<URL, Map<Tag, string>>,
  ): Promise<Array<{ url: string } & Record<Tag, string[]>>> {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    // Process each input in parallel
    const keywordPromises = [...input].map(async ([urlObject, parsedHtml]) => {
      const url = urlObject.toString();
      const systemPrompt = `You are an AI assistant specialized in SEO keyword extraction for bloggers.
      You'll be provided the outline of a blog and some sections of what was an HTML document, and based on the outline of the blog you have to 
      select relevant keywords from the HTML sections.
      Guidelines:
      - Extract keywords separately for each section (title, meta description, h1, h2, h3, and main content)
      - Keywords must appear in their respective sections
      - For each section:
        * Focus on most relevant keywords, you can decide the relevance by looking at the context which will be provided
        * Assign confidence scores (0-1) based on relevance and importance
        * Consider search intent and volume potential
        * Consider potential keywords which can improve SEO
      - Title and H1 keywords should be highly focused (1-3 keywords)
      - H2 and H3 can include supporting keywords (1-3 keywords each)
      - Content section should capture broader topic coverage (up to 10 long tail keywords)
      - Always include the main long tail keywords which is the targeted keywords of the blog
      - You can also suggest your own keywords (which might not necessarily be in the HTML) for headings only
      
      Information about the blog:
        - title: ${title}
        - introduction: ${introduction}
        - summary: ${summary}
        - conclusion: ${conclusion}
      `;

      const userPrompt = `Extract SEO keywords from each section of the following content.
      For each section (title, description, headers, content):
      - Extract keywords that appear in that specific section
      - Assign confidence scores based on:
        * Keyword relevance to the topic (most important)
        * Placement and prominence
        * Search potential
        * User intent match

      HTML Sections:
      Title: ${parsedHtml.get('title')}
      Meta Description: ${parsedHtml.get('meta')}
      H1: ${parsedHtml.get('h1')}
      H2: ${parsedHtml.get('h2')}
      H3: ${parsedHtml.get('h3')}
      Content: ${parsedHtml.get('content')}

      Please provide keywords and confidence scores for each section separately.`;

      const Schema = Type.Array(
        Type.Object({
          keyword: Type.String(),
          confidence: Type.Number({ minimum: 0, maximum: 1 }),
        }),
      );

      const KeywordsSchema = Type.Object({
        title: Schema,
        meta: Schema,
        h1: Schema,
        h2: Schema,
        h3: Schema,
        content: Schema,
      });

      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        opts: {
          temperature: 0.1,
        },
        systemPrompt,
        tools: [
          {
            name: 'extract_seo_keywords',
            description: 'Extract SEO keywords from content with confidence scores',
            parameters: KeywordsSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const result = response.message.toolCalls[0].args as Record<
          Tag,
          Array<{ keyword: string; confidence: number }>
        >;

        // Process each section's keywords
        const processSection = (keywords: (typeof result)[Tag]) =>
          keywords.filter(({ confidence }) => confidence > 0.7).map(({ keyword }) => keyword);

        return {
          url,
          title: processSection(result.title),
          meta: processSection(result.meta),
          h1: processSection(result.h1),
          h2: processSection(result.h2),
          h3: processSection(result.h3),
          content: processSection(result.content),
        };
      } else throw new Error('Failed to extract keywords from the response');
    });

    return Promise.all(keywordPromises);
  }

  private contentKeywordRange(content: string, keyword: string): [min: number, max: number] {
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // case-insensitive search
    const regex = new RegExp(`\\b${escapedKeyword}\\b`, 'gi');
    const matches = content.match(regex);
    const count = matches ? matches.length : 0;

    if (count > 0) {
      // TODO: understand why getcounts is changing the value setting a lower value
      // 20% of the count as minimum
      return [Math.round(count * 0.1), Math.round(count * 0.4)];
    }
    // Fallback default range
    return [0, 1];
  }
}
