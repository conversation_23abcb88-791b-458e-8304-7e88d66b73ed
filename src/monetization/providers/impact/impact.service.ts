import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AffiliateNetworkService } from '../../interfaces/affiliate-network-service.abstract';
import { AffiliateAction } from '../../interfaces/affiliate-action.interface';
import { AffiliateConversionStatus } from '../../affiliate-link-tracking/affiliate-link-tracking.model';
import { ImpactAction } from './impact-action.interface';
import axios from 'axios';

@Injectable()
export class ImpactNetworkService extends AffiliateNetworkService {
  private readonly logger = new Logger(ImpactNetworkService.name);
  private readonly baseUrl: string;
  private readonly authHeader: string;

  constructor(private readonly configService: ConfigService) {
    super();
    const accountSid = this.configService.get('IMPACT_ACCOUNT_SID');
    const apiKey = this.configService.get('IMPACT_API_KEY');
    this.baseUrl = `${this.configService.get('IMPACT_API_BASE_URL')}/${accountSid}`;
    this.authHeader = Buffer.from(`${accountSid}:${apiKey}`).toString('base64');
  }

  async fetchActions(startDate: Date, endDate: Date): Promise<AffiliateAction[]> {
    try {
      const impactActions = await this.fetchImpactActions(startDate, endDate);
      return impactActions.map(this.mapImpactAction);
    } catch (error) {
      this.logger.error('Failed to fetch Impact actions:', error);
      throw error;
    }
  }

  private async fetchImpactActions(startDate: Date, endDate: Date): Promise<ImpactAction[]> {
    try {
      // Validate date range constraints
      this.validateDateRange(startDate, endDate);

      // Format dates to match Impact's expected format: YYYY-MM-DDT00:00:00Z
      const formatDate = (date: Date): string => {
        const isoString = date.toISOString();
        return `${isoString.split('T')[0]}T00:00:00Z`;
      };

      // Ensure we're not querying future dates
      const now = new Date();
      const actualEndDate = endDate > now ? now : endDate;
      const actualStartDate = startDate > now ? now : startDate;

      const params = new URLSearchParams({
        ActionDateStart: formatDate(actualStartDate),
        ActionDateEnd: formatDate(actualEndDate),
        PageSize: '20000',
        Page: '1',
        OrderBy: 'ActionDate',
        OrderByDirection: 'DESC',
      });

      const response = await axios.get(`${this.baseUrl}/Actions`, {
        headers: {
          Authorization: `Basic ${this.authHeader}`,
          Accept: 'application/json',
        },
        params: params,
      });

      if (response.data?.Status === 'ERROR') {
        this.logger.error('Impact API Error:', response.data.Message);
        throw new Error(response.data.Message);
      }

      console.log(response.data);

      return response.data?.Actions || [];
    } catch (error) {
      this.logger.error('Failed to fetch Impact actions:', error);
      if (axios.isAxiosError(error) && error.response?.data) {
        this.logger.error('API Response:', error.response.data);
        this.logger.error('Request URL:', error.config?.url);
        this.logger.error('Request params:', error.config?.params);
      }
      throw error;
    }
  }

  private async fetchActionsByChunks(startDate: Date, endDate: Date): Promise<ImpactAction[]> {
    const allRecords: ImpactAction[] = [];
    let currentStartDate = new Date(startDate);

    while (currentStartDate < endDate) {
      // Calculate chunk end date (45 days or remaining days)
      let chunkEndDate = new Date(currentStartDate);
      chunkEndDate.setDate(chunkEndDate.getDate() + 45);

      // If chunk end date exceeds the overall end date, use the overall end date
      if (chunkEndDate > endDate) {
        chunkEndDate = endDate;
      }

      // Fetch chunk
      const chunkRecords = await this.fetchImpactActions(currentStartDate, chunkEndDate);
      allRecords.push(...chunkRecords);

      // Move to next chunk
      currentStartDate = new Date(chunkEndDate);
      currentStartDate.setDate(currentStartDate.getDate() + 1);

      // Add a small delay between requests to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    return allRecords;
  }

  // Helper method to validate date constraints
  private validateDateRange(startDate: Date, endDate: Date): void {
    // Check if start date is within 3 years
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);

    if (startDate < threeYearsAgo) {
      throw new Error('StartDate cannot exceed 3 years in the past');
    }

    // Check if end date is provided
    if (!endDate) {
      throw new Error('EndDate must be specified when StartDate is provided');
    }

    // Check if date range is valid
    if (startDate >= endDate) {
      throw new Error('StartDate must be before EndDate');
    }
  }

  private mapImpactAction(action: ImpactAction): AffiliateAction {
    return {
      id: action.Id,
      networkId: action.ActionTrackerId,
      campaignId: action.CampaignId,
      campaignName: action.CampaignName,
      state: action.State,
      amount: parseFloat(action.Amount),
      currency: action.Currency,
      commission: parseFloat(action.Payout),
      purchaseDate: new Date(action.EventDate),
      lockDate: action.LockingDate ? new Date(action.LockingDate) : undefined,
      releaseDate: action.ClearedDate ? new Date(action.ClearedDate) : undefined,
      customerLocation: {
        city: action.CustomerCity || undefined,
        region: action.CustomerRegion || undefined,
        country: action.CustomerCountry || undefined,
      },
      metadata: {
        actionTrackerId: action.ActionTrackerId,
        actionTrackerName: action.ActionTrackerName,
        stateDetail: action.State,
        clickId: action.SharedId || undefined,
        eventCode: action.EventCode || '',
        intendedPayout: parseFloat(action.IntendedPayout),
        deltaAmount: parseFloat(action.DeltaAmount),
        orderId: action.Oid || undefined,
        promoCode: action.PromoCode || '',
        referringType: action.ReferringType || undefined,
        referringDomain: action.ReferringDomain || undefined,
        creationDate: action.CreationDate ? new Date(action.CreationDate) : undefined,
        referringDate: action.ReferringDate ? new Date(action.ReferringDate) : undefined,
        subId1: action.SubId1 || undefined,
        subId2: action.SubId2 || undefined,
        subId3: action.SubId3 || undefined,
      },
    };
  }

  mapNetworkStatus(state: string): AffiliateConversionStatus {
    switch (state?.toUpperCase()) {
      case 'PAID':
        return AffiliateConversionStatus.PAID;
      case 'PAYMENT_PENDING':
        return AffiliateConversionStatus.APPROVED;
      case 'PENDING':
        return AffiliateConversionStatus.PENDING;
      case 'PROCESSING':
        return AffiliateConversionStatus.PROCESSING;
      case 'REJECTED':
        return AffiliateConversionStatus.REJECTED;
      case 'REVERSED':
        return AffiliateConversionStatus.REVERSED;
      default:
        return AffiliateConversionStatus.NO_CONVERSION;
    }
  }
}
