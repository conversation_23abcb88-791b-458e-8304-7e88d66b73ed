export interface ImpactAction {
  Id: string;
  CampaignId: string;
  CampaignName: string;
  ActionTrackerName: string;
  ActionTrackerId: string;
  EventCode: string;
  State: string;
  AdId: string;
  Payout: string;
  DeltaPayout: string;
  IntendedPayout: string;
  Amount: string;
  DeltaAmount: string;
  IntendedAmount: string;
  Currency: string;
  ReferringDate: string;
  EventDate: string;
  CreationDate: string;
  LockingDate: string;
  ClearedDate: string;
  ReferringType: string;
  ReferringDomain: string;
  PromoCode: string;
  Oid: string;
  CustomerArea: string;
  CustomerCity: string;
  CustomerRegion: string;
  CustomerCountry: string;
  SubId1: string;
  SubId2: string;
  SubId3: string;
  SharedId: string;
  Uri: string;
}
