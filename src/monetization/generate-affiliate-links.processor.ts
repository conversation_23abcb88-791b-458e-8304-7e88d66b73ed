import { Processor, Process, InjectQueue, OnQueueCompleted } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { BaseProcessor } from '@common/queue/base.processor';
import { JOB_QUEUES } from '@/common/constants';
import { BlogService } from '@/blog/blog.service';
import { BlogQueuePayload, BlogAffiliateKeyword, Blog } from '@/blog/blog.model';
import { AffiliateLinkTrackingService } from './affiliate-link-tracking/affiliate-link-tracking.service';
import { GatewayService } from '@/modules/gateway/gateway.service';

interface LinkDensitySection {
  start: number;
  end: number;
  density: number;
  links: Array<{ start: number; end: number }>;
}

@Processor(JOB_QUEUES.GENERATE_AFFILIATE_LINKS)
export class GenerateAffiliateLinksProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_AFFILIATE_LINKS;
  protected readonly logger = new Logger(GenerateAffiliateLinksProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.DISPATCH_TO_PUBLISHERS) private dispatchToPublishersQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS) generateAffiliateKeywordsQueue: Queue,
    private readonly gatewayService: GatewayService,
    private readonly blogService: BlogService,
    private readonly affiliateLinkTrackingService: AffiliateLinkTrackingService,
  ) {
    super(generateAffiliateKeywordsQueue);
  }

  @Process({ concurrency: 3 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { blogId, bid, uid, email, blogContent, affiliateKeywords } = job.data;

      this.logger.log(`Starting affiliate link generation for blog ${blogId} (${email})`);

      // Replace keywords with affiliate links
      const { updatedContent, replacementCounts } = this.replaceKeywordsWithAffiliateLinks({
        content: blogContent,
        affiliateKeywords,
        bid,
        blogId,
      });

      // Update affiliate keywords with replacement counts
      const updatedAffiliateKeywords = affiliateKeywords.map((keyword) => ({
        ...keyword,
        replacementCount: replacementCounts[keyword.keyword] || 0,
        matchedAt: new Date(),
      }));

      // Create tracking entries for keywords that were actually replaced in the content
      for (const keyword of updatedAffiliateKeywords) {
        if (keyword.affiliateLink && keyword.replacementCount > 0) {
          try {
            await this.affiliateLinkTrackingService.createTrackingEntry(bid, blogId, {
              ...keyword,
              searchTerms: [keyword.keyword, ...(keyword.searchTerms || [])],
            });
            this.logger.debug(
              `Created affiliate tracking entry for blog ${blogId} and keyword "${keyword.keyword}"`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to create affiliate tracking entry for blog ${blogId} and keyword "${keyword.keyword}": ${error.message}`,
            );
          }
        }
      }

      // Update blog with new content and affiliate links
      await this.blogService.update(bid, blogId, {
        content: updatedContent,
        affiliateLinkGenerationStatus: 'affiliate_link_generated',
        generationStatus: `Generated ${Object.values(replacementCounts).reduce(
          (a, b) => a + b,
          0,
        )} affiliate links`,
        affiliateKeywords: updatedAffiliateKeywords,
        affiliateKeywordsUpdatedAt: new Date(),
      } satisfies Partial<Blog>);

      // Send Final status update after affiliate links are generated
      this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
        _id: blogId,
        identifier: blogId,
        affiliateLinkGenerationStatus: 'affiliate_link_generated',
        percentComplete: 100,
      });
      this.logger.log(`Successfully processed affiliate links for blog ${blogId} (${email})`);
    });
  }

  @OnQueueCompleted()
  protected async onCompleted(job: Job<BlogQueuePayload>): Promise<void> {
    const { blogId, email } = job.data;

    try {
      // Add to dispatch queue for auto publishing after blog generation
      await this.dispatchToPublishersQueue.add(job.data, {
        jobId: blogId,
      });
      this.logger.debug(`Job added to dispatch to publishers for blog ${blogId} (${email})`);
    } catch (error) {
      this.logger.error(`Failed to handle completion for blog ${blogId}:`, error);
    }
  }

  private replaceKeywordsWithAffiliateLinks({
    content,
    affiliateKeywords,
    bid,
    blogId,
  }: {
    content: string;
    affiliateKeywords: BlogAffiliateKeyword[];
    bid: string;
    blogId: string;
  }): { updatedContent: string; replacementCounts: Record<string, number> } {
    let updatedContent = content;
    const replacementCounts: Record<string, number> = {};

    // First, analyze link density in sections
    const sections = this.analyzeLinkDensity(content);

    // Replace keywords with context
    for (const keyword of affiliateKeywords) {
      if (!keyword.affiliateLink) {
        this.logger.warn(
          `No affiliate link found for keyword: ${keyword.keyword} blogId ${blogId}`,
        );
        continue;
      }

      try {
        // Find existing links first
        const existingLinks = this.findExistingLinks(updatedContent);

        // First attempt: Try with context pattern if beforeWord or afterWord exists
        let match: { index: number; text: string } | null = null;

        if (keyword.beforeWord || keyword.afterWord) {
          const contextPattern = this.buildContextPattern(keyword);
          this.logger.debug(
            { keyword: keyword.keyword, contextPattern, blogId },
            'Context pattern generated',
          );

          const contextRegex = new RegExp(contextPattern, 'gi');
          const contextMatches = this.findValidMatches(updatedContent, contextRegex, existingLinks);

          if (contextMatches.length > 0) {
            // Take the first valid match
            match = this.findBestMatch(contextMatches, sections);
          }
        }

        // Fallback: Try simple keyword matching in low-density sections if no context match
        if (!match) {
          const keywordPattern = `\\b${this.escapeRegExp(keyword.keyword)}\\b`;
          const keywordRegex = new RegExp(keywordPattern, 'gi');

          const keywordMatches = sections
            .filter((section) => section.density < 0.3) // Sections with low link density
            .flatMap((section) => {
              const sectionContent = updatedContent.slice(section.start, section.end);
              const matches = this.findValidMatches(sectionContent, keywordRegex, section.links);

              // Adjust match positions to global content
              return matches.map((match) => ({
                ...match,
                index: match.index + section.start,
              }));
            });

          if (keywordMatches.length > 0) {
            match = this.findBestMatch(keywordMatches, sections);
          }
        }

        // Replace the single best match if found
        if (match) {
          // https://2www.net/?utm_source=66151c1c6bb4bd249483c080&utm_medium=674530c5536559d52f9bdff0&ref=https://google.com
          const protocol = process.env.NODE_ENV === 'dev' ? 'http' : 'https';
          const trackingHost = process.env.TRACKING_DOMAIN?.includes('http')
            ? process.env.TRACKING_DOMAIN
            : `${protocol}://${process.env.TRACKING_DOMAIN}`;
          const trackingUrl = `${trackingHost}?utm_source=${bid}&utm_medium=${blogId}&ref=${keyword.affiliateLink}`;

          const before = updatedContent.slice(0, match.index);
          const after = updatedContent.slice(match.index + match.text.length);

          updatedContent = `${before}<a href="${trackingUrl}" target="_blank" rel="noopener noreferrer">${match.text}</a>${after}`;
          replacementCounts[keyword.keyword] = 1;

          this.logger.debug(
            { keyword: keyword.keyword, position: match.index, blogId },
            'Replaced keyword at position',
          );
        } else {
          replacementCounts[keyword.keyword] = 0;
        }
      } catch (error) {
        this.logger.warn(
          { keyword: keyword.keyword, blogId, err: error },
          'Error replacing keyword. Skipping this keyword.',
        );
        replacementCounts[keyword.keyword] = 0;
      }
    }

    return { updatedContent, replacementCounts };
  }

  private buildContextPattern(keyword: BlogAffiliateKeyword): string {
    const keywordPattern = this.escapeRegExp(keyword.keyword.trim());

    let pattern = '';

    if (keyword.beforeWord && keyword.afterWord) {
      const beforePattern = this.escapeRegExp(keyword.beforeWord.trim());
      const afterPattern = this.escapeRegExp(keyword.afterWord.trim());
      pattern = `(?<=${beforePattern}\\s+)(${keywordPattern})(?=\\s+${afterPattern})`;
    } else if (keyword.beforeWord) {
      const beforePattern = this.escapeRegExp(keyword.beforeWord.trim());
      pattern = `(?<=${beforePattern}\\s+)(${keywordPattern})`;
    } else if (keyword.afterWord) {
      const afterPattern = this.escapeRegExp(keyword.afterWord.trim());
      pattern = `(${keywordPattern})(?=\\s+${afterPattern})`;
    }

    return `\\b${pattern}\\b`;
  }

  private findBestMatch(
    matches: Array<{ index: number; text: string }>,
    sections: LinkDensitySection[],
  ): { index: number; text: string } | null {
    // Sort matches by link density of their sections (prefer lower density)
    return (
      matches
        .map((match) => ({
          match,
          section: sections.find(
            (section) => match.index >= section.start && match.index < section.end,
          ),
        }))
        .filter(({ section }) => section && section.density < 0.3)
        .sort((a, b) => (a.section?.density || 1) - (b.section?.density || 1))
        .map(({ match }) => match)[0] || null
    );
  }

  private analyzeLinkDensity(content: string): LinkDensitySection[] {
    const sectionLength = 500; // Characters per section
    const sections: LinkDensitySection[] = [];

    // Find all links first
    const allLinks = this.findExistingLinks(content);

    // Create sections
    for (let i = 0; i < content.length; i += sectionLength) {
      const sectionStart = i;
      const sectionEnd = Math.min(i + sectionLength, content.length);

      // Find links in this section
      const sectionLinks = allLinks.filter(
        (link) =>
          (link.start >= sectionStart && link.start < sectionEnd) ||
          (link.end > sectionStart && link.end <= sectionEnd),
      );

      // Calculate link density
      const totalLinkLength = sectionLinks.reduce((sum, link) => sum + (link.end - link.start), 0);
      const density = totalLinkLength / (sectionEnd - sectionStart);

      sections.push({
        start: sectionStart,
        end: sectionEnd,
        density,
        links: sectionLinks,
      });
    }

    return sections;
  }

  private findExistingLinks(content: string): Array<{ start: number; end: number }> {
    const links: Array<{ start: number; end: number }> = [];
    const linkRegex = /<a[^>]*>.*?<\/a>/gi;
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      links.push({
        start: match.index,
        end: match.index + match[0].length,
      });
    }

    return links;
  }

  private findValidMatches(
    content: string,
    regex: RegExp,
    existingLinks: Array<{ start: number; end: number }>,
  ): Array<{ index: number; text: string }> {
    const matches: Array<{ index: number; text: string }> = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      const isInsideLink = existingLinks.some(
        (link) => match.index >= link.start && match.index < link.end,
      );

      if (!isInsideLink) {
        matches.push({
          index: match.index,
          text: match[0],
        });
      }
    }

    return matches;
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}
