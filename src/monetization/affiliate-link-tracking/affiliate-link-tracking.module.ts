import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { BlogSchema, Blog } from '@/blog/blog.model';
import { WalletModule } from '@/resources/wallet/wallet.module';
import { S3Service } from '@/common/services/s3.service';
import { JobModule } from '@/job/job.module';

import {
  AffiliateLinkTrackingSchema,
  AffiliateLinkTracking,
} from './affiliate-link-tracking.model';
import { AffiliateLinkTrackingController } from './affiliate-link-tracking.controller';
import { AffiliateLinkTrackingService } from './affiliate-link-tracking.service';
import { ImpactService } from '../impact.service';

@Module({
  providers: [AffiliateLinkTrackingService, ImpactService, S3Service],
  controllers: [AffiliateLinkTrackingController],
  imports: [
    MongooseModule.forFeature([
      { name: AffiliateLinkTracking.name, schema: AffiliateLinkTrackingSchema },
      { name: Blog.name, schema: BlogSchema },
    ]),
    WalletModule,
    JobModule,
  ],
  exports: [AffiliateLinkTrackingService, ImpactService, MongooseModule],
})
export class AffiliateLinkTrackingModule {}
