import type { ListResponse, CrudQuery } from '@/crud/crud.interface';
import type { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';

import {
  NotFoundException,
  Controller,
  UseGuards,
  Headers,
  Delete,
  Param,
  Query,
  Post,
  Get,
  Req,
} from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { RolesGuard, Roles } from '@/auth/guards/roles.guard';
import { CrudController } from '@/crud/crud.controller';
import { WalletService } from '@/resources/wallet/wallet.service';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { Auth } from '@/auth/guards/auth.guard';
import { Blog } from '@/blog/blog.model';

import { AffiliateConversionStatus, AffiliateLinkTracking } from './affiliate-link-tracking.model';
import { AffiliateLinkTrackingService } from './affiliate-link-tracking.service';
import { generateTrackingLink } from '../utils/tracking.utils';

@Controller('affiliate-link-trackings')
export class AffiliateLinkTrackingController extends CrudController<
  AffiliateLinkTracking,
  null,
  null
> {
  constructor(
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
    private readonly affiliateLinkTrackingService: AffiliateLinkTrackingService,
    private readonly walletService: WalletService,
  ) {
    super(affiliateLinkTrackingService);
  }

  @Get('pending-balance')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getPendingBalance(
    @Query() query: CrudQuery,
    @Req() { bid, user }: AuthenticatedRequest,
    @Headers() headers: { 'x-app-name': 'admin' },
  ): Promise<ListResponse<AffiliateLinkTracking>> {
    const isRequestFromAdmin = user.role === 'superadmin' && headers['x-app-name'] === 'admin';
    if (!isRequestFromAdmin) {
      query.filter = { bid: bid };
    }
    query.filter = {
      ...query.filter,
      conversionStatus: {
        $ne: AffiliateConversionStatus.NO_CONVERSION,
      },
    };
    query.sort = ['updatedAt,DESC'];
    const resp = await this.affiliateLinkTrackingService.findAll(query);

    const blogIds = [...new Set(resp.data.map((item) => item.blogId))].map(
      (id) => new Types.ObjectId(id),
    );

    const blogs = await this.blogModel
      .find({ _id: { $in: blogIds } })
      .select('title')
      .lean()
      .exec();

    const blogMap = blogs.reduce((map, blog) => {
      map[blog._id.toString()] = blog.title;
      return map;
    }, {});

    const getActionCount = (item: AffiliateLinkTracking) =>
      resp.data.filter((i) => i.affiliateLink === item.affiliateLink).length;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore For Now
    resp.data = resp.data.map((item) => ({
      ...item.toObject(),
      actionCount: getActionCount(item),
      trackingLink: generateTrackingLink(item.bid, item.blogId, item.affiliateLink),
      blogTitle: item.blogId ? blogMap[item.blogId.toString()] || 'Unknown Blog' : 'No Blog',
    }));

    return resp;
  }

  @Get('pending-balance/stats')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getPendingBalanceStats(@Req() { bid }: AuthenticatedRequest) {
    return this.affiliateLinkTrackingService.getPendingBalanceStats(bid);
  }

  @Get('active')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getAffiliateLinkTracking(@Req() { bid }: AuthenticatedRequest, @Query() query: CrudQuery) {
    return this.affiliateLinkTrackingService.getAffiliateLinks(bid, query);
  }

  @Get('active-links-by-brand')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getActiveLinksByBrand(
    @Req() { bid }: AuthenticatedRequest,
    @Query('brandName') brandName: string,
  ) {
    return this.affiliateLinkTrackingService.getActiveLinksByBrand(bid, brandName);
  }

  @Get('brands')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getAffiliateLinkTrackingBrands(
    @Req() { bid }: AuthenticatedRequest,
    @Query() query: CrudQuery,
  ) {
    return this.affiliateLinkTrackingService.getAffiliateLinkTrackingBrands(bid, query);
  }

  @Post(':id/approve')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async approveAffiliateSale(@Param('id') id: string) {
    const alt = await this.affiliateLinkTrackingService.update(id, {
      conversionStatus: AffiliateConversionStatus.CREDITED,
      amountCreditedDate: new Date(),
    });

    if (!alt?._id) {
      throw new NotFoundException('Affiliate link tracking data not found.');
    }

    await this.walletService.creditAffiliateEarning(
      { bid: alt.bid },
      alt.affiliateCommission,
      alt._id,
    );

    return { success: true };
  }

  @Delete(':id/reject')
  @Roles('superadmin')
  @UseGuards(Auth, RolesGuard)
  async rejectAffiliateSale(@Param('id') id: string) {
    await this.affiliateLinkTrackingService.update(id, {
      conversionStatus: AffiliateConversionStatus.REJECTED,
    });

    return { success: true };
  }
}
