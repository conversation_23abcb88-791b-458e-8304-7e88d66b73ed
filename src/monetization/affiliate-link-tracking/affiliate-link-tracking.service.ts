import type { CrudQuery } from '@/crud/crud.interface';

import { Injectable, Logger } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { BlogAffiliateKeyword } from '@/blog/blog.model';

import { AffiliateNetwork, AffiliateConversionStatus } from './affiliate-link-tracking.model';
import { AffiliateLinkTracking } from './affiliate-link-tracking.model';
import { ImpactService } from '../impact.service';
import { generateTrackingLink } from '../utils/tracking.utils';
import { CrudService } from '@/crud/crud.service';

@Injectable()
export class AffiliateLinkTrackingService extends CrudService<AffiliateLinkTracking> {
  private readonly logger = new Logger(this.constructor.name);
  constructor(
    @InjectModel(AffiliateLinkTracking.name)
    private readonly affiliateLinkTrackingModel: Model<AffiliateLinkTracking>,
    private readonly impactService: ImpactService,
  ) {
    super(affiliateLinkTrackingModel);
  }

  async getPendingBalanceStats(bid: string) {
    const pipeline = [
      {
        $match: {
          bid: String(bid),
          conversionStatus: { $in: ['PENDING', 'PROCESSING', 'APPROVED', 'PAID'] },
        },
      },
      {
        $group: {
          _id: '$conversionStatus',
          amount: { $sum: '$affiliateCommission' },
        },
      },
      {
        $project: {
          status: '$_id',
          amount: 1,
          _id: 0,
        },
      },
    ];

    const result: Record<string, number> = (
      await this.affiliateLinkTrackingModel.aggregate(pipeline)
    ).reduce((r, c) => {
      r[c.status] = c.amount;
      return r;
    }, {});

    const inReview =
      (result[AffiliateConversionStatus.PENDING] || 0) +
      (result[AffiliateConversionStatus.PROCESSING] || 0);
    const approved = result[AffiliateConversionStatus.APPROVED] || 0;
    const totalPayable = inReview + approved + (result[AffiliateConversionStatus.PAID] || 0);

    return { inReview, approved, totalPayable };
  }

  async getAffiliateLinks(bid: string, query: CrudQuery) {
    let skip = 0;
    if (query.page) {
      skip = +(query.limit || 10) * (+query.page - 1);
    }

    const result = await this.affiliateLinkTrackingModel.aggregate([
      { $match: { bid: String(bid) } },

      {
        $group: {
          _id: '$affiliateLink',
          blogId: { $first: '$blogId' },
          brandLogoUrl: { $first: '$networkBrandLogoUrl' },
          brandName: { $first: '$networkBrandName' },
          productImageUrl: { $first: '$productImageUrl' },
          productName: { $first: '$productName' },
          totalActionCount: {
            $sum: {
              $cond: [
                { $ne: ['$conversionStatus', AffiliateConversionStatus.NO_CONVERSION] },
                1,
                0,
              ],
            },
          },
          totalClickCount: { $sum: '$clickCount' },
          totalEarnings: { $sum: '$affiliateCommission' },
        },
      },

      {
        $facet: {
          metadata: [{ $count: 'total' }],
          data: [
            { $sort: { totalActionCount: -1, totalClickCount: -1, _id: -1 } },
            { $skip: skip },
            { $limit: +query?.limit || 10 },
          ],
        },
      },
    ]);

    const metadata = result[0]?.metadata[0] || { total: 0 };
    const data = result[0]?.data || [];

    return {
      data: data.map((item) => ({
        ...item,
        trackingLink: generateTrackingLink(bid, item.blogId, item._id),
      })),
      total: metadata.total,
    };
  }

  async getActiveLinksByBrand(bid: string, brandName: string) {
    const links = await this.affiliateLinkTrackingModel
      .find({ bid, networkBrandName: brandName })
      .select(
        'affiliateLink productName productImageUrl productCategory networkBrandName networkBrandLogoUrl blogId',
      );
    return links.map((link) => ({
      ...link.toObject(),
      trackingLink: generateTrackingLink(bid, link.blogId, link.affiliateLink),
    }));
  }

  async getAffiliateLinkTrackingBrands(bid: string, query: CrudQuery) {
    let skip = 0;
    if (query.page) {
      skip = +(query.limit || 10) * (+query.page - 1);
    }

    const result = await this.affiliateLinkTrackingModel.aggregate([
      { $match: { bid: String(bid) } },

      {
        $group: {
          _id: '$networkBrandName',
          brandLogoUrl: { $first: '$networkBrandLogoUrl' },
          brandName: { $first: '$networkBrandName' },
          productImageUrl: { $first: '$productImageUrl' },
          productName: { $first: '$productName' },
          totalActionCount: {
            $sum: {
              $cond: [
                { $ne: ['$conversionStatus', AffiliateConversionStatus.NO_CONVERSION] },
                1,
                0,
              ],
            },
          },
          totalClickCount: { $sum: '$clickCount' },
          totalEarnings: { $sum: '$affiliateCommission' },
          affiliateLinks: { $addToSet: '$affiliateLink' },
        },
      },

      {
        $addFields: {
          linkCount: { $size: '$affiliateLinks' },
        },
      },

      {
        $facet: {
          metadata: [{ $count: 'total' }],
          data: [
            { $sort: { totalActionCount: -1, totalClickCount: -1, _id: -1 } },
            { $skip: skip },
            { $limit: +query?.limit || 10 },
          ],
        },
      },
    ]);

    const metadata = result[0]?.metadata[0] || { total: 0 };
    const data = result[0]?.data || [];

    return {
      data: data,
      total: metadata.total,
    };
  }

  async createTrackingEntry(
    bid: string,
    blogId: string,
    affiliateKeyword: BlogAffiliateKeyword,
  ): Promise<AffiliateLinkTracking> {
    try {
      const campaign = await this.impactService.fetchCampaign(affiliateKeyword.productCampaignId);
      const advertiserFavicon = `https://www.google.com/s2/favicons?domain=${campaign.AdvertiserUrl}&sz=64`;
      const tracking = new this.affiliateLinkTrackingModel({
        bid,
        blogId,
        affiliateLink: affiliateKeyword.affiliateLink,
        networkBrandId: campaign.AdvertiserId,
        networkBrandName: campaign.AdvertiserName,
        networkBrandLogoUrl: advertiserFavicon,
        productId: affiliateKeyword.productId,
        productName: affiliateKeyword.productName,
        productImageUrl: affiliateKeyword.productImageUrl,
        productCatalogId: affiliateKeyword.productCatalogId,
        productPrice: affiliateKeyword.productCurrentPrice,
        productCurrency: affiliateKeyword.productCurrency,
        productCategory: affiliateKeyword.productCategory,
        affiliateNetwork: AffiliateNetwork.IMPACT,
        networkCampaignId: affiliateKeyword.productCampaignId,
        searchTerms: [affiliateKeyword.keyword, ...(affiliateKeyword.searchTerms || [])],
        confidence: affiliateKeyword.confidence,
        clickCount: 0,
        metadata: {},
      });

      return await tracking.save();
    } catch (error) {
      this.logger.error(`Failed to create affiliate link tracking entry: ${error.message}`);
      throw error;
    }
  }

  async updateConversionStatus(
    trackingId: string,
    status: AffiliateConversionStatus,
    metadata: Record<string, any>,
  ): Promise<void> {
    await this.affiliateLinkTrackingModel.findByIdAndUpdate(
      trackingId,
      {
        $set: {
          conversionStatus: status,
          metadata: { ...metadata },
        },
      },
      { new: true },
    );
  }

  async incrementClickCount(trackingId: string): Promise<void> {
    await this.affiliateLinkTrackingModel.findByIdAndUpdate(
      trackingId,
      {
        $inc: { clickCount: 1 },
        $set: { lastClickDate: new Date() },
      },
      { new: true },
    );
  }

  async countActions(bid: Types.ObjectId, start: Date, end: Date) {
    return this.affiliateLinkTrackingModel
      .aggregate([
        {
          $match: {
            bid: bid.toString(),
            conversionStatus: { $ne: AffiliateConversionStatus.NO_CONVERSION },
            createdAt: { $gte: start, $lte: end },
          },
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' },
            },
            count: { $sum: 1 },
          },
        },
      ])
      .then(
        (data: { _id: Record<'year' | 'month' | 'day', number>; count: number }[]) =>
          new Map(
            data.map(
              ({ _id: { year, month, day }, count }) =>
                <const>[new Date(Date.UTC(year, month - 1, day)), count],
            ),
          ),
      );
  }

  async getAffiliateLinkTracking(
    filters: Record<string, any> = {},
    options: {
      select?: string;
    } = {},
  ) {
    const query = this.affiliateLinkTrackingModel.find(filters);
    if (options.select) {
      query.select(options.select);
    }
    return query.exec();
  }
}
