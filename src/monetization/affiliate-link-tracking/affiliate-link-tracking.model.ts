import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum AffiliateNetwork {
  IMPACT = 'IMPACT',
  // Future networks can be added here
  // AWIN = 'AWIN',
  // RAKUTEN = 'RAKUTEN',
  // ShareASale = 'SHAREASALE',
  // ClickBank = 'CLICKBANK',
  // Amazon Associates = 'AMAZON_ASSOCIATES',
  // eBay Partner Network = 'EBAY_PARTNER_NETWORK',
  // Commission Junction = 'COMMISSION_JUNCTION',
}

export enum AffiliateConversionStatus {
  NO_CONVERSION = 'NO_CONVERSION',
  // User Status: Reviewing
  PENDING = 'PENDING', // Initial tracking state
  PROCESSING = 'PROCESSING', // Being processed

  // User Status: Approved
  APPROVED = 'APPROVED', // Approved but not paid

  // User Status: Processing
  PAID = 'PAID', // Payment received

  // User Status: Paid
  CREDITED = 'CREDITED',

  // User Status: Denied/Expired
  REJECTED = 'REJECTED', // Rejected
  REVERSED = 'REVERSED', // Reversed after approval
  EXPIRED = 'EXPIRED', // Past tracking window
}

@Schema({ timestamps: true })
export class AffiliateLinkTracking extends Document {
  // Blog and Content Association
  @Prop({ required: true, index: true })
  bid: string; // Blog unique identifier

  @Prop({ required: true, index: true })
  blogId: string; // Blog MongoDB _id

  @Prop({ required: true, index: true })
  affiliateLink: string;

  // Product Information
  @Prop({ required: true })
  productId: string;

  @Prop()
  productName: string;

  @Prop()
  productImageUrl: string;

  @Prop()
  productCatalogId: string;

  @Prop({ required: true })
  productPrice: number;

  @Prop({ required: true })
  productCurrency: string;

  @Prop()
  productCategory?: string;

  // Affiliate Network Details
  @Prop({ required: true, enum: AffiliateNetwork })
  affiliateNetwork: AffiliateNetwork;

  @Prop({ required: true })
  networkCampaignId: string; // e.g. Impact's CampaignId

  @Prop()
  networkBrandId: string; // Brand/Advertiser ID

  @Prop()
  networkBrandName: string;

  @Prop()
  networkBrandLogoUrl: string;

  // Tracking Information
  @Prop({ default: 0 })
  clickCount: number;

  @Prop()
  lastClickDate?: Date;

  @Prop()
  purchaseDate?: Date; // When the purchase occurred

  @Prop({
    type: String,
    enum: AffiliateConversionStatus,
    default: AffiliateConversionStatus.NO_CONVERSION,
  })
  conversionStatus: AffiliateConversionStatus;

  @Prop()
  statusDetails?: string; // Additional status information

  // Financial Information
  @Prop()
  conversionAmount?: number; // Actual sale amount

  @Prop()
  conversionCurrency?: string;

  @Prop()
  affiliateCommission?: number; // Our commission (after network split)

  // Important Dates
  @Prop()
  networkLockDate?: Date; // When network locks the conversion

  @Prop()
  networkReleaseDate?: Date; // When network releases payment

  @Prop()
  amountCreditedDate?: Date; // When we received the payment

  // Network-specific Metadata (any type)
  @Prop({ type: Object })
  metadata: Record<string, any>;

  // Search and Matching
  @Prop({ type: [String] })
  searchTerms: string[];

  @Prop()
  confidence?: number;
}

export type AffiliateLinkTrackingDocument = AffiliateLinkTracking & Document;
export const AffiliateLinkTrackingSchema = SchemaFactory.createForClass(AffiliateLinkTracking);

// Indexes
AffiliateLinkTrackingSchema.index({ bid: 1 });
AffiliateLinkTrackingSchema.index({ blogId: 1 });
AffiliateLinkTrackingSchema.index({ affiliateLink: 1 });
AffiliateLinkTrackingSchema.index({ networkCampaignId: 1, conversionStatus: 1 });
AffiliateLinkTrackingSchema.index({ purchaseDate: 1 });
AffiliateLinkTrackingSchema.index({ networkLockDate: 1 });
AffiliateLinkTrackingSchema.index({ networkReleaseDate: 1 });
