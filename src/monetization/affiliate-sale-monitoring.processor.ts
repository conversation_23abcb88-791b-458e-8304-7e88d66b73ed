import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InjectQueue } from '@nestjs/bull';
import { <PERSON>ron } from '@nestjs/schedule';
import { Inject } from '@nestjs/common';

import { BaseProcessor } from '@common/queue/base.processor';
import { JOB_QUEUES } from '@/common/constants';
import { AffiliateLinkTracking } from './affiliate-link-tracking/affiliate-link-tracking.model';
import { AffiliateAction } from './interfaces/affiliate-action.interface';
import { AffiliateNetworkService } from './interfaces/affiliate-network-service.abstract';
import { Analytics } from '@/analytics/analytics.model';
import { SlackService } from '@/common/services/slack.service';

interface MonitoringJobData {
  lastFetchDate?: string; // ISO string date
}

@Processor(JOB_QUEUES.AFFILIATE_SALE_MONITORING)
export class AffiliateSaleMonitoringProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.AFFILIATE_SALE_MONITORING;
  protected readonly logger = new Logger(AffiliateSaleMonitoringProcessor.name);

  constructor(
    @InjectModel(AffiliateLinkTracking.name)
    private readonly affiliateLinkTrackingModel: Model<AffiliateLinkTracking>,
    @InjectModel(Analytics.name)
    private readonly analyticsModel: Model<Analytics>,
    @InjectQueue(JOB_QUEUES.AFFILIATE_SALE_MONITORING)
    affiliateSaleMonitoringQueue: Queue,
    @Inject(AffiliateNetworkService)
    private readonly affiliateNetworkService: AffiliateNetworkService,
    private readonly slackService: SlackService,
  ) {
    super(affiliateSaleMonitoringQueue);
  }

  @Process()
  protected async process(job: Job<MonitoringJobData>): Promise<void> {
    return this.processWithLock(job, async () => {
      if (process.env.NODE_ENV === 'production') {
        await this.monitorSales(job);
      }
    });
  }

  @Cron('*/30 * * * *') // Run every hour
  protected async scheduledMonitoring() {
    try {
      // Get the last job
      const lastJob = await this.queue.getCompleted(0, 1);
      const jobData: MonitoringJobData = lastJob.length > 0 ? lastJob[0].data : {};

      // Create a new job with the last fetch date
      await this.queue.add(jobData);
    } catch (error) {
      this.logger.error(`Scheduled monitoring failed: ${error.message}`);
    }
  }

  private async monitorSales(job: Job<MonitoringJobData>): Promise<void> {
    try {
      let startDate: Date;

      if (job.data.lastFetchDate) {
        // Use the last fetch date if available
        startDate = new Date(job.data.lastFetchDate);
      } else {
        // Default to 1 days ago if no last fetch date
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 1);
      }

      const endDate = new Date();
      const actions = await this.affiliateNetworkService.fetchActions(startDate, endDate);

      // Notify about found actions
      if (actions.length > 0) {
        await this.slackService.sendAffiliateActionsFound(actions.length, startDate, endDate);
      }

      for (const action of actions) {
        const potentialMatches = await this.findPotentialMatches(action);
        console.log({ potentialMatches });

        // Notify about potential matches
        if (potentialMatches.length > 0) {
          await this.slackService.sendAffiliatePotentialMatches(action, potentialMatches);

          const bestMatch = await this.findBestMatch(action, potentialMatches);

          // Notify about best match result
          if (bestMatch) {
            await this.slackService.sendAffiliateBestMatch(action, bestMatch);

            await this.updateTrackingEntry(bestMatch, action);
          } else {
            // Notify about no best match found
            await this.slackService.sendAffiliateNoMatch(action);
          }
        }
      }

      // Update the job data with the current fetch time
      await job.update({
        lastFetchDate: endDate.toISOString(),
      });
    } catch (error) {
      // Notify about errors
      await this.slackService.sendAffiliateError(error);

      this.logger.error(`Failed to update conversion statuses: ${error.message}`);
      throw error;
    }
  }

  private async findPotentialMatches(action: AffiliateAction): Promise<AffiliateLinkTracking[]> {
    const timeWindow = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    const eventDate = new Date(action.purchaseDate);
    const startDate = new Date(eventDate.getTime() - timeWindow);
    const endDate = new Date(eventDate.getTime() + timeWindow);
    console.log({
      networkCampaignId: action.campaignId,
      startDate,
      endDate,
    });
    return this.affiliateLinkTrackingModel.find({
      networkCampaignId: action.campaignId,
      // lastClickDate: {
      //   $gte: startDate,
      //   $lte: endDate,
      // },
      // affiliateLink: {
      //   $regex: action.metadata.adId?.toString() || '',
      // },
    });
  }

  private async findBestMatch(
    action: AffiliateAction,
    potentialMatches: AffiliateLinkTracking[],
  ): Promise<AffiliateLinkTracking | null> {
    let bestMatch = null;
    let highestScore = 0;

    for (const match of potentialMatches) {
      let score = 0;

      // Match campaign and ad ID (highest weight)
      if (match.networkCampaignId === action.campaignId) score += 5;
      if (match.affiliateLink.includes(action.metadata.adId?.toString() || '')) score += 5;

      // Match amount (within 10% difference)
      const amountDiff = Math.abs(match.productPrice - action.amount);
      if (amountDiff / action.amount <= 0.1) score += 3;

      // Check analytics data
      try {
        // Look for analytics entries around the purchase date
        const startDate = new Date(action.purchaseDate);
        startDate.setDate(startDate.getDate() - 1); // Look back 1 day

        const endDate = new Date(action.purchaseDate);
        endDate.setDate(endDate.getDate() + 1); // Look forward 1 day

        // Find analytics entries for this blog
        const analytics = await this.analyticsModel.findOne({
          bid: match.bid,
          blogId: match.blogId,
        });

        if (analytics) {
          // If the ref matches our affiliate link
          if (analytics.ref.includes(match.affiliateLink)) {
            score += 8; // High score for matching ref
          }
          // Check for clicks around the action date
          for (const [dateKey, eventData] of Object.entries(analytics.eventsByDate)) {
            const [date, eventType] = dateKey.split('_');

            // Only look at click events
            if (eventType === 'click') {
              const eventDate = new Date(date);

              // Check if the click is within our time window
              if (eventDate >= startDate && eventDate <= endDate) {
                // Geo matching (if available)
                if (eventData && action.customerLocation) {
                  if (Object.keys(eventData['country']).includes(action.customerLocation.country)) {
                    score += 2;
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        this.logger.error(`Error checking analytics data: ${error.message}`);
      }

      if (score > highestScore) {
        highestScore = score;
        bestMatch = match;
      }
    }

    // Require higher minimum confidence since we have more data points
    return highestScore >= 10 ? bestMatch : null;
  }

  private async updateTrackingEntry(
    entry: AffiliateLinkTracking,
    action: AffiliateAction,
  ): Promise<void> {
    const updates: Partial<AffiliateLinkTracking> = {
      conversionStatus: this.affiliateNetworkService.mapNetworkStatus(action.state),
      conversionAmount: action.amount,
      conversionCurrency: action.currency,
      affiliateCommission: action.commission * 0.4, // 40% of the commission
      purchaseDate: action.purchaseDate,
      networkLockDate: action.lockDate,
      networkReleaseDate: action.releaseDate,
      metadata: {
        ...entry.metadata,
        ...action.metadata,
      },
    };

    await this.affiliateLinkTrackingModel.findByIdAndUpdate(entry._id, updates, { new: true });
  }
}
