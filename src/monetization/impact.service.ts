import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { JOB_QUEUES, JOB_OPTIONS } from '@/common/constants';
import { ImpactProduct } from './interfaces/impact-product.interface';
import { ConfigService } from '@nestjs/config';
import { mapImpactProductResponse } from './utils/impact.utils';
import axios from 'axios';
import * as http from 'http';
import * as https from 'https';
import { S3Service } from '@/common/services/s3.service';
import { ImpactCampaign } from './interfaces/impact-campaign.interface';
import { ImpactCatalog } from './interfaces/impact-catalog.interface';

@Injectable()
export class ImpactService {
  private readonly logger = new Logger(ImpactService.name);
  private requestCount = 0;
  private lastResetTime = Date.now();
  private recentRequests: number[] = [];
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly authHeader: string;
  private readonly httpAgent: http.Agent;
  private readonly httpsAgent: https.Agent;

  constructor(
    @InjectQueue(JOB_QUEUES.IMPACT_PRODUCT_SEARCH)
    private readonly impactQueue: Queue,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    const accountSid = this.configService.get('IMPACT_ACCOUNT_SID');
    this.apiKey = this.configService.get('IMPACT_API_KEY');
    this.baseUrl = `${this.configService.get('IMPACT_API_BASE_URL')}/${accountSid}`;
    this.authHeader = `Basic ${Buffer.from(`${accountSid}:${this.apiKey}`).toString('base64')}`;

    // Set up connection pooling with limited max sockets
    this.httpAgent = new http.Agent({
      keepAlive: true,
      maxSockets: 10,
      timeout: 60000,
    });
    this.httpsAgent = new https.Agent({
      keepAlive: true,
      maxSockets: 10,
      timeout: 60000,
    });

    // Reset counter every hour
    setInterval(() => {
      this.requestCount = 0;
      this.lastResetTime = Date.now();
    }, 3600000);
  }

  async fetchCatalog(catalogId: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/Catalogs/${catalogId}`, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        auth: {
          username: this.configService.get('IMPACT_ACCOUNT_SID'),
          password: this.configService.get('IMPACT_API_KEY'),
        },
        httpAgent: this.httpAgent,
        httpsAgent: this.httpsAgent,
      });

      return response.data as ImpactCatalog;
    } catch (error) {
      this.logger.error(
        `Failed to fetch catalog from Impact API for catalogId "${catalogId}"`,
        error.stack,
      );
      throw error;
    }
  }

  async fetchCampaign(campaignId: string) {
    try {
      const response = await axios.get(`${this.baseUrl}/Campaigns/${campaignId}`, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        auth: {
          username: this.configService.get('IMPACT_ACCOUNT_SID'),
          password: this.configService.get('IMPACT_API_KEY'),
        },
        httpAgent: this.httpAgent,
        httpsAgent: this.httpsAgent,
      });

      return response.data as ImpactCampaign;
    } catch (error) {
      this.logger.error(
        `Failed to fetch catalog from Impact API for catalogId "${campaignId}"`,
        error.stack,
      );
      throw error;
    }
  }

  async getPublicImageLink(imagePath: string) {
    try {
      const response = await axios.get(
        this.configService.get<string>('IMPACT_API_BASE_URL') + imagePath,
        {
          auth: {
            username: this.configService.get('IMPACT_ACCOUNT_SID'),
            password: this.configService.get('IMPACT_API_KEY'),
          },
          httpAgent: this.httpAgent,
          httpsAgent: this.httpsAgent,
        },
      );
      return await this.s3Service.uploadFile(
        Buffer.from(response.data),
        imagePath,
        'impact-images',
        response.headers['content-type'],
      );
    } catch (error) {
      this.logger.error(
        `Failed to fetch image from Impact API for imagePath "${imagePath}"`,
        error.stack,
      );
      throw error;
    }
  }

  async searchProducts(keyword: string): Promise<ImpactProduct[]> {
    const job = await this.impactQueue.add(
      'search',
      { keyword },
      {
        ...JOB_OPTIONS,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );

    return (await job.finished()) as ImpactProduct[];
  }

  async executeSearch(keyword: string): Promise<ImpactProduct[]> {
    try {
      const response = await this.makeRequest(keyword);
      this.requestCount++;
      this.recentRequests.push(Date.now());
      return response.map(mapImpactProductResponse);
    } catch (error) {
      if (error.response?.status === 429) {
        await this.handleRateLimitError(error);
        return this.executeSearch(keyword);
      }
      throw error;
    }
  }

  private async makeRequest(keyword: string): Promise<any[]> {
    try {
      const response = await fetch(this.buildSearchUrl(keyword), {
        method: 'GET',
        headers: {
          Authorization: this.authHeader,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Impact API error: ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      this.logger.debug(
        `Affiliate product search: ${
          data?.Items?.length || 0
        } results found for keyword ${keyword}`,
      );

      return data?.Items || [];
    } catch (error) {
      this.logger.error(
        `Failed to fetch products from Impact API for keyword "${keyword}"`,
        error.stack,
      );
      throw error;
    }
  }

  private buildSearchUrl(keyword: string): string {
    const params = new URLSearchParams({
      Keyword: keyword,
      PageSize: '100',
    });

    return `${this.baseUrl}/Catalogs/ItemSearch?${params}`;
  }

  private async handleRateLimitError(error: any): Promise<void> {
    const retryAfter = error.response?.headers?.['retry-after'];
    const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : 5000;
    this.logger.warn(`Rate limit hit, waiting ${waitTime}ms before retry`);
    await new Promise((resolve) => setTimeout(resolve, waitTime));
  }
}
