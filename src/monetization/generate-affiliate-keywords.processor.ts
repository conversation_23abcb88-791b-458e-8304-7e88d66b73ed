import { Processor, Process, InjectQueue, OnQueueFailed } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { BaseProcessor } from '@common/queue/base.processor';
import { JOB_QUEUES } from '@/common/constants';
import { Blog, BlogAffiliateKeyword, BlogQueuePayload } from '@/blog/blog.model';
import { BlogService } from '@/blog/blog.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { MonetizationService } from './monetization.service';

@Processor(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS)
export class GenerateAffiliateKeywordsProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS;
  protected readonly logger = new Logger(GenerateAffiliateKeywordsProcessor.name);
  private readonly MAX_RETRIES = 3;

  constructor(
    @InjectQueue(JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH) private affiliateProductSearchQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS) generateAffiliateKeywordsQueue: Queue,
    private readonly blogService: BlogService,
    private readonly monetizationService: MonetizationService,
    private readonly gatewayService: GatewayService,
  ) {
    super(generateAffiliateKeywordsQueue);
  }

  @Process({ concurrency: 3 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { blogId, bid, uid, blogContent, identifier, blogTitle, blogLanguage } = job.data;

      // Send initial status update
      this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
        _id: blogId,
        identifier,
        affiliateLinkGenerationStatus: 'affiliate_keywords_generating',
        percentComplete: 5,
      });

      // Find keywords from content
      const additionalKeywords =
        await this.monetizationService.findPotentialKeywordsForAffiliateLinks(
          blogContent,
          blogTitle,
          blogLanguage,
        );

      // Combine and format keywords with initial status
      const affiliateKeywords: BlogAffiliateKeyword[] = [
        ...additionalKeywords.map((k) => ({
          ...k,
          matchedAt: new Date(),
          status: 'pending' as const,
        })),
      ];

      // Save initial keywords to blog using Partial<Blog>
      await this.blogService.update(bid, blogId, {
        affiliateKeywords,
        affiliateKeywordsUpdatedAt: new Date(),
      } as Partial<Blog>);

      this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
        _id: blogId,
        identifier,
        affiliateLinkGenerationStatus: 'affiliate_keywords_generating',
        percentComplete: 10,
      });

      // Add to product search queue
      await this.affiliateProductSearchQueue.add({
        ...job.data,
        affiliateKeywords,
      });

      this.logger.debug(
        `Generated and saved ${affiliateKeywords.length} keywords for blog ${blogId}`,
      );

      // Update status on completion
      this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
        _id: blogId,
        identifier,
        affiliateLinkGenerationStatus: 'affiliate_keywords_generated',
        percentComplete: 15,
      });
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    const { blogId, bid, uid, identifier } = job.data;

    // For errors, retry up to MAX_RETRIES times
    if (job.attemptsMade < this.MAX_RETRIES) {
      this.logger.warn(
        `Affiliate keyword generation attempt ${job.attemptsMade + 1}/${
          this.MAX_RETRIES
        } failed for blog ${blogId}. Retrying...`,
      );
      await super.handleFailedJob(job, error);
      return;
    }

    // After MAX_RETRIES attempts, mark as permanently failed
    this.logger.error(
      `Affiliate keyword generation failed permanently after ${this.MAX_RETRIES} attempts for blog ${blogId}`,
    );

    await this.blogService.update(bid, blogId, {
      affiliateLinkGenerationStatus: 'affiliate_keywords_failed',
      generationStatus: 'Failed to generate affiliate keywords',
      failedQueue: JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS,
    } as Partial<Blog>);

    this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
      _id: blogId,
      identifier,
      affiliateLinkGenerationStatus: 'affiliate_keywords_failed',
    });

    await job.moveToFailed(
      {
        message: `Failed after ${this.MAX_RETRIES} attempts: ${error.message}`,
      },
      true,
    );
  }
}
