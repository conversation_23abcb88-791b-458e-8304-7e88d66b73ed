import { AnthropicProvider, OpenAiProvider } from '@llm/index';
import { BaseProvider } from '@llm/providers/base.provider';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Type } from '@sinclair/typebox';
import { OpenAiEmbeddingProvider } from '@llm/embedding/providers/openai.embedding.provider';
import { ImpactProduct } from './interfaces/impact-product.interface';
import { Cron } from 'nest-schedule';
import * as path from 'path';
import * as fs from 'fs/promises';
import { COUNTRY_TLD_MAP } from './interfaces/country-tld-map.interface';

type MatchingMethod = 'semantic' | 'llm' | 'auto';

interface ProductMatch {
  product: ImpactProduct;
  confidence: number;
  reasoning?: string;
}

interface ToolCallArgs {
  matches: {
    productIndex: number;
    confidence: number;
    reasoning: string;
  }[];
}

interface FindTopMatchesParams {
  keyword: string;
  blogTitle: string;
  products: ImpactProduct[];
  method?: MatchingMethod;
  language?: string;
  limit?: number;
  affiliateTargetLocation?: string;
}

interface FindSemanticMatchesParams {
  keyword: string;
  products: ImpactProduct[];
  limit: number;
}

interface FindLLMMatchesParams {
  keyword: string;
  blogTitle?: string;
  products: ImpactProduct[];
  language?: string;
  limit?: number;
  affiliateTargetLocation?: string;
}

interface GenerateEmbeddingsParams {
  keyword: string;
  products: ImpactProduct[];
}

interface FindBestAffiliateProductParams {
  keyword: string;
  products: ImpactProduct[];
  method?: MatchingMethod;
  language?: string;
}

interface GetAffiliateProductParams {
  keyword: string;
  products: ImpactProduct[];
  language?: string;
}

interface AffiliateKeywordMatch {
  keyword: string;
  beforeWord: string;
  afterWord: string;
  confidence?: number;
  searchTerms: string[];
}

interface KeywordExtractionResponse {
  keywords: AffiliateKeywordMatch[];
}

@Injectable()
export class MonetizationService {
  private readonly logger = new Logger(MonetizationService.name);
  private providers: Map<string, BaseProvider>;
  private embeddingProvider: OpenAiEmbeddingProvider;
  private embeddingCache = new Map<string, any>();
  private readonly cacheTTL = 1000 * 60 * 60; // 1 hour

  constructor(private readonly configService: ConfigService) {
    this.providers = new Map();
    this.providers.set(
      OpenAiProvider.PROVIDER_NAME,
      new OpenAiProvider(this.configService.get('OPENAI_API_KEY')),
    );
    this.providers.set(
      AnthropicProvider.PROVIDER_NAME,
      new AnthropicProvider(this.configService.get('ANTHROPIC_API_KEY')),
    );
    this.embeddingProvider = new OpenAiEmbeddingProvider({
      apiKey: this.configService.get('OPENAI_API_KEY'),
    });
  }

  async findPotentialKeywordsForAffiliateLinks(
    content: string,
    title: string,
    blogLanguage: string,
  ): Promise<AffiliateKeywordMatch[]> {
    this.logger.debug(`Starting keyword extraction from content of length: ${content.length}`);
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);

    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    const systemPrompt = `You are an AI assistant specialized in identifying potential ${blogLanguage} keywords for affiliate marketing from ${blogLanguage} blog content.
    Your task is to:
    1. Extract keywords in their EXACT original form from content, preserving original spelling, script, and language
    2. Find product or service related keywords with commercial/transactional intent (brand names, product names, models)
    3. Capture the exact 2 to 4 words before and after each keyword to later match with content
    4. Generate English search terms for ALL keywords, regardless of the original language:
       - Create 1-3 search terms for each keyword (each term 1-4 words maximum)
       - Ensure search terms maintain commercial intent and product focus
       - Search terms should reflect what someone might search when looking to purchase the mentioned product/service

    Guidelines:
    - The content is in ${blogLanguage} language so most keywords should be in ${blogLanguage} and some may be in English or mixed with English
    - Extract keywords in their original form with NO transliteration or translation in the keyword field
    - Handle ALL content uniformly regardless of language - apply the same extraction process for all text
    - Adjust keyword count based on content length (more keywords for longer content, fewer for shorter)
    - Prioritize brand names, product names, model numbers, and commercially relevant terms
    - Multi-product content should have terms for each relevant product mentioned
    - Extract 2-4 words before and after each keyword for exact content matching
    - Never extract keywords from headers; use content instead

    Examples:
    Title: "Samsung Galaxy S24 vs iPhone 15 Camera Comparison"
    Keyword: "Samsung Galaxy S24" [original language preserved]
    Search terms: [
      "Samsung Galaxy S24",
      "Galaxy S24 smartphone"
    ]

    Title: "最好的电竞笔记本电脑 2024" (Best Gaming Laptops 2024)
    Keyword: "华硕 ROG" [original Chinese preserved]
    Search terms: [
      "ASUS ROG laptop",
      "ROG gaming notebook"
    ]

    Title: "iPhone 15 vs Samsung Galaxy S24 カメラ比較" (Camera Comparison)
    Keyword: "iPhone 15 プロマックス" [original Japanese preserved]
    Search terms: [
      "iPhone 15 Pro Max",
      "iPhone 15 camera"
    ]

    - Avoid overlapping keywords
    - Do not pick any adult, alcohol, drugs or illegal keywords
    - Prioritize commercially relevant product terms`;

    const userPrompt = `Analyze the following blog content and identify potential keywords for affiliate marketing links.
    You must find at least 5 keywords from the content. Must follow the given instructions carefully.

    Important instructions:

    1. EXTRACT KEYWORDS EXACTLY as they appear in the content:
       - Preserve original spelling, script, and language
       - Do not translate the keywords themselves
       - Include 2-4 words before and after each keyword for context
       - Keywords can be primarily in ${blogLanguage} language or mix with English or in English

    2. GENERATE ENGLISH SEARCH TERMS for each keyword:
       - ALL search terms must be in ENGLISH regardless of content language
       - If keywords are not in English, translate their meaning to English search terms
       - Create 1-3 English search terms per keyword (max 4 words each)
       - Combine relevant terms from the title "${title}" with keyword concepts
       - Ensure all search terms maintain commercial intent

    3. ADJUST OUTPUT BASED ON CONTENT LENGTH:
       - For large content (2000+ words): Extract 8-15 keywords
       - For longer content (1000+ words): Extract 5-8 keywords
       - For medium content (500-1000 words): Extract 3-5 keywords
       - For short content (under 500 words): Extract 2-3 keywords

    4. FOCUS ON COMMERCIAL INTENT:
       - Prioritize product names, brand names, model numbers
       - Select keywords someone might search when looking to purchase
       - Include key product specifications that affect purchasing decisions

    5. OUTPUT FORMAT:
       - Keywords: List in original form ${blogLanguage} keywords or mix with English (exactly as in content)
       - Search Terms: List in English only (1-3 terms per keyword)
       - Assign confidence score (0-1) based on commercial intent

    Blog content delimited by triple quotes:
    Content: """${content}"""

    You must find at least 5 keywords from the content. Must follow the above instructions carefully.`;

    const KeywordSchema = Type.Object({
      keyword: Type.String(),
      beforeWord: Type.String({ minLength: 1 }),
      afterWord: Type.String({ minLength: 1 }),
      confidence: Type.Number({ minimum: 0, maximum: 1 }),
      searchTerms: Type.Array(Type.String(), {
        minItems: 1,
        maxItems: 3,
        description: 'Array of search terms combining title and keyword context',
      }),
    });

    const KeywordsSchema = Type.Object({
      keywords: Type.Array(KeywordSchema, {
        minItems: Math.min(5, Math.floor(content.length / 200)), // Dynamic minimum based on content length
        maxItems: Math.min(30, Math.floor(content.length / 100)), // Dynamic maximum based on content length
        description: `Array of keywords extracted from the ${blogLanguage} blog content`,
      }),
    });

    try {
      this.logger.debug('Sending request to OpenAI for keyword extraction');
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'find_affiliate_keywords',
            description: `Find all potential keywords for placing affiliate links based on the guidelines provided from the ${blogLanguage} blog content`,
            parameters: KeywordsSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls && response.message.toolCalls.length > 0) {
        const rawArgs = response.message.toolCalls[0].args as unknown;

        if (this.isKeywordExtractionResponse(rawArgs)) {
          const keywords = this.postProcessKeywords(rawArgs.keywords, content);
          this.logger.debug(`Found ${keywords.length} potential keywords with context`);
          return keywords;
        }

        this.logger.warn('Invalid response structure from OpenAI');
        return [];
      }

      this.logger.warn('No keywords found in the OpenAI response');
      return [];
    } catch (error) {
      this.logger.error('Error finding potential keywords for affiliate links', error);
      throw error;
    }
  }

  private postProcessKeywords(
    keywords: AffiliateKeywordMatch[],
    content: string,
  ): AffiliateKeywordMatch[] {
    return keywords
      .filter((keyword) => {
        // Ensure keyword actually exists in content
        const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword.keyword)}\\b`, 'i');
        return keywordRegex.test(content);
      })
      .map((keyword) => {
        // Find the actual context in content
        const keywordRegex = new RegExp(
          `(.{0,30})\\b${this.escapeRegExp(keyword.keyword)}\\b(.{0,30})`,
          'i',
        );
        const match = content.match(keywordRegex);

        if (match) {
          return {
            ...keyword,
            beforeWord: match[1].trim(),
            afterWord: match[2].trim(),
          };
        }
        return keyword;
      })
      .filter(
        (keyword, index, self) =>
          // Remove duplicates and ensure minimum confidence
          index === self.findIndex((k) => k.keyword === keyword.keyword) &&
          keyword.confidence >= 0.6,
      )
      .sort((a, b) => b.confidence - a.confidence);
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // Type guard to check if the response matches our expected structure
  private isKeywordExtractionResponse(response: unknown): response is KeywordExtractionResponse {
    if (!response || typeof response !== 'object') {
      return false;
    }

    const typedResponse = response as Record<string, unknown>;

    if (!Array.isArray(typedResponse.keywords)) {
      return false;
    }

    return typedResponse.keywords.every(
      (keyword) =>
        typeof keyword === 'object' &&
        keyword !== null &&
        'keyword' in keyword &&
        'beforeWord' in keyword &&
        'afterWord' in keyword &&
        typeof (keyword as AffiliateKeywordMatch).keyword === 'string' &&
        typeof (keyword as AffiliateKeywordMatch).beforeWord === 'string' &&
        typeof (keyword as AffiliateKeywordMatch).afterWord === 'string' &&
        (!('confidence' in keyword) ||
          typeof (keyword as AffiliateKeywordMatch).confidence === 'number'),
    );
  }

  async findTopMatches({
    keyword,
    products,
    method = 'auto',
    language,
    limit = 3,
    blogTitle,
    affiliateTargetLocation,
  }: FindTopMatchesParams): Promise<ProductMatch[]> {
    this.logger.debug(
      `Finding top matches for keyword "${keyword}" using ${method} method total ${products.length} products`,
    );

    if (method === 'semantic') {
      return this.findTopSemanticMatches({ keyword, products, limit });
    }

    if (method === 'llm') {
      return this.findTopLLMMatches({
        keyword,
        products,
        language,
        limit,
        blogTitle,
        affiliateTargetLocation,
      });
    }

    const llmMatches = await this.findTopLLMMatches({
      keyword,
      products,
      language,
      limit,
      blogTitle,
      affiliateTargetLocation,
    });

    // Only generate embeddings if LLM didn't find good matches
    if (llmMatches.length === 0 || llmMatches[0].confidence < 0.7) {
      const semanticMatches = await this.findTopSemanticMatches({
        keyword,
        products,
        limit,
      });
      return this.deduplicateMatches([...llmMatches, ...semanticMatches]).slice(0, limit);
    }

    return llmMatches.slice(0, limit);
  }

  private async findTopSemanticMatches({
    keyword,
    products,
    limit,
  }: FindSemanticMatchesParams): Promise<ProductMatch[]> {
    this.logger.debug(`Finding semantic matches for keyword "${keyword}"`);
    const { keywordEmbedding, productEmbeddings } = await this.generateEmbeddings({
      keyword,
      products,
    });

    this.logger.debug('Calculating similarity scores');
    const matches: ProductMatch[] = productEmbeddings.embeddings.map((embedding, index) => {
      const similarity = this.cosineSimilarity(
        keywordEmbedding.embeddings[0].embedding,
        embedding.embedding,
      );
      return {
        product: products[index],
        confidence: similarity,
      };
    });

    const filteredMatches = matches
      .filter((match) => match.confidence > 0.5)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, limit);

    // Log first 5 semantic matches
    await this.logMatches('semantic', keyword, filteredMatches);

    return filteredMatches;
  }

  private async findTopLLMMatches({
    keyword,
    blogTitle,
    products,
    language = 'English',
    limit = 3,
    affiliateTargetLocation,
  }: FindLLMMatchesParams): Promise<ProductMatch[]> {
    this.logger.debug(`Finding LLM matches for keyword "${keyword}" (${language})`);
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    const locationPriority = affiliateTargetLocation
      ? `6. Location Matching (IMPORTANT):
       - Target location is "${affiliateTargetLocation}"
       - Prefer products from the target country/region
       - Check product domain TLDs (.com, .co.uk, etc.) to identify location
       - Check product name/description for location mentions
       - Check that product currency matches the target location (e.g., GBP for UK, EUR for Germany)
       - Products matching the target location should be scored higher`
      : '';

    const systemPrompt = `You are an AI assistant specialized in matching product or service keywords with affiliate products or services.
    Important matching rules:
    1. Language Matching (STRICT):
       - Product names MUST be in ${language} language
       - Reject products in different languages

    2. Keyword Matching (STRICT):
       - Product name or description MUST directly relate to the keyword and blog title
       - Reject products that don't explicitly match the search intent

    3. Product Relevance:
       - Prioritize exact product matches over accessories
       - For phones/devices, prioritize the actual device over cases/accessories
       - Match product categories accurately
       - If there is no match in categories set confidence score low

    4. Quality Checks:
       - Verify product name contains the actual keyword or its variants
       - Check price ranges are appropriate for the product type
       - Ensure currency is appropriate for the target market

    5. Price Consideration:
       - Products with higher prices (but still reasonable for the category) should be scored slightly higher
       - Compare prices within the same category/type of products
       - Extremely low or high prices might indicate poor quality or incorrect listings

    ${locationPriority}

    7. Currency Matching (CRITICAL):
       - Products with incorrect currency for the target location are COMPLETELY IRRELEVANT
       - Products MUST have the appropriate currency for the target location:
         * For UK/Britain: Must be GBP (£)
         * For US/USA: Must be USD ($)
         * For EU countries (Germany, France, Italy, Spain): Must be EUR (€)
         * For Canada: Must be CAD (C$)
         * For Australia: Must be AUD (A$)
         * For New Zealand: Must be NZD (NZ$)
         * For Japan: Must be JPY (¥)
       - Products with mismatched currency MUST receive EXTREMELY LOW confidence (below 0.3)
       - Currency is THE MOST IMPORTANT indicator of regional relevance
       - The audience CANNOT use products with incorrect regional currency

    8. Reasoning Requirements:
       - Explain EXACTLY why the product matches the keyword and blog title
       - Point out specific matching terms in the product name
       - Note any mismatches or concerns
       - Include price and location considerations in your reasoning
       - Explicitly mention if currency matches the target location

    Never match products that:
    - Are in a different language than specified
    - Don't directly relate to the keyword
    - Are adult content, alcohol, or drug-related
    - Have misleading titles or descriptions
    - Have incorrect currency for the target location`;

    // Create a stable array for product indexing and include indices in product info
    const productArray = products.map((p, index) => ({
      index,
      name: p.name,
      price: p.currentPrice,
      currency: p.currency,
      category: p.category || 'Uncategorized',
      subCategory: p.subCategory || '',
      url: p.url, // Include URL to help determine location by domain TLD
    }));

    const locationInstructions =
      affiliateTargetLocation && affiliateTargetLocation?.toLowerCase() !== 'global'
        ? `4. Location Relevance (CRITICAL):
       - Products matching the target location "${affiliateTargetLocation}" get MUCH higher priority
       - The location is in ISO 3166-1 alpha-2 code: ${affiliateTargetLocation} (like US, CA, GB, etc.)
       - Check product domain extensions (like .uk for UK, .ca for Canada)
       - Look for location names in product descriptions
       - Language in the content can also be considered to match the currency
       - ABSOLUTELY REQUIRED: Currency MUST match the target location:
         * For UK/Britain: Must be GBP (£)
         * For US/USA: Must be USD ($)
         * For EU countries: Must be EUR (€)
         * For Canada: Must be CAD (C$)
         * For Australia: Must be AUD (A$)
       - Products with mismatched currency are IRRELEVANT to the audience
       - Give the LOWEST possible confidence score to products with wrong currency
       - Products with correct currency but other location mismatches should get reduced confidence
       - ONLY the correct country's products are useful to the reader`
        : '';

    const userPrompt = `Find the top 5 matching products or services for the keyword "${keyword}" and to be placed in a blog where title is "${
      blogTitle || ''
    }" from the given list of products.

    IMPORTANT: For each match, use the exact product name from the list when explaining the reasoning.
    RULES:
    1. Language Matching
      - Only select products with names in ${language} language
    2. Context Alignment:
      - How well the product matches the blog title and keyword
      - Category should match the blog category
    3. Matching Rules:
      - Products must align with both blog title context AND keyword context
      - Products in the same category as the blog topic get higher priority
      - Brand matches are valuable but not required
      - Products with higher prices (but still reasonable) should be scored slightly higher
    ${locationInstructions}
    5. Confidence should be between 0.1 to 1

    Products: ${JSON.stringify(
      productArray.map((p, index) => ({
        index, // Include index in the product info
        name: p.name,
        price: p.price,
        currency: p.currency,
        category: p.category || 'Uncategorized',
        subCategory: p.subCategory || '',
      })),
    )}`;

    const TopMatchesSchema = Type.Object({
      matches: Type.Array(
        Type.Object({
          productIndex: Type.Number(),
          confidence: Type.Number(),
          reasoning: Type.String({
            description: 'one line explanation of why this product matches the keyword and blog',
          }),
        }),
        { minItems: 1, maxItems: 5 }, // Always require exactly 5 matches
      ),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'find_top_product_matches',
            description: 'Find the 5 best matching products with detailed reasoning',
            parameters: TopMatchesSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls && response.message.toolCalls.length > 0) {
        const matchData = response.message.toolCalls[0].args as unknown as ToolCallArgs;

        // Validate indices before creating matches
        const allMatches = matchData.matches
          .filter((match) => {
            if (match.productIndex < 0 || match.productIndex >= products.length) {
              this.logger.error(
                `Invalid product index ${match.productIndex}, max valid index: ${
                  products.length - 1
                }`,
              );
              return false;
            }
            return true;
          })
          .map((match) => {
            const product = products[match.productIndex];
            // Validate that reasoning mentions the correct product
            const reasoning = match.reasoning.includes(product.name)
              ? match.reasoning
              : `${product.name}: ${match.reasoning}`;

            // Apply price-based scoring (products with higher price get a small boost)
            let priceBoost = 0;
            if (product.currentPrice && !isNaN(parseFloat(product.currentPrice.toString()))) {
              const price = parseFloat(product.currentPrice.toString());
              // Simple price-based boost - max 5% boost
              priceBoost = Math.min(price / 1000, 0.05);
            }

            // Apply location-based scoring
            let locationBoost = 0;
            if (affiliateTargetLocation && product.url) {
              // Parse URL to extract domain
              try {
                const url = new URL(product.url);
                const domain = url.hostname;

                // Extract TLD (e.g., .com, .co.uk)
                const tldMatch = domain.match(/\.[^.]+(\.[^.]+)?$/);
                const tld = tldMatch ? tldMatch[0].substring(1) : null;

                // Check if TLD corresponds to target location
                if (
                  tld &&
                  COUNTRY_TLD_MAP.tldToCountry[tld] &&
                  COUNTRY_TLD_MAP.tldToCountry[tld].toLowerCase() ===
                    affiliateTargetLocation.toLowerCase()
                ) {
                  locationBoost = 0.1; // 10% boost for location match
                  this.logger.debug(
                    `Location boost applied for ${product.name}: TLD ${tld} matches ${affiliateTargetLocation}`,
                  );
                }

                // Also check product name for location mentions
                const nameAndDesc = `${product.name}`.toLowerCase();
                if (nameAndDesc.includes(affiliateTargetLocation.toLowerCase())) {
                  locationBoost = Math.max(locationBoost, 0.05); // Smaller boost for text mention
                }
              } catch (error) {
                this.logger.error(`Error parsing URL for location matching: ${error.message}`);
              }
            }

            // Apply boosts but ensure confidence doesn't exceed 1.0
            const adjustedConfidence = Math.min(match.confidence + locationBoost + priceBoost, 1.0);

            return {
              product,
              confidence: adjustedConfidence,
              reasoning: `${reasoning}${locationBoost > 0 ? ' (Location match bonus applied)' : ''}${
                priceBoost > 0 ? ' (Price consideration applied)' : ''
              }`,
            };
          });

        // Sort by adjusted confidence score
        const sortedMatches = allMatches.sort((a, b) => b.confidence - a.confidence);

        // Log all matches for debugging
        await this.logMatches('llm', keyword, sortedMatches);

        // Return only the requested number of matches that meet the confidence threshold
        return sortedMatches.filter((match) => match.confidence >= 0.5).slice(0, limit);
      }

      return [];
    } catch (error) {
      this.logger.error('Error finding top product matches using LLM', error);
      throw error;
    }
  }

  private deduplicateMatches(matches: ProductMatch[]): ProductMatch[] {
    this.logger.debug(`Deduplicating ${matches.length} matches`);
    const seen = new Map<string, ProductMatch>();

    matches
      .filter((match) => {
        // First filter out any invalid matches
        if (!match?.product?.id || !match?.product?.name || !match?.product?.url) {
          this.logger.debug('Skipping invalid product match:', match);
          return false;
        }
        // Filter out low confidence matches
        if (match.confidence < 0.5) {
          this.logger.debug(
            `Skipping low confidence match (${match.confidence}) for product: ${match.product.name}`,
          );
          return false;
        }
        return true;
      })
      .forEach((match) => {
        const existingMatch = seen.get(match.product.id);
        if (!existingMatch || match.confidence > existingMatch.confidence) {
          seen.set(match.product.id, match);
          if (existingMatch) {
            this.logger.debug(
              `Replaced product "${match.product.name}" (${existingMatch.confidence} -> ${match.confidence})`,
            );
          }
        }
      });

    const uniqueMatches = Array.from(seen.values()).sort((a, b) => b.confidence - a.confidence);

    this.logger.debug(
      `Deduplication complete: ${matches.length} matches -> ${uniqueMatches.length} unique matches`,
    );

    return uniqueMatches;
  }

  async findBestAffiliateProduct({
    keyword,
    products,
    method = 'semantic',
    language,
  }: FindBestAffiliateProductParams): Promise<ImpactProduct | null> {
    // Only call semantic matching if method is semantic
    if (method === 'semantic') {
      return this.getAffiliateProductUsingSemanticMatching({ keyword, products });
    }

    // For LLM method, directly use LLM without generating embeddings
    if (method === 'llm' && products.length > 0) {
      return this.getAffiliateProductUsingLLM({ keyword, products, language });
    }

    return null;
  }

  async getAffiliateProductUsingSemanticMatching({
    keyword,
    products,
  }: GetAffiliateProductParams): Promise<ImpactProduct | null> {
    const { keywordEmbedding, productEmbeddings } = await this.generateEmbeddings({
      keyword,
      products,
    });

    const bestMatch = this.findBestMatch(
      keywordEmbedding.embeddings[0].embedding,
      productEmbeddings.embeddings,
    );

    return bestMatch ? products[bestMatch.index] : null;
  }

  private async generateEmbeddings({ keyword, products }: GenerateEmbeddingsParams) {
    this.logger.debug(
      `Generating embeddings for keyword "${keyword}" and ${products.length} products`,
    );

    // Check cache for keyword embedding
    const cacheKey = `keyword_${keyword}`;
    let keywordEmbedding = this.embeddingCache.get(cacheKey);

    if (!keywordEmbedding) {
      keywordEmbedding = await this.embeddingProvider.embed({
        texts: [keyword],
        model: 'text-embedding-3-small',
      });
      this.embeddingCache.set(cacheKey, {
        data: keywordEmbedding,
        timestamp: Date.now(),
      });
    }

    // Generate product embeddings in batches
    const batchSize = 20;
    const productBatches = this.chunkArray(products, batchSize);
    const allProductEmbeddings = [];

    for (const batch of productBatches) {
      const embeddings = await this.embeddingProvider.embed({
        texts: batch.map((product) => product.name),
        model: 'text-embedding-3-small',
      });
      allProductEmbeddings.push(...embeddings.embeddings);

      // Small delay between batches to respect rate limits
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return {
      keywordEmbedding,
      productEmbeddings: { embeddings: allProductEmbeddings },
    };
  }

  async getAffiliateProductUsingLLM({
    keyword,
    products = [],
    language,
  }: GetAffiliateProductParams): Promise<ImpactProduct | null> {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    // Create a stable array for product indexing
    const productArray = [...products];

    const systemPrompt = `You are an AI assistant specialized in matching product keywords with affiliate products.
    Consider product categories and pricing in your matching process.`;

    const userPrompt = `Find the best matching product for the keyword "${keyword}" from the given list of products.
    Consider:
    1. Language matching (keyword and product should be in ${language || 'any'} language)
    2. Semantic relevance between keyword and product name/description
    3. Product relevance to the keyword context
    4. Category alignment
    5. Currency appropriateness

    Products: ${JSON.stringify(
      productArray.map((p, index) => ({
        index, // Include index in the product info
        name: p.name,
        price: p.currentPrice,
        currency: p.currency,
        category: p.category || 'Uncategorized',
        subCategory: p.subCategory || '',
      })),
    )}`;

    const ProductMatchSchema = Type.Object({
      productIndex: Type.Number(),
      confidence: Type.Number(),
      reasoning: Type.String(),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'find_best_product_match',
            description: 'Find the best matching product considering language and currency',
            parameters: ProductMatchSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls && response.message.toolCalls.length > 0) {
        const matchData: any = response.message.toolCalls[0].args;

        // Validate index before returning
        if (matchData.productIndex >= 0 && matchData.productIndex < productArray.length) {
          return productArray[matchData.productIndex];
        } else {
          this.logger.error(
            `Invalid product index returned: ${matchData.productIndex}, max valid index: ${
              productArray.length - 1
            }`,
          );
          return null;
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Error finding best product match using LLM', error);
      throw error;
    }
  }

  private findBestMatch(keywordEmbedding: number[], productEmbeddings: { embedding: number[] }[]) {
    let bestMatch = null;
    let highestSimilarity = -Infinity;

    productEmbeddings.forEach((productEmbedding, index) => {
      const similarity = this.cosineSimilarity(keywordEmbedding, productEmbedding.embedding);
      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = { index, similarity };
      }
    });

    return bestMatch && bestMatch.similarity > 0.7 ? bestMatch : null;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    const dotProduct = a.reduce((sum, _, i) => sum + a[i] * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  // Clean up expired cache entries periodically
  @Cron('0 * * * *') // Run every hour
  private cleanupCache() {
    const now = Date.now();
    for (const [key, value] of this.embeddingCache.entries()) {
      if (now - value.timestamp > this.cacheTTL) {
        this.embeddingCache.delete(key);
      }
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private async logMatches(
    method: 'semantic' | 'llm',
    keyword: string,
    matches: ProductMatch[],
  ): Promise<void> {
    // Only proceed if we're in DEBUG mode
    if (process.env.NODE_ENV !== 'debug') {
      return;
    }

    try {
      const debugDir = path.join(process.cwd(), 'debug', 'affiliate-matching');
      await fs.mkdir(debugDir, { recursive: true });

      if (!matches || matches.length === 0) {
        return;
      }

      const matchesToLog = matches
        .slice(0, 5)
        .map((match) => {
          if (!match.product) {
            return null;
          }

          return {
            product: {
              name: match.product.name,
              price: match.product.currentPrice,
              currency: match.product.currency,
              category: match.product.category || 'Uncategorized',
              subCategory: match.product.subCategory || '',
            },
            confidence: match.confidence,
            reasoning: match.reasoning,
          };
        })
        .filter(Boolean);

      if (matchesToLog.length > 0) {
        const filename = path.join(
          debugDir,
          `${keyword.replace(/[^a-z0-9]/gi, '_')}_${method}_matches.json`,
        );

        await fs.writeFile(
          filename,
          JSON.stringify(
            {
              keyword,
              matchingMethod: method,
              timestamp: new Date().toISOString(),
              topMatches: matchesToLog,
            },
            null,
            2,
          ),
          'utf8',
        );
      }
    } catch (error) {
      this.logger.error(`Failed to log ${method} matches for keyword "${keyword}"`, error);
    }
  }
}
