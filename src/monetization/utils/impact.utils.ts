import { ImpactProduct } from '../interfaces/impact-product.interface';

export function mapImpactProductResponse(response: any): ImpactProduct {
  // Extract mediaPartnerId from URI
  const mediaPartnerId = response.Uri.split('/')[2];

  return {
    id: response.Id,
    catalogId: response.CatalogId,
    campaignId: response.CampaignId,
    catalogItemId: response.CatalogItemId,
    productItemGroupId: response.ItemGroupId,
    name: response.Name,
    url: response.Url,
    imageUrl: response.ImageUrl,
    currentPrice: parseFloat(response.CurrentPrice),
    originalPrice: parseFloat(response.OriginalPrice),
    currency: response.Currency,
    launchDate: response.LaunchDate ? new Date(response.LaunchDate) : null,
    expirationDate: response.ExpirationDate ? new Date(response.ExpirationDate) : null,
    category: response.Category,
    subCategory: response.SubCategory,
    mediaPartnerId,
  };
}
