export function generateTrackingLink(bid: string, blogId: string, affiliateLink: string): string {
  const protocol = process.env.NODE_ENV === 'dev' ? 'http' : 'https';
  const trackingDomain = process.env.TRACKING_DOMAIN?.includes('http')
    ? process.env.TRACKING_DOMAIN
    : `${protocol}://${process.env.TRACKING_DOMAIN}`;

  return `${trackingDomain}?utm_source=${bid}&utm_medium=${blogId}&ref=${affiliateLink}`;
}
