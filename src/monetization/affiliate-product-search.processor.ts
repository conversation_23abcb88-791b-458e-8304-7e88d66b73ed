import { Processor, Process, InjectQueue } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { BaseProcessor } from '@common/queue/base.processor';
import { JOB_QUEUES } from '@/common/constants';
import { RATE_LIMITS } from '@/common/configs/rate-limits.config';
import { BlogService } from '@/blog/blog.service';
import { ImpactService } from './impact.service';
import { MonetizationService } from './monetization.service';
import { BlogQueuePayload, BlogAffiliateKeyword, Blog } from '@/blog/blog.model';
import { GatewayService } from '@/modules/gateway/gateway.service';
import * as fs from 'fs/promises';
import * as path from 'path';

@Processor(JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH)
export class AffiliateProductSearchProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH;
  protected readonly logger = new Logger(AffiliateProductSearchProcessor.name);
  private readonly MAX_SEARCH_RETRIES = 3;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_LINKS) private generateLinksQueue: Queue,
    @InjectQueue(JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH) affiliateProductSearchQueue: Queue,
    private readonly impactService: ImpactService,
    private readonly monetizationService: MonetizationService,
    private readonly blogService: BlogService,
    private readonly gatewayService: GatewayService,
  ) {
    super(affiliateProductSearchQueue);
  }

  @Process({ concurrency: RATE_LIMITS.IMPACT_API.MAX_CONCURRENT_REQUESTS })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const {
        blogId,
        bid,
        uid,
        blogLanguage,
        affiliateKeywords,
        identifier,
        blogTitle,
        affiliateTargetLocation,
      } = job.data;
      const startTime = Date.now();

      // Only create debug directory if in DEBUG mode
      let debugDir: string | undefined;
      if (process.env.NODE_ENV === 'debug') {
        debugDir = path.join(process.cwd(), 'debug', 'affiliate-matching');
        await fs.mkdir(debugDir, { recursive: true });
      }

      if (!affiliateKeywords || affiliateKeywords.length === 0) {
        this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
          _id: blogId,
          identifier,
          affiliateLinkGenerationStatus: 'affiliate_link_generated',
          percentComplete: 100,
          generationStatus: 'No keywords found for affiliate product matching',
        });
        return;
      }

      // Send initial status update
      this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
        _id: blogId,
        identifier,
        affiliateLinkGenerationStatus: 'affiliate_product_matching',
        percentComplete: 20, // Starting at 30% as per your progressMap
      });

      // Set initial status to in-progress
      await this.blogService.update(bid, blogId, {
        affiliateLinkGenerationStatus: 'affiliate_product_matching',
        generationStatus: 'Starting affiliate product matching...',
      } as Partial<Blog>);

      // Calculate progress percentages based on number of keywords
      const totalKeywords = affiliateKeywords.length;
      // Calculate the percentage range we have to distribute between 30% and 100%
      const progressRange = 80; // 100% - 30%
      const progressPerKeyword = progressRange / (totalKeywords > 0 ? totalKeywords : 1);

      const updatedKeywords: BlogAffiliateKeyword[] = [];
      let consecutiveErrors = 0;
      const MAX_CONSECUTIVE_ERRORS = 3;
      const ERROR_BACKOFF_MS = 3000;

      // Process keywords one-by-one instead of in batches
      for (let i = 0; i < affiliateKeywords.length; i++) {
        const keyword = affiliateKeywords[i];

        try {
          // Calculate current progress - base 30% + progress for completed keywords
          const baseProgress = 20;
          const completedProgress = i * progressPerKeyword;
          const currentProgress = Math.min(Math.round(baseProgress + completedProgress), 99); // Cap at 99% until fully complete

          // Update progress status for this keyword
          const progressStatus = `Processing keyword ${i + 1}/${totalKeywords}: "${keyword.keyword}"`;

          await this.blogService.update(bid, blogId, {
            generationStatus: progressStatus,
          } as Partial<Blog>);

          // Send progress update through gateway
          this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
            _id: blogId,
            identifier,
            affiliateLinkGenerationStatus: 'affiliate_product_matching',
            percentComplete: currentProgress,
          });

          const searchTerms = keyword.searchTerms || [keyword.keyword];
          this.logger.debug(
            `${blogId} Processing keyword: "${
              keyword.keyword
            }" with search terms: ${searchTerms.join(', ')}`,
          );

          // Search products for each search term - controlled sequentially to manage API rate limits
          const allSearchResults = [];
          for (const searchTerm of searchTerms) {
            try {
              // Update status to show which search term is being processed
              await this.blogService.update(bid, blogId, {
                generationStatus: `Processing search term: "${searchTerm}" for keyword "${keyword.keyword}" (${i + 1}/${totalKeywords})`,
              } as Partial<Blog>);

              const results = await this.impactService.searchProducts(searchTerm);
              allSearchResults.push(results);
              // delay between search terms
              await new Promise((resolve) => setTimeout(resolve, 100));
            } catch (error) {
              this.logger.error(
                `Error searching for term "${searchTerm}": ${error.message}`,
                error.stack,
              );
            }
          }

          // Combine and deduplicate products by ID
          const uniqueProducts = new Map();
          allSearchResults.flat().forEach((product) => {
            if (product?.id) {
              uniqueProducts.set(product.id, product);
            }
          });
          const searchResults = Array.from(uniqueProducts.values());

          // Debug logging
          if (process.env.NODE_ENV === 'debug' && debugDir) {
            await fs.writeFile(
              path.join(debugDir, `${blogId}_${keyword.keyword}_search_results.json`),
              JSON.stringify(
                {
                  searchTerms,
                  results: searchResults.map((product) => ({
                    name: product.name,
                    url: product.url,
                  })),
                },
                null,
                2,
              ),
              'utf-8',
            );
          }

          // Update status to show we're matching products for this keyword
          await this.blogService.update(bid, blogId, {
            generationStatus: `Finding best product match for "${keyword.keyword}" (${i + 1}/${totalKeywords})`,
          } as Partial<Blog>);

          // Send product matching status update
          this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
            _id: blogId,
            identifier,
            affiliateLinkGenerationStatus: 'affiliate_product_matched',
            percentComplete: Math.min(
              Math.round(baseProgress + completedProgress + progressPerKeyword * 0.5),
              99,
            ),
          });

          // Process search results and find matches
          const processedKeyword = await this.processKeywordSearchResults(
            keyword,
            searchResults,
            blogTitle,
            blogLanguage,
            affiliateTargetLocation,
          );

          updatedKeywords.push(processedKeyword);
          consecutiveErrors = 0; // Reset error counter on success

          // Send link generation status update
          this.gatewayService.sendBlogAffiliateLinkGenerationStatusUpdate(uid, {
            _id: blogId,
            identifier,
            affiliateLinkGenerationStatus: 'affiliate_link_generating',
            percentComplete: Math.min(
              Math.round(baseProgress + completedProgress + progressPerKeyword * 0.75),
              99,
            ),
          });

          // Respect rate limits between keywords
          await new Promise((resolve) =>
            setTimeout(
              resolve,
              RATE_LIMITS.IMPACT_API.BATCH_DELAY_MS / RATE_LIMITS.IMPACT_API.BATCH_SIZE,
            ),
          );
        } catch (error) {
          this.logger.error(
            `Error processing keyword "${keyword.keyword}": ${error.message}`,
            error.stack,
          );

          // Return keyword with failed status
          updatedKeywords.push({
            ...keyword,
            status: 'failed' as const,
            matchedAt: new Date(),
            replacementCount: 0,
          });

          consecutiveErrors++;

          // Simple backoff for consecutive errors
          if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
            this.logger.warn(
              `Multiple consecutive errors (${consecutiveErrors}), pausing for ${ERROR_BACKOFF_MS}ms`,
            );
            await new Promise((resolve) => setTimeout(resolve, ERROR_BACKOFF_MS));
            consecutiveErrors = 0; // Reset after backoff
          }
        }
      }

      // Update blog with results
      await this.blogService.update(bid, blogId, {
        affiliateKeywords: updatedKeywords,
        affiliateKeywordsUpdatedAt: new Date(),
        generationStatus: 'Affiliate product matching complete',
      } as Partial<Blog>);

      // Add matched keywords to generate links queue
      const matchedKeywords = updatedKeywords.filter((k) => k.status === 'matched');
      if (matchedKeywords.length > 0) {
        await this.generateLinksQueue.add({
          ...job.data,
          affiliateKeywords: matchedKeywords,
        });
      }

      // Log completion metrics
      const matchedCount = updatedKeywords.filter((k) => k.status === 'matched').length;
      const failedCount = updatedKeywords.filter((k) => k.status === 'failed').length;
      const noMatchCount = updatedKeywords.filter((k) => k.status === 'no_match').length;

      this.logger.debug(
        `Completed product search in ${Date.now() - startTime}ms. ` +
          `Matched: ${matchedCount}, Failed: ${failedCount}, No Match: ${noMatchCount}`,
      );
    });
  }

  private async processKeywordSearchResults(
    keyword: BlogAffiliateKeyword,
    searchResults: any[],
    blogTitle: string,
    blogLanguage: string,
    affiliateTargetLocation: string,
  ): Promise<BlogAffiliateKeyword> {
    // Filter out invalid products
    const validProducts = searchResults.filter((product) => {
      if (!product?.name || !product?.url || !product?.id) {
        this.logger.debug(
          `Skipping product due to missing required fields: ${JSON.stringify(product)}`,
        );
        return false;
      }
      return true;
    });

    if (!validProducts.length) {
      return {
        ...keyword,
        status: 'no_match' as const,
        matchedAt: new Date(),
        replacementCount: 0,
      };
    }

    const topMatches = await this.monetizationService.findTopMatches({
      keyword: keyword.keyword,
      blogTitle,
      products: validProducts,
      method: 'llm',
      language: blogLanguage,
      limit: 5,
      affiliateTargetLocation,
    });

    if (topMatches.length > 0) {
      const bestMatch = topMatches.reduce((best, current) => {
        if (!best || (current.confidence && current.confidence > best.confidence)) {
          return current;
        }
        return best;
      }, topMatches[0]);

      return this.createMatchedKeyword(keyword, bestMatch);
    }

    return {
      ...keyword,
      status: 'no_match' as const,
      matchedAt: new Date(),
      replacementCount: 0,
    };
  }

  private createMatchedKeyword(
    keyword: BlogAffiliateKeyword,
    bestMatch: any,
  ): BlogAffiliateKeyword {
    const product = bestMatch.product;

    return {
      ...keyword,
      // Product details - ensure all fields are mapped correctly
      productName: product.name,
      productId: product.id,
      productCatalogId: product.catalogId?.toString(),
      productCampaignId: product.campaignId?.toString(),
      productCatalogItemId: product.catalogItemId?.toString(),
      productImageUrl: product.imageUrl,
      productCurrentPrice:
        typeof product.currentPrice === 'string'
          ? parseFloat(product.currentPrice)
          : product.currentPrice,
      productOriginalPrice:
        typeof product.originalPrice === 'string'
          ? parseFloat(product.originalPrice)
          : product.originalPrice,
      productCurrency: product.currency,
      productCategory: product.category,
      productItemGroupId: product.productItemGroupId,
      // Affiliate and matching details
      affiliateLink: product.url,
      confidence: bestMatch.confidence,
      status: 'matched' as const,
      matchedAt: new Date(),
      replacementCount: 0,
      // Context
      searchTerms: keyword.searchTerms || [keyword.keyword],
      beforeWord: keyword.beforeWord || '',
      afterWord: keyword.afterWord || '',
    } satisfies BlogAffiliateKeyword;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
