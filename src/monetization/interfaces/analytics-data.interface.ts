import { Schema, SchemaFactory, Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';

interface EventData {
  platform?: Record<string, number>;
  ref?: string;
  vendor?: Record<string, number>;
  browser?: Record<string, number>;
  os?: Record<string, number>;
  country?: Record<string, number>;
  city?: Record<string, number>;
}

@Schema()
export class Analytics extends Document {
  @Prop({ required: true })
  combinedKey: string;

  @Prop({ required: true, index: true })
  bid: string;

  @Prop({ required: true, index: true })
  blogId: string;

  @Prop({ type: Object })
  eventsByDate: Record<string, EventData>;

  @Prop({ type: Object })
  geoInfo: {
    country: Record<string, number>;
    city: Record<string, number>;
  };

  @Prop({ type: Object })
  uaInfo: {
    platform: Record<string, number>;
    vendor: Record<string, number>;
    browser: Record<string, number>;
    os: Record<string, number>;
  };
}

export const AnalyticsSchema = SchemaFactory.createForClass(Analytics);

// Add indexes
AnalyticsSchema.index({ bid: 1, blogId: 1 });
AnalyticsSchema.index({ combinedKey: 1 }, { unique: true });
