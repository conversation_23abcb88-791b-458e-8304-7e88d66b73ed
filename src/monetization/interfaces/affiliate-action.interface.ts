export interface AffiliateAction {
  id: string;
  networkId: string; // Identifier in the affiliate network
  campaignId: string; // Campaign/Program ID
  campaignName: string;
  state: string; // Network-specific state
  amount: number; // Sale amount
  currency: string;
  commission: number; // Commission amount
  purchaseDate: Date; // When purchase occurred
  lockDate?: Date; // When conversion locks
  releaseDate?: Date; // When payment releases
  customerLocation?: {
    city?: string;
    region?: string;
    country?: string;
  };
  metadata: Record<string, any>; // Network-specific data
}
