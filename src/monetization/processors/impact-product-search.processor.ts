import { Processor, Process, InjectQueue, OnQueueFailed } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { JOB_QUEUES } from '@/common/constants';
import { ImpactService } from '../impact.service';
import { BaseProcessor } from '@common/queue/base.processor';

@Processor(JOB_QUEUES.IMPACT_PRODUCT_SEARCH)
export class ImpactProductSearchProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.IMPACT_PRODUCT_SEARCH;
  protected readonly logger = new Logger(ImpactProductSearchProcessor.name);
  private readonly MAX_SEARCH_RETRIES = 3;

  constructor(
    private readonly impactService: ImpactService,
    @InjectQueue(JOB_QUEUES.IMPACT_PRODUCT_SEARCH) impactProductSearchQueue: Queue,
  ) {
    super(impactProductSearchQueue);
  }

  @Process('search')
  protected async process(job: Job<{ keyword: string }>): Promise<any[]> {
    return this.processWithLock(job, async (job) => {
      this.logger.debug(`Processing Impact product search for keyword: ${job.data.keyword}`);
      return await this.impactService.executeSearch(job.data.keyword);
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<{ keyword: string }>, error: Error): Promise<void> {
    if (error.message.includes('429')) {
      await super.handleFailedJob(job, error);
      return;
    }

    if (job.attemptsMade < this.MAX_SEARCH_RETRIES) {
      this.logger.warn(
        `Impact product search attempt ${job.attemptsMade + 1}/${
          this.MAX_SEARCH_RETRIES
        } failed for keyword: ${job.data.keyword}. Retrying...`,
      );
      await super.handleFailedJob(job, error);
      return;
    }

    this.logger.error(
      `Impact product search failed permanently after ${this.MAX_SEARCH_RETRIES} attempts for keyword: ${job.data.keyword}`,
    );

    await job.moveToFailed(
      {
        message: `Failed after ${this.MAX_SEARCH_RETRIES} attempts: ${error.message}`,
      },
      true,
    );
  }
}
