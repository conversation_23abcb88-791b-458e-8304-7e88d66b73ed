import { Module } from '@nestjs/common';

import { AnalyticsModule } from '@/analytics/analytics.module';
import { SlackService } from '@/common/services/slack.service';
import { WalletModule } from '@/resources/wallet/wallet.module';
import { BlogModule } from '@/blog/blog.module';
import { S3Service } from '@/common/services/s3.service';
import { JobModule } from '@/job/job.module';

import { GenerateAffiliateKeywordsProcessor } from './generate-affiliate-keywords.processor';
import { AffiliateSaleMonitoringProcessor } from './affiliate-sale-monitoring.processor';
import { AffiliateProductSearchProcessor } from './affiliate-product-search.processor';
import { GenerateAffiliateLinksProcessor } from './generate-affiliate-links.processor';
import { ImpactProductSearchProcessor } from './processors/impact-product-search.processor';
import { AffiliateLinkTrackingModule } from './affiliate-link-tracking/affiliate-link-tracking.module';
import { AffiliateNetworkService } from './interfaces/affiliate-network-service.abstract';
import { ImpactNetworkService } from './providers/impact/impact.service';
import { MonetizationService } from './monetization.service';
import { ImpactService } from './impact.service';

@Module({
  providers: [
    { provide: AffiliateNetworkService, useClass: ImpactNetworkService },
    GenerateAffiliateKeywordsProcessor,
    AffiliateSaleMonitoringProcessor,
    AffiliateProductSearchProcessor,
    GenerateAffiliateLinksProcessor,
    ImpactProductSearchProcessor,
    MonetizationService,
    ImpactService,
    SlackService,
    S3Service,
  ],
  controllers: [],
  imports: [AffiliateLinkTrackingModule, AnalyticsModule, WalletModule, BlogModule, JobModule],
  exports: [ImpactService],
})
export class MonetizationModule {}
