import type { OnApplicationShutdown } from '@nestjs/common';
import type { Queue } from 'bull';

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';

import { JOB_QUEUES } from '@common/constants';

@Injectable()
export class AppShutdownService implements OnApplicationShutdown {
  private readonly logger = new Logger(AppShutdownService.name);

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY)
    private readonly generateTranscriptSummaryQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS)
    private readonly generateAffiliateKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH)
    private readonly affiliateProductSearchQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_LINKS)
    private readonly generateAffiliateLinksQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT)
    private readonly searchRelevantContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_KEYWORDS)
    private readonly generateBlogKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_OUTLINE)
    private readonly generateBlogOutlineQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT)
    private readonly generateBlogContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_ANALYZE_KEYWORD)
    private readonly seoAnalyzeKeywordQueue: Queue,
    @InjectQueue(JOB_QUEUES.TRANSCRIBE_MEDIA)
    private readonly transcribeMediaQueue: Queue,
    @InjectQueue(JOB_QUEUES.PUBLISH_SOCIAL)
    private readonly publishSocialQueue: Queue,
    @InjectQueue(JOB_QUEUES.BLOG_REQUEST)
    private readonly blogRequestQueue: Queue,
    @InjectQueue(JOB_QUEUES.PUBLISH_BLOG)
    private readonly publishBlogQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SCORING)
    private readonly seoScoringQueue: Queue,
  ) {}

  async onApplicationShutdown(signal?: string) {
    this.logger.log({ signal }, 'Server is shutting down...');

    await this.blogRequestQueue.close();
    await this.generateTranscriptSummaryQueue.close();
    await this.generateBlogOutlineQueue.close();
    await this.generateBlogContentQueue.close();
    await this.generateBlogKeywordsQueue.close();
    await this.generateAffiliateKeywordsQueue.close();
    await this.affiliateProductSearchQueue.close();
    await this.generateAffiliateLinksQueue.close();
    await this.publishBlogQueue.close();
    await this.publishSocialQueue.close();
    await this.transcribeMediaQueue.close();
    await this.searchRelevantContentQueue.close();
    await this.seoAnalyzeKeywordQueue.close();
    await this.seoScoringQueue.close();

    this.logger.log({ signal }, 'Server shut down complete.');
  }
}
