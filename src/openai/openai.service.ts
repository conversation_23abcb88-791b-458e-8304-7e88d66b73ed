import type { Blog } from '@blog/blog.model';

import { Injectable, Logger } from '@nestjs/common';
import { decode, encode } from 'gpt-3-encoder';
import { ConfigService } from '@nestjs/config';
import { Type } from '@sinclair/typebox';
import pRetry from 'p-retry';

import { KEYWORDS_EXTRACTION_PROMPT, BUFFER_TOKEN_LIMIT } from '@/common/constants';
import { createChunks, countTokens, delay } from '@/common/helpers';
import { OpenAiProvider, Message } from '@/llm';

enum GPT3Model {
  CHAT_GPT_4 = 'gpt-4o',
  CHAT_GPT_4_MINI = 'gpt-4o-mini',
  TEXT_DAVINCI_003 = 'text-davinci-003',
  TEXT_DAVINCI_002 = 'text-davinci-002',
  TEXT_CURIE_001 = 'text-curie-001',
  TEXT_BABBAGE_001 = 'text-babbage-001',
  TEXT_ADA_001 = 'text-ada-001',
}

@Injectable()
export class OpenaiService {
  private readonly logger = new Logger(OpenaiService.name);
  private readonly openAiProvider: OpenAiProvider;
  private blogGPTtokens: Record<string, any>;
  private blogChatML: Record<string, any>;

  constructor(private readonly configService: ConfigService) {
    this.openAiProvider = new OpenAiProvider({
      apiKey: this.configService.get('OPENAI_API_KEY'),
    });
    this.blogGPTtokens = {};
    this.blogChatML = {};
  }

  async generateSocialMediaContentFromPrompt(
    blogId: string,
    blogContent: string,
    language: string,
    platform: string,
    link: string,
    options: { include: { emoji: boolean; hashtag: boolean } },
  ): Promise<string> {
    try {
      if (!this.blogChatML[blogId]) {
        this.blogChatML[blogId] = [
          {
            role: 'assistant',
            content: `The blog post is ready:
              """
              ${blogContent}
              """`,
          },
        ];
      }

      const characterLimit = ['twitter', 'x'].includes(platform) ? 270 : 1200;
      const platformMaximumLength = `Limit the text within ${characterLimit} character`;
      const readPostPrompt = link
        ? `Use your creativity to craft a post that will capture the attention of our followers and encourage them to read the full blog at ${link}`
        : ``;
      const promptToWritePost = `Write an engaging social media post  in ${language} to be published on ${platform}.
      strictly follow the following guidelines:
      - Do ${options.include.emoji === false ? 'not' : ''} use emojis
      - Do ${options.include.hashtag === false ? 'not' : ''} generate hashtags
      ${platformMaximumLength} ${readPostPrompt}`;
      const socialMediaPost = await this.generateChatResponse(blogId, promptToWritePost);

      this.logger.debug(`generated social media content from openai`);
      return socialMediaPost;
    } catch (error) {
      this.logger.error(
        `Error generating social media content`,
        error?.response?.data?.error?.message,
      );
      throw error;
    }
  }

  private async generateChatResponse(blogId: string, prompt: string) {
    try {
      this.blogChatML[blogId] = this.blogChatML[blogId] || [];
      this.blogChatML[blogId].push({ role: 'user', content: prompt });

      const tokensInPrompt = this.countTokens(prompt);
      const completionLimit = 2000;
      const tokensUsed = this.blogChatML[blogId].reduce(
        (acc, curr) => acc + this.countTokens(curr.content),
        0,
      );
      this.logger.log('tokensUsed', tokensUsed);
      this.logger.log('tokensInPrompt', tokensInPrompt);

      const response = await this.openAiProvider.chatCompletion({
        model:
          tokensInPrompt + tokensUsed + (this.blogGPTtokens[blogId] || 0) + BUFFER_TOKEN_LIMIT <
          completionLimit
            ? GPT3Model.CHAT_GPT_4_MINI
            : GPT3Model.CHAT_GPT_4_MINI,
        messages: [...this.blogChatML[blogId], { role: 'user', content: prompt }],
        systemPrompt: 'You are a helpful assistant and a professional blogger.',
      });

      const chatResponse = response?.message;
      this.blogChatML[blogId] = this.blogChatML[blogId] || [];
      this.blogChatML[blogId].push(chatResponse);
      const usage = response?.usage;
      this.logger.debug(`usage of tokens for blog ${blogId}`, usage);
      this.blogGPTtokens[blogId] = this.blogGPTtokens[blogId]
        ? this.blogGPTtokens[blogId] + usage.inputTokens + usage.outputTokens
        : usage.inputTokens + usage.outputTokens;

      return chatResponse?.content;
    } catch (error) {
      this.logger.error(`Error generating chat response`, error?.response?.data?.error?.message);
      throw error;
    }
  }

  async generateKeywords(blogId: string, content: string): Promise<string[]> {
    this.logger.log({
      blogId,
      contentToken: countTokens(content),
    });

    try {
      const maxChunkTokenLimit = 10000;

      const contentChunks = createChunks(content, maxChunkTokenLimit);

      const keywords = [];

      const promises = [];
      contentChunks.forEach((chunk) => {
        promises.push(this.generateKeywordsForContent(blogId, chunk));
        promises.push(delay(1000));
      });

      await Promise.all(promises).then((generatedKeywords) => {
        if (Array.isArray(generatedKeywords)) {
          keywords.concat(generatedKeywords);
        } else {
          keywords.push(generatedKeywords);
        }
      });

      return [...new Set(keywords)];
    } catch (error) {
      this.logger.error(`Error generating keywords by chunk`);
      this.logger.error(error?.message);

      const keywords = await this.generateKeywordsForContent(
        blogId,
        content,
        GPT3Model.CHAT_GPT_4_MINI,
      );

      return keywords;
    }
  }

  async generateKeywordsForContent(
    blogId: string,
    content: string,
    model = GPT3Model.CHAT_GPT_4_MINI,
  ): Promise<string[]> {
    this.logger.log({ blogId, contentToken: this.countTokens(content) });
    const ExtractKeywordsParamsSchema = Type.Object({
      keywords: Type.String({
        description: 'The content of the blog post from which to extract keywords',
      }),
    });
    try {
      const response = await this.openAiProvider.chatCompletion({
        model,
        messages: [
          { role: 'assistant', content },
          { role: 'user', content: KEYWORDS_EXTRACTION_PROMPT },
        ],
        tools: [
          {
            name: 'extract_keywords',
            description: 'Extract keywords from the given content',
            parameters: ExtractKeywordsParamsSchema,
          },
        ],
        systemPrompt:
          'You are a helpful assistant and you never give up and answer based on your existing knowledge.',
      });

      const keywords = response.message.toolCalls[0].args.keywords;
      return keywords as string[];
    } catch (error) {
      this.logger.error(`Error generating keywords`, error?.message);
      this.logger.error(error?.response?.data?.error?.message);
      return [];
    }
  }

  async matchKeywordsWithHashtags(
    blogId: string,
    keywords: string[],
    categories: string[],
  ): Promise<Record<string, string>> {
    const retryOptions = {
      retries: 3, // Number of retries
      minTimeout: 5000, // Minimum timeout between retries in milliseconds
      maxTimeout: 50000, // Maximum timeout between retries in milliseconds
    };

    try {
      const result = await pRetry(async () => {
        const messages: Message[] = [
          {
            role: 'assistant',
            content: `Your are given the following List of keywords delimited by triple quotes: """${keywords}"""`,
          },
          {
            role: 'assistant',
            content: `And Your are given the following List of categories delimited by ---: ---${categories}---`,
          },
          {
            role: 'user',
            content: `
              Match all the keywords with it corresponding where the meaning of a keyword must match the meaning of a category within the list. Please try to use make sure to strictly adhere to the given list of categories and avoid any unrelated or fabricated mappings and if no match found use empty string. Try to map with  distinct categories and avoid mapping with the same category multiple times.
              Here is category list:
              ${categories}
              Here is the keyword list:
              Output JSON object of key-value pairs where the key is the keyword and the value is mapped the category`,
          },
        ];

        const response = await this.openAiProvider.chatCompletion({
          model: GPT3Model.CHAT_GPT_4_MINI,
          messages,
          systemPrompt: 'You are a data science engineer who can match keywords with categories.',
          opts: {
            max_tokens: 500,
            temperature: 0.2,
          },
        });

        const keywordMap = JSON.parse(response.message.content);
        return keywordMap;
      }, retryOptions);

      return result;
    } catch (error) {
      this.logger.error(
        `Error matching keywords with hashtags for blog post ${blogId}: ${error?.response?.data?.error?.message}`,
      );
      // throw error;
    }
  }

  countTokens(text: string): number {
    const encoded = encode(text);
    return encoded.length;
  }

  truncateText(text: string, maxTokens: number): string {
    const encoded = encode(text);

    if (encoded.length > maxTokens) {
      const truncatedEncoded = encoded?.slice(0, maxTokens);
      return decode(truncatedEncoded);
    } else {
      return text;
    }
  }

  async generateSummary(
    blogId: string,
    prompt: string,
    isChunk = false,
    pov = 'First Person',
    blogSize = 'medium',
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateSummary');
    try {
      if (this.countTokens(prompt) < 1000 && !isChunk && pov === 'First Person') {
        return prompt;
      }

      if (this.countTokens(prompt) > 12000) {
        prompt = this.truncateText(prompt, 12000);
      }

      const summarizeThisVideo =
        pov === 'First Person' ? `Summarize My video transcript` : `Summarize the video transcript`;
      const finalInstruction = `\n\nMust follow the following rules strictly:
      1. Summarize in ${pov} perspective
      2. Exclude all forms of repetitive and non-essential information
      3. Avoid introductory phrases for directness
      4. Follow the original order of the text
      5. Do not mention any filler words used in a video or podcast: ### video transcript, in this video, in this podcast, hello there, Hey there, it's great to have you back, great to have you back, Hello there, welcome back, Hello there, welcome back to another episode or hi there ### which is used in the video or audio but not in blogs
      6. Summarize in maximum 500 words
      `;
      let summaryInstruction = ``;
      if (blogSize === 'small') {
        summaryInstruction = `${summarizeThisVideo} below delimited by triple quotes: """${prompt}""" in 4 paragraphs with line breaks. ${finalInstruction}`;
      } else if (blogSize === 'medium') {
        summaryInstruction = `${summarizeThisVideo} below delimited by triple quotes: """${prompt}""" in 4 paragraphs with line breaks. ${finalInstruction}`;
      } else {
        summaryInstruction = `${summarizeThisVideo} below delimited by triple quotes: """${prompt}"""  in 4 paragraphs with line breaks. ${finalInstruction}`;
      }
      const response = await this.openAiProvider.chatCompletion({
        model: GPT3Model.CHAT_GPT_4_MINI,
        messages: [
          {
            role: 'user',
            content: summaryInstruction,
          },
        ],
        systemPrompt: 'You are a professional blog writer with expertise in various niches.',
        opts: { temperature: 0.2 },
      });

      const usage = response?.usage;
      this.logger.debug(
        `usage of tokens for summary in ${
          this.countTokens(prompt) < 2000 ? GPT3Model.CHAT_GPT_4_MINI : GPT3Model.CHAT_GPT_4_MINI
        } for blog ${blogId}`,
        usage,
      );
      const summary = response.message.content;
      return summary;
    } catch (error) {
      this.logger.error(`Error generating summary`, error?.response?.data?.error?.message);
      throw error;
    } finally {
      this.logMethodExecutionTime('generateSummary', invokeTime);
    }
  }

  async generateBlogOutlineJson(
    blogId: string,
    prompt: string,
    blogLanguage: string,
    blogSize: Blog['blogSize'],
  ): Promise<Blog['blogOutline']> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogOutlineJson');
    try {
      return this.generateBlogOutlineInJsonFormat(blogId, prompt, blogLanguage, blogSize);
    } catch (error) {
      this.logger.error(
        `Error generating blog outline for blogId: ${blogId}`,
        error?.response?.data?.error?.message,
      );
      const outline = await this.generateBlogOutline(blogId, prompt, blogLanguage, blogSize);

      return this.generateBlogOutlineInJsonFormat(blogId, outline, blogLanguage, blogSize);
    } finally {
      this.logMethodExecutionTime('generateBlogOutlineJson', invokeTime);
    }
  }

  private async generateBlogOutlineInJsonFormat(
    blogId: string,
    prompt: string,
    blogLanguage: string,
    blogSize: Blog['blogSize'],
  ): Promise<Blog['blogOutline']> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogOutlineInJsonFormat');
    try {
      const blogOutlineInstruction = `Please Generate outline for the blog post post in ${blogLanguage} language\n
        The 'bulletPoints' field should have ${
          blogSize === 'small' ? 'maximum 2' : blogSize === 'medium' ? 'maximum 3' : 'maximum 5'
        } points each with at most 10 words and Heading field should not have any points indicating a sequence or order
      `;
      const model = GPT3Model.CHAT_GPT_4_MINI;
      const truncatedPrompt = this.truncateText(
        prompt,
        model === GPT3Model.CHAT_GPT_4_MINI ? 10000 : 2000,
      );
      const messages: Message[] = [
        {
          role: 'user',
          content: `Summary of my blog is delimited by triple quotes \n"""summary: ${truncatedPrompt}"""\n\n ${blogOutlineInstruction}`,
        },
      ];
      // Define the schema for a section
      const SectionSchema = Type.Object({
        heading: Type.String({
          description: 'The heading of the section',
        }),
        bulletPoints: Type.Array(
          Type.String({
            description: 'A bullet point in the section',
          }),
          {
            description: 'The bullet points under the section',
          },
        ),
      });

      // Define the schema for the blog outline
      const BlogOutlineSchema = Type.Object({
        title: Type.String({
          description: 'The title of the blog post',
        }),
        introduction: Type.String({
          description: 'The introduction of the blog post',
        }),
        sections: Type.Array(SectionSchema, {
          description: 'The sections of the blog post',
        }),
      });

      const response = await this.openAiProvider.chatCompletion({
        model,
        messages,
        tools: [
          {
            name: 'generate_blog_outline',
            description: 'Generate outline for the blog post from the given prompt',
            parameters: BlogOutlineSchema,
          },
        ],
        systemPrompt: 'You are a professional blog writer with expertise in various niches.',
        opts: { temperature: 0.2 },
      });
      const usage = response?.usage;
      this.logger.debug(`usage of tokens for blog outline ${blogId}`, usage);
      const blogOutline = response.message.toolCalls[0].args as Blog['blogOutline'];

      return blogOutline;
    } catch (error) {
      console.error(error);
      this.logger.error(
        `Error generating blog outline`,
        error?.response?.data?.error?.message || error?.message,
      );
      throw error;
    } finally {
      this.logMethodExecutionTime('generateBlogOutlineInJsonFormat', invokeTime);
    }
  }

  async generateMetaDescription(
    _blogId: string,
    prompt: Blog['blogOutline'] | string,
    blogLanguage: string,
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateMetaDescription');

    const retryOptions = {
      retries: 3,
      minTimeout: 5000,
      maxTimeout: 10000,
    };

    const retryFunction = async () => {
      const blogTitlePrompt = `Please generate a meta description for the following blog outline delimited by """ in ${blogLanguage} language
      """
      ${prompt}
      """
      `;
      const model = GPT3Model.CHAT_GPT_4_MINI;

      const response = await this.openAiProvider.chatCompletion({
        model,
        messages: [
          {
            role: 'user',
            content: blogTitlePrompt,
          },
        ],
        systemPrompt: 'You are a professional blog writer with expertise in various niches.',
        opts: { temperature: 0.2 },
      });

      return response.message.content;
    };

    const result = await pRetry(retryFunction, retryOptions);
    this.logMethodExecutionTime('generateMetaDescription', invokeTime);
    return result;
  }

  async generateBlogTitle(
    _blogId: string,
    prompt: Blog['blogOutline'] | string,
    blogLanguage: string,
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogTitle');

    const retryOptions = {
      retries: 3,
      minTimeout: 5000,
      maxTimeout: 10000,
    };

    const retryFunction = async () => {
      const blogTitlePrompt = `Please generate a single title for the following blog outline delimited by """ in ${blogLanguage} language
      """
      ${prompt}
      """
      `;
      const model =
        countTokens(prompt + blogTitlePrompt + BUFFER_TOKEN_LIMIT) > 2000
          ? GPT3Model.CHAT_GPT_4_MINI
          : GPT3Model.CHAT_GPT_4_MINI;

      const response = await this.openAiProvider.chatCompletion({
        model,
        messages: [
          {
            role: 'user',
            content: blogTitlePrompt,
          },
        ],
        systemPrompt: 'You are a professional blog writer with expertise in various niches.',
        opts: {
          temperature: 0.2,
        },
      });

      return response.message.content;
    };

    const result = await pRetry(retryFunction, retryOptions);
    this.logMethodExecutionTime('generateBlogTitle', invokeTime);
    return result;
  }

  private async generateBlogOutline(
    _blogId: string,
    prompt: string,
    blogLanguage: string,
    blogSize: Blog['blogSize'],
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogOutline');
    const retryOptions = {
      retries: 3,
      minTimeout: 5000,
      maxTimeout: 10000,
    };

    const retryFunction = async () => {
      const blogOutlineInstruction = `Please generate ${
        blogSize === 'small' ? 'a small' : blogSize === 'medium' ? 'a medium' : 'the'
      } outline for blog post in ${blogLanguage} language`;
      const model = GPT3Model.CHAT_GPT_4_MINI;

      const response = await this.openAiProvider.chatCompletion({
        model,
        messages: [
          {
            role: 'user',
            content: `${this.truncateText(
              prompt,
              model === GPT3Model.CHAT_GPT_4_MINI ? 10000 : 2000,
            )} ${blogOutlineInstruction}`,
          },
        ],
        systemPrompt: 'You are a professional blog writer with expertise in various niches.',
        opts: {
          temperature: 0.2,
        },
      });

      return response.message.content;
    };

    const result = await pRetry(retryFunction, retryOptions);
    this.logMethodExecutionTime('generateBlogOutline', invokeTime);
    return result;
  }

  async generateBlogSection(
    blogOutline: Blog['blogOutline']['sections'][0],
    prompt: string,
    blogTone?: Blog['blogTone'],
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogSection');

    const inputToken = countTokens(
      JSON.stringify(blogOutline, null, 2) + prompt + BUFFER_TOKEN_LIMIT,
    );

    const toneInstruction = blogTone
      ? `Use a ${blogTone} tone throughout the content.`
      : 'Use a professional and informative tone.';

    const response = await this.openAiProvider.chatCompletion({
      model: inputToken > 2000 ? GPT3Model.CHAT_GPT_4_MINI : GPT3Model.CHAT_GPT_4_MINI,
      messages: [{ role: 'user', content: prompt }],
      systemPrompt: `You are a helpful assistant and a professional blogger. You have the following blog outline in JSON delimeted by """
      """
      ${JSON.stringify(blogOutline, null, 2)}
      """
      You can write SEO friendly content for each blog section individually when user ask for it in the prompt.
      ${toneInstruction}
      `,
    });

    const output = response.message.content || '';
    this.logMethodExecutionTime('generateBlogSection', invokeTime);
    return output;
  }

  async rewriteBlogContent(
    blogContent: string,
    tone: Blog['blogTone'],
    pointOfView: Blog['pov'],
    inputText: string,
    isTranscript: boolean,
  ): Promise<string> {
    const invokeTime = Date.now();
    this.logMethodInvocation('generateBlogSection');

    const response = await this.openAiProvider.chatCompletion({
      model: GPT3Model.CHAT_GPT_4_MINI,
      messages: [
        {
          role: 'user',
          content: `Re-Write the following Blog delimited by triple back ticks
            """${blogContent}"""

            \n\n
            ------------------
            ${isTranscript ? 'Video Transcript' : 'Prompt'} from which the blog is generated from delimited by triple hashtags
            ###${inputText}###

            \n\n
            ------------------
            Blog Re-Writing Instructions:
            1. Re-write the blog content in ${tone} tone.
            2. Change the point of view to ${pointOfView}.
            3. Do not add multiple conclusions or introductions.
            4. Ensure the content is original, plagiarism-free, and tailored to the target audience
            5. Enhance readability with quotes, bullet points, and subheadings.
            6. Remove any incomplete html tags or markups
            7. Ensure the content is SEO optimized and includes relevant keywords.
            8. Make sure the content is reader-friendly and do not sounds like AI written content
            9. Make sure to keep all the H2 or Blog Headings as it is.
            10. ${
              isTranscript
                ? 'Make sure include all the key points and details from the video transcript the main purpose is to create a blog post from the video transcript.'
                : 'Make sure include all the key points and details from the Prompt'
            }
          `,
        },
      ],
      systemPrompt: `You are a professional blog writer with expertise
      in various niches including technology, lifestyle, travel, and education. You are
      adept at creating engaging, informative, and reader-friendly content that captivates
      audiences. Your skills include SEO optimization, incorporating keywords seamlessly
      into content, and understanding the latest trends in digital content creation.
      You excel in writing clear, concise introductions, detailed and structured main
      content, and impactful conclusions. You're known for your ability to weave compelling
      narratives, provide valuable insights, and encourage reader interaction through
      questions or calls-to-action. Ensure all content is original, plagiarism-free, and
      tailored to the target audience, enhancing readability with relevant images, bullet
      points, and subheadings. Your goal is to produce blogs that not only inform and
      entertain but also rank well on search engines and drive traffic.`,
    });

    const output = response.message.content || '';
    this.logMethodExecutionTime('rewriteBlogContent', invokeTime);
    return output;
  }

  private logMethodInvocation(methodName: string) {
    this.logger.log(`Invoking ${methodName}...`);
  }

  private logMethodExecutionTime(methodName: string, startTime: number) {
    const endTime = Date.now();
    const executionTime = endTime - startTime;
    this.logger.log(`${methodName} executed in ${executionTime} milliseconds.`);
  }
}
