import type { TrackEventDto } from '@/event/dto/track-event.dto';
import type { Model } from 'mongoose';

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { AffiliateLinkTracking } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.model';
import { AnalyticsService } from '@/analytics/analytics.service';
import { EventService } from '@/event/event.service';

@Injectable()
export class TrackingService {
  private readonly logger = new Logger(TrackingService.name);
  constructor(
    @InjectModel(AffiliateLinkTracking.name)
    private readonly affiliateLinkTrackingModel: Model<AffiliateLinkTracking>,
    private readonly analyticsService: AnalyticsService,
    private readonly eventService: EventService,
  ) {}

  async trackLink(
    {
      ref,
      type,

      business,
      website,
      blog,
    }: TrackEventDto,
    userAgent: string,
    ip: string,
  ): Promise<void> {
    const [userAgentInfo, geoInfo] = await this.eventService.trackEvent(
      { ref, type, business, website, blog },
      userAgent,
      ip,
    );

    if (type === 'click') {
      await this.affiliateLinkTrackingModel.updateOne(
        { affiliateLink: ref, bid: business, blogId: blog },
        {
          $inc: { clickCount: 1 },
          $set: { lastClickDate: new Date() },
        },
      );
    }

    try {
      await this.analyticsService.updateAnalytics(
        ref,
        business,
        blog,
        userAgentInfo,
        geoInfo,
        type,
      );
    } catch (error) {
      this.logger.error(`Error updating analytics: ${error?.message}`);
      this.logger.error(error);
    }
  }
}
