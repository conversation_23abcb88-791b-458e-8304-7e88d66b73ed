import { Module } from '@nestjs/common';

import { AffiliateLinkTrackingModule } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.module';
import { AnalyticsModule } from '@/analytics/analytics.module';
import { EventModule } from '@/event/event.module';

import { ThrottlerViewGuard } from './throttler-view.guard';
import { TrackingController } from './tracking.controller';
import { TrackingService } from './tracking.service';

@Module({
  imports: [AffiliateLinkTrackingModule, AnalyticsModule, EventModule],
  controllers: [TrackingController],
  providers: [ThrottlerViewGuard, TrackingService],
  exports: [TrackingService],
})
export class TrackingModule {}
