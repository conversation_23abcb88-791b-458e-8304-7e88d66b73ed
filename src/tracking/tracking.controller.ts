import type { Request, Response } from 'express';

import { <PERSON>, UseG<PERSON><PERSON>, Lo<PERSON>, Query, Get, Req, Res } from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';

import { EventType } from '@/event/event.enums';

import { ThrottlerViewGuard } from './throttler-view.guard';
import { TrackingService } from './tracking.service';

@Controller('track')
export class TrackingController {
  private readonly logger = new Logger(TrackingController.name);

  constructor(private readonly trackingService: TrackingService) {}

  @Get('url')
  @Throttle({ default: { limit: 6, ttl: 60 * 1000 } }) // 6 requests per minute
  @UseGuards(ThrottlerViewGuard) // Throttles only view events
  async trackLink(
    @Query('ref') ref: string,
    @Query('site_id') siteId: string,
    @Query('utm_source') bid: string,
    @Query('utm_medium') blogId: string,
    @Query('utm_content') eventType: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    /**
     * utm_source: blogging platform (wordpress, blogger, wix, medium, etc.)
     * utm_medium: affiliate network (rakutan, shareasale, cj, impact radius, etc.)
     * utm_campaign: brand name (amazon, walmart, target, etc.)
     * utm_term: category (watch, shoes, electronics, etc.)
     * ref: afflilate link (eg: https://click.linksynergy.com/fs-bin/click?id=/7KMM9AWdfA&offerid=1297430.3&type=3&subid=0)
     * example:
     * const trackingUrl = https://api.blogify.ai/url?utm_source=bid&utm_medium=blogId&ref=link
     * const replacement = `<a href="${trackingUrl}" target="_blank">${keyword}</a>`;
     */
    try {
      // Parse URL using BASE_URL as the base
      const url = new URL(req.protocol + '://' + req.get('host') + req.originalUrl);
      const refIndex = url.search.indexOf('ref=');
      if (refIndex !== -1) {
        const refParams = url.search.substring(refIndex + 4);
        ref = decodeURIComponent(refParams);
      }
      this.logger.log(`Tracking url: ref ${ref}`);

      const userAgent = req.headers['user-agent'];
      const ipAddress = req.headers['x-forwarded-for'] || req.ip || req.connection.remoteAddress;
      const ip = Array.isArray(ipAddress) ? ipAddress[0] : ipAddress;

      eventType = eventType && eventType === 'v' ? 'view' : 'click';

      await this.trackingService.trackLink(
        {
          ref,
          type: eventType as EventType,

          business: bid,
          website: siteId,
          blog: blogId,
        },
        userAgent,
        ip,
      );
      this.logger.log(`link tracked for bid: ${bid} link: ${ref} ${ip}`);

      if (eventType === 'view') {
        return res.send({ success: true });
      } else {
        // Use 302 Found instead of default 301 to prevent browser caching
        this.logger.log(`redirecting to ${ref} for bid: ${bid} blogId: ${blogId}`);
        return res.redirect(302, ref);
      }
    } catch (error) {
      this.logger.error(`Error tracking link: ${ref}`, error?.message);
      if (eventType === 'view') {
        return res.send({ error: error?.message });
      } else {
        // Use 302 Found for error case as well
        this.logger.log(`redirecting to ${ref} for bid: ${bid} blogId: ${blogId}`);
        return res.redirect(302, ref);
      }
    }
  }
}
