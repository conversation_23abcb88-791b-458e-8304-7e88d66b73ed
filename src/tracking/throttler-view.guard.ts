import { ThrottlerGuard, ThrottlerRequest } from '@nestjs/throttler';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ThrottlerViewGuard extends ThrottlerGuard {
  async handleRequest(requestProps: ThrottlerRequest) {
    const { context } = requestProps;
    const req = context.switchToHttp().getRequest();

    if (req.query.utm_content === 'v') {
      return super.handleRequest(requestProps);
    }

    return true;
  }
}
