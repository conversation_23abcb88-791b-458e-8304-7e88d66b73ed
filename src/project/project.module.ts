import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Project, ProjectSchema } from './project.model';
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { DataForSeoApiService } from '@/common/services/data-for-seo-api.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: Project.name, schema: ProjectSchema }])],
  controllers: [ProjectController],
  providers: [ProjectService, DataForSeoApiService],
  exports: [ProjectService, MongooseModule],
})
export class ProjectModule {}
