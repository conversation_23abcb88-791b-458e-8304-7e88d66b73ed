import { CreateProjectDto } from './dto/create-project.dto';
import { ProjectService } from './project.service';
import { Project } from './project.model';
import { UpdateProjectDto } from './dto/update-project.dto';
import { BaseController } from '@/resources/base/base.controller';

export class ProjectController extends BaseController<
  Project,
  typeof ProjectService,
  CreateProjectDto,
  UpdateProjectDto
>({
  name: 'Project',
  path: 'projects',
  service: ProjectService,
}) {}
