import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

import { BaseModel } from '@/resources/base/base.model';
import { Business } from '@/business/business.model';
import { DomainAnalytics } from '@/common/services/data-for-seo-api.service';

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'projects',
})
export class Project extends BaseModel {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  websiteUrl: string;

  @Prop({ type: Object })
  domainAnalytics?: DomainAnalytics;

  @Prop({ type: ObjectId, ref: 'Business' })
  business: Relation<Business>;
}

export type ProjectDocument = Project & Document;

export const ProjectSchema = SchemaFactory.createForClass(Project);
