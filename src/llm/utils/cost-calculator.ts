import { logger } from '@/common/logger';
import { ModelPricing } from '../llm.models';

/**
 * TokenUsage interface representing token usage data from LLM responses
 */
export interface TokenUsage {
  // Standard format
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;

  // Alternative formats (different providers use different naming)
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
}

/**
 * Cost calculation result
 */
export interface CostCalculationResult {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  inputCost: number;
  outputCost: number;
  totalCost: number;
}

/**
 * Calculate the cost of an LLM operation based on token usage, provider, and model
 *
 * @param usage Token usage data from LLM operation
 * @param provider The LLM provider (e.g., 'openai', 'anthropic')
 * @param model The specific model used
 * @param context Optional context string for logging (e.g., agent name)
 * @returns Cost calculation result containing token counts and costs
 */
export function calculateCost(
  usage: TokenUsage,
  provider: string,
  model: string,
  context = '',
): CostCalculationResult {
  usage = usage || {
    promptTokens: 0,
    completionTokens: 0,
    totalTokens: 0,
    prompt_tokens: 0,
    completion_tokens: 0,
    total_tokens: 0,
  };
  // Normalize provider name to lowercase
  const providerKey = provider.toLowerCase();

  // Extract token counts with fallbacks
  const promptTokens = usage.promptTokens || usage.prompt_tokens || 0;
  const completionTokens = usage.completionTokens || usage.completion_tokens || 0;
  const totalTokens = usage.totalTokens || usage.total_tokens || promptTokens + completionTokens;

  // For logging context
  const contextPrefix = context ? `${context} - ` : '';

  // Initialize rates
  let inputRate = 0;
  let outputRate = 0;

  // Debug log
  logger.debug(`${contextPrefix}Calculating cost for provider: ${providerKey}, model: ${model}`);

  // Handle known provider-specific pricing
  if (providerKey === 'anthropic') {
    if (model.includes('haiku') || model.includes('claude-3-5-haiku')) {
      // Claude 3.5 Haiku pricing: $0.8/M input, $4/M output
      inputRate = 0.8 / 1000000;
      outputRate = 4.0 / 1000000;
      logger.debug(
        `${contextPrefix}Using Claude 3.5 Haiku pricing: $${inputRate * 1000000}/M in, $${outputRate * 1000000}/M out`,
      );
    } else {
      // Standard Claude Sonnet pricing: $3/M input, $15/M output (default)
      inputRate = 3.0 / 1000000;
      outputRate = 15.0 / 1000000;
      logger.debug(
        `${contextPrefix}Using Claude Sonnet pricing: $${inputRate * 1000000}/M in, $${outputRate * 1000000}/M out`,
      );
    }
  } else if (providerKey === 'openai' && model.includes('gpt-4')) {
    // Standard GPT-4o pricing: $5/M input, $15/M output
    inputRate = 5.0 / 1000000;
    outputRate = 15.0 / 1000000;
    logger.debug(
      `${contextPrefix}Using GPT-4 pricing: $${inputRate * 1000000}/M in, $${outputRate * 1000000}/M out`,
    );
  } else {
    // Try to find pricing in the ModelPricing object
    if (ModelPricing[providerKey]) {
      logger.debug(
        `${contextPrefix}Available models for ${providerKey}: ${Object.keys(ModelPricing[providerKey]).join(', ')}`,
      );

      const pricingInfo = ModelPricing[providerKey]?.[model];

      if (pricingInfo) {
        inputRate = pricingInfo.pricing.inputTokensCost;
        outputRate = pricingInfo.pricing.outputTokensCost;
      } else {
        logger.warn(`${contextPrefix}No pricing information found for ${providerKey}/${model}`);
      }
    } else {
      logger.warn(`${contextPrefix}Provider not found in pricing data: ${providerKey}`);
    }
  }

  // Calculate costs
  const inputCost = promptTokens * inputRate;
  const outputCost = completionTokens * outputRate;
  const totalCost = inputCost + outputCost;

  // Log calculation details
  logger.debug(
    `${contextPrefix}Cost calculation: Input tokens: ${promptTokens}, Output tokens: ${completionTokens}`,
  );
  logger.debug(
    `${contextPrefix}Input cost: $${inputCost.toFixed(6)}, Output cost: $${outputCost.toFixed(6)}, Total: $${totalCost.toFixed(6)}`,
  );

  return {
    promptTokens,
    completionTokens,
    totalTokens,
    inputCost,
    outputCost,
    totalCost,
  };
}
