import OpenAI from 'openai';
import { estimateInputTokens, estimateOutputTokens } from '../tokenizer';
import type {
  ChatCompletionInput,
  Message,
  ModelDefinition,
  ModelResponse,
  PartialModelResponse,
  ToolCall,
} from '../types';
import { BaseProvider } from './base.provider';
import { LLMModels } from '../llm.models';

const providerName = 'OPENAI';
const models = Object.assign([], LLMModels.OPENAI) satisfies ModelDefinition[];

export type ModelName = (typeof models)[number]['name'] | (typeof models)[number]['alias'][number];

export class OpenAiProvider extends BaseProvider {
  static PROVIDER_NAME = providerName;
  private openai: OpenAI;
  constructor(options?: OpenAiProviderOptions) {
    super(providerName, models);
    this.openai = new OpenAI(options);
  }

  async chatCompletion(input: ChatCompletionInput<ModelName>): Promise<ModelResponse> {
    const response = await this.openai.chat.completions.create({
      ...toOpenAiChatCompletionCreateParams(input),
      stream: false,
    });

    const completionMessage = response.choices[0]?.message;
    if (!completionMessage) {
      throw new Error('no completion message');
    }

    return {
      message: {
        content: completionMessage.content ?? '',
        role: 'assistant',
        toolCalls: completionMessage.tool_calls?.map((toolCall) => ({
          id: toolCall.id,
          name: toolCall.function.name,
          args: toolCall.function.arguments ? JSON.parse(toolCall.function.arguments) : undefined,
        })),
      },
      usage: {
        inputTokens: response.usage?.prompt_tokens ?? 0,
        outputTokens: response.usage?.completion_tokens ?? 0,
      },
    };
  }

  async *chatCompletionStream(
    input: ChatCompletionInput<ModelName>,
  ): AsyncGenerator<PartialModelResponse, ModelResponse> {
    const stream = await this.openai.chat.completions.create({
      ...toOpenAiChatCompletionCreateParams(input),
      stream: true,
    });

    let content = '';
    let streamingToolCalls:
      | {
          id?: string;
          name?: string;
          argsText?: string;
        }[]
      | undefined;

    for await (const chunk of stream) {
      const completion = chunk.choices[0];

      if (completion.delta.tool_calls) {
        streamingToolCalls = streamingToolCalls || [];
        for (const deltaToolCall of completion.delta.tool_calls) {
          const streamingToolCall = (streamingToolCalls[deltaToolCall.index] =
            streamingToolCalls[deltaToolCall.index] || {});

          if (deltaToolCall.id) {
            streamingToolCall.id = deltaToolCall.id;
          }
          if (deltaToolCall.function?.name) {
            streamingToolCall.name ??= '';
            streamingToolCall.name += deltaToolCall.function.name;
          }
          if (deltaToolCall.function?.arguments) {
            streamingToolCall.argsText ??= '';
            streamingToolCall.argsText += deltaToolCall.function.arguments;
          }
        }
      }
      if (completion.delta.content) {
        content += completion.delta.content;

        yield {
          delta: {
            content: completion.delta.content,
            role: 'assistant',
          },
        };
      }
    }

    return {
      message: {
        content,
        role: 'assistant',
        toolCalls: streamingToolCalls
          ?.map((toolCall) => ({
            id: toolCall.id,
            name: toolCall.name,
            args: toolCall.argsText ? JSON.parse(toolCall.argsText) : undefined,
          }))
          .filter((toolCall): toolCall is ToolCall => !!toolCall.id && !!toolCall.name),
      },
      usage: {
        inputTokens: estimateInputTokens(input.messages),
        outputTokens: estimateOutputTokens(content),
      },
    };
  }
}

function toOpenAiChatCompletionCreateParams(
  input: ChatCompletionInput,
): OpenAI.Chat.ChatCompletionCreateParams {
  const tools = input.tools?.map((tool) => ({
    type: 'function',
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters || undefined,
    },
  }));

  const openAiInput = {
    model: input.model,
    messages: toOpenAiMessages(input.messages, input.systemPrompt),
    ...input.opts,
  };

  if (tools && tools.length) {
    Object.assign(openAiInput, { tools, tool_choice: 'required', parallel_tool_calls: false });
  }

  return openAiInput;
}

function toOpenAiMessages(
  messages: Message[],
  systemPrompt: string,
): OpenAI.Chat.ChatCompletionMessageParam[] {
  return [
    { role: 'system', content: systemPrompt },
    ...messages
      .map(
        (
          message,
        ): OpenAI.Chat.ChatCompletionMessageParam | OpenAI.Chat.ChatCompletionMessageParam[] => {
          if (message.role === 'assistant') {
            return {
              role: 'assistant',
              content: message.content,
              tool_calls: message.toolCalls?.map((toolCall) => ({
                type: 'function',
                id: toolCall.id,
                function: {
                  name: toolCall.name,
                  arguments: JSON.stringify(toolCall.args),
                },
              })),
            };
          }
          if (message.toolResults) {
            return message.toolResults.map((toolResults) => ({
              role: 'tool',
              content: toolResults.content,
              tool_call_id: toolResults.toolCallId,
            }));
          }
          return {
            role: 'user',
            content: message.content,
          };
        },
      )
      .flat(),
  ];
}

export interface OpenAiProviderOptions {
  apiKey?: string;
}
