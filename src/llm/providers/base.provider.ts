import type {
  ChatCompletionInput,
  ModelDefinition,
  ModelResponse,
  PartialModelResponse,
} from '../types';

export abstract class BaseProvider {
  constructor(
    public providerName: string,
    public models: ModelDefinition[],
  ) {}

  abstract chatCompletion(input: ChatCompletionInput): Promise<ModelResponse>;
  abstract chatCompletionStream(input: ChatCompletionInput): AsyncGenerator<PartialModelResponse>;
}
