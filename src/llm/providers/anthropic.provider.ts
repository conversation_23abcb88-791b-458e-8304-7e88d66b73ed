import Anthropic from '@anthropic-ai/sdk';
import type {
  ChatCompletionInput,
  Message,
  ModelDefinition,
  ModelResponse,
  ModelUsage,
  PartialModelResponse,
} from '../types';
import { BaseProvider } from './base.provider';
import { LLMModels } from '../llm.models';

const providerName = 'ANTHROPIC';
const models = Object.assign([], LLMModels.ANTHROPIC) satisfies ModelDefinition[];

export type ModelName = (typeof models)[number]['name'] | (typeof models)[number]['alias'][number];

export class AnthropicProvider extends BaseProvider {
  static PROVIDER_NAME = providerName;
  private anthropic: Anthropic;

  constructor(options?: AnthropicProviderOptions) {
    super(providerName, models);
    this.anthropic = new Anthropic(options);
  }

  async chatCompletion(input: ChatCompletionInput<ModelName>): Promise<ModelResponse> {
    const response = await this.anthropic.beta.tools.messages.create({
      ...toAnthropicMessageCreateParams(input),
      stream: false,
    });

    if (!response.content.length) {
      throw new Error('no content block returned');
    }

    const toolUseBlocks = response.content.filter(
      (content): content is Anthropic.Beta.Tools.ToolUseBlock => content.type === 'tool_use',
    );

    const textBlocks = response.content.filter(
      (content): content is Anthropic.Messages.ContentBlock => content.type === 'text',
    );

    return {
      message: {
        content: textBlocks.map((block) => block.text).join('\n'),
        toolCalls: toolUseBlocks.length
          ? toolUseBlocks.map((block) => ({
              id: block.id,
              name: block.name,
              args: block.input as Record<string, unknown>,
            }))
          : undefined,
        role: 'assistant',
      },
      usage: {
        inputTokens: response.usage.input_tokens,
        outputTokens: response.usage.output_tokens,
      },
    };
  }

  async *chatCompletionStream(
    input: ChatCompletionInput<ModelName>,
  ): AsyncGenerator<PartialModelResponse, ModelResponse> {
    if (input.tools?.length) {
      console.warn('Anthropic tools are still in beta and does not support streaming mode');
    }
    const stream = await this.anthropic.beta.tools.messages.create({
      ...toAnthropicMessageCreateParams(input),
      stream: true,
    });

    const usage: ModelUsage = {
      inputTokens: 0,
      outputTokens: 0,
    };
    const streamingTextBlocks: Anthropic.Messages.ContentBlock[] = [];

    for await (const chunk of stream) {
      if (chunk.type === 'message_start') {
        usage.inputTokens += chunk.message.usage.input_tokens;
        usage.outputTokens += chunk.message.usage.output_tokens;
      } else if (chunk.type === 'message_delta') {
        usage.outputTokens += chunk.usage.output_tokens;
      } else if (chunk.type === 'content_block_start') {
        streamingTextBlocks[chunk.index] ??= {
          type: 'text',
          text: '',
        };
        if (chunk.content_block.type === 'text' && chunk.content_block.text) {
          streamingTextBlocks[chunk.index].text += chunk.content_block.text;

          yield {
            delta: {
              content: chunk.content_block.text,
              role: 'assistant',
            },
          };
        }
      } else if (chunk.type === 'content_block_delta') {
        if (chunk.delta.type === 'text_delta') {
          streamingTextBlocks[chunk.index].text += chunk.delta.text;
        }
      }
    }

    return {
      message: {
        content: streamingTextBlocks.map((block) => block.text).join('\n'),
        role: 'assistant',
      },
      usage,
    };
  }
}

function toAnthropicMessageCreateParams(
  input: ChatCompletionInput,
): Anthropic.Beta.Tools.MessageCreateParams {
  return {
    model: input.model,
    messages: toAnthropicMessages(input.messages),
    system: input.systemPrompt,
    max_tokens: 1024,
    tools: input.tools?.map((tool) => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters || {
        type: 'object',
        properties: null,
      },
    })),
  };
}

function toAnthropicMessages(messages: Message[]): Anthropic.Beta.Tools.ToolsBetaMessageParam[] {
  return messages.map((message): Anthropic.Beta.Tools.ToolsBetaMessageParam => {
    if (message.role === 'assistant') {
      return {
        role: 'assistant',
        content: [
          ...(message.content
            ? [
                {
                  type: 'text',
                  text: message.content,
                } as Anthropic.Messages.ContentBlock,
              ]
            : []),
          ...(message.toolCalls?.map(
            (toolCall): Anthropic.Beta.Tools.ToolUseBlockParam => ({
              type: 'tool_use',
              id: toolCall.id,
              name: toolCall.name,
              input: toolCall.args,
            }),
          ) ?? []),
        ],
      };
    }

    if (message.toolResults) {
      return {
        role: 'user',
        content: message.toolResults.map(
          (toolResult): Anthropic.Beta.Tools.ToolResultBlockParam => ({
            type: 'tool_result',
            tool_use_id: toolResult.toolCallId,
            content: [
              {
                type: 'text',
                text: toolResult.content,
              },
            ],
          }),
        ),
      };
    }

    return {
      role: 'user',
      content: [{ type: 'text', text: message.content }],
    };
  });
}

export interface AnthropicProviderOptions {
  apiKey?: string;
}
