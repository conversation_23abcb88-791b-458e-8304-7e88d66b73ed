import OpenAI from 'openai';
import type { EmbeddingInput, EmbeddingModelDefinition, EmbeddingResponse } from '../types';
import { BaseEmbeddingProvider } from './base.embedding.provider';
import { EMBEDDING_MODELS } from './embedding.model';

export class OpenAiEmbeddingProvider extends BaseEmbeddingProvider {
  providerName = 'OpenAI';
  models: EmbeddingModelDefinition[] = EMBEDDING_MODELS;

  private openai: OpenAI;
  constructor(options: OpenAiEmbeddingProviderOptions) {
    super();
    this.openai = new OpenAI(options);
  }

  async embed(input: EmbeddingInput): Promise<EmbeddingResponse> {
    const response = await this.openai.embeddings.create({
      input: input.texts,
      model: input.model,
      dimensions: input.dimensions,
    });

    return {
      embeddings: response.data.map((embedding) => ({
        embedding: embedding.embedding,
      })),
      usage: {
        inputTokens: response.usage.total_tokens,
        outputTokens: 0, // embedding is billed by input tokens only
      },
    };
  }
}

export interface OpenAiEmbeddingProviderOptions {
  apiKey: string;
}
