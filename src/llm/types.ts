import type { Static, TObject } from '@sinclair/typebox';

export interface ChatCompletionInput<ModelName extends string = string> {
  systemPrompt: string;
  messages: Message[];
  tools?: ToolDefinition[];
  model: ModelName;
  opts?: Record<string, unknown>;
  response_format?: Record<string, unknown>;
}

export interface ModelDefinition {
  name: string;
  alias?: string[];
  pricing: ModelPricing;
}

export interface ModelPricing {
  inputTokensCost?: number;
  outputTokensCost?: number;
}

export interface ToolDefinition<Params extends TObject = TObject> {
  name: string;
  description: string;
  parameters: Params | null;
  strict?: boolean;
}

export interface Tool<Params extends TObject = TObject, Context = any, ParamsType = Static<Params>>
  extends ToolDefinition<Params> {
  execute?(ctx: Context, params: ParamsType): Promise<any> | any;
  explain?: (params: ParamsType) => string;
}

export interface ToolCall {
  id: string;
  name: string;
  args: Record<string, unknown> | undefined;
}

export interface ToolResult {
  toolCallId: string;
  content: string;
}

export interface UserMessage {
  role: 'user';
  content: string;
  toolResults?: ToolResult[];
}

export interface AssistantMessage {
  role: 'assistant';
  content: string;
  toolCalls?: ToolCall[];
}

export type Message = UserMessage | AssistantMessage;

export interface ModelResponse {
  message: AssistantMessage;
  usage: ModelUsage;
}

export interface PartialModelResponse {
  delta: AssistantMessage;
}

export interface ModelUsage {
  inputTokens: number;
  outputTokens: number;
}
