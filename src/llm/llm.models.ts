/**
 * Model capabilities enumeration
 */
export enum ModelCapability {
  TEXT = 'text',
  IMAGES = 'images',
  AUDIO = 'audio',
  VIDEO = 'video',
  CODE = 'code',
  FUNCTION_CALLING = 'function-calling',
  REASONING = 'reasoning',
}

/**
 * Detailed model information including pricing, context window, and capabilities
 */
export interface ModelInfo {
  name: string;
  pricing: {
    inputTokensCost: number; // Cost per token in USD
    outputTokensCost: number; // Cost per token in USD
  };
  contextLength?: number;
  capabilities?: ModelCapability[];
  alias?: readonly string[];
}

/**
 * All available LLM models organized by provider
 */
// Image generation models
export const OPENAI_IMAGE_MODELS = {
  DALLE_2: 'dall-e-2',
  DALLE_3: 'dall-e-3',
  GPT_IMAGE_1: 'gpt-image-1',
};

export const GOOGLE_IMAGE_MODELS = {
  GEMINI_IMAGE_GENERATION: 'gemini-2.0-flash-preview-image-generation',
  IMAGEN_3: 'imagen-3',
};

export const IMAGE_MODELS = {
  ...OPENAI_IMAGE_MODELS,
  ...GOOGLE_IMAGE_MODELS,
};

export const IMAGE_SIZES = {
  SQUARE_1024: '1024x1024',
  PORTRAIT_1024_1792: '1024x1792',
  LANDSCAPE_1792_1024: '1792x1024',
  SQUARE_512: '512x512',
  SQUARE_256: '256x256',
};

export const IMAGE_QUALITIES = {
  STANDARD: 'standard',
  HD: 'hd',
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
};

export type ImageGenerationModel =
  | (typeof OPENAI_IMAGE_MODELS)[keyof typeof OPENAI_IMAGE_MODELS]
  | (typeof GOOGLE_IMAGE_MODELS)[keyof typeof GOOGLE_IMAGE_MODELS];

export type ImageSize = (typeof IMAGE_SIZES)[keyof typeof IMAGE_SIZES];
export type ImageQuality = (typeof IMAGE_QUALITIES)[keyof typeof IMAGE_QUALITIES];

export const LLMModels = {
  OPENAI: [
    {
      name: 'gpt-4.1-2025-04-14',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.1'] as const,
    },
    {
      name: 'gpt-4.1-mini-2025-04-14',
      pricing: {
        inputTokensCost: 0.4 / 1000000,
        outputTokensCost: 1.6 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.1-mini'] as const,
    },
    {
      name: 'gpt-4.1-nano-2025-04-14',
      pricing: {
        inputTokensCost: 0.1 / 1000000,
        outputTokensCost: 0.4 / 1000000,
      },
      contextLength: 128000,
      capabilities: [ModelCapability.TEXT, ModelCapability.CODE, ModelCapability.FUNCTION_CALLING],
      alias: ['gpt-4.1-nano'] as const,
    },
    {
      name: 'gpt-4.5-preview-2025-02-27',
      pricing: {
        inputTokensCost: 75.0 / 1000000,
        outputTokensCost: 150.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['gpt-4.5-preview'] as const,
    },
    {
      name: 'gpt-4o-2024-08-06',
      pricing: {
        inputTokensCost: 2.5 / 1000000,
        outputTokensCost: 10.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o'] as const,
    },
    {
      name: 'gpt-4o-mini-2024-07-18',
      pricing: {
        inputTokensCost: 0.15 / 1000000,
        outputTokensCost: 0.6 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-mini'] as const,
    },
    {
      name: 'gpt-4o-audio-preview-2024-12-17',
      pricing: {
        inputTokensCost: 2.5 / 1000000,
        outputTokensCost: 10.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.AUDIO,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-audio-preview'] as const,
    },
    {
      name: 'gpt-4o-realtime-preview-2024-12-17',
      pricing: {
        inputTokensCost: 5.0 / 1000000,
        outputTokensCost: 20.0 / 1000000,
      },
      contextLength: 128000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.CODE,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gpt-4o-realtime-preview'] as const,
    },
    {
      name: 'o3-mini',
      pricing: {
        inputTokensCost: 0.25 / 1000000,
        outputTokensCost: 1.25 / 1000000,
      },
      contextLength: 100000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.REASONING,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['o3-mini'] as const,
    },
  ],
  ANTHROPIC: [
    {
      name: 'claude-3-5-sonnet-20240620' as const,
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-sonnet-3.5'] as const,
    },
    {
      name: 'claude-3-5-haiku-20241022' as const,
      pricing: {
        inputTokensCost: 0.8 / 1000000,
        outputTokensCost: 4.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-haiku-3.5'] as const,
    },
    {
      name: 'claude-3-7-sonnet-20250219' as const,
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      contextLength: 200000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
        ModelCapability.REASONING,
      ],
      alias: ['claude-sonnet-3.7'] as const,
    },
  ],
  GOOGLE: [
    {
      name: 'gemini-1.5-flash',
      pricing: {
        inputTokensCost: 0.35 / 1000000,
        outputTokensCost: 1.05 / 1000000,
      },
      contextLength: 1000000,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gemini-flash-2'] as const,
    },
    {
      name: 'gemini-2.0-flash',
      pricing: {
        inputTokensCost: 0.1 / 1000000,
        outputTokensCost: 0.4 / 1000000,
      },
      contextLength: 1048576,
      capabilities: [
        ModelCapability.TEXT,
        ModelCapability.IMAGES,
        ModelCapability.AUDIO,
        ModelCapability.VIDEO,
        ModelCapability.FUNCTION_CALLING,
      ],
      alias: ['gemini-2.0-flash'] as const,
    },
    {
      name: 'gemini-2.0-flash-lite',
      pricing: {
        inputTokensCost: 0.075 / 1000000,
        outputTokensCost: 0.3 / 1000000,
      },
      contextLength: 1048576,
      capabilities: [ModelCapability.TEXT],
      alias: ['gemini-2.0-flash-lite'] as const,
    },
  ],
  PERPLEXITY: [
    {
      name: 'sonar-deep-research',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-deep-research'] as const,
    },
    {
      name: 'sonar-reasoning-pro',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-reasoning-pro'] as const,
    },
    {
      name: 'sonar-reasoning',
      pricing: {
        inputTokensCost: 1.0 / 1000000,
        outputTokensCost: 5.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT, ModelCapability.REASONING],
      alias: ['sonar-reasoning'] as const,
    },
    {
      name: 'sonar-pro',
      pricing: {
        inputTokensCost: 3.0 / 1000000,
        outputTokensCost: 15.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['sonar-pro'] as const,
    },
    {
      name: 'sonar',
      pricing: {
        inputTokensCost: 1.0 / 1000000,
        outputTokensCost: 1.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['sonar'] as const,
    },
    {
      name: 'r1-1776',
      pricing: {
        inputTokensCost: 2.0 / 1000000,
        outputTokensCost: 8.0 / 1000000,
      },
      capabilities: [ModelCapability.TEXT],
      alias: ['r1-1776'] as const,
    },
  ],
};

export type LLMModel =
  | (typeof LLMModels.OPENAI)[number]
  | (typeof LLMModels.ANTHROPIC)[number]
  | (typeof LLMModels.GOOGLE)[number]
  | (typeof LLMModels.PERPLEXITY)[number];

export const LLMProviders = ['OPENAI', 'ANTHROPIC', 'GOOGLE', 'PERPLEXITY'] as const;

export const getProviderName = (modelName: string): string => {
  for (const provider of LLMProviders) {
    const models = LLMModels[provider];
    for (const model of models) {
      if (model.name === modelName || model.alias == (modelName as any)) {
        return provider;
      }
    }
  }
  throw new Error(`No provider found for model ${modelName} not found`);
};

export const MODEL_BY_PLAN = {
  free: {
    default: 'gpt-4o-mini',
    summary: 'gpt-4.1-nano-2025-04-14',
    outline: 'gpt-4.1-mini-2025-04-14',
    blog: 'claude-3-5-haiku-20241022',
    research: 'sonar',
  },
  monthly: {
    default: 'gpt-4o-mini',
    summary: 'gpt-4.1-nano-2025-04-14',
    outline: 'gpt-4.1-2025-04-14',
    blog: 'claude-3-7-sonnet-20250219',
    research: 'sonar',
  },
  yearly: {
    default: 'gpt-4o-mini',
    summary: 'gpt-4.1-nano-2025-04-14',
    outline: 'gpt-4.1-2025-04-14',
    blog: 'claude-3-7-sonnet-20250219',
    research: 'sonar',
  },
  lifetime: {
    default: 'gpt-4.1-2025-04-14',
    summary: 'gpt-4.1-2025-04-14',
    outline: 'gpt-4.1-2025-04-14',
    blog: 'gpt-4.1-2025-04-14',
    research: 'sonar',
  },
} as const;

export const DEFAULT_MODEL = 'gpt-4o-mini';

/**
 * Model pricing information organized by provider and model name
 * for easy lookup in cost calculation
 */
export const ModelPricing: Record<string, Record<string, ModelInfo>> = {
  openai: LLMModels.OPENAI.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  anthropic: LLMModels.ANTHROPIC.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  google: LLMModels.GOOGLE.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
  perplexity: LLMModels.PERPLEXITY.reduce(
    (acc, model) => {
      acc[model.name] = model;
      if (model.alias) {
        model.alias.forEach((alias) => {
          acc[alias] = model;
        });
      }
      return acc;
    },
    {} as Record<string, ModelInfo>,
  ),
};

export enum TASK {
  DEFAULT = 'default',
  SUMMARY = 'summary',
  OUTLINE = 'outline',
  BLOG = 'blog',
  RESEARCH = 'research',
}

export const getPlanModel = (plan: string, task: TASK = TASK.DEFAULT): string => {
  const planType = plan.split('_')[0].toLowerCase();

  if (planType in MODEL_BY_PLAN) {
    const modelsByTask = MODEL_BY_PLAN[planType as keyof typeof MODEL_BY_PLAN];

    if (task in modelsByTask) {
      return modelsByTask[task];
    }
  }

  return DEFAULT_MODEL;
};
