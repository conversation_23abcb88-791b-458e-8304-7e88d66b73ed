import { Logger } from '@nestjs/common';
import { AnswerError } from '../common/errors/errors';
import type { BaseProvider } from './providers/base.provider';
import type {
  Message,
  ModelResponse,
  ModelUsage,
  PartialModelResponse,
  Tool,
  ToolResult,
  UserMessage,
} from './types';
import { isDefined } from '@common/utils/object';

// Maximum number of iterations for the agent to process a request with tool calls
const MAX_ITERATION_COUNT = 5;

export class Agent {
  private logger: Logger;

  constructor({ logger }: { logger?: Logger }) {
    this.logger = logger;
  }

  async process(
    request: AgentRequest,
    completion: ModelResponse,
  ): Promise<
    | {
        type: 'response';
        response: ModelResponse;
      }
    | {
        type: 'next';
        request: AgentRequest;
      }
  > {
    const {
      messages,
      context,
      tools,
      onIntermediateMessage,
      loopCount = 0,
      usage = {
        inputTokens: 0,
        outputTokens: 0,
      },
    } = request;

    if (loopCount > MAX_ITERATION_COUNT) {
      // safeguard against infinite loops
      throw new Error('max iteration count exceeded');
    }

    usage.inputTokens += completion.usage.inputTokens;
    usage.outputTokens += completion.usage.outputTokens;

    const toolCalls = completion.message.toolCalls;
    if (!toolCalls?.length) {
      // return the completion message if there are no tool calls
      this.logger.debug('process', 'no tool calls in completion message');

      return {
        type: 'response',
        response: {
          message: completion.message,
          usage,
        },
      };
    }

    const toolCallings = toolCalls.map((toolCall) => {
      const toolCallId = toolCall.id;
      const name = toolCall.name;
      // TODO: add validation for toolCall.args
      const args = toolCall.args;
      const callingTool = tools.find((tool) => tool.name === name);

      if (!callingTool) {
        throw new AnswerError(`Sorry.`);
      }

      return {
        name,
        args,
        execute: callingTool.execute,
        explainingText: callingTool.explain?.(args || {}),
        toolCallId,
      };
    });

    const nextMessages: Message[] = [];
    nextMessages.push(completion.message);

    onIntermediateMessage?.(
      completion.message,
      toolCallings.map((toolCalling) => toolCalling.explainingText).filter(isDefined),
    );

    const toolResults: ToolResult[] = [];

    for (const toolCalling of toolCallings) {
      const toolRes = await toolCalling.execute(context, toolCalling.args as any);

      const toolResText = toolRes
        ? typeof toolRes !== 'string'
          ? JSON.stringify(toolRes)
          : toolRes
        : '';

      toolResults.push({
        toolCallId: toolCalling.toolCallId,
        content: toolResText,
      });
    }

    const userToolResultsMessage: UserMessage = {
      role: 'user',
      content: '',
      toolResults,
    };

    nextMessages.push(userToolResultsMessage);
    onIntermediateMessage?.(userToolResultsMessage);

    const nextRequest: AgentRequest = {
      ...request,
      messages: [...messages, ...nextMessages],
      loopCount: loopCount + 1,
    };

    return {
      type: 'next',
      request: nextRequest,
    };
  }

  async chat(request: AgentRequest): Promise<ModelResponse> {
    const { provider, model, systemPrompt, messages, tools } = request;

    this.logger.debug('new chat request');

    const completion = await provider.chatCompletion({
      messages,
      model,
      systemPrompt,
      tools,
    });

    const result = await this.process(request, completion);

    if (result.type === 'response') {
      return result.response;
    } else {
      return this.chat(result.request);
    }
  }

  async *chatStream(
    initialRequest: AgentRequest,
  ): AsyncGenerator<PartialModelResponse, ModelResponse> {
    let request = initialRequest;

    while (true) {
      const { provider, model, systemPrompt, messages, tools } = request;

      this.logger.debug('new chat stream request');

      const stream = provider.chatCompletionStream({
        messages,
        model,
        systemPrompt,
        tools,
      });

      let current = await stream.next();

      while (!current.done) {
        this.logger.debug({ delta: current.value.delta }, 'partial completion');
        yield current.value;
        current = await stream.next();
      }

      const completion = current.value;

      const result = await this.process(request, completion);

      if (result.type === 'response') {
        return result.response;
      } else {
        // recursively call chatStream with the next request
        request = result.request;
      }
    }
  }
}

export interface AgentRequest {
  provider: BaseProvider;
  model: string;
  messages: Message[];
  tools: Tool[];
  loopCount?: number;
  context?: any;
  onIntermediateMessage?: OnIntermediateMessageFn;
  systemPrompt: string;
  usage?: ModelUsage;
}

export type OnIntermediateMessageFn = (message: Message, explainingTexts?: string[]) => void;
