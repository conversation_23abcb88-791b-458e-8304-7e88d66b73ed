import { AiAgent } from './AiAgent';
import { AiAgentOptions } from './types';

/**
 * Configuration for a worker agent in the orchestrator
 */
export interface WorkerAgentConfig<InputType = any> {
  /** Unique identifier for this worker */
  id: string;
  /** Human-readable name for this worker */
  name: string;
  /** Description of what this worker does */
  description: string;
  /** The agent instance */
  agent: AiAgent;
  /** Function to determine if this worker should handle a given task */
  shouldHandle?: (input: InputType, context: OrchestratorContext) => Promise<boolean> | boolean;
}

/**
 * Context object shared between workers in the orchestration process
 */
export interface OrchestratorContext {
  /** The original input to the orchestrator */
  originalInput: any;
  /** Current workflow state */
  state: Record<string, any>;
  /** History of worker executions and their results */
  executionHistory: Array<{
    workerId: string;
    input: any;
    output: any;
    timestamp: Date;
  }>;
  /** Progress information (0-100) */
  progress: {
    percentCompleted: number;
    currentStage: string;
  };
}

/**
 * Agent implementing the Orchestrator-Worker pattern
 *
 * This pattern uses a primary model (orchestrator) to coordinate
 * specialized workers optimized for specific subtasks.
 *
 * @example
 * ```typescript
 * // Create worker agents
 * const researchAgent = new SequentialAgent({...});
 * const reviewAgent = new EvaluatorAgent({...});
 *
 * // Create orchestrator
 * const orchestrator = new OrchestratorAgent({
 *   provider: 'openai',
 *   model: 'gpt-4o',
 * })
 *   .addWorker({
 *     id: 'research',
 *     name: 'Research & Ideation',
 *     description: 'Researches topics and generates ideas',
 *     agent: researchAgent,
 *     shouldHandle: (input, context) => !context.state.research,
 *   })
 *   .addWorker({
 *     id: 'review',
 *     name: 'Content Review',
 *     description: 'Reviews and improves content',
 *     agent: reviewAgent,
 *     shouldHandle: (input, context) => !!context.state.draft,
 *   });
 *
 * // Define workflow
 * orchestrator.setWorkflow([
 *   'research',
 *   'review'
 * ]);
 *
 * // Run the orchestrated process
 * const result = await orchestrator.run("Create an article about AI agents");
 * ```
 */
export class OrchestratorAgent extends AiAgent {
  private workers: Map<string, WorkerAgentConfig>;
  private workflow: string[] | null;
  private workflowGraph: Map<string, string[]>;
  private decisionMaker?: (
    input: any,
    context: OrchestratorContext,
    workers: Map<string, WorkerAgentConfig>,
  ) => Promise<string | null>;
  private inputMappers: Map<
    string,
    (input: any, context: OrchestratorContext) => Promise<any> | any
  >;
  private outputMappers: Map<
    string,
    (output: any, context: OrchestratorContext) => Promise<any> | any
  >;

  constructor(options: AiAgentOptions & { orchestrationPrompt?: string }) {
    super({
      ...options,
      systemPrompt: options.orchestrationPrompt || options.systemPrompt,
    });
    this.workers = new Map();
    this.workflow = null;
    this.workflowGraph = new Map();
    this.inputMappers = new Map();
    this.outputMappers = new Map();
  }

  /**
   * Add a worker agent to the orchestrator
   *
   * @param worker Configuration for the worker agent
   * @returns The orchestrator instance for chaining
   */
  addWorker(worker: WorkerAgentConfig): this {
    this.workers.set(worker.id, worker);
    return this;
  }

  /**
   * Set a fixed sequential workflow by defining the order of worker execution
   *
   * @param workflowSteps Array of worker IDs defining execution order
   * @returns The orchestrator instance for chaining
   */
  setWorkflow(workflowSteps: string[]): this {
    // Validate all workers exist
    workflowSteps.forEach((workerId) => {
      if (!this.workers.has(workerId)) {
        throw new Error(`Worker ${workerId} not found in orchestrator`);
      }
    });

    this.workflow = workflowSteps;
    return this;
  }

  /**
   * Set a dynamic workflow graph with conditional paths
   *
   * @param graph Map of worker IDs to arrays of possible next worker IDs
   * @returns The orchestrator instance for chaining
   */
  setWorkflowGraph(graph: Map<string, string[]>): this {
    // Validate all workers exist
    for (const [workerId, nextSteps] of graph.entries()) {
      if (!this.workers.has(workerId)) {
        throw new Error(`Worker ${workerId} not found in orchestrator`);
      }

      for (const nextStep of nextSteps) {
        if (!this.workers.has(nextStep)) {
          throw new Error(`Worker ${nextStep} not found in orchestrator`);
        }
      }
    }

    this.workflowGraph = graph;
    return this;
  }

  /**
   * Set a custom decision maker function to dynamically determine the next worker
   *
   * @param decisionMaker Function that selects the next worker based on context
   * @returns The orchestrator instance for chaining
   */
  setDecisionMaker(
    decisionMaker: (
      input: any,
      context: OrchestratorContext,
      workers: Map<string, WorkerAgentConfig>,
    ) => Promise<string | null>,
  ): this {
    this.decisionMaker = decisionMaker;
    return this;
  }

  /**
   * Set an input mapper for a specific worker
   *
   * @param workerId ID of the worker
   * @param mapper Function to transform input before passing to worker
   * @returns The orchestrator instance for chaining
   */
  setInputMapper(
    workerId: string,
    mapper: (input: any, context: OrchestratorContext) => Promise<any> | any,
  ): this {
    if (!this.workers.has(workerId)) {
      throw new Error(`Worker ${workerId} not found in orchestrator`);
    }

    this.inputMappers.set(workerId, mapper);
    return this;
  }

  /**
   * Set an output mapper for a specific worker
   *
   * @param workerId ID of the worker
   * @param mapper Function to transform output after worker execution
   * @returns The orchestrator instance for chaining
   */
  setOutputMapper(
    workerId: string,
    mapper: (output: any, context: OrchestratorContext) => Promise<any> | any,
  ): this {
    if (!this.workers.has(workerId)) {
      throw new Error(`Worker ${workerId} not found in orchestrator`);
    }

    this.outputMappers.set(workerId, mapper);
    return this;
  }

  /**
   * Determine the next worker to execute based on workflow configuration
   *
   * @param input Current input
   * @param context Current orchestration context
   * @returns ID of the next worker or null if workflow complete
   */

  private async determineNextWorker(
    input: any,
    context: OrchestratorContext,
  ): Promise<string | null> {
    // If custom decision maker is set, use it
    if (this.decisionMaker) {
      return this.decisionMaker(input, context, this.workers);
    }

    // If using fixed workflow sequence
    if (this.workflow) {
      const history = context.executionHistory;

      if (history.length === 0) {
        return this.workflow[0]; // Start with first worker
      }

      const lastWorker = history[history.length - 1].workerId;
      const lastWorkerIndex = this.workflow.indexOf(lastWorker);

      if (lastWorkerIndex < this.workflow.length - 1) {
        return this.workflow[lastWorkerIndex + 1];
      }

      return null; // Workflow complete
    }

    // If using workflow graph
    if (this.workflowGraph.size > 0) {
      const history = context.executionHistory;

      if (history.length === 0) {
        // Start with first worker that has no incoming edges
        for (const workerId of this.workers.keys()) {
          let hasIncoming = false;

          for (const [_, nextSteps] of this.workflowGraph.entries()) {
            if (nextSteps.includes(workerId)) {
              hasIncoming = true;
              break;
            }
          }

          if (!hasIncoming) {
            return workerId;
          }
        }

        // If no clear starting point, use first worker
        return Array.from(this.workers.keys())[0];
      }

      const lastWorker = history[history.length - 1].workerId;
      const nextOptions = this.workflowGraph.get(lastWorker) || [];

      if (nextOptions.length === 0) {
        return null; // No next steps defined
      }

      // Evaluate each potential next worker using shouldHandle
      for (const nextWorkerId of nextOptions) {
        const worker = this.workers.get(nextWorkerId);

        if (worker && worker.shouldHandle) {
          const shouldHandle = await worker.shouldHandle(input, context);

          if (shouldHandle) {
            return nextWorkerId;
          }
        } else if (worker) {
          return nextWorkerId; // Default to first available if no shouldHandle
        }
      }
    }

    // Default behavior: find any worker that should handle the input
    for (const [workerId, worker] of this.workers.entries()) {
      if (worker.shouldHandle) {
        const shouldHandle = await worker.shouldHandle(input, context);

        if (shouldHandle) {
          return workerId;
        }
      }
    }

    return null; // No suitable worker found
  }

  /**
   * Run the orchestration process with a given input
   *
   * @param input Input to the orchestration process
   * @returns Final result from the orchestration
   */
  async run(input: any): Promise<{
    result: any;
    context: OrchestratorContext;
  }> {
    if (this.workers.size === 0) {
      throw new Error('No workers registered with orchestrator');
    }

    // Initialize orchestration context
    const context: OrchestratorContext = {
      originalInput: input,
      state: {},
      executionHistory: [],
      progress: {
        percentCompleted: 0,
        currentStage: 'Starting orchestration',
      },
    };

    let currentInput = input;
    let workerId: string | null = null;

    // Main orchestration loop
    do {
      // Determine next worker
      workerId = await this.determineNextWorker(currentInput, context);

      if (!workerId) {
        break; // No more workers to execute
      }

      const worker = this.workers.get(workerId)!;

      // Update progress
      const totalWorkflow = this.workflow?.length || this.workers.size;
      const currentStep = context.executionHistory.length + 1;
      context.progress = {
        percentCompleted: Math.min(Math.round((currentStep / totalWorkflow) * 100), 99),
        currentStage: worker.name,
      };

      // Transform input if mapper exists
      const inputMapper = this.inputMappers.get(workerId);
      const workerInput = inputMapper ? await inputMapper(currentInput, context) : currentInput;

      // Execute worker
      const output = await worker.agent.run(workerInput);

      // Transform output if mapper exists
      const outputMapper = this.outputMappers.get(workerId);
      const processedOutput = outputMapper ? await outputMapper(output, context) : output;

      // Record execution
      context.executionHistory.push({
        workerId,
        input: workerInput,
        output: processedOutput,
        timestamp: new Date(),
      });

      // Update state with output
      context.state[workerId] = processedOutput;

      // Pass processed output to next iteration
      currentInput = processedOutput;
    } while (workerId);

    // Orchestration complete
    context.progress = {
      percentCompleted: 100,
      currentStage: 'Orchestration complete',
    };

    // Return the final result and context
    return {
      result: currentInput,
      context,
    };
  }
}
