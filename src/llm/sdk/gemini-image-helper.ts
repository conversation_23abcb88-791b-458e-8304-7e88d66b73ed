import { google } from 'googleapis';
import { Logger } from '@nestjs/common';
import axios from 'axios';
import { GoogleGenAI } from '@google/genai';

const logger = new Logger('GeminiImageHelper');

/**
 * Helper function to generate images using Gemini 2.0 Flash Preview Image Generation model directly with Google's API
 * This bypasses the Vercel AI SDK to avoid compatibility issues
 */
export async function generateGeminiImage({
  prompt,
  size = '1024x1024',
  n = 1,
  apiKey,
  projectId,
  location = 'us-central1',
}: {
  prompt: string;
  size?: string;
  n?: number;
  apiKey: string;
  projectId: string;
  location?: string;
}): Promise<{ image?: { base64: string }; images?: { base64: string }[] }> {
  try {
    // Create Google Auth client
    const auth = new google.auth.GoogleAuth({
      credentials: {
        type: 'service_account',
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: process.env.GOOGLE_CLIENT_EMAIL,
        project_id: projectId,
      },
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });

    // Get access token
    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    if (!accessToken.token) {
      throw new Error('Failed to obtain access token for Gemini');
    }

    // Convert size to aspect ratio for Gemini
    const [width, height] = size.split('x').map(Number);
    let aspectRatio = '1:1'; // Default

    if (width > height) {
      aspectRatio = '16:9';
    } else if (height > width) {
      aspectRatio = '9:16';
    }

    // Gemini API endpoint for image generation
    const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/gemini-2.0-flash-preview-image-generation:predict`;

    // Generate images
    const images: { base64: string }[] = [];

    for (let i = 0; i < n; i++) {
      try {
        // Prepare request payload for Gemini
        const requestPayload = {
          instances: [
            {
              prompt: prompt,
            },
          ],
          parameters: {
            sampleCount: 1,
            aspectRatio: aspectRatio,
            safetyFilterLevel: 'block_some',
          },
        };

        // Make API call to Gemini
        const response = await axios.post(endpoint, requestPayload, {
          headers: {
            Authorization: `Bearer ${accessToken.token}`,
            'Content-Type': 'application/json',
          },
          timeout: 60000, // 60 second timeout
        });

        // Extract image data from response
        if (response.data && response.data.predictions && response.data.predictions.length > 0) {
          const prediction = response.data.predictions[0];

          if (prediction.bytesBase64Encoded) {
            const base64Data = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
            images.push({ base64: base64Data });
          } else if (prediction.mimeType && prediction.bytesBase64Encoded) {
            const base64Data = `data:${prediction.mimeType};base64,${prediction.bytesBase64Encoded}`;
            images.push({ base64: base64Data });
          }
        }
      } catch (imageError) {
        console.log('imgageError', imageError.response.data);
        logger.warn(`Failed to generate image ${i + 1}/${n} with Gemini: ${imageError.message}`);
        // Continue with next image generation attempt
      }
    }

    if (images.length === 0) {
      throw new Error('No images were successfully generated with Gemini');
    }

    // Return in the format expected by AiSdk
    if (n === 1 && images.length === 1) {
      return { image: images[0] };
    } else {
      return { images };
    }
  } catch (error) {
    logger.error(`Error generating image with Gemini: ${error.message}`);
    throw error;
  }
}
