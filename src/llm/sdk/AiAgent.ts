import { ToolSet } from 'ai';
import { AiSdk } from './AiSdk';
import { AiAgentOptions } from './types';
import { logger } from '@/common/logger';
import { z } from 'zod';
import { ImageGenerationModel, ImageQuality, ImageSize } from '../llm.models';

/**
 * Base class for building AI agents using different patterns
 *
 * This class provides core functionality for creating agents that can:
 * - Use tools to accomplish tasks
 * - Make multi-step decisions
 * - Follow different workflow patterns
 */
export abstract class AiAgent {
  protected sdk: AiSdk;
  protected options: AiAgentOptions;

  /**
   * Create a new agent
   *
   * @param options Configuration options for the agent
   */
  constructor(options: AiAgentOptions) {
    this.options = {
      maxSteps: 10,
      ...options,
    };

    this.sdk = new AiSdk({
      provider: this.options.provider,
      model: this.options.model,
      identifierName: this.options.identifierName,
      identifierValue: this.options.identifierValue,
    });
  }

  /**
   * Run the agent with a specific input
   *
   * This method must be implemented by derived classes to define
   * the specific agent pattern and execution strategy.
   *
   * @param input Input for the agent (typically a prompt)
   * @returns The result of the agent's execution
   */
  abstract run(input: any): Promise<any>;

  /**
   * Create a function tool definition using Zod schema
   *
   * @param options Tool configuration options
   * @returns A function tool definition compatible with AI SDK
   */
  protected createToolDefinition<T = any>(options: {
    name: string;
    description: string;
    schema: z.ZodType<T>;
  }): any {
    return {
      [options.name]: {
        description: options.description,
        parameters: options.schema,
      },
    };
  }

  /**
   * Create a function tool for use with AI SDK
   *
   * @param options Tool configuration options
   * @returns A function tool compatible with AI SDK
   */
  protected createFunctionTool(options: {
    name: string;
    description: string;
    parameters: any;
  }): any {
    // Convert JSON Schema parameters to Zod schema
    const zodSchema = this.jsonSchemaToZod(options.parameters);

    return {
      description: options.description,
      parameters: zodSchema,
    };
  }

  /**
   * Convert JSON Schema to Zod schema
   *
   * @param schema JSON Schema object
   * @returns Equivalent Zod schema
   */
  private jsonSchemaToZod(schema: any): z.ZodType {
    if (!schema || typeof schema !== 'object') {
      return z.any();
    }

    // Handle simple types
    if (schema.type === 'string') {
      return z.string().describe(schema.description || '');
    } else if (schema.type === 'number' || schema.type === 'integer') {
      return z.number().describe(schema.description || '');
    } else if (schema.type === 'boolean') {
      return z.boolean().describe(schema.description || '');
    } else if (schema.type === 'array' && schema.items) {
      // Handle arrays
      const itemSchema = this.jsonSchemaToZod(schema.items);
      return z.array(itemSchema).describe(schema.description || '');
    } else if (schema.type === 'object' && schema.properties) {
      // Handle objects
      const shape: Record<string, z.ZodType> = {};

      // Process each property
      for (const [key, prop] of Object.entries(schema.properties)) {
        shape[key] = this.jsonSchemaToZod(prop as any);
      }

      const zodObject = z.object(shape).describe(schema.description || '');

      // Add required fields
      if (schema.required && Array.isArray(schema.required)) {
        const required = schema.required as string[];
        required.forEach((field) => {
          if (shape[field]) {
            // This cast is safe because we know shape has this key
            shape[field] = shape[field].optional().unwrap();
          }
        });
      }

      return zodObject;
    }

    // Default fallback
    return z.any();
  }

  /**
   * Extract and parse tool call arguments consistently
   *
   * @param toolCall The tool call object from LLM response
   * @returns Parsed arguments
   */
  protected extractToolArgs(toolCall: any): any {
    if (!toolCall) {
      logger.warn('extractToolArgs: toolCall is null or undefined');
      return null;
    }

    try {
      // Log the tool call structure for debugging
      logger.debug(`extractToolArgs: toolCall structure: ${JSON.stringify(toolCall, null, 2)}`);

      // AI SDK format - toolCalls have a "args" property
      if (toolCall.args) {
        logger.debug('extractToolArgs: Using AI SDK format with args property');
        return toolCall.args;
      }

      // Older OpenAI format
      if ('arguments' in toolCall && toolCall['arguments']) {
        logger.debug('extractToolArgs: Using OpenAI format with arguments property');
        const args = toolCall['arguments'];
        return typeof args === 'string' ? JSON.parse(args) : args;
      }

      // Anthropic format
      if ('function' in toolCall && toolCall['function'] && toolCall['function']['arguments']) {
        logger.debug('extractToolArgs: Using Anthropic format with function.arguments property');
        const args = toolCall['function']['arguments'];
        return typeof args === 'string' ? JSON.parse(args) : args;
      }

      // OpenAI v2 API format
      if (toolCall.type === 'function' && toolCall.function && toolCall.function.arguments) {
        logger.debug('extractToolArgs: Using OpenAI v2 API format');
        const args = toolCall.function.arguments;
        return typeof args === 'string' ? JSON.parse(args) : args;
      }

      logger.warn(`extractToolArgs: Unrecognized toolCall format: ${JSON.stringify(toolCall)}`);
      return null;
    } catch (error) {
      logger.error(`Error parsing tool call arguments: ${error}`);
      logger.error(`Tool call that caused error: ${JSON.stringify(toolCall)}`);
      return null;
    }
  }

  /**
   * Execute a single LLM call with the agent's configuration
   */
  protected async generateText(
    prompt: string,
    options?: {
      tools?: ToolSet;
      systemPrompt?: string;
      tool_choice?: any;
    },
  ): Promise<any> {
    return this.sdk.generateText({
      provider: this.options.provider,
      model: this.options.model,
      prompt,
      systemPrompt: options?.systemPrompt || this.options.systemPrompt,
      tools: options?.tools || this.options.tools,
      tool_choice: options?.tool_choice,
    });
  }

  /**
   * Generate structured data using Zod schema
   */
  protected async generateObject<T = any>(
    prompt: string,
    schema: z.ZodType<T>,
    options?: {
      systemPrompt?: string;
    },
  ): Promise<T> {
    const result = await this.sdk.generateObject({
      provider: this.options.provider,
      model: this.options.model,
      prompt,
      systemPrompt: options?.systemPrompt || this.options.systemPrompt,
      schema,
    });

    return result.object;
  }

  /**
   * Stream text from the LLM with the agent's configuration
   */
  protected async streamText(
    prompt: string,
    options?: {
      tools?: ToolSet;
      systemPrompt?: string;
    },
  ) {
    return this.sdk.streamText({
      provider: this.options.provider,
      model: this.options.model,
      prompt,
      systemPrompt: options?.systemPrompt || this.options.systemPrompt,
      tools: options?.tools || this.options.tools,
    });
  }

  /**
   * Generate an image based on a text prompt
   *
   * @param prompt The text description of the image to generate
   * @param options Additional options for image generation
   * @returns Generated image(s) information
   */
  protected async generateImage(
    prompt: string,
    options?: {
      model?: ImageGenerationModel;
      size?: ImageSize;
      quality?: ImageQuality;
      style?: string;
      n?: number;
    },
  ): Promise<{ images: string[] }> {
    return this.sdk.generateImage({
      provider: this.options.provider,
      model: options?.model,
      prompt,
      size: options?.size || '1024x1024',
      quality: options?.quality || 'standard',
      style: options?.style,
      n: options?.n || 1,
      identifierName: this.options.identifierName,
      identifierValue: this.options.identifierValue,
    });
  }
}
