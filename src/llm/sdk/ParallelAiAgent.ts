import { AiAgent } from './AiAgent';
import { AiAgentOptions } from './types';

/**
 * Agent implementing the Parallel Processing pattern
 *
 * This pattern executes multiple tasks simultaneously and then
 * combines their results.
 *
 * @example
 * ```typescript
 * const agent = new ParallelAgent({
 *   provider: 'openai',
 *   model: 'gpt-4o'
 * })
 *   .addTask(async (input) => {
 *     // Task 1: Analyze sentiment
 *     return { sentiment: 'positive', score: 0.9 };
 *   })
 *   .addTask(async (input) => {
 *     // Task 2: Extract entities
 *     return { entities: ['product', 'feature'] };
 *   })
 *   .setAggregator(async (results) => {
 *     // Combine results
 *     return {
 *       analysis: { ...results[0], entities: results[1].entities }
 *     };
 *   });
 *
 * const result = await agent.run("Text to analyze in parallel...");
 * ```
 */
export class ParallelAgent extends AiAgent {
  private tasks: Array<(input: string) => Promise<any>>;
  private aggregator?: (results: any[]) => Promise<any>;

  constructor(options: AiAgentOptions) {
    super(options);
    this.tasks = [];
  }

  /**
   * Add a task to be executed in parallel
   *
   * @param task Function that processes input and returns output
   * @returns The agent instance for chaining
   */
  addTask(task: (input: string) => Promise<any>): this {
    this.tasks.push(task);
    return this;
  }

  /**
   * Set a function to aggregate results from parallel tasks
   *
   * @param aggregator Function that combines task results
   * @returns The agent instance for chaining
   */
  setAggregator(aggregator: (results: any[]) => Promise<any>): this {
    this.aggregator = aggregator;
    return this;
  }

  /**
   * Run all tasks in parallel and aggregate their results
   *
   * @param input Input to pass to each task
   * @returns Aggregated result from all tasks
   */
  async run(input: string): Promise<any> {
    const results = await Promise.all(this.tasks.map((task) => task(input)));

    if (this.aggregator) {
      return this.aggregator(results);
    }

    return results;
  }
}
