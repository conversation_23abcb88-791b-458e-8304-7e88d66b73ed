import { AiAgent } from './AiAgent';
import { AiAgentOptions } from './types';

/**
 * Agent implementing the Evaluator-Optimizer pattern
 *
 * This pattern adds quality control by evaluating results and
 * potentially running additional iterations to improve them.
 *
 * @example
 * ```typescript
 * const agent = new EvaluatorAgent({
 *   provider: 'openai',
 *   model: 'gpt-4o',
 *   qualityThreshold: 8,
 *   maxIterations: 3
 * })
 *   .setGenerator(async (input) => {
 *     // Initial generation
 *     return `Generated output for: ${input}`;
 *   })
 *   .setEvaluator(async (result, input) => {
 *     // Evaluate quality
 *     return {
 *       qualityScore: 7,
 *       feedback: 'Needs more detail and examples'
 *     };
 *   })
 *   .setOptimizer(async (result, feedback, input) => {
 *     // Improve based on feedback
 *     return `Improved output with ${feedback}: ${result}`;
 *   });
 *
 * const result = await agent.run("Input requiring high-quality output...");
 * ```
 */
export class EvaluatorAgent extends AiAgent {
  private generator: (input: string) => Promise<string>;
  private evaluator: (
    result: string,
    input: string,
  ) => Promise<{
    qualityScore: number;
    feedback?: string;
  }>;
  private optimizer: (result: string, feedback: string, input: string) => Promise<string>;
  private qualityThreshold: number;
  private maxIterations: number;

  constructor(options: AiAgentOptions & { qualityThreshold?: number; maxIterations?: number }) {
    super(options);
    this.qualityThreshold = options.qualityThreshold || 7;
    this.maxIterations = options.maxIterations || 3;
  }

  /**
   * Set the function to generate initial results
   *
   * @param generator Function that generates output from input
   * @returns The agent instance for chaining
   */
  setGenerator(generator: (input: string) => Promise<string>): this {
    this.generator = generator;
    return this;
  }

  /**
   * Set the function to evaluate results
   *
   * @param evaluator Function that evaluates output quality
   * @returns The agent instance for chaining
   */
  setEvaluator(
    evaluator: (
      result: string,
      input: string,
    ) => Promise<{
      qualityScore: number;
      feedback?: string;
    }>,
  ): this {
    this.evaluator = evaluator;
    return this;
  }

  /**
   * Set the function to optimize results based on feedback
   *
   * @param optimizer Function that improves output based on feedback
   * @returns The agent instance for chaining
   */
  setOptimizer(
    optimizer: (result: string, feedback: string, input: string) => Promise<string>,
  ): this {
    this.optimizer = optimizer;
    return this;
  }

  /**
   * Run the agent with evaluation and optimization loops
   *
   * @param input Input for the agent
   * @returns Optimized result that meets quality threshold
   */
  async run(input: string): Promise<{
    result: string;
    qualityScore: number;
    iterations: number;
  }> {
    if (!this.generator || !this.evaluator || !this.optimizer) {
      throw new Error('Generator, evaluator, and optimizer must be set before running');
    }

    let currentResult = await this.generator(input);
    let iterations = 0;

    while (iterations < this.maxIterations) {
      // Increment iterations counter at the beginning of each loop
      iterations++;
      const evaluation = await this.evaluator(currentResult, input);

      if (evaluation.qualityScore >= this.qualityThreshold) {
        return {
          result: currentResult,
          qualityScore: evaluation.qualityScore,
          iterations,
        };
      }

      // Only run optimizer if we haven't reached max iterations yet
      if (evaluation.feedback && iterations < this.maxIterations) {
        currentResult = await this.optimizer(currentResult, evaluation.feedback, input);
      } else {
        // Break out of the loop if no feedback or reached max iterations
        break;
      }
    }

    // Return the best result we have after max iterations
    const finalEvaluation = await this.evaluator(currentResult, input);

    return {
      result: currentResult,
      qualityScore: finalEvaluation.qualityScore,
      iterations,
    };
  }
}
