import { ToolSet } from 'ai';
import { LLMModels } from '../llm.models';

export type ProviderType = 'openai' | 'anthropic' | 'google' | 'perplexity';

// Extract model names from LLMModels
export type OpenAIModelName = (typeof LLMModels.OPENAI)[number]['name'];
export type AnthropicModelName = (typeof LLMModels.ANTHROPIC)[number]['name'];
export type GoogleModelName = (typeof LLMModels.GOOGLE)[number]['name'];
export type PerplexityModelName = (typeof LLMModels.PERPLEXITY)[number]['name'];

// Combined model name type
export type LLMModelName =
  | OpenAIModelName
  | AnthropicModelName
  | GoogleModelName
  | PerplexityModelName;

export interface ToolCall {
  id: string;
  name: string;
  args: Record<string, unknown>;
}

/**
 * Entity identification for logging and tracking
 */
export interface EntityId {
  /** The identifier name (e.g., 'blogId', 'bid', 'userId', etc.) */
  identifierName: string;

  /** The value of the identifier */
  identifierValue: string;
}

/**
 * Options for LLM Provider configuration
 */
export interface ProviderOptions extends Partial<EntityId> {
  /** LLM provider to use (openai, anthropic, google, perplexity) */
  provider: ProviderType;
  /** Model name to use */
  model: LLMModelName;
  /** Web search mode for Perplexity */
  searchMode?: string;
  /** Maximum number of sources to return when using Perplexity */
  maxSourceCount?: number;
}

/**
 * Configuration options for an AiAgent
 */
export interface AiAgentOptions extends EntityId {
  /** LLM provider to use (openai, anthropic, google, perplexity) */
  provider: ProviderType;
  /** Model name to use */
  model: LLMModelName;
  /** Identifier name */
  identifierName: string;
  /** Identifier value */
  identifierValue: string;
  /** System prompt that defines the agent's behavior */
  systemPrompt?: string;
  /** Maximum number of steps allowed in multi-step executions */
  maxSteps?: number;
  /** Tools the agent can use */
  tools?: ToolSet;
  /** Callback when a step is completed */
  onStepFinish?: (stepResult: StepResult) => void | Promise<void>;
  /** Web search mode for Perplexity */
  searchMode?: string;
  /** Maximum number of sources to return when using Perplexity */
  maxSourceCount?: number;
}

/**
 * Result from a completed step
 */
export interface StepResult {
  /** Text generated in this step */
  text: string;
  /** Tool calls made in this step */
  toolCalls?: any[];
  /** Results from tool calls */
  toolResults?: any[];
  /** Reason for step completion */
  finishReason?: string;
  /** Token usage statistics */
  usage?: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
  };
}

/**
 * Usage log entry for LLM operations
 */
export interface UsageLogEntry extends EntityId {
  /** Timestamp when the operation was performed */
  timestamp: Date;
  /** Provider used for the operation */
  provider: ProviderType;
  /** Model used for the operation */
  model: LLMModelName;
  /** Operation type (e.g., 'text-generation', 'chat-completion') */
  operationType: string;
  /** Token usage information */
  usage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  /** Estimated cost in USD */
  estimatedCost: number;
}
