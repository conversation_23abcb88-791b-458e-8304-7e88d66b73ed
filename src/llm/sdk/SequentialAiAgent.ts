import { AiAgent } from './AiAgent';
import { AiAgentOptions } from './types';

/**
 * Agent implementing the Sequential Processing pattern
 *
 * This pattern executes steps in a predefined order, with each step's
 * output becoming input for the next step.
 *
 * @example
 * ```typescript
 * const agent = new SequentialAgent({
 *   provider: 'openai',
 *   model: 'gpt-4o'
 * })
 *   .addStep(async (input) => {
 *     // Extract key points
 *     return `Key points: ${input}`;
 *   })
 *   .addStep(async (input) => {
 *     // Create summary
 *     return `Summary: ${input}`;
 *   });
 *
 * const result = await agent.run("Text to process...");
 * ```
 */
export class SequentialAgent extends AiAgent {
  private steps: Array<(input: string) => Promise<string>>;

  constructor(options: AiAgentOptions) {
    super(options);
    this.steps = [];
  }

  /**
   * Add a processing step to the agent
   *
   * @param step Function that processes input and returns output
   * @returns The agent instance for chaining
   */
  addStep(step: (input: string) => Promise<string>): this {
    this.steps.push(step);
    return this;
  }

  /**
   * Run the sequential agent through all defined steps
   *
   * @param input Initial input for the first step
   * @returns Result from the final step
   */
  async run(input: string): Promise<string> {
    let currentInput = input;

    for (const step of this.steps) {
      currentInput = await step(currentInput);
    }

    return currentInput;
  }
}
