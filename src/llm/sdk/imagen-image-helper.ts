import { google } from 'googleapis';
import { Logger } from '@nestjs/common';
import axios from 'axios';

const logger = new Logger('ImagenImageHelper');

/**
 * Helper function to generate images using Imagen 3.0 Generate 002 model directly with Google Cloud's API
 * This bypasses the Vercel AI SDK to avoid compatibility issues
 */
export async function generateImagenImage({
  prompt,
  size = '1024x1024',
  n = 1,
  projectId,
  location = 'us-central1',
}: {
  prompt: string;
  size?: string;
  n?: number;
  projectId: string;
  location?: string;
}): Promise<{ image?: { base64: string }; images?: { base64: string }[] }> {
  try {
    // Create Google Auth client
    const auth = new google.auth.GoogleAuth({
      credentials: {
        type: 'service_account',
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        client_email: process.env.GOOGLE_CLIENT_EMAIL,
        project_id: projectId,
      },
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });

    // Get access token
    const authClient = await auth.getClient();
    const accessToken = await authClient.getAccessToken();

    if (!accessToken.token) {
      throw new Error('Failed to obtain access token for Imagen');
    }

    // Convert size to aspect ratio for Imagen
    const [width, height] = size.split('x').map(Number);
    let aspectRatio = '1:1'; // Default

    if (width > height) {
      aspectRatio = '16:9';
    } else if (height > width) {
      aspectRatio = '9:16';
    }

    // Imagen API endpoint
    const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/imagen-3.0-generate-002:predict`;

    console.log('endpoint', endpoint);

    // Generate images
    const images: { base64: string }[] = [];

    console.log(100);
    for (let i = 0; i < n; i++) {
      try {
        // Prepare request payload for Imagen
        console.log(101);
        const requestPayload = {
          instances: [
            {
              prompt: prompt,
            },
          ],
          parameters: {
            sampleCount: 1,
            aspectRatio: aspectRatio,
            safetyFilterLevel: 'block_some',
            personGeneration: 'allow_adult',
          },
        };

        console.log(102);
        // Make API call to Imagen
        const response = await axios.post(endpoint, requestPayload, {
          headers: {
            Authorization: `Bearer ${accessToken.token}`,
            'Content-Type': 'application/json',
          },
          timeout: 60000, // 60 second timeout
        });

        console.log(103);
        console.log('response', response.data);

        // Extract image data from response
        if (response.data && response.data.predictions && response.data.predictions.length > 0) {
          const prediction = response.data.predictions[0];

          if (prediction.bytesBase64Encoded) {
            const base64Data = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
            images.push({ base64: base64Data });
          } else if (prediction.mimeType && prediction.bytesBase64Encoded) {
            const base64Data = `data:${prediction.mimeType};base64,${prediction.bytesBase64Encoded}`;
            images.push({ base64: base64Data });
          }
        }
      } catch (imageError) {
        console.log(109);
        console.log('imageError', JSON.stringify(imageError.response.data, null, 2));
        logger.warn(`Failed to generate image ${i + 1}/${n} with Imagen: ${imageError.message}`);
        // Continue with next image generation attempt
      }
    }

    console.log(104);
    if (images.length === 0) {
      console.log(105);
      throw new Error('No images were successfully generated with Imagen');
    }

    console.log(106);
    // Return in the format expected by AiSdk
    if (n === 1 && images.length === 1) {
      return { image: images[0] };
    } else {
      return { images };
    }
  } catch (error) {
    logger.error(`Error generating image with Imagen: ${error.message}`);
    throw error;
  }
}
