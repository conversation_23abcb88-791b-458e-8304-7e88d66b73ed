import { OpenAI } from 'openai';
import { Logger } from '@nestjs/common';

const logger = new Logger('GptImageHelper');

/**
 * Helper function to generate images using GPT Image 1 model directly with OpenAI's SDK
 * This bypasses the Vercel AI SDK to avoid the response_format issue
 */
export async function generateGptImage({
  prompt,
  size = '1024x1024',
  quality = 'low',
  n = 1,
  apiKey,
}: {
  prompt: string;
  size?: string;
  quality?: string;
  n?: number;
  apiKey: string;
}): Promise<string[]> {
  try {
    // Create direct instance of OpenAI client
    const openai = new OpenAI({ apiKey });

    // Call the OpenAI API directly
    const response = await openai.images.generate({
      model: 'gpt-image-1',
      prompt,
      n,
      size: size as any,
      quality: quality as any,
    });

    // Extract and return the image data
    const images = response.data
      .map((image) => {
        // For URL responses
        if (image.url) {
          return image.url;
        }
        // For b64_json responses
        if (image.b64_json) {
          return `data:image/png;base64,${image.b64_json}`;
        }
        return '';
      })
      .filter(Boolean);

    return images;
  } catch (error) {
    logger.error(`Error generating image with GPT Image 1: ${error.message}`);
    throw error;
  }
}
