import { OpenAI } from 'openai';
import { Logger } from '@nestjs/common';

const logger = new Logger('GptImageHelper');

/**
 * Helper function to generate images using GPT Image 1 model directly with OpenAI's SDK
 * This bypasses the Vercel AI SDK to avoid the response_format issue
 */
export async function generateGptImage({
  prompt,
  size = '1024x1024',
  quality = 'low',
  n = 1,
  apiKey,
}: {
  prompt: string;
  size?: string;
  quality?: string;
  n?: number;
  apiKey: string;
}): Promise<{ image?: { base64: string }; images?: { base64: string }[] }> {
  try {
    // Create direct instance of OpenAI client
    const openai = new OpenAI({ apiKey });

    // Call the OpenAI API directly
    const response = await openai.images.generate({
      model: 'gpt-image-1',
      prompt,
      n,
      size: size as any,
      quality: quality as any,
    });

    // Extract and return the image data
    const images = response.data
      .map((image) => {
        // For URL responses
        if (image.url) {
          return { base64: image.url };
        }
        // For b64_json responses
        if (image.b64_json) {
          return { base64: `data:image/png;base64,${image.b64_json}` };
        }
        return null;
      })
      .filter(Boolean);

    // Return in the format expected by AiSdk
    if (n === 1 && images.length === 1) {
      return { image: images[0] };
    } else {
      return { images };
    }
  } catch (error) {
    logger.error(`Error generating image with GPT Image 1: ${error.message}`);
    throw error;
  }
}
