import { logger } from '@/common/logger';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { openai } from '@ai-sdk/openai';
import { perplexity } from '@ai-sdk/perplexity';
import {
  generateObject,
  generateText,
  LanguageModelV1,
  streamText,
  ToolSet,
  experimental_generateImage as generateImage,
} from 'ai';
import { z } from 'zod';
import { ImageGenerationModel, ImageQuality, ImageSize, ModelPricing } from '../llm.models';
import { TokenUsage } from './aiSdk.types';
import { EntityId, ProviderOptions, ProviderType, UsageLogEntry } from './types';
import { generateGptImage } from './gpt-image-helper';
import { generateGeminiImage } from './gemini-image-helper';
import { generateImagenImage } from './imagen-image-helper';

/**
 * AI SDK Client for unified interactions with multiple LLM providers
 */
export class AiSdk {
  private options: ProviderOptions;
  private usageLogs: UsageLogEntry[] = [];

  constructor(
    options: ProviderOptions = {
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'default',
      identifierValue: 'default',
    },
  ) {
    this.validateEntityId(options);
    this.options = options;
  }

  /**
   * Validate that entity identification is provided
   */
  private validateEntityId(options: Partial<EntityId>): void {
    if (!options.identifierName || !options.identifierValue) {
      throw new Error(
        'Both identifierName and identifierValue must be provided for tracking and logging purposes',
      );
    }
  }

  /**
   * Get a configured model based on provider and model name
   */
  getProvider(options: ProviderOptions) {
    const { provider, model } = options || this.options;

    switch (provider) {
      case 'openai':
        return openai(model);
      case 'anthropic':
        return anthropic(model);
      case 'google':
        return google(model);
      case 'perplexity':
        return perplexity(model);
      default:
        logger.error(`Unsupported provider: ${provider}, using OpenAI`);
        return openai('gpt-4o');
    }
  }

  /**
   * Stream text with specified model and tools
   */
  async streamText(options: {
    prompt: string;
    systemPrompt?: string;
    tools?: ToolSet;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    const result = await streamText({
      model: model as LanguageModelV1,
      prompt: options.prompt,
      system: options.systemPrompt,
      tools: options.tools,
    });

    // Log usage for streaming operations when complete
    if (result.usage) {
      const usage = (await result.usage) as TokenUsage;
      const inputTokens = usage.promptTokens;
      const outputTokens = usage.completionTokens;
      const totalTokens = usage.totalTokens;

      this.logUsage({
        provider: providerOptions.provider,
        model: providerOptions.model,
        identifierName: providerOptions.identifierName,
        identifierValue: providerOptions.identifierValue,
        operationType: 'stream-text',
        usage: {
          inputTokens,
          outputTokens,
          totalTokens,
        },
        estimatedCost: this.calculateCost(
          providerOptions.provider,
          providerOptions.model,
          inputTokens,
          outputTokens,
        ),
      });
    }

    return result;
  }

  /**
   * Generate text with specified model and tools
   */
  async generateText(options: {
    prompt: string;
    systemPrompt?: string;
    tools?: ToolSet | any[];
    tool_choice?: any;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
    searchMode?: string; // Added for Perplexity web search
    maxSourceCount?: number; // Added for Perplexity source control
  }) {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    // Process tools into consistent format
    const toolsOption = this.normalizeTools(options.tools);

    // Create the base parameters
    const generateParams: any = {
      model,
      prompt: options.prompt,
      system: options.systemPrompt,
    };

    // Only add tools if they exist and are properly formatted
    if (
      toolsOption &&
      ((typeof toolsOption === 'object' && Object.keys(toolsOption).length > 0) ||
        (Array.isArray(toolsOption) && toolsOption.length > 0))
    ) {
      generateParams.tools = toolsOption;
    }

    // Add tool_choice if provided
    if (options.tool_choice) {
      generateParams.tool_choice = options.tool_choice;
    }

    // Add Perplexity-specific options
    if (providerOptions.provider === 'perplexity') {
      // Add searchMode parameter for web search
      if (options.searchMode) {
        generateParams.search_mode = options.searchMode;
      }

      // Add max source count for limiting sources
      if (options.maxSourceCount) {
        generateParams.max_source_count = options.maxSourceCount;
      }
    }

    try {
      const result = await generateText(generateParams);

      // Log usage after text generation
      if (result.usage) {
        const usage = (await result.usage) as TokenUsage;
        const inputTokens = usage.promptTokens;
        const outputTokens = usage.completionTokens;
        const totalTokens = usage.totalTokens;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName,
          identifierValue: providerOptions.identifierValue,
          operationType: 'generate-text',
          usage: {
            inputTokens,
            outputTokens,
            totalTokens,
          },
          estimatedCost: this.calculateCost(
            providerOptions.provider,
            providerOptions.model,
            inputTokens,
            outputTokens,
          ),
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error in generateText: ${error}`);
      throw error;
    }
  }

  /**
   * Generate structured data using the AI SDK's generateObject function
   */
  async generateObject<T = any>(options: {
    prompt: string;
    systemPrompt?: string;
    schema: z.ZodType<T>;
    provider?: ProviderType;
    model?: string;
    identifierName?: string;
    identifierValue?: string;
  }): Promise<{ object: T }> {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };

    this.validateEntityId(providerOptions);
    const model = this.getProvider(providerOptions);

    try {
      const result = await generateObject({
        model: model as LanguageModelV1,
        prompt: options.prompt,
        system: options.systemPrompt,
        schema: options.schema,
      });

      // Log usage
      if (result.usage) {
        const usage = (await result.usage) as TokenUsage;
        const inputTokens = usage.promptTokens;
        const outputTokens = usage.completionTokens;
        const totalTokens = usage.totalTokens;

        this.logUsage({
          provider: providerOptions.provider,
          model: providerOptions.model,
          identifierName: providerOptions.identifierName,
          identifierValue: providerOptions.identifierValue,
          operationType: 'generate-object',
          usage: {
            inputTokens,
            outputTokens,
            totalTokens,
          },
          estimatedCost: this.calculateCost(
            providerOptions.provider,
            providerOptions.model,
            inputTokens,
            outputTokens,
          ),
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error in generateObject: ${error}`);
      throw error;
    }
  }

  /**
   * Normalize tools to consistent format
   */
  private normalizeTools(tools?: ToolSet | any[]): ToolSet | any[] | undefined {
    if (!tools) return undefined;

    // For OpenAI format with type: 'function', pass arrays directly
    // This is important for tool calling format compliance
    if (Array.isArray(tools) && tools.length > 0 && tools[0]?.type === 'function') {
      return tools;
    }

    // If tools is already in object format, return as is
    if (!Array.isArray(tools)) return tools;

    // Convert array tools to object format for compatibility with AI SDK
    const normalizedTools: ToolSet = {};
    for (const tool of tools) {
      if (tool && tool.function && tool.function.name) {
        normalizedTools[tool.function.name] = tool;
      }
    }

    return Object.keys(normalizedTools).length > 0 ? normalizedTools : undefined;
  }

  /**
   * Log usage data for analytics and billing purposes
   */
  private logUsage(data: Omit<UsageLogEntry, 'timestamp'>): void {
    const logEntry: UsageLogEntry = {
      ...data,
      timestamp: new Date(),
    };

    // Add to in-memory log
    this.usageLogs.push(logEntry);

    // Log to console
    logger.debug(
      `LLM Usage: ${logEntry.identifierName}:${logEntry.identifierValue} | ${logEntry.operationType} | ${logEntry.provider}/${logEntry.model} | Tokens: ${logEntry.usage.totalTokens} | Cost: ${logEntry.estimatedCost}`,
    );
  }

  /**
   * Calculate the estimated cost of a request based on tokens used
   */
  private calculateCost(
    provider: ProviderType,
    model: string,
    inputTokens: number,
    outputTokens: number,
  ): number {
    const pricing = ModelPricing[provider]?.[model];

    if (!pricing) {
      logger.warn(`No pricing information available for ${provider}/${model}`);
      return 0;
    }

    const inputCost = inputTokens * pricing.pricing.inputTokensCost;
    const outputCost = outputTokens * pricing.pricing.outputTokensCost;

    return inputCost + outputCost;
  }

  /**
   * Get all logged usage data
   */
  getUsageLogs(): UsageLogEntry[] {
    return [...this.usageLogs];
  }

  /**
   * Generate an image with the specified model and parameters
   */
  // eslint-disable-next-line complexity
  async generateImage(options: {
    prompt: string;
    provider?: ProviderType;
    model?: ImageGenerationModel;
    size?: ImageSize;
    quality?: ImageQuality;
    style?: string;
    n?: number;
    identifierName?: string;
    identifierValue?: string;
  }): Promise<{ images: string[] }> {
    const providerOptions = {
      provider: options.provider || this.options.provider,
      model: options.model || this.options.model,
      identifierName: options.identifierName || this.options.identifierName,
      identifierValue: options.identifierValue || this.options.identifierValue,
    };
    console.log('providerOptions', providerOptions);
    console.log('options', options);

    this.validateEntityId(providerOptions);
    const { prompt, size = '1024x1024', quality = 'standard', style, n = 1 } = options;

    try {
      const allImages: string[] = [];

      if (providerOptions.provider === 'openai') {
        // Using OpenAI for DALL-E and GPT Image 1 models
        const modelName = options.model || 'dall-e-3';
        const sdkModel = openai.image(modelName as any);

        // For multiple images, we'll need to make multiple calls for models like DALL-E 3
        // that only support generating one image at a time
        const imagesToGenerate = n;
        const batchSize = modelName === 'dall-e-3' ? 1 : Math.min(n, 10); // DALL-E 3 can only generate 1 image at a time, DALL-E 2 up to 10
        const batches = Math.ceil(imagesToGenerate / batchSize);

        for (let i = 0; i < batches; i++) {
          const currentBatchSize = Math.min(batchSize, imagesToGenerate - i * batchSize);

          // Set up provider-specific options for OpenAI
          const aiSdkProviderOptions: Record<string, any> = {
            openai: {},
          };

          // Map quality values specifically for GPT Image 1 model
          if (quality) {
            if (modelName === 'gpt-image-1') {
              // Map DALL-E quality terms to GPT Image 1 quality terms
              if (quality === 'standard') {
                aiSdkProviderOptions.openai.quality = 'low';
              } else if (quality === 'hd') {
                aiSdkProviderOptions.openai.quality = 'medium';
              } else {
                // Pass through other values like 'low', 'medium', 'high' directly
                aiSdkProviderOptions.openai.quality = quality;
              }
            } else {
              // For other models, use the provided quality directly
              aiSdkProviderOptions.openai.quality = quality;
            }
          }

          if (style) aiSdkProviderOptions.openai.style = style;

          console.log('sdkModel', sdkModel.modelId);
          console.log('aiSdkProviderOptions', aiSdkProviderOptions);

          const result =
            sdkModel.modelId === 'gpt-image-1'
              ? await generateGptImage({
                  prompt,
                  size: size as any,
                  quality: aiSdkProviderOptions.openai.quality,
                  n: currentBatchSize,
                  apiKey: process.env.OPENAI_API_KEY,
                })
              : await generateImage({
                  model: sdkModel,
                  prompt,
                  size: size as any,
                  providerOptions: aiSdkProviderOptions,
                });

          // Add the generated images to our collection
          if (currentBatchSize === 1) {
            allImages.push(result.image.base64);
          } else if (result.images) {
            result.images.forEach((img) => allImages.push(img.base64));
          }
        }

        // Log usage for OpenAI image generation
        this.logUsage({
          provider: providerOptions.provider,
          model: modelName,
          identifierName: providerOptions.identifierName,
          identifierValue: providerOptions.identifierValue,
          operationType: 'generate-image',
          usage: {
            inputTokens: 0, // OpenAI doesn't provide token usage for image generation
            outputTokens: 0,
            totalTokens: 0,
          },
          estimatedCost: this.calculateImageCost(
            providerOptions.provider,
            modelName,
            size,
            quality,
            n,
          ),
        });
      } else if (providerOptions.provider === 'google') {
        // Using Google for Gemini and Imagen models
        const modelName = options.model || 'gemini-2.0-flash-preview-image-generation';

        // Google models might have different batching capabilities
        const imagesToGenerate = n;
        const batchSize = 1; // Default to 1 for safety
        const batches = Math.ceil(imagesToGenerate / batchSize);

        for (let i = 0; i < batches; i++) {
          const currentBatchSize = Math.min(batchSize, imagesToGenerate - i * batchSize);

          let result: { image?: { base64: string }; images?: { base64: string }[] };

          if (modelName === 'gemini-2.0-flash-preview-image-generation') {
            console.log('gemini', {
              prompt,
              size: size as any,
              n: currentBatchSize,
              apiKey: process.env.GOOGLE_API_KEY || '',
              projectId: process.env.GOOGLE_PROJECT_ID || '',
              location: process.env.GOOGLE_LOCATION || 'us-central1',
            });
            // Use Gemini helper
            result = await generateGeminiImage({
              prompt,
              size: size as any,
              n: currentBatchSize,
              apiKey: process.env.GOOGLE_API_KEY || '',
              projectId: process.env.GOOGLE_PROJECT_ID || '',
              location: process.env.GOOGLE_LOCATION || 'us-central1',
            });
          } else if (modelName === 'imagen-3.0-generate-002') {
            // Use Imagen helper
            console.log('imagen', {
              prompt,
              size: size as any,
              n: currentBatchSize,
              projectId: process.env.GOOGLE_PROJECT_ID || '',
              location: process.env.GOOGLE_LOCATION || 'us-central1',
            });
            result = await generateImagenImage({
              prompt,
              size: size as any,
              n: currentBatchSize,
              projectId: process.env.GOOGLE_PROJECT_ID || '',
              location: process.env.GOOGLE_LOCATION || 'us-central1',
            });
          } else {
            // Fallback to AI SDK for other models
            const sdkModel = google(modelName as any);
            const [width, height] = size.split('x').map(Number);
            const aspectRatio = `${width}:${height}`;

            result = await generateImage({
              model: sdkModel as any,
              prompt,
              aspectRatio: aspectRatio as any,
              n: currentBatchSize,
            });
          }

          // Add the generated images to our collection
          if (currentBatchSize === 1 && result.image) {
            allImages.push(result.image.base64);
          } else if (result.images) {
            result.images.forEach((img) => allImages.push(img.base64));
          }
        }

        // Log usage for Google image generation
        this.logUsage({
          provider: providerOptions.provider,
          model: modelName,
          identifierName: providerOptions.identifierName,
          identifierValue: providerOptions.identifierValue,
          operationType: 'generate-image',
          usage: {
            inputTokens: 0, // Google doesn't provide token usage for image generation
            outputTokens: 0,
            totalTokens: 0,
          },
          estimatedCost: this.calculateImageCost(
            providerOptions.provider,
            modelName,
            size,
            'standard', // Google doesn't have a quality parameter
            n,
          ),
        });
      } else {
        throw new Error(
          `Image generation not implemented for provider: ${providerOptions.provider}`,
        );
      }

      if (allImages.length === 0) {
        throw new Error('No images were generated');
      }

      return { images: allImages };
    } catch (error) {
      logger.error(
        `Error generating image with ${providerOptions.provider}/${providerOptions.model}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Calculate the estimated cost of an image generation request
   */
  // eslint-disable-next-line complexity
  private calculateImageCost(
    provider: ProviderType,
    model: string,
    size: string,
    quality?: string,
    n = 1,
  ): number {
    let cost = 0;

    if (provider === 'openai') {
      // Pricing based on OpenAI's pricing structure
      switch (model) {
        case 'dall-e-2':
          // DALL-E 2 pricing
          if (size === '256x256') cost = 0.016;
          else if (size === '512x512') cost = 0.018;
          else if (size === '1024x1024') cost = 0.02;
          break;
        case 'dall-e-3':
          // DALL-E 3 pricing
          if (size === '1024x1024') {
            cost = quality === 'hd' ? 0.08 : 0.04;
          } else if (size === '1024x1792' || size === '1792x1024') {
            cost = quality === 'hd' ? 0.12 : 0.08;
          }
          break;
        case 'gpt-image-1':
          // GPT Image 1 pricing
          if (size === '1024x1024') {
            if (quality === 'low') cost = 0.011;
            else if (quality === 'medium') cost = 0.042;
            else if (quality === 'high') cost = 0.167;
          } else if (size === '1024x1536' || size === '1536x1024') {
            if (quality === 'low') cost = 0.016;
            else if (quality === 'medium') cost = 0.063;
            else if (quality === 'high') cost = 0.25;
          }
          break;
      }
    } else if (provider === 'google') {
      // Google Gemini and Imagen pricing (based on documentation)
      switch (model) {
        case 'gemini-2.0-flash-preview-image-generation':
          // Free for now at limited preview (as per Google's documentation)
          cost = 0;
          break;
        case 'imagen-3.0-generate-002':
          // Imagen 3.0 pricing - approximately $0.04 per image
          cost = 0.04;
          break;
        case 'imagen-3':
          // Legacy Imagen pricing
          cost = 0.04;
          break;
      }
    }

    // Multiply by the number of images
    return cost * n;
  }
}

// Re-export types
export * from './types';
