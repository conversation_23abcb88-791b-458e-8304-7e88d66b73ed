import { forwardRef, Module } from '@nestjs/common';
import { FacebookController } from './facebook.controller';
import { FacebookService } from './facebook.service';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtService } from '@nestjs/jwt';
import { PaymentsModule } from 'src/payments/payments.module';
import { SlackService } from '@common/services/slack.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SignupAttempt, SignupAttemptSchema } from '@/auth/signup-attempt.model';
import { AuthModule } from '@/auth/auth.module';

@Module({
  controllers: [FacebookController],
  imports: [
    MongooseModule.forFeature([{ name: SignupAttempt.name, schema: SignupAttemptSchema }]),
    forwardRef(() => AuthModule),
    UserModule,
    BusinessModule,
    PaymentsModule,
  ],
  providers: [FacebookService, JwtService, SlackService],
  exports: [FacebookService],
})
export class FacebookModule {}
