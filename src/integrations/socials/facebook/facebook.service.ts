import axios from 'axios';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '@user/user.service';
import { BusinessService } from '@business/business.service';
import { StripeService } from 'src/payments/stripe.service';
import { AuthService } from '@auth/auth.service';
import { IncomingWebhook } from '@slack/webhook';

@Injectable()
export class FacebookService {
  private readonly logger = new Logger(FacebookService.name);
  private readonly slack: IncomingWebhook;
  readonly graphAPIversion = 'v19.0';

  constructor(
    private readonly configService: ConfigService,
    private jwtService: JwtService,
    private userService: UserService,
    private businessService: BusinessService,
    private readonly stripeService: StripeService,
    private readonly authService: AuthService,
  ) {
    this.slack = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_MONITOR_SIGNUP'),
    );
  }

  async isTokenRenewable(bid: string): Promise<boolean> {
    try {
      const business = await this.businessService.findOne(bid);
      const pages = business.fbPages;
      let fbPages = pages;
      fbPages = await Promise.all(
        pages.map(async (page: any) => {
          const response = await this.getPageAccessToken(page.id, business.facebookAccessToken);
          page.accessToken = response.data.access_token;
          return page;
        }),
      );

      await this.businessService.updateBusinessFacebookToken(
        bid,
        fbPages,
        business.facebookAccessToken,
        new Date().getTime() + 7 * 24 * 60 * 60 * 1000,
      );

      return true;
    } catch (error) {
      this.logger.error(`Token is not renewable: for ${bid}`, error?.message);
      return false;
    }
  }

  async callback(req, res) {
    try {
      this.logger.debug(req['user']);
      const facebookAccessToken = req['user']?.['accessToken'];
      const pages = req['user']?.['pages'];
      const jwtToken: string = req.query?.state as string;

      // Save the tokens to your database for future use
      this.jwtService.verify(jwtToken, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(jwtToken);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }

      const fbPages = await Promise.all(
        pages.map(async (page) => {
          const response = await this.getPageAccessToken(page.id, facebookAccessToken);
          page.accessToken = response.data.access_token;
          return {
            id: page.id,
            name: page.name,
            accessToken: response?.data?.access_token || page.access_token,
            category: page.category,
          };
        }),
      );

      await this.businessService.updateBusinessFacebookToken(
        payload.bid,
        fbPages,
        facebookAccessToken,
        new Date().getTime() + 7 * 24 * 60 * 60 * 1000,
      );

      res.redirect(
        this.configService.get('DASHBOARD_REDIRECT_URL') +
          '/dashboard/settings?message=Successfully connected to Facebook',
      );
    } catch (error) {
      this.logger.error(`Error in facebook callback`, error?.message);
      res.redirect(
        `${this.configService.get('DASHBOARD_REDIRECT_URL')}/dashboard/settings?error=${
          error?.message
        }`,
      );
    }
  }

  async fetchSites(businessID: string) {
    const business = await this.businessService.findOne(businessID);
    return business.fbPages;
  }

  async loginCallback(req, res) {
    try {
      this.logger.debug(req['user']);
      const { email, name, picture, accessToken } = req['user'];
      const user = await this.userService.findByEmail(email);
      if (!user) {
        const stripeCustomer = await this.stripeService.createCustomer(email, name);

        const createdUser = await this.userService.create({
          email,
          password: accessToken,
          name,
          businessName: name,
          stripeId: stripeCustomer.id,
          profilePicture: picture,
        });

        try {
          const slackMessage = {
            text: `${user.email} signed up using Facebook! :tada:`,
          };
          if (process.env.NODE_ENV === 'production') {
            this.slack.send(slackMessage);
          }
        } catch (error) {
          this.logger.error('Slack webhook failed for Facebook', error?.message);
        }

        const { access_token } = await this.authService.login(createdUser);

        res.redirect(
          this.configService.get('DASHBOARD_REDIRECT_URL') + `/login?token=${access_token}`,
        );
      } else {
        const { access_token } = await this.authService.login(user);

        res.redirect(
          this.configService.get('DASHBOARD_REDIRECT_URL') + `/login?token=${access_token}`,
        );
      }
    } catch (error) {
      res.redirect(
        this.configService.get('DASHBOARD_REDIRECT_URL') +
          `/login?error=${
            error.code === 11000 ? 'Email Already exist.' : `Signup failed ${error?.message}`
          }`,
      );
    }
  }

  async getPages(accessToken: string) {
    const response = await axios.get(
      `https://graph.facebook.com/${this.graphAPIversion}/me/accounts?access_token=${accessToken}`,
    );
    return response;
  }

  async getPageAccessToken(pageId: string, userAccessToken: string) {
    const response = await axios.get(
      `https://graph.facebook.com/${this.graphAPIversion}/${pageId}?fields=access_token&access_token=${userAccessToken}`,
    );
    return response;
  }

  async publishPost(
    bid: string,
    message: string,
    siteIDs: string[],
    link?: string,
    image?: string,
    draft?: boolean,
  ): Promise<string> {
    try {
      const business = await this.businessService.findOne(bid);
      const pages = business.fbPages;
      const currentDate = new Date().getTime();
      let fbPages = pages;

      if (currentDate >= business.facebookTokenExpiry) {
        fbPages = await Promise.all(
          pages.map(async (page: any) => {
            const response = await this.getPageAccessToken(page.id, business.facebookAccessToken);
            page.accessToken = response.data.access_token;
            return page;
          }),
        );

        await this.businessService.updateBusinessFacebookToken(
          bid,
          fbPages,
          business.facebookAccessToken,
          new Date().getTime() + 7 * 24 * 60 * 60 * 1000,
        );
      }

      const promises = fbPages
        .filter((page: any) => siteIDs.includes(page.id))
        .map(async (page: any) => {
          try {
            const res = await this.postMessageWithLink(
              page.id,
              page.accessToken,
              message,
              link,
              draft,
            );
            this.logger.debug(`Posted to page ${page.id} for business ${bid}`);
            return res;
          } catch (error) {
            this.logger.error(
              `Failed to post for business ${bid} to page ${page.id}: ${error?.message}`,
            );
            throw error;
          }
        });

      const results = await Promise.all(promises);

      return `https://facebook.com/${results[0]}`;
    } catch (error) {
      this.logger.error(`Failed to post for fb business ${bid}` + error?.message);
      throw error;
    }
  }

  async postMessageWithLink(
    pageId: string,
    accessToken: string,
    message: string,
    link: string,
    draft: boolean,
  ): Promise<string> {
    try {
      const response = await axios.post(`https://graph.facebook.com/${pageId}/feed`, {
        access_token: accessToken,
        message: message,
        link,
        published: !draft,
      });
      return response.data?.id;
    } catch (error) {
      this.logger.error(`Failed to post for fb page ${pageId}`, error?.message);
      throw error;
    }
  }
}
