import { <PERSON>, Get, Req, Res, UseGuards } from '@nestjs/common';
import { FacebookService } from './facebook.service';
import { AuthGuard } from '@nestjs/passport';
import { QueryTokenGuard } from '@auth/guards/query-token.guard';
import { Auth } from '@auth/guards/auth.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';

@Controller('facebook')
export class FacebookController {
  constructor(private readonly facebookService: FacebookService) {}

  @Get('connect')
  @UseGuards(AuthGuard('facebook'))
  async connect() {
    return 'facebook connect strategy';
  }

  @Get('login')
  @UseGuards(AuthGuard('facebook-login'))
  async login() {
    return 'facebook connect strategy';
  }

  @Get('callback')
  @UseGuards(AuthGuard('facebook'))
  async callback(@Req() req, @Res() res) {
    await this.facebookService.callback(req, res);
  }

  @Get('login-callback')
  @UseGuards(AuthGuard('facebook-login'))
  async loginCallback(@Req() req, @Res() res) {
    await this.facebookService.loginCallback(req, res);
  }

  @Get('sites')
  @UseGuards(QueryTokenGuard, Auth)
  async getSites(@Req() request: AuthenticatedRequest) {
    const siteData = await this.facebookService.fetchSites(request.bid);

    const sites = siteData.map((site) => ({
      id: site.id,
      name: site.name,
      url: `https://www.facebook.com/profile.php?id=${site.id}`,
      description: site.category,
    }));

    return {
      sites: sites,
    };
  }
}
