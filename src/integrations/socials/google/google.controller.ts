import { Controller, Get, Req, Res, UseGuards } from '@nestjs/common';
import { GoogleService } from './google.service';
import { AuthGuard } from '@nestjs/passport';

@Controller('google')
export class GoogleController {
  constructor(private readonly googleService: GoogleService) {}

  @Get('login')
  @UseGuards(AuthGuard('google'))
  async login() {
    return 'google connect strategy';
  }

  @Get('login-callback')
  @UseGuards(AuthGuard('google'))
  async loginCallback(@Req() req, @Res() res) {
    await this.googleService.loginCallback(req, res);
  }
}
