import { Modu<PERSON> } from '@nestjs/common';
import { GoogleController } from './google.controller';
import { GoogleService } from './google.service';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtService } from '@nestjs/jwt';
import { PaymentsModule } from 'src/payments/payments.module';
import { SlackService } from '@common/services/slack.service';
import { MongooseModule } from '@nestjs/mongoose';
import { SignupAttempt, SignupAttemptSchema } from '@/auth/signup-attempt.model';
import { AuthModule } from '@/auth/auth.module';

@Module({
  controllers: [GoogleController],
  imports: [
    MongooseModule.forFeature([{ name: SignupAttempt.name, schema: SignupAttemptSchema }]),
    UserModule,
    BusinessModule,
    PaymentsModule,
    AuthModule,
  ],
  providers: [GoogleService, JwtService, SlackService],
  exports: [GoogleService],
})
export class GoogleModule {}
