import type { Request, Response } from 'express';

import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { IncomingWebhook } from '@slack/webhook';
import { StripeService } from 'src/payments/stripe.service';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '@auth/auth.service';
import { UserService } from '@user/user.service';
import config from '@common/configs/config';

@Injectable()
export class GoogleService {
  private readonly logger = new Logger(GoogleService.name);
  private readonly slack: IncomingWebhook;

  constructor(
    private readonly configService: ConfigService,
    private readonly stripeService: StripeService,
    private readonly authService: AuthService,
    private userService: UserService,
  ) {
    this.slack = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_MONITOR_SIGNUP'),
    );
  }

  async loginCallback(req: Request, res: Response) {
    const state = JSON.parse(String(req.query.state || '{}'));
    const isAdmin = state.app === 'admin';
    const redirectBase = config().internalApps[isAdmin ? 'blogifyAdmin' : 'blogifyClient'].url;
    try {
      this.logger.debug(req['user']);
      const { email, name, picture, accessToken } = req['user'] as any;
      const user = await this.userService.findByEmail(email);
      if (!user) {
        const stripeCustomer = await this.stripeService.createCustomer(email, name);

        const createdUser = await this.userService.create({
          email,
          password: accessToken,
          name,
          businessName: name,
          stripeId: stripeCustomer.id,
          profilePicture: picture,
        });

        if (process.env.NODE_ENV === 'production') {
          try {
            const slackMessage = {
              text: `${user.email} signed up using Google! :tada:`,
            };
            this.slack.send(slackMessage);
          } catch (error: any) {
            this.logger.error('Slack webhook failed for Google', error?.message);
          }
        }

        const { access_token } = await this.authService.login(createdUser);

        res.redirect(redirectBase + `/login?token=${access_token}`);
      } else {
        if (isAdmin && user.role !== 'superadmin') {
          throw new ForbiddenException();
        }
        const { access_token } = await this.authService.login(user);

        res.redirect(redirectBase + `/login?token=${access_token}`);
      }
    } catch (error: any) {
      res.redirect(
        redirectBase +
          `/login?error=${
            error.code === 11000 ? 'Email Already exist.' : `Signup failed ${error?.message}`
          }`,
      );
    }
  }
}
