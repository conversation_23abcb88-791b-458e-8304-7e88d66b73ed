import { BadRequestException, NotFoundException, Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';

import { getPriceAfterDiscount } from '@/common/utils/price';
import config from '@/common/configs/config';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  private readonly logger = new Logger(this.constructor.name);
  constructor() {
    this.stripe = new Stripe(config().stripe.secreteKey, {
      apiVersion: '2022-11-15',
    });
  }

  async getPrice(priceId: string) {
    try {
      const price = await this.stripe.prices.retrieve(priceId);
      if (!price) {
        throw new Error('Subscription plan not found.');
      }
      return price;
    } catch (e) {
      this.logger.error(e);
      throw new NotFoundException('Subscription plan not found');
    }
  }

  async getIntent(
    intentId: string,
    isTrial?: boolean,
  ): Promise<Stripe.SetupIntent | Stripe.PaymentIntent> {
    return isTrial
      ? this.stripe.setupIntents.retrieve(intentId)
      : this.stripe.paymentIntents.retrieve(intentId, { expand: ['invoice.subscription'] });
  }

  async verifyCoupon(coupon: string, productId: string, price?: number) {
    const stripeCoupon = await this.stripe.coupons.retrieve(coupon, {
      expand: ['applies_to'],
    });

    if (!stripeCoupon?.valid) {
      throw new BadRequestException('Invalid coupon code.');
    }

    if (
      stripeCoupon.applies_to?.products.length &&
      !stripeCoupon.applies_to?.products.find((p) => p === productId)
    ) {
      throw new BadRequestException('Coupon is not applicable for the selected package.');
    }

    const discount = stripeCoupon.amount_off || stripeCoupon.percent_off;
    return {
      ...(price ? { priceAfterDiscount: getPriceAfterDiscount(price, discount) } : {}),
      isPercentDiscount: !!stripeCoupon.percent_off,
      discount,
    };
  }

  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return this.stripe.subscriptions.retrieve(subscriptionId);
  }

  async createSubscription({
    customerId,
    priceId,
    trialPeriod,
    coupon,
  }: {
    customerId: string;
    priceId: string;
    trialPeriod?: number;
    coupon?: string;
  }): Promise<Stripe.Subscription> {
    return this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      off_session: true,
      payment_settings: {
        save_default_payment_method: 'on_subscription',
        payment_method_options: {
          card: {
            request_three_d_secure: 'automatic',
          },
        },
      },
      expand: ['latest_invoice.payment_intent', 'pending_setup_intent'],
      ...(trialPeriod ? { trial_period_days: trialPeriod } : {}),
      ...(coupon ? { coupon } : {}),
    });
  }

  async cancelSubscription(subscriptionId: string, comment?: string) {
    const stripeOptions = {
      cancellation_details: { comment: comment || '' },
    };

    return this.stripe.subscriptions.del(subscriptionId, stripeOptions);
  }
}
