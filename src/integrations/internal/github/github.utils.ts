import type { GitCommitType } from './github.type';

const types: GitCommitType[] = ['feat', 'fix', 'doc', 'style', 'refactor', 'perf', 'test', 'chore'];

const isFormattedCommit = (message: string): boolean =>
  types.some((t) => message.startsWith(`${t}(`));

const isPartiallyFormattedCommit = (message: string): boolean =>
  ['feat', 'fix'].some((t) => message.startsWith(`${t}:`));

const parseCommitMessage = (
  commitMessage: string,
): { type: GitCommitType; scope: string; subject: string; jira: string } => {
  const regex =
    /^(?<type>[a-z]+)(\((?<scope>[^)]+)\))?\s*:\s*(?<subject>[^[]+)(\s*\[(?<jira>[^\]]+)\])?$/i;
  const match = commitMessage.match(regex);

  if (match) {
    const { type, scope, subject, jira } = match.groups;
    return {
      type: type as GitCommitType,
      scope,
      subject: subject.split('\n')[0].trim(),
      jira: jira || null,
    };
  } else {
    return null;
  }
};

const getMessageFromFormattedCommit = (message: string): string => {
  const { type, scope, subject, jira } = parseCommitMessage(message);
  let text = '';

  switch (type) {
    case 'feat':
      text = `Added feature '${subject}' for *${scope}*`;
      break;
    case 'fix':
      text = `Fixed issue with *${scope}*, '${subject}'`;
      break;
    case 'refactor':
      text = `Refactored *${scope}*: ${subject}`;
      break;
    case 'doc':
      text = `Update documentation for *${scope}*: ${subject}`;
      break;
    case 'test':
      text = `Test for *${scope}*: ${subject}`;
      break;
    default:
      text = message;
  }

  if (jira) {
    text += ` [<https://blogifyai.atlassian.net/browse/${jira}|${jira}>]`;
  }

  return text;
};

const getMessageFromPartiallyFormattedCommit = (message: string): string => {
  const [type, subject] = message.split(': ');
  switch (type) {
    case 'feat':
      return `Added feature '${subject}'`;
    case 'fix':
      return `Fixed issue '${subject}'`;
    default:
      return message;
  }
};

export function formatCommitMessageForSlack(message: string) {
  if (isFormattedCommit(message)) {
    return getMessageFromFormattedCommit(message);
  }

  if (isPartiallyFormattedCommit(message)) {
    return getMessageFromPartiallyFormattedCommit(message);
  }

  return message;
}
