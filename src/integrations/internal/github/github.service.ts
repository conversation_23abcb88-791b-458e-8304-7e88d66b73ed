import type { GithubCommitsByAuthor, GithubCommit } from './github.type';

import { Injectable, Logger } from '@nestjs/common';
import { Octokit } from '@octokit/rest';
import moment from 'moment';

import config from '@common/configs/config';

import { GITHUB_DEVELOPERS_AVATARS, GITHUB_OWNER, GITHUB_REPOS } from './github.constants';
import { formatCommitMessageForSlack } from './github.utils';

const owner = GITHUB_OWNER;

@Injectable()
export class GithubService {
  private readonly logger = new Logger(GithubService.name);
  private readonly github: Octokit;

  constructor() {
    this.github = new Octokit({ auth: config().github.authToken });
  }

  async generateDailyUpdates(): Promise<GithubCommitsByAuthor[]> {
    const commits = await this.fetchTodaysCommits();
    const commitsByAuthor: Record<string, GithubCommit[]> = {};
    commits.forEach((commit) => {
      const authorsCommits = commitsByAuthor[commit.author] || [];
      commitsByAuthor[commit.author] = [...authorsCommits, commit];
    });

    const updates: GithubCommitsByAuthor[] = [];
    Object.keys(commitsByAuthor).forEach((author) => {
      const avatar = commitsByAuthor[author][0].avatar;
      const commitsByRepo = commitsByAuthor[author].reduce((obj, commit) => {
        const commits = obj[commit.repo] || [];
        obj[commit.repo] = [...commits, formatCommitMessageForSlack(commit.message)];
        return obj;
      }, {});
      updates.push({ author, avatar, commitsByRepo });
    });

    return updates;
  }

  private async fetchTodaysCommits(): Promise<Map<string, GithubCommit>> {
    const since = moment().subtract(1, 'day').utc().toISOString();
    const until = moment().utc().toISOString();
    const todaysCommits = new Map<string, GithubCommit>();

    try {
      for (let i = 0; i < GITHUB_REPOS.length; i += 1) {
        const repo = GITHUB_REPOS[i];
        const { data: branches } = await this.github.repos.listBranches({ owner, repo });
        for (let j = 0; j < branches.length; j += 1) {
          const branch = branches[j];
          const query = { owner, repo, since, until, sha: branch.name };
          const { data: commits } = await this.github.repos.listCommits(query);
          commits.forEach(({ sha, commit, committer }) => {
            if (!commit.message.includes('Merge')) {
              todaysCommits.set(sha, {
                avatar: GITHUB_DEVELOPERS_AVATARS[commit.author.name] || committer.avatar_url,
                author: commit.author.name,
                message: commit.message,
                repo,
              });
            }
          });
        }
      }

      return todaysCommits;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }
}
