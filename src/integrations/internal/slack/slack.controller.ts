import type { <PERSON>la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SlackCommandRequest } from './slack.type';
import { Controller, UseGuards, Body, Post, Logger, HttpCode, HttpStatus } from '@nestjs/common';
import { <PERSON>ronExpression, Cron } from '@nestjs/schedule';
import { SlackService } from './slack.service';
import { SlackGuard } from './slack.guard';
import { allowedChannels, SlackChannelName, SlackCommand } from './slack.constants';
import validator, { Validator } from './slack.validators';
import formatter, { Formatter } from './slack.formater';
import { includes } from '@/common/utils/array';
import { SlackCommandService } from './slack.service.command';
import { pipe } from 'fp-ts/lib/function';

/**
 * Controller handling Slack command interactions and automated tasks.
 * Manages command processing, validation, and scheduled message sending to Slack channels.
 *
 * @class
 * @description Provides endpoints and scheduled jobs for Slack integration:
 * - Processing Slack commands with validation and formatting
 * - Weekly user count reports
 * - Daily KPI updates with detailed blog status
 *
 */
@Controller('slack')
export class SlackController {
  readonly logger = new Logger(this.constructor.name);
  constructor(
    private readonly slackCommandService: SlackCommandService,
    private readonly slackService: SlackService,
  ) {}

  /**
   * Processes a Slack command through validation, handling, and formatting pipeline
   *
   * @template Command - Type extending SlackCommand
   * @param {object} params - The parameters object
   * @param {string[]} params.args - Arguments passed with the Slack command
   * @param {Command} params.command - The Slack command to process
   * @param {SlackChannelName} params.channel - The Slack channel name
   * @param {Validator} params.validator - Validator object containing validation methods for commands
   * @param {SlackCommandHandler} params.handle - Handler object containing command processing methods
   * @param {Formatter} params.format - Formatter object containing formatting methods for command responses
   *
   * @returns - Resolves when message is sent to Slack
   *
   */
  private process = <Command extends SlackCommand>({
    args,
    command,
    channel,
    validator,
    handle,
    format,
  }: {
    args: string[];
    command: Command;
    channel: SlackChannelName;
    validator: Validator;
    handle: SlackCommandHandler;
    format: Formatter;
  }) =>
    pipe(
      args,
      (args) => validator[command](...args),
      (args) => handle[command](...args),
    )
      .then(format[command])
      .then((blocks) =>
        this.slackService.sendMessage({
          message: `Response to ${command}`,
          channel,
          blocks,
        }),
      )
      .catch((e) =>
        this.slackService.sendMessage({
          channel,
          message: `🚨 Error: ${e.message}`,
        }),
      );

  /**
   * Endpoint for handling all Slack command interactions.
   * Processes incoming Slack commands and validates them against allowed channels.
   *
   * @param {SlackCommandRequest} body - The request body from Slack containing command details
   * @throws {Error} When the command is not allowed in the specified channel
   * @returns {Object} Response object with text and response_type
   * @returns {string} response.text - Either a processing message or error message
   * @returns {string} response.response_type - 'ephemeral' to show message only to the user
   *
   */
  @UseGuards(SlackGuard)
  @HttpCode(HttpStatus.ACCEPTED)
  @Post('command')
  async respond(@Body() body: SlackCommandRequest) {
    try {
      const command = body.command.slice(1) as SlackCommand;
      const args = body.text.trim().split(/\s+/);
      if (!includes(allowedChannels[command], body.channel_name)) {
        throw new Error('This command is not allowed in this channel.');
      }
      const channel = body.channel_name;
      this.process({
        args,
        command,
        channel,
        validator,
        handle: this.slackCommandService,
        format: formatter,
      });
      return {
        text: '⌛ Processsing...',
        response_type: 'ephemeral',
      };
    } catch (e) {
      return { text: `❌ ${e.message}`, response_type: 'ephemeral' };
    }
  }

  /**
   * Sends a weekly users count message to the Slack 'kpi' channel.
   * The message includes formatted blocks containing user count statistics.
   *
   * @async
   * @returns A promise that resolves when the message is sent successfully
   * @throws {Error} If there is an error retrieving user counts or sending the Slack message
   */
  @Cron(CronExpression.EVERY_WEEK, { timeZone: 'UTC' })
  async weeklyUsersCounts() {
    await this.slackService.sendMessage({
      blocks: await this.slackCommandService.users_counts().then(formatter['users_counts']),
      channel: 'kpi',
      message: 'Weekly Users Counts',
    });
  }

  /**
   * Sends daily KPI (Key Performance Indicators) updates to a Slack channel.
   *
   * This method performs the following operations:
   * 1. Retrieves KPI data from the slack command service
   * 2. Sends a main KPI message to the 'kpi' channel
   * 3. Creates a thread with details about failed and in-progress blogs
   * 4. For each blog in these categories, sends detailed status information in the thread
   *
   * @returns  A promise that resolves when all messages have been sent
   *
   * @throws {Error} May throw an error if Slack API calls fail or if there are issues
   * retrieving blog status information
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, { timeZone: 'UTC' })
  async dailyKPI() {
    const data = await this.slackCommandService.kpi();
    const { ts } = await this.slackService.sendMessage({
      blocks: formatter['kpi'](data),
      channel: 'kpi',
      message: 'Daily KPI Updates',
    });
    const blogs = { failed: data.failed, inProgress: data.inProgress };
    for (const status of ['failed', 'inProgress'] as const) {
      await this.slackService.sendMessage({
        message: `~~~~~~~Blogs of status: *${status}(${blogs[status].length})*`,
        channel: 'kpi',
        thread_ts: ts,
      });
      await Promise.all(
        blogs[status].map(async (id) =>
          this.slackService.sendMessage({
            blocks: await this.slackCommandService.blog_status(id).then(formatter['blog_status']),
            channel: 'kpi',
            message: 'Blog details for BlogID:' + id,
            thread_ts: ts,
          }),
        ),
      );
    }
  }
}
