// بسم الله الرحمن الرحيم

import moment from 'moment';

export const WORD_TO_SINCE_UNTIL: Record<string, { start: moment.Moment; end: moment.Moment }> = {
  today: { start: moment().startOf('day'), end: moment() },
  month: { start: moment().startOf('month'), end: moment() },
  year: { start: moment().startOf('year'), end: moment() },
  yesterday: {
    start: moment().subtract(1, 'day').startOf('day'),
    end: moment().subtract(1, 'day').endOf('day'),
  },
  last_month: {
    start: moment().subtract(1, 'month').startOf('month'),
    end: moment().subtract(1, 'month').endOf('month'),
  },
  last_year: {
    start: moment().subtract(1, 'year').startOf('year'),
    end: moment().subtract(1, 'year').endOf('year'),
  },
} as const;

export const SLACK_CHANNELS = {
  'monitor-blog-create': 'C0560N3Q1QA',
  'blogify-alerts-dev': 'C08T92DLC5B',
  'blogify-command-o': 'C06B97W1AG5',
  'payout-requests': 'C089KJWCRFC',
  'dev-monitor': 'C063ECU22JE',
  'dev-slack': 'C0699409ULE',
  kpi: 'C05E2PA6UPM',
} as const satisfies Record<string, `C${string}`>;

export type SlackChannelName = keyof typeof SLACK_CHANNELS;

/**
 * @description This constant defines the allowed Slack channels for each command.
 * It is a record where the key is the command name and the value is an array of allowed Slack channel names.
 * The `satisfies` keyword ensures that the value conforms to the `Record<string, readonly SlackChannelName[]>` type.
 *
 * @example
 * ```typescript
 *  allowedChannels.blog_list // ['blogify-command-o', 'kpi']
 * ```
 */
export const allowedChannels = {
  blog_list: ['blogify-command-o'],
  blog_details: ['blogify-command-o'],
  blog_status: ['blogify-command-o'],
  business_add_credit: ['blogify-command-o'],
  business_add_extra_credit: ['blogify-command-o'],
  kpi: ['blogify-command-o', 'kpi'],
  source_toggle: ['blogify-command-o'],
  ping: ['blogify-command-o'],
  user_details: ['blogify-command-o'],
  users_counts: ['blogify-command-o', 'kpi'],
} as const satisfies Record<string, readonly SlackChannelName[]>;

export type SlackCommand = keyof typeof allowedChannels;
