// بسم الله الرحمن الرحيم

import { <PERSON><PERSON>, Positive<PERSON><PERSON>ger, SlackCommandHandler } from './slack.type';
import type { UserDocument } from '@user/user.model';
import type { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import moment from 'moment';

import { CreditTransactionService } from '@resources/credit-transaction/credit-transaction.service';
import { ALL_SUBSCRIPTION_PLANS } from '@common/constants';
import { mongoDateRange } from '@common/utils/db';
import { Business } from '@business/business.model';
import { Addon } from '@resources/product/product.model';
import { Blog } from '@blog/blog.model';
import { User } from '@user/user.model';
import { BlogSource } from '@/resources/blog-source/blog-source.model';
import { BlogSourceStatus } from '@/resources/blog-source/blog-source.enums';
import { BlogSourceName } from '@/blog/blog.enums';
import mongoose from 'mongoose';
import { Transaction } from '@/resources/transaction/transaction.model';

/**
 * @fileoverview This file defines the SlackCommandService class, which implements the SlackCommandHandler interface.
 * It provides methods to handle various commands related to blogs, users, and business operations.
 * The service interacts with MongoDB models for Business, Blog, BlogSource, and User, and utilizes the CreditTransactionService.
 */
@Injectable()
export class SlackCommandService implements SlackCommandHandler {
  constructor(
    @InjectModel(Transaction.name) private readonly transaction: Model<Transaction>,
    @InjectModel(Business.name) private readonly business: Model<Business>,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    @InjectModel(BlogSource.name) private readonly blogSource: Model<BlogSource>,
    @InjectModel(User.name) private readonly user: Model<User>,
    private readonly creditTransactionService: CreditTransactionService,
  ) {}

  /**
   * Simple health check endpoint
   * @returns 'Pong' if the service is running
   */
  async ping() {
    return 'Pong';
  }

  /**
   * Retrieves the most recent blogs for a user by their email
   * @param email - Email address of the user
   * @returns Array of the 10 most recent blog documents
   * @throws Error if user is not found or has no blogs
   */
  async blog_list(email: Email) {
    const user = await this.user.findOne({ email }).select({ business: 1 });
    if (!user) throw new Error('User not found');

    const blogs = await this.blog.find({ bid: user.business }).sort({ createdAt: -1 }).limit(10);

    if (!blogs?.length) throw new Error(`User has not created any blogs yet.`);

    return blogs;
  }

  /**
   * Retrieves the status of a specific blog
   * @param blogId - MongoDB ObjectId of the blog
   * @returns Blog document with status information
   * @throws Error if blog is not found
   */
  async blog_status(blogId: mongoose.Types.ObjectId) {
    const blog = await this.blog.findById(blogId).lean();
    if (!blog) throw new Error('Blog not found');

    return blog;
  }

  /**
   * Retrieves detailed information about a specific blog
   * @param blogId - MongoDB ObjectId of the blog
   * @returns Complete blog document
   * @throws Error if blog is not found
   */
  async blog_details(blogId: mongoose.Types.ObjectId) {
    const blog = await this.blog.findById(blogId).lean();
    if (!blog) throw new Error('Blog not found');

    return blog;
  }

  /**
   * Adds monthly credits to a user's business account
   * @param email - Email address of the user
   * @param amount - Number of credits to add
   * @returns Updated user details after credit addition
   * @throws Error if user is not found or credit addition fails
   */
  async business_add_credit(email: Email, amount: PositiveInteger) {
    const user = await this.user.findOne({ email }).select({ business: 1 });
    if (!user) throw new Error('User not found');

    const bid = user.business;
    try {
      await this.creditTransactionService.addMonthlyCredits(
        String(bid),
        parseInt(String(amount), 10),
        'Added by Blogify Support',
      );
      return this.user_details(email);
    } catch (e) {
      throw new Error(`Error adding ${amount} credit to business ${bid} ${e?.message}`);
    }
  }

  /**
   * Adds extra (additional) credits to a user's business account
   * @param email - Email address of the user
   * @param amount - Number of additional credits to add
   * @returns Updated user details after credit addition
   * @throws Error if user is not found or credit addition fails
   */
  async business_add_extra_credit(email: Email, amount: PositiveInteger) {
    const user = await this.user.findOne({ email }).select({ business: 1 });
    if (!user) throw new Error('User not found');

    const bid = user.business;
    try {
      await this.creditTransactionService.addAdditionalCredits(
        String(bid),
        parseInt(String(amount), 10),
        'Added by Blogify Support',
      );
      return this.user_details(email);
    } catch (e) {
      throw new Error(`Error adding ${amount} additional credit to business ${bid} ${e?.message}`);
    }
  }

  /**
   * Generates Key Performance Indicators for the last 24 hours
   * @returns Object containing transaction stats, user stats, and blog stats
   * - Transaction stats: new subscribers for different addons
   * - User stats: new users and subscribers
   * - Blog stats: successful, failed, and in-progress blogs
   */
  async kpi() {
    const now = moment();
    const end = now.utc().toDate();
    const start = now.subtract(24, 'hours').utc().toDate();

    const getTransactionStats = async (since: Date, until: Date) => {
      const pipeline = [
        { $match: { status: 'confirmed', ...mongoDateRange(since, until) } },
        { $group: { _id: '$description', count: { $sum: 1 } } },
      ];
      const result: { _id: string; count: number }[] = await this.transaction.aggregate(pipeline);

      const newSnippetSubscribers = result.find((a) => a._id === Addon.Snippet)?.count || 0;
      const newYoutubeSubscribers = result.find((a) => a._id === Addon.YouTube)?.count || 0;
      const newYoutubeProSubscribers = result.find((a) => a._id === Addon.YouTubePro)?.count || 0;

      return { newSnippetSubscribers, newYoutubeSubscribers, newYoutubeProSubscribers };
    };
    const getNewUserStats = async (since: Date, until: Date) => {
      const newUsersQuery = [{ $match: mongoDateRange(since, until) }, { $count: 'count' }];
      const newSubscribers = [
        {
          $match: {
            ...mongoDateRange(since, until),
            subscriptionPlan: { $nin: ['NOPLAN', 'FREE'] },
          },
        },
        { $count: 'count' },
      ];

      const pipeline = [
        {
          $facet: {
            newUsers: newUsersQuery,
            newSubscribers: newSubscribers,
          },
        },
        {
          $project: {
            newUsers: { $arrayElemAt: ['$newUsers.count', 0] },
            newSubscribers: { $arrayElemAt: ['$newSubscribers.count', 0] },
          },
        },
      ];
      const result = (await this.business.aggregate(pipeline))[0];

      return {
        newUsers: result.newUsers || 0,
        newSubscribers: result.newSubscribers || 0,
      };
    };
    const fetchBlogStats = async (since: Date, until: Date) => {
      const initialState = {
        successful: [],
        failed: [],
        inProgress: [],
      };

      const pipeline = [
        { $match: mongoDateRange(since, until) },
        { $group: { _id: '$status', blogs: { $addToSet: '$_id' } } },
      ];
      const result: { _id: string; count: number; blogs: string[] }[] =
        await this.blog.aggregate(pipeline);

      const wasSuccess = (status: string) =>
        ['publisher_dispatched', 'blog_published', 'social_published', 'completed'].includes(
          status,
        );

      const wasFailed = (status: string) => status.endsWith('failed');

      const classify = (status: string): keyof typeof initialState => {
        if (wasFailed(status)) return 'failed';
        else if (wasSuccess(status)) return 'successful';
        else return 'inProgress';
      };

      return result.reduce((acc, { _id: status, blogs }) => {
        acc[classify(status)].push(...blogs);
        return acc;
      }, initialState);
    };
    const transactionStats = await getTransactionStats(start, end);
    const userStats = await getNewUserStats(start, end);
    const blogStats = await fetchBlogStats(start, end);

    return { ...userStats, ...transactionStats, ...blogStats } as const;
  }

  /**
   * Toggles the status of a blog source between Active and Inactive
   * @param source - The name of the blog source to toggle
   * @returns The updated blog source document
   * @throws Error if the blog source is not found
   */
  async source_toggle(source: BlogSourceName) {
    const blogSource = (
      await this.blogSource.find({ name: { $regex: new RegExp(source, 'i') } })
    )[0];
    switch (blogSource.status) {
      case BlogSourceStatus.Active:
        blogSource.status = BlogSourceStatus.Inactive;
        break;
      case BlogSourceStatus.Inactive:
        blogSource.status = BlogSourceStatus.Active;
        break;
    }
    return blogSource.save();
  }

  /**
   * Retrieves subscription plan statistics and active user counts
   * @returns Object containing subscription data and active subscriber count
   * - subscriptionData: Array of objects with plan titles and subscriber counts
   * - activeSubscriberCount: Number of users who created blogs in last 30 days
   */
  async users_counts() {
    /**
     * Fetches the number of subscribers to all plans of Blogify.ai
     * @returns An array of all plans and their number of subscribers
     */
    const getSubscriptionPlanData = async () => {
      const plans = await this.business.aggregate([
        {
          $group: {
            _id: '$subscriptionPlan',
            count: {
              $sum: 1,
            },
          },
        },
        {
          $sort: {
            _id: 1,
          },
        },
      ]);
      const subscriptionData = plans.map(
        (plan: { _id: keyof typeof ALL_SUBSCRIPTION_PLANS; count: number }) => {
          const name = plan._id
            .split('_')
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
          return {
            title: name,
            count: plan.count,
          };
        },
      );
      return subscriptionData;
    };

    /**
     * Counts the number of users who have created a blog in the last 30 days
     * @returns The number of such users
     */
    const getActiveSubscriberCount = async () => {
      const businessIds = await this.blog.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(new Date().setDate(new Date().getDate() - 30)),
              $lte: new Date(),
            },
          },
        },
        {
          $group: {
            _id: '$bid',
          },
        },
        {
          $count: 'uniqueIDs',
        },
      ]);
      return businessIds.shift().uniqueIDs as number;
    };
    return {
      subscriptionData: await getSubscriptionPlanData(),
      activeSubscriberCount: await getActiveSubscriberCount(),
    };
  }

  /**
   * Retrieves detailed user information including associated business data
   * @param email - Email address of the user to look up
   * @returns User document populated with business information
   * @throws Error if user is not found
   */
  async user_details(email: Email) {
    const user: UserDocument = await this.user.findOne({ email }).populate('business');
    if (!user) throw new Error('User not found');

    return user;
  }
}
