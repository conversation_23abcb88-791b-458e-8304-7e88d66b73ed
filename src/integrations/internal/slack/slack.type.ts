import { Slack<PERSON><PERSON><PERSON><PERSON><PERSON>, SlackCommand } from './slack.constants';

// https://api.slack.com/reference/block-kit/blocks#header
type Header = {
  type: 'header';
  text: Extract<Text, { type: 'plain_text' }>;
  block_id?: string; // max length: 255 && should be unique
};

// https://api.slack.com/reference/block-kit/blocks#divider
type Divider = {
  type: 'divider';
  block_id?: string; // max length: 255 && should be unique
};

// https://api.slack.com/reference/block-kit/composition-objects#text
type Text =
  | {
      type: 'plain_text';
      text: string; // min length: 1, max length: 3000
    }
  | {
      type: 'mrkdwn';
      text: string; // min length: 1, max length: 3000
      verbatim?: boolean;
    };

// https://api.slack.com/reference/block-kit/blocks#section
type Section = {
  type: 'section';
  text?: Text; // You can provide either text or fields or both
  fields?: Text[]; // Up to 10 items in the Array, each text in an item can be max 2000 characters
  block_id?: string; // max 255 characters & should be unique
  expand?: boolean;
};

/**
 * Represents an array of Slack block elements that can be used to compose messages.
 * Can contain {@link Header}, {@link Section}, {@link Text}, or {@link Divider} blocks.
 * @see https://api.slack.com/reference/block-kit/blocks
 */
export type Blocks = Array<Header | Section | Text | Divider>;

/**
 * @description Defines a type for Slack command handlers. It's a record where the keys are Slack commands
 * and the values are asynchronous functions that handle those commands.
 * Each handler function accepts a variable number of arguments and returns a Promise.
 */
export type SlackCommandHandler = Record<
  SlackCommand,
  (...args: ReadonlyArray<any>) => Promise<any>
>;

export type Email = string & { readonly __brand: unique symbol };
export type PositiveInteger = number & { readonly __brand: unique symbol };

export interface SlackCommandRequest {
  token: string;
  team_id: string;
  team_domain: 'pixelshadow-inc';
  channel_id: string;
  channel_name: SlackChannelName | string;
  user_id: string;
  user_name: string;
  command: `/${SlackCommand}`;
  text: string;
  api_app_id: string;
  is_enterprise_install: 'true' | 'false';
  response_url: string;
  trigger_id: string;
}
