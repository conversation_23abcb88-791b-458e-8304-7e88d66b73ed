import type {
  ChatPostMessageArguments,
  ChatPostMessageResponse,
  ChatUpdateArguments,
  ChatUpdateResponse,
} from '@slack/web-api';
import type { Blog } from '@/blog/blog.model';

import { Injectable, Logger } from '@nestjs/common';
import { WebClient } from '@slack/web-api';

import config from '@common/configs/config';

import { SLACK_CHANNELS, SlackChannelName } from './slack.constants';

const icon_url =
  'https://avatars.slack-edge.com/2025-01-20/8343905287728_239cee060b35a7d8ba7a_512.png';

const getStatusEmoji = ({ content, status }: Blog) => {
  if (status === 'content_generated' || content?.length > 500) {
    return '✅';
  } else if (status === 'content_generation_failed') {
    return '❌';
  } else {
    return '🟡';
  }
};

const getContentGenerationStatus = ({ content, status }: Blog) => {
  if (status === 'content_generated' || content?.length > 500) {
    return 'Generated';
  } else if (status === 'content_generation_failed') {
    return 'Failed';
  } else {
    return 'Generating...';
  }
};

const sanitizedBlocks = (blocks: ChatPostMessageArguments['blocks']) =>
  blocks?.filter((block) => {
    if ('fields' in block) {
      return Array.isArray(block.fields) && block.fields.length > 0;
    }
    return true;
  });

@Injectable()
export class SlackService {
  private readonly logger = new Logger(SlackService.name);
  private readonly slack: WebClient;

  constructor() {
    this.slack = new WebClient(config().slack.botToken);
  }

  async sendMessage({
    channel,
    message,
    blocks,
    thread_ts,
  }: {
    channel: SlackChannelName;
    message: string;
  } & ChatPostMessageArguments): Promise<ChatPostMessageResponse> {
    if (!config().slack.botToken || !config().isProd) {
      return Promise.resolve({}) as any;
    }

    try {
      return await this.slack.chat.postMessage({
        text: message,
        channel: SLACK_CHANNELS[channel].toUpperCase(),
        blocks: sanitizedBlocks(blocks),
        username: 'Blogify',
        thread_ts,
        icon_url,
      });
    } catch (e) {
      this.logger.warn('Slack Send Message Failed:', e);
    }
  }

  async updateMessage({
    channel,
    message,
    ts,
    blocks,
  }: {
    channel: SlackChannelName;
    message: string;
  } & ChatUpdateArguments): Promise<ChatUpdateResponse> {
    if (!config().slack.botToken || !config().isProd) {
      return Promise.resolve({}) as any;
    }

    try {
      return await this.slack.chat.update({
        text: message,
        channel: SLACK_CHANNELS[channel].toUpperCase(),
        blocks: sanitizedBlocks(blocks),
        username: 'Blogify',
        icon_url,
        ts,
      });
    } catch (e) {
      this.logger.warn('Slack Update Message Failed', e);
    }
  }

  sendBlogCreateAlert(blog: Blog, extras: { bid: string; email: string; attemptsMade: number }) {
    const { message, blocks } = this.getBlogMessageAndBlocks(blog, extras);

    return this.sendMessage({ message, blocks, channel: 'monitor-blog-create' });
  }

  updateBlogCreateAlert(blog: Blog, ts: string, extras: { bid: string; email: string }) {
    const { message, blocks } = this.getBlogMessageAndBlocks(blog, extras);

    return this.updateMessage({
      channel: 'monitor-blog-create',
      message,
      blocks,
      ts,
    });
  }

  private getBlogMessageAndBlocks(
    blog: Blog,
    { bid, email, attemptsMade }: { bid: string; email: string; attemptsMade?: number },
  ) {
    const generationMode = blog.generationMode === 'auto' ? 'Auto' : 'Co-Pilot';

    const message = `Blog generation request:\n*Business ID:* ${bid}\n*Email:* ${email}\n*Blog ID:* ${blog._id}`;

    const blocks = [
      { type: 'section', text: { type: 'mrkdwn', text: message } },
      {
        type: 'section',
        fields: [
          { type: 'mrkdwn', text: `*Source Type:* ${blog.sourceType}` },
          { type: 'mrkdwn', text: `*Source Name:* ${blog.sourceName}` },

          { type: 'mrkdwn', text: `*Source Language:* ${blog.inputLanguage}` },
          { type: 'mrkdwn', text: `*Blog Language:* ${blog.blogLanguage}` },

          { type: 'mrkdwn', text: `*Size:* ${blog.blogSize}` },
          { type: 'mrkdwn', text: `*Tone:* ${blog.blogTone}` },

          { type: 'mrkdwn', text: `*Mode:* ${generationMode}` },
          { type: 'mrkdwn', text: `*Status:* ${blog.status}` },
        ],
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Content Status:*${getStatusEmoji(blog)} ${getContentGenerationStatus(blog)}`,
          },
        ],
      },
    ];

    if (blog.title) {
      blocks[0].text.text += `\n*Title:* ${blog.title.substring(0, 75)}`;
    }

    if (attemptsMade) {
      blocks[2].fields.push({ type: 'mrkdwn', text: `*Attempts:* ${attemptsMade}` });
    }

    return { blocks, message };
  }
}
