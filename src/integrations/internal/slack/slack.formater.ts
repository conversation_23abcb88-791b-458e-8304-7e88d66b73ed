// بسم الله الرحمن الرحيم

import { SlackCommand } from './slack.constants';
import moment from 'moment';
import { SlackCommandService } from './slack.service.command';
import { BlogSourceStatus } from '@/resources/blog-source/blog-source.enums';
import { Blocks } from './slack.type';
import { capitalize } from 'lodash';

export type Formatter = {
  [K in SlackCommand]: (data: Awaited<ReturnType<SlackCommandService[K]>>) => Blocks;
};
/**
 * Configuration object containing methods to format Slack messages
 *
 * Implements formatting methods for Slack message components including:
 * - Text formatting (bold, italic, strikethrough)
 * - Block elements
 * - Interactive components
 * - Message layouts
 */

const format: Formatter = function () {
  throw new Error(
    'This function is never to be called. It is simply used to allow adding properties to an object in a type-safe manner and also for properties to reference each other',
  );
};

export default format;

format.ping = (reply: string) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `Pong! ${reply}!`,
      },
    },
  ];

format.blog_list = (blogs) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `📚 Recent Blogs (${blogs.length})`,
      },
    },
    ...blogs
      .map(
        (blog, index) =>
          <Blocks>[
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: `_#${index + 1}_ *🆔:* <https://blogify.ai/dashboard/blogs/${blog._id}|${blog._id}>`,
              },
            },
            {
              type: 'section',
              fields: [
                {
                  type: 'mrkdwn',
                  text: `*Title:* ${blog.title}`,
                },
                {
                  type: 'mrkdwn',
                  text: `*Status:* ${blog.status}`,
                },
                {
                  type: 'mrkdwn',
                  text: `*Generation Mode:* ${capitalize(blog.generationMode)}`,
                },
                {
                  type: 'mrkdwn',
                  text: `*Source Type:* ${capitalize(blog.sourceType)}`,
                },
                {
                  type: 'mrkdwn',
                  verbatim: true,
                  text: `*Source:* ${blog.url?.length ? `<${blog.url}|${blog.sourceName}>` : blog.prompt?.length ? blog.prompt.slice(0, 50) + '...' : 'N/A'}`,
                },
                {
                  type: 'mrkdwn',
                  text: `*Created (UTC):* ${moment(blog['createdAt']).utc().format('LLL')}`,
                },
              ],
            },
          ],
      )
      .reduce(
        (acc, block) =>
          acc.concat(
            [
              {
                type: 'divider',
              },
            ],
            block,
          ),
        [],
      ),
  ];

format.blog_details = (blog) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `✨ Blog ID: ${blog._id}`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '🕒 *Timestamps* (UTC)',
      },
      fields: [
        { type: 'mrkdwn', text: '*Created*\n' + moment(blog['createdAt']).utc().format('LLL') },
        { type: 'mrkdwn', text: '*Updated*\n' + moment(blog['updatedAt']).utc().format('LLL') },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '🔑 *Identifiers*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Business 🆔*\n' + (blog.bid.toString() || 'N/A') },
        { type: 'mrkdwn', text: '*User 🆔*\n' + (blog.uid?.toString() || 'N/A') },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: 'ℹ️ *Status Information*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Blog Status*\n' + blog.status },
        { type: 'mrkdwn', text: '*Generation Mode*\n' + capitalize(blog.generationMode) },
      ],
    },
    {
      type: 'section',
      text: { type: 'mrkdwn', text: '*Current Status*\n' + blog.generationStatus },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '❌ *Error Information*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Error Code*\n' + (blog.errorCode || 'N/A') },
        { type: 'mrkdwn', text: '*Fail Reason*\n' + (blog.failReason || 'N/A') },
        { type: 'mrkdwn', text: '*Failed Queue*\n' + (blog.failedQueue || 'N/A') },
        { type: 'mrkdwn', text: '*Total Retries*\n' + String(blog.totalRetries) },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '💡 *Source Information*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Source Type*\n' + blog.sourceType },
        { type: 'mrkdwn', text: '*Source URL*\n' + ((blog.url && `<${blog.url}|URL>`) || 'N/A') },
        { type: 'mrkdwn', text: '*Source Name*\n' + blog.sourceName },
        {
          type: 'mrkdwn',
          text:
            '*Source Prompt*\n' +
            (blog.prompt
              ? blog.prompt.length > 100
                ? blog.prompt.slice(0, 100).concat(' [TRUNCATED]')
                : blog.prompt
              : 'N/A'),
        },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '🌐 *Language Settings*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Input Language*\n' + blog.inputLanguage },
        { type: 'mrkdwn', text: '*Blog Language*\n' + capitalize(blog.blogLanguage) },
      ],
    },
  ];

format.blog_status = (blog) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `ℹ️ Blog ID: ${blog._id}`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '*Status Information*',
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Source Name*: ${blog.sourceName}`,
        },
        {
          type: 'mrkdwn',
          text: `*Current Status:* ${blog.generationStatus}`,
        },
        {
          type: 'mrkdwn',
          text: `*Blog Status:* ${blog.status}`,
        },
        {
          type: 'mrkdwn',
          text: `*Generation Mode:* ${capitalize(blog.generationMode)}`,
        },
        {
          type: 'mrkdwn',
          text: `*Total Retries:* ${blog.totalRetries}`,
        },
        {
          type: 'mrkdwn',
          text: `*Fail Reason:* ${blog.failReason ?? 'N/A'}`,
        },
        {
          type: 'mrkdwn',
          text: `*Failed Queue:* ${blog.failedQueue ?? 'N/A'}`,
        },
        {
          type: 'mrkdwn',
          text: `*Created (UTC):* ${moment(blog['createdAt']).utc().format('LLL')}`,
        },
      ],
    },
  ];

format.user_details = (user) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `🎭 User Details: ${user.name}`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: 'ℹ️ *Basic Information*',
      },
      fields: [
        { type: 'mrkdwn', text: '*ID*' },
        { type: 'plain_text', text: String(user._id) },
        { type: 'mrkdwn', text: '*Email*' },
        { type: 'plain_text', text: user.email },
        { type: 'mrkdwn', text: '*Name*' },
        { type: 'plain_text', text: user.name },
        { type: 'mrkdwn', text: '*Role*' },
        { type: 'plain_text', text: capitalize(user.role) },
        { type: 'mrkdwn', text: '*Joined*' },
        {
          type: 'plain_text',
          text: moment(user['createdAt']).utc().format('LLL'),
        },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '💼 *Business Information*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Business 🆔*' },
        { type: 'plain_text', text: String(user.business._id) },
        { type: 'mrkdwn', text: '*Total Credits*' },
        { type: 'plain_text', text: String(user.business.credits) },
        { type: 'mrkdwn', text: '*Plan*' },
        { type: 'plain_text', text: user.business.subscriptionPlan },
        { type: 'mrkdwn', text: '*Custom Plan*' },
        { type: 'plain_text', text: user.business.customPlan || 'N/A' },
        { type: 'mrkdwn', text: '*Subscription Status*' },
        { type: 'plain_text', text: capitalize(user.business.subscriptionStatus) },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '📕 *Account Status*',
      },
      fields: [
        { type: 'mrkdwn', text: '*Status*' },
        { type: 'plain_text', text: capitalize(user.status) },
        { type: 'mrkdwn', text: '*Verified*' },
        { type: 'plain_text', text: user.verified ? '✅' : '❌' },
      ],
    },
  ];

format.business_add_credit = format.user_details;

format.business_add_extra_credit = format.user_details;

format.kpi = ({
  newUsers,
  newSubscribers,
  newSnippetSubscribers,
  newYoutubeSubscribers,
  newYoutubeProSubscribers,
  successful,
  inProgress,
  failed,
}) => {
  const blocks: Blocks = [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: '📈 KPI Metrics Report',
      },
    },
  ];

  // User Stats
  if (newUsers || newSubscribers) {
    blocks.push(
      { type: 'divider' },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*User Acquisitions* 👥',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*⭐ New Users:* ${newUsers || 0}`,
          },
          {
            type: 'mrkdwn',
            text: `*💰 New Subscribers:* ${newSubscribers || 0}`,
          },
        ],
      },
    );
  }

  // Addon Stats
  if (newSnippetSubscribers || newYoutubeSubscribers || newYoutubeProSubscribers) {
    blocks.push(
      { type: 'divider' },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '🔌 *Addon Subscribers*',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `▶ *YouTube Connect:* ${newYoutubeSubscribers || 0}`,
          },
          {
            type: 'mrkdwn',
            text: `▶️ *YouTube Connect Pro:* ${newYoutubeProSubscribers || 0}`,
          },
          {
            type: 'mrkdwn',
            text: `📝 *Writing Snippets:* ${newSnippetSubscribers || 0}`,
          },
        ],
      },
    );
  }

  // Blog Stats
  const totalBlogs = successful.length + inProgress.length + failed.length;
  if (totalBlogs > 0) {
    blocks.push(
      { type: 'divider' },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Blog Generations* :blogify:',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `🚩 *Total Blogs:* ${totalBlogs}`,
          },
          {
            type: 'mrkdwn',
            text: `✅ *Successful:* ${successful.length}`,
          },
          {
            type: 'mrkdwn',
            text: `⏳ *In Progress:* ${inProgress.length}`,
          },
          {
            type: 'mrkdwn',
            text: `❌ *Failed:* ${failed.length}`,
          },
        ],
      },
    );
  }

  return blocks;
};

format.source_toggle = ({ displayName, type, status, iconUrl }) =>
  <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `🔄 Source Updated`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Name:* <${iconUrl}|${displayName}>`,
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Source Type:* ${capitalize(type)}`,
        },
        {
          type: 'mrkdwn',
          text: `*Status:* ${status === BlogSourceStatus.Active ? '✅' : '⛔'}`,
        },
      ],
    },
  ];

format.users_counts = ({ activeSubscriberCount, subscriptionData }) => {
  const packageMessage = (prefix: 'Lifetime' | 'Monthly' | 'Yearly' | 'Free', emoji: string) => {
    const packageData = subscriptionData.filter(({ title }) => title.startsWith(prefix));
    return [
      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `${emoji} *${prefix} Users*: ${packageData.reduce(
            (acc, { count }) => acc + count,
            0,
          )}`,
        },
      },
      {
        type: 'section',
        fields: packageData.map(({ title, count }) => ({
          type: 'mrkdwn',
          text: `${title}: ${count}`,
        })),
      },
    ];
  };

  return <Blocks>[
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: '📶 User Statistics',
        emoji: true,
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `✏️ *Total Active Users*: ${activeSubscriberCount}`,
        },
      ],
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '*Subscription List*:',
      },
    },
    ...packageMessage('Lifetime', '♾️'),
    ...packageMessage('Monthly', '🌒'),
    ...packageMessage('Yearly', '📅'),
    ...packageMessage('Free', '🆓'),
  ];
};
