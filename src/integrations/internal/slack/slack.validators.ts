// بسم الله الرحمن الرحيم

import mongoose from 'mongoose';
import { SlackCommand } from './slack.constants';
import { SlackCommandService } from './slack.service.command';
import { BLOG_SOURCE_NAMES } from '@/blog/blog.constants';
import { BlogSourceName } from '@/blog/blog.enums';
import { z } from 'zod';
import { Email, PositiveInteger } from './slack.type';

/**
 * Performs validation checks for commonly used parameters to slack commands.
 */
const check = function () {
  throw new Error(
    'This function is never to be called. It is simply used to allow adding properties to an object in a type-safe manner and also for properties to reference each other',
  );
};
check.email = z
  .string()
  .email()
  .describe('email')
  .transform((v) => v as Email);
check.id = z
  .string()
  .refine((value) => mongoose.Types.ObjectId.isValid(value), {
    message: 'Invalid ObjectId',
  })
  .describe('id')
  .transform((v) => new mongoose.Types.ObjectId(v));
check.credit = z
  .string()
  .pipe(z.coerce.number().positive().int())
  .describe('credits')
  .transform((v) => v as PositiveInteger);

/**
 * @description Defines a type `Validator` which is an object that maps each `SlackCommand` to a validator function.
 * Each validator function takes a variable number of string arguments and returns a read-only tuple representing the parameters
 * expected by the corresponding method in `SlackCommandService`.
 */
export type Validator = {
  [K in SlackCommand]: (...args: string[]) => Readonly<Parameters<SlackCommandService[K]>>;
};

/**
 * The validator functions for Slack commands arguments.
 *
 * This function is intended to validate input or configurations
 * related to the Slack integration. The specific validation logic
 * should be implemented within this function.
 *
 */
const validator: Validator = function () {
  throw new Error(
    'This function is never to be called. It is simply used to allow adding properties to an object in a type-safe manner and also for properties to reference each other',
  );
};
export default validator;
validator.user_details = (email: string) => <const>[check.email.parse(email)];
validator.blog_list = validator.user_details;
validator.blog_details = (blogID: string) => <const>[check.id.parse(blogID)];
validator.blog_status = validator.blog_details;
validator.business_add_credit = (email: string, amount: string) =>
  <const>[check.email.parse(email), check.credit.parse(amount)];
validator.business_add_extra_credit = validator.business_add_credit;
validator.source_toggle = (name: string) =>
  <const>[
    z
      .enum(
        BLOG_SOURCE_NAMES.filter((name) => name.split(/\s+/).length === 1).map((name) =>
          name.toLowerCase(),
        ) as [string, ...string[]],
      )
      .transform((name) => name as BlogSourceName)
      .describe('blog-source')
      .parse(name),
  ];
validator.ping = () => [] satisfies [];
validator.kpi = validator.ping;
validator.users_counts = validator.ping;
