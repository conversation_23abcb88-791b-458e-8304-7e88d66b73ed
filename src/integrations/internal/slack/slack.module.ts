import { ScheduleModule } from '@nestjs/schedule';
import { forwardRef, Module } from '@nestjs/common';

import { CreditTransactionModule } from '@resources/credit-transaction/credit-transaction.module';
import { BusinessModule } from '@business/business.module';
import { BlogModule } from '@blog/blog.module';
import { UserModule } from '@user/user.module';

import { SlackController } from './slack.controller';
import { SlackService } from './slack.service';
import { SlackGuard } from './slack.guard';
import { SlackCommandService } from './slack.service.command';
import { BlogSourceModule } from '@/resources/blog-source/blog-source.module';
import { TransactionModule } from '@/resources/transaction/transaction.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    forwardRef(() => BlogModule),
    BlogSourceModule,
    TransactionModule,
    CreditTransactionModule,
    BusinessModule,
    UserModule,
  ],
  controllers: [SlackController],
  providers: [SlackCommandService, SlackService, SlackGuard],
  exports: [SlackService],
})
export class SlackModule {}
