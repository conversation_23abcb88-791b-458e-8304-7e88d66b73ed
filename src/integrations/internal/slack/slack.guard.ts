import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { Request } from 'express';

import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

import config from '@common/configs/config';

@Injectable()
export class SlackGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const { headers, body } = request;
    const timestamp = headers['x-slack-request-timestamp'];
    const signature = headers['x-slack-signature'] as string;

    const sigBaseString = `v0:${timestamp}:${new URLSearchParams(body).toString()}`;
    const hmac = crypto.createHmac('sha256', config().slack.signingSecret);
    hmac.update(sigBaseString, 'utf-8');
    const computedSignature = `v0=${hmac.digest('hex')}`;

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'utf-8'),
      Buffer.from(computedSignature, 'utf-8'),
    );
  }
}
