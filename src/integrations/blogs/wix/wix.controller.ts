import axios from 'axios';
import { Controller, Get, Query, Req, Res } from '@nestjs/common';
import { WixService } from './wix.service';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';

@Controller('wix')
export class WixController {
  constructor(
    private readonly configService: ConfigService,
    private readonly wixService: WixService,
  ) {}

  @Get('connect')
  async connect(@Query('state') state: string, @Res() res: Response) {
    console.log('WIX_CLIENT_ID', this.configService.get('WIX_CLIENT_ID'));
    return res.redirect(
      `https://www.wix.com/installer/install?appId=${this.configService.get(
        'WIX_CLIENT_ID',
      )}&redirectUrl=${this.configService.get('BASE_URL')}/wix/callback&state=${state}`,
    );
  }

  @Get('callback')
  async callback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Query('instanceId') instanceId: string,
    @Req() req,
    @Res() res,
  ) {
    const { access_token: accessToken, refresh_token: refreshToken } = await this.getWixToken(code);
    const url = await this.wixService.saveWixToken(state, accessToken, refreshToken, instanceId);

    res.redirect(url);
  }

  async getWixToken(authorizationCode: string): Promise<any> {
    const url = 'https://www.wixapis.com/oauth/access';
    const body = {
      grant_type: 'authorization_code',
      client_id: this.configService.get('WIX_CLIENT_ID'),
      client_secret: this.configService.get('WIX_CLIENT_SECRET'),
      code: authorizationCode,
    };

    const response = await axios.post(url, body, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  }
}
