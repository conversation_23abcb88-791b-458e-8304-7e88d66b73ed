import { Module } from '@nestjs/common';
import { WixController } from './wix.controller';
import { UserModule } from '@user/user.module';
import { BusinessModule } from '@business/business.module';
import { JwtService } from '@nestjs/jwt';
import { WixService } from './wix.service';

@Module({
  controllers: [WixController],
  imports: [UserModule, BusinessModule],
  providers: [JwtService, WixService],
  exports: [WixService],
})
export class WixModule {}
