import { UnauthorizedException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import * as fs from 'fs';

import { BusinessService } from '@/business/business.service';
import { UserService } from '@/user/user.service';

@Injectable()
export class WixService {
  private readonly logger = new Logger(WixService.name);

  constructor(
    private configService: ConfigService,
    private businessService: BusinessService,
    private userService: UserService,
    private jwtService: JwtService,
  ) {}

  async publishPost({
    bid,
    blogId,
    title,
    content,
    draft = false,
    image,
    blogKeywords,
  }: {
    bid: string;
    blogId: string;
    title: string;
    content: string;
    draft?: boolean;
    image?: string;
    socials?: any[];
    blogKeywords?: string[];
  }): Promise<string> {
    try {
      this.logger.debug('publishing to Wix');
      const { accessToken, wixId } = await this.getAccessToken(bid);
      const url = 'https://www.wixapis.com/blog/v3/draft-posts/';
      const richContent = {
        nodes: [
          {
            type: 'PARAGRAPH',
            nodes: [
              {
                type: 'HTML',
                htmlData: {
                  html: content,
                },
              },
            ],
          },
        ],
      };
      const data = {
        draftPost: {
          title,
          richContent,
          draft,
          keywords: blogKeywords,
        },
      };

      const response = await axios.post(url, data, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (image) {
        await this.addImageToPost(accessToken, wixId, response.data.id, image);
      }

      console.log(`published to Wix`);
      return response?.data?.url;
    } catch (error) {
      this.logger.error(`Error publishing to Wix for BlogId: ${blogId}`, error?.message);
      throw error;
    }
  }

  async addImageToPost(accessToken, siteId, postId, imageFilePath) {
    const imageContent = fs.readFileSync(imageFilePath, { encoding: 'base64' });

    const headers = {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'multipart/form-data',
    };

    const formData = new FormData();
    formData.append('image', imageContent);

    const response = await axios.post(
      `https://www.wixapis.com/blog/v3/sites/${siteId}/posts/${postId}/media`,
      formData,
      { headers },
    );

    return response?.data?.url;
  }

  async saveWixToken(
    token: string,
    accessToken: string,
    refreshToken: string,
    wixId: string,
  ): Promise<string> {
    if (!accessToken || !token) {
      throw new Error(`You denied the app or tokens didn't match!`);
    }

    try {
      this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const payload: any = this.jwtService.decode(token);
      const { sub: userId } = payload;
      const user = await this.userService.findById(userId);
      if (!user || !user.business === payload.bid) {
        throw new UnauthorizedException();
      }
      await this.businessService.updateBusinessWixToken(
        payload.bid,
        accessToken,
        refreshToken,
        wixId,
      );
      return this.configService.get('DASHBOARD_REDIRECT_URL') + '/dashboard/settings?success=true';
    } catch (error) {
      this.logger.error(`Error saving Wix token`, error?.message);
      return (
        this.configService.get('DASHBOARD_REDIRECT_URL') +
        `/dashboard/settings?error=${error?.message}`
      );
    }
  }

  async getAccessToken(bid: string): Promise<any> {
    try {
      const business = await this.businessService.findOne(bid);

      if (!business) {
        throw new UnauthorizedException('Business not found');
      }

      const data = {
        grant_type: 'refresh_token',
        client_id: this.configService.get('WIX_CLIENT_ID'),
        client_secret: this.configService.get('WIX_CLIENT_SECRET'),
        refresh_token: business.wixRefreshToken,
      };

      const response = await axios.post('https://www.wixapis.com/oauth/access', data);
      const accessToken = response.data.access_token;
      const refreshToken = response.data.refresh_token;

      await this.businessService.updateBusinessWixToken(
        bid,
        accessToken,
        refreshToken,
        business.wixId,
      );

      return {
        accessToken: response.data?.access_token,
        wixId: business.wixId,
      };
    } catch (error) {
      this.logger.error('Failed to refresh Wix access token:', error?.message);
      throw error;
    }
  }
}
