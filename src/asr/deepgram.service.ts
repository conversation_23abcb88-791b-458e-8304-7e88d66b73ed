import { Injectable, Logger } from '@nestjs/common';

import { NoSpokenWordsException } from '@blog/blog.errors';
import { DEEPGRAM_TOKEN } from '@common/constants';
import { Deepgram } from '@deepgram/sdk';

import {
  DeepgramTranscriptionPayloadDto,
  DeepgramSubmitTranscriptApiDto,
  DeepgramTranscript,
} from './dto/deepgram.dto';

@Injectable()
export class DeepgramService {
  private readonly logger = new Logger(DeepgramService.name);

  private readonly deepgram: Deepgram;

  constructor() {
    this.deepgram = new Deepgram(DEEPGRAM_TOKEN);
  }

  /**
   * Get transcript from deepgram
   * This webhook url callback will get invoked with transcript data from deepgram
   * @category Deepgram
   * @param {object} param
   * @param {string} param.id - meeting parse id
   * @param {string} param.audioUrl - meeting audio url
   * @param {string} param.model - asr provider model
   * @param {string} param.languageCode - preferred language code
   * @param {array} param.customVocabularies - custom vocabs
   * @returns {Promise<DeepgramTranscript> response
   * returns DeepgramTranscript
   * @example DeepgramTranscript
   */
  async getTranscript({
    id,
    audioUrl,
    languageCode = 'auto',
    params,
  }: DeepgramTranscriptionPayloadDto): Promise<DeepgramTranscript> {
    const languageOpts =
      !languageCode || languageCode === 'auto'
        ? { detect_language: true }
        : { language: languageCode };
    const deepgramRequestPayload: DeepgramSubmitTranscriptApiDto = {
      punctuate: true,
      summarize: false,
      profanity_filter: true,
      paragraphs: true,
      ...params,
      ...languageOpts,
    };

    try {
      const deepgramResponse = await this.deepgram.transcription.preRecorded(
        {
          url: audioUrl,
        },
        deepgramRequestPayload,
      );

      this.logger.debug(
        `Deepgram response for ${id} request_id: ${
          deepgramResponse.request_id || deepgramResponse?.metadata?.request_id
        }`,
      );

      // TODO: deepgram response is not consistent with the interface
      return deepgramResponse as unknown as DeepgramTranscript;
    } catch (error) {
      this.logger.error(`Deepgram error for ${id}: ${error?.message}`);
      throw error;
    }
  }

  transformTranscript(transcriptObject: DeepgramTranscript) {
    const { results } = transcriptObject;
    const { channels } = results || {};
    const { alternatives } = channels[0] || {};
    const { transcript, summaries, paragraphs } = alternatives[0] || {};

    if (!transcript || transcript?.length < 100) {
      throw new NoSpokenWordsException();
    }

    const transcription = transcript || '';
    const summary = summaries?.map((s) => s.summary).join('. ');
    const captions = paragraphs.paragraphs.map((p) => ({
      startTime: p.start,
      endTime: p.end,
      text: p.sentences.map((sentence) => sentence.text).join(' '),
      speaker: p.speaker?.toString(),
    }));

    return {
      summary,
      transcription,
      captions,
    };
  }
}
