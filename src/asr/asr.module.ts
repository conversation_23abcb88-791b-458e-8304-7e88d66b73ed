import { Module, Global } from '@nestjs/common';

import { TranscriptionModule } from '@/transcription/transcription.module';
import { BlogModule } from '@/blog/blog.module';
import { JobModule } from '@/job/job.module';

import { AssemblyService } from './assembly.service';
import { DeepgramService } from './deepgram.service';
import { ASRService } from './asr.service';

@Global()
@Module({
  imports: [TranscriptionModule, BlogModule, JobModule],
  providers: [ASRService, AssemblyService, DeepgramService],
  exports: [ASRService, AssemblyService, DeepgramService],
})
export class ASRModule {}
