/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as fs from 'fs';

import { ASSEMBLY_TOKEN, SERVICE_URL, isLastLetterPunctuation } from '@/common/constants';
import { getDurationMinutes } from '@/common/helpers';

import {
  AssemblySubmitTranscriptWebhookDto,
  TransformedTranscriptV2,
  AssemblyApiTranscript,
  TranscriptApiJobResp,
  AssemblyUtterance,
  Monologue,
} from './dto';
import { transformMonologueToTranscripts } from './monologue-transformer';

const baseURL = 'https://api.assemblyai.com/v2/transcript';
const uploadURL = 'https://api.assemblyai.com/v2/upload';

// create axios instance
const assembly = axios.create({
  baseURL,
  headers: {
    authorization: ASSEMBLY_TOKEN,
  },
});
const assemblyUpload = axios.create({
  baseURL: uploadURL,
  headers: {
    authorization: ASSEMBLY_TOKEN,
  },
});

@Injectable()
export class AssemblyService {
  CALLBACK_URL = `${SERVICE_URL}/assembly-cb`;

  /**
   * Send POST request to assembly with webhook url
   * This webhook url callback will get invoked with transcript data from assembly
   * @category Assembly
   * @param {object} param
   * @param {string} param.audio_url - meeting audio_url
   * @param {string} param.callbackUrlSubPath - subPath of webhook url
   * @param {boolean} param.removeDisfluencies - option to remove disfluenies
   * @param {array} param.customVocabularies - custom vocabs
   * @param {boolean} param.filterProfanity - option to filter profanity
   * @returns {Promise<{transcript_id:string, status:string}|string>} response
   * returns transcript_id and status completed|error
   * @example { transcript_id: 5552493-16d8-42d8-8feb-c2a16b56f6e8, status: completed }
   */
  submitTranscriptJob = async ({ audio_url = '', callbackUrlSubPath = '' }) => {
    const callbackUrl = `${this.CALLBACK_URL}${callbackUrlSubPath}`;

    const resp = await assembly.post('', {
      audio_url,
      language_detection: true,
      speaker_labels: false,
      webhook_url: callbackUrl,
      webhook_auth_header_name: 'Authorization',
      webhook_auth_header_value: `Bearer ${ASSEMBLY_TOKEN}`,
    });
    const { data } = resp;
    return data;
  };

  private async readAndSendFile(filePath: string, chunkSize = 5242880) {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      const readStream = fs.createReadStream(filePath, {
        highWaterMark: chunkSize,
      });
      readStream.on('data', (chunk: Buffer) => chunks.push(chunk));
      readStream.on('end', () => resolve(Buffer.concat(chunks)));
      readStream.on('error', (error) => reject(error));
    });
  }

  async uploadFile(filePath: string) {
    const chunkSize = 5242880;
    const data = await this.readAndSendFile(filePath, chunkSize);
    const resp = await assemblyUpload.post('', data);

    return resp.data;
  }

  /**
 * @category Assembly
 * @param {Object} param
 * @param {string} param.id - transcript_id
 * @returns {Promise<any>} response
 * @example
{
    "acoustic_model": "assemblyai_default",
    "audio_duration": 12.0960090702948,
    "audio_url": "https://bit.ly/3yxKEIY",
    "confidence": 0.956,
    "dual_channel": null,
    "format_text": true,
    "id": "5551722-f677-48a6-9287-39c0aafd9ac1",
    "language_model": "assemblyai_default",
    "punctuate": true,
    "status": "completed",
    "text": "dummy text ...",
    "utterances": null,
    "webhook_status_code": "completed",
    "webhook_url": "url",
    "words": [
        {
            "confidence": 1.0,
            "end": 440,
            "start": 0,
            "text": "You"
        },
        ...
    ]
}
 */

  parseWebhook = (
    body: any,
  ): {
    transcriptionPayload: AssemblySubmitTranscriptWebhookDto;
    audio_duration: number;
  } => {
    const { transcript_id, audio_duration }: { transcript_id: string; audio_duration: number } =
      body || {};

    if (!transcript_id) {
      // rev does not have this check
      const errorMessage = `assembly did not send the transcription payload`;
      throw new Error(errorMessage);
    }

    return {
      transcriptionPayload: { ...body, id: transcript_id },
      audio_duration,
    };
  };

  getTranscript = async ({ id: transcript_id }: TranscriptApiJobResp) => {
    const response = await assembly.get(`/${transcript_id}`).then(({ data = {} }) => data);
    return response;
  };

  /**
   * Transform utterance to monologue
   */
  _transformUtteranceToMonologue = (utterances: AssemblyUtterance[]) => {
    let acc: {
      _monologues: Monologue[];
      _speakers: string[];
    } = { _monologues: [], _speakers: [] };

    let resp: Monologue[] = [];

    utterances.forEach((utterance, index) => {
      const { _monologues, _speakers } = acc;
      const { speaker: _speaker, words } = utterance;

      const speaker = _speakers.includes(_speaker) ? _speakers.indexOf(_speaker) : _speakers.length;

      const elements: {
        type: string;
        ts: number;
        end_ts: number;
        confidence: number;
        value: string;
      }[] = words.reduce((elementsAcc = [], word) => {
        const { start, end, confidence, text } = word;
        const space = {
          type: 'punct',
          value: ' ',
        };
        const hasPunctuation = isLastLetterPunctuation(text);
        const punctuation = {
          type: 'punct',
          value: text ? text?.slice(-1) : '',
        };

        const element = {
          type: 'text',
          ts: start / 1000,
          end_ts: end / 1000,
          confidence,
          value: hasPunctuation && text ? text?.slice(0, text.length - 1) : text,
        };
        const newElements = [element];
        // @ts-ignore
        if (hasPunctuation) newElements.push(punctuation);
        // @ts-ignore
        newElements.push(space);

        return [...elementsAcc, ...newElements];
      }, []);

      const monologoues = _monologues.concat({
        speaker,
        elements,
      });

      // @ts-ignore
      if (index === utterances.length - 1) {
        /** original it is using utterances.reduce but internal it
         * would return different type for acc (non final time vs final time)
         * it is hard for typing/maintainability, so change to use forEach */
        resp = monologoues;
      }

      acc = {
        _monologues: monologoues,
        _speakers: _speakers.includes(_speaker) ? _speakers : [..._speakers, _speaker],
      };
    });

    return resp;
  };

  transformTranscript = (transcriptObject: AssemblyApiTranscript): TransformedTranscriptV2 => {
    const { utterances, text, audio_duration: durationSeconds, words } = transcriptObject;
    const durationMinutes = getDurationMinutes(durationSeconds);
    if (utterances || words) {
      const monologues = this._transformUtteranceToMonologue(utterances);

      // @ts-ignore
      const transformedElements = monologues.reduce(
        (acc, monologue) => {
          const { elements, speaker } = monologue;

          const { captions, transcripts, transcription } = transformMonologueToTranscripts(
            elements,
            speaker,
          );

          // @ts-ignore
          acc.transcript = acc.transcript.concat(transcripts);
          acc.caption = acc.caption.concat(captions);
          acc.transcription = acc.transcription.concat(transcription);

          return acc;
        },
        {
          transcription: '',
          transcript: [],
          caption: [],
        },
      );

      return { ...transformedElements, transcription: text, durationMinutes };
    }

    return {
      transcription: '',
      transcript: [],
      caption: [],
    };
  };
}
