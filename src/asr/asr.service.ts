import type { Queue } from 'bull';

import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Request } from 'express';

import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { TranscriptionService } from '@/transcription/transcription.service';

import {
  TransformedTranscriptV2,
  AssemblyApiTranscript,
  TranscriptApiJobResp,
  AsrTranscript,
} from './dto';
import { AssemblyService } from './assembly.service';

@Injectable()
export class ASRService {
  private readonly logger = new Logger(ASRService.name);

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY)
    private generateTranscriptSummaryQueue: Queue,
    @InjectQueue(JOB_QUEUES.TRANSCRIBE_MEDIA)
    private transcribeMediaQueue: Queue,
    private readonly transcriptionService: TranscriptionService,
    private readonly assemblyService: AssemblyService,
  ) {}

  submitTranscriptJob = async ({ id, url }: { id: string; url: string; languageCode: string }) => {
    return this.assemblyService.submitTranscriptJob({
      audio_url: url,
      callbackUrlSubPath: `/${id}`,
    });
  };

  async webhookHandler(req: Request) {
    const { body } = req;
    const { id } = req.params;
    const { transcriptionPayload, audio_duration } = this.assemblyService.parseWebhook(body);

    if (transcriptionPayload.status === 'error') {
      this.logger.error(
        {
          err: new Error('Error processing transcription payload'),
          id,
          transcriptionID: transcriptionPayload.id,
        },
        'An error occurred while processing the transcription payload',
      );
    }

    try {
      const transcriptData = await this.getTranscript(transcriptionPayload);
      this.logger.debug({ id: transcriptionPayload.id }, 'saving transcript');
      await this.transcriptionService.create({
        id: transcriptionPayload.id,
        summary: transcriptData.transcription,
        transcript: transcriptData.transcript,
        caption: transcriptData.caption,
      });

      const transcriptionJob = await this.transcribeMediaQueue.getJob(id);
      this.logger.debug({ id: transcriptionPayload.id }, 'got transcription job');

      if (transcriptionJob) {
        await this.generateTranscriptSummaryQueue.add(
          {
            ...transcriptionJob.data,
            transcription: transcriptData.transcription,
          },
          { ...JOB_OPTIONS, jobId: id },
        );
        this.logger.debug(
          { id: transcriptionPayload.id },
          'pushed to generate transcript summary queue',
        );
      } else {
        this.logger.error(`transcription job not found for ${id}`);
        throw new Error(`transcription job not found for ${id}`);
      }
    } catch (error) {
      this.logger.error(`Error getting transcript for ${transcriptionPayload.id}`, error?.message);
    }

    return {
      transcriptionPayload,
      audio_duration,
    };
  }

  async getTranscript(transcriptionPayload: TranscriptApiJobResp) {
    const transcriptObject = await this.assemblyService.getTranscript(transcriptionPayload);
    const transformTranscriptResult = await this._transformTranscript(transcriptObject);

    return transformTranscriptResult;
  }

  /** this job is not bull job, it is asr job */
  _getTranscriptionObject = async (job: TranscriptApiJobResp) => {
    const transcriptObj: AsrTranscript = (await this.assemblyService.getTranscript(
      job,
    )) as AssemblyApiTranscript;

    return transcriptObj;
  };

  _monologuesChecker = (transcriptObject: AsrTranscript) => {
    let isEmptyAssemblyMonologues = false;

    /** use in operator to do type narrowing for a union type
     * ref:
     * https://www.typescriptlang.org/docs/handbook/2/narrowing.html#the-in-operator-narrowing */
    if ('utterances' in transcriptObject) {
      /** when assembly works on non-en language, its utterances is null  */
      const { utterances } = transcriptObject;
      isEmptyAssemblyMonologues = !utterances || utterances?.length === 0;
    }

    return isEmptyAssemblyMonologues;
  };

  _transformTranscript = async (transcriptObject: AsrTranscript) => {
    const transformedTranscript: TransformedTranscriptV2 = this.assemblyService.transformTranscript(
      transcriptObject as AssemblyApiTranscript,
    );

    return transformedTranscript;
  };

  getTranscription = async ({
    job,
    urlTranscript,
  }: {
    job: TranscriptApiJobResp;
    urlTranscript: string;
    /** TODO: define it later */
    user: any;
  }) => {
    if ('status' in job) {
      const { status /* {failure_detail: failureDetail, error, failure} */ } = job;
      if (status === 'failed' || status === 'error') {
        const messageError = ''; // error || `${failureDetail} - ${failure}`; // rev case
        this.logger.log(`Transcription:error: Failed to transcribe: ${messageError}`);

        throw new Error(`Service transcription failed - ${messageError}`);
      }
    }

    this.logger.log(
      `transcribing:getTranscription:info: getting the transcribed result for ${urlTranscript}`,
    );

    const transcriptionObjectResult = await this._getTranscriptionObject(job);

    return transcriptionObjectResult;
  };
}
