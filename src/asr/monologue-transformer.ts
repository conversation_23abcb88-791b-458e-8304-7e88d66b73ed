import { isLastLetterPunctuation } from '@common/helpers';
import { MonologueElement, TransformedTranscript, TransformedTranscriptV0 } from './dto';

/**
 */

export const transformMonologueToTranscripts = (
  elements: MonologueElement[] = [],
  speaker: number,
) => {
  const resp = elements.reduce(
    // eslint-disable-next-line complexity
    (elementsAcc, element, index) => {
      const { type, value, ts, end_ts: endTs, confidence } = element;
      const { sentenceIndex, captions, transcripts, transcription } = elementsAcc;

      const _string = type === `punct` && !value ? ' ' : value;
      // remove disfluencies
      const textWithoutDisfluencies = _string.replace(/\b((uh+|um+|hm+|ah+)(?!\w)(\W?))/gi, '');
      const { paragraph = '', startTime = ts } = transcripts || {};
      const {
        sentence = '',
        time = ts,
        speaker_id = speaker,
        endTime, // endTime is undefined always
        averageConfidence = {},
      } = captions[sentenceIndex] || {};

      // As of 2022.12.24,
      // comment const previousTimeEndTime = `${ts}${endTs}`;
      // since collisionAvoidance was not really saved. it should be some legacy code

      // remove inaudible
      if (value === '<inaudible>') {
        return;
      }
      // if (collisionAvoidance === previousTimeEndTime && type === 'text') return elementsAcc;

      const isLastIteration = index + 1 === elements.length;

      const shouldBreakCaption =
        type === 'punct' && (value === `.` || value === `!` || value === `?` || isLastIteration);

      /** = 0 means this elements iteration is a new (sentence)index of captions */
      const { averageConfidenceTotal = 0, totalSample = 0 } =
        (averageConfidence as {
          averageConfidenceTotal: number;
          totalSample: number;
        }) || {};

      const newAverageConfidence = confidence
        ? {
            averageConfidenceTotal: averageConfidenceTotal + confidence,
            totalSample: totalSample + 1,
          }
        : averageConfidence;

      const { averageConfidenceTotal: total, totalSample: quantity } = newAverageConfidence as {
        averageConfidenceTotal: number;
        totalSample: number;
      };

      const _sentence = `${sentence}${textWithoutDisfluencies}`;
      const trimmedSentence = _sentence?.trim();
      const isValidCaption =
        trimmedSentence.length === 1 && isLastLetterPunctuation(trimmedSentence)
          ? false
          : !!trimmedSentence;

      /** multiple elements could belong to the same caption
       * newSentenceIndex is used in next iteration of elements
       */
      const newSentenceIndex =
        shouldBreakCaption && isValidCaption ? sentenceIndex + 1 : sentenceIndex;

      /** newCaptions is for the current sentenceIndex (a.k.a. captionIndex) */
      const newCaptions = isValidCaption
        ? {
            sentence: shouldBreakCaption ? trimmedSentence : _sentence,
            speaker_id,
            time,
            endTime: endTs || endTime,
            speaker,

            averageConfidence: shouldBreakCaption
              ? quantity === 0
                ? 0
                : Number(total / quantity)
              : newAverageConfidence,
          }
        : {};

      const newTranscription =
        shouldBreakCaption && isValidCaption
          ? `${transcription}${newCaptions.sentence}`
          : transcription;

      return {
        captions: isValidCaption
          ? Object.assign([], captions, { [sentenceIndex]: newCaptions })
          : captions,
        transcripts: {
          ...transcripts,
          startTime,
          paragraph: `${paragraph}${textWithoutDisfluencies}`,
        },
        sentenceIndex: newSentenceIndex,
        transcription: newTranscription,
      };
    },
    {
      sentenceIndex: 0,
      captions: [],
      transcripts: { speaker },
      captionsSRT: '',
      captionsSRTV2: [],
      transcription: '',
    } as TransformedTranscriptV0,
  );
  return resp as TransformedTranscript;
};
