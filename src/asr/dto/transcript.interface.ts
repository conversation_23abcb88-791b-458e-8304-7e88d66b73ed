export interface MonologueElement {
  type: string;
  ts?: number; // rev's ts is optional
  end_ts?: number; // rev's end_ts is optional
  confidence?: number; // rev's confidence is optional
  value: string;
}

export interface Monologue {
  elements: MonologueElement[];
  speaker: number;
}

export interface TransformedTranscriptField {
  speaker: number;
  paragraph?: string;
  startTime?: number;
}

export interface TransformedTranscriptV0 {
  sentenceIndex?: number;
  captions: {
    sentence: string;
    speaker_id: number;
    time: number;
    endTime: number;
    speaker: number;
    /** NOTE: this union type is very tricky
     * averageConfidence will be number when the current elements iteration
     * is the last one of the same caption (shouldBreakCaption=true)
     * then it will do total/quantity to get some number eventually
     */
    averageConfidence:
      | number
      | {
          averageConfidenceTotal?: number;
          totalSample?: number;
        };
  }[];
  transcripts: TransformedTranscriptField;
  transcription: string;
}

export interface TransformedTranscript {
  sentenceIndex?: number;
  captions: {
    sentence: string;
    speaker_id: number;
    time: number;
    endTime: number;
    speaker: number;
    averageConfidence: number;
  }[];
  transcripts: TransformedTranscriptField;
  captionsSRT?: string;
  captionsSRTV2?: string[];
  transcription: string;
}

export interface Caption {
  sentence: string;
  speaker_id: number;
  time: number;
  endTime: number;
  speaker: number;
  averageConfidence: number;
}

export interface TransformedTranscriptV2 {
  transcript: TransformedTranscriptField[];

  /* NOTE: this may be changed in mergeSpeakerLabels */
  caption: Caption[];

  transcription: string;

  /** append at
   * { ...transformedElements, transcription: text, durationMinutes };
   */
  durationMinutes?: number;
}

/** use V2 result in _fetchAndMergeSpeakerLabels and modify a little to return */
export interface TransformedTranscriptV3 {
  transcription?: TransformedTranscriptField[]; // same as V2's transcript
  captions?: Caption[]; // same as V2's caption
  transcriptionText?: string; // same as V2's transcript's transcription
  durationMinutes?: number;
  // TransformedTranscriptV2 does not have this tone
  languageCode?: string;
}
