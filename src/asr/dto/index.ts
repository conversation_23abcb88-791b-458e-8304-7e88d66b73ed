import { AssemblyApiTranscript, AssemblySubmitTranscriptWebhookDto } from './assembly.dto';

export * from './assembly.dto';
export * from './transcript.interface';

export type AsrTranscript = AssemblyApiTranscript;
export type TranscriptApiJobResp = AssemblySubmitTranscriptWebhookDto & {
  /** assembly does not have these two */

  // /** rev: https://github.com/revdotcom/revai-node-sdk/blob/142e8041fdbc710f0975812dd75e16ed0dde5aa0/src/models/RevAiApiJob.ts#L15 */
  // duration_seconds?: number; // moved to RevAiApiJob

  /** TODO: it seems to be always undefined, figure it out */
  data?: any;
};
