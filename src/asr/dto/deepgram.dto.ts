interface DeepgramModelParams {
  model: string;
  tier?: string;
}
export interface DeepgramTranscriptionPayloadDto {
  id?: string;
  audioUrl?: string;
  params?: DeepgramModelParams;
  languageCode?: string;
  customVocabularies?: string[];
}

export interface DeepgramSubmitTranscriptApiDto {
  model: string;
  tier?: string;
  paragraphs?: boolean;
  diarize?: boolean;
  punctuate?: boolean;
  detect_topics?: boolean;
  smart_format?: boolean;
  profanity_filter?: boolean;
  keywords?: string[];
  detect_language?: boolean;
  summarize?: boolean;
}

export interface DeepgramTranscript {
  request_id?: string;
  metadata?: Metadata;
  results?: {
    channels: Channel[];
    utterances?: Utterance[];
  };
}

export type DeepgramParagraph = Paragraph;

interface Metadata {
  request_id: string;
  transaction_key: string;
  sha256: string;
  created: string;
  duration: number;
  channels: number;
}

interface Channel {
  alternatives: Alternative[];
}

interface Utterance {
  start: number;
  end: number;
  confidence: number;
  channel: number;
  transcript: string;
  words: Word[];
  speaker?: number;
  id: string;
}

interface Alternative {
  transcript: string;
  summaries: Summary[];
  confidence: number;
  words: Word[];
  paragraphs?: ParagraphGroup;
}

interface Summary {
  summary: string;
  start_word: number;
  end_word: number;
}

interface Word {
  word: string;
  start: number;
  end: number;
  confidence: number;
  speaker: number;
  speaker_confidence: number;
  punctuated_word: string;
}

interface ParagraphGroup {
  transcript: string;
  paragraphs: Paragraph[];
}

interface Paragraph {
  sentences: Sentence[];
  speaker: number;
  num_words: number;
  start: number;
  end: number;
}

interface Sentence {
  text: string;
  start: number;
  end: number;
}
