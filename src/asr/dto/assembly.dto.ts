export interface AssemblySubmitTranscriptApiDto {
  audio_url: string;
  language_code: string;
  language_detection: boolean;
  speaker_labels?: boolean;
  word_boost?: string[];
  disfluencies?: boolean;
  filter_profanity?: boolean;
  webhook_url: string;
  webhook_auth_header_name: string;
  webhook_auth_header_value: string;
  auto_chapters?: boolean;
}

export interface AssemblySubmitTranscriptWebhookDto {
  // The status of your transcription. queued, processing, completed, or error
  // https://www.assemblyai.com/docs/reference
  status: string;
  // e.g. "rilie77312-Odac-4800-8502-258c89492938"
  transcript_id: string;

  // TODO: remove it later. does not have this field actually,
  audio_duration?: number;

  /** TODO: These seem to belong Rev, not Assembly */
  // failure_detail?: any;
  // error?: any;
  // failure?: any;

  /** append by ourself */
  id?: string;
}

export interface AssemblyWord {
  text: string;
  confidence: number;
  start: number;
  end: number;
  speaker?: string;
}

export interface AssemblyUtterance {
  text: string;
  confidence: number;
  speaker?: string;
  start: number;
  end: number;
  words: AssemblyWord[];
}

export interface AssemblyApiTranscript {
  language_code: string;
  utterances?: AssemblyUtterance[];
  text: string;
  audio_duration: number;
  words: AssemblyWord[];
  /** there are additional fields but we do not use them */
  id: string;
}
