/**
 * Filters a union type U based on a Record<U, boolean>, returning only those members where the boolean is true
 */
export type Filter<U extends string, R extends Record<U, boolean>> = {
  [K in U]: R[K] extends true ? K : never;
}[U];

/**
 * Prepends parameters to the beginning of a function's parameter list
 */
export type PrependParameters<F extends (...args: any[]) => any, P extends any[]> = (
  ...args: [...P, ...Parameters<F>]
) => ReturnType<F>;
