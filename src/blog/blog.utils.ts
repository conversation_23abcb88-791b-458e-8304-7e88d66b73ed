import { BlogPublishStatus } from './blog.enums';
import type { Blog } from './blog.model';

const isPublished = ({ timestamp, platform }: Blog['platforms'][0], blog: Blog) =>
  new Date() > new Date(timestamp) || !!blog[`${platform}Link`];

const hasSchedule = ({ timestamp }: Blog['platforms'][0]) => new Date() < new Date(timestamp);

const getPublishStatus = (blog: Blog): Blog['publishStatus'] => {
  if (blog.platforms?.length) {
    if (blog.platforms.every((p) => isPublished(p, blog))) return BlogPublishStatus.published;
    if (blog.platforms.some(hasSchedule)) return BlogPublishStatus.scheduled;
  }

  return BlogPublishStatus.draft;
};

const countWords = (content?: string): number => {
  if (!content) return 0;
  const strippedContent = content.replace(/<[^>]+>/g, '');
  const words = strippedContent.split(/\s+/);
  const filteredWords = words.filter((word) => word.trim() !== '');
  return filteredWords.length;
};

const formatUrl = (url) => {
  if (!url) return '#';
  if (url.match(/^https?:\/\//i)) {
    return url;
  }
  return `https://${url}`;
};

export { getPublishStatus, countWords, formatUrl };
