import type { Blog, BlogQueuePayload } from '@blog/blog.model';
import type { YouTubeTranscript } from '@/youtube/youtube-content/youtube-content.interface';
import type { Job, Queue } from 'bull';

import { OnQueueCompleted, OnQueueFailed, InjectQueue, Processor, Process } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BlogGenerationService } from '@/blog-generation/blog-generation.service';
import { getPlanModel, TASK } from '@/llm/llm.models';
import { BusinessService } from '@/business/business.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { OpenaiService } from '@/openai/openai.service';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { ImageService } from '@/resources/image/image.service';
import { BlogType } from '@/blog/blog.model';
import { delay } from '@/common/helpers';

import { BlogCoverImageType, BlogTldrPosition, BlogSize, BlogTone } from './blog.enums';
import { BlogService } from './blog.service';
import { formatUrl } from './blog.utils';

const predefinedColors = [
  '#D1D8DC',
  '#00E2B1',
  '#CCD7E6',
  '#54D3DA',
  '#A2ACE0',
  '#41B8DD',
  '#FBD0B9',
];

const SINGLE_SHOT_BLOG_MAX_SIZE = 800;

@Processor(JOB_QUEUES.GENERATE_BLOG_CONTENT)
export class BlogGenerateContentProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_BLOG_CONTENT;
  protected readonly logger = new Logger(BlogGenerateContentProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_KEYWORDS) private generateBlogKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) queue: Queue,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly blogGenerationService: BlogGenerationService,
    private readonly businessService: BusinessService,
    private readonly gatewayService: GatewayService,
    private readonly configService: ConfigService,
    private readonly openaiService: OpenaiService,
    private readonly imageService: ImageService,
    private readonly slackService: SlackService,
    private readonly blogService: BlogService,
  ) {
    super(queue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    // eslint-disable-next-line complexity
    return this.processWithLock(job, async (job) => {
      const prompt = job.data.prompt;
      const {
        uid,
        blogId,
        bid,
        email,
        title,
        url,
        blogTitle,
        identifier,
        transcription,
        transcript,
        transcriptionSummary,
        blogOutline,
        blogTone,
        blogSize,
        sourceType,
        wordCountApprox,
        blogLanguage = 'English',
        pov,
        customInstruction,
        tldrPosition,
        authorsToGiveCredit,
        sourceSimilarity,
        coverImageType,
        aiGeneratedCoverImageConfig,
        aiGeneratedContentImagesConfig,
        cta,
        includeQuotation = true,
      } = job.data;
      let { contentImages } = job.data;

      if (!blogOutline) {
        throw new Error(`Failed to generate blog, outline missing.`);
      }

      const { subscriptionPlan } = await this.businessService.findOne(bid);
      const blogSizeToWordCount = {
        mini: 400,
        small: 800,
        medium: 1800,
        large: 2500,
        'x-large': 4000,
      };
      const blogWordCount = wordCountApprox || blogSizeToWordCount[blogSize];
      const eachSection = Math.floor(blogWordCount / blogOutline.sections.length);

      this.logger.log(
        {
          blogSize,
          blogLanguage,
          blogTone,
          blogId,
        },
        'Generating blog content',
      );
      await this.blogService.update(bid, blogId, { status: 'content_generating' });

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'content_generating',
      });

      let blogContent = '';
      let contentTitle = blogTitle || title;

      // Generate Image using AI
      // Cover Image
      let image = '';
      if (aiGeneratedCoverImageConfig && coverImageType === BlogCoverImageType.AIGenerated) {
        try {
          const { url: imgUrl } = await this.imageService.generateAndUploadImage(
            { uid, bid },
            { prompt: contentTitle, ...aiGeneratedCoverImageConfig },
          );
          image = imgUrl;
        } catch (e) {
          this.logger.log(`Failed to generate cover image for blog ${blogId}:`, e.message);
        }
      }

      // Content Images
      contentImages = contentImages || [];
      if (aiGeneratedContentImagesConfig?.count && !contentImages.length) {
        try {
          const promises = [];
          for (let i = 0; i < aiGeneratedContentImagesConfig?.count; i++) {
            const promise = this.imageService.generateAndUploadImage(
              { uid, bid },
              { prompt: blogOutline?.sections?.[i]?.heading, ...aiGeneratedContentImagesConfig },
            );
            promises.push(promise);
          }

          await Promise.allSettled(promises).then((resp) => {
            resp.forEach((r, i) => {
              if (r.status === 'fulfilled' && r.value?.url) {
                contentImages.push(r.value?.url);
              } else if (r.status === 'rejected') {
                this.logger.log(
                  `Failed to generate content image ${i + 1} for blog ${blogId}:`,
                  r.reason,
                );
              }
            });
          });
        } catch (e) {
          this.logger.log(`Failed to generate content images for blog ${blogId}:`, e.message);
        }
      }
      // End Generate Image using AI

      const contentImagesMapping: Record<string, string> = this.generateBlogContentImagesMapping(
        blogOutline,
        contentImages,
      );
      const inputText = subscriptionPlan.includes(`LIFETIME`)
        ? transcriptionSummary || prompt
        : transcription || transcriptionSummary || prompt;
      try {
        blogContent = await this.generateBlogSections({
          job,
          blogId,
          bid,
          blogOutline,
          blogLanguage,
          eachSection,
          blogTone,
          pov,
          contentImagesMapping,
          blogSize,
          inputText,
          transcript,
          customInstruction,
          sourceSimilarity,
          singleShotBlog: blogWordCount <= SINGLE_SHOT_BLOG_MAX_SIZE,
          includeQuotation,
        });

        blogContent = this.convertMarkdownBoldToHtml(blogContent);
      } catch (error) {
        this.logger.error({ err: error, blogId }, 'Failed to generate blog sections');
        throw error;
      }

      try {
        if (!contentTitle) {
          this.logger.log(`Generating blog title from content for blog ${blogId}`);
          contentTitle = await this.openaiService.generateBlogTitle(
            blogId,
            transcriptionSummary || prompt,
            blogLanguage,
          );
          this.logger.log(`Generated blog title from content for blog ${blogId}`);
        }
      } catch (error) {
        this.logger.error(`Blog title generation failed for blog ${blogId}`, error.message);
        await this.handleContentGenerationFailure(
          job,
          bid,
          blogId,
          'Failed to generate blog title: ' + error?.message,
        );
        throw error;
      }

      const tldrContent = blogOutline.tldr
        ? blogOutline.tldr.replace(/TL;DR: |TLDR:|TLDR|TL;DR|tl;dr|tldr/, '')
        : '';

      if (blogOutline.introduction) {
        blogContent = `<p>${blogOutline.introduction}</p><!--INTROEND-->
        ${
          blogOutline.tldr && tldrPosition === BlogTldrPosition.start
            ? `<p><b>TL;DR: </b>${tldrContent}</p>`
            : ''
        }
        ${blogContent}`;
      }

      blogContent = blogContent.replace(/```html/g, '').replace(/```/g, '');

      if (blogOutline.tldr && tldrPosition === BlogTldrPosition.end) {
        blogContent += `<p><b>TL;DR: </b>${tldrContent}</p>`;
      }

      if (!!cta?.text) {
        const colorCode = cta.bgColor.replace('#', '');
        const ctaButton = `
          <div data-type="cta-button" data-node-type="ctaButton"><a href="${formatUrl(
            cta.link,
          )}" target="_blank" rel="noopener noreferrer" style="background-color: #${colorCode}; border-radius: ${
            cta.borderRadius
          }px; color: #ffffff; padding: 10px 20px; margin: 10px 0; text-decoration: none;">${
            cta.text
          }</a></div>
        `;
        blogContent += ctaButton;
      }

      if (url && authorsToGiveCredit && authorsToGiveCredit.length) {
        try {
          const creditSection = await this.blogGenerationService.generateCreditSection({
            url,
            authorsToGiveCredit,
            language: blogLanguage,
          });
          blogContent += `<p>${creditSection}</p>`;
        } catch (e) {
          this.logger.error(`Failed to add credit section for blog ${blogId}`, e.message);
        }
      }

      try {
        const generationStatus = `Generated blog content, sending for review`;
        const status = 'content_generated';
        const updates: Partial<Blog> = {
          title: blogTitle,
          content: blogContent,
          contentImagesMapping,
          status,
          generationStatus,
        };
        if (image) {
          updates.image = image;
        }
        if (contentImages?.length) {
          updates.contentImages = contentImages;
        }
        const blog = await this.blogService.update(bid, blogId, updates);

        // Update Existing Slack Message
        await this.slackService.updateBlogCreateAlert(blog, blog.slackAlertTs, { bid, email });

        // Push New Message to Same Thread
        const blocks = [
          { type: 'section', text: { type: 'mrkdwn', text: 'Generated blog *content*.' } },
        ];
        await this.slackService.sendMessage({
          channel: 'monitor-blog-create',
          message: 'Generated blog content.',
          thread_ts: blog.slackAlertTs,
          blocks,
        });

        this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
        this.gatewayService.sendBlogStatusUpdate(uid, { _id: blogId, identifier, status });

        await this.creditTransactionService.deductContentCost(
          { bid, uid },
          {
            contentType: ['audio', 'video'].includes(sourceType) ? BlogType.Media : BlogType.Text,
            contentIds: [blogId],
            status: 'confirmed',
          },
          blogTitle || 'Blog Generation Cost',
        );

        // Queue the blog for review using Claude 3.7 to make it more human-like
        try {
          const isReviewRequired = this.configService.get('featureFlags.isReviewRequired');

          if (isReviewRequired) {
            // Queue blog review to make content more human-like
            const reviewQueued = await this.blogGenerationService.queueBlogForReview(
              blogId,
              blogContent,
              {
                title: blogTitle || title,
                blogTone,
                blogLanguage,
                perspective: pov,
                seoKeywords: blogOutline.keywords,
                blogSize,
                wordCountApprox,
                sourceType: job.data.sourceType,
                sourceName: job.data.sourceName,
                transcription: job.data.transcription,
                prompt: job.data.prompt,
              },
            );

            if (reviewQueued) {
              this.logger.log(`Queued blog ${blogId} for human-like review`);
            } else {
              this.logger.warn(`Failed to queue blog ${blogId} for human-like review`);
            }
          } else {
            // Skip review queue and send event to keyword generate queue
            const sourceContent = inputText || '';
            const keywordsQueueData = {
              ...job.data,
              blogContent: blogContent,
              blogTitle: blogTitle || title,
              sourceContent: sourceContent || '',
            };
            await this.generateBlogKeywordsQueue.add(keywordsQueueData, {
              ...JOB_OPTIONS,
              jobId: blogId,
            });
            this.logger.log(
              `Review queue bypassed for blog ${blogId}, sent directly to keyword generation`,
            );
          }
        } catch (reviewError) {
          // Non-critical error
          this.logger.error(`Error queueing blog ${blogId} for review: ${reviewError.message}`);
        }
      } catch (error) {
        this.logger.error(`Failed to update blog service ${blogId}`, error.message);
        await this.handleContentGenerationFailure(
          job,
          bid,
          blogId,
          'Failed to update blog service: ' + error.message,
        );
        throw error;
      }
    });
  }

  @OnQueueCompleted()
  async onCompleted({ data: { bid, generationMode, creditTransactionId } }: Job<BlogQueuePayload>) {
    if (generationMode === 'auto' && creditTransactionId) {
      await this.creditTransactionService.confirmTransaction(bid, creditTransactionId);
    }
  }

  @OnQueueFailed()
  async onFailed(
    { data: { bid, blogId, creditTransactionId } }: Job<BlogQueuePayload>,
    error: Error,
  ) {
    if (creditTransactionId) {
      await this.creditTransactionService.refund(bid, creditTransactionId);
      this.logger.error(
        { err: error, bid, blogId, creditTransactionId },
        'Failed to generate content, credit refunded.',
      );
    }
  }

  async generateBlogSections({
    job,
    blogId,
    bid,
    blogOutline,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    contentImagesMapping,
    blogSize,
    inputText = '',
    transcript,
    customInstruction,
    singleShotBlog,
    sourceSimilarity,
    includeQuotation,
  }): Promise<string> {
    // For shorter blogs, use orchestrated multi-agent generation
    if (singleShotBlog) {
      try {
        // Using the new orchestrated blog generation for single-shot blogs
        this.logger.log('Using orchestrated blog generation for single-shot blog');

        this.gatewayService.sendBlogStatusUpdate(bid, {
          _id: bid,
          status: 'content_generating',
          percentComplete: 10,
        });

        // Create sourceContent from inputText to avoid null reference
        const sourceContent = inputText || '';

        // Use the new blog generation service
        const result = await this.blogGenerationService.generateCompleteBlog({
          blogId,
          topic: inputText || '',
          language: blogLanguage,
          blogTone,
          blogSize,
          pov: pov || 'neutral',
          customInstruction,
          tldrPosition: BlogTldrPosition.none,
          seoInputKeywords: blogOutline.keywords || [],
          sourceContent, // Pass the sourceContent explicitly
        });

        // Save generation cost to the blog model as a simple number
        if (result.generationCost) {
          await this.blogService.update(bid, blogId, {
            generationCost: result.generationCost.totalCost,
            generationCostBreakdown: result.generationCost.breakdown,
          });

          this.logger.log(
            `Generation cost saved for blog ${blogId}: $${result.generationCost.totalCost.toFixed(4)}`,
          );
        }

        // We also have access to the research data and outline which could be saved for future use
        this.logger.log('Orchestrated blog generation completed successfully');

        const keywordsQueueData = {
          ...job.data,
          blogContent: result.content,
          blogTitle: result.title,
          sourceContent: sourceContent || '',
        };

        await this.generateBlogKeywordsQueue.add(keywordsQueueData, {
          ...JOB_OPTIONS,
          jobId: blogId,
        });
        this.logger.log(
          `Review queue bypassed for orchestrated blog ${blogId}, sent directly to keyword generation`,
        );

        this.gatewayService.sendBlogStatusUpdate(bid, {
          _id: bid,
          status: 'content_generated',
          percentComplete: 90,
        });

        return result.content;
      } catch (error) {
        console.error(error);
        this.logger.error(
          'Error in orchestrated blog generation, falling back to traditional approach',
          error,
        );
        throw error;
      }
    }

    // Existing section-by-section generation logic
    const tasks = blogOutline.sections.map(async (section, index) => {
      await delay(100);
      const sectionResp = await this.generateSectionContent({
        bid,
        blogSectionOutline: section,
        index,
        totalSections: blogOutline.sections.length,
        blogLanguage,
        eachSection,
        blogTone,
        pov,
        blogSize,
        inputText,
        transcript,
        customInstruction,
        tableOption: section.data?.length > 0 ? 'Include a table' : undefined,
        chartOption: section.data?.length > 0 ? 'Include a chart' : undefined,
        sourceSimilarity,
        blogId,
        title: blogOutline.title,
        includeQuotation,
      });
      const title = section.heading || section['title'];

      let blogSectionHtml = sectionResp.content;
      if (sectionResp.svgInstruction) {
        const svgCode = await this.blogGenerationService.generateSvg({
          svgInstruction: sectionResp.svgInstruction,
        });

        const pTagCount = (sectionResp.content.match(/<p>/g) || []).length;

        if (pTagCount >= 3) {
          const parts = sectionResp.content.split(/<p>/);
          parts.splice(3, 0, `${svgCode}`);
        }

        // only keep the svg code <svg>...</svg>
        const parsedSvgCode = svgCode.match(/<svg[^>]*>(.*?)<\/svg>/)?.[1];

        if (parsedSvgCode) {
          blogSectionHtml = `${sectionResp.content}${parsedSvgCode}`;
        }

        blogSectionHtml += `<!--SECTION_END_${index}-->`;
      }

      const processedHtml =
        await this.blogGenerationService.processBlogSectionHtml(blogSectionHtml);

      if (contentImagesMapping[title]) {
        return `<img src="${contentImagesMapping[title]}" style="width: 100%; height: auto; display: block;" alt="${title}" /><br>${processedHtml}`;
      }

      return processedHtml;
    });

    const sectionsContent = await Promise.all(tasks);
    return sectionsContent.join('<br>');
  }

  async generateSectionContent({
    bid,
    blogSectionOutline,
    index,
    totalSections,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    blogSize,
    inputText,
    transcript,
    customInstruction,
    tableOption,
    chartOption,
    sourceSimilarity,
    blogId,
    title,
    includeQuotation = true,
  }: {
    bid: string;
    blogSectionOutline: Blog['blogOutline']['sections'][0];
    index: number;
    totalSections: number;
    blogLanguage: Blog['blogLanguage'];
    eachSection: number;
    blogTone: BlogTone;
    pov: Blog['pov'];
    blogSize: BlogSize;
    inputText: string;
    transcript?: YouTubeTranscript[];
    customInstruction?: string;
    tableOption?: string;
    chartOption?: string;
    sourceSimilarity?: number;
    blogId: string;
    title: string;
    includeQuotation?: boolean;
  }): Promise<{ content: string; svgInstruction: string }> {
    const isLastSection = index >= totalSections - 1;

    // parse transcript texts from caption indexes
    const transcriptText = blogSectionOutline.captionIndexes
      ?.map((c) => {
        return transcript
          ?.slice(c.startCaptionIndex, c.endCaptionIndex)
          .filter((t) => t && typeof t === 'object' && 'text' in t)
          .map((t) => `${t.startTime?.toFixed(2)}-${t.endTime?.toFixed(2)}: ${t.text}`)
          .join('\n');
      })
      .join('\n');

    const blogGenInstruction = this.constructBlogGenInstruction({
      section: blogSectionOutline,
      isLastSection,
      blogLanguage,
      eachSection,
      blogTone,
      pov,
      customInstruction,
      blogSize,
      inputText:
        transcriptText && transcriptText.length > 50 ? transcriptText : inputText || inputText,
      hasTimestampedTranscript: !!transcriptText,
      tableOption,
      chartOption,
      title,
      includeQuotation,
    });

    const { subscriptionPlan } = await this.businessService.findOne(bid);
    const model = getPlanModel(subscriptionPlan, TASK.BLOG);

    const sectionResp = await this.blogGenerationService.generateBlogSection({
      model,
      blogSectionOutline,
      prompt: blogGenInstruction,
      sourceSimilarity,
      blogTone,
      includeQuotation,
    });

    // Track the cost for this section
    if (sectionResp.metadata?.estimatedCost > 0) {
      try {
        // Get current cost breakdown if exists
        const blog = await this.blogService.findById(bid, blogId);
        const costBreakdown = blog.generationCostBreakdown || [];

        // Add this section's cost
        costBreakdown.push({
          step: `section_${index + 1}`,
          cost: sectionResp.metadata.estimatedCost,
          model,
          timestamp: new Date(),
        });

        // Calculate total cost
        const totalCost = costBreakdown.reduce((sum, item) => sum + item.cost, 0);

        // Update blog with new costs
        await this.blogService.update(bid, blogId, {
          generationCost: totalCost,
          generationCostBreakdown: costBreakdown,
        });

        this.logger.debug(
          `Cost for section ${index + 1}/${totalSections} (${blogSectionOutline.heading}): $${sectionResp.metadata.estimatedCost.toFixed(6)}, total: $${totalCost.toFixed(6)}`,
        );
      } catch (error) {
        this.logger.error('Error tracking section cost', error);
      }
    }

    return sectionResp;
  }

  generateBlogContentImagesMapping(
    blogOutline: Blog['blogOutline'],
    contentImages: string[],
  ): Record<string, string> {
    const totalSections = blogOutline?.sections?.length || 0;
    const totalImages = contentImages?.length || 0;
    const interval = Math.floor(totalSections / totalImages);
    let currentImageIndex = 0;
    const mapping: Record<string, string> = {};

    if (totalSections === 0 || totalImages === 0) {
      return {}; // Return an empty mapping if no sections or images
    }

    // If only one image, place it in the middle section
    if (totalImages === 1) {
      const middleIndex = Math.floor(totalSections / 2);
      for (let i = 1; i < totalSections; i++) {
        const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
        // Start from 1 to skip the first section
        if (i === middleIndex) {
          mapping[title] = contentImages[0];
        } else {
          mapping[title] = null;
        }
      }
      return mapping;
    }

    for (let i = 1; i < totalSections; i++) {
      // Start from 1 to skip the first section
      const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
      if ((i + 1) % interval === 0 && currentImageIndex < totalImages) {
        mapping[title] = contentImages[currentImageIndex];
        currentImageIndex++;
      } else {
        mapping[title] = null; // No image for this section
      }
    }

    // If there are still unused images, map them to the remaining sections
    for (let i = 1; i < totalSections && currentImageIndex < totalImages; i++) {
      // Start from 1 to skip the first section
      const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
      if (!mapping[title]) {
        mapping[title] = contentImages[currentImageIndex];
        currentImageIndex++;
      }
    }

    return mapping;
  }

  constructBlogGenInstruction({
    section,
    isLastSection,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    blogSize,
    inputText,
    hasTimestampedTranscript,
    customInstruction,
    title,
    includeQuotation,
  }: {
    section: Blog['blogOutline']['sections'][0];
    isLastSection: boolean;
    blogLanguage: string;
    eachSection: number;
    blogTone: string;
    pov: string | undefined;
    blogSize: 'mini' | 'small' | 'medium' | 'large' | 'x-large';
    inputText: string | undefined;
    hasTimestampedTranscript: boolean;
    customInstruction?: string;
    tableOption?: string;
    chartOption?: string;
    title: string;
    includeQuotation: boolean;
  }): string {
    const sectionDetails = this.formatSectionDetails(section, includeQuotation);
    const sizeInstruction = this.getSizeInstruction(blogSize, isLastSection);
    const styleInstruction = `Use a ${blogTone} tone from a ${
      pov || 'neutral'
    } perspective in ${blogLanguage}.`;
    const formattingInstruction = this.getFormattingInstruction();
    const povInstruction = this.getPovInstruction(pov);

    const additionalContentInstructions = [];
    if (section.bulletPoints?.length > 0) {
      additionalContentInstructions.push(
        `Incorporate these bullet points into the content: ${section.bulletPoints.join(', ')}`,
      );
    }

    if (includeQuotation && section.quotes?.length > 0) {
      additionalContentInstructions.push(
        `Use these quotes where appropriate: ${section.quotes.join('; ')}`,
      );
    }
    if (section.svgPrompt && section.shouldGenerateChart) {
      const shuffledColors = [...predefinedColors].sort(() => Math.random() - 0.5).slice(0, 5); // Get 5 random colors

      additionalContentInstructions.push(
        `Generate a professional SVG line chart, bar chart, tree map, donut chart, column chart, Sunburst diagram, Radar chart, Gantt chart or mind map based on this prompt: ${
          section.svgPrompt
        }
        Use these color options: ${shuffledColors.join(', ')}
        use small font size for labels
        Do not mention something like "Here is the SVG code" or "Below is the SVG image"
        `,
      );
    }

    // When section.data is Record<string, ''>
    if (section.data && !Array.isArray(section.data)) {
      section.data = Object.keys(section.data).reduce((prev, curr) => {
        if (section.data[curr] === '') {
          prev.push(curr);
        }
        return prev;
      }, []);
    }

    if (section.data?.length > 0 && section.shouldGenerateTable) {
      additionalContentInstructions.push(
        `Include a table or chart with this data: ${section.data.join(', ')}`,
      );
    }

    const additionalContentSection =
      additionalContentInstructions.length > 0
        ? `8. Additional content elements:\n   ${additionalContentInstructions.join('\n   ')}\n`
        : '';

    return `
Create original, plagiarism-free blog section for the blog titled: ${title} within maximum ${eachSection} words
by strictly following the following instructions:

1. Writing style to sound like a human:
   - Use short, varied sentences. Avoid long, complex structures.
   - Mix paragraph lengths. Include some single-sentence paragraphs.
   - Use simple words. Explain complex terms.
   - Add rhetorical questions or analogies for engagement.
   - Include occasional minor errors for a human touch.
   - Break the text into multiple lines if you need to use comma or colon.

2. Content structure:
   - Use trending blog section structure.
   - Use numbered lists and bullet points for clarity if possible.
   - Bold or italicize key points if possible.
   - Add subheadings (H3-H6) to break up text if possible.
   - ${
     section.shouldGenerateTable && section.data?.length
       ? `Generate a table with the following data: ${section.data.join(', ')}. Use <table> tag.`
       : ''
   }
   - ${
     section.shouldGenerateChart && section.data?.length
       ? `Generate a chart with the following data: ${section.data.join(', ')}`
       : ''
   }

3. Voice and tone:
   ${povInstruction}
   ${styleInstruction}

4. Formatting:
   ${formattingInstruction}

5. Section details:
   ${sectionDetails}

6. Additional instructions:
   ${sizeInstruction}
   Avoid filler phrases like "in this video" or "welcome to another episode".
   ${customInstruction}

7. Source material:
   ${inputText ? `Use this as reference: """${inputText}"""` : ''}
   ${
     hasTimestampedTranscript ? 'Use relevant content from the transcript based on timestamps.' : ''
   }

${additionalContentSection}
Focus on valuable, engaging content while maintaining a natural, human-like style.
Return the content in valid HTML format with the correct tags and maximum ${eachSection} words.
`;
  }

  private formatSectionDetails(
    section: Blog['blogOutline']['sections'][0],
    includeQuotation = true,
  ): string {
    if (typeof section.quotes === 'string') {
      section.quotes = [section.quotes];
    }

    const details = [
      `Heading: ${section.heading}`,
      section.bulletPoints?.length > 0 ? `Talking Points: ${section.bulletPoints.join('. ')}` : ``,
      section.notes && `Notes: ${section.notes}`,
      section.keywords?.length > 0 && `Keywords: ${section.keywords.join(', ')}`,
      section.metaDescription && `Meta Description: ${section.metaDescription}`,
      section.data?.length > 0 && `Data: ${section.data.join(', ')}`,
      includeQuotation && section.quotes?.length > 0 && `Quotes: ${section.quotes.join('; ')}`,
      section.relevantContent && `Relevant Content: ${section.relevantContent}`,
    ].filter(Boolean);

    return details.join('\n');
  }

  private getSizeInstruction(
    blogSize: 'mini' | 'small' | 'medium' | 'large' | 'x-large',
    isLastSection: boolean,
  ): string {
    const sizeDesc =
      blogSize === 'small' ? 'concise' : blogSize === 'medium' ? 'moderate' : 'comprehensive';
    const lastSectionNote = isLastSection
      ? 'For this final section write the conclusion of the blog without bullet points.'
      : 'Do not conclude or write any conclusion for the post or preview upcoming sections. This is just a section of the blog.';

    return `Generate ${sizeDesc} content. ${lastSectionNote}`;
  }

  private getPovInstruction(pov: string | undefined): string {
    switch (pov?.toLowerCase()) {
      case 'first person':
        return 'Use "I" and "we" statements to give a personal touch.';
      case 'second person':
        return 'Address the reader directly using "you" to create engagement.';
      case 'third person':
        return 'Maintain an objective tone, using "he," "she," "they," or the subject\'s name.';
      default:
        return 'Adapt the narrative voice to best suit the content and intended audience.';
    }
  }

  private getFormattingInstruction(): string {
    return `
Use these HTML tags:
- <h2> for main heading
- <h3> to <h6> for subheadings
- <p> for paragraphs
- <ul>, <ol>, <li> for lists
- <b>, <i>, <em> for emphasis
- <blockquote> for quotes
- <code> for code snippets
- <br> for line breaks
- <table>, <tr>, <th>, <td> for tables

No overall wrapping tag needed.
`;
  }

  private convertMarkdownBoldToHtml(content: string): string {
    return content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  }

  async handleContentGenerationFailure(
    job: Job<BlogQueuePayload>,
    bid: string,
    blogId: string,
    failReason: string,
  ) {
    this.gatewayService.sendBlogStatusUpdate(job.data.uid, {
      _id: blogId,
      status: 'content_generation_failed',
      identifier: job.data.identifier,
      failReason,
      failedQueue: JOB_QUEUES.GENERATE_BLOG_CONTENT,
    });

    if (job.attemptsMade >= job.opts.attempts) {
      await this.blogService.update(bid, blogId, {
        status: 'content_generation_failed',
        failReason,
        failedQueue: JOB_QUEUES.GENERATE_BLOG_CONTENT,
      });
    }
  }
}
