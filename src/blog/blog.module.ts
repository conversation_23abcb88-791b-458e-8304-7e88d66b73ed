import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { AffiliateLinkTrackingModule } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.module';
import { CreditTransactionModule } from '@/resources/credit-transaction/credit-transaction.module';
import { FileConversionService } from '@/common/services/file-conversion.service';
import { BlogGenerationModule } from '@/blog-generation/blog-generation.module';
import { BlogifyMediaService } from '@/blogify-media/blogify-media.service';
import { PublishingModule } from '@/publishing/publishing.module';
import { AnalyticsModule } from '@/analytics/analytics.module';
import { BusinessModule } from '@/business/business.module';
import { OpenaiModule } from '@/openai/openai.module';
import { SlackModule } from '@/integrations/internal/slack/slack.module';
import { ImageModule } from '@/resources/image/image.module';
import { EventModule } from '@/event/event.module';
import { UrlService } from '@/common/services/url.service';
import { AuthModule } from '@/auth/auth.module';
import { UserModule } from '@/user/user.module';
import { S3Service } from '@/common/services/s3.service';
import { JobModule } from '@/job/job.module';

import { BlogGenerateKeywordsProcessor } from './blog-generate-keywords.processor';
import { BlogGenerateContentProcessor } from './blog-generate-content.processor';
import { BlogGenerateOutlineProcessor } from './blog-generate-outline.processor';
import { BlogDispatcherToPublishers } from './blog-dispatcher.processor';
import { BlogRequestProcessor } from './blog-request.processor';
import { BlogReviewProcessor } from './blog-review.processor';
import { BlogSchema, Blog } from './blog.model';
import { BlogController } from './blog.controller';
import { ScraperService } from './scraper.service';
import { BlogMLService } from './blog-ml/blog-ml.service';
import { BlogService } from './blog.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Blog.name, schema: BlogSchema }]),
    AffiliateLinkTrackingModule,
    CreditTransactionModule,
    BlogGenerationModule,
    PublishingModule,
    AnalyticsModule,
    BusinessModule,
    OpenaiModule,
    EventModule,
    ImageModule,
    SlackModule,
    AuthModule,
    UserModule,
    JobModule,
  ],
  providers: [
    BlogGenerateKeywordsProcessor,
    BlogGenerateContentProcessor,
    BlogGenerateOutlineProcessor,
    BlogDispatcherToPublishers,
    FileConversionService,
    BlogRequestProcessor,
    BlogReviewProcessor,
    BlogifyMediaService,
    ScraperService,
    BlogMLService,
    BlogService,
    UrlService,
    S3Service,
  ],
  controllers: [BlogController],
  exports: [MongooseModule, ScraperService, BlogMLService, BlogService],
})
export class BlogModule {}
