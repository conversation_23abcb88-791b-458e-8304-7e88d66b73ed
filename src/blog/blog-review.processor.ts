import { JOB_OPTIONS } from '@/common/constants';
import { JOB_QUEUES } from '@/common/constants/config';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { BlogGenerationOrchestrator } from '../blog-generation/agents';
import { SentencePair } from '../blog-generation/agents/HumanizationAgent';
import { BlogSize, BlogTone } from './blog.enums';
import { BlogService } from './blog.service';
/**
 * Processor for blog review queue
 * Takes generated blog content and runs it through an advanced multi-agent pipeline
 * to make it more human-like, better researched, and undetectable by AI content detection tools
 */
@Injectable()
@Processor(JOB_QUEUES.BLOG_REVIEW)
export class BlogReviewProcessor {
  private readonly logger = new Logger(BlogReviewProcessor.name);

  constructor(
    private readonly blogService: BlogService,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_KEYWORDS) private generateBlogKeywordsQueue: Queue,
    private readonly gatewayService: GatewayService,
  ) {}

  /**
   * Process a blog review job
   *
   * @param job The job containing blog information to review
   */
  @Process('review')
  async processBlogReview(
    job: Job<{
      blogId: string;
      content: string;
      title: string;
      blogTone: BlogTone;
      language: string;
      perspective?: string;
      seoKeywords?: string[];
      wordCountApprox?: number;
      targetAudience?: string;
      transcription?: string;
      prompt?: string;
      sourceType?: string;
      sourceName?: string;
      sentencePairs?: SentencePair[];
    }>,
  ) {
    const jobData = job.data;
    const blogId = jobData.blogId;

    this.logger.log(`Processing blog review for blog: ${blogId}`);

    try {
      // Get the blog from the database to verify it exists
      const blog = await this.blogService.findBlogById(blogId);

      if (!blog) {
        this.logger.error(`Blog not found: ${blogId}`);
        return;
      }

      // Send status update that the content review process has started
      this.sendStatusUpdate(blog, blogId, 'content_generating', 60);

      // Run the enhancement process
      const enhancedResult = await this.enhanceBlogContent(jobData, blogId);

      if (!enhancedResult) {
        this.handleEnhancementFailure(blog, blogId);
        return;
      }

      // Update the blog with enhanced content
      await this.updateBlogContent(blog, blogId, enhancedResult);

      // Queue the blog for keyword generation after review
      await this.queueKeywordGeneration(blog, blogId, enhancedResult);
    } catch (error) {
      this.handleProcessingError(error, blogId);
    }
  }

  /**
   * Run the content enhancement process using the orchestrator
   */
  private async enhanceBlogContent(jobData: any, blogId: string) {
    // Initialize the BlogGenerationOrchestrator for advanced content improvement
    const orchestrator = new BlogGenerationOrchestrator({
      provider: 'anthropic',
      model: 'claude-3-7-sonnet-20250219',
      identifierName: 'blogId',
      identifierValue: blogId,
    });

    // Determine blog size based on wordCountApprox
    const blogSize = this.determineBlogSize(jobData.wordCountApprox);

    // Combine transcription and prompt as sourceContent for improved relevance extraction
    // Use the content as draftContent that needs to be improved
    const sourceContent = this.buildSourceContent(
      jobData.sourceType,
      jobData.sourceName,
      jobData.transcription,
      jobData.prompt,
    );

    this.logger.debug(
      `Blog review for ${blogId}: sourceContent length: ${sourceContent?.length || 0}, draftContent length: ${
        jobData.content?.length || 0
      }, sentencePairs count: ${jobData.sentencePairs?.length || 0}`,
    );

    // Log sentence pairs if available
    if (jobData.sentencePairs && jobData.sentencePairs.length > 0) {
      this.logger.debug(
        `Blog review for ${blogId} has ${jobData.sentencePairs.length} sentence pairs to inform humanization`,
      );
    }

    // Run the orchestrator to improve the content
    const enhancedResult = await orchestrator.generateBlog({
      topic: jobData.title,
      language: jobData.language,
      blogTone: jobData.blogTone,
      blogSize,
      pov: jobData.perspective || 'second-person',
      customInstruction: '',
      seoInputKeywords: jobData.seoKeywords,
      sourceContent: sourceContent,
      draftContent: jobData.content,
      title: jobData.title,
    });

    // Store the sentence pairs in the result for future reference
    if (enhancedResult && jobData.sentencePairs && jobData.sentencePairs.length > 0) {
      enhancedResult.sentencePairs = jobData.sentencePairs;
    }

    return enhancedResult;
  }

  /**
   * Determine blog size based on word count
   */
  private determineBlogSize(wordCount?: number): BlogSize {
    if (!wordCount) return BlogSize.medium;

    if (wordCount <= 500) return BlogSize.mini;
    if (wordCount <= 800) return BlogSize.small;
    if (wordCount <= 1200) return BlogSize.medium;
    if (wordCount <= 2000) return BlogSize.large;
    return BlogSize['x-large'];
  }

  /**
   * Update blog content with enhanced result
   */
  private async updateBlogContent(blog: any, blogId: string, enhancedResult: any) {
    if (!enhancedResult || !enhancedResult.content) {
      this.logger.warn(
        `Enhancement didn't produce valid content for blog ${blogId}. The orchestrator failed to generate content.`,
      );
      return false;
    }

    // Send progress update at 80%
    this.sendStatusUpdate(blog, blogId, 'content_generating', 80);

    // Update blog content
    await this.blogService.updateBlogContent(blogId, {
      content: enhancedResult.content,
      isReviewed: true,
    });

    // Log detailed information about the enhancement process
    this.logger.log(`Successfully enhanced blog ${blogId} with improved content`);
    this.logger.debug(`Blog enhancement cost: $${enhancedResult.generationCost.toFixed(4)}`);

    // Notify client that content is fully generated (after enhancement)
    this.sendStatusUpdate(blog, blogId, 'content_generated', 100);

    return true;
  }

  /**
   * Send status update to client
   */
  private sendStatusUpdate(
    blog: any,
    blogId: string,
    status: 'content_generating' | 'content_generated' | 'content_generation_failed',
    percentComplete: number,
  ) {
    this.gatewayService.sendBlogStatusUpdate(blog.uid, {
      _id: blogId,
      identifier: blog.identifier,
      status,
      percentComplete,
    });
  }

  /**
   * Handle enhancement failure
   */
  private handleEnhancementFailure(blog: any, blogId: string) {
    this.logger.warn(
      `Enhancement didn't produce valid content for blog ${blogId}. The orchestrator failed to generate content.`,
    );

    // Notify client that an error occurred
    this.sendStatusUpdate(blog, blogId, 'content_generation_failed', 100);
  }

  /**
   * Queue keyword generation job
   */
  private async queueKeywordGeneration(blog: any, blogId: string, enhancedResult: any) {
    try {
      await this.generateBlogKeywordsQueue.add(
        {
          blogId,
          blogContent: enhancedResult.content,
          bid: blog.bid,
          uid: blog.uid,
          title: blog.title || enhancedResult.title,
          identifier: blog.identifier,
          blogLanguage: enhancedResult.language,
          // Include any SEO keywords passed from original job
          blogOutline: {
            keywords: enhancedResult.seoInputKeywords || [],
          },
          affiliateCommissionOptIn: blog.affiliateCommissionOptIn,
        },
        { ...JOB_OPTIONS, jobId: blogId },
      );
      this.logger.log(`Job added to generate blog keywords for blog ${blogId} after enhancement`);
    } catch (keywordsError) {
      this.logger.error(
        `Failed to add job to generate blog keywords for blog ${blogId} after enhancement`,
        keywordsError.message,
      );
      // Continue execution - keyword generation error shouldn't block the review process
    }
  }

  /**
   * Handle errors during processing
   */
  private handleProcessingError(error: any, blogId: string) {
    this.logger.error(`Error processing blog enhancement for blog ${blogId}: ${error.message}`);

    // Try to get the blog to send a failure status update
    try {
      this.blogService.findBlogById(blogId).then((blog) => {
        if (blog) {
          this.sendStatusUpdate(blog, blogId, 'content_generation_failed', 100);
        }
      });
    } catch (statusError) {
      this.logger.error(`Failed to send error status update: ${statusError.message}`);
    }
  }

  /**
   * Build source content by combining transcription and prompt
   * This provides the RelevanceExtractorAgent with all available source material
   */
  private buildSourceContent(
    sourceType: string,
    sourceName: string,
    transcription: string,
    prompt: string,
  ): string {
    const parts: string[] = [];

    parts.push(`## SOURCE TYPE: ${sourceType}`);
    parts.push(`## SOURCE NAME: ${sourceName}`);

    // Add transcription with proper header if it exists
    if (transcription && transcription.trim()) {
      parts.push('## TRANSCRIPTION\n\n' + transcription.trim());
    }

    // Add prompt with proper header if it exists
    if (prompt && prompt.trim()) {
      parts.push('## PROMPT\n\n' + prompt.trim());
    }

    // Join the parts with clear separation
    return parts.length > 0 ? parts.join('\n\n---\n\n') : '';
  }
}
