type BlogTone =
  | 'Neutral'
  | 'Engaging'
  | 'Inspirational'
  | 'Informative'
  | 'Professional'
  | 'Conversational'
  | 'Promotional'
  | 'Storytelling'
  | 'Educational'
  | 'News'
  | 'Humorous'
  | 'Casual'
  | 'Review'
  | 'How-to'
  | string;

type BlogPerspective = 'First Person' | 'Second Person' | 'Third Person' | string;
type BlogSize = 'mini' | 'small' | 'medium' | 'large' | 'x-large' | string;

interface BlogOutlineSection {
  heading: string;
  bulletPoints: string[];
  notes: string;
  keywords: string[];
  metaDescription: string;
  data: string[];
  quotes: string[];
  relevantContent: string;
}

export interface GenerateBlogOutlineRequest {
  title: string;
  transcript: string;
  input_language: string;
  output_language: string;
  pov: BlogPerspective;
  tone: BlogTone;
  size: BlogSize;
}
export interface GenerateBlogOutlineResponse {
  title: string[];
  summary: string;
  keywords: string[];
  meta_tags: string[];
  meta_description: string;
  outlines: [
    {
      headline: string;
      intro_copy: string;
      tldr: string;
      outline_table_of_contents: string[];
      sections: BlogOutlineSection[];
      summary: string;
      conclusion: string;
    },
  ];
}

export interface GenerateBlogTranscriptSummaryRequest {
  transcript: string;
  input_language: string;
  output_language: string;
  pov: BlogPerspective;
  tone: BlogTone;
  size: BlogSize;
}

export interface GenerateBlogSectionRequest {
  title: string;
  summary: string;
  blogOutline: BlogOutlineSection[];
  sectionHeading: string;
  sectionTalkingPoints: string[];
  keywords: string[];
  isLastSection: boolean;

  input_language: string;
  output_language: string;
  pov: BlogPerspective;
  tone: BlogTone;
  blogSize: BlogSize;
}

export interface BlogMLErrorResponse {
  detail?:
    | string
    | {
        type: 'missing' | string;
        url: string;
        msg: string;
        input: [];
        loc: [];
      }[];
}
