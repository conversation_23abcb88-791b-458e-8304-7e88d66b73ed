import type {
  GenerateBlogTranscriptSummaryRequest,
  GenerateBlogOutlineResponse,
  GenerateBlogOutlineRequest,
  GenerateBlogSectionRequest,
  BlogMLErrorResponse,
} from './blog-ml.interface';
import type { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import pRetry from 'p-retry';
import axios from 'axios';

import config from '@common/configs/config';

@Injectable()
export class BlogMLService {
  private readonly logger = new Logger(BlogMLService.name);
  private api = axios.create({
    baseURL: config().internalApps.blogifyML.url,
    headers: {
      'x-api-key': config().internalApps.blogifyML.apiKey,
    },
  });

  async generateOutlines(body: GenerateBlogOutlineRequest): Promise<GenerateBlogOutlineResponse> {
    return this.blogifyML('/generate-blog-outline', { body, type: 'blog outlines' }).catch(
      this.handleError,
    );
  }

  async generateTranscriptSummary(body: GenerateBlogTranscriptSummaryRequest): Promise<string> {
    return this.blogifyML('/summarize-transcript', {
      body,
      type: 'transcript summary',
      responseType: 'text',
    }).catch(this.handleError);
  }

  async generateSection(body: GenerateBlogSectionRequest): Promise<string> {
    return this.blogifyML('/generate-blog-section', {
      body,
      type: 'blog section',
      responseType: 'text',
    }).catch(this.handleError);
  }

  private blogifyML = async (
    url: string,
    {
      body,
      type,
      responseType = 'json',
    }: {
      body?: any;
      type?: 'blog outlines' | 'transcript summary' | 'blog section';
      responseType?: AxiosRequestConfig['responseType'];
    },
  ) => {
    const request = () =>
      this.api({ url, method: 'post', data: body, responseType }).then(
        (r: AxiosResponse) => r.data,
      );

    return pRetry(request, {
      retries: 3,
      onFailedAttempt: (e: { attemptNumber: number; retriesLeft: number }) => {
        this.logger.error(
          `Attempt ${e.attemptNumber} failed to generate ${type}. There are ${e.retriesLeft} retries left.`,
        );
      },
    });
  };

  private formatError(axiosError: AxiosError<BlogMLErrorResponse>) {
    const error = axiosError.response?.data;
    const detail = error?.detail;
    let message = '';
    if (Array.isArray(detail)) {
      detail.forEach((d) => (message += [d.msg, ...d.loc].join(',')));
    } else if (typeof detail === 'string') {
      message = detail;
    } else {
      message = JSON.stringify(error);
    }
    return message || 'Something went wrong with Blogify ML API!';
  }

  private handleError = (error: AxiosError<BlogMLErrorResponse>) => {
    // console.log('Blogify ML Error:', error);
    const message = this.formatError(error);
    throw new Error(message);
  };
}
