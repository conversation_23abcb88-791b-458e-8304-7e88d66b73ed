import type { BlogHeading } from './blog.type';

import { load } from 'cheerio';

export const attachIdsToBlogHeadings = (content: string): string => {
  if (!content) return '';

  const $ = load(content);
  const headingElements = $('h2, h3');

  headingElements.each((index, element) => {
    const $element = $(element);
    if (!$element.attr('id')) {
      $element.attr('id', `heading-${index}`);
    }
  });

  return $.html();
};

export const getTableOfContents = (content: string): BlogHeading[] => {
  if (!content) return [];

  const $ = load(content);
  const headingElements = $('h2, h3');
  const headings: BlogHeading[] = [];

  headingElements.each((index, element) => {
    const $element = $(element);
    let id = $element.attr('id');

    if (!id) {
      id = `heading-${index}`;
      $element.attr('id', id);
    }

    headings.push({
      id,
      text: $element.text(),
      level: (element as unknown as Element).tagName?.toLowerCase(),
    });
  });

  return headings;
};
