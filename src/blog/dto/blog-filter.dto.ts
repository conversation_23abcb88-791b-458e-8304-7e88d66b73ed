import { IsEnum, IsInt, IsString } from 'class-validator';
import { BlogPublishStatus, BlogSourceName } from '@blog/blog.enums';
import { ApiProperty } from '@nestjs/swagger';
import { Blog } from '@blog/blog.model';

export class BlogFilterDto {
  @ApiProperty({ default: 1 })
  @IsInt()
  readonly page: number = 1;

  @ApiProperty({ default: 10 })
  @IsInt()
  readonly limit: number = 10;

  @ApiProperty()
  @IsString()
  readonly q: string;

  @ApiProperty()
  @IsEnum(BlogSourceName)
  readonly sourceName: Blog['sourceName'];

  @ApiProperty()
  @IsString()
  readonly uid: string;

  @ApiProperty()
  @IsEnum(BlogPublishStatus)
  readonly publishStatus: Blog['publishStatus'];

  @ApiProperty()
  @IsString()
  readonly publishedOn: string;

  @ApiProperty()
  @IsInt()
  readonly wordCountMin: number;

  @ApiProperty()
  @IsInt()
  readonly wordCountMax: number;

  @ApiProperty()
  @IsString()
  readonly identifier: string;
}
