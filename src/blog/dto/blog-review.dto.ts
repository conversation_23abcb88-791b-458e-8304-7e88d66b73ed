import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BlogTone, BlogPov, BlogSize } from '../blog.enums';

export class BlogReviewOptionsDto {
  @ApiProperty({ enum: BlogTone, required: false })
  @IsOptional()
  @IsEnum(BlogTone)
  tone?: BlogTone;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ enum: BlogPov, required: false })
  @IsOptional()
  @IsEnum(BlogPov)
  perspective?: BlogPov;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  targetAudience?: string;

  @ApiProperty({ enum: BlogSize, required: false })
  @IsOptional()
  @IsEnum(BlogSize)
  blogSize?: BlogSize;
}
