import type {
  BlogAffiliateLinkGenerationStatus,
  SocialPlatform,
  BlogPlatform,
  BlogStatus,
} from '../blog.type';

import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsObject,
  IsString,
  IsArray,
  IsDate,
} from 'class-validator';
import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { BlogAffiliateKeyword } from '../blog.model';

export class UpdateBlogDto {
  @ApiHideProperty()
  @IsOptional()
  @IsString()
  readonly uid?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly title?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly failReason?: string;

  @IsString()
  @IsOptional()
  readonly failedQueue?: string;

  readonly totalRetries?: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly content?: string;

  @ApiProperty({ type: [String], default: [] })
  @IsArray()
  @IsOptional()
  readonly keywords?: string[];

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly facebookContent?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly twitterContent?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly linkedinContent?: string;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  readonly publishAt?: Date;

  @ApiProperty({ type: [String], default: [] })
  @IsArray()
  @IsOptional()
  readonly platforms?: BlogPlatform[];

  @ApiProperty({ type: [String], default: [] })
  @IsArray()
  @IsOptional()
  readonly socials?: SocialPlatform[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly status?: BlogStatus;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly affiliateLinkGenerationStatus?: BlogAffiliateLinkGenerationStatus;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly inputLanguage?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly blogLanguage?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wordpressLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wordpressorgLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly bloggerLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly mediumLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wixLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly mailchimpLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly facebookLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly twitterLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly linkedinLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly zapierLink?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly publishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wordpressPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wordpressorgPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly bloggerPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly mediumPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly wixPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly mailchimpPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly facebookPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly twitterPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly linkedinPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly zapierPublishTime?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly transcription?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly transcriptionSummary?: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  readonly blogOutline?: object;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly generationStatus?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  readonly metaDescription?: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  readonly contentImagesMapping?: object;

  @IsOptional()
  @IsArray()
  affiliateKeywords?: BlogAffiliateKeyword[];

  @IsOptional()
  @IsDate()
  affiliateKeywordsUpdatedAt?: Date;

  @ApiPropertyOptional()
  generationCost?: number;

  @ApiPropertyOptional()
  generationCostBreakdown?: {
    step: string;
    cost: number;
    model: string;
    timestamp: Date;
  }[];
}
