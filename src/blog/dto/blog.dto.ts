import type { SocialPlatform, BlogPlatform } from '../blog.type';

import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsString } from 'class-validator';

export class BlogDto {
  @ApiProperty()
  @IsString()
  readonly title: string;

  @ApiProperty()
  @IsString()
  readonly content: string;

  @ApiProperty()
  @IsDateString()
  readonly publishAt: Date;

  @ApiProperty({ type: [String], default: [] })
  @IsArray()
  readonly platforms: BlogPlatform[] | string[];

  @ApiProperty({ type: [String], default: [] })
  @IsArray()
  readonly socials: SocialPlatform[] | string[];
}

export class PaginatedBlogsDto {
  @ApiProperty({ type: [BlogDto] })
  readonly data: BlogDto[];

  @ApiProperty({ type: Number })
  readonly total: number;
}
