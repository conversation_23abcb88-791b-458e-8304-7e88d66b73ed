import { IsOptional, IsString, IsArray } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

import { Blog } from '../blog.model';

export class CreateManualBlogDto {
  // Blog Contents
  @ApiPropertyOptional({ description: 'The title of the blog post' })
  @IsString()
  readonly title: string;

  @ApiPropertyOptional({ description: 'The content of the blog post' })
  @IsString()
  readonly content: string;
  // End Blog Contents

  // Images
  @ApiPropertyOptional({ description: 'The cover image of the blog post' })
  @IsString()
  image?: string;
  // End Images

  // Meta Info
  @ApiPropertyOptional({ description: 'List of keywords for SEO' })
  @IsString({ each: true })
  @IsArray()
  readonly keywords: string[];

  @ApiPropertyOptional({ description: 'Meta Description for SEO' })
  @IsString()
  readonly metaDescription: string;
  // End Meta Info

  // Publish Settings
  @ApiPropertyOptional({
    description: 'The platforms where the blog should be published',
    example: [
      {
        platform: 'wordpress',
        timestamp: '2022-03-01T08:00:00.000Z',
        draft: false,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  readonly platforms?: Blog['platforms'];
  // End Publish Settings
}
