import { Is<PERSON>ptional, IsBoolean, IsString, <PERSON>A<PERSON><PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

import { ShopifyProductDto } from './shopify-product.dto';
import { CreateBaseBlogDto } from './create-blog.dto';

export class CreateBulkBlogDto extends CreateBaseBlogDto {
  /* User Inputs */
  // Source Info
  @ApiPropertyOptional({ description: 'Array of YouTube URLs' })
  @IsString()
  @IsOptional()
  @IsArray()
  readonly urls?: string[];
  // End Source Info

  // YouTube
  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isYoutubeBulk?: boolean;

  // Shopify
  @ApiPropertyOptional({ description: 'Array of Shopify product data' })
  @IsString()
  @IsOptional()
  @IsArray()
  readonly shopifyData?: ShopifyProductDto[];
}
