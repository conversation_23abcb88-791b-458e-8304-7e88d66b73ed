import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsBoolean,
  IsDate,
  IsObject,
  IsOptional,
  IsArray,
} from 'class-validator';

class PriceDto {
  @IsString()
  @IsOptional()
  readonly amount: string;

  @IsString()
  @IsOptional()
  readonly currency_code: string;
}

class PresentmentPriceDto {
  @IsObject()
  @IsOptional()
  readonly price: PriceDto;

  @IsObject()
  @IsOptional()
  readonly compare_at_price?: PriceDto;
}

class VariantDto {
  @IsNumber()
  @IsOptional()
  readonly id: number;

  @IsNumber()
  @IsOptional()
  readonly product_id: number;

  @IsString()
  @IsOptional()
  readonly title: string;

  @IsString()
  @IsOptional()
  readonly price: string;

  @IsString()
  @IsOptional()
  readonly sku: string;

  @IsNumber()
  @IsOptional()
  readonly position: number;

  @IsString()
  @IsOptional()
  readonly inventory_policy: string;

  @IsString()
  @IsOptional()
  readonly fulfillment_service: string;

  @IsString()
  @IsOptional()
  readonly inventory_management: string;

  @IsString()
  @IsOptional()
  readonly option1: string;

  @IsString()
  @IsOptional()
  readonly option2: string;

  @IsString()
  @IsOptional()
  readonly option3: string;

  @IsDate()
  @IsOptional()
  readonly created_at: Date;

  @IsDate()
  @IsOptional()
  readonly updated_at: Date;

  @IsBoolean()
  @IsOptional()
  readonly taxable: boolean;

  @IsString()
  @IsOptional()
  readonly barcode: string;

  @IsNumber()
  @IsOptional()
  readonly grams: number;

  @IsNumber()
  @IsOptional()
  readonly weight: number;

  @IsString()
  @IsOptional()
  readonly weight_unit: string;

  @IsNumber()
  @IsOptional()
  readonly inventory_item_id: number;

  @IsNumber()
  @IsOptional()
  readonly inventory_quantity: number;

  @IsNumber()
  @IsOptional()
  readonly old_inventory_quantity: number;

  @IsArray()
  @IsOptional()
  readonly presentment_prices: PresentmentPriceDto[];

  @IsBoolean()
  @IsOptional()
  readonly requires_shipping: boolean;

  @IsString()
  @IsOptional()
  readonly admin_graphql_api_id: string;
}

class OptionDto {
  @IsNumber()
  @IsOptional()
  readonly id: number;

  @IsNumber()
  @IsOptional()
  readonly product_id: number;

  @IsString()
  @IsOptional()
  readonly name: string;

  @IsArray()
  @IsOptional()
  readonly values: string[];
}

class ImageDto {
  @IsNumber()
  @IsOptional()
  readonly id: number;

  @IsNumber()
  @IsOptional()
  readonly product_id: number;

  @IsString()
  @IsOptional()
  readonly src: string;

  @IsNumber()
  @IsOptional()
  readonly width: number;

  @IsNumber()
  @IsOptional()
  readonly height: number;

  @IsDate()
  @IsOptional()
  readonly created_at: Date;

  @IsDate()
  @IsOptional()
  readonly updated_at: Date;

  @IsString()
  @IsOptional()
  readonly admin_graphql_api_id: string;

  @IsArray()
  @IsOptional()
  readonly variant_ids: number[];
}

export class ShopifyProductDto {
  @ApiPropertyOptional({ description: 'Unique identifier of the product' })
  @IsString()
  @IsOptional()
  readonly id?: number;

  @ApiPropertyOptional({ description: 'Title of the product' })
  @IsString()
  readonly title: string;

  @ApiPropertyOptional({ description: 'HTML content describing the product' })
  @IsString()
  readonly body_html: string;

  @ApiPropertyOptional({ description: 'Vendor of the product' })
  @IsString()
  @IsOptional()
  readonly vendor?: string;

  @ApiPropertyOptional({ description: 'Type of the product' })
  @IsString()
  @IsOptional()
  readonly product_type?: string;

  @ApiPropertyOptional({ description: 'Handle of the product' })
  @IsString()
  @IsOptional()
  readonly handle?: string;

  @ApiPropertyOptional({ description: 'Creation date of the product' })
  @IsDate()
  @IsOptional()
  readonly created_at?: Date;

  @ApiPropertyOptional({ description: 'Last updated date of the product' })
  @IsDate()
  @IsOptional()
  readonly updated_at?: Date;

  @ApiPropertyOptional({ description: 'Publication date of the product' })
  @IsDate()
  @IsOptional()
  readonly published_at?: Date;

  @ApiPropertyOptional({ description: 'Template suffix' })
  @IsString()
  @IsOptional()
  readonly template_suffix?: string;

  @ApiPropertyOptional({ description: 'Scope of publication' })
  @IsString()
  @IsOptional()
  readonly published_scope?: string;

  @ApiPropertyOptional({ description: 'Tags associated with the product' })
  @IsString()
  @IsOptional()
  readonly tags?: string;

  @ApiPropertyOptional({ description: 'Status of the product' })
  @IsString()
  @IsOptional()
  readonly status?: string;

  @ApiPropertyOptional({ description: 'GraphQL API ID' })
  @IsString()
  @IsOptional()
  readonly admin_graphql_api_id?: string;

  @ApiPropertyOptional({ description: 'Product variants' })
  @IsArray()
  @IsOptional()
  readonly variants?: VariantDto[];

  @ApiPropertyOptional({ description: 'Product options' })
  @IsArray()
  @IsOptional()
  readonly options?: OptionDto[];

  @ApiPropertyOptional({ description: 'Product images' })
  @IsArray()
  @IsOptional()
  readonly images?: ImageDto[];

  @ApiPropertyOptional({ description: 'Main image of the product' })
  @IsObject()
  @IsOptional()
  readonly image?: ImageDto;
}
