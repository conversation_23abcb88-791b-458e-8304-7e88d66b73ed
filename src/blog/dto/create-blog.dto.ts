import type { GenerateImageDto } from '@/resources/image/dto/generate-image.dto';

import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  // IsDateString,
  ValidateIf,
} from 'class-validator';

import {
  BlogCoverImageType,
  BlogGenerationMode,
  BlogPov,
  BlogSize,
  BlogSourceName,
  BlogSourceType,
  BlogTldrPosition,
  BlogTone,
} from '../blog.enums';

import { Blog, SeoOption } from '../blog.model';
import { ShopifyProductDto } from './shopify-product.dto';

export class CreateBaseBlogDto {
  /* User Inputs */
  // Source Info
  @ApiProperty({ description: 'Type of the source' })
  @IsEnum(BlogSourceType)
  readonly sourceType: Blog['sourceType'];

  @ApiProperty({ description: 'Name of the source' })
  @IsEnum(BlogSourceName)
  readonly sourceName: Blog['sourceName'];
  // End Source Info

  // Blog Settings
  @ApiPropertyOptional({
    description: 'Auto Pilot or Co-Pilot Generation Mode',
    default: BlogGenerationMode.Auto,
  })
  @IsEnum(BlogGenerationMode)
  @IsOptional()
  readonly generationMode?: Blog['generationMode'] | string = 'auto'; // BlogGenerationMode.Auto

  @ApiPropertyOptional({ description: 'Point of View', default: BlogPov.FirstPerson })
  @IsOptional()
  @IsString()
  @IsEnum(BlogPov)
  readonly pov?: Blog['pov'] | string = 'First Person'; // BlogPov.FirstPerson

  @ApiPropertyOptional({ description: 'Blog Tone', default: BlogTone.Neutral })
  @IsOptional()
  @IsString()
  @IsEnum(BlogTone)
  readonly blogTone?: Blog['blogTone'] | string = 'Neutral'; // BlogTone.Neutral

  @ApiPropertyOptional({ description: 'Blog Size', default: BlogSize.medium })
  @IsOptional()
  @IsString()
  @IsEnum(BlogSize)
  readonly blogSize?: Blog['blogSize'] | string = 'medium'; // BlogSize.medium;

  @ApiPropertyOptional({ description: 'Blog Size', default: BlogTldrPosition.end })
  @IsOptional()
  @IsString()
  @IsEnum(BlogTldrPosition)
  readonly tldrPosition?: Blog['tldrPosition'] | string = 'end'; // BlogTldrPosition.end;

  @ApiPropertyOptional({ description: 'Input Language', default: 'Global English' })
  @IsOptional()
  @IsString()
  readonly inputLanguage?: string = 'Global English';

  @ApiPropertyOptional({ description: 'Blog Language', default: 'english' })
  @IsOptional()
  @IsString()
  readonly blogLanguage?: string = 'english';

  @ApiPropertyOptional({ description: 'Table Option', default: 'No Table' })
  @IsOptional()
  @IsString()
  readonly tableOption?: string = 'No Chart';

  @ApiPropertyOptional({ description: 'Chart Option', default: 'A single chart' })
  @IsOptional()
  @IsString()
  readonly chartOption?: string = 'A single chart';

  @ApiPropertyOptional({ description: 'Whether to include quotation', default: true })
  @IsOptional()
  @IsBoolean()
  readonly includeQuotation?: boolean = true;

  @ApiPropertyOptional({ description: 'Whether to embed the source media', default: false })
  @IsOptional()
  @IsBoolean()
  readonly embedSource?: boolean;

  @ApiPropertyOptional({ description: 'Whether to embed the source as cover', default: false })
  @IsOptional()
  @IsBoolean()
  readonly embedSourceAsCover?: boolean;

  @ApiPropertyOptional({ description: 'Custom Instructions' })
  @IsOptional()
  @IsString()
  readonly customInstruction?: string;

  @ApiPropertyOptional({ description: 'Opt-In to Affiliate Commission', default: false })
  @IsOptional()
  @IsBoolean()
  readonly affiliateCommissionOptIn?: boolean;

  @ApiPropertyOptional({ description: 'List of author names to give credits' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  readonly authorsToGiveCredit?: string[];

  @ApiPropertyOptional({
    description: 'SEO analysis option',
    enum: SeoOption,
    default: SeoOption.PASSIVE,
  })
  @IsOptional()
  @IsEnum(SeoOption)
  readonly seoOption?: SeoOption;
  // End Blog Settings

  // Images
  @ApiPropertyOptional({ description: 'The cover image of the blog post' })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiPropertyOptional({ description: 'Cover Image Type', enum: BlogCoverImageType })
  @IsOptional()
  @IsEnum(BlogCoverImageType)
  readonly coverImageType?: BlogCoverImageType;

  @ApiPropertyOptional({ description: 'AI Generated cover image config' })
  @IsOptional()
  @IsObject()
  readonly aiGeneratedCoverImageConfig?: Omit<GenerateImageDto, 'prompt'>;

  @ApiPropertyOptional({ description: 'The content images of the blog post' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contentImages?: string[];

  @ApiPropertyOptional({ description: 'AI Generated content images config' })
  @IsOptional()
  @IsObject()
  readonly aiGeneratedContentImagesConfig?: Omit<GenerateImageDto, 'prompt'> & { count: number };
  // End Images

  // Publish Settings
  @ApiPropertyOptional({
    description: 'The platforms where the blog should be published',
    example: [
      {
        platform: 'wordpress',
        timestamp: '2022-03-01T08:00:00.000Z',
        draft: false,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  readonly platforms?: Blog['platforms'];

  @ApiPropertyOptional({
    description: 'The social media platforms where the blog should be shared',
    example: [
      {
        platform: 'twitter',
        timestamp: '2022-03-01T08:00:00.000Z',
        draft: false,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  readonly socials?: Blog['socials'];
  // End Publish Settings

  // Blog Contents
  @ApiPropertyOptional({ description: 'The title of the blog post' })
  @IsOptional()
  @IsString()
  readonly title?: string;

  @ApiPropertyOptional({
    description: 'The date the blog post should be published',
    example: '2022-03-01T08:00:00.000Z',
  })
  // @IsDateString()
  @IsOptional()
  readonly publishAt?: Date;
  // End Blog Contents

  // Identifiers
  @ApiHideProperty()
  @IsOptional()
  @IsString()
  identifier?: string;

  @ApiHideProperty()
  @IsOptional()
  @IsBoolean()
  isPartOfBulkGeneration?: boolean;
  // End Identifiers

  @ApiPropertyOptional({ description: 'Approximate word count target for the blog' })
  @IsOptional()
  @IsNumber()
  readonly wordCountApprox?: number;

  @ApiPropertyOptional({
    description: 'Target similarity percentage with source content (0-100)',
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  readonly sourceSimilarity?: number;

  @ApiPropertyOptional({ description: 'SEO keywords to target in the content' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  readonly seoInputKeywords?: string[];

  @ApiPropertyOptional({
    description: 'Whether to optimize content for SEO',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  readonly seoOptimization?: boolean;

  @ApiPropertyOptional({
    description: 'Target location for affiliate content optimization',
  })
  @IsOptional()
  @IsString()
  readonly affiliateTargetLocation?: string;

  @ApiPropertyOptional({
    type: Object,
    default: {
      text: '',
      link: '',
      bgColor: '',
      borderRadius: 0,
    },
  })
  @IsObject()
  @IsOptional()
  readonly cta?: {
    text: string;
    link: string;
    bgColor: string;
    borderRadius: number;
  };
}

export class CreateBlogDto extends CreateBaseBlogDto {
  // Source Info
  // Source Information: Either URL or prompt is required
  @ApiProperty({ description: 'URL of the source' })
  @IsString()
  @IsUrl()
  @ValidateIf((dto) => !dto.prompt)
  url: string;

  @ApiProperty({ description: 'The prompt for the blog post' })
  @IsString()
  @ValidateIf((dto) => !dto.url)
  prompt: string;
  // End Source Info

  // YouTube
  @ApiPropertyOptional({ description: 'YouTube Video ID' })
  @IsOptional()
  @IsString()
  youtubeVideoId?: string;

  @ApiPropertyOptional({
    description: 'YouTube or other media Start & End time',
    example: '10:140 (10 seconds to 140 seconds)',
  })
  @IsOptional()
  @IsString()
  readonly clipTimeSpan?: string;

  // Shopify
  @ApiPropertyOptional({ description: 'Shopify product id' })
  @IsOptional()
  @IsString()
  shopifyProductId?: string;

  @ApiPropertyOptional({ description: 'Shopify product data' })
  @IsOptional()
  readonly shopifyData?: ShopifyProductDto;
}
