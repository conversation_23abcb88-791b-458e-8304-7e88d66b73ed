import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { load } from 'cheerio';
import { BlogSourceName } from './blog.enums';
import { extract, pruneWhiteSpace } from '@/common/utils/parse';
import Redis from 'ioredis';
import { RateLimiterRedis } from 'rate-limiter-flexible';

@Injectable()
export class ScraperService {
  private readonly logger = new Logger(ScraperService.name);
  private oxylabsScraper: AxiosInstance;
  private rateLimiter: RateLimiterRedis;

  constructor(private readonly configService: ConfigService) {
    const username = this.configService.getOrThrow<string>('OXYLABS_USERNAME');
    const password = this.configService.getOrThrow<string>('OXYLABS_PASSWORD');

    if (!username || !password) {
      this.logger.error('Oxylabs credentials not properly configured');
      throw new Error('Oxylabs credentials not properly configured');
    }

    this.oxylabsScraper = axios.create({
      baseURL: 'https://realtime.oxylabs.io',
      auth: { username, password },
    });
    this.logger.log('ScraperService initialized with Oxylabs configuration');

    // Initialize persistent rate limiter using Redis
    const redisURL = this.configService.get<string>('REDIS_URL');
    if (!redisURL) {
      this.logger.error('REDIS_URL is not configured');
      throw new Error('REDIS_URL is not configured');
    }
    const redisClient = new Redis(redisURL, { enableOfflineQueue: false });
    redisClient.on('error', (err) => {
      this.logger.error('Redis error in rate limiter:', err);
    });
    this.rateLimiter = new RateLimiterRedis({
      storeClient: redisClient,
      points: 10, // 10 requests per second rate limit for Oxylabs
      duration: 1, // per second
      blockDuration: 0, // wait instead of blocking
      keyPrefix: 'oxylabs_rate_limiter',
    });
  }

  tags = {
    Apple: ['div .component-content .pagebody-header', 'div .component-content .pagebody-copy'],
    Verge: ['div .duet--article--article-body-component p'],
    Blogger: ['.post-body'],
    BBC: ['.fYAfXe'],
  } as const;

  private async rateLimitedPost(endpoint: string, requestData: any): Promise<any> {
    try {
      await this.rateLimiter.consume('oxylabs');
    } catch (rejRes: any) {
      this.logger.warn(`Rate limit exceeded; delaying for ${rejRes.msBeforeNext} ms`);
      await new Promise((resolve) => setTimeout(resolve, rejRes.msBeforeNext));
      await this.rateLimiter.consume('oxylabs');
    }
    return this.oxylabsScraper.post(endpoint, requestData);
  }

  scrape = async (url: URL, sourceName: BlogSourceName) => {
    try {
      if (!url) throw new Error('URL is required');
      this.logger.debug(`Starting scrape for ${url} with source ${sourceName}`);

      return sourceName === BlogSourceName.Amazon
        ? this.fetchAmazonProduct(url)
        : sourceName === BlogSourceName.Walmart
          ? this.fetchWalmartProduct(url)
          : sourceName === BlogSourceName.Etsy
            ? this.fetchEtsyProduct(url)
            : this.fetchHTML(url)
                .then((s) => this.parse(s, this.tags[sourceName]))
                .then((data) =>
                  Object.values<string>(data).reduce(
                    (prev, current) => prev.concat(' ').concat(current),
                    '',
                  ),
                );
    } catch (error) {
      this.logger.error(`Scraping failed for ${url}`, error.stack);
      throw new Error(`Scraping failed for ${url}: ${error.message}`);
    }
  };

  async fetchHTML(url: URL) {
    try {
      this.logger.debug(`Fetching HTML for ${url}`);
      const { data } = await this.rateLimitedPost('/v1/queries', {
        url: url.toString(),
        render: 'html',
        source: 'universal',
      });

      if (!data?.results?.[0]?.content) {
        this.logger.error(`Invalid response format from Oxylabs for ${url}`);
        throw new Error('Invalid response format from Oxylabs');
      }

      return data.results[0].content as string;
    } catch (error) {
      if (error?.response?.status === 401) {
        this.logger.error('Authentication failed with Oxylabs service', error.stack);
        throw new Error('Authentication failed with Oxylabs service');
      }
      this.logger.error(`HTML fetch failed for ${url}`, error.message);
      throw new Error(`HTML fetch failed: ${error.message}`);
    }
  }

  private async fetchWalmartProduct(url: URL) {
    try {
      this.logger.debug(`Fetching Walmart product from ${url}`);
      const { data } = await this.rateLimitedPost('/v1/queries', {
        url: url.toString(),
        source: 'universal',
        parse: true,
      });

      if (!data?.results?.[0]?.content) {
        this.logger.error(`Invalid Walmart product data format for ${url}`);
        throw new Error('Invalid Walmart product data format');
      }

      return JSON.stringify(data.results[0].content) as string;
    } catch (error) {
      this.logger.error(`Walmart product fetch failed for ${url}`, error.stack);
      throw new Error(`Walmart product fetch failed: ${error.message}`);
    }
  }

  private async fetchEtsyProduct(url: URL) {
    try {
      this.logger.debug(`Fetching Etsy product from ${url}`);
      const { data } = await this.rateLimitedPost('/v1/queries', {
        url: url.toString(),
        source: 'universal',
      });

      const html = data.results[0].content as string;
      if (!html) {
        this.logger.error(`No HTML content received for Etsy product ${url}`);
        throw new Error('No HTML content received');
      }

      const $ = load(html);
      const products = $('script[type="application/ld+json"]')
        .contents()
        .get()
        .map(({ data }) => JSON.parse(data) as Record<'@type' | 'name' | 'description', string>)
        .filter((data) => data['@type'] === 'Product')
        .map((data) => [data['name'], data['description']].join(' '));

      if (!products.length) {
        this.logger.error(`No product data found for Etsy URL ${url}`);
        throw new Error('No product data found');
      }

      return products.join(' ');
    } catch (error) {
      this.logger.error(`Etsy product fetch failed for ${url}`, error.stack);
      throw new Error(`Etsy product fetch failed: ${error.message}`);
    }
  }

  private async fetchAmazonProduct(url: URL) {
    try {
      this.logger.debug(`Fetching Amazon product from ${url}`);
      const paths = url.pathname.split('/');
      const index = paths.indexOf('dp') + 1;
      const productID = paths[index];

      if (!productID) {
        this.logger.error(`Invalid Amazon product URL: ${url}`);
        throw new Error('Invalid Amazon product URL');
      }

      const tld = url.host.match(/(?<=amazon\.)\w+(\.\w+)?/)?.[0];
      if (!tld) {
        this.logger.error(`Invalid Amazon domain: ${url.host}`);
        throw new Error('Invalid Amazon domain');
      }

      const response = await this.rateLimitedPost('/v1/queries', {
        source: 'amazon_product',
        query: productID,
        parse: true,
        domain: tld,
      });

      if (!response?.data?.results?.[0]?.content) {
        this.logger.error(`Invalid Amazon product data format for ${url}`);
        throw new Error('Invalid Amazon product data format');
      }

      return JSON.stringify(response.data.results[0].content);
    } catch (error) {
      this.logger.error(`Amazon product fetch failed for ${url}`, error.stack);
      throw new Error(`Amazon product fetch failed: ${error.message}`);
    }
  }

  private parse = (html: string, tags?: string[]) => {
    try {
      if (!html) {
        this.logger.error('No HTML content to parse');
        throw new Error('No HTML content to parse');
      }

      const $ = load(html);
      return {
        pageTitle: extract.pageTitle($),
        metaDescription: extract.metaDescription($),
        tagsText: pruneWhiteSpace(extract.tagsText($, tags)),
      };
    } catch (error) {
      this.logger.error('HTML parsing failed', error.stack);
      throw new Error(`HTML parsing failed: ${error.message}`);
    }
  };

  async search(term: string, country: string, count: number): Promise<Array<URL>> {
    try {
      if (!term) {
        this.logger.error('Search term is required');
        throw new Error('Search term is required');
      }

      this.logger.debug(`Searching for "${term}" in ${country}, limit: ${count}`);
      const { data } = await this.rateLimitedPost('/v1/queries', {
        source: 'google_search',
        query: `blog articles about ${term}`,
        parse: true,
        geo_location: country,
        start_page: 1,
        limit: count,
        page: 1,
      });

      if (!data?.results?.[0]?.content?.results?.organic) {
        this.logger.error('Invalid search results format', { term, country, count });
        throw new Error('Invalid search results format');
      }

      const urls = data.results[0].content.results.organic
        .map(({ url }) => {
          try {
            return new URL(url);
          } catch (error) {
            this.logger.warn(`Invalid URL in search results: ${url}`, error?.message);
            return null;
          }
        })
        .filter(Boolean);

      this.logger.debug(`Found ${urls.length} valid URLs for search term "${term}"`);
      return urls;
    } catch (error) {
      if (error?.response?.status === 401) {
        this.logger.error('Authentication failed with Oxylabs service', error.stack);
        throw new Error('Authentication failed with Oxylabs service');
      }
      this.logger.error(`Search failed for term "${term}"`, error.stack);
      throw new Error(`Search failed: ${error.message}`);
    }
  }
}
