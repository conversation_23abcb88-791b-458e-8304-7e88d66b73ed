import { BadRequestException, ForbiddenException } from '@nestjs/common';

enum ErrorCode {
  InvalidUrl = 'INVALID_URL',
  PrivateUrl = 'PRIVATE_URL',
  DurationLimitExceed = 'DURATION_LIMIT_EXCEED',
  NoSpokenWords = 'NO_SPOKEN_WORDS',
  FileNotDownloadable = 'FILE_NOT_DOWNLOADABLE',
  UnsupportedFile = 'UNSUPPORTED_FILE',
  NoTextContent = 'NO_TEXT_CONTENT',
  PromptTooLarge = 'PROMPT_TOO_LARGE',
}

const ERROR_MESSAGES = {
  InvalidUrl: 'Invalid url provided.',
  PrivateUrl: 'Private url provided.',
  DurationLimitExceed: 'Duration limited exceeded',
  NoSpokenWords: `The media doesn't have any spoken words, couldn't generate transcription.`,
  FileNotDownloadable: 'The file is not directly downloadable',
  UnsupportedFile: 'Unsupported file format',
  NoTextContent: `The document file doesn't have any text content.`,
  PromptTooLarge: `The input text is too large to generate blog from it.`,
};

export class InvalidUrlException extends BadRequestException {
  code = ErrorCode.InvalidUrl;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.InvalidUrl);
  }
}

export class InaccessibleUrlException extends ForbiddenException {
  code = ErrorCode.PrivateUrl;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.PrivateUrl);
  }
}

export class DurationLimitExceedException extends BadRequestException {
  code = ErrorCode.DurationLimitExceed;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.DurationLimitExceed);
  }
}

export class NoSpokenWordsException extends BadRequestException {
  code = ErrorCode.NoSpokenWords;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.NoSpokenWords);
  }
}

export class FileNotDownloadableException extends BadRequestException {
  code = ErrorCode.FileNotDownloadable;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.FileNotDownloadable);
  }
}

export class NoTextContentException extends BadRequestException {
  code = ErrorCode.NoTextContent;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.NoTextContent);
  }
}

export class UnsupportedFileException extends BadRequestException {
  code = ErrorCode.UnsupportedFile;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.UnsupportedFile);
  }
}

export class PromptTooLargeException extends BadRequestException {
  code = ErrorCode.PromptTooLarge;

  constructor(message?: string) {
    super(message || ERROR_MESSAGES.PromptTooLarge);
  }
}
