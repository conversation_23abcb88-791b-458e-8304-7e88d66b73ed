// بسم الله الرحمن الرحيم
import type { Queue, Job } from 'bull';
import type { Model } from 'mongoose';

import { InjectQueue, OnQueueCompleted, OnQueueFailed, Processor, Process } from '@nestjs/bull';
import { InjectModel } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';
import { Logger } from '@nestjs/common';

import { USER_FRIENDLY_ERROR_MESSAGE, JOB_QUEUES } from '@/common/constants';
import { BlogQueuePayload, Blog } from '@/blog/blog.model';
import { PublishingService } from '@/publishing/publishing.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { BlogService } from '@/blog/blog.service';

@Processor(JOB_QUEUES.DISPATCH_TO_PUBLISHERS)
export class BlogDispatcherToPublishers extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.DISPATCH_TO_PUBLISHERS;
  protected readonly logger = new Logger(BlogDispatcherToPublishers.name);

  constructor(
    @InjectQueue(JOB_QUEUES.DISPATCH_TO_PUBLISHERS) dispatchQueue: Queue,
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    private readonly blogService: BlogService,
    private readonly publishingService: PublishingService,
    private readonly gatewayService: GatewayService,
    private readonly slackService: SlackService,
  ) {
    super(dispatchQueue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { blogId, uid, platforms = [], identifier } = job.data;

      await this.blog.findByIdAndUpdate(blogId, {
        status: 'publisher_dispatching',
      });
      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'publisher_dispatching',
      });

      await Promise.allSettled(
        platforms.map(({ platform, timestamp, draft = false, sites }) =>
          this.publishingService.addToPublishQueue({
            sites,
            timestamp: timestamp ? new Date(timestamp) : new Date(),
            payload: {
              blogID: new ObjectId(blogId),
              draft,
              platform,
            },
          }),
        ),
      );
    });
  }

  @OnQueueCompleted()
  protected async onComplete(job: Job<BlogQueuePayload>) {
    const { identifier, blogId, email, bid } = job.data;
    const { data } = job;

    const generationStatus = `Scheduled for publishing`;
    const status = 'publisher_dispatched';

    const blog = await this.blog.findByIdAndUpdate(blogId, { status, generationStatus });
    if (!blog) {
      return this.logger.error(`Blog with id ${blogId} not found`);
    }

    this.gatewayService.sendBlogStatusUpdate(data.uid, { _id: blogId, identifier, status });
    this.logger.log(`${generationStatus} for blog ${blogId} email ${data.email}`);

    // Update Existing Slack Message
    await this.slackService.updateBlogCreateAlert(blog, blog.slackAlertTs, { bid, email });

    // Push New Message to Same Thread
    const blocks = [
      { type: 'section', text: { type: 'mrkdwn', text: 'Scheduled for *publishing*.' } },
      { type: 'section', fields: [] },
    ];

    if (data.shouldGenerateAffiliateLinks) {
      blocks[1].fields.push({
        type: 'mrkdwn',
        text: `*Affiliate Links Generation Status:* ${data.affiliateLinksGeneration ? 'Success' : 'Failed'}`,
      });
    }

    if (data.platforms?.length) {
      blocks[1].fields.push({
        type: 'mrkdwn',
        text: `*Blog Platforms:* ${data.platforms.map((p) => p.platform).join(', ')}`,
      });
    }

    if (data.socials?.length) {
      blocks[1].fields.push({
        type: 'mrkdwn',
        text: `*Social Platforms:* ${data.socials.map((s) => s.platform).join(', ')}`,
      });
    }

    await this.slackService.sendMessage({
      channel: 'monitor-blog-create',
      message: 'Scheduled for publishing.',
      thread_ts: blog.slackAlertTs,
      blocks,
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    const { blogId, bid, uid, identifier } = job.data;

    if (job.attemptsMade >= job.opts.attempts - 1) {
      await this.blogService.update(bid, blogId, {
        status: 'publisher_dispatching_failed',
        generationStatus: `Failed to dispatch to publisher`,
        failReason: USER_FRIENDLY_ERROR_MESSAGE.PUBLISHER_DISPATCH_FAILED + ': ' + error?.message,
        failedQueue: JOB_QUEUES.DISPATCH_TO_PUBLISHERS,
      } as Blog);

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'publisher_dispatching_failed',
      });
    }

    await super.handleFailedJob(job, error);
  }
}
