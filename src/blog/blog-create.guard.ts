import type { Request } from '@auth/interfaces/authenticated-request.interface';

import { ExecutionContext, CanActivate, Injectable } from '@nestjs/common';

import { CreateBlogDto } from './dto/create-blog.dto';
import { UrlService } from '../common/services/url.service';
import { PACKAGE_LIMITS } from '@common/constants';

@Injectable()
export class BlogCreateGuard implements CanActivate {
  constructor(private readonly urlService: UrlService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest() as Request<CreateBlogDto>;
    request.body.prompt = request.body.prompt?.trim();
    request.body.url = request.body.url?.trim();
    const { sourceType, sourceName, url } = request.body;

    if (url) {
      const maxDuration =
        PACKAGE_LIMITS[request.user.subscriptionPlan]?.MAX_DURATION_IN_SECONDS ?? 10 * 60 ** 2;

      await this.urlService.validateUrl(url, { sourceType, sourceName, maxDuration });
    }

    return true;
  }
}
