import type { BlogQueuePayload } from '@blog/blog.model';
import type { Queue, Job } from 'bull';
import type { Model } from 'mongoose';

import { InjectQueue, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { InjectModel } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';

import { USER_FRIENDLY_ERROR_MESSAGE, JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { BlogGenerationService } from '@/blog-generation/blog-generation.service';
import { FileConversionService } from '@/common/services/file-conversion.service';
import { getPlanModel, TASK } from '@/llm/llm.models';
import { BusinessService } from '@/business/business.service';
import { Blog, SeoOption } from '@/blog/blog.model';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { ImageService } from '@/resources/image/image.service';
import { countTokens } from '@/common/helpers';

import { PromptTooLargeException } from './blog.errors';
import { BlogSourceType } from './blog.enums';
import { ScraperService } from './scraper.service';
import { BlogMLService } from './blog-ml/blog-ml.service';
import { BlogService } from './blog.service';

@Processor(JOB_QUEUES.BLOG_REQUEST)
export class BlogRequestProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.BLOG_REQUEST;
  protected readonly logger = new Logger(BlogRequestProcessor.name);

  constructor(
    // @InjectQueue(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT)
    // private seoSearchRelevantContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_OUTLINE) private generateBlogOutlineQueue: Queue,
    @InjectQueue(JOB_QUEUES.TRANSCRIBE_MEDIA) private transcribeMediaQueue: Queue,
    @InjectQueue(JOB_QUEUES.BLOG_REQUEST) blogRequestQueue: Queue,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly fileConversionService: FileConversionService,
    private readonly blogGenerationService: BlogGenerationService,
    private readonly businessService: BusinessService,
    private readonly gatewayService: GatewayService,
    private readonly scraperService: ScraperService,
    private readonly blogMLService: BlogMLService,
    private readonly imageService: ImageService,
    private readonly slackService: SlackService,
    private readonly blogService: BlogService,
  ) {
    super(blogRequestQueue);
  }

  @Process({ concurrency: 5 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const { data } = job;
      const { bid, uid, email, blogId, sourceType } = data;

      this.sendAlert(job);
      job.progress(20);

      await this.saveThumbnailForCoverImage(data);
      job.progress(40);

      try {
        if (['audio', 'video'].includes(sourceType)) {
          this.transcribeMediaQueue.add(data, { ...JOB_OPTIONS, jobId: blogId });
          this.logger.debug({ email, bid, uid, blogId }, 'Added to transcribe queue');
        } else {
          data.prompt = await this.generatePromptForNonMedia(data);
          job.progress(60);

          data.prompt = await this.shortenPrompt(data);
          job.progress(80);

          /**
           * SEO Option
           * ACTIVE: Run seo analysis and use the seo keywords to improve score during the generation
           * PASSIVE: Passively run the seo analysis and populate results to get score
           */
          const { seoOption } = data;
          if (!seoOption || seoOption === SeoOption.PASSIVE) {
            this.generateBlogOutlineQueue.add(data, { ...JOB_OPTIONS, jobId: blogId });
          } else {
            // TODO: implement the ACTIVE seo analysis and use the results in generation
            // this.seoSearchRelevantContentQueue.add(data, { ...JOB_OPTIONS, jobId: blogId });
            this.generateBlogOutlineQueue.add(data, { ...JOB_OPTIONS, jobId: blogId });
          }
          this.logger.debug({ email, bid, uid, blogId }, 'Added to generate blog outline queue');
          job.progress(90);
        }

        await this.blogService.update(bid, blogId, { status: 'request_dispatched' });
        this.gatewayService.sendBlogStatusUpdate(uid, {
          _id: blogId,
          identifier: data.identifier,
          status: 'request_dispatched',
        });
        job.progress(100);
      } catch (error) {
        if (error['code']) job.discard();
        throw error;
      }
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    if (job.attemptsMade >= job.opts.attempts) {
      const errorCode = error['code'] || null;
      const { uid, bid, blogId, identifier, creditTransactionId } = job.data;
      const failedQueue = JOB_QUEUES.BLOG_REQUEST;
      const failReason = USER_FRIENDLY_ERROR_MESSAGE.BLOG_REQUEST_FAILED + ': ' + error?.message;
      const status = 'failed';

      const promises = [];
      promises.push(
        this.blogModel.updateOne(
          { _id: blogId, bid },
          { failedQueue, failReason, errorCode, status },
        ),
      );

      if (creditTransactionId) {
        promises.push(this.creditTransactionService.refund(bid, creditTransactionId));
      }

      await Promise.allSettled(promises);

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        failReason,
        status,
      });
    }
  }

  private async sendAlert({
    data: { bid, uid, email, blogId, ...data },
    attemptsMade,
  }: Job<BlogQueuePayload>) {
    try {
      // Slack
      const { ts } = await this.slackService.sendBlogCreateAlert(
        { _id: blogId, ...data, status: 'request_dispatching' } as unknown as Blog,
        { bid, email, attemptsMade },
      );

      // Database
      await this.blogModel.updateOne({ _id: blogId, bid }, {
        status: 'request_dispatching',
        slackAlertTs: ts,
      } as Blog);

      // WebSocket
      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier: data.identifier,
        status: 'request_dispatching',
      });
    } catch (e) {
      this.logger.warn('Request Dispatching Alert Error:', e);
    }
  }

  private async saveThumbnailForCoverImage({ bid, image, blogId }: BlogQueuePayload) {
    if (image) {
      try {
        const thumbnail: string | undefined = await this.imageService
          .convertToThumbnail(new URL(image))
          .catch(() => undefined);

        if (thumbnail) {
          await this.blogService.update(bid, blogId, { thumbnail });
        }
      } catch (error) {
        this.logger.error(`Failed to convert image to thumbnail for blog ${blogId}`, error.message);
      }
    }
  }

  private async generatePromptForNonMedia(data: BlogQueuePayload): Promise<string> {
    if ([BlogSourceType.webLink, BlogSourceType.ECommerce].includes(data.sourceType)) {
      return await this.scraperService.scrape(new URL(data.url), data.sourceName);
    } else if (data.sourceType === 'document') {
      const isGoogleDocs = data.sourceName === 'Google Docs';
      const downloadUrl = isGoogleDocs
        ? `https://docs.google.com/document/d/${data.url.split('/')[5]}/export?format=txt`
        : data.url;
      return await this.fileConversionService.downloadAndConvert(
        downloadUrl,
        isGoogleDocs ? 'txt' : undefined,
      );
    }

    return data.prompt;
  }

  private async shortenPrompt({ prompt, ...data }: BlogQueuePayload): Promise<string> {
    try {
      if (countTokens(prompt) > 6000) {
        const { subscriptionPlan } = await this.businessService.findOne(data.bid);

        const options = {
          model: getPlanModel(subscriptionPlan, TASK.SUMMARY),
          context: prompt,
          transcription: prompt,
          language: data.blogLanguage,
          tone: data.blogTone,
          ...data,
        };

        return await this.generateSummaryFromSource(options);
      }
    } catch (e) {
      this.logger.error('Error with shortening prompt', e.message);
      throw new PromptTooLargeException(e.message);
    }

    return prompt;
  }

  private async generateSummaryFromSource(options) {
    try {
      const response = await this.blogGenerationService.generateSummary(options);

      // Track cost if available
      if (response.metadata?.estimatedCost > 0) {
        try {
          const { blogId, bid, model } = options;
          // Get current cost breakdown if exists
          const blog = await this.blogService.findById(bid, blogId);
          const costBreakdown = blog.generationCostBreakdown || [];

          // Add this step's cost
          costBreakdown.push({
            step: 'summary_generation',
            cost: response.metadata.estimatedCost,
            model,
            timestamp: new Date(),
          });

          // Calculate total cost
          const totalCost = costBreakdown.reduce((sum, item) => sum + item.cost, 0);

          // Update blog with new costs
          await this.blogService.update(bid, blogId, {
            generationCost: totalCost,
            generationCostBreakdown: costBreakdown,
          });

          this.logger.debug(
            `Cost for summary generation: $${response.metadata.estimatedCost.toFixed(6)}, total: $${totalCost.toFixed(6)}`,
          );
        } catch (error) {
          this.logger.error('Error tracking summary generation cost', error);
        }
      }

      return response.content;
    } catch (e) {
      this.logger.error(`Primary summary generation failed for the Blog ID: ${options.blogId}.`, e);
      this.logger.log({ blogId: options.blogId }, 'Trying fallback method...');
      return await this.generateSummaryUsingFallbackMethod(options);
    }
  }

  private async generateSummaryUsingFallbackMethod(options) {
    try {
      const body = {
        transcript: options.transcription,
        input_language: options.inputLanguage,
        output_language: options.blogLanguage,
        pov: options.pov,
        tone: options.blogTone,
        size: options.blogSize,
      };

      const blogifySummaries = await this.blogMLService.generateTranscriptSummary(body);

      if (typeof blogifySummaries !== 'string' || !blogifySummaries?.length) {
        throw new Error('Error generating transcription summary using Blogify ML, invalid format.');
      }

      return blogifySummaries;
    } catch (e) {
      this.logger.error(
        `BlogifyML failed to generate transcript summary for the Blog ID: ${options.blogId}.`,
        e,
      );
      return options.transcription;
    }
  }
}
