import type { Response } from 'express';

import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Header,
  HttpCode,
  InternalServerErrorException,
  Ip,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
// import { Cron, CronExpression } from '@nestjs/schedule';
import { getURLVideoID } from '@distube/ytdl-core';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import HTMLtoDOCX from 'html-to-docx';
import { Model } from 'mongoose';

import { Auth } from '@auth/guards/auth.guard';
import { BusinessGuard } from '@auth/guards/business.guard';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { BusinessService } from '@business/business.service';
import { getImageFromYouTubeUrl } from '@common/helpers';
import { CONTENT_COST } from '@resources/credit-transaction/credit-transaction.constant';
import { User } from '@user/user.model';

import { BlogReviewService } from '../blog-generation/blog-review.service';
import { BlogCreateGuard } from './blog-create.guard';
import { Blog } from './blog.model';
import { BlogService } from './blog.service';
import { BlogFilterDto } from './dto/blog-filter.dto';
import { BlogReviewOptionsDto } from './dto/blog-review.dto';
import { BlogDto } from './dto/blog.dto';
import { CreateBlogDto } from './dto/create-blog.dto';
import { CreateBulkBlogDto } from './dto/create-bulk-blog.dto';
import { CreateManualBlogDto } from './dto/create-manual-blog.dto';
import { PublishBlogDto } from './dto/publish-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';

@ApiTags('blogs')
@Controller('blogs')
export class BlogController {
  private readonly logger = new Logger(this.constructor.name);

  constructor(
    @InjectModel(Blog.name) private readonly blog: Model<Blog>,
    @InjectModel(User.name) private readonly user: Model<User>,
    private readonly businessService: BusinessService,
    private readonly configService: ConfigService,
    private readonly blogService: BlogService,
    private readonly blogReviewService: BlogReviewService,
  ) {}

  @Post('validate-url')
  @UseGuards(Auth, BusinessGuard, BlogCreateGuard)
  async validateUrl() {
    return { isValid: true };
  }

  @Post('/')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, BlogCreateGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createBlog(
    @Req() { uid, bid, user }: AuthenticatedRequest,
    @Body() createBlogDto: CreateBlogDto,
    @Ip() ip: string,
  ) {
    try {
      const { shopifyData, sourceType, url } = createBlogDto;
      const cost =
        CONTENT_COST[
          ['audio', 'video'].includes(sourceType) ? 'blog-from-media' : 'blog-from-text'
        ];
      const hasCredit = await this.businessService.hasBlogCredit(bid, cost);

      if (!hasCredit) {
        throw new BadRequestException('No blog credit available');
      }

      if (createBlogDto.youtubeVideoId) {
        createBlogDto.identifier = createBlogDto.youtubeVideoId;
      }
      if (sourceType === 'webLink') {
        createBlogDto.identifier = url;
      } else if (sourceType === 'image') {
        createBlogDto.identifier = url;
        createBlogDto.prompt = await this.blogService.generatePromptFromImageLink(url);
        if (createBlogDto.prompt === 'Error in generating image') {
          throw new BadRequestException(`Couldn't generate prompt from the image.`);
        }
        if (!createBlogDto.image) createBlogDto.image = createBlogDto.url;
      }

      // ! Only for Shopify; Currently Unused.
      if (shopifyData) {
        if (!shopifyData?.id) {
          throw new BadRequestException('Shopify product id is required');
        }
        const existingBlog = await this.blogService.findByShopifyProductId(
          bid,
          shopifyData?.id?.toString(),
        );
        if (existingBlog) {
          throw new BadRequestException(
            'Blog already exists for this shopify product. Try to regenerate',
          );
        }

        const generatedShopifyPrompt = await this.blogService.generatePromptFromShopifyData(
          createBlogDto.shopifyData,
        );
        createBlogDto.shopifyProductId = createBlogDto.shopifyData?.id?.toString();
        createBlogDto.prompt = generatedShopifyPrompt.prompt;
        createBlogDto.image = generatedShopifyPrompt.image;
        createBlogDto.contentImages = generatedShopifyPrompt.contentImages;
        createBlogDto.identifier = createBlogDto.shopifyProductId;
      }

      const result = await this.blogService.createBlog({ bid, uid }, createBlogDto, user.email, ip);

      if (result.success) {
        return {
          generationMode: result.generationMode,
          message: result.success,
          status: 'queued',
          identifier: result.identifier,
          _id: result._id,
        };
      } else {
        throw new BadRequestException(result);
      }
    } catch (error) {
      this.logger.error(`Create blog failed for ${user.email}`, error?.message);
      throw new BadRequestException('Failed to create blog: ' + error?.message);
    }
  }

  @Post('/bulk')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async createBulkBlog(
    @Req() req: AuthenticatedRequest,
    @Body() createBulkBlogDto: CreateBulkBlogDto,
    @Ip() ip: string,
  ) {
    try {
      const { uid, bid, user } = req;
      const { urls, shopifyData, isYoutubeBulk, ...bulkData } = createBulkBlogDto;
      let createBlogsPromise = [];

      if (!uid || !bid) {
        throw new UnauthorizedException('Business not found for the user');
      }

      if (urls?.length) {
        const createBlogDto = bulkData as CreateBlogDto;
        createBlogsPromise = urls.map(async (url) => {
          return this.blogService.createBlog(
            { bid, uid },
            {
              ...createBlogDto,
              url,
              identifier: isYoutubeBulk ? getURLVideoID(url) : url,
              image: getImageFromYouTubeUrl(url),
            },
            user.email,
            ip,
          );
        });
      } else if (shopifyData?.length) {
        const blogSettings = bulkData as CreateBlogDto;
        createBlogsPromise = shopifyData.map(async (data) => {
          const createBlogDto = {} as CreateBlogDto;
          if (!data?.id) {
            throw new BadRequestException('Shopify product id is required');
          }
          const existingBlog = await this.blogService.findByShopifyProductId(
            bid,
            data?.id?.toString(),
          );
          if (existingBlog) {
            throw new BadRequestException(
              'Blog already exists for this shopify product. Try to regenerate',
            );
          }
          const generatedShopifyPrompt = await this.blogService.generatePromptFromShopifyData(data);
          createBlogDto.shopifyProductId = data?.id?.toString();
          createBlogDto.identifier = data?.id?.toString();
          createBlogDto.prompt = generatedShopifyPrompt.prompt;
          createBlogDto.image = generatedShopifyPrompt.image;
          createBlogDto.contentImages = generatedShopifyPrompt.contentImages;
          return this.blogService.createBlog(
            { bid, uid },
            { ...blogSettings, ...createBlogDto },
            user.email,
            ip,
          );
        });
      } else {
        throw new BadRequestException('URLs or Shopify data is required');
      }

      const hasCredit = urls?.length
        ? await this.businessService.hasBlogCredit(
            bid,
            createBlogsPromise.length * CONTENT_COST['blog-from-media'],
          )
        : await this.businessService.hasBlogCredit(
            bid,
            createBlogsPromise.length * CONTENT_COST['blog-from-text'],
          );

      if (!hasCredit) {
        throw new BadRequestException('No blog credit available');
      }

      Promise.all(createBlogsPromise)
        .then((results) => {
          const successResults = results.map((result) => ({
            generationMode: result.generationMode,
            message: result.success,
            identifier: result.identifier,
            status: 'queued',
            _id: result._id,
          }));
          this.logger.log(`Create bulk blog generation success for ${req.user?.email} bid ${bid}`);
          return successResults;
        })
        .catch((error) => {
          throw new BadRequestException(error?.message);
        });
    } catch (error) {
      this.logger.error(
        `Create bulk blog generation failed for ${req.user.email} bid ${req.bid}`,
        error?.message,
      );
      throw new BadRequestException(error?.message);
    }
  }

  @Post('/create-from-scratch')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, BlogCreateGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createBlogFromScratch(@Req() { bid, uid, user }: AuthenticatedRequest) {
    return this.blogService.createBlogManual<undefined>({ bid, uid, user }, undefined);
  }

  @Post('/create')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard, BusinessGuard, BlogCreateGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createBlogManual(
    @Req() { bid, uid, user }: AuthenticatedRequest,
    @Body() body: CreateManualBlogDto,
  ) {
    return this.blogService.createBlogManual<CreateManualBlogDto>({ bid, uid, user }, body);
  }

  @Get(':id/regenerate')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async regenerateBlog(@Req() req: AuthenticatedRequest, @Param('id') id: string) {
    try {
      const { uid, bid, user } = req;

      if (!id) {
        throw new UnauthorizedException('Blog id is required');
      }
      if (!uid || !bid) {
        throw new UnauthorizedException('Business not found for the user');
      }

      const result = await this.blogService.regenerateBlog(id, bid, uid, user.email);
      if (result.message) {
        return {
          generationMode: result.generationMode,
          message: result.message,
          status: result.status,
          identifier: result.identifier,
          _id: result._id,
        };
      } else {
        throw new BadRequestException('Regenerate blog request failed: ' + result);
      }
    } catch (error) {
      this.logger.error({ err: error, blogId: id }, 'Regenerate blog failed');
      throw new BadRequestException('Regenerate blog request failed: ' + error?.message);
    }
  }

  @Get('counts')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async counts(@Req() { bid }: AuthenticatedRequest, @Query('uid') uid?: string) {
    return this.blogService.counts(bid, uid);
  }

  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @ApiQuery({ name: 'page', required: false, schema: { default: 1 } })
  @ApiQuery({ name: 'limit', required: false, schema: { default: 10 } })
  @ApiResponse({ status: 200, description: 'Return blogs for current user' })
  async findAll(@Req() req: AuthenticatedRequest, @Query() filter: BlogFilterDto) {
    try {
      const { bid } = req;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      const result = await this.blogService.findAll(bid, filter);
      return result;
    } catch (error) {
      this.logger.error(`Find all blogs failed`, error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Post('shopify-product-blogs')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @ApiQuery({ name: 'page', required: false, schema: { default: 1 } })
  @ApiQuery({ name: 'limit', required: false, schema: { default: 10 } })
  @ApiResponse({ status: 200, description: 'Return blogs for current user' })
  async findAllShopifyProductBlogs(
    @Req() req: AuthenticatedRequest,
    @Query('limit') limit: number,
    @Query('page') page: number,
    @Body('ids') ids: string[],
  ) {
    try {
      const { bid } = req;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      const result = await this.blogService.shopifyProductsBlogStatus(bid, ids, page, limit);
      return result;
    } catch (error) {
      this.logger.error(`Find all shopify product blogs failed`, error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Post('find/youtube')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @ApiResponse({ status: 200, description: 'Return blogs for youtube ids' })
  async findByUrl(@Req() req: AuthenticatedRequest, @Body('ids') ids: string[]) {
    try {
      const { bid } = req;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      const urls = ids.map((id) => `https://www.youtube.com/watch?v=${id}`);
      const result = await this.blogService.findBlogsByUrls(bid, urls);
      return result;
    } catch (error) {
      this.logger.error(`Find blogs by urls failed`, error?.message);
      throw new BadRequestException(error?.message);
    }
  }

  @Get(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async findById(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<Partial<Blog>> {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    const blog = await this.blogService.findById(bid, id);
    const { _seoResults, _seoKeywords, _affiliateKeywords, ...rest } = blog.toObject();
    if (!blog) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }
    return rest;
  }

  @Get(':id/download-docx')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
  async downloadDocx(
    @Req() req: AuthenticatedRequest,
    @Res() res: Response,
    @Param('id') id: string,
  ) {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    const blog = await this.blogService.findById(bid, id);
    if (!blog) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }
    if (!blog.content) {
      throw new Error(`Blog content not generated yet`);
    }

    const fileName = `${blog.title.replace(/[^a-zA-Z0-9\s-_.]/g, '')}.docx`;

    const blogHtmlContent = `
      <h1>${blog.title}</h1>
      <img src="${blog.image}" />
      ${blog.content}
    `;
    const docxBuffer = await HTMLtoDOCX(blogHtmlContent);

    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.send(docxBuffer);
  }

  @Post(':id/share')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async generateSocialMediaPost(
    @Param('id') blogID: string,
    @Body('platform') platform: string,
    @Body('link') publishLink: string,
    @Body('hashtag') hashtag: boolean,
    @Body('emoji') emoji: boolean,
    @Req() { bid }: AuthenticatedRequest,
  ) {
    const { content } = await this.blogService.findById(bid, blogID);
    return {
      socialMediaPost: await this.blogService.generateSocialMediaContent(
        blogID,
        content,
        platform,
        publishLink,
        emoji,
        hashtag,
      ),
    };
  }

  @Post(':id/publish-social')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @HttpCode(205)
  async publishSocial(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() publishBlogDto: PublishBlogDto,
  ) {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    return await this.blogService.publishSocial(bid, id, publishBlogDto);
  }

  @Post(':id/publish')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @HttpCode(205)
  async publish(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() publishBlogDto: PublishBlogDto,
  ) {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    await this.blogService.publish(bid, id, publishBlogDto);
  }

  @Patch(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async update(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateBlogDto: UpdateBlogDto,
  ): Promise<BlogDto> {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    const blog = await this.blogService.update(bid, id, updateBlogDto);
    return this.mapToDto(blog);
  }

  @Patch(':id/assisted')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async updateAssisted(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() body: any,
  ): Promise<BlogDto> {
    const { uid, bid, user } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }

    const blog = await this.blogService.updateAssisted(id, body, { ...user, uid, bid });
    return this.mapToDto(blog);
  }

  @Patch(':id/assisted/publish')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async publishAssisted(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<BlogDto> {
    const { uid, bid, user } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }

    const blog = await this.blogService.publishAssisted(id, { ...user, uid, bid });
    return this.mapToDto(blog);
  }

  @Delete(':id')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  async delete(@Req() req: AuthenticatedRequest, @Param('id') id: string): Promise<void> {
    const { bid } = req;
    if (!bid) {
      throw new UnauthorizedException('Business not found for the user');
    }
    await this.blogService.delete(bid, id);
  }

  @Post(':id/review')
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async reviewBlog(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() reviewOptions: BlogReviewOptionsDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const { bid } = req;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }

      // Find the blog
      const blog = await this.blogService.findById(bid, id);

      if (!blog) {
        throw new NotFoundException(`Blog with id ${id} not found`);
      }

      if (!blog.content) {
        throw new BadRequestException(
          'Blog content is not yet generated. Cannot review empty content.',
        );
      }

      // Queue the blog for review
      const queued = await this.blogReviewService.reviewBlogContent({
        blogId: id,
        content: blog.content,
        title: blog.title,
        blogTone: reviewOptions.tone || blog.blogTone,
        language: reviewOptions.language || blog.blogLanguage,
        perspective: reviewOptions.perspective || blog.pov,
        seoKeywords: blog.seoKeywords?.flatMap((k) => Object.values(k))?.flat() || [],
        blogSize: reviewOptions.blogSize || blog.blogSize,
        wordCountApprox: blog.wordCount,
        targetAudience: reviewOptions.targetAudience || 'general',
      });

      if (queued) {
        return {
          success: true,
          message: 'Blog queued for review. The content will be optimized for human-like quality.',
        };
      } else {
        throw new InternalServerErrorException('Failed to queue blog for review');
      }
    } catch (error) {
      this.logger.error(`Blog review failed for ${id}`, error?.message);
      throw new BadRequestException('Failed to queue blog for review: ' + error?.message);
    }
  }

  // TODO: Remove Later if Not Needed Anymore
  // @Cron(CronExpression.EVERY_10_MINUTES)
  // async autoRetryBlogs() {
  //   const failedBlogs = await this.blog
  //     .find({
  //       createdAt: {
  //         $lte: new Date(Date.now() - 10 * 60 * 1000) /* 10 Minutes */,
  //       },
  //       status: { $regex: /.*failed$/i },
  //     })
  //     .select({ _id: 1, bid: 1, uid: 1, status: 1, createdAt: 1, failReason: 1 });

  //   for (const failedBlog of failedBlogs) {
  //     const user = await this.user.findById(failedBlog.uid).select({ email: 1 });
  //     await this.blogService.regenerateBlog(
  //       failedBlog._id,
  //       failedBlog.bid,
  //       failedBlog.uid,
  //       user?.email ?? 'unknown',
  //     );
  //     await this.slack.send({
  //       text: `Blog ${failedBlog._id} has been sent for retry, it failed previously with error: ${failedBlog.failReason}`,
  //     });
  //   }
  // }

  private mapToDto(blog: Blog): BlogDto {
    const { title, content, publishAt, platforms, socials } = blog;
    return {
      title,
      content,
      publishAt,
      platforms,
      socials,
    };
  }
}
