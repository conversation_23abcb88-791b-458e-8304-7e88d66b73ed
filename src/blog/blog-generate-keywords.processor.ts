import { Processor, Process, InjectQueue, OnQueueFailed } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Queue, Job } from 'bull';
import { BaseProcessor } from '@common/queue/base.processor';

import { JOB_QUEUES } from '@common/constants'; // USER_FRIENDLY_ERROR_MESSAGE
import { GatewayService } from '@modules/gateway/gateway.service';
import { OpenaiService } from '@openai/openai.service';

import { BlogService } from './blog.service';
import { Blog, BlogQueuePayload } from './blog.model';

@Processor(JOB_QUEUES.GENERATE_BLOG_KEYWORDS)
export class BlogGenerateKeywordsProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_BLOG_KEYWORDS;
  protected readonly logger = new Logger(BlogGenerateKeywordsProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.SEO_SCORING) private seoScoringQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_KEYWORDS) generateBlogKeywordsQueue: Queue,
    private readonly gatewayService: GatewayService,
    private readonly openaiService: OpenaiService,
    private readonly blogService: BlogService,
  ) {
    super(generateBlogKeywordsQueue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const {
        blogId,
        uid,
        bid,
        email,
        blogContent,
        blogOutline,
        blogLanguage,
        affiliateCommissionOptIn,
        identifier,
      } = job.data;
      let { keywords } = job.data;

      const totalTokens = this.openaiService.countTokens(blogContent);

      this.logger.log(
        `Generating blog keywords for blog ${blogId} for ${email} total tokens in input: ${totalTokens}`,
      );
      await this.blogService.update(bid, blogId, { status: 'keywords_generating' });
      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'keywords_generating',
      });

      if (typeof keywords === 'string' && (keywords as string).includes(',')) {
        keywords = (keywords as string).split(',');
      }

      let blogKeywords = keywords || blogOutline.keywords || [];
      let blogMetaDescription =
        blogOutline.metaDescription || blogOutline['meta_description'] || '';

      if (!blogKeywords.length) {
        // Generate blog keywords
        blogKeywords =
          totalTokens < 12000
            ? await this.openaiService.generateKeywordsForContent(blogId, blogContent)
            : await this.openaiService.generateKeywords(blogId, blogContent);
      }

      // generate blog meta description
      if (!blogMetaDescription) {
        blogMetaDescription = await this.openaiService.generateMetaDescription(
          blogId,
          blogOutline && Object.keys(blogOutline).length ? blogOutline : blogContent,
          blogLanguage,
        );
      }

      if (
        !blogKeywords ||
        !Array.isArray(blogKeywords) ||
        (totalTokens < 12000 && !blogKeywords.length)
      ) {
        throw new Error('Failed to generate blog keywords');
      }

      const shouldGenerateAffiliateLinks = !!(blogKeywords.length && affiliateCommissionOptIn);

      await this.seoScoringQueue.add({ ...job.data, blogKeywords, shouldGenerateAffiliateLinks });

      const generationStatus = `Generated blog keywords next step is to SEO Score`;
      const status = 'keywords_generated';
      await this.blogService.update(bid, blogId, {
        keywords: blogKeywords,
        metaDescription: blogMetaDescription,
        generationStatus,
        status,
      } as Blog);
      this.gatewayService.sendBlogStatusUpdate(uid, { _id: blogId, identifier, status });
      this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
    });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    const { blogId, bid, uid, identifier } = job.data;

    if (job.attemptsMade >= job.opts.attempts - 1) {
      await this.blogService.update(bid, blogId, {
        status: 'keywords_generation_failed',
        generationStatus: 'Failed to generate blog keywords retrying',
        failedQueue: JOB_QUEUES.GENERATE_BLOG_KEYWORDS,
      } as Blog);

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'keywords_generation_failed',
      });
    }

    await super.handleFailedJob(job, error);
  }
}
