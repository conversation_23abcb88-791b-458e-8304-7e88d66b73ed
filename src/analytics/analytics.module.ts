import { MongooseModule } from '@nestjs/mongoose';
import { Mo<PERSON><PERSON> } from '@nestjs/common';

import { AffiliateLinkTrackingModule } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.module';

import { AnalyticsSchema, Analytics } from './analytics.model';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Analytics.name, schema: AnalyticsSchema }]),
    AffiliateLinkTrackingModule,
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
  exports: [AnalyticsService, MongooseModule],
})
export class AnalyticsModule {}
