import { Injectable, Logger } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { AffiliateLinkTrackingService } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.service';

import { Analytics, AnalyticsDocument } from './analytics.model';
import { GeoInfo, UaInfo } from './analytics.interface';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);
  constructor(
    @InjectModel(Analytics.name)
    private readonly analyticsModel: Model<AnalyticsDocument>,
    private readonly affiliateLinkTrackingService: AffiliateLinkTrackingService,
  ) {}

  /**
   * Fetch the events for all blogs of a given business ID (bid).
   *
   * @param  bid - The business ID for which to fetch events.
   * @returns  A promise that resolves to a Map where the key is the blog ID as a mongoose ObjectId,
   * and the value is an array of objects containing event details such as event name, date, and event frequency on that date.
   */
  async fetchEvents(bid: string) {
    const analytics = await this.analyticsModel.find({ bid });
    return new Map(
      analytics.map(
        ({ blogId, eventsByDate }) =>
          <const>[
            new Types.ObjectId(blogId),
            Object.entries(eventsByDate).map(
              ([date, counts]) =>
                <const>{
                  name: date.split('_')[1] as 'view' | 'click',
                  date: new Date(date.split('_')[0]),
                  frequency: Object.values(counts.platform).reduce((a, b) => a + b, 0),
                },
            ),
          ],
      ),
    );
  }
  async calculateAnalytics(bid: string, startDateTime: Date, endDateTime: Date, blogId?: string) {
    // period = period ?? 'overall';
    const query = blogId ? { bid, blogId } : { bid };
    const analytics = await this.analyticsModel.find(query);

    // Calculate aggregated analytics for a specific period if provided
    // const startDateTime = this.calculateStartDate(period);
    // const endDateTime = new Date(); // Use the current date astartDateTimes the end date

    const startDate = new Date(startDateTime).toISOString().substring(0, 10);
    const endDate = new Date(endDateTime).toISOString().substring(0, 10);

    const filteredAnalytics = analytics.map((entry) => {
      const eventKeys = Object.keys(entry.eventsByDate);
      const dateStrings = eventKeys.map((eventKey) => eventKey.split('_')[0]);
      const dates = dateStrings.map((dateString) => new Date(dateString));
      const filteredEventsByDate = {};
      dates.forEach((date) => {
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);

        if (date >= startDateObj && date <= endDateObj) {
          const dateString = date.toISOString().substring(0, 10);
          filteredEventsByDate[`${dateString}_view`] = entry.eventsByDate[`${dateString}_view`];
          filteredEventsByDate[`${dateString}_click`] = entry.eventsByDate[`${dateString}_click`];
        }
      });

      entry.eventsByDate = filteredEventsByDate;

      return entry;
    });

    // Aggregate analytics data for each date in the period
    const aggregatedAnalytics = filteredAnalytics.reduce((result: any, entry: any) => {
      const eventKeys = Object.keys(entry.eventsByDate);

      eventKeys.forEach((eventKey) => {
        const [_, eventType] = eventKey.split('_');

        result[eventType] = result[eventType] || {
          platform: {},
          vendor: {},
          browser: {},
          os: {},
          country: {},
          city: {},
        };

        const { platform, vendor, browser, os, country, city } = entry.eventsByDate[eventKey] ?? {};

        result[eventType].platform = this.incrementKeyCount(result[eventType].platform, platform);
        result[eventType].vendor = this.incrementKeyCount(result[eventType].vendor, vendor);
        result[eventType].browser = this.incrementKeyCount(result[eventType].browser, browser);
        result[eventType].os = this.incrementKeyCount(result[eventType].os, os);
        result[eventType].country = this.incrementKeyCount(result[eventType].country, country);
        result[eventType].city = this.incrementKeyCount(result[eventType].city, city);
      });

      return result;
    }, {});

    const businessId = new Types.ObjectId(bid);

    const actions = await this.affiliateLinkTrackingService.countActions(
      businessId,
      new Date(startDateTime),
      new Date(endDateTime),
    );

    return {
      ...aggregatedAnalytics,
      actions: Array.from(actions),
    };
  }

  async fetchAnalytics(bid: string, blogIds: string[]) {
    const analytics = await this.analyticsModel.find(
      { bid, blogId: { $in: blogIds } },
      { views: 1, clicks: 1, blogId: 1 },
    );
    return analytics;
  }

  // Utility function to increment the count for a key in an object
  incrementKeyCount(
    obj: Record<string, any>,
    key: string | Record<string, any>,
  ): Record<string, any> {
    obj = obj ?? {};

    if (typeof key === 'string') {
      if (key !== '') {
        if (!Object.prototype.hasOwnProperty.call(obj, key)) {
          obj[key] = 1;
        } else {
          obj[key]++;
        }
      }
    } else if (typeof key === 'object' && key !== null) {
      for (const subKey in key) {
        if (Object.prototype.hasOwnProperty.call(key, subKey)) {
          const count = key[subKey];
          if (subKey !== '') {
            if (!Object.prototype.hasOwnProperty.call(obj, subKey)) {
              obj[subKey] = count;
            } else {
              obj[subKey] += count;
            }
          }
        }
      }
    }

    return obj;
  }

  // Utility function to calculate the start date based on the period
  calculateStartDate(period: string): Date {
    const currentDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'today':
        startDate.setDate(currentDate.getDate());
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(currentDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setDate(currentDate.getDate() - 30);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'overall':
        // Set a start date far in the past to include all entries
        startDate.setFullYear(1900);
        break;
      default:
        startDate.setFullYear(1900);
        break;
    }

    return startDate;
  }

  // eslint-disable-next-line complexity
  async updateAnalytics(
    ref: string,
    bid: string,
    blogId: string,
    uaInfo: UaInfo,
    geoInfo: Omit<GeoInfo, 'latlng' | 'timezone'>,
    eventType?: string,
  ): Promise<void> {
    const currentDate = new Date();
    const today = currentDate.toISOString().substring(0, 10);
    const dateKey = `${today}_${eventType ?? 'click'}`;
    const combinedKey = `${bid}-${blogId}`;
    let analytics = await this.analyticsModel.findOne({ combinedKey });

    if (!analytics) {
      analytics = new this.analyticsModel({
        combinedKey,
        bid,
        blogId,
        uaInfo: {},
        geoInfo: {},
        eventsByDate: { [dateKey]: {} },
        clicks: 0,
        views: 0,
        ref: ref,
      });
    }

    // Increment total clicks or views based on eventType
    const isView = eventType === 'view';
    if (isView) {
      analytics.views = (analytics.views || 0) + 1;
    } else {
      analytics.clicks = (analytics.clicks || 0) + 1;
    }

    // Update distinct properties of uaInfo
    for (const [key, value] of Object.entries(uaInfo)) {
      analytics.uaInfo = analytics.uaInfo ?? {};
      analytics.eventsByDate[dateKey] = analytics.eventsByDate[dateKey] ?? {};
      if (value !== undefined && value !== null) {
        const mapObj = analytics?.uaInfo[key] ?? {};
        const mapObjByDate = analytics?.eventsByDate[dateKey][key] ?? {};
        const count = mapObj[value] ?? 0;
        const countByDate = mapObjByDate[value] ?? 0;
        mapObj[value] = count + 1;
        mapObjByDate[value] = countByDate + 1;
        analytics.uaInfo[key] = mapObj;
        analytics.eventsByDate[dateKey][key] = mapObjByDate;
        analytics.eventsByDate[dateKey]['ref'] = ref;
      }
    }

    // Update distinct properties of geoInfo
    for (const [key, value] of Object.entries(geoInfo)) {
      analytics.geoInfo = analytics.geoInfo ?? {};
      analytics.eventsByDate[dateKey] = analytics.eventsByDate[dateKey] ?? {};
      if (value !== undefined && value !== null && key !== 'latlng' && key !== 'timezone') {
        const mapObj = analytics?.geoInfo[key] ?? {};
        const mapObjByDate = analytics?.eventsByDate[dateKey][key] ?? {};
        const count = mapObj[value] ?? 0;
        const countByDate = mapObjByDate[value] ?? 0;
        mapObj[value] = count + 1;
        mapObjByDate[value] = countByDate + 1;
        analytics.geoInfo[key] = mapObj;
        analytics.eventsByDate[dateKey][key] = mapObjByDate;
        analytics.eventsByDate[dateKey]['ref'] = ref;
      }
    }

    try {
      await this.analyticsModel.findOneAndUpdate(
        { combinedKey },
        {
          $set: {
            bid,
            blogId,
            uaInfo: analytics.uaInfo,
            geoInfo: analytics.geoInfo,
            eventsByDate: analytics.eventsByDate,
            clicks: analytics.clicks,
            views: analytics.views,
            ref: ref,
          },
        },
        { new: true, upsert: true },
      );
    } catch (error) {
      this.logger.error({ err: error, bid, blogId, ref }, 'Failed to update tracking analytics');
    }
  }
}
