import {
  <PERSON>,
  Get,
  Logger,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles, RolesGuard } from '@auth/guards/roles.guard';
import { Auth } from '@auth/guards/auth.guard';
import { AuthenticatedRequest } from '@auth/interfaces/authenticated-request.interface';
import { Response } from 'express';

@ApiTags('analytics')
@Controller('analytics')
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(private readonly analyticsService: AnalyticsService) {}

  // Analytics Overall for BID
  @Get()
  @Roles('admin')
  @UseGuards(Auth, RolesGuard)
  @ApiQuery({
    name: 'period',
    required: false,
    schema: { default: 'overall' },
    enum: ['today', 'week', 'month', 'overall'],
  })
  @ApiQuery({
    name: 'blogId',
    required: false,
    description: `single blogId`,
  })
  @ApiResponse({
    status: 200,
    description: 'Return analytics for current business',
  })
  async analytics(
    @Req() req: AuthenticatedRequest,
    @Query() { startDate, endDate, blogId }: { startDate: Date; endDate: Date; blogId?: string },
    @Res() res: Response,
  ) {
    try {
      const { bid } = req;
      if (!bid) {
        throw new UnauthorizedException('Business not found for the user');
      }
      const result = await this.analyticsService.calculateAnalytics(
        bid,
        startDate,
        endDate,
        blogId,
      );
      res.status(200).send(result);
    } catch (error) {
      this.logger.error({ err: error, bid: req?.bid }, 'Failed to generate analytics');
      res.status(400).send({ message: 'Failed to generate analytics: ' + error?.message });
    }
  }

  // Analytics Overall for BID and BlogID

  // Analytics today for BID

  // Analytics today for BID and BlogID

  // Analytics this week for BID

  // Analytics this week for BID and BlogID

  // Analytics this month for BID

  // Analytics this month for BID and BlogID
}
