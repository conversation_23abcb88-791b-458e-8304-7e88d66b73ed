import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

@Schema()
export class Analytics {
  @Prop({ required: true, unique: true })
  combinedKey: string;

  @Prop({})
  bid: string;

  @Prop({})
  blogId: string;

  @Prop({ type: Object })
  uaInfo: object;

  @Prop({ type: Object })
  geoInfo: object;

  @Prop({ type: Object, default: {} })
  eventsByDate: Record<
    `${string}_${'view' | 'click'}`,
    Record<'platform' | 'vendor' | 'browser' | 'os' | 'country' | 'city', Record<string, number>>
  >;

  @Prop({ type: Number, default: 0 })
  clicks: number;

  @Prop({ type: Number, default: 0 })
  views: number;

  @Prop({ type: String })
  ref: string;
}

export type AnalyticsDocument = HydratedDocument<Analytics>;

export const AnalyticsSchema = SchemaFactory.createForClass(Analytics);
