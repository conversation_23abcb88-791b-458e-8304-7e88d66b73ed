import {
  ParseArrayPipe,
  ValidationPipe,
  Controller,
  UseGuards,
  UsePipes,
  Query,
  Req,
  Get,
} from '@nestjs/common';
import { IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import mongoose from 'mongoose';

import { AffiliateLinkTrackingService } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.service';
import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { RolesGuard } from '@/auth/guards/roles.guard';
import { JWTToken } from '@/publishing/oauth2/oauth2-admin.controller';
import { Auth } from '@/auth/guards/auth.guard';

import { AffiliateService } from './affiliate.service';

class TimeQueryParams {
  @IsDate()
  @Type(() => Date)
  start: Date;

  @IsDate()
  @Type(() => Date)
  end: Date;
}
@Controller('affiliate')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class AffiliateController {
  constructor(
    private readonly affiliateLinkTrackingService: AffiliateLinkTrackingService,
    private readonly affiliateService: AffiliateService,
  ) {}

  @Get('stats')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  getStats(@Req() { bid }: AuthenticatedRequest) {
    return this.affiliateService.getStats(bid);
  }

  @Get('chart')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getChartData(
    @Req() { bid }: AuthenticatedRequest,
    @Query() { start, end }: TimeQueryParams,
  ) {
    const businessId = new mongoose.Types.ObjectId(bid);
    const actions = await this.affiliateLinkTrackingService.countActions(businessId, start, end);
    const beaconCounts = await this.affiliateService.countBeacons(businessId, start, end);
    const earnings = await this.affiliateService.fetchEarnings(businessId, start, end);
    return {
      sales: Array.from(actions),
      clicks: Array.from(beaconCounts.get('click')),
      views: Array.from(beaconCounts.get('view')),
      earnings: earnings.get('USD') || 0,
    };
  }

  @Get('products')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async searchProducts(
    @JWTToken('bid') bid: mongoose.Types.ObjectId,
    @Query('query') query: string,
    @Query('regions', new ParseArrayPipe({ items: String, optional: true })) countries?: string[],
    @Query('source') source?: 'personal' | 'impact',
  ) {
    return await this.affiliateService.searchProducts({ bid, query, source, countries });
  }

  @Get('links')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  fetchLinks(@Query('limit') limit: number, @Query('offset') offset: number) {
    type Link = {
      image: URL;
      link: URL;
      brand: { iconSrc: URL; name: string };
      clicks: number;
      action: number;
      earnings: number;
      epc: number;
      eps: number;
      cr: number;
    };
    const datum: Link = {
      image: new URL('/images/blogify.svg', 'https://blogify.ai'),
      link: new URL('https://www.example.net'),
      brand: { iconSrc: new URL('/images/blogify.svg', 'https://blogify.ai'), name: 'Blogify' },
      clicks: 100,
      action: 50,
      earnings: 10.0,
      epc: 0.1,
      eps: 4.0,
      cr: 0.2,
    };

    return {
      total: 100,
      limit: limit,
      offset: offset,
      data: Array(limit).fill(datum),
    };
  }

  @Get('brands')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  fetchBrands(@Query('limit') limit: number, @Query('offset') offset: number) {
    type Brand = {
      link: number;
      brand: { iconSrc: URL; name: string };
      clicks: number;
      action: number;
      earnings: number;
      epc: number;
      eps: number;
      cr: number;
    };

    const datum: Brand = {
      link: 70,
      brand: { iconSrc: new URL('/images/blogify.svg', 'https://blogify.ai'), name: 'Blogify' },
      clicks: 100,
      action: 50,
      earnings: 10.0,
      epc: 0.1,
      eps: 4.0,
      cr: 0.2,
    };

    return {
      total: 100,
      limit: limit,
      offset: offset,
      data: Array(limit).fill(datum),
    };
  }

  @Get('activity')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async calculateActivity(@Query() { start, end }: TimeQueryParams) {
    type Summary = {
      actions: number;
      clicks: number;
      cr: number;
    };
    type ClicksPerDevice = {
      mobile: number;
      laptop: number;
      desktop: number;
      other: number;
    };

    type ClicksPerCountry = Record<string /* ISO-COUNTRY-CODE */, number>;

    return {
      start,
      end,
      summary: {
        clicks: 100,
        actions: 20,
        cr: 0.2,
      } as Summary,
      device: {
        mobile: 200,
        laptop: 100,
        desktop: 40,
        other: 10,
      } as ClicksPerDevice,
      country: {
        SA: 100,
        US: 300,
        CN: 300,
      } as ClicksPerCountry,
    };
  }
}
