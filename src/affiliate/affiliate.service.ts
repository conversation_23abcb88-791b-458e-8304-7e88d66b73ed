import type { Model } from 'mongoose';

import { getAllInfoByISO } from 'iso-country-currency';
import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';
import mongoose from 'mongoose';

import {
  AffiliateConversionStatus,
  AffiliateLinkTracking,
} from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.model';
import { AnalyticsService } from '@/analytics/analytics.service';
import { ImpactService } from '@/monetization/impact.service';
import { WalletService } from '@/resources/wallet/wallet.service';

import { LinkLibraryService } from './link-library/link-library.service';

@Injectable()
export class AffiliateService {
  constructor(
    @InjectModel(AffiliateLinkTracking.name)
    private readonly affiliateLinkTrackingModel: Model<AffiliateLinkTracking>,
    private readonly linkLibraryService: LinkLibraryService,
    private readonly analyticsService: AnalyticsService,
    private readonly impactService: ImpactService,
    private readonly walletService: WalletService,
  ) {}

  async searchProducts({
    bid,
    query,
    countries,
    source,
  }: {
    bid: mongoose.Types.ObjectId;
    query: string;
    countries?: string[];
    source?: string;
  }): Promise<Array<Record<'name' | 'image' | 'category' | 'link', string>>> {
    if (source === 'personal') {
      const keywords = query ? query.split(' ').filter((k) => k.trim().length > 0) : [];

      return (await this.linkLibraryService.search(bid, keywords)).map(
        ({ name, category, affiliateLink }) => ({
          name,
          image: 'https://blogify.ai/images/blogify.svg',
          category,
          link: affiliateLink,
        }),
      );
    } else {
      const currencies = countries?.map(
        (countryISOCode) => getAllInfoByISO(countryISOCode).currency,
      );
      const products = (await this.impactService.searchProducts(query)).map(
        ({ currency, name, url, imageUrl, category }) => ({
          name,
          link: url,
          category,
          image: imageUrl,
          currency: currency,
        }),
      );
      return currencies
        ? products.filter((product) => currencies.includes(product.currency))
        : products;
    }
  }

  /**
   * Fetches the earnings for a given affiliate within a specified date range.
   *
   * @param bid - The business ObjectId of the affiliate.
   * @param start - The start date of the period to fetch earnings for.
   * @param end - The end date of the period to fetch earnings for.
   * @returns A promise that resolves to a Map where the keys are currency codes and the values are the total earnings in that currency.
   */
  async fetchEarnings(bid: mongoose.Types.ObjectId, start: Date, end: Date) {
    return this.affiliateLinkTrackingModel
      .aggregate([
        {
          $match: {
            bid: bid.toString(),
            purchaseDate: { $gte: start, $lte: end },
            conversionStatus: {
              $in: [AffiliateConversionStatus.CREDITED, AffiliateConversionStatus.PAID],
            },
          },
        },
        {
          $group: {
            _id: '$conversionCurrency',
            total: { $sum: '$conversionAmount' },
          },
        },
      ])
      .then(
        (data: Array<{ _id: string; total: number }>) =>
          new Map(data.map(({ _id, total }) => [_id, total])),
      );
  }

  /**
   * Fetches and filters analytics events for a given blog ID within a specified date range.
   *
   * @param bid - The blog ID for which to fetch events.
   * @param start - The start date of the date range filter.
   * @param end - The end date of the date range filter.
   * @returns A promise that resolves to a Map where the keys are event types and the values are Maps where the keys are dates and the values are the frequency events on those dates.
   */
  async countBeacons(bid: mongoose.Types.ObjectId, start: Date, end: Date) {
    const data = await this.analyticsService
      .fetchEvents(bid.toString())
      .then(
        (analytics) =>
          new Map(
            [...analytics].map(
              ([blogId, events]) =>
                <const>[blogId, events.filter(({ date }) => date >= start && date <= end)],
            ),
          ),
      )
      .then((result) => [...result.values()].flat());
    return new Map(
      (['view', 'click'] as const).map(
        (type) =>
          <const>[
            type,
            new Map(
              data
                .filter(({ name }) => name === type)
                .map(({ date, frequency }) => <const>[date, frequency]),
            ),
          ],
      ),
    );
  }

  async getStats(bid: string) {
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const stringBid = String(bid);

    const result = await this.affiliateLinkTrackingModel.aggregate([
      {
        $match: {
          bid: stringBid,
        },
      },
      {
        $facet: {
          todayEarnings: [
            { $match: { purchaseDate: { $gte: startOfDay, $lte: endOfDay } } },
            {
              $group: {
                _id: null,
                total: { $sum: '$affiliateCommission' },
              },
            },
          ],
          totalEarnings: [
            {
              $group: {
                _id: null,
                total: { $sum: '$affiliateCommission' },
              },
            },
          ],
          activeLinks: [
            {
              $match: {
                lastClickDate: { $gte: threeMonthsAgo },
              },
            },
            {
              $group: {
                _id: '$affiliateLink',
              },
            },
            {
              $count: 'total',
            },
          ],
          totalLinks: [
            {
              $group: {
                _id: '$affiliateLink',
              },
            },
            {
              $count: 'total',
            },
          ],
          pendingBalance: [
            {
              $match: {
                conversionStatus: {
                  $in: ['PENDING', 'PROCESSING', 'APPROVED', 'PAID'],
                },
              },
            },
            {
              $group: {
                _id: null,
                total: { $sum: '$affiliateCommission' },
              },
            },
          ],
        },
      },
    ]);

    const pendingBalance = result[0]?.pendingBalance?.[0]?.total || 0;
    const todayEarnings = result[0]?.todayEarnings?.[0]?.total || 0;
    const totalEarnings = result[0]?.totalEarnings?.[0]?.total || 0;
    const activeLinks = result[0]?.activeLinks?.[0]?.total || 0;
    const totalLinks = result[0]?.totalLinks?.[0]?.total || 0;

    // Wallet
    const { balance, currency } = await this.walletService.getBalance(bid);
    const { debit: lastWithdraw = 0 } = (await this.walletService.getLastPayout(bid)) || {};

    return {
      earnings: {
        today: todayEarnings,
        total: totalEarnings,
      },
      links: {
        active: activeLinks,
        total: totalLinks,
      },
      balance: {
        pending: pendingBalance,
        lastPayout: lastWithdraw,
        current: balance,
        currency,
      },
    };
  }
}
