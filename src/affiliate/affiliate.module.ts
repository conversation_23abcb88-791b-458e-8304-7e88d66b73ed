import { RouterModule } from '@nestjs/core';
import { Modu<PERSON> } from '@nestjs/common';

import { AnalyticsModule } from '@/analytics/analytics.module';
import { WalletModule } from '@/resources/wallet/wallet.module';

import { AffiliateLinkTrackingModule } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.module';
import { AffiliateController } from './affiliate.controller';
import { LinkLibraryModule } from './link-library/link-library.module';
import { AffiliateService } from './affiliate.service';

@Module({
  imports: [
    RouterModule.register([{ path: 'affiliate', module: LinkLibraryModule }]),
    AffiliateLinkTrackingModule,
    LinkLibraryModule,
    AnalyticsModule,
    WalletModule,
  ],
  controllers: [AffiliateController],
  providers: [AffiliateService],
})
export class AffiliateModule {}
