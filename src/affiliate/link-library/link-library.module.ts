import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LinkLibraryController } from './link-library.controller';
import { LinkLibrary, LinkLibrarySchema } from './link-library.model';
import { LinkLibraryService } from './link-library.service';

@Module({
  imports: [MongooseModule.forFeature([{ name: LinkLibrary.name, schema: LinkLibrarySchema }])],
  controllers: [LinkLibraryController],
  providers: [LinkLibraryService],
  exports: [LinkLibraryService],
})
export class LinkLibraryModule {}
