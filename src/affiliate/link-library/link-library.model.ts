import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

@Schema({
  timestamps: true,
  collection: 'affiliate-links',
})
export class LinkLibrary {
  @Prop({ required: true })
  bid: string;

  @Prop({ required: true })
  uid: string;

  @Prop({ required: true, index: 'text' })
  name: string;

  @Prop({ required: true, index: 'text' })
  category: string;

  @Prop({ required: true })
  affiliateLink: string;

  @Prop()
  productPage?: string;

  @Prop({ default: false })
  deleted: boolean;
}

export type LinkLibraryDocument = HydratedDocument<LinkLibrary>;
export const LinkLibrarySchema = SchemaFactory.createForClass(LinkLibrary);
