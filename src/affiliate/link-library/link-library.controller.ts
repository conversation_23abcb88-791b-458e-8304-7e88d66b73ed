import { Auth } from '@/auth/guards/auth.guard';
import { RolesGuard } from '@/auth/guards/roles.guard';
import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { CrudController } from '@/crud/crud.controller';
import { CrudQ<PERSON>y, ListResponse } from '@/crud/crud.interface';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreateLinkLibraryDto, UpdateLinkLibraryDto } from './link-library.dto';
import { LinkLibrary } from './link-library.model';
import { LinkLibraryService } from './link-library.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { parse } from 'csv-parse/sync';

@Controller('link-library')
@UsePipes(new ValidationPipe())
@UseGuards(Auth, RolesGuard)
export class LinkLibraryController extends CrudController<
  LinkLibrary,
  CreateLinkLibraryDto,
  UpdateLinkLibraryDto
> {
  constructor(private readonly linkLibraryService: LinkLibraryService) {
    super(linkLibraryService);
  }

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  async importFromCsv(
    @UploadedFile() file: Express.Multer.File,
    @Body('mapping') mapping: Record<string, string>,
    @Req() { bid, uid }: AuthenticatedRequest,
  ) {
    if (!file) throw new BadRequestException('No file uploaded');

    try {
      const parsedRecords = parse(file.buffer, {
        columns: true,
        trim: true,
        skip_empty_lines: true,
      }) as Record<string, string>[];

      const linkLibraryRecords = parsedRecords
        .map((record) => {
          const MapName = mapping.name;
          const MapCategory = mapping.category;
          const MapAffiliateLink = mapping.affiliateLink;
          const MapProductPage = mapping.productPage;

          return {
            name: record[MapName]?.trim(),
            category: record[MapCategory]?.trim(),
            affiliateLink: record[MapAffiliateLink]?.trim(),
            productPage: MapProductPage ? record[MapProductPage]?.trim() : undefined,
            bid,
            uid,
          };
        })
        .filter((record) => Boolean(record.name && record.category && record.affiliateLink));

      if (linkLibraryRecords.length === 0) {
        throw new BadRequestException('No valid records found in CSV');
      }
      if (linkLibraryRecords.length > 1000) {
        throw new BadRequestException('CSV file contains too many records (max: 1000)');
      }

      return this.linkLibraryService.bulkImport(linkLibraryRecords);
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new InternalServerErrorException('Failed to process CSV file');
    }
  }

  @Get()
  async findAll(
    @Query() query: CrudQuery,
    @Req() { bid }: AuthenticatedRequest,
  ): Promise<ListResponse<LinkLibrary>> {
    query.filter = { bid };
    return this.linkLibraryService.findAll(query);
  }

  @Get(':id')
  async findOne(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid }: AuthenticatedRequest,
  ): Promise<LinkLibrary> {
    query.filter = { bid };
    return this.linkLibraryService.findOneById(id, query);
  }

  @Post()
  async create(
    @Body() body: CreateLinkLibraryDto,
    @Req() { bid, uid }: AuthenticatedRequest,
  ): Promise<LinkLibrary> {
    return super.create({ ...body, bid, uid });
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() body: UpdateLinkLibraryDto,
    @Query() query: CrudQuery,
    @Req() { bid, uid }: AuthenticatedRequest,
  ): Promise<LinkLibrary> {
    query.filter = { bid };
    return super.update(id, { ...body, bid, uid });
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @Query() query: CrudQuery,
    @Req() { bid }: AuthenticatedRequest,
  ): Promise<{ success: boolean }> {
    query.filter = { bid };
    return super.remove(id);
  }
}
