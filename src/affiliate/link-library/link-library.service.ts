import { CrudService } from '@/crud/crud.service';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { LinkLibrary, LinkLibraryDocument } from './link-library.model';

@Injectable()
export class LinkLibraryService extends CrudService<LinkLibrary> {
  constructor(
    @InjectModel(LinkLibrary.name)
    private linkLibraryModel: Model<LinkLibraryDocument>,
  ) {
    super(linkLibraryModel);
  }
  /**
   * Searches the link library for entries that match the given bid and keywords.
   *
   * @param bid - The ObjectId of the bid to search for.
   * @param keywords - An array of keywords to search for in the name field.
   * @returns A promise that resolves to an array of matching link library entries.
   */
  async search(bid: mongoose.Types.ObjectId, keywords: string[]) {
    const query: any = {
      bid: bid.toString(),
      deleted: false,
    };

    if (keywords.length > 0) {
      query.$text = { $search: keywords.join(' ') };
    }

    return this.linkLibraryModel.find(query).exec();
  }

  async bulkImport(records: Partial<LinkLibrary>[]): Promise<LinkLibrary[]> {
    const session = await this.linkLibraryModel.startSession();
    try {
      session.startTransaction();

      const bulkOps = records.map((record) => ({
        updateOne: {
          filter: {
            bid: record.bid,
            name: record.name,
            affiliateLink: record.affiliateLink,
          },
          update: { $set: record },
          upsert: true,
        },
      }));

      const result = await this.linkLibraryModel.bulkWrite(bulkOps, { session });
      await session.commitTransaction();

      const insertedIds = Object.entries(result.upsertedIds || {}).map(([key, id]) => ({
        ...records[parseInt(key)],
        _id: id,
      })) as LinkLibrary[];

      return insertedIds;
    } catch (_error) {
      await session.abortTransaction();
      throw new InternalServerErrorException('Failed to import links');
    } finally {
      session.endSession();
    }
  }
}
