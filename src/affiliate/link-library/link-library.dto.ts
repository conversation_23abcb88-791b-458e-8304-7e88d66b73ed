import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateLinkLibraryDto {
  @IsString()
  @IsOptional()
  readonly bid?: string;

  @IsString()
  @IsOptional()
  readonly uid?: string;

  @IsString()
  @IsNotEmpty()
  readonly name: string;

  @IsString()
  @IsNotEmpty()
  readonly category: string;

  @IsString()
  @IsNotEmpty()
  readonly affiliateLink: string;

  @IsString()
  @IsOptional()
  readonly productPage?: string;
}

export class UpdateLinkLibraryDto extends PartialType(CreateLinkLibraryDto) {}
