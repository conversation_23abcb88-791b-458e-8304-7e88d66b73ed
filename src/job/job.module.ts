import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { JOB_OPTIONS, JOB_QUEUES, QueueOpts } from '@common/constants';
import { RATE_LIMITS } from '@/common/configs/rate-limits.config';

const BullQueues = [
  BullModule.registerQueue({
    name: JOB_QUEUES.BLOG_REQUEST,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.TRANSCRIBE_MEDIA,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 3, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 3, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_BLOG_OUTLINE,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 3, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 10, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.SEO_ANALYZE_KEYWORD,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_BLOG_CONTENT,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 2, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_BLOG_KEYWORDS,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.SEO_SCORING,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.AFFILIATE_PRODUCT_SEARCH,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 20, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.GENERATE_AFFILIATE_LINKS,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 10, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.DISPATCH_TO_PUBLISHERS,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.PUBLISH_BLOG,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 3, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.PUBLISH_SOCIAL,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 3, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.BLOG_CREDITS,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 5, duration: 1000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.IMPACT_PRODUCT_SEARCH,
    defaultJobOptions: JOB_OPTIONS,
    limiter: {
      max: RATE_LIMITS.IMPACT_API.REQUESTS_PER_MINUTE,
      duration: 60000,
    },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.AFFILIATE_SALE_MONITORING,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 1, duration: 60000 },
    settings: QueueOpts,
  }),
  BullModule.registerQueue({
    name: JOB_QUEUES.BLOG_REVIEW,
    defaultJobOptions: JOB_OPTIONS,
    limiter: { max: 2, duration: 1000 },
    settings: QueueOpts,
  }),
];
@Module({
  imports: [...BullQueues],
  exports: [...BullQueues],
})
export class JobModule {}
