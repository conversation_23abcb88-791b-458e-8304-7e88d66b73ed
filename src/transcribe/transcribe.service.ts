/* eslint-disable complexity */
import type { Blog } from '@blog/blog.model';

import { getURLVideoID, getVideoID, getInfo, filterFormats } from '@distube/ytdl-core';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import * as fs from 'fs';

import { JOB_OPTIONS, JOB_QUEUES } from '@common/constants';
import { DeepgramService } from '@asr/deepgram.service';
import { BlogService } from '@blog/blog.service';

import { mapLanguageCode } from './asr-model-mapper';
import { TranscribeDto } from './transcribe.dto';
import { convertMillisecondsToMinutes, createChunks, isYouTubeURL } from '@common/helpers';
import { OpenaiService } from '@openai/openai.service';
import config from '@common/configs/config';
import { YouTubeTranscript } from 'src/youtube/youtube-content/youtube-content.interface';
import { InjectModel } from '@nestjs/mongoose';
import { YoutubeModel } from 'src/youtube/youtube-data/youtube.model';
import { Model } from 'mongoose';
import { YoutubeTranscriptV2 } from './youtube-transcript';
import { isYouTubeUrl } from '@/common/utils/url';

@Injectable()
export class TranscribeService {
  private readonly logger = new Logger(TranscribeService.name);
  private blogifyMedia: AxiosInstance;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY) private generateTranscriptSummary: Queue,
    @InjectModel(YoutubeModel.name) private youtubeModel: Model<YoutubeModel>,
    private readonly deepgramService: DeepgramService,
    private readonly configService: ConfigService,
    private readonly blogService: BlogService,
    private readonly openaiService: OpenaiService,
  ) {
    this.blogifyMedia = axios.create({
      baseURL: config().internalApps.blogifyMedia.url,
      headers: {
        'x-api-key': config().internalApps.blogifyMedia.apiKey,
      },
    });
  }

  async transcribe(
    data: TranscribeDto,
    bid: string,
    blogId: string,
    jobData = {},
    inputLanguage = 'Global English',
    pov = 'First Person',
  ) {
    let caption: string;
    let timestampedTranscription: Blog['transcript'];
    let transcription: string;
    let summary: string;
    let hasUsableCaption = false;

    const { email, url } = data;

    const mediaUrl = url;

    if (isYouTubeURL(url) && jobData['clipTimeSpan']) {
      const [startTime, endTime] = jobData['clipTimeSpan'].split(':');
      const youtubeTranscript: YouTubeTranscript[] = await this.getYoutubeTranscript(bid, url);
      const clipTranscript = youtubeTranscript.filter(
        (c) => c.startTime >= Number(startTime) && c.endTime <= Number(endTime),
      );
      timestampedTranscription = clipTranscript;
      caption = clipTranscript.map((c) => c.text).join(' ');
    } else if (isYouTubeURL(url)) {
      try {
        caption = (await this.getYouTubeTranscript(url)) as string;
        timestampedTranscription = (await this.getYouTubeTranscript(
          url,
          false,
        )) as typeof timestampedTranscription;
        this.logger.debug(`got caption for ${url} blogId ${blogId} bid ${bid}`);
      } catch (error) {
        this.logger.error(
          `Error getting caption for ${url} blogId ${blogId} bid ${bid} ${error?.message}`,
        );
        hasUsableCaption = false;
      }
    } else {
      hasUsableCaption = false;
    }

    hasUsableCaption = caption && caption.length > 1000;

    if (url.includes('/shorts/') || jobData['clipTimeSpan']) {
      hasUsableCaption = caption && caption.length > 400;
    }

    let audioPath: string;
    if (hasUsableCaption) {
      await this.generateTranscriptSummary.add(
        {
          ...jobData,
          transcription: caption,
          transcript: timestampedTranscription,
        },
        JOB_OPTIONS,
      );

      await this.blogService.update(bid, blogId, {
        transcription: caption,
        transcript: timestampedTranscription,
        status: 'transcribed',
      } as Blog);
      this.logger.debug(`pushed to generate blog queue from caption for ${blogId}`);
    } else {
      try {
        this.logger.debug(`getting final media url using axios for ${mediaUrl} for ${blogId}`);

        const url = new URL(mediaUrl);

        if (isYouTubeUrl(url.toString())) {
          ['list', 'index'].forEach((param) => url.searchParams.delete(param));
        }

        const downloadData = await this.blogifyMedia.post('/transcode', {
          url: url.toString(),
        });

        audioPath = downloadData?.data?.download_url;
      } catch (error) {
        this.logger.error(
          {
            mediaUrl,
            blogId,
            bid,
            err: error,
          },
          'Error getting final media url using axios',
        );
        if (error instanceof AxiosError) {
          throw new Error(error.response?.data?.error || error.request || error.message);
        } else {
          throw new Error('Error getting final media url using axios ' + JSON.stringify(error), {
            cause: error,
          });
        }
      }

      // Transcribe using Deepgram
      try {
        if (!audioPath) {
          throw new Error(
            `Failed to download or convert video to mp3 for ${mediaUrl} job ${blogId}`,
          );
        }
        const { languageCode, params } = mapLanguageCode(inputLanguage, 'deepgram');
        this.logger.debug(
          `transcribing using deepgram ${mediaUrl} ${blogId} ${languageCode} ${JSON.stringify(
            params,
          )} for ${email}`,
        );
        const deepgramResponse = await this.deepgramService.getTranscript({
          id: blogId,
          audioUrl: audioPath,
          languageCode,
          params,
        });
        const deepgramTranscript = this.deepgramService.transformTranscript(deepgramResponse);
        transcription = deepgramTranscript.transcription;
        timestampedTranscription = deepgramTranscript.captions;
        summary = deepgramTranscript.summary;

        await this.generateTranscriptSummary.add(
          {
            ...jobData,
            transcription:
              pov === 'First Person' ? summary || transcription : transcription || summary,
            transcript: timestampedTranscription,
          },
          JOB_OPTIONS,
        );
        this.logger.debug(
          `Transcribe successfully and pushed to generate blog queue from deepgram transcript for ${blogId}`,
        );
        const generationStatus = `Generated transcription from url next step is to generate transcription summary`;
        await this.blogService.update(bid, blogId, {
          transcription,
          transcript: timestampedTranscription,
          status: 'transcribed',
          generationStatus,
        } as Blog);
        this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
        if (audioPath && fs.existsSync(audioPath)) {
          fs.unlinkSync(audioPath);
        }
      } catch (error) {
        this.logger.error(
          `Error transcribing using deepgram ${mediaUrl} ${blogId}`,
          error?.message || error,
        );
        await this.blogService.update(bid, blogId, {
          transcription,
          transcript: timestampedTranscription,
          status: 'transcription_failed',
          generationStatus: `Failed to generate transcription from the YouTube video this video is blocked and cannot be downloaded if this video is just uploaded then try again after few hours or try another video`,
        } as Blog);
        if (audioPath && fs.existsSync(audioPath)) {
          fs.unlinkSync(audioPath);
        }
        throw error;
      }
    }

    return 'submitted for transcription';
  }

  async transcribeYouTubeVideo(url: string, bid: string, language = 'Global English') {
    if (!isYouTubeURL(url)) {
      throw new Error(`Invalid YouTube URL: ${url}`);
    }

    let hasUsableCaption = false;
    let caption = '';
    let transcription = '';
    let summary = '';
    let audioPath: string;

    try {
      caption = (await this.getYouTubeTranscript(url)) as string;
      this.logger.debug(`got caption for ${url} bid ${bid}`);
    } catch (error) {
      this.logger.error(`Error getting caption for ${url} bid ${bid} ${error?.message}`);
      hasUsableCaption = false;
    }

    hasUsableCaption = caption && caption.length > 1000;

    if (url.includes('/shorts/')) {
      hasUsableCaption = caption && caption.length > 400;
    }

    if (hasUsableCaption) {
      summary = await this.generateSummary(caption, bid);
      return summary;
    }

    // Get transcoded media url for blogify media
    try {
      this.logger.debug(
        {
          url,
        },
        'getting final media url using axios',
      );
      const downloadData = await this.blogifyMedia.post('/transcode', {
        url,
      });

      audioPath = downloadData?.data?.download_url;
    } catch (error) {
      this.logger.error({ url, err: error }, 'Error getting final media url using axios');

      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.error || error.request);
      } else {
        throw error;
      }
    }

    // Transcribe using Deepgram
    try {
      if (!audioPath) {
        throw new Error(`Failed to download or convert video to mp3 for ${url}`);
      }
      const { languageCode, params } = mapLanguageCode(language, 'deepgram');
      this.logger.debug(
        `transcribing using deepgram ${url} ${languageCode} ${params} for bid ${bid}`,
      );
      const deepgramResponse = await this.deepgramService.getTranscript({
        id: bid,
        audioUrl: audioPath,
        languageCode,
        ...params,
      });
      const deepgramTranscript = this.deepgramService.transformTranscript(deepgramResponse);
      transcription = deepgramTranscript.transcription;
      summary = deepgramTranscript.summary;

      if (!summary) {
        summary = await this.generateSummary(transcription, bid);
      }

      return summary;
    } catch (error) {
      this.logger.error(
        `Error transcribing using deepgram ${url} for bid: ${bid}`,
        error?.message || error,
      );
      throw error;
    }
  }

  async getYouTubeTranscript(url: string, textOnly = true): Promise<string | YouTubeTranscript[]> {
    try {
      const { youtubeUrl } = await this.getYouTubeInfo(url);
      let ytCaptions = [];

      // First attempt - try with English language
      try {
        const opts = textOnly ? { lang: 'en' } : {};
        this.logger.debug(`Attempting to fetch transcript with options: ${JSON.stringify(opts)}`);
        ytCaptions = await YoutubeTranscriptV2.fetchTranscript(youtubeUrl, opts);
      } catch (error) {
        this.logger.debug(`Failed to get English captions for ${youtubeUrl}: ${error?.message}`);
      }

      // Second attempt - try without language specification
      if (!ytCaptions.length) {
        try {
          this.logger.debug(`Attempting to fetch transcript without language specification`);
          ytCaptions = await YoutubeTranscriptV2.fetchTranscript(youtubeUrl);
        } catch (error) {
          this.logger.error(`Failed to get any captions for ${youtubeUrl}: ${error?.message}`);
          throw new Error(`Could not extract captions from video: ${error?.message}`);
        }
      }

      // Process captions
      if (textOnly) {
        const caption = ytCaptions.map((c) => c.text).join(' ');
        this.logger.debug(`Successfully extracted text-only captions`);
        return caption;
      }

      // Map captions with timestamps
      const youtubeCaptions = ytCaptions.map((caption, index, array) => {
        const startTime = convertMillisecondsToMinutes(caption.offset);
        const endTime =
          index < array.length - 1
            ? convertMillisecondsToMinutes(array[index + 1].offset)
            : convertMillisecondsToMinutes(caption.offset + caption.duration);

        return { startTime, endTime, text: caption.text };
      });

      this.logger.debug(`Successfully extracted ${youtubeCaptions.length} caption segments`);
      return youtubeCaptions;
    } catch (error) {
      this.logger.error(`Error in getYouTubeTranscript for ${url}: ${error?.message}`);
      throw new Error(`Failed to process YouTube transcript: ${error?.message}`);
    }
  }

  async getYouTubeTranscriptUsingASR(
    url: string,
    bid: string,
    language = 'Global English',
  ): Promise<YouTubeTranscript[]> {
    let audioPath: string;

    // Get transcoded media url for blogify media
    try {
      this.logger.debug(`getting blogify media url for ${url} bid: ${bid}`);
      const downloadData = await this.blogifyMedia.post('/transcode', {
        url,
      });

      audioPath = downloadData?.data?.download_url;
    } catch (error) {
      this.logger.error(`Error getting blogify media url for ${url} bid: ${bid}`, error?.message);
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.error || error.request);
      } else {
        throw error;
      }
    }

    // Transcribe using Deepgram
    try {
      if (!audioPath) {
        throw new Error(`Failed to download or convert video to mp3 for ${url}`);
      }
      const { languageCode, params } = mapLanguageCode(language, 'deepgram');
      this.logger.debug(
        `transcribing using deepgram ${url} ${languageCode} ${params} for bid ${bid}`,
      );
      const deepgramResponse = await this.deepgramService.getTranscript({
        id: bid,
        audioUrl: audioPath,
        languageCode,
        ...params,
      });
      const { captions } = this.deepgramService.transformTranscript(deepgramResponse);

      const youtubeCaptions: YouTubeTranscript[] = captions.map((caption) => ({
        startTime: caption.startTime,
        endTime: caption.endTime,
        text: caption.text,
        speaker: caption.speaker?.toString(),
      }));

      return youtubeCaptions;
    } catch (error) {
      this.logger.error(
        `Error transcribing using deepgram ${url} for bid: ${bid}`,
        error?.message || error,
      );
      throw error;
    }
  }

  private async generateSummary(transcription: string, bid: string): Promise<string> {
    let summaries = '';
    const maxChunkTokenLimit = 8000;
    const maxTranscriptTokenLimit = 8000;

    try {
      const transcriptChunks = createChunks(transcription, maxChunkTokenLimit);

      this.logger.log(`Total trancription chunks: ${transcriptChunks.length} for bid ${bid}`);

      const promises = [];
      transcriptChunks.forEach((chunk) => {
        promises.push(this.openaiService.generateSummary(bid, chunk, true));
      });

      await Promise.all(promises).then((summary) => {
        if (Array.isArray(summary)) {
          summaries += summary.join('\n');
        } else {
          summaries += summary;
        }
      });

      return summaries;
    } catch (error) {
      summaries = this.openaiService.truncateText(transcription, maxTranscriptTokenLimit);
      throw error;
    }
  }

  private getVideoIdFromYouTubeUrl(url: string): string {
    // Remove any URL parameters after cleaning the URL
    const cleanUrl = url.split('?')[0];
    const youtubeUrl = cleanUrl.replace(/^(?:https?:\/\/)?(?:www\.)?/i, '');

    const patterns = [
      // Standard watch URLs
      /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([\w-]{11})/i,
      // Live and premiere URLs
      /youtube\.com\/live\/([\w-]{11})/i,
      // Shortened youtu.be URLs
      /youtu\.be\/([\w-]{11})/i,
      // Mobile app URLs
      /youtube\.com\/watch\/([\w-]{11})/i,
      // Shorts URLs
      /youtube\.com\/shorts\/([\w-]{11})/i,
      // Channel video URLs
      /youtube\.com\/.*[?&]v=([\w-]{11})/i,
      // Embed URLs
      /youtube\.com\/embed\/([\w-]{11})/i,
      // Attribution links
      /youtube\.com\/attribution_link.*[?&]v=([\w-]{11})/i,
      // Share URLs
      /youtube\.com\/shared\?.*[?&]v=([\w-]{11})/i,
    ];

    const match = patterns.map((pattern) => youtubeUrl.match(pattern)).find(Boolean);

    this.logger.debug(
      `Extracted video ID from URL: ${match?.[1] || 'not found'} from ${youtubeUrl}`,
    );
    return match ? match[1] : '';
  }

  async getYouTubeInfo(url: string) {
    let id = '';
    try {
      id = getVideoID(url);
    } catch (error) {
      this.logger.error(error);
      id = this.getVideoIdFromYouTubeUrl(url);
    }
    if (!id) {
      throw new Error(`Invalid YouTube URL: ${url}`);
    }

    try {
      const info = await getInfo(id);
      const youtubeUrl = `https://youtu.be/${id}`;
      const { title, description } = info.videoDetails;
      const formats = filterFormats(info.formats, 'audio');
      const mediaFormats = formats.find((f) => f.container === 'mp4');
      const ytVideoUrl = mediaFormats[0]?.url || mediaFormats?.url;
      return { title, description, youtubeUrl, ytVideoUrl };
    } catch (error) {
      this.logger.error(`Error getting video info for ${url}`);
      this.logger.error(error?.message || error);
      throw new Error(`Invalid YouTube URL: ${url}`);
    }
  }

  async getYoutubeTranscript(bid: string, url: string): Promise<YouTubeTranscript[]> {
    const videoId = getURLVideoID(url);
    const entry = await this.youtubeModel.findOne({
      bid,
      $or: [{ videoId }, { url }],
    });

    if (entry && entry.transcript) {
      return entry.transcript as YouTubeTranscript[];
    }

    throw new Error('Transcript not found');
  }
}
