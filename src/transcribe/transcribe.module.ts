import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';

import { YoutubeSchema, YoutubeModel } from 'src/youtube/youtube-data/youtube.model';
import { CreditTransactionModule } from '@resources/credit-transaction/credit-transaction.module';
import { OpenaiModule } from '@openai/openai.module';
import { EventModule } from 'src/event/event.module';
import { BlogModule } from '@blog/blog.module';
import { ASRModule } from 'src/asr/asr.module';
import { JobModule } from '@job/job.module';

import { TranscriptSummaryProcessor } from './transcript-summary.processor';
import { TranscribeMediaProcessor } from './transcribe-media.processor';
import { TranscribeController } from './transcribe.controller';
import { TranscribeService } from './transcribe.service';
import { BusinessModule } from '@business/business.module';
import { BlogGenerationModule } from '@blog-generation/blog-generation.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: YoutubeModel.name, schema: YoutubeSchema }]),
    CreditTransactionModule,
    OpenaiModule,
    EventModule,
    BlogModule,
    ASRModule,
    JobModule,
    BusinessModule,
    BlogGenerationModule,
  ],
  providers: [TranscriptSummaryProcessor, TranscribeMediaProcessor, TranscribeService],
  controllers: [TranscribeController],
  exports: [TranscribeService],
})
export class TranscribeModule {}
