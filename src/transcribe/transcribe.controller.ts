import { Body, Controller, Logger, <PERSON>, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { TranscribeDto } from './transcribe.dto';
import { TranscribeService } from './transcribe.service';

@Controller('transcribe')
@ApiTags('Transcribe Controller')
export class TranscribeController {
  private readonly logger = new Logger(TranscribeController.name);
  constructor(private readonly transcribeService: TranscribeService) {}
  @Post('/')
  async assemblyController(@Res() res: Response, @Body() body: TranscribeDto) {
    try {
      // const result = await this.transcribeService.transcribe(body);
      // res.status(200).send(result);
    } catch (error) {
      this.logger.error(`Failed to transcribe ${body.url} for ${body.email}`, error?.message);
      res.status(400).send(error?.message);
    }
  }
}
