import type { BlogQueuePayload, Blog } from '@blog/blog.model';
import type { Queue, Job } from 'bull';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { JOB_OPTIONS, JOB_QUEUES } from '@common/constants';
import { countTokens, createChunks } from '@common/helpers';
import { GatewayService } from '@modules/gateway/gateway.service';
import { BlogMLService } from '@blog/blog-ml/blog-ml.service';
import { OpenaiService } from '@openai/openai.service';
import { BlogService } from '@blog/blog.service';
import { BusinessService } from '@business/business.service';
import { getPlanModel, TASK } from '@llm/llm.models';
import { BlogGenerationService } from '@blog-generation/blog-generation.service';
import { BaseProcessor } from '@common/queue/base.processor';

@Processor(JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY)
export class TranscriptSummaryProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_TRANSCRIPT_SUMMARY) transcriptSummaryQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_OUTLINE) private generateBlogOutline: Queue,
    private readonly gatewayService: GatewayService,
    private readonly openaiService: OpenaiService,
    private readonly blogMLService: BlogMLService,
    private readonly blogService: BlogService,
    private readonly businessService: BusinessService,
    private readonly blogGenerationService: BlogGenerationService,
  ) {
    super(transcriptSummaryQueue);
  }

  @Process({ concurrency: 5 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const {
        blogId,
        bid,
        uid,
        email,
        transcription,
        identifier,
        pov,
        blogSize,
        inputLanguage,
        blogLanguage,
        blogTone,
        sourceType,
        sourceName,
      } = job.data;

      try {
        const totalTokens = countTokens(transcription);
        await this.blogService.update(bid, blogId, { status: 'summarizing' });
        this.gatewayService.sendBlogStatusUpdate(uid, {
          _id: blogId,
          identifier,
          status: 'summarizing',
        });

        this.logger.log(
          `Generating transcript summary for blog ${blogId} for ${email} total tokens in transcript: ${totalTokens}`,
        );

        const maxTranscriptTokenLimit = this.getMaxTokenLimit(blogSize);
        const maxChunkTokenLimit = this.getMaxChunkTokenLimit(blogSize);

        const { subscriptionPlan } = await this.businessService.findOne(bid);
        const model = getPlanModel(subscriptionPlan, TASK.SUMMARY);

        const options = {
          model,
          context: transcription,
          language: blogLanguage,
          tone: blogTone,
          pov,
          sourceType,
          sourceName,
          blogId,
          transcription,
          inputLanguage,
          blogLanguage,
          blogSize,
          totalTokens,
          maxTranscriptTokenLimit,
          maxChunkTokenLimit,
        };

        const summaries = await this.generateSummaryFromSource(options);

        await this.updateBlogWithSummary(bid, blogId, summaries, email, uid, identifier);
        await this.addJobToGenerateBlogOutline(job.data, summaries, blogId, email);
      } catch (error) {
        this.logger.error(`Error processing transcript summary for blog ${job.data.blogId}`, error);
        throw error;
      }
    });
  }

  private async generateSummaryFromSource(options) {
    try {
      const response = await this.blogGenerationService.generateSummary(options);

      // Track cost if available
      if (response.metadata?.estimatedCost > 0) {
        try {
          const { blogId, bid, model } = options;
          // Get current cost breakdown if exists
          const blog = await this.blogService.findById(bid, blogId);
          const costBreakdown = blog.generationCostBreakdown || [];

          // Add this step's cost
          costBreakdown.push({
            step: 'summary_generation',
            cost: response.metadata.estimatedCost,
            model,
            timestamp: new Date(),
          });

          // Calculate total cost
          const totalCost = costBreakdown.reduce((sum, item) => sum + item.cost, 0);

          // Update blog with new costs
          await this.blogService.update(bid, blogId, {
            generationCost: totalCost,
            generationCostBreakdown: costBreakdown,
          });

          this.logger.debug(
            `Cost for summary generation: $${response.metadata.estimatedCost.toFixed(6)}, total: $${totalCost.toFixed(6)}`,
          );
        } catch (error) {
          this.logger.error('Error tracking summary generation cost', error);
        }
      }

      return response.content;
    } catch (e) {
      this.logger.error(`Primary summary generation failed for the Blog ID: ${options.blogId}.`, e);
      this.logger.log('Trying fallback method...');
      return await this.generateSummaryUsingFallbackMethod(options);
    }
  }

  private async generateSummaryUsingFallbackMethod(options) {
    try {
      const body = {
        transcript: options.transcription,
        input_language: options.inputLanguage,
        output_language: options.blogLanguage,
        pov: options.pov,
        tone: options.blogTone,
        size: options.blogSize,
      };

      const blogifySummaries = await this.blogMLService.generateTranscriptSummary(body);

      if (typeof blogifySummaries !== 'string' || !blogifySummaries?.length) {
        throw new Error('Error generating transcription summary using Blogify ML, invalid format.');
      }

      return blogifySummaries;
    } catch (e) {
      this.logger.error(
        `BlogifyML failed to generate transcript summary for the Blog ID: ${options.blogId}.`,
        e,
      );
      return await this.generateSummaryUsingLegacyMethod(options);
    }
  }

  private async generateSummaryUsingLegacyMethod(options) {
    if (options.totalTokens >= options.maxTranscriptTokenLimit) {
      return await this.generateChunkedSummary(options);
    } else {
      return await this.generateFullSummary(options);
    }
  }

  private async generateChunkedSummary(options) {
    try {
      const transcriptChunks = createChunks(options.transcription, options.maxChunkTokenLimit);
      this.logger.log(
        `Total transcription chunks: ${transcriptChunks.length} for blog ${options.blogId}`,
      );

      const promises = transcriptChunks.map((chunk) =>
        this.openaiService.generateSummary(
          options.blogId,
          chunk,
          true,
          options.pov,
          options.blogSize,
        ),
      );

      const results = await Promise.all(promises);
      return results.join('\n');
    } catch (error) {
      this.logger.error(error);
      return this.openaiService.truncateText(
        options.transcription,
        options.maxTranscriptTokenLimit,
      );
    }
  }

  private async generateFullSummary(options) {
    try {
      return await this.openaiService.generateSummary(
        options.blogId,
        options.transcription,
        false,
        options.pov,
        options.blogSize,
      );
    } catch (error) {
      this.logger.error(error);
      return options.transcription;
    }
  }

  private async updateBlogWithSummary(bid, blogId, summaries, email, uid, identifier) {
    try {
      const generationStatus = `Transcribed the media successfully next step is to generate blog outline`;
      await this.blogService.update(bid, blogId, {
        transcriptionSummary: summaries,
        status: 'summarized',
        generationStatus,
      } as Blog);
      this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'summarized',
      });
    } catch (error) {
      this.logger.error(
        `Transcription summary generation failed for blog ${blogId}`,
        error.message,
      );
      throw error;
    }
  }

  private async addJobToGenerateBlogOutline(data, summaries, blogId, email) {
    try {
      await this.generateBlogOutline.add(
        { ...data, transcriptionSummary: summaries },
        { ...JOB_OPTIONS, jobId: blogId },
      );
      this.logger.debug(`Job added to generate blog outline for blog ${blogId} email ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to add job to generate blog outline for blog ${blogId} email ${email}`,
        error.message,
      );
      throw error;
    }
  }

  private getMaxTokenLimit(blogSize) {
    return blogSize === 'large' ? 3000 : blogSize === 'medium' ? 5000 : 8000;
  }

  private getMaxChunkTokenLimit(blogSize) {
    return blogSize === 'large' ? 3000 : blogSize === 'medium' ? 5000 : 8000;
  }
}
