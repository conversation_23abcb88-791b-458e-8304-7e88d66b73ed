import type { BlogQueuePayload } from '@/blog/blog.model';
import type { Queue, Job } from 'bull';
import type { Model } from 'mongoose';

import { OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';

import { USER_FRIENDLY_ERROR_MESSAGE, JOB_QUEUES } from '@/common/constants';
import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { BlogService } from '@/blog/blog.service';
import { Blog } from '@/blog/blog.model';

import { TranscribeService } from './transcribe.service';

@Processor(JOB_QUEUES.TRANSCRIBE_MEDIA)
export class TranscribeMediaProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.TRANSCRIBE_MEDIA;

  constructor(
    @InjectQueue(JOB_QUEUES.TRANSCRIBE_MEDIA) transcribeMediaQueue: Queue,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly transcribeService: TranscribeService,
    private readonly gatewayService: GatewayService,
    private readonly blogService: BlogService,
  ) {
    super(transcribeMediaQueue, 2);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    const { bid, uid, blogId, email, url, inputLanguage, identifier, pov } = job.data;

    const payload: Partial<Blog> = { _id: blogId, identifier, status: 'transcribing' };
    await this.blogService.update(bid, blogId, { status: 'transcribing' });
    this.gatewayService.sendBlogStatusUpdate(uid, payload);

    await job.progress(50);

    await this.transcribeService.transcribe(
      { email, url },
      bid,
      job.id as string,
      job.data,
      inputLanguage,
      pov,
    );

    await job.progress(100);
  }

  @OnQueueCompleted()
  protected async handleCompleted(job: Job<BlogQueuePayload>) {
    const { blogId, uid } = job.data;
    this.gatewayService.sendBlogStatusUpdate(uid, { _id: blogId, status: 'transcribed' });
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    try {
      const retriesLeft = job.attemptsMade < job.opts.attempts;
      const errorCode = error['code'] || null;
      const isRetryableError =
        !errorCode || errorCode === 'NETWORK_ERROR' || errorCode === 'TIMEOUT_ERROR';

      // Retry the job if we have retries left and it's a retryable error
      if (retriesLeft && isRetryableError) {
        await job.retry();
        return;
      }

      const { blogId, bid, uid, identifier, creditTransactionId } = job.data;
      const failedQueue = JOB_QUEUES.TRANSCRIBE_MEDIA;
      const failReason = USER_FRIENDLY_ERROR_MESSAGE.TRANSCRIPTION_FAILED + ': ' + error?.message;
      const status = 'transcription_failed';

      await this.blogModel.updateOne(
        { _id: blogId, bid },
        { failedQueue, failReason, errorCode, status },
      );

      if (creditTransactionId) {
        await this.creditTransactionService.refund(bid, creditTransactionId);
      }

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        failReason,
        status,
      });
    } catch (e) {
      this.logger.error("Couldn't handle failed job", e);
    }
  }
}
