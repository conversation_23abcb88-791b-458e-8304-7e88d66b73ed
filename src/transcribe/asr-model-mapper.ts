export const asrLanguageTable = [
  /**
   * Assembly.
   */
  {
    asr: 'assembly',
    languageName: 'Dutch',
    languageCode: 'nl',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Global English',
    languageCode: 'en',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'US English',
    languageCode: 'en-US',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Australia English',
    languageCode: 'en-AU',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'UK English',
    languageCode: 'en-GB',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'French',
    languageCode: 'fr',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'German',
    languageCode: 'de',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Hindi',
    languageCode: 'hi',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Italian',
    languageCode: 'it',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Japanese',
    languageCode: 'ja',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Portuguese',
    languageCode: 'pt',
    priority: 1,
  },
  {
    asr: 'assembly',
    languageName: 'Spanish',
    languageCode: 'es',
    priority: 1,
  },

  /**
   * Deepgram nova-2/nova/base/whisper.
   */
  {
    asr: 'deepgram',
    languageName: 'Chinese',
    languageCode: 'zh',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'China Chinese',
    languageCode: 'zh-CN',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Taiwan Chinese',
    languageCode: 'zh-TW',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Danish',
    languageCode: 'da',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Dutch',
    languageCode: 'nl',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Global English',
    languageCode: 'en',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Australia English',
    languageCode: 'en-AU',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'UK English',
    languageCode: 'en-GB',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'India English',
    languageCode: 'en-IN',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'New Zealand English',
    languageCode: 'en-NZ',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'US English',
    languageCode: 'en-US',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Flemish',
    languageCode: 'nl',
    priority: 2,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'French',
    languageCode: 'fr',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'French Canada',
    languageCode: 'fr-CA',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'German',
    languageCode: 'de',
    priority: 2,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Hindi',
    languageCode: 'hi',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Hindi Latin',
    languageCode: 'hi-Latn',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Indonesian',
    languageCode: 'id',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Italian',
    languageCode: 'it',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Japanese',
    languageCode: 'ja',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Korean',
    languageCode: 'ko',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Norwegian',
    languageCode: 'no',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Polish',
    languageCode: 'pl',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Brazil Portuguese',
    languageCode: 'pt-BR',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Portugal Portuguese',
    languageCode: 'pt-PT',
    priority: 1,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Portuguese',
    languageCode: 'pt',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Russian',
    languageCode: 'ru',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Spanish',
    languageCode: 'es',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Latin American Spanish',
    languageCode: 'es-419',
    priority: 1,
    params: { model: 'nova-2' },
  },
  {
    asr: 'deepgram',
    languageName: 'Swedish',
    languageCode: 'sv',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Tamil',
    languageCode: 'ta',
    priority: 2,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Turkish',
    languageCode: 'tr',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },
  {
    asr: 'deepgram',
    languageName: 'Ukrainian',
    languageCode: 'uk',
    priority: 4,
    params: { tier: 'base', model: 'general' },
  },

  /**
   * Deepgram whisper.
   */
  {
    asr: 'deepgram',
    languageName: 'Afrikaans',
    languageCode: 'af',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Arabic',
    languageCode: 'ar',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Armenian',
    languageCode: 'hy',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Azerbaijani',
    languageCode: 'az',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Belarusian',
    languageCode: 'be',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Bosnian',
    languageCode: 'bs',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Bulgarian',
    languageCode: 'bg',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Catalan',
    languageCode: 'ca',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Chinese',
    languageCode: 'zh',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Croatian',
    languageCode: 'hr',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Czech',
    languageCode: 'cs',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Danish',
    languageCode: 'da',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Dutch',
    languageCode: 'nl',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'English',
    languageCode: 'en',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Estonian',
    languageCode: 'et',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Finnish',
    languageCode: 'fi',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'French',
    languageCode: 'fr',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Galician',
    languageCode: 'gl',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'German',
    languageCode: 'de',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Greek',
    languageCode: 'el',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Hebrew',
    languageCode: 'he',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Hindi',
    languageCode: 'hi',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Hungarian',
    languageCode: 'hu',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Icelandic',
    languageCode: 'is',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Indonesian',
    languageCode: 'id',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Italian',
    languageCode: 'it',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Japanese',
    languageCode: 'ja',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Kannada',
    languageCode: 'kn',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Kazakh',
    languageCode: 'kk',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Korean',
    languageCode: 'ko',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Latvian',
    languageCode: 'lv',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Lithuanian',
    languageCode: 'lt',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Macedonian',
    languageCode: 'mk',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Malay',
    languageCode: 'ms',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Marathi',
    languageCode: 'mr',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Maori',
    languageCode: 'mi',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Nepali',
    languageCode: 'ne',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Norwegian',
    languageCode: 'no',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Persian',
    languageCode: 'fa',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Polish',
    languageCode: 'pl',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Portuguese',
    languageCode: 'pt',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Romanian',
    languageCode: 'ro',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Russian',
    languageCode: 'ru',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Serbian',
    languageCode: 'sr',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Slovak',
    languageCode: 'sk',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Slovenian',
    languageCode: 'sl',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Spanish',
    languageCode: 'es',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Swahili',
    languageCode: 'sw',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Swedish',
    languageCode: 'sv',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Tagalog',
    languageCode: 'tl',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Tamil',
    languageCode: 'ta',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Thai',
    languageCode: 'th',
    priority: 3,
    params: { model: 'whisper-large' },
  },
  {
    asr: 'deepgram',
    languageName: 'Turkish',
    languageCode: 'tr',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Ukrainian',
    languageCode: 'uk',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Urdu',
    languageCode: 'ur',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Vietnamese',
    languageCode: 'vi',
    priority: 3,
    params: { model: 'whisper' },
  },
  {
    asr: 'deepgram',
    languageName: 'Welsh',
    languageCode: 'cy',
    priority: 3,
    params: { model: 'whisper' },
  },
];

export const mapLanguageCode = (languageName: string, asr?: string) => {
  const specificLanguages = asrLanguageTable.filter(
    (asrLanguage) =>
      asrLanguage.languageName.toLowerCase() === languageName.toLowerCase() &&
      (!asr || asrLanguage.asr === asr),
  );

  if (specificLanguages.length > 0) {
    const highestPriority = Math.min(
      ...specificLanguages.map((asrLanguage) => asrLanguage.priority),
    );
    const bestLanguage = specificLanguages.find(
      (asrLanguage) => asrLanguage.priority === highestPriority,
    );

    if (bestLanguage) {
      return {
        asr: bestLanguage.asr,
        languageCode: bestLanguage.languageCode,
        params: bestLanguage.params,
      };
    }
  }

  return {
    asr: 'deepgram',
    languageCode: 'auto',
    params: { model: 'whisper' },
  };
};
