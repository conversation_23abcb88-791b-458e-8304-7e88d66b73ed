import { ApiTags } from '@nestjs/swagger';
import { <PERSON>, Post, Req, Re<PERSON>, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ASRService } from 'src/asr';

@ApiTags('Assembly Controller')
@Controller('assembly-cb')
export class AssemblyController {
  private readonly logger = new Logger(AssemblyController.name);
  constructor(private readonly asrService: ASRService) {}
  @Post('/:id')
  async assemblyController(@Req() req: Request, @Res() res: Response) {
    this.logger.debug(`Received webhook for ${req.params.id}`);
    try {
      const results = await this.asrService.webhookHandler(req);
      this.logger.debug(`Webhook results for ${req.params.id}`, results);

      res.sendStatus(200).end();
    } catch (error) {
      this.logger.error(`Error processing webhook for ${req.params.id}`, error?.message);
      res.status(400).send(error?.message);
    }
  }
}
