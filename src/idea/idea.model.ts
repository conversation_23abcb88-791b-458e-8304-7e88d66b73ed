import { BaseModel } from '@/resources/base/base.model';
import { IdeaStatus } from './idea.interface';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  timestamps: true,
  versionKey: false,
})
export class Idea extends BaseModel {
  @Prop({ type: String })
  bid: string;

  @Prop({ type: String })
  uid: string;

  @Prop({ type: String })
  projectId: string;

  @Prop({ type: String })
  rechercheId: string;

  @Prop({ type: String })
  prompt: string;

  @Prop({ type: [String] })
  seoKeywords?: string[];

  @Prop({ type: String })
  title: string;

  @Prop({ type: String, enum: IdeaStatus, default: IdeaStatus.GENERATED })
  status: IdeaStatus;
}

export type IdeaDocument = Idea & Document;

export const IdeaSchema = SchemaFactory.createForClass(Idea);

// indexes
IdeaSchema.index({ bid: 1 });
IdeaSchema.index({ uid: 1 });
IdeaSchema.index({ projectId: 1 });
IdeaSchema.index({ rechercheId: 1 });
