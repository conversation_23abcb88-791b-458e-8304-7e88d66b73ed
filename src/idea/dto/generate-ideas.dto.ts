import { <PERSON><PERSON><PERSON>y, IsNotEmpty, <PERSON>Optional, IsString } from 'class-validator';

export class GenerateIdeasDto {
  @IsOptional()
  @IsString()
  bid!: string;

  @IsOptional()
  @IsString()
  uid!: string;

  @IsNotEmpty()
  @IsString()
  prompt: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  seoKeywords?: string[];

  @IsOptional()
  @IsString()
  projectId?: string;
}
