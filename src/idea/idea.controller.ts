import type { WebsiteMetadata } from './idea.service';

import {
  InternalServerErrorException,
  BadRequestException,
  Controller,
  Logger,
  Body,
  Post,
  Req,
  UseGuards,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ScrapeUrlDto } from './dto/scrape-url.dto';
import { IdeaService } from './idea.service';
import { GenerateIdeasDto } from './dto/generate-ideas.dto';
import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { Auth } from '@/auth/guards/auth.guard';
import { RolesGuard } from '@/auth/guards/roles.guard';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { SaveIdeasDto } from './dto/save-ideas.dto';
import { Idea } from './idea.model';
import { GetIdeasDto } from './dto/get-ideas.dto';

@ApiTags('ideas')
@Controller('ideas')
export class IdeaController {
  private readonly logger = new Logger(IdeaController.name);

  constructor(private readonly ideaService: IdeaService) {}

  @Post('scrape')
  @ApiOperation({ summary: 'Scrape a website for metadata' })
  @ApiResponse({
    status: 200,
    description: 'Website metadata successfully retrieved',
    type: Object,
  })
  @ApiResponse({ status: 400, description: 'Invalid URL or request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async scrapeWebsite(@Body() scrapeUrlDto: ScrapeUrlDto): Promise<WebsiteMetadata> {
    try {
      this.logger.debug(`Received request to scrape URL: ${scrapeUrlDto.url}`);
      return await this.ideaService.scrapeWebsite(scrapeUrlDto.url);
    } catch (error) {
      this.logger.error(`Error scraping website: ${error.message}`, error.stack);

      if (error.message.includes('Invalid URL') || error.name === 'TypeError') {
        throw new BadRequestException('Invalid URL provided');
      }

      throw new InternalServerErrorException(`Failed to scrape website: ${error.message}`);
    }
  }

  @Get()
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async getIdeas(
    @Req() { bid, uid }: AuthenticatedRequest,
    @Query() filters: GetIdeasDto,
  ): Promise<Idea[]> {
    return this.ideaService.getIdeas({ ...filters, bid, uid });
  }

  @Post('generate')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async generateIdeas(
    @Body() body: GenerateIdeasDto,
    @Req() { bid, uid }: AuthenticatedRequest,
  ): Promise<Idea[]> {
    return this.ideaService.generateIdeas({ ...body, bid, uid });
  }

  @Post('save')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async saveIdeas(
    @Body() body: SaveIdeasDto,
    @Req() { bid, uid }: AuthenticatedRequest,
  ): Promise<{ success: boolean }> {
    return this.ideaService.saveIdeas({ ...body, bid, uid });
  }

  @Delete()
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async deleteIdeas(@Body('ids') ids: string[]): Promise<{ success: boolean }> {
    return this.ideaService.deleteIdeas(ids);
  }
}
