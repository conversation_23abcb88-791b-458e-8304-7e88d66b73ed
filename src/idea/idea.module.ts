import { Modu<PERSON> } from '@nestjs/common';

import { BlogModule } from '@/blog/blog.module';

import { IdeaController } from './idea.controller';
import { IdeaService } from './idea.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Idea, IdeaSchema } from './idea.model';

@Module({
  imports: [BlogModule, MongooseModule.forFeature([{ name: Idea.name, schema: IdeaSchema }])],
  controllers: [IdeaController],
  providers: [IdeaService],
  exports: [IdeaService],
})
export class IdeaModule {}
