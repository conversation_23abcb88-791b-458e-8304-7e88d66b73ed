import { Injectable, Logger } from '@nestjs/common';
import { load } from 'cheerio';

import { pruneWhiteSpace, extract } from '@/common/utils/parse';
import { ScraperService } from '@/blog/scraper.service';
import { GenerateIdeasDto } from './dto/generate-ideas.dto';
import { SaveIdeasDto } from './dto/save-ideas.dto';
import { Idea } from './idea.model';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { IdeaStatus } from './idea.interface';
import { GetIdeasDto } from './dto/get-ideas.dto';

export interface WebsiteMetadata {
  title: string;
  description: string;
  keywords: string[];
  headings: {
    h1: string[];
    h2: string[];
    h3: string[];
  };
  content: string;
  url: string;
}

@Injectable()
export class IdeaService {
  private readonly logger = new Logger(IdeaService.name);

  constructor(
    @InjectModel(Idea.name) private readonly ideaModel: Model<Idea>,
    private readonly scraperService: ScraperService,
  ) {}

  async scrapeWebsite(urlString: string): Promise<WebsiteMetadata> {
    try {
      this.logger.debug(`Scraping website: ${urlString}`);

      // Create URL object for validation
      const url = new URL(urlString);

      // Fetch HTML content using the existing ScraperService
      const html = await this.scraperService.fetchHTML(url);

      // Parse the HTML to extract metadata
      return this.extractMetadata(html, urlString);
    } catch (error) {
      this.logger.error(`Error scraping website ${urlString}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getIdeas(filters: GetIdeasDto & { bid: string; uid: string }): Promise<Idea[]> {
    return this.ideaModel.find(filters).sort({ createdAt: -1 }).exec();
  }

  async generateIdeas(dto: GenerateIdeasDto): Promise<Idea[]> {
    const generatedTitles = await this.generateIdeasWithAI(dto.prompt);
    const rechercheId = this.generaterechercheId();

    const ideasToCreate = generatedTitles.map((title) => ({
      ...dto,
      projectId: dto.projectId || null,
      rechercheId: rechercheId,
      title,
    }));
    return await this.ideaModel.insertMany(ideasToCreate);
  }

  async saveIdeas(dto: SaveIdeasDto): Promise<{ success: boolean }> {
    const result = await this.ideaModel.updateMany(
      { _id: { $in: dto.ideaIds } },
      { $set: { status: IdeaStatus.SAVED, projectId: dto.projectId } },
    );
    return {
      success: result.acknowledged,
    };
  }

  async deleteIdeas(ids: string[]): Promise<{ success: boolean }> {
    const result = await this.ideaModel.deleteMany({ _id: { $in: ids } });
    return {
      success: result.acknowledged,
    };
  }

  async generateIdeasWithAI(prompt: string): Promise<string[]> {
    const ideaTitles = [
      `How ${prompt} is changing the industry`,
      `The complete guide to ${prompt}`,
      `5 ways to leverage ${prompt} for growth`,
      `${prompt}: What no one is talking about`,
      `Why ${prompt} matters more than you think`,
    ];
    return ideaTitles;
  }

  private extractMetadata(html: string, urlString: string): WebsiteMetadata {
    try {
      const $ = load(html);

      // Extract basic metadata
      const title = extract.pageTitle($);
      const description = extract.metaDescription($);

      // Extract keywords from meta tags
      const keywordsContent = $('meta[name="keywords"]').attr('content') || '';
      const keywords = keywordsContent
        ? keywordsContent.split(',').map((keyword) => keyword.trim())
        : [];

      // Extract headings
      const h1 = extract.tagText($, 'h1');
      const h2 = extract.tagText($, 'h2');
      const h3 = extract.tagText($, 'h3');

      // Extract main content
      const content = pruneWhiteSpace(extract.bodyText($));

      return {
        title,
        description,
        keywords,
        headings: {
          h1,
          h2,
          h3,
        },
        content,
        url: urlString,
      };
    } catch (error) {
      this.logger.error(`Error extracting metadata: ${error.message}`, error.stack);
      throw new Error(`Failed to extract metadata: ${error.message}`);
    }
  }

  private generaterechercheId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
  }
}
