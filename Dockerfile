# syntax=docker/dockerfile:1

FROM --platform=$BUILDPLATFORM node:lts AS builder

# These ARGs are automatically set by buildx
ARG BUILDPLATFORM    # Platform where the build is running
ARG TARGETPLATFORM   # Platform for the target image
ARG TARGETOS         # OS for the target image
ARG TARGETARCH       # Architecture for the target image

ENV NODE_OPTIONS=--max_old_space_size=4096
ENV TZ="UTC"
ARG SENTRY_AUTH_TOKEN
ENV SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# Skip prepare script entirely
ENV SKIP_PREPARE=true

WORKDIR /usr/src/app

COPY package.json pnpm-lock.yaml ./

# Install dependencies required for Puppeteer and native modules
RUN apt-get update && apt-get install -y \
    antiword \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    python3 \
    python3-pip \
    build-essential \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm and dependencies
RUN npm install -g corepack@latest --force && \
    corepack enable && \
    corepack prepare pnpm@latest --activate

# Install all dependencies for build
RUN pnpm install --frozen-lockfile --ignore-scripts

COPY . .

RUN pnpm build

# Production stage
FROM --platform=$TARGETPLATFORM node:lts

WORKDIR /usr/src/app

# Skip prepare script entirely
ENV SKIP_PREPARE=true
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Install build essentials in the final stage
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    antiword \
    fonts-liberation \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    wget \
    gnupg \
    ca-certificates \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Copy the package manifest for production dependencies
COPY --from=builder /usr/src/app/package.json ./package.json
COPY --from=builder /usr/src/app/pnpm-lock.yaml ./pnpm-lock.yaml

# Copy files from builder
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/src/components/email-templates ./src/components/email-templates

# Install only production dependencies and rebuild bcrypt
RUN npm install -g corepack@latest --force && \
    corepack enable && \
    corepack prepare pnpm@latest --activate && \
    pnpm install --prod --frozen-lockfile --ignore-scripts && \
    cd node_modules/bcrypt && \
    npm rebuild bcrypt --build-from-source

CMD [ "node", "dist/main.js" ]
