{"version": "0.2.0", "configurations": [{"name": "Debug Server", "skipFiles": ["<node_internals>/**"], "type": "node", "request": "launch", "cwd": "${workspaceRoot}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:debug"], "console": "integratedTerminal"}, {"name": "Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/jest/bin/jest", "args": ["--runInBand", "--watch<PERSON>ll", "${relativeFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "envFile": "${workspaceFolder}/.env"}, {"type": "node", "request": "attach", "name": "Attach to Process", "port": 9229, "restart": true, "sourceMaps": true, "cwd": "${workspaceFolder}"}]}