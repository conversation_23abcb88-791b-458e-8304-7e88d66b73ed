{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.formatOnSave": true, "eslint.format.enable": true, "eslint.workingDirectories": [{"mode": "auto"}], "typescript.tsdk": "node_modules/typescript/lib", "cSpell.words": ["blogify", "bowser", "Brevo", "dalle", "deepgram", "geoip", "mailchimp", "Openai", "REGENERABLE", "superadmin", "Tldr", "uuid", "websockets", "wordpressorg"], "js/ts.implicitProjectConfig.target": "ES2022", "thunder-client.httpLibrary": "axios"}