{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never",
  },
  "editor.quickSuggestions": {
    "strings": true,
  },
  "eslint.workingDirectories": [{ "mode": "auto" }],
  "cSpell.words": [
    "Barchart",
    "Benzinga",
    "Blogify",
    "castbox",
    "Digidip",
    "embla",
    "Flickr",
    "Minyanville",
    "nextjs",
    "Pinterest",
    "Podbean",
    "Podchaser",
    "Promocodes",
    "Quora",
    "Signup",
    "Skimlinks",
    "Soundcloud",
    "Sovrn",
    "Starkville",
    "superadmin",
    "Swiper",
    "tailwindcss",
    "Tiktok",
    "Tldr",
    "Trustpilot",
    "Unoptimized",
    "Wickfire",
    "Yieldkit"
  ],
  "css.validate": false,
  "editor.inlineSuggest.enabled": true,
  "search.exclude": {
    "**/*.next/": true,
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "xmlTools.splitAttributesOnFormat": true,
  "xmlTools.enforcePrettySelfClosingTagOnFormat": true,
  "tailwindCSS.classAttributes": ["class", "className", "containerClassName"],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["Variants \\=([^;]*);", "\"([^\"]*)\""]
  ],
  "tailwindCSS.experimental.configFile": {
    "apps/blogify-web/tailwind.config.ts": "apps/blogify-web/**",
    "packages/ui/tailwind.config.ts": "packages/ui/**"
  },
}
