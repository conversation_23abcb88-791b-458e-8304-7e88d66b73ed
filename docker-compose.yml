version: '3.7'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
      - SERVICE_URL=${SERVICE_URL}
      - MONGO_URL=${MONGO_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ASSEMBLY_TOKEN=${ASSEMBLY_TOKEN}
    container_name: blogify
    ports:
      - '7777:7777'
    networks:
      - 'app-network'
    depends_on:
      - db
    environment:
      - NODE_ENV=development
      - PORT=7777
      - MONGO_URL=mongodb://db:27017/blogify
      - SERVICE_URL=${SERVICE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ASSEMBLY_TOKEN=${ASSEMBLY_TOKEN}

  db:
    image: mongo:4.2.8
    restart: unless-stopped
    volumes:
      - mongodb-data:/data/db
    ports:
      - '27014:27017'
    networks:
      - 'app-network'

networks:
  app-network:
    driver: bridge

volumes:
  mongodb-data:
