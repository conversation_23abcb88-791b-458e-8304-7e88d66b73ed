// بسم الله الرحمن الرحيم
import { MongoClient, Db, Collection, Document, WithId } from 'mongodb';
import minimist from 'minimist';

// Define Integration type and Blog interface
type Integration = 'blogger' | 'wordpress' | 'wordpressorg' | 'medium' | 'mailchimp' | 'zapier';

interface Blog extends Document {
  bloggerLink?: string;
  bloggerPublishTime?: Date;
  wordpressLink?: string;
  wordpressPublishTime?: Date;
  wordpressorgLink?: string;
  wordpressorgPublishTime?: Date;
  mediumLink?: string;
  mediumPublishTime?: Date;
  mailchimpLink?: string;
  mailchimpPublishTime?: Date;
  zapierLink?: string;
  zapierPublishTime?: Date;
  publishResult?: {
    integration: Integration;
    outcome: { link: string; time: string };
  }[];
}

// Parse command-line arguments using minimist
const args = minimist(process.argv.slice(2), {
  string: ['db-url', 'db-name'],
  default: {
    'db-url': 'mongodb://localhost:27017',
  },
});

// Get MongoDB connection and blog collection
const getDatabaseAndCollection = async (
  client: MongoClient,
): Promise<{ database: Db; blogs: Collection<Blog> }> => {
  const database = client.db(args['db-name']);
  const blogs = database.collection<Blog>('blogs');
  return { database, blogs };
};

// Build the MongoDB query to fetch blogs with both link and time present
const buildFindQuery = () => {
  return {
    $or: [
      { bloggerLink: { $exists: true, $ne: '' }, bloggerPublishTime: { $exists: true } },
      { wordpressLink: { $exists: true, $ne: '' }, wordpressPublishTime: { $exists: true } },
      { wordpressorgLink: { $exists: true, $ne: '' }, wordpressorgPublishTime: { $exists: true } },
      { mediumLink: { $exists: true, $ne: '' }, mediumPublishTime: { $exists: true } },
      { mailchimpLink: { $exists: true, $ne: '' }, mailchimpPublishTime: { $exists: true } },
      { zapierLink: { $exists: true, $ne: '' }, zapierPublishTime: { $exists: true } },
    ],
  };
};

// Fetch blogs from the collection
const fetchBlogs = async (blogs: Collection<Blog>): Promise<WithId<Blog>[]> => {
  const cursor = blogs.find(buildFindQuery());
  return cursor.toArray();
};

// Map blog data into the publishResult format
const mapPublishResults = (blog: Blog) => {
  const integrations: Integration[] = [
    'blogger',
    'wordpress',
    'wordpressorg',
    'medium',
    'mailchimp',
    'zapier',
  ];

  return integrations
    .filter(
      (integration) =>
        blog[`${integration}Link` as keyof Blog] && blog[`${integration}PublishTime` as keyof Blog],
    )
    .map((integration) => ({
      integration: integration,
      outcome: {
        link: blog[`${integration}Link` as keyof Blog] as string,
        time:
          blog[`${integration}PublishTime` as keyof Blog]?.toISOString() ||
          new Date().toISOString(),
      },
    }));
};

// Update the blog with publish results
const updateBlog = async (blogs: Collection<Blog>, blog: WithId<Blog>, publishResult: any[]) => {
  if (publishResult.length > 0) {
    await blogs.updateOne({ _id: blog._id }, { $set: { publishResult } });
    return 1;
  }
  return 0;
};

// Migrate blogs by updating the publishResult field
const migrateBlogs = async (): Promise<void> => {
  const client = new MongoClient(args['db-url']);

  try {
    await client.connect();
    console.log('Connected to MongoDB');
    const { blogs } = await getDatabaseAndCollection(client);

    const blogsArray = await fetchBlogs(blogs);

    const results = await Promise.all(
      blogsArray.map(async (blog) => {
        const publishResult = mapPublishResults(blog);
        return updateBlog(blogs, blog, publishResult);
      }),
    );

    const migratedCount = results.reduce((acc, val) => acc + val, 0);
    console.log(`Migration completed. ${migratedCount} documents updated.`);
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
};

// Run the migration
migrateBlogs().catch(console.error);
