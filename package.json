{"name": "blogify", "version": "5", "description": "The core of blogify representing the main server", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "private": true, "license": "UNLICENSED", "scripts": {"generate-blogify-media-client": "openapi --input https://blogify-media-staging-fgrhqeafta-uc.a.run.app/openapi.json --output ./src/blogify-media/client --client axios --useOptions --useUnionTypes", "prebuild": "pnpm dlx rimraf dist", "build": "MAX_OLD_SPACE_SIZE=4096 nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "pnpm start:dev", "start": "env-cmd nest start", "start:dev": "env-cmd nest start --watch", "start:debug": "env-cmd nest start src --debug", "start:prod": "node dist/main", "start:repl": "env-cmd nest start --entryFile repl", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepush": "pnpm lint && pnpm build", "prepare": "[ -n \"$SKIP_PREPARE\" ] || husky", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org pixel-shadow-inc --project blogify-server ./dist && sentry-cli sourcemaps upload --org pixel-shadow-inc --project blogify-server ./dist", "migration:mailchimp": "env-cmd ts-node -r tsconfig-paths/register src/scripts/migrate-users-to-mailchimp.ts", "docker-build": "env-cmd -f .env docker buildx build --platform=linux/arm64 -t blogify-server --build-arg SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN --load .", "docker-run": "docker run -v temp:/job/temp/ --env-file ./.env -it blogify-server", "docker": "pnpm run docker-build && pnpm run docker-run"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.15", "@ai-sdk/google": "^1.1.20", "@ai-sdk/openai": "^1.2.1", "@ai-sdk/perplexity": "^1.1.8", "@anthropic-ai/sdk": "^0.21.0", "@bull-board/api": "^5.21.4", "@bull-board/express": "^5.21.4", "@bull-board/ui": "5.21.4", "@deepgram/sdk": "^1.20.0", "@distube/ytdl-core": "^4.16.10", "@getbrevo/brevo": "^2.1.1", "@google-cloud/recaptcha-enterprise": "^5.11.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@mailchimp/mailchimp_transactional": "^1.0.50", "@nestjs/bull": "^10.2.0", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.1", "@nestjs/platform-socket.io": "^10.4.1", "@nestjs/schedule": "^4.1.0", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.2.1", "@nestjs/websockets": "^10.4.1", "@octokit/rest": "^20.0.2", "@sentry/node": "^7.114.0", "@sentry/profiling-node": "^7.114.0", "@sinclair/typebox": "^0.32.30", "@slack/web-api": "^6.10.0", "@slack/webhook": "^6.1.0", "ai": "^4.3.16", "aws-sdk": "^2.1328.0", "axios": "^1.6.8", "basic-auth": "^2.0.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "bowser": "^2.11.0", "bull": "^4.12.2", "cache-manager": "^5.1.4", "cache-manager-redis-yet": "^4.1.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "connect-redis": "^8.1.0", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "email-templates": "^11.1.1", "express": "^4.21.2", "express-session": "^1.17.3", "form-data": "^4.0.0", "fp-ts": "^2.16.1", "geoip-lite": "^1.4.10", "googleapis": "114.0.0", "gpt-3-encoder": "^1.1.4", "html-to-docx": "^1.8.0", "ioredis": "^5.3.1", "iso-country-currency": "^0.7.2", "lodash": "^4.17.21", "mammoth": "^1.6.0", "minimalist": "^1.0.0", "moment": "^2.30.1", "mongodb": "^5.4.0", "mongoose": "^7.8.6", "multer": "^1.4.5-lts.1", "natural": "^6.5.0", "nest-schedule": "^0.6.4", "nestjs-pino": "^4.3.1", "node-cache": "^5.1.2", "node-html-parser": "^6.1.13", "nodemailer": "^6.10.0", "nunjucks": "^3.2.4", "openai": "^4.85.0", "outdent": "^0.8.0", "p-retry": "^4.6.2", "passport": "^0.6.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-oauth2": "^1.7.0", "pdf-parse": "^1.1.1", "pino": "^9.6.0", "pino-stackdriver": "^3.0.0", "preview-email": "^3.1.0", "puppeteer": "^22.15.0", "rate-limiter-flexible": "^5.0.5", "reflect-metadata": "^0.2.2", "rxjs": "^7.2.0", "sass": "^1.69.0", "sharp": "^0.33.5", "simple-statistics": "^7.8.7", "socket.io": "^4.8.1", "stripe": "^12.3.0", "textract": "^2.5.0", "tiktoken": "^1.0.15", "twitter-api-v2": "^1.14.2", "uuid": "^9.0.1", "web-push": "^3.6.7", "youtube-search-api": "^1.2.1", "youtube-transcript": "^1.1.0", "yup": "^1.3.3", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@nestjs/cli": "^10.4.4", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.1", "@sentry/cli": "^2.31.2", "@swc/cli": "^0.4.0", "@swc/core": "^1.7.11", "@types/basic-auth": "^1.1.8", "@types/bcrypt": "^5.0.0", "@types/body-parser": "^1.19.5", "@types/cheerio": "0.22.35", "@types/email-templates": "^10.0.2", "@types/express": "^4.17.13", "@types/express-session": "^1.18.0", "@types/gapi": "^0.0.47", "@types/geoip-lite": "^1.4.4", "@types/html-to-docx": "^1.8.0", "@types/jest": "28.1.8", "@types/lodash": "^4.17.16", "@types/mailchimp__mailchimp_marketing": "^3.0.17", "@types/mailchimp__mailchimp_transactional": "^1.0.6", "@types/multer": "^1.4.7", "@types/node": "^22.10.1", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.16", "@types/pdf-parse": "^1.1.4", "@types/supertest": "^2.0.11", "@types/textract": "^2.4.5", "@types/uuid": "^9.0.8", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "env-cmd": "^10.1.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tsdoc": "^0.4.0", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "28.1.3", "openapi-typescript-codegen": "^0.27.0", "pino-pretty": "^13.0.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "source-map-support": "^0.5.20", "supertest": "^7.0.0", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4", "webpack": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.test\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@10.11.0", "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@parcel/watcher", "@sentry/cli", "@sentry/profiling-node", "@swc/core", "aws-sdk", "bcrypt", "bufferutil", "msgpackr-extract", "nest<PERSON><PERSON>-pino", "protobufjs", "puppeteer", "sharp", "utf-8-validate"]}}