name: Linter

on:
  pull_request:
    branches:
      - develop

jobs:
  type-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@main

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Modules
        run: pnpm install

      - name: Run TypeScript Compiler
        run: NODE_OPTIONS=--max-old-space-size=8192 pnpm tsc

      - name: Run ESLint
        run: NODE_OPTIONS=--max-old-space-size=8192 pnpm lint
