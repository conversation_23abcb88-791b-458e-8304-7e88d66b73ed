name: Build and Deploy to GKE

on:
  push:
    branches:
      - main
      - develop

permissions:
  contents: read
  id-token: write

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}
  GKE_ZONE: ${{ secrets.GKE_ZONE }}
  IMAGE: ${{ secrets.IMAGE }}

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch all history for proper versioning
        lfs: true      # Include LFS files if any

    - name: List files for debugging
      run: |
        ls -la
        ls -la src/components/ || echo "No components directory"

    - name: Set environment variables
      run: |
        # Set ENV_SUFFIX first
        if [[ $GITHUB_REF == "refs/heads/main" ]]; then
          ENV_SUFFIX="production"
          API_HOST="api.blogify.ai"
        else
          ENV_SUFFIX="staging"
          API_HOST="testapi.blogify.ai"
        fi

        # Export for immediate use in this step
        export ENV_SUFFIX="$ENV_SUFFIX"

        # Set for future steps
        echo "ENV_SUFFIX=$ENV_SUFFIX" >> $GITHUB_ENV
        echo "NAMESPACE=$ENV_SUFFIX" >> $GITHUB_ENV
        echo "DEPLOYMENT_NAME=blogify-server-$ENV_SUFFIX" >> $GITHUB_ENV
        echo "INGRESS_NAME=blogify-server-$ENV_SUFFIX-ingress" >> $GITHUB_ENV
        echo "API_HOST=$API_HOST" >> $GITHUB_ENV
        echo "CERTIFICATE_NAME=blogify-$ENV_SUFFIX-managed-cert" >> $GITHUB_ENV
        echo "IMAGE_TAG=$(echo $GITHUB_SHA | cut -c1-7)" >> $GITHUB_ENV

        # Debug output
        echo "ENV_SUFFIX: $ENV_SUFFIX"
        echo "NAMESPACE: $NAMESPACE"
        echo "DEPLOYMENT_NAME: blogify-server-$ENV_SUFFIX"
        echo "INGRESS_NAME: blogify-server-$ENV_SUFFIX-ingress"
        echo "API_HOST: $API_HOST"
        echo "CERTIFICATE_NAME: blogify-$ENV_SUFFIX-managed-cert"
        echo "IMAGE_TAG: $(echo $GITHUB_SHA | cut -c1-7)"

    # Authentication
    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GCP_SA_KEY }}'

    # Setup gcloud CLI
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    # Enable GKE logging
    - name: Configure GKE logging
      run: |
        # Get cluster credentials first
        gcloud container clusters get-credentials $GKE_CLUSTER \
          --zone $GKE_ZONE \
          --project $PROJECT_ID

        # Try to enable APIs if not already enabled
        gcloud services enable logging.googleapis.com --quiet || echo "Logging API already enabled or permission denied"
        gcloud services enable monitoring.googleapis.com --quiet || echo "Monitoring API already enabled or permission denied"

        # Configure GKE logging with error handling
        gcloud container clusters update $GKE_CLUSTER \
          --zone $GKE_ZONE \
          --logging=SYSTEM,WORKLOAD \
          --monitoring=SYSTEM,WORKLOAD \
          --quiet || echo "Failed to update cluster logging settings"

        # Verify kubectl access
        kubectl cluster-info

    # Configure Docker for GCR
    - name: Configure Docker
      run: gcloud auth configure-docker gcr.io --quiet

    # Build and push the Docker image
    - name: Build and Push
      run: |
        docker buildx create --use
        docker buildx build \
          --platform=linux/amd64 \
          --build-arg NODE_ENV_ARG="${ENV_SUFFIX}" \
          --tag "gcr.io/$PROJECT_ID/$IMAGE-$ENV_SUFFIX:$IMAGE_TAG" \
          --tag "gcr.io/$PROJECT_ID/$IMAGE-$ENV_SUFFIX:latest" \
          --build-arg GITHUB_SHA="$GITHUB_SHA" \
          --build-arg GITHUB_REF="$GITHUB_REF" \
          --build-arg SENTRY_AUTH_TOKEN="${{ secrets.SENTRY_AUTH_TOKEN }}" \
          --progress=plain \
          --push \
          .

    - name: Debug Docker build
      if: failure()
      run: |
        docker buildx build \
          --platform=linux/amd64 \
          --build-arg NODE_ENV_ARG="${ENV_SUFFIX}" \
          --progress=plain \
          --load \
          . 2>&1 | tee build.log

    # Get GKE credentials
    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials $GKE_CLUSTER \
          --zone $GKE_ZONE \
          --project $PROJECT_ID

    # Create namespace if it doesn't exist
    - name: Create namespace
      run: |
        kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

    # Create image pull secret
    - name: Create image pull secret
      run: |
        echo "Creating image pull secret in namespace: $NAMESPACE"
        kubectl delete secret gcr-json-key -n $NAMESPACE --ignore-not-found
        kubectl create secret docker-registry gcr-json-key \
          --docker-server=gcr.io \
          --docker-username=_json_key \
          --docker-password='${{ secrets.GCP_SA_KEY }}' \
          --docker-email=<EMAIL> \
          --namespace $NAMESPACE

    # Patch service account
    - name: Patch service account
      run: |
        kubectl patch serviceaccount default -n $NAMESPACE \
          -p '{"imagePullSecrets": [{"name": "gcr-json-key"}]}' \
          --type=merge

    # Add a verification step before deployment
    - name: Verify variables
      run: |
        echo "Verifying deployment variables:"
        echo "NAMESPACE: $NAMESPACE"
        echo "DEPLOYMENT_NAME: $DEPLOYMENT_NAME"
        echo "ENV_SUFFIX: $ENV_SUFFIX"
        echo "IMAGE_TAG: $IMAGE_TAG"

        if [ -z "$DEPLOYMENT_NAME" ]; then
          echo "Error: DEPLOYMENT_NAME is empty"
          exit 1
        fi

    # Set HPA replicas
    - name: Set HPA replicas
      run: |
        if [[ $GITHUB_REF == "refs/heads/main" ]]; then
          echo "MIN_REPLICAS=1" >> $GITHUB_ENV
          echo "MAX_REPLICAS=3" >> $GITHUB_ENV
          echo "CPU_UTILIZATION=70" >> $GITHUB_ENV
          echo "MEMORY_UTILIZATION=80" >> $GITHUB_ENV
          echo "SCALE_DOWN_WINDOW=300" >> $GITHUB_ENV
        else
          echo "MIN_REPLICAS=1" >> $GITHUB_ENV
          echo "MAX_REPLICAS=2" >> $GITHUB_ENV
          echo "CPU_UTILIZATION=60" >> $GITHUB_ENV
          echo "MEMORY_UTILIZATION=70" >> $GITHUB_ENV
          echo "SCALE_DOWN_WINDOW=180" >> $GITHUB_ENV
        fi

    # Deploy
    - name: Deploy
      run: |
        echo "Deploying to namespace: $NAMESPACE"
        echo "Using deployment name: $DEPLOYMENT_NAME"
        # Export variables for envsubst
        export PROJECT_ID="$PROJECT_ID"
        export IMAGE="$IMAGE"
        export ENV_SUFFIX="$ENV_SUFFIX"
        export IMAGE_TAG="$IMAGE_TAG"
        export DEPLOYMENT_NAME="$DEPLOYMENT_NAME"
        export MIN_REPLICAS="$MIN_REPLICAS"
        export MAX_REPLICAS="$MAX_REPLICAS"
        export CPU_UTILIZATION="$CPU_UTILIZATION"
        export MEMORY_UTILIZATION="$MEMORY_UTILIZATION"
        export SCALE_DOWN_WINDOW="$SCALE_DOWN_WINDOW"
        export GITHUB_SHA="$GITHUB_SHA"

        # Apply the deployment and HPA
        envsubst < k8s/deployment.yaml | kubectl apply -n $NAMESPACE -f -
        envsubst < k8s/hpa.yaml | kubectl apply -n $NAMESPACE -f -

        # Wait for rollout
        kubectl -n $NAMESPACE rollout status deployment/$DEPLOYMENT_NAME --timeout=900s

    # Debug deployment if failed
    - name: Debug deployment
      if: failure()
      run: |
        echo "Checking pod status..."
        kubectl get pods -n $NAMESPACE
        echo "Checking pod logs..."
        kubectl logs -n $NAMESPACE -l app=$DEPLOYMENT_NAME --tail=100 || true
        echo "Checking pod events..."
        kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp'

    # Deploy Cloudflare TLS secret
    - name: Deploy Cloudflare TLS Secret
      env:
        CLOUDFLARE_CERT: ${{ secrets.CLOUDFLARE_CERT }}
        CLOUDFLARE_PRIVATE_KEY: ${{ secrets.CLOUDFLARE_PRIVATE_KEY }}
      run: |
        export CLOUDFLARE_CERT_B64=$(echo "$CLOUDFLARE_CERT" | base64 | tr -d '\n')
        export CLOUDFLARE_PRIVATE_KEY_B64=$(echo "$CLOUDFLARE_PRIVATE_KEY" | base64 | tr -d '\n')
        envsubst < k8s/cloudflare-tls-secret.yaml | kubectl apply -f -

    # Deploy managed cert
    - name: Deploy Ingress Resource
      run: |
        envsubst < k8s/ingress.yaml | kubectl apply -n $NAMESPACE -f -
