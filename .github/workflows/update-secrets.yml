name: Update Kubernetes Secrets

on:
  workflow_dispatch:  # Only allow manual triggering

permissions:
  contents: read
  id-token: write

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}
  GKE_ZONE: ${{ secrets.GKE_ZONE }}

jobs:
  update-secrets:
    name: Update Kubernetes Secrets
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set environment variables
      run: |
        if [[ $GITHUB_REF == "refs/heads/main" ]]; then
          echo "ENV_SUFFIX=production" >> $GITHUB_ENV
          echo "NAMESPACE=production" >> $GITHUB_ENV
        else
          echo "ENV_SUFFIX=staging" >> $GITHUB_ENV
          echo "NAMESPACE=staging" >> $GITHUB_ENV
        fi

        echo "DEPLOYMENT_NAME=blogify-server-$ENV_SUFFIX" >> $GITHUB_ENV

        # Debug output
        echo "ENV_SUFFIX: $ENV_SUFFIX"
        echo "NAMESPACE: $NAMESPACE"
        echo "DEPLOYMENT_NAME: blogify-server-$ENV_SUFFIX"

    # Authentication
    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v2'
      with:
        credentials_json: '${{ secrets.GCP_SA_KEY }}'

    # Setup gcloud CLI
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    # Get GKE credentials
    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials $GKE_CLUSTER \
          --zone $GKE_ZONE \
          --project $PROJECT_ID

    # Create namespace if it doesn't exist
    - name: Create namespace
      run: |
        kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

    # Create application secrets from Infisical API
    - name: Create application secrets from Infisical API
      env:
        INFISICAL_CLIENT_ID: ${{ secrets.INFISICAL_CLIENT_ID }}
        INFISICAL_CLIENT_SECRET: ${{ secrets.INFISICAL_CLIENT_SECRET }}
        INFISICAL_PROJECT_ID: ${{ secrets.INFISICAL_PROJECT_ID }}
      run: |
        echo "Fetching secrets from Infisical API for environment: ${ENV_SUFFIX}..."
        echo "Using project ID (workspace ID): ${INFISICAL_PROJECT_ID}"

        # Get access token from Infisical
        TOKEN_RESPONSE=$(curl --silent --location --request POST 'https://app.infisical.com/api/v1/auth/universal-auth/login' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode "clientSecret=${INFISICAL_CLIENT_SECRET}" \
          --data-urlencode "clientId=${INFISICAL_CLIENT_ID}")

        # Extract access token
        ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.accessToken')

        if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
          echo "Failed to get access token from Infisical API."
          echo "Response: $TOKEN_RESPONSE"
          exit 1
        fi

        echo "Successfully obtained Infisical access token."

        # First, try to verify the workspace ID is valid
        echo "Verifying workspace access..."
        WORKSPACE_CHECK=$(curl --silent --location --request GET "https://app.infisical.com/api/v1/workspace" \
          --header "Authorization: Bearer ${ACCESS_TOKEN}")

        # Debug the response
        echo "Response format check:"
        echo "$WORKSPACE_CHECK" | head -c 100  # Show first 100 chars

        # Try to parse as JSON, gracefully handle errors
        if echo "$WORKSPACE_CHECK" | jq . > /dev/null 2>&1; then
          echo "Response is valid JSON"

          # Check if it's an array
          if echo "$WORKSPACE_CHECK" | jq 'if type=="array" then true else false end' | grep -q true; then
            echo "Response is an array of workspaces"
            echo "Available workspace IDs:"
            echo "$WORKSPACE_CHECK" | jq -r '.[] | try ._id // "No _id field found"'

            # Extract the first workspace ID if available
            FIRST_WORKSPACE_ID=$(echo "$WORKSPACE_CHECK" | jq -r '.[0] | try ._id // ""')
          else
            echo "Response is an object, not an array"
            echo "Available workspace details:"
            echo "$WORKSPACE_CHECK" | jq '.'

            # Try a different field that might contain workspace info
            if echo "$WORKSPACE_CHECK" | jq '.workspaces' > /dev/null 2>&1; then
              echo "Found 'workspaces' field in response"
              FIRST_WORKSPACE_ID=$(echo "$WORKSPACE_CHECK" | jq -r '.workspaces[0] | try ._id // ""')
            fi
          fi
        else
          echo "Response is not valid JSON:"
          echo "$WORKSPACE_CHECK"
          echo "Will proceed with provided workspace ID"
          FIRST_WORKSPACE_ID=""
        fi

        # Use provided workspace ID or fall back to discovered one
        WORKSPACE_ID="${INFISICAL_PROJECT_ID}"
        if [ -z "$WORKSPACE_ID" ] && [ -n "$FIRST_WORKSPACE_ID" ]; then
          echo "Using discovered workspace ID: ${FIRST_WORKSPACE_ID}"
          WORKSPACE_ID="${FIRST_WORKSPACE_ID}"
        fi

        if [ -z "$WORKSPACE_ID" ]; then
          echo "No workspace ID available. Cannot proceed."
          exit 1
        fi

        # Try directly with the workspace/project ID
        echo "Attempting to fetch secrets using workspace/project ID: ${WORKSPACE_ID}"

        # Try multiple API endpoints with debugging
        echo "Trying v3 API with workspaceId parameter..."
        SECRETS_URL="https://app.infisical.com/api/v3/secrets/raw?workspaceId=${WORKSPACE_ID}&environment=${ENV_SUFFIX}"
        echo "Request URL: $SECRETS_URL"

        SECRETS_RESPONSE=$(curl --silent --location --request GET "$SECRETS_URL" \
          --header "Authorization: Bearer ${ACCESS_TOKEN}")

        echo "Response preview:"
        echo "$SECRETS_RESPONSE" | head -c 100

        # Check if the response contains a secrets array
        if echo "$SECRETS_RESPONSE" | jq -e '.secrets' > /dev/null 2>&1; then
          echo "✅ Successfully retrieved secrets using v3 API with workspaceId"
        else
          echo "Trying alternate API endpoint with different parameter..."
          SECRETS_URL="https://app.infisical.com/api/v3/secrets?workspaceId=${WORKSPACE_ID}&environment=${ENV_SUFFIX}"
          echo "Request URL: $SECRETS_URL"

          SECRETS_RESPONSE=$(curl --silent --location --request GET "$SECRETS_URL" \
            --header "Authorization: Bearer ${ACCESS_TOKEN}")

          if echo "$SECRETS_RESPONSE" | jq -e '.secrets' > /dev/null 2>&1; then
            echo "✅ Successfully retrieved secrets from alternate endpoint"
          else
            echo "Failed to get secrets from Infisical API."
            echo "Final response: $SECRETS_RESPONSE"
            exit 1
          fi
        fi

        # Create a temporary file with all secrets
        echo "Creating environment file from secret values..."
        touch /tmp/app.env

        # Extract secrets, handling potential errors
        if ! echo "$SECRETS_RESPONSE" | jq -r '.secrets[] | try (.secretKey + "=" + .secretValue) // empty' > /tmp/app.env 2>/dev/null; then
          echo "Error extracting secrets with expected format. Trying alternate format..."

          # Try alternate format
          if ! echo "$SECRETS_RESPONSE" | jq -r '.secrets[] | try (.key + "=" + .value) // empty' > /tmp/app.env 2>/dev/null; then
            echo "Could not extract secrets in any expected format."
            echo "Manual creation of minimal secret..."
            echo "NODE_ENV=${ENV_SUFFIX}" > /tmp/app.env
          fi
        fi

        # Add some additional standard values if needed
        echo "NODE_ENV=${ENV_SUFFIX}" >> /tmp/app.env

        # Debug - count how many secrets we got (without showing values)
        SECRET_COUNT=$(cat /tmp/app.env | wc -l)
        echo "Retrieved $SECRET_COUNT environment variables (including duplicates)."

        # Remove duplicates from the file
        sort /tmp/app.env | uniq > /tmp/app.env.unique
        mv /tmp/app.env.unique /tmp/app.env

        SECRET_COUNT=$(cat /tmp/app.env | wc -l)
        echo "After removing duplicates: $SECRET_COUNT environment variables."

        # Delete existing secret if it exists
        kubectl delete secret app-env -n $NAMESPACE --ignore-not-found

        # Create the Kubernetes secret
        kubectl create secret generic app-env \
          --from-env-file=/tmp/app.env \
          --namespace=$NAMESPACE

        # Cleanup
        rm /tmp/app.env

        echo "✅ app-env secret created successfully in namespace ${NAMESPACE}."

    # Restart the deployment if necessary
    - name: Restart the deployment
      run: |
        # Check if deployment exists before attempting to restart
        if kubectl get deployment $DEPLOYMENT_NAME -n $NAMESPACE &> /dev/null; then
          echo "Restarting deployment to apply new secrets..."
          kubectl rollout restart deployment/$DEPLOYMENT_NAME -n $NAMESPACE

          echo "Waiting for rollout to complete..."
          kubectl rollout status deployment/$DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
          echo "✅ Deployment successfully restarted with new secrets."
        else
          echo "⚠️ Deployment $DEPLOYMENT_NAME does not exist in namespace $NAMESPACE."
          echo "Secrets have been updated, but no deployment was restarted."
        fi
