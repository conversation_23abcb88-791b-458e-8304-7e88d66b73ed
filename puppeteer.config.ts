import { platform } from 'os';

const executablePath = (() => {
  switch (platform()) {
    case 'darwin':
      return '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    case 'win32':
      return 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';
    default:
      return '/usr/bin/google-chrome';
  }
})();

export default {
  executablePath,
  chrome: {
    skipDownload: false,
  },
  firefox: {
    skipDownload: false,
  },
};
