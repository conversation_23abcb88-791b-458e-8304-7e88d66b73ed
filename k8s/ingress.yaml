apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${INGRESS_NAME}
  annotations:
    kubernetes.io/ingress.class: "gce"
spec:
  tls:
  - hosts:
    - ${API_HOST}
    secretName: cloudflare-tls-secret
  rules:
  - host: ${API_HOST}
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: ${DEPLOYMENT_NAME}-service
            port:
              number: 80
