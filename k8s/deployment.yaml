apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${DEPLOYMENT_NAME}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1         # Allow 1 extra pod during updates
      maxUnavailable: 0   # Maintain zero downtime
  selector:
    matchLabels:
      app: ${DEPLOYMENT_NAME}
  progressDeadlineSeconds: 900  # Increased from 600 to 900 (15 minutes)
  template:
    metadata:
      labels:
        app: ${DEPLOYMENT_NAME}
      annotations:
        timestamp: "${GITHUB_SHA}"  # Use Git SHA instead of date to ensure consistent deployments
        kubernetes.io/logging: '{"producer": "application", "format": "json"}'
    spec:
      terminationGracePeriodSeconds: 120  # Increased from 90 to 120 seconds
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: ${DEPLOYMENT_NAME}
      containers:
      - name: ${DEPLOYMENT_NAME}
        image: gcr.io/${PROJECT_ID}/${IMAGE}-${ENV_SUFFIX}:${IMAGE_TAG}
        ports:
        - containerPort: 7777
        envFrom:
        - secretRef:
            name: app-env
        env:
        - name: K8S_SERVICE_NAME
          value: ${DEPLOYMENT_NAME}-service
        - name: NODE_ENV
          value: ${ENV_SUFFIX}
        - name: GOOGLE_CLOUD_PROJECT
          value: ${PROJECT_ID}
        - name: K8S_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: K8S_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        resources:
          requests:
            cpu: "300m"
            memory: "500Mi"
            ephemeral-storage: "1Gi"
          limits:
            cpu: "800m"
            memory: "2Gi"
            ephemeral-storage: "1Gi"
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 7777
          initialDelaySeconds: 60  # Increased from 30 to 60 to allow for slower startup
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 7777
          initialDelaySeconds: 60  # Increased from 30 to 60 to allow for slower startup
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
                - /bin/sh
                - -c
                - |
                  # Graceful shutdown
                  echo "Pod is shutting down - initiating graceful termination"
                  sleep 15 # Wait for load balancer to remove pod from service
                  # Send SIGTERM to the main process and allow time for cleanup
                  kill -SIGTERM 1
                  # Wait for application to handle shutdown
                  sleep 5
---
apiVersion: v1
kind: Service
metadata:
  name: ${DEPLOYMENT_NAME}-service
spec:
  type: ClusterIP
  selector:
    app: ${DEPLOYMENT_NAME}
  ports:
    - protocol: TCP
      port: 80
      targetPort: 7777
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ${DEPLOYMENT_NAME}-pdb
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: ${DEPLOYMENT_NAME}