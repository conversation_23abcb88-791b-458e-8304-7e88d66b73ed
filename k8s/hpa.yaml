apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ${DEPLOYMENT_NAME}-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${DEPLOYMENT_NAME}
  minReplicas: ${MIN_REPLICAS}  # 1 for prod, 0 for staging
  maxReplicas: ${MAX_REPLICAS}  # 5 for prod, 3 for staging
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: ${CPU_UTILIZATION}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: ${MEMORY_UTILIZATION}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: ${SCALE_DOWN_WINDOW}
      policies:
      - type: Pods
        value: 1
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60    # 1 minute wait before scaling up
      policies:
      - type: Pods
        value: 2
        periodSeconds: 60
