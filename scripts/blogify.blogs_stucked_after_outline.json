[{"url": "https://www.youtube.com/watch?v=KHWA1MzZcts", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://wykop.pl/link/7643571/polska-z-limitem-na-chipy-usa-nie-umieja-uzasadnic-decyzji", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1919}, {"url": "https://worldwildschooling.com/12-hidden-us-beaches-you-might-not-know-about-but-are-definitely-worth-a-visit/?utm_source=flipboard&utm_content=travelers/magazine/The+Travel+Exchange", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2702, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://youtu.be/M2IUj8RHiKg?si=Rzs6OBpIMLF8aH8T", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "mini", "blogLanguage": "persian", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=6PGsn5hYWQw", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=feWxcZoIn8M", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2356}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1508, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=nC8RXyeSinE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=DSKcHxXcg5w", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=88CKi2i8t-M", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2343}, {"url": "https://www.youtube.com/watch?v=DSKcHxXcg5w", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=i-eNmbq92yg", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 3929}, {"url": "https://www.youtube.com/watch?v=kKC7hL5s2Lw", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=qnkQ4NoQRNQ", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2128}, {"url": "https://www.youtube.com/watch?v=f3Fw-z3AylA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2179}, {"url": "https://www.youtube.com/watch?v=QYGA5nTTRSA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2222}, {"url": "https://www.youtube.com/watch?v=f7zrKnm_iiQ", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 4313}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fvideos%2F1738784497064_Spotify%20not%20the%20goal.mp4", "sourceType": "video", "sourceName": "Video File", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1747}, {"url": "https://www.einfachmalene.de/kirsch-schmand-kuchen/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "german", "tableOption": "Table for Each Section", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1927, "sourceSimilarity": 85, "seoOptimization": false}, {"url": "https://youtu.be/5XnEcm5ZOTw?si=QUtPUaqxLSGAXmMp", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.bild.de/leben-wissen/medizin/omega-3-fettsaeuren-koennen-jung-halten-67a223b5332d6d76e2cf8dcb?t_ref=https%3A%2F%2Fm.bild.de%2F", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "german", "tableOption": "Table for Each Section", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1198}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1707, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1599}, {"url": "https://seaga.com/products/healthcare/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1503, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1498}, {"url": "https://youtu.be/s2oql936g94", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 4561}, {"url": "https://www.spain.info/es/top/catedral-sevilla-curiosidades/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.aei.org/wp-content/uploads/2025/01/Rebalancing-Trade-with-China.pdf?x85095", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.aei.org/wp-content/uploads/2025/01/Rebalancing-Trade-with-China.pdf?x85095", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.aei.org/wp-content/uploads/2025/01/Rebalancing-Trade-with-China.pdf?x85095", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1548}, {"url": "https://www.aei.org/wp-content/uploads/2025/01/Rebalancing-Trade-with-China.pdf?x85095", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=Iy__R2cWrrI", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://m.youtube.com/watch?v=BgrQ16r84pM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2223}, {"url": "https://youtu.be/qf2EGq4X4NE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 60, "seoOptimization": false, "wordCount": 2089}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738765945704_Marketing%20Google%20Maps%20%3A%20Guide%20ultime%202025%20pour%20dominer%20la%20recherche%20locale.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 20, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=FNgWjRHh5j8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 4339}, {"url": "https://youtu.be/YpHdG8N9mSg", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "medium", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2507, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2327}, {"url": "https://appsumo.com/products/redirhub/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://www.crystalvaults.com/crystal-encyclopedia/moss-agate/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "large", "blogLanguage": "swedish", "tableOption": "No Table", "chartOption": "One Chart", "seoOption": "ACTIVE", "wordCountApprox": 3005, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://wykop.pl/link/7642615/ponad-40-postow-na-facebooku-jest-prawdopodobnie-generowane-przez-ai", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1758}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://topdealtech.com/boardroom", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 70, "seoOptimization": false}, {"url": "https://appsumo.com/products/eachlabs/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1961}, {"url": "https://www.lovingessentialoils.com/a/blog/essential-oils-for-romance", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "How-to", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2498, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2553}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "Two Charts", "seoOption": "ACTIVE", "wordCountApprox": 1507, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1341}, {"url": "https://calculator.dev/beauty/oil-dilution/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2508, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738718373288_Drug_mule_for_love.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3531, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3699}, {"url": "https://en.wikipedia.org/wiki/Gneiss", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=f7zrKnm_iiQ", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=hhtTf2sZluo", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://seo-revolution.com/seo-tools/serp-overlap-tool/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738707576952_Marketing%20Plan%20Simplified%20-%20Toolstack.docx", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false}, {"url": "https://docs.google.com/document/d/125CgvaFfuqv1IEFCjyggnJTw4qNOplHvF6nTuPkKHyk/edit?usp=sharing", "sourceType": "document", "sourceName": "Google Docs", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://dontwastethecrumbs.com/natural-makeup-remover/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 20, "seoOptimization": false, "wordCount": 2373}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=caSeEd1jjFQ", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4524}, {"url": "https://www.youtube.com/watch?v=lqg6A5dhUhc", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4717}, {"url": "https://www.lovingessentialoils.com/a/blog/aromatherapy-bath", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "PASSIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=4LslSVaREnM&ab_channel=GoodTimesBadTimesPolska", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4320}, {"url": "https://www.youtube.com/watch?v=P4hGKsLaKfk&t=70s&ab_channel=<PERSON><PERSON><PERSON>", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=ZVFB2g25OkM&ab_channel=James<PERSON><PERSON><PERSON>", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4524}, {"url": "https://www.youtube.com/watch?v=EfAd55nmXM4", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=ALITavYXZl4", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=K_Ya3A_Xb_0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://youtu.be/H0YRniHh2tg?si=OoBA51IRVgua-dV9", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "medium", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2571, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://youtu.be/xwNRyKItpOk?si=U7xIAy9RiUnysRpE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2519, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://youtu.be/j6rBMkp9Rns?si=vFk_q72jA-xGa7P3", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3056, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.breitbart.com/2nd-amendment/2025/01/31/chicago-garbage-truck-driver-with-concealed-carry-license-kills-alleged-armed-robber/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2502, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2848}, {"url": "https://xaubot.com/how-to-use-ichimoku-kinko-hyo-in-forex/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1445, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.m4markets.com/about/news/sustainable-forex-trading-incorporating-esg-factors", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1437, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://free.com.tw/raphael-ai/#google_vignette", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "chinese traditional", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 53, "seoOptimization": false}, {"url": "https://www.youtube.com/watch?v=eJgcxYMhdoU", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4383, "sourceSimilarity": 84, "seoOptimization": false, "wordCount": 4318}, {"url": "https://en.wikipedia.org/wiki/Gneiss", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "polish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://wykop.pl/link/7642561/jak-panstwo-polskie-zabija-klase-srednia", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4469, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://wykop.pl/link/7642133/samochody-byd-zalaly-swiatowy-rynek-nie-mamy-s<PERSON><PERSON>-z-<PERSON>ami", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4414, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://zforex.com/blog/general-trading/top-20-trading-products-to-watch-in-2025/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1349, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://quantpedia.com/quantum-computing-as-the-means-to-algorithmic-trading/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1471, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://seattlemedium.com/quantum-computing-financial-sector/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1493, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.investopedia.com/decentralized-finance-defi-5113835", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1500, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://www.climatepolicyinitiative.org/publication/managing-currency-risk-to-catalyze-climate-finance/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1485, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1708}, {"url": "https://zforex.com/blog/general-trading/top-20-trading-products-to-watch-in-2025/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1474, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://seattlemedium.com/quantum-computing-financial-sector/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1496, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://wykop.pl/link/7642561/jak-panstwo-polskie-zabija-klase-srednia", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4434, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "vietnamese", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738652807042_Die%20Werbung%20des%20Kantons%20Wallis_%20Ein%20Erlebnisbericht.docx", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Humorous", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2029}, {"url": "https://vimeo.com/133896821", "sourceType": "video", "sourceName": "Vimeo", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2281}, {"url": "https://www.youtube.com/watch?v=EgMResmwGcw", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4309}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738644163404_Aus%20dem%20Bu%CC%88cher%20Beat%20Ambord.docx", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Educational", "blogSize": "x-large", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4482, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 4814}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738644163404_Aus%20dem%20Bu%CC%88cher%20Beat%20Ambord.docx", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Educational", "blogSize": "x-large", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4483, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 4431}, {"url": "https://www.youtube.com/watch?v=DU_Qmyje1LE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3748}, {"url": "https://x.com/ap_from_ny/status/1885901159900094826?s=46", "sourceType": "video", "sourceName": "X", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "bulgarian", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1505, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1248}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "One Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3274}, {"url": "https://www.youtube.com/watch?v=M6SbEpHtzKs", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4203}, {"url": "https://www.youtube.com/watch?v=J0QHeYP3V0A", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4280}, {"url": "https://www.afilicheck.com/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "portuguese", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3033}, {"url": "https://www.youtube.com/watch?v=kscWsaz6qJ0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3863}, {"url": "https://www.youtube.com/watch?v=aoTBguXAoOs", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4158}, {"url": "https://www.youtube.com/watch?v=rwFV4oRWXt8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4361}, {"url": "https://www.youtube.com/watch?v=BTplv8sLmzI", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4224}, {"url": "https://www.youtube.com/watch?v=7l9RHmbvqJ8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4346}, {"url": "https://www.youtube.com/watch?v=difYOI7nZYA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3871}, {"url": "https://www.youtube.com/watch?v=cgN7kVKNRGE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4238}, {"url": "https://www.youtube.com/watch?v=-l_L9Oy_TU8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4858}, {"url": "https://www.youtube.com/watch?v=Qvy9oAldcxY", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4611}, {"url": "https://www.youtube.com/watch?v=FvwlUhHFCbA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4076}, {"url": "https://www.youtube.com/watch?v=rNa1Hxt54cM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4719}, {"url": "https://www.youtube.com/watch?v=Q24bNM1AVmw", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3857}, {"url": "https://www.youtube.com/watch?v=QYP_q4wEXHE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4227}, {"url": "https://youtu.be/qEY488zCmTw?si=kTr260T1SGnMCTZJ", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3024, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3219}, {"url": "https://www.youtube.com/watch?v=3DPvo3f1R0k", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "american english", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4217}, {"url": "https://karmaguitaramps.com/machine-shop/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "italian", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1500, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 1642}, {"url": "https://youtu.be/m70bYDLa-DE?si=ljZ5zHQ_vJsgCVrY", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4439, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 4436}, {"url": "https://youtu.be/JAc2yc60ny0?si=oS0czrEewnDKlk2u", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3472, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 3501}, {"url": "https://youtu.be/rcV6CwoKwGg?si=WHz77twCmZQDJuix", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4367, "sourceSimilarity": 79, "seoOptimization": false, "wordCount": 4056}, {"url": "https://youtu.be/e_qc0IE3cTw?si=OskMZNCN2SE1ODe0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4428, "sourceSimilarity": 88, "seoOptimization": false, "wordCount": 3949}, {"url": "https://youtu.be/Y9SXLAmooHE?si=6CPHldhOuDkP6oAm", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4434, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 756}, {"url": "https://youtu.be/Bu-ZA1V8B04?si=G1ccs4TNdADZmiVF", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3395, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 3235}, {"url": "https://youtu.be/zNuovUKmvJM?si=7SUFsGzbLPEXvZyH", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3494, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 3295}, {"url": "https://youtu.be/QTE2zDaBTOo", "sourceType": "video", "sourceName": "Video Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1944}, {"url": "https://youtu.be/bCXpkVGQTWE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2506, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2776}, {"url": "https://youtu.be/RYtEUd9HQxk", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1796}, {"url": "https://youtu.be/AmUTNekC9EE?si=f-YN97xbYoPYK6CP", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4467, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 4410}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "Two Table", "chartOption": "Two Charts", "seoOption": "ACTIVE", "wordCountApprox": 4001, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 4175}, {"url": "https://youtu.be/JgxGqQrC6Ds?si=MLgDgi0cG6aOn-9g", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3489, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 3072}, {"url": "https://www.fr.de/ratgeber/gesundheit/formel-fuer-muskelaufbau-wichtige-aminosaeuren-und-ihre-vorkommen-93546003.html", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "german", "tableOption": "Two Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1580}, {"url": "https://youtu.be/xpsLXmntINE?si=7-vROivd-RKltyDp", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2986, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 2898}, {"url": "https://wethewild.us/blogs/plant-advice/4-myths-about-caring-for-orchids", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1791}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1799}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1676}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1552}, {"url": "https://www.youtube.com/watch?v=D3MZePBycnU", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2483, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 2559}, {"url": "https://www.youtube.com/watch?v=YcsYBh1_W8E&list=PLAlnT8XcpopZh1CDoZPM3h3-l_9v9unFS&index=13", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 95}, {"url": "https://www.youtube.com/watch?v=FSgl95BEmd0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3469}, {"url": "https://www.youtube.com/watch?v=FSgl95BEmd0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 30, "seoOptimization": false, "wordCount": 2251}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2211}, {"url": "https://www.kana.fr/top-15-des-citations-de-one-piece/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1381, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1442}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 40, "seoOptimization": false, "wordCount": 1952}, {"url": "https://www.youtube.com/watch?v=Do4DHHEExQ8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1985}, {"url": "https://www.kana.fr/top-15-des-citations-de-one-piece/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1381, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1707}, {"url": "https://www.youtube.com/watch?v=Do4DHHEExQ8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2120}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738597159666_Shadow%20Book%20test.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 2247}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2281, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2197}, {"url": "https://forgetmenotflowermarkets.com/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2384}, {"url": "https://forgetmenotflowermarkets.com/plant-care-tips/can-you-leave-a-plant-in-the-container-it-came-in/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2278}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2058}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2573}, {"url": "https://youtu.be/Vzaed7ddHfM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1798}, {"url": "https://www.youtube.com/watch?v=kE7UwSc_Z6s&t=704s", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2150}, {"url": "https://x.com/MarioNawfal/status/1845889306793038114", "sourceType": "video", "sourceName": "X", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2274}, {"url": "https://rumble.com/v5r00hq-barstool-pizza-review-funkys-pizza-oxford-ms.html", "sourceType": "video", "sourceName": "Rumble", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2235}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1929}, {"url": "https://youtu.be/0D_YZCrpI_s", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1774}, {"url": "https://vimeo.com/1052928734?share=copy", "sourceType": "video", "sourceName": "Vimeo", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1750}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738591910581_Untitled%20document.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "british english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1511, "sourceSimilarity": 69, "seoOptimization": false, "wordCount": 1527}, {"url": "https://wykop.pl/link/7641367/mezczyzni-sie-poddali-i-nikogo-to-nie-ob<PERSON><PERSON>zi", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4424}, {"url": "https://wykop.pl/link/7641423/w-popularnym-sprzecie-medycznym-wykryto-chinskiego-backdoora-pilne-zalecenie", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4483, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4346}, {"url": "https://wykop.pl/link/7641489/czy-chlopcy-i-mezczyzni-sa-w-polsce-dyskryminowani", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4550}, {"url": "https://www.automotivedive.com/news/Tesla-revenue-declines-Q4-2024-earnings-call-robotaxi-optimus/738786/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1939}, {"url": "https://es.wikipedia.org/wiki/Iglesia_de_Santa_Catalina_(Valencia)", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2040}, {"url": "https://youtu.be/gCrbHzMRbA0?si=l8ugZXMZJ0MYoIRj", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1982}, {"url": "https://youtu.be/2mGaVJhMmAA?si=aSTPBoUvsZhbXgqg", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1913}, {"url": "https://youtu.be/U2vF6qyzFAI?si=w9zboM8WRRn7FoEu", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1992}, {"url": "https://youtu.be/BNYxH-GB_ZY?si=e8RtWf4ZFZsoKjkk", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2080}, {"url": "https://youtu.be/U2vF6qyzFAI?si=MuVNMG60MmEPukuq", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2044}, {"url": "https://www.youtube.com/watch?v=Z2N5a7XZWg8", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2496, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2615}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Faudios%2F1738577605350_Systems%20Are%20Sexy.m4a", "sourceType": "audio", "sourceName": "Audio File", "generationMode": "auto", "blogTone": "How-to", "blogSize": "x-large", "blogLanguage": "vietnamese", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 6067}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1671}, {"url": "https://youtu.be/v-ZfXtvGx4Y", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1985}, {"url": "https://youtu.be/qMC8J4EiX4Y", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1836}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738561391085_Here%20are%20some%20talking%20points%20you%20can%20use%20for%20your%20talk%20on%20why%20people%20should%20not%20chase%20money_.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2133}, {"url": "https://ebook.wilfriedbechtle.com/kaltakquise-telefontraining", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1852}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "Two Table", "chartOption": "Two Charts", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3950}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1028, "sourceSimilarity": 65, "seoOptimization": false, "wordCount": 1527}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 968, "sourceSimilarity": 60, "seoOptimization": false, "wordCount": 1715}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1054, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 1981}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1065, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1782}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1038, "sourceSimilarity": 69, "seoOptimization": false, "wordCount": 1561}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1558}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 960, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1481}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 945, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1460}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1001, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1720}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1803}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 995, "sourceSimilarity": 66, "seoOptimization": false, "wordCount": 1589}, {"url": "https://podcasts.apple.com/kw/podcast/all-the-different-ways-your-life-could-have-turned-out/id1675310669?i=1000686684230", "sourceType": "audio", "sourceName": "Apple Podcasts", "generationMode": "auto", "blogTone": "Informative", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3017, "sourceSimilarity": 80, "seoOptimization": false, "wordCount": 3192}, {"url": "https://youtu.be/S31l-F7CkaA", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2132}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738503519528_2024.Q3%20GR%20Market%20Commentary.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1515, "sourceSimilarity": 40, "seoOptimization": false, "wordCount": 803}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738501179589_2024.Q3%20GR%20Market%20Commentary.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1481, "sourceSimilarity": 31, "seoOptimization": false, "wordCount": 0}, {"url": "https://www.bbc.com/news/articles/cn4z23kndlyo", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1448, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1507}, {"url": "https://www.bbc.com/future/article/20250131-what-does-deepseeks-new-app-mean-for-the-future-of-ai", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1456, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1385}, {"url": "https://www.bbc.com/news/articles/c20myx1erl6o", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1489, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1421}, {"url": "https://www.bbc.com/news/articles/c93qnk92174o", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1478, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1360}, {"url": "https://www.bbc.com/news/articles/cd9qzg92g72o", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1489, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1602}, {"url": "https://www.bbc.com/news/articles/c0lzxz7kg4wo", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1526, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1530}, {"url": "https://www.bbc.com/news/articles/c627nx42xelo", "sourceType": "webLink", "sourceName": "BBC", "generationMode": "auto", "blogTone": "News", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1474, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1520}, {"url": "https://sunnyvacation365.com/5-best-beaches-in-the-rijeka-area/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2210, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2091}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1936}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1869}, {"url": "https://docs.google.com/document/d/1mI-gH6XMwVl2ultUGcboFAW4oDf413qXCBgcFxyR4dM/edit?usp=sharing", "sourceType": "document", "sourceName": "Google Docs", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1518, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1593}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fvideos%2F1738468423435_shopeecoth-2025-2-2-0-0--Main%20Ads-d6b51683b41c144511dd9e90d5f0cffe.mp4", "sourceType": "video", "sourceName": "Video File", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "thai", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 174}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fvideos%2F1738468035803_shopeecoth-2025-2-2-0-0--Main%20Ads-cd2d11032784203f1d996f06c5bb0e96.mp4", "sourceType": "video", "sourceName": "Video File", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "thai", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 196}, {"url": "https://youtu.be/558B5M0YmQA?si=3ZRcuq1-MrpdyTAu", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1804}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1746}, {"url": "https://www.youtube.com/watch?v=g--9njNBqa8&ab_channel=TheRichDadChannel", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2189}, {"url": "https://www.youtube.com/watch?v=fM6Yte8-75s&ab_channel=GoMetawithOliSharpe", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2483}, {"url": "https://www.youtube.com/watch?v=aohCjoAx6ZA&ab_channel=ErikaTaughtMewithErikaK<PERSON>berg", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2257}, {"url": "https://www.youtube.com/watch?v=ntfCzbsW0_Q&ab_channel=TheRichDadChannel", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2115}, {"url": "https://www.youtube.com/watch?v=B4UroEdq7k0&ab_channel=TheRamseyShowHighlights", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2245}, {"url": "https://www.youtube.com/watch?v=ergbGuUdapU&ab_channel=TODAY", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1951}, {"url": "https://www.raklinger.de/pflichtteil/auskunftsanspruch-wertermittlungsanspruch/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "How-to", "blogSize": "medium", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2997, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 2331}, {"url": "https://www.youtube.com/watch?v=ScGF-GB6BqM&ab_channel=TECHtalk", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2254}, {"url": "https://www.youtube.com/watch?v=xUzzWUlSk98&ab_channel=<PERSON>B<PERSON>le", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1961}, {"url": "https://youtu.be/wbxfvd9nVkc?si=PBoQ_7h4agBU19fn", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1735}, {"url": "https://www.youtube.com/watch?v=d-VjcaRyo40", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "News", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4401, "sourceSimilarity": 13, "seoOptimization": false, "wordCount": 4372}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2109}, {"url": "https://youtu.be/IbPdC2Alv4Y?si=kbani6AkN_XMGm3w", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4258}, {"url": "https://www.automotivedive.com/news/automotive-industry-trends-outlook-2025/738954/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1923}, {"url": "https://www.youtube.com/watch?v=11A2BCt3QDA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "mini", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 790, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 908}, {"url": "https://appsumo.com/products/cmaps/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1792}, {"url": "https://appsumo.com/products/seo-generator/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1818}, {"url": "https://appsumo.com/products/writeseed-ai-content-writer/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1774}, {"url": "https://appsumo.com/products/buildfast/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1786}, {"url": "https://appsumo.com/products/unmixr-ai/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1859}, {"url": "https://appsumo.com/products/picmaker/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1729}, {"url": "https://appsumo.com/products/sendfox/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1708}, {"url": "https://appsumo.com/products/reelcraft/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1798}, {"url": "https://appsumo.com/products/tubeiq/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1950}, {"url": "https://appsumo.com/products/mimicpc/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1774}, {"url": "https://appsumo.com/products/projectbloom/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1853}, {"url": "https://youtu.be/jF9eiIA-ea8?si=3WjPgla9r0X5ATEq", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "medium", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2581, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2643}, {"url": "https://appsumo.com/products/lazybird/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1769}, {"url": "https://youtu.be/ZZpPaxamgHc?si=hgNahL1BpVjvAd6i", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "medium", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2990, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2751}, {"url": "https://appsumo.com/products/flipbooklets/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1740}, {"url": "https://appsumo.com/products/msgbubble/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1744}, {"url": "https://www.youtube.com/watch?v=cI2Hdh1paIs", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 787, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 631}, {"url": "https://youtu.be/MicrDm0yZj4", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1728}, {"url": "https://www.youtube.com/watch?v=TGWhfl9MJqc", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "mini", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 994, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2596}, {"url": "https://appsumo.com/products/documentero", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1771}, {"url": "https://appsumo.com/products/wp-login-lockdown", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1697}, {"url": "https://appsumo.com/products/contact-button", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1772}, {"url": "https://appsumo.com/products/tiny-talk", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1921}, {"url": "https://appsumo.com/products/edworking", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1770}, {"url": "https://appsumo.com/products/camelo", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1760}, {"url": "https://appsumo.com/products/invoiless", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1829}, {"url": "https://appsumo.com/products/support-board", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1822}, {"url": "https://appsumo.com/products/open-elms", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1874}, {"url": "https://appsumo.com/products/email-verifier-blacklist-shield-by-brandnav", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1723}, {"url": "https://appsumo.com/products/marketplace-ideabuddy/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1825}, {"url": "https://appsumo.com/products/updf/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1804}, {"url": "https://appsumo.com/products/wpautoblogcom/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1686}, {"url": "https://appsumo.com/products/insecureweb/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1826}, {"url": "https://appsumo.com/products/more-good-reviews", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1870}, {"url": "https://vimeo.com/1052509654?share=copy", "sourceType": "video", "sourceName": "Vimeo", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "german", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2173}, {"url": "https://appsumo.com/products/kingsumo/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1804}, {"url": "https://www.youtube.com/watch?v=JpN5AXzndGo", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "medium", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2994, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2776}, {"url": "https://appsumo.com/products/marketplace-siteguru/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1804}, {"url": "https://appsumo.com/products/vivomeetings/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1713}, {"url": "https://appsumo.com/products/charla", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1722}, {"url": "https://appsumo.com/products/speechactors/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1983}, {"url": "https://appsumo.com/products/bunnydoc", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2020}, {"url": "https://appsumo.com/products/sheerseo", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1815}, {"url": "https://appsumo.com/products/rankspro", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1865}, {"url": "https://appsumo.com/products/onlyprompts", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1851}, {"url": "https://appsumo.com/products/gobrunch", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1859}, {"url": "https://appsumo.com/products/triplo-ai", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1693}, {"url": "https://appsumo.com/products/reoon-email-verifier", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2006}, {"url": "https://appsumo.com/products/local-rank-tracker", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1688}, {"url": "https://appsumo.com/products/suitedash", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1773}, {"url": "https://appsumo.com/products/squirrly-seo", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1840}, {"url": "https://appsumo.com/products/wecp", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1901}, {"url": "https://appsumo.com/products/tu-latch", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1700}, {"url": "https://appsumo.com/products/tu-latch", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1868}, {"url": "https://appsumo.com/products/syncsignature", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1722}, {"url": "https://appsumo.com/products/adirectory", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1806}, {"url": "https://appsumo.com/products/cx-genie", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1913}, {"url": "https://appsumo.com/products/taja", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1701}, {"url": "https://appsumo.com/products/swipe-one", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1854}, {"url": "https://appsumo.com/products/liftos", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1881}, {"url": "https://www.entrepreneur.com/growing-a-business/im-an-seo-expert-here-are-6-content-tips-to-stand-out/483226", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "french", "tableOption": "A single table", "chartOption": "A single chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2024}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2118}, {"url": "https://youtu.be/upDOltxNRqQ?si=g3BYKaO5VBX7b1mS", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3091, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3093}, {"url": "https://m.youtube.com/watch?v=RbDBTOrdqyE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3046, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3077}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 527, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 536}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2113}, {"url": "https://swiy.co/TSMw", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2031}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "bengali", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1361}, {"url": "https://youtu.be/-CC7B-9xl-A?si=Iu2QtXkeYsWTLVYM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2641, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2831}, {"url": "https://www.youtube.com/watch?v=ggsOEVQZcMU", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "italian", "tableOption": "Table for Each Section", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 2053}, {"url": "https://www.youtube.com/watch?v=CjZ7omhOyQ0", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2475, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2748}, {"url": "https://www.youtube.com/watch?v=QCFb4BiDDcE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 1979}, {"url": "https://youtu.be/4tgobfVdMxQ?si=z6g9xJq4eNdrxdVK", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2396}, {"url": "https://youtu.be/cOW7pK_wn38?si=GZMgR14KnTon1TQu", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2177}, {"url": "https://youtu.be/Yud_4UtyQD4?si=HiJkZwVBbGJA_T-J", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2096}, {"url": "https://youtu.be/XSF9AOx7sdU?si=e12FirQzPijMKw2x", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2146}, {"url": "https://youtu.be/uz6rjbw0ZA0?si=45X55aoKbliVeyKE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1899}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1599}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1848}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "One Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1748}, {"url": "https://youtu.be/1wnZZmATL-o?si=p15ZAcrIOjRyRCxH", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2200}, {"url": "https://youtu.be/ZYqDVlpIDXs?si=olCcK5unFuE4OPK5", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2059}, {"url": "https://youtu.be/lYJGyWr1A6Y?si=VBRiPTTRy-G7isj9", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1838}, {"url": "https://youtu.be/5REjDcSILw4?si=fowji5MEmPIv01NC", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1812}, {"url": "https://youtu.be/iFkdOAqYA9o?si=hIenbo0FJn3wK_zC", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2043}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1718}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1965}, {"url": "https://youtu.be/oADaM2L1YLc?si=ZlvQYbA5eT6XgIIh", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2013}, {"url": "https://youtu.be/CMQp0bwjokw?si=mig-fws46mr3Z2iv", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1843}, {"url": "https://youtu.be/OiBQwlT2_cE?si=PbxsSFYc3bXBN4gW", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2007}, {"url": "https://youtu.be/fY5THLQ83pM?si=fssAPXJP_tE_Pktr", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1899}, {"url": "https://youtu.be/48rdiDt_PZs?si=3MMLxd3jsynqsKh_", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 21640}, {"url": "http://ocoursat.com/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Promotional", "blogSize": "small", "blogLanguage": "arabic", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 539}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "How-to", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2493, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2474}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1035, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1440}, {"url": "https://www.youtube.com/watch?v=nC3ApvVsxtc", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1335, "sourceSimilarity": 40, "seoOptimization": false, "wordCount": 1288}, {"url": "https://castbox.fm/vi/746103846", "sourceType": "audio", "sourceName": "CastBox", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4500, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 4661}, {"url": "https://www.truedata.in/blog/stock-market-fomo-fear-of-missing-out-and-how-to-overcome-it", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1511, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1596}, {"url": "https://blog.beacon.by/how-many-lead-magnets-should-you-create/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "How-to", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3006, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3205}, {"url": "https://youtu.be/748ijSufmZg?si=qLs-JNCJ0rSu17nT", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 650, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 637}, {"url": "https://youtu.be/748ijSufmZg?si=e_jXfmUSaV7E63dE", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2094}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738355569737_The%20Basics%20of%20a%20Small%20Business.docx", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Conversational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2157}, {"url": "https://www.automotivedive.com/news/evs-hybrid-vehicles-sales-growth-2025/738724/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1986}, {"url": "https://www.reddit.com/r/srilanka/comments/1iei29r/has_anyone_recently_gone_through_the_online/", "sourceType": "webLink", "sourceName": "Reddit", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 752, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 749}, {"url": "https://youtu.be/WkqI4XTYu-o?si=v-n8ogFJUmYZsjxU", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "One Table", "chartOption": "Two Charts", "seoOption": "ACTIVE", "wordCountApprox": 4450, "sourceSimilarity": 60, "seoOptimization": false, "wordCount": 4750}, {"url": "https://docs.google.com/document/d/1Ua3wu9W_AdNYvAT91nqlSobUOjiJNfOcCJcWMRQFlng/edit?usp=sharing", "sourceType": "document", "sourceName": "Google Docs", "generationMode": "auto", "blogTone": "How-to", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4464, "sourceSimilarity": 80, "seoOptimization": false, "wordCount": 3917}, {"url": "https://noloco.io/noloco-vs-softr", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Educational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3986, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3764}, {"url": "https://ileauxepices.com/epices/55-vanille-bourbon.html", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3684, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3703}, {"url": "https://appsumo.com/products/ranklytics/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2119}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-verts-parfumes/90-903-the-vert-des-amants-cerise-framboise-vanille.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2988, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3042}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-verts-parfumes/144-921-the-vert-des-3-dragons-fraise-pamplemousse-vanille.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2992, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2864}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-verts-parfumes/225-930-the-vert-soleil-de-bahia-passion-citron.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2907, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2773}, {"url": "https://www.abacai.fr/produit/the-vert-de-the-vert-de-l-amazone-figue-mandarine-aronia/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2955, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2671}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-noirs-parfumes/69-869-the-noir-soleil-du-bresil-passion-mangue.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2833, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2937}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738330642395_VAMP.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1674}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-noirs-parfumes/137-860-the-noir-jardin-d-aracaju.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2955, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2569}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes-noirs-parfumes/187-857-the-noir-carioca-gingembre-mangue-orange-saveur-bresil.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2929, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2907}, {"url": "https://lecomptoirdetoamasina.fr/fr/rooibos/194-rooibos-hiver-bresilien-anana<PERSON>-coco.html", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3006, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3023}, {"url": "https://youtu.be/Akk9RRQ5r9A", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3844}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738326555186_retraite%20cdsscsd.txt", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2132}, {"url": "https://youtu.be/wdmbVeFSxlo", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "italian", "tableOption": "One Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 79, "seoOptimization": false, "wordCount": 1912}, {"url": "https://www.youtube.com/watch?v=SpykbY7Cv34", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "italian", "tableOption": "One Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 80, "seoOptimization": false, "wordCount": 1802}, {"url": "https://youtu.be/kJOqIaGwQ7Y?si=kutr82itqOTxCRIm", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2077}, {"url": "https://youtu.be/vFXwQSuY_gw?si=ClZuO1nWKto0_7hx", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2222}, {"url": "https://youtu.be/P2ECl-mLmvY?si=CJDtUtHaUOtHakBn", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2022}, {"url": "https://youtu.be/tmgfpdpQ5sk?si=MNKxWJ5hzxXiWIkS", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1834}, {"url": "https://youtu.be/4FanBYNGS6U?si=UPzSsY6elGJ09R04", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2081}, {"url": "https://youtu.be/lehqadPYvxo?si=BA_tYRPOdn9k-L1V", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2054}, {"url": "https://youtu.be/neQ7D90R0xo?si=BDlnAbRPK3xRyPZP", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2110}, {"url": "https://youtu.be/kgyVU0gwIgY?si=NS-I-p2XR-c0eDGG", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1800}, {"url": "https://appsumo.com/products/easyspeak/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Review", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2210}, {"url": "https://www.facebook.com/watch/?v=1085477806442904&ref=sharing", "sourceType": "video", "sourceName": "Facebook", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "hindi", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3022, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2987}, {"url": "https://www.container-xchange.com/blog/one-trip-shipping-containers/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1991}, {"url": "https://youtube.com/shorts/lQZoSWkblsY?si=2AQoJNNkJIimvTGQ", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "small", "blogLanguage": "spanish", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1824}, {"url": "https://www.youtube.com/watch?v=GNt70Ef59RA", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4003, "sourceSimilarity": 86, "seoOptimization": false, "wordCount": 3621}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1864, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1680}, {"url": "https://youtu.be/bTSakEnK2hY", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "large", "blogLanguage": "german", "tableOption": "Two Table", "chartOption": "Two Charts", "seoOption": "ACTIVE", "wordCountApprox": 3008, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2698}, {"url": "https://docs.google.com/document/d/1Hbp0eRD3FhYTtruIuv_uGdsZDy3JhNIehaXSupkc_yo/edit?usp=sharing", "sourceType": "document", "sourceName": "Google Docs", "generationMode": "auto", "blogTone": "Storytelling", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1297, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1537}, {"url": "https://docs.google.com/document/d/1Hbp0eRD3FhYTtruIuv_uGdsZDy3JhNIehaXSupkc_yo/edit?usp=sharing", "sourceType": "document", "sourceName": "Google Docs", "generationMode": "auto", "blogTone": "Inspirational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2240}, {"url": "https://youtu.be/6elnsE7T_Ws?feature=shared", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "dutch", "tableOption": "Table for Each Section", "chartOption": "One Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1898}, {"url": "https://www.youtube.com/watch?v=_ZLeDdz72J4", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 1947}, {"url": "https://pmc.ncbi.nlm.nih.gov/articles/PMC10686034/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3906, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 3835}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2239}, {"url": "https://youtu.be/uC-_x6kXzWM", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1945}, {"url": "https://pmc.ncbi.nlm.nih.gov/articles/PMC5516425/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3930, "sourceSimilarity": 89, "seoOptimization": false, "wordCount": 3557}, {"url": "https://pmc.ncbi.nlm.nih.gov/articles/PMC9954726/", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Educational", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3564, "sourceSimilarity": 90, "seoOptimization": false, "wordCount": 3785}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4101, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3759}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4068, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3814}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4125, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3623}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4091, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3404}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4019, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3482}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4093, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3726}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4070, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3736}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4100, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3707}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4074, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3694}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4044, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3764}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4109, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3510}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4067, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3899}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4120, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3508}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4077, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3417}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4092, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3657}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4045, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3870}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4114, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3871}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4088, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3846}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4053, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3701}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4076, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3706}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4017, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4192}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3972, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3488}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4059, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4071}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4071, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3989}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4068, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3598}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4043, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3856}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4026, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3713}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4023, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3899}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4017, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3513}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4086, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3894}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4048, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3470}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4039, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3611}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4078, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3878}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4031, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 4256}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3961, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3895}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3997, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3761}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "x-large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 4017, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3840}, {"url": "", "sourceType": "prompt", "sourceName": "Prompt", "generationMode": "auto", "blogTone": "Informative", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3951, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3658}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes/136-rooibos-soleil-du-bresil-passion-mangue.html", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2999, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2704}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes/51-223-rooibos-nature-super-grade-premium.html#", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2995, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2730}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738279422664_2025-global-cmbs-webinar-new.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2131}, {"url": "https://s3.amazonaws.com/blogifyai/blog%2Fsources%2Fdocuments%2F1738278794527_2025-global-cmbs-webinar-new.pdf", "sourceType": "document", "sourceName": "Document File", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "Table for Each Section", "chartOption": "Charts for Each Section", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1876}, {"url": "https://haiti.loopnews.com/content/jason-derulo-investit-dans-le-business-du-car-wash", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Informative", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1670}, {"url": "https://blog.prepscholar.com/what-is-a-good-act-score-2025", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "small", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 14, "seoOptimization": false, "wordCount": 1858}, {"url": "https://lecomptoirdetoamasina.fr/fr/tisanes-et-infusions/263-396-tisane-silhouette-carioca.html#/158-tisane_carioca-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3029, "sourceSimilarity": 60, "seoOptimization": false, "wordCount": 3289}, {"url": "https://www.youtube.com/watch?v=Ic6QqCuYZsk", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Engaging", "blogSize": "mini", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 988, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1957}, {"url": "https://lecomptoirdetoamasina.fr/fr/aromes-alimentaires/14-719-eau-de-fleur-d-oranger-patisserie-maroc-trait-de-fleur-d-oranger-culinaire-patisserie-haut-de-gamme-recette.html#/328-arome-flacon_de_50ml", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3006, "sourceSimilarity": 60, "seoOptimization": false, "wordCount": 3200}, {"url": "https://ahrefs.com/seo/glossary/dofollow-link", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "How-to", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1977}, {"url": "https://lecomptoirdetoamasina.fr/fr/aromes-alimentaires/106-393-extrait-de-vanille-liquide-de-madagascar-patisserie.html#/155-extrait_de_vanille_de_madagascar-flacon_de_60g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3017, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 3078}, {"url": "https://lecomptoirdetoamasina.fr/fr/the-vert-achat-vente-thes-verts-grands-cru/230-936-the-vert-sencha-selection.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3014, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2913}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes/146-924-the-vert-genmaicha.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "large", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 3029, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 3222}, {"url": "https://lecomptoirdetoamasina.fr/fr/the-vert-achat-vente-thes-verts-grands-cru/369-1015-the-vert-chun-mee.html#/375-the_vert_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2999, "sourceSimilarity": 71, "seoOptimization": false, "wordCount": 3095}, {"url": "https://lecomptoirdetoamasina.fr/fr/the-noir/40-890-the-noir-des-indes-kerala-op.html#/374-the_noir_conditionnement-sachet_de_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2483, "sourceSimilarity": 69, "seoOptimization": false, "wordCount": 2671}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes/39-844-the-noir-de-ceylan-pettiagalla-op.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2524, "sourceSimilarity": 71, "seoOptimization": false, "wordCount": 2593}, {"url": "https://lecomptoirdetoamasina.fr/fr/thes/38-887-darjeeling-ftgfop1-pussimbing.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Neutral", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2513, "sourceSimilarity": 70, "seoOptimization": false, "wordCount": 2834}, {"url": "https://lecomptoirdetoamasina.fr/fr/the-noir/43-884-the-fume-lapsang-souchong-achat-vente-the-noir-lapsang-spuchong.html#/371-the_noir_conditionnement-sachet_refermable_80g", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Professional", "blogSize": "medium", "blogLanguage": "english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2517, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 2701}, {"url": "https://www.youtube.com/watch?v=du2tOOkpOBY", "sourceType": "video", "sourceName": "YouTube", "generationMode": "auto", "blogTone": "Professional", "blogSize": "small", "blogLanguage": "american english", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 1501, "sourceSimilarity": 55, "seoOptimization": false, "wordCount": 1181}, {"url": "https://www.cnbc.com/2023/02/23/micha<PERSON>-b-<PERSON><PERSON><PERSON>-talks-financial-literacy-at-hbcu-basketball-classic.html", "sourceType": "webLink", "sourceName": "Web Link", "generationMode": "auto", "blogTone": "Educational", "blogSize": "small", "blogLanguage": "french", "tableOption": "No Table", "chartOption": "No Chart", "seoOption": "ACTIVE", "wordCountApprox": 2000, "sourceSimilarity": 50, "seoOptimization": false, "wordCount": 1897}]