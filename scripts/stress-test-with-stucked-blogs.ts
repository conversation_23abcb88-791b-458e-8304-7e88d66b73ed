import axios from 'axios';
import * as fs from 'fs';

const BASE_URL = 'http://localhost:7777';
const API_KEY = 'px-zexapixqnpqjlqjnnzTNnxPzpnnxAbnqIqPnqPn';
const LOGIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'f0@Active',
};

interface RequestMetric {
  duration: number;
  success: boolean;
  statusCode: number;
  error?: string;
  source?: {
    type: 'prompt' | 'video';
    value: string; // will contain either the prompt text or video URL
  };
}

const ecommercePrompts = [
  'How to start an online store in 2024: A complete guide for beginners',
  '10 essential e-commerce marketing strategies that drive sales',
  'The ultimate guide to product photography for your online store',
  'How to write product descriptions that convert browsers into buyers',
  "E-commerce SEO: Boost your online store's visibility in search results",
  'Creating a successful email marketing strategy for your e-commerce business',
  'Social media marketing tips for e-commerce brands',
  'Understanding e-commerce analytics: Key metrics to track for success',
  'How to reduce shopping cart abandonment in your online store',
  'Building customer loyalty in e-commerce: Strategies that work',
  'E-commerce shipping strategies: How to optimize your delivery process',
  'Mobile commerce trends and optimization techniques',
  'Essential e-commerce security measures to protect your online store',
  'Creating an effective returns policy for your e-commerce business',
  'How to choose the right payment gateway for your online store',
  'Inventory management best practices for e-commerce businesses',
  'Customer service excellence in e-commerce: A comprehensive guide',
  'Using AI and automation in your e-commerce business',
  'Seasonal marketing strategies for e-commerce success',
  'How to conduct competitive analysis in e-commerce',
];

const getRandomPrompt = () => {
  const randomIndex = Math.floor(Math.random() * ecommercePrompts.length);
  return ecommercePrompts[randomIndex];
};

// Add video URLs that match different tones
const toneMappedVideos = {
  professional: [
    'https://www.youtube.com/watch?v=q8d9uuO1Cf4', // Professional business tips
    'https://www.youtube.com/watch?v=GfDMqfnFHxM', // LinkedIn marketing strategies
  ],
  conversational: [
    'https://www.youtube.com/watch?v=Cqg8tQH4U8M', // Casual vlog style content
    'https://www.youtube.com/watch?v=_wO8B1aiG7g', // Interactive Q&A sessions
  ],
  review: [
    'https://www.youtube.com/watch?v=SrYkrsBzbOQ', // Product reviews
    'https://youtu.be/fqjQNUc4yRc?feature=shared', // Service reviews
  ],
  neutral: [
    'https://youtu.be/RVwSh7fiae8?feature=shared', // News content
    'https://youtu.be/J8DGjUv-Vjc?feature=shared', // Documentary style
  ],
  educational: [
    'https://youtu.be/ocMOZpuAMw4?feature=shared', // Tutorial content
    'https://youtu.be/b093aqAZiPU?feature=shared', // How-to guides
  ],
};

// Function to get a random video URL based on tone
const getRandomVideo = (tone: string) => {
  const videos = toneMappedVideos[tone.toLowerCase()] || Object.values(toneMappedVideos)[0];
  return videos[Math.floor(Math.random() * videos.length)];
};

const generateRandomTitle = (): string => {
  return `Blog Post ${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
};

// Modify createBlogPayload to handle both prompt and video sources
const createBlogPayload = (blogData?: any) => {
  if (blogData) {
    // Use the blog data from JSON but ensure required fields are present
    if (blogData.sourceType === 'prompt' && !blogData.prompt) {
      blogData.prompt = getRandomPrompt();
    }
    return {
      ...blogData,
      title: generateRandomTitle(),
      generationMode: blogData.generationMode || 'auto',
      pov: blogData.pov || 'First Person',
      inputLanguage: blogData.blogLanguage || 'Global English',
      blogLanguage: blogData.blogLanguage || 'english',
      affiliateCommissionOptIn: blogData.affiliateCommissionOptIn || false,
    };
  }

  // Fallback to original random payload generation
  const blogTone = ['Professional', 'Conversational', 'Review', 'Neutral', 'Educational'][
    Math.floor(Math.random() * 5)
  ];
  const sourceType = ['prompt', 'video'][Math.floor(Math.random() * 2)];

  const commonPayload = {
    blogTone,
    blogSize: ['small', 'medium', 'large'][Math.floor(Math.random() * 3)],
    affiliateCommissionOptIn: true,
    generationMode: 'auto',
    pov: 'First Person',
    inputLanguage: 'Global English',
    blogLanguage: 'english',
  };

  if (sourceType === 'video') {
    return {
      ...commonPayload,
      sourceType: 'video',
      sourceName: 'YouTube',
      url: getRandomVideo(blogTone),
    };
  }

  return {
    ...commonPayload,
    sourceType: 'prompt',
    sourceName: 'Prompt',
    prompt: getRandomPrompt(),
  };
};

async function getAuthToken(): Promise<string> {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, LOGIN_CREDENTIALS, {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
      },
    });
    return response.data.access_token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw new Error('Failed to get auth token');
  }
}

async function createBlog(authToken: string, blogData?: any): Promise<RequestMetric> {
  const startTime = Date.now();
  const payload = createBlogPayload(blogData);

  try {
    const response = await axios.post(`${BASE_URL}/blogs`, payload, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
      },
    });

    return {
      duration: Date.now() - startTime,
      success: true,
      statusCode: response.status,
      source: {
        type: payload.sourceType,
        value: payload.sourceType === 'prompt' ? payload.prompt : payload.url,
      },
    };
  } catch (error) {
    // Log detailed error information
    console.error('\nRequest Failed:');
    console.error('---------------');
    console.error('Status:', error.response?.status);
    console.error('Error Message:', error.response?.data?.message || error.message);
    console.error('Source:', payload.sourceType === 'prompt' ? payload.prompt : payload.url);
    if (error.response?.data?.error) {
      console.error('Error Details:', error.response.data.error);
    }
    console.error('Request Payload:', JSON.stringify(payload, null, 2));
    console.error('---------------\n');

    return {
      duration: Date.now() - startTime,
      success: false,
      statusCode: error.response?.status || 0,
      error: error.response?.data?.message || error.message,
      source: {
        type: payload.sourceType,
        value: payload.sourceType === 'prompt' ? payload.prompt : payload.url,
      },
    };
  }
}

async function runStressTest({
  totalRequests = 100,
  concurrentRequests = 10,
  delayBetweenBatches = 1000,
  blogsData = [],
}: {
  totalRequests?: number;
  concurrentRequests?: number;
  delayBetweenBatches?: number;
  blogsData?: any[];
} = {}) {
  console.log(
    `Starting stress test with ${totalRequests} total requests, ${concurrentRequests} concurrent requests`,
  );

  // Get auth token first
  const authToken = await getAuthToken();
  console.log('Successfully authenticated');

  const startTime = Date.now();
  const metrics: RequestMetric[] = [];
  const statusCodes: { [key: number]: number } = {};
  const errors: { [key: string]: number } = {};

  // If blogsData is provided, use its length as totalRequests
  if (blogsData) {
    totalRequests = blogsData.length;
    console.log(`Adjusted total requests to ${totalRequests} based on provided blogs data`);
  }

  for (let i = 0; i < totalRequests; i += concurrentRequests) {
    const batchSize = Math.min(concurrentRequests, totalRequests - i);
    const batch = Array(batchSize)
      .fill(null)
      .map((_, index) => {
        const blogData = blogsData ? blogsData[i + index] : null;
        return createBlog(authToken, blogData);
      });

    console.log(`Processing batch ${Math.floor(i / concurrentRequests) + 1}...`);

    const results = await Promise.all(batch);
    metrics.push(...results);

    results.forEach((result) => {
      statusCodes[result.statusCode] = (statusCodes[result.statusCode] || 0) + 1;
      if (result.error) {
        errors[result.error] = (errors[result.error] || 0) + 1;
      }
    });

    if (i + batchSize < totalRequests) {
      await new Promise((resolve) => setTimeout(resolve, delayBetweenBatches));
    }
  }

  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  // Calculate metrics
  const successfulRequests = metrics.filter((m) => m.success);
  const failedRequests = metrics.filter((m) => !m.success);

  const avgResponseTime = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
  const avgSuccessResponseTime =
    successfulRequests.length > 0
      ? successfulRequests.reduce((sum, m) => sum + m.duration, 0) / successfulRequests.length
      : 0;
  const avgFailureResponseTime =
    failedRequests.length > 0
      ? failedRequests.reduce((sum, m) => sum + m.duration, 0) / failedRequests.length
      : 0;

  const minResponseTime = Math.min(...metrics.map((m) => m.duration));
  const maxResponseTime = Math.max(...metrics.map((m) => m.duration));

  // Print results
  console.log('\nStress Test Results:');
  console.log('===================');
  console.log('\nGeneral Statistics:');
  console.log('------------------');
  console.log(`Total Requests: ${totalRequests}`);
  console.log(`Successful Requests: ${successfulRequests.length}`);
  console.log(`Failed Requests: ${failedRequests.length}`);
  console.log(`Total Duration: ${duration.toFixed(2)} seconds`);
  console.log(`Requests per second: ${(totalRequests / duration).toFixed(2)}`);
  console.log(`Success Rate: ${((successfulRequests.length / totalRequests) * 100).toFixed(2)}%`);

  console.log('\nResponse Times:');
  console.log('--------------');
  console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`Average Success Response Time: ${avgSuccessResponseTime.toFixed(2)}ms`);
  console.log(`Average Failure Response Time: ${avgFailureResponseTime.toFixed(2)}ms`);
  console.log(`Minimum Response Time: ${minResponseTime}ms`);
  console.log(`Maximum Response Time: ${maxResponseTime}ms`);

  console.log('\nStatus Code Distribution:');
  console.log('----------------------');
  Object.entries(statusCodes)
    .sort(([a], [b]) => Number(a) - Number(b))
    .forEach(([code, count]) => {
      console.log(`${code}: ${count} requests`);
    });

  if (Object.keys(errors).length > 0) {
    console.log('\nError Distribution:');
    console.log('-----------------');
    Object.entries(errors)
      .sort(([, a], [, b]) => b - a)
      .forEach(([error, count]) => {
        console.log(`${error}: ${count} occurrences`);
      });
  }

  console.log('\nDetailed Error Summary:');
  console.log('---------------------');
  const errorDetails = metrics
    .filter((m) => !m.success)
    .map((m) => ({
      statusCode: m.statusCode,
      error: m.error,
      source: m.source,
      duration: m.duration,
    }));

  if (errorDetails.length > 0) {
    errorDetails.forEach((error, index) => {
      console.log(`\nError ${index + 1}:`);
      console.log(`Status Code: ${error.statusCode}`);
      console.log(`Error Message: ${error.error}`);
      console.log(`Source Type: ${error.source?.type}`);
      console.log(`Source Value: ${error.source?.value}`);
      console.log(`Duration: ${error.duration}ms`);
    });
  } else {
    console.log('No errors occurred during the test.');
  }
}

// Load blogs data from JSON file
const blogsData: any[] = JSON.parse(
  fs.readFileSync('scripts/blogify.blogs_stucked_after_outline.json', 'utf8'),
);

// Run the stress test with the blogs data
runStressTest({
  concurrentRequests: 5, // Reduced concurrent requests to be gentler on the API
  delayBetweenBatches: 2000, // Increased delay between batches
  blogsData,
}).catch((error) => {
  console.error('Stress test failed:', error);
  process.exit(1);
});
