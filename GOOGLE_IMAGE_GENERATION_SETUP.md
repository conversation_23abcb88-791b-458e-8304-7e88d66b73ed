# Google Image Generation Setup

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# Google Cloud Configuration for Image Generation
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_PROJECT_ID=your_google_cloud_project_id
GOOGLE_LOCATION=us-central1  # Optional, defaults to us-central1

# Google Service Account Credentials (for Imagen)
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
GOOGLE_CLIENT_EMAIL=<EMAIL>
```

## Usage Examples

### Using Gemini 2.0 Flash Preview Image Generation

```typescript
const result = await aiSdk.generateImage({
  prompt: "A beautiful sunset over mountains",
  provider: 'google',
  model: 'gemini-2.0-flash-preview-image-generation',
  size: '1024x1024',
  n: 1,
  identifierName: 'user',
  identifierValue: 'user123'
});

console.log('Generated images:', result.images);
```

### Using Imagen 3.0 Generate 002

```typescript
const result = await aiSdk.generateImage({
  prompt: "A futuristic city with flying cars",
  provider: 'google',
  model: 'imagen-3.0-generate-002',
  size: '1024x1024',
  n: 1,
  identifierName: 'user',
  identifierValue: 'user123'
});

console.log('Generated images:', result.images);
```

### Using GPT Image 1 (Fixed)

```typescript
const result = await aiSdk.generateImage({
  prompt: "A serene lake with mountains in the background",
  provider: 'openai',
  model: 'gpt-image-1',
  size: '1024x1024',
  quality: 'medium',
  n: 1,
  identifierName: 'user',
  identifierValue: 'user123'
});

console.log('Generated images:', result.images);
```

## Supported Models

1. **gpt-image-1** (OpenAI) - Fixed return format
2. **gemini-2.0-flash-preview-image-generation** (Google) - Free during preview
3. **imagen-3.0-generate-002** (Google) - $0.04 per image

## Supported Sizes

- **1024x1024** (square)
- **1024x1792** (portrait)
- **1792x1024** (landscape)

## Quality Options (GPT Image 1 only)

- **low** - $0.011 per image (1024x1024)
- **medium** - $0.042 per image (1024x1024)
- **high** - $0.167 per image (1024x1024)

## Error Handling

All helper functions include proper error handling and logging. If image generation fails, the functions will:

1. Log the error with appropriate context
2. Throw a descriptive error message
3. Continue attempting to generate remaining images if multiple were requested

## Authentication Setup

### For Gemini (using API Key):
1. Go to Google Cloud Console
2. Enable the Vertex AI API
3. Create an API key
4. Set `GOOGLE_API_KEY` environment variable

### For Imagen (using Service Account):
1. Go to Google Cloud Console
2. Create a service account
3. Download the JSON key file
4. Extract the private key and client email
5. Set the environment variables accordingly

## Notes

- Gemini 2.0 Flash Preview is currently free during the preview period
- Imagen 3.0 pricing is approximately $0.04 per image
- All models support batch generation (multiple images in one request)
- The helpers bypass Vercel AI SDK issues by using direct API calls
