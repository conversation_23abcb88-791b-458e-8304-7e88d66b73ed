{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "module": "commonjs",
    "moduleResolution": "node",
    "lib": ["es2022", "es2023.array", "esnext", "dom"],
    "target": "ES2022",
    "forceConsistentCasingInFileNames": false,
    "allowSyntheticDefaultImports": true,
    "noFallthroughCasesInSwitch": false,
    "useDefineForClassFields": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "strictBindCallApply": false,
    "preserveWatchOutput": true,
    "noUnusedParameters": false,
    "resolveJsonModule": true,
    "strictNullChecks": false,
    "isolatedModules": true,
    "noUnusedLocals": false,
    "declarationMap": false,
    "esModuleInterop": true,
    "removeComments": true,
    "noImplicitAny": false,
    "inlineSources": true,
    "skipLibCheck": true,
    "declaration": false,
    "incremental": true,
    "composite": false,
    "sourceMap": true,
    "strict": false,
    "outDir": "./dist",
    "baseUrl": "./",
    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/",
    "paths": {
      "@/*": ["src/*"],
      "@auth/*": ["src/auth/*"],
      "@integrations/*": ["src/integrations/*"],
      "@resources/*": ["src/resources/*"],
      "@common/*": ["src/common/*"],
      "@modules/*": ["src/modules/*"],
      "@user/*": ["src/user/*"],
      "@business/*": ["src/business/*"],
      "@asr/*": ["src/asr/*"],
      "@blog/*": ["src/blog/*"],
      "@openai/*": ["src/openai/*"],
      "@transcode/*": ["src/transcode/*"],
      "@transcribe/*": ["src/transcribe/*"],
      "@transcription/*": ["src/transcription/*"],
      "@job/*": ["src/job/*"],
      "@tracking/*": ["src/tracking/*"],
      "@queue/*": ["src/queue/*"],
      "@analytics/*": ["src/analytics/*"],
      "@image/*": ["src/image/*"],
      "@llm/*": ["src/llm/*"],
      "@blog-generation/*": ["src/blog-generation/*"]
    }
  },
  "watchOptions": {
    "watchFile": "fixedPollingInterval"
  },
  "exclude": [
    "node_modules",
    "dist",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    "openai",
    "src/metadata.ts"
  ],
  "include": ["src/**/*.ts"]
}
