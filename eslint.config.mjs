import { fileURLToPath } from 'node:url';
import { FlatCompat } from '@eslint/eslintrc';
import typescriptEslintEslintPlugin from '@typescript-eslint/eslint-plugin';
import prettierPlugin from 'eslint-plugin-prettier/recommended';
import prettierConfig from 'eslint-config-prettier';
import importPlugin from 'eslint-plugin-import';
import tsParser from '@typescript-eslint/parser';
import globals from 'globals';
import path from 'node:path';
import js from '@eslint/js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

const tsconfigRootDir = __dirname;
const project = path.join(tsconfigRootDir, 'tsconfig.json');

export default [
  {
    // Ignore patterns
    ignores: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**',
      '**/downloads/**',
      '**/debug/**',
      '**/migration/**',
      '**/*.spec.ts',
      '**/*.test.ts',
      '**/*.d.ts',
      'scripts/**',
      'puppeteer.config.ts',
      'src/metadata.ts',
      'test/**',
    ],
  },
  prettierConfig,
  prettierPlugin,
  // Base configs
  ...compat.extends('eslint:recommended', 'plugin:@typescript-eslint/recommended'),
  {
    files: ['**/*.ts', '**/*.tsx'],
    plugins: {
      '@typescript-eslint': typescriptEslintEslintPlugin,
      import: importPlugin, // Added import plugin
    },
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
      parser: tsParser,
      parserOptions: {
        project,
        tsconfigRootDir,
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
    },
    rules: {
      // Added import/no-extraneous-dependencies rule
      'import/no-extraneous-dependencies': [
        'error',
        {
          devDependencies: false,
          optionalDependencies: false,
          peerDependencies: false,
          // Add these:
          includeTypes: true,
          packageDir: __dirname,
        },
      ],

      // TypeScript rules
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/no-empty-function': 'warn',
      '@typescript-eslint/no-empty-interface': 'warn',
      '@typescript-eslint/no-inferrable-types': 'warn',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-var-requires': 'off',

      // Core ESLint rules
      'no-async-promise-executor': 'warn',
      'no-case-declarations': 'off',
      'no-empty': 'warn',
      'no-extra-boolean-cast': 'off',
      'no-useless-escape': 'warn',
      'prefer-const': 'warn',

      // Prettier configuration
      'prettier/prettier': [
        'warn',
        {
          endOfLine: 'auto',
          singleQuote: true,
          trailingComma: 'all',
          printWidth: 100,
        },
      ],

      // Style rules
      complexity: ['warn', { max: 25 }],
      'spaced-comment': ['warn', 'always'],
      curly: ['warn', 'multi-line'],
      'brace-style': [
        'warn',
        '1tbs',
        {
          allowSingleLine: true,
        },
      ],
      'nonblock-statement-body-position': ['warn', 'beside'],
    },
    settings: {
      'import/resolver': {
        typescript: {
          project,
          alwaysTryTypes: true, // Add this
        },
        node: true, // Add node resolver as fallback
      },
    },
  },
];
