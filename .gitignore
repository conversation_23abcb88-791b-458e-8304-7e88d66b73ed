# env
.env

# Downloads folder
downloads

# Generated files
transcript_*
audio_*

# Compiled output
/dist
/node_modules
/src/metadata.ts

# Logs
logs
debug_logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
stripe_backup.json
dump.rdb

# Sentry Config File
.sentryclirc

# Development
certs

# Husky
.husky/_

# yarn
.yarn

# debug
debug
