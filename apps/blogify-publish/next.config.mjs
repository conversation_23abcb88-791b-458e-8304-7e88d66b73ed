/** @type {import('next').NextConfig} */

import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig = {
  assetPrefix: '/publish',
  transpilePackages: ['@ps/common', '@ps/db', '@ps/ui'],
  async rewrites() {
    return [
      { source: '/', destination: '/blog' },
      { source: '/publish/_next/:path*', destination: '/_next/:path*' },
      { source: '/sitemap.xml', destination: '/api/sitemap' },
    ];
  },
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
