{"name": "blogify-publish", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo --experimental-https -p 7003", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/third-parties": "^15.1.6", "@ps/common": "workspace:*", "@ps/db": "workspace:*", "@ps/ui": "workspace:*", "@t3-oss/env-nextjs": "^0.11.1", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "mongodb": "^6.16.0", "mongoose": "^8.7.0", "next": "15.1.4", "next-intl": "^4.0.2", "nextjs-toploader": "^1.6.12", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-use": "^17.6.0"}, "devDependencies": {"@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/node": "^22.4.2", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "postcss": "^8", "tailwindcss": "^3.4.10", "typescript": "^5.5.4"}, "browserslist": ["defaults"]}