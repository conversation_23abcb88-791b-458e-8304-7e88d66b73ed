<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="60" cy="60" r="60" fill="url(#s4f2c7omta)"/>
    <mask id="2ps5i4mg3c" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="120" height="120">
        <circle cx="60" cy="60" r="60" fill="url(#1cm16to8ab)"/>
    </mask>
    <g mask="url(#2ps5i4mg3c)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M94.747 81.1H26.022v31.806a3.435 3.435 0 0 0 2.448 3.292c6.905 2.07 26.323 7.896 30.926 9.278a3.461 3.461 0 0 0 1.976 0c4.603-1.382 24.022-7.208 30.927-9.278a3.44 3.44 0 0 0 2.448-3.292V81.1z" fill="url(#7tsxr7wqbd)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M94.75 81.1H60.385s-.333 44.52 0 44.52c.335 0 .668-.048.988-.144 4.603-1.382 24.022-7.208 30.927-9.278a3.44 3.44 0 0 0 2.448-3.292V81.1z" fill="url(#0yesxcu1ue)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="m60.385 92.249-34.363-11.15-8.72 12.928a1.717 1.717 0 0 0 .894 2.596l30.918 10.045a1.718 1.718 0 0 0 1.953-.671l9.318-13.748z" fill="url(#956990nd1f)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M94.75 81.1 60.387 92.249l9.317 13.748a1.716 1.716 0 0 0 1.954.671l30.918-10.045a1.721 1.721 0 0 0 1.173-1.86 1.722 1.722 0 0 0-.28-.736L94.749 81.1z" fill="url(#5u2mk0prng)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M52.067 57.682a1.718 1.718 0 0 0-1.952-.666c-5.065 1.644-24.392 7.924-30.92 10.046a1.718 1.718 0 0 0-.893 2.594l7.72 11.447 34.363-11.187-8.318-12.234z" fill="url(#wqz3fw7plh)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M102.469 69.656a1.72 1.72 0 0 0-.219-2.186 1.717 1.717 0 0 0-.674-.408c-6.527-2.122-25.855-8.402-30.92-10.046a1.718 1.718 0 0 0-1.952.666l-8.317 12.234L94.75 81.102l7.719-11.446z" fill="url(#j2ptukor8i)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M94.747 81.1 60.384 69.915 26.022 81.101 60.384 92.25 94.747 81.1z" fill="url(#3h9xta3kyj)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M60.384 92.25V69.914L26.022 81.101 60.384 92.25z" fill="url(#w4glxft40k)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M53.482 74.85a15.089 15.089 0 0 1-1.931-1.491 1.72 1.72 0 0 0-2.427.103 1.718 1.718 0 0 0 .104 2.427c.74.676 1.534 1.29 2.375 1.837a1.718 1.718 0 0 0 1.88-2.876z" fill="#FBBAA3"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M58.553 31.642c1.859-10.527 17.97-9.846 8.59 0h-8.59z" fill="url(#43uop2or5l)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M58.553 34.309c1.859 10.525 17.97 9.846 8.59 0h-8.59z" fill="url(#v8af8ip3km)"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M48.126 69.57a14.94 14.94 0 0 1-1.295-2.084 1.719 1.719 0 0 0-3.064 1.559c.46.896.993 1.753 1.593 2.563a1.718 1.718 0 1 0 2.766-2.038zM45.614 62.559c.016-.68.118-1.355.304-2.009a1.718 1.718 0 0 0-3.303-.947 11.522 11.522 0 0 0-.437 2.87 1.719 1.719 0 0 0 3.436.086zM48.255 56.82c.457-.42.952-.798 1.478-1.128a1.719 1.719 0 1 0-1.833-2.907 13.49 13.49 0 0 0-1.98 1.515 1.717 1.717 0 1 0 2.335 2.52zM54.647 53.856a19.487 19.487 0 0 1 2.746-.302 1.719 1.719 0 0 0-.146-3.435c-1.082.044-2.16.163-3.225.357a1.72 1.72 0 0 0-1.13 2.626 1.717 1.717 0 0 0 1.755.754zM63.6 53.755a41.17 41.17 0 0 0 4.097-.146 1.717 1.717 0 1 0-.304-3.422c-1.248.106-2.5.15-3.753.132a1.718 1.718 0 0 0-.04 3.436zM73.346 52.556c1.7-.52 3.137-1.187 4.34-1.951a1.72 1.72 0 0 0 .062-2.858 1.718 1.718 0 0 0-1.91-.041c-.967.615-2.126 1.144-3.497 1.563a1.718 1.718 0 0 0 1.005 3.287zM82.09 45.795a10.67 10.67 0 0 0 1.042-5.16 1.718 1.718 0 0 0-3.432.16 7.242 7.242 0 0 1-.705 3.506 1.719 1.719 0 1 0 3.094 1.494zM80.995 34.561c-1.252-1.692-2.951-2.87-4.803-3.25a1.719 1.719 0 0 0-.691 3.367c1.069.218 2.01.952 2.733 1.93a1.718 1.718 0 1 0 2.761-2.047zM58.397 34.712h11.505a1.718 1.718 0 1 0 0-3.437H58.397a1.718 1.718 0 1 0 0 3.437z" fill="#F9AA90"/>
        <g filter="url(#rsig2ywa3n)">
            <circle cx="60" cy="60" r="60" fill="#D9D9D9" fill-opacity=".01"/>
        </g>
    </g>
    <defs>
        <linearGradient id="s4f2c7omta" x1="60" y1="0" x2="60" y2="120" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FAB59E"/>
            <stop offset="1" stop-color="#FFF8F5"/>
        </linearGradient>
        <linearGradient id="1cm16to8ab" x1="60" y1="0" x2="60" y2="120" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E4E4E4"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <linearGradient id="7tsxr7wqbd" x1="26.027" y1="103.464" x2="60" y2="114" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FBC8B6"/>
            <stop offset="1" stop-color="#FAB69E"/>
        </linearGradient>
        <linearGradient id="0yesxcu1ue" x1="60.446" y1="103.464" x2="94.964" y2="103.464" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FBC7B6"/>
            <stop offset="1" stop-color="#F9A386"/>
        </linearGradient>
        <linearGradient id="956990nd1f" x1="17.129" y1="92" x2="55.751" y2="107.587" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FCDACF"/>
            <stop offset="1" stop-color="#FBC8B6"/>
        </linearGradient>
        <linearGradient id="5u2mk0prng" x1="60.508" y1="92" x2="99.13" y2="107.587" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FDE5DD"/>
            <stop offset="1" stop-color="#FCD3C5"/>
        </linearGradient>
        <linearGradient id="wqz3fw7plh" x1="42" y1="82" x2="40.449" y2="62.488" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FEEDE7"/>
            <stop offset="1" stop-color="#FBC8B6"/>
        </linearGradient>
        <linearGradient id="j2ptukor8i" x1="79.5" y1="84" x2="89.196" y2="62.201" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FDE5DD"/>
            <stop offset="1" stop-color="#FBC8B6"/>
        </linearGradient>
        <linearGradient id="3h9xta3kyj" x1="78.5" y1="90" x2="78.508" y2="76" gradientUnits="userSpaceOnUse">
            <stop offset=".017" stop-color="#FBC8B6"/>
            <stop offset="1" stop-color="#FAB69E"/>
        </linearGradient>
        <linearGradient id="w4glxft40k" x1="51.5" y1="97.5" x2="47.705" y2="76.561" gradientUnits="userSpaceOnUse">
            <stop offset=".43" stop-color="#F9A88B"/>
            <stop offset="1" stop-color="#F9A88B"/>
            <stop offset="1" stop-color="#FAB69E"/>
        </linearGradient>
        <linearGradient id="43uop2or5l" x1="58.809" y1="27.667" x2="70.321" y2="27.667" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#FEECE5"/>
        </linearGradient>
        <linearGradient id="v8af8ip3km" x1="58.809" y1="37.975" x2="70.321" y2="37.975" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#FEECE5"/>
        </linearGradient>
        <filter id="rsig2ywa3n" x="0" y="-10" width="120" height="130" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-10"/>
            <feGaussianBlur stdDeviation="5"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0.976471 0 0 0 0 0.713726 0 0 0 0 0.623529 0 0 0 0.15 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_3265_14882"/>
        </filter>
    </defs>
</svg>
