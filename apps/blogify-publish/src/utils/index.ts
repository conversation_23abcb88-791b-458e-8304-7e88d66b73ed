import type { useTranslations } from 'next-intl';
import type { Blog } from '@ps/types';

import Cookies from 'js-cookie';

export function getBlogQuery(bid: string, websiteId: string): Record<string, any> {
  return {
    bid,
    publishResult: {
      $elemMatch: {
        siteID: websiteId,
        visibility: { $ne: 'hidden' },
        'outcome.link': { $exists: true, $ne: null },
      },
    },
  };
}

export function getReadingTime(wordCount: number, t: ReturnType<typeof useTranslations>): string {
  const wordsPerMinute = 200;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return minutes > 1 ? t('Blog.ReadingTimes', { minutes }) : t('Blog.ReadingTime', { minutes });
}

export function mapBlog(blog: Blog): Blog {
  return {
    ...blog,
    _id: blog?._id.toString(),
    uid: blog?.uid?.toString() as any,
    user: blog?.user ? { ...blog.user, _id: blog.user._id.toString() } : null,
  } as Blog;
}

export const setLanguageCookies = (newLang: string) => {
  const existLang = Cookies.get('NEXT_LOCALE');
  if (existLang && existLang === newLang) return;
  Cookies.set('NEXT_LOCALE', newLang, { path: '/' });
  window.location.reload();
};
