'use client';

import type { BlogCategory, Website } from '@ps/types';

import { createContext, useContext } from 'react';

export interface IPInfo {
  ip: string;
  city: string;
  region: string;
  country: string;
  loc: string;
  org: string;
  postal: string;
  timezone: string;
}

export interface AppContextType {
  website: Website;
  categories: BlogCategory[];

  ipInfo?: IPInfo;
}

const AppContext = createContext<AppContextType>({
  website: {} as Website,
  categories: [],
});

const useApp = () => {
  const context = useContext(AppContext);

  if (context === undefined) {
    throw new Error('useApp must be used within a AppProvider');
  }

  return context;
};

export default useApp;
export { AppContext };
