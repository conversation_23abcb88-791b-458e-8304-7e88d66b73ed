'use client';

import type { AppContextType } from './index';

import { useLayoutEffect } from 'react';

import { setLanguageCookies } from '@/utils';

import { AppContext } from './index';

const AppProvider = ({ children, value }: { children: React.ReactNode; value: AppContextType }) => {
  useLayoutEffect(() => {
    setLanguageCookies(value.website.defaultLanguage);
  }, [value.website.defaultLanguage]);

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export default AppProvider;
