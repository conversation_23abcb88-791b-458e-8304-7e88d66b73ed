'use server';

import type { Website as WebsiteType } from '@ps/types';
import type { AppContextType, IPInfo } from '.';

import { unstable_cache } from 'next/cache';
import { ObjectId } from 'mongodb';
import { headers } from 'next/headers';
import { cache } from 'react';

import { BlogCategory, Website } from '@ps/db';
import { CACHE_TIME_SECS, env } from '@/config';
import { defaultWebsite } from '@ps/types';
import MongoConnect from '@ps/db/connect';

const defaultHost = `publish${env.NEXT_PUBLIC_RELEASE !== 'production' ? '-test' : ''}.blogify.ai`;

const getWebsite = (host: string) =>
  unstable_cache(
    async () => {
      const filters = {
        status: { $in: ['active', 'checking'] },
        deleted: { $ne: true },
        url: host === 'blogify.ai' || host.includes('localhost') ? defaultHost : host,
      };

      await MongoConnect(env.MONGO_DB_URL);
      return await Website.findOne(filters).lean();
    },
    ['website', host],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  );

const getCategories = (business: string, website: string) =>
  unstable_cache(
    async () => {
      await MongoConnect(env.MONGO_DB_URL);
      return await BlogCategory.find({
        business: new ObjectId(business),
        website: new ObjectId(website),
        deleted: { $ne: true },
      })
        .select({ name: 1 })
        .sort({ name: 1 })
        .lean();
    },
    ['categories', business, website],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  );

const getIpInfo = (bid: string, ip: string | null) =>
  unstable_cache(
    async () => {
      try {
        const ipInfo = await fetch(
          `https://ipinfo.io/${ip === '::1' ? 'json' : ip}?token=${env.NEXT_PUBLIC_IP_INFO_TOKEN}`,
        ).then((r) => r.json());
        return ipInfo as IPInfo;
      } catch (error) {
        console.log(error);
        return;
      }
    },
    ['ip-info', bid, String(ip)],
    { revalidate: CACHE_TIME_SECS.FOURTEEN_DAYS },
  );

export const getAppContext: () => Promise<AppContextType> = cache(async () => {
  const headersList = await headers();
  const host = headersList.get('host') || defaultHost;

  const websiteDoc = await getWebsite(host)();

  const website = {
    ...defaultWebsite,
    ...websiteDoc,
    _id: websiteDoc?._id.toString(),
  } as WebsiteType;

  const categories = (await getCategories(website.bid, website._id)()).map((c) => ({
    ...c,
    _id: c._id.toString(),
  }));

  const ipInfo = await getIpInfo(website.bid, headersList.get('x-forwarded-for'))();

  return {
    ipInfo,
    website,
    categories,
  } satisfies AppContextType;
});
