import { EU_REGIONS } from '@ps/common/constants/country';

export function GATrackConsent(response: 'granted' | 'denied') {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'update', {
      ad_storage: response,
      ad_user_data: response,
      ad_personalization: response,
      analytics_storage: response,
      functionality_storage: response,
      personalization_storage: response,
      security_storage: response,
      region: EU_REGIONS,
    });
  } else {
    console.warn('Google Analytics gtag not available');
  }
}
