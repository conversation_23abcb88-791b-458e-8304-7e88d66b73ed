import { createEnv } from '@t3-oss/env-nextjs';
import { z } from '@ps/ui';

const isDev = process.env.NEXT_PUBLIC_RELEASE === 'develop';

export const CACHE_TIME_SECS = {
  FIVE_MINUTES: isDev ? 1 : 5 * 60,
  FIFTEEN_MINUTES: isDev ? 1 : 15 * 60,
  ONE_HOUR: isDev ? 1 : 60 * 60,
  TWENTY_FOUR_HOURS: isDev ? 1 : 24 * 60 * 60,
  FOURTEEN_DAYS: isDev ? 1 : 14 * 24 * 60 * 60,
};

export const env = createEnv({
  isServer: typeof window === 'undefined',
  server: {
    MONGO_DB_URL: z.string(),
    API_KEY: z.string(),
  },
  client: {
    NEXT_PUBLIC_RELEASE: z.enum(['production', 'staging', 'develop']),
    NEXT_PUBLIC_API_URL: z.string().url(),
    NEXT_PUBLIC_IP_INFO_TOKEN: z.string(),
  },
  runtimeEnv: {
    MONGO_DB_URL: process.env.MONGO_DB_URL || '',
    API_KEY: process.env.API_KEY || '',

    NEXT_PUBLIC_RELEASE: process.env.NEXT_PUBLIC_RELEASE || 'develop',
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || '',
    NEXT_PUBLIC_IP_INFO_TOKEN: process.env.NEXT_PUBLIC_IP_INFO_TOKEN || '',
  },
});
