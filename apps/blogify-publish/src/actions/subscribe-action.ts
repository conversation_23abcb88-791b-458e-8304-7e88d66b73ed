'use server';

import type { WebsiteSubscriber as WebsiteSubscriberType } from '@ps/types';

import { WebsiteSubscriber } from '@ps/db';

export default async function subscribeAction(
  body: Partial<WebsiteSubscriberType>,
): Promise<WebsiteSubscriberType> {
  try {
    const subscriber = new WebsiteSubscriber(body);
    await subscriber.save();

    return JSON.parse(JSON.stringify(subscriber.toObject()));
  } catch (e) {
    if ((e as Error).message.includes('duplicate key error')) {
      throw new Error(`You're already subscribed to this blog.`);
    } else {
      throw new Error((e as Error).message);
    }
  }
}
