'use client';

import { IoMdClose } from 'react-icons/io';
import { useState } from 'react';
import { FiMenu } from 'react-icons/fi';

import {
  SheetDescription,
  SheetContent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  SheetClose,
  Sheet,
} from '@ps/ui/components/sheet';
import { VisuallyHidden } from '@ps/ui';
import { hexToRgba } from '@ps/common/utils/color';
import { Button } from '@ps/ui/components/button';
import useApp from '@/providers/app';

type Props = { Logo: React.FC<{ className?: string }>; children: React.ReactNode };
export default function MobileHeader({ Logo, children }: Props) {
  const [open, setOpen] = useState<boolean>(false);
  const { website } = useApp();

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button className="-mr-4" variant="icon">
          <span className="sr-only">Open Main Menu</span>
          <FiMenu className="size-6" aria-hidden="true" />
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full overflow-y-auto border-0 bg-white p-0 text-black">
        <VisuallyHidden>
          <SheetHeader>
            <SheetTitle>Main Menu Sidebar</SheetTitle>
            <SheetDescription>
              Menu Sidebar with options to navigate the current page.
            </SheetDescription>
          </SheetHeader>
        </VisuallyHidden>

        <div
          className="h-full border-none px-6 py-3"
          style={{ backgroundColor: hexToRgba(website.brandColor, 5) }}
        >
          <header className="mt-0.5 flex items-center justify-between">
            <Logo />

            <SheetClose asChild>
              <Button className="-mr-2" variant="icon">
                <span className="sr-only">Open Main Menu</span>
                <IoMdClose className="size-6" aria-hidden="true" />
              </Button>
            </SheetClose>
          </header>

          <div onClick={() => setOpen(false)}>{children}</div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
