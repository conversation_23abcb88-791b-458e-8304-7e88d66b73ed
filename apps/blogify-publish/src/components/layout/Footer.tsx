'use client';

import type { Website } from '@ps/types';

import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';

import useApp from '@/providers/app';

import BlogifyLogoBlack from '@img/blogify-logo-black.svg';

export default function Footer() {
  const { website } = useApp();
  const t = useTranslations('Footer');

  return (
    <footer className="text-13 font-normal text-black">
      <div className="mx-auto flex max-w-[1920px] flex-wrap items-center justify-between gap-2 px-6 py-5">
        <p className="w-full md:w-auto">{website.footerText}</p>

        <ul className="flex flex-col gap-3 md:flex-row md:items-center md:gap-8">
          <FooterLinks />
          {website.showBlogifyBranding !== false && (
            <li>
              <a href="https://blogify.ai/" target="_blank" className="flex items-center gap-1">
                <span>{t('PoweredBy')}</span>
                <Image
                  className="-mt-0.5 h-4 w-auto"
                  src={BlogifyLogoBlack}
                  alt="Blogify Logo Light"
                />
              </a>
            </li>
          )}
        </ul>
      </div>
    </footer>
  );
}

const FooterLinks = () => {
  const { website } = useApp();
  const pathname = usePathname();

  const getStyle = (link: Website['links'][number]) => {
    if (link.url === '/blog' && /^\/blog(\/.*)?$/.test(pathname)) {
      return {
        color: website.brandColor,
      };
    }

    return {};
  };

  return (
    <>
      {website.footerLinks
        .filter((l) => l.title && l.url)
        .map((link, index) => (
          <li key={index}>
            <Link className="text-13" style={getStyle(link)} href={link.url}>
              {link.title}
            </Link>
          </li>
        ))}
    </>
  );
};
