'use client';

import type { ImageProps } from 'next/image';
import type { Website } from '@ps/types';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';

import { hexToRgba } from '@ps/common/utils/color';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import useApp from '@/providers/app';

import SubscribeDialog from '../dialogs/SubscribeDialog';
import MobileHeader from './MobileHeader';

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [open, setOpen] = useState(false);
  const { website } = useApp();
  const pathname = usePathname();
  const t = useTranslations('Header');

  const isHomePage = pathname === '/' || /^\/blog(\/.*)?$/.test(pathname);
  const isNotFound = pathname === '/not-found';
  const backgroundColor =
    (isHomePage || isNotFound) && !isScrolled ? hexToRgba(website.brandColor, 5) : 'white';

  const SubscribeButton = () => (
    <Button style={{ backgroundColor: website.brandColor }} onClick={() => setOpen(true)} size="sm">
      {t('Subscribe')}
    </Button>
  );

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className="sticky top-0 z-10 max-h-[60px] overflow-hidden bg-white transition-colors duration-200">
      <div style={{ backgroundColor }}>
        <div className="mx-auto flex w-full max-w-[1920px] items-center justify-between px-6 py-3 lg:px-12">
          <Logo />
          <nav className="hidden lg:flex lg:gap-12">
            <ul className="flex items-center gap-10">
              <NavLinks className="text-sm font-medium hover:underline" />

              <li>
                <SubscribeButton />
              </li>
            </ul>
          </nav>

          <div className="lg:hidden">
            <MobileHeader Logo={Logo}>
              <nav>
                <ul className="flex flex-col gap-1 py-5">
                  <NavLinks className="block py-2 text-base" />
                  <li className="block py-2">
                    <SubscribeButton />
                  </li>
                </ul>
              </nav>
            </MobileHeader>
          </div>
        </div>
      </div>

      <SubscribeDialog open={open} setOpen={setOpen} />
    </header>
  );
}

const Logo = ({ className, ...props }: Partial<ImageProps>) => {
  const { website } = useApp();

  return (
    <Link className={cn('flex gap-1.5', className)} href={`https://${website.url}`}>
      {website.logo ? (
        <Image
          className="h-[36px] w-auto rounded-lg object-contain"
          src={website.logo}
          alt="Blogify Logo"
          width={100}
          height={36}
          unoptimized
          {...props}
        />
      ) : (
        <DefaultLogo />
      )}
    </Link>
  );
};

const NavLinks = ({ className }: { className?: string }) => {
  const { website } = useApp();
  const pathname = usePathname();

  const getStyle = (link: Website['links'][number]) => {
    if (link.url === '/blog' && /^\/blog(\/.*)?$/.test(pathname)) {
      return {
        color: website.brandColor,
      };
    }

    return {};
  };

  return (
    <>
      {website.links
        .filter((l) => l.title && l.url)
        .map((link, index) => (
          <li key={index}>
            <Link
              className={cn('text-15 font-medium', className)}
              style={getStyle(link)}
              href={link.url}
            >
              {link.title}
            </Link>
          </li>
        ))}
    </>
  );
};

const DefaultLogo = () => {
  const { website } = useApp();

  return (
    <svg width="54" height="36" viewBox="0 0 54 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 23.849V7H3.89463V23.849H0ZM2.32226 23.849V20.2867H11.1033V23.849H2.32226Z"
        fill={website.brandColor}
      />
      <path
        d="M18.7456 24.1379C17.4715 24.1379 16.3427 23.8731 15.3589 23.3435C14.3913 22.798 13.6253 22.0518 13.0608 21.105C12.5125 20.1583 12.2384 19.0671 12.2384 17.8315C12.2384 16.5959 12.5125 15.5047 13.0608 14.558C13.6092 13.6112 14.3671 12.8731 15.3347 12.3435C16.3185 11.798 17.4312 11.5252 18.673 11.5252C19.947 11.5252 21.0678 11.798 22.0354 12.3435C23.0192 12.8731 23.7852 13.6112 24.3335 14.558C24.8818 15.5047 25.156 16.5959 25.156 17.8315C25.156 19.0671 24.8818 20.1583 24.3335 21.105C23.7852 22.0518 23.0272 22.798 22.0596 23.3435C21.1081 23.8731 20.0034 24.1379 18.7456 24.1379ZM18.7214 20.5755C19.2213 20.5755 19.6487 20.4632 20.0034 20.2385C20.3744 19.9978 20.6646 19.6769 20.8743 19.2757C21.0839 18.8585 21.1888 18.3771 21.1888 17.8315C21.1888 17.2859 21.0759 16.8125 20.8501 16.4114C20.6405 15.9942 20.3502 15.6652 19.9793 15.4245C19.6083 15.1838 19.181 15.0635 18.6972 15.0635C18.2134 15.0635 17.786 15.1838 17.4151 15.4245C17.0442 15.6652 16.7539 15.9942 16.5442 16.4114C16.3346 16.8125 16.2298 17.2859 16.2298 17.8315C16.2298 18.3611 16.3346 18.8344 16.5442 19.2516C16.7539 19.6689 17.0442 19.9978 17.4151 20.2385C17.8021 20.4632 18.2376 20.5755 18.7214 20.5755Z"
        fill={website.brandColor}
      />
      <path
        d="M32.7952 29C31.8114 29 30.9486 28.9358 30.2068 28.8074C29.4811 28.6791 28.8602 28.5106 28.3442 28.302C27.8281 28.0934 27.4007 27.8607 27.0621 27.6039L28.5377 24.6193C28.7957 24.7316 29.1021 24.868 29.4569 25.0284C29.8117 25.205 30.2471 25.3574 30.7632 25.4858C31.2792 25.6142 31.9001 25.6783 32.6258 25.6783C33.1903 25.6783 33.6902 25.566 34.1256 25.3414C34.5772 25.1328 34.9239 24.7958 35.1658 24.3304C35.4238 23.8811 35.5529 23.3115 35.5529 22.6214V11.814H39.3265V22.477C39.3265 23.8249 39.0685 24.9883 38.5525 25.9672C38.0364 26.946 37.2946 27.6922 36.3269 28.2057C35.3593 28.7352 34.1821 29 32.7952 29ZM31.9243 23.3917C30.7954 23.3917 29.8198 23.167 28.9973 22.7177C28.191 22.2524 27.562 21.5945 27.1105 20.744C26.675 19.8935 26.4573 18.8826 26.4573 17.7112C26.4573 16.4274 26.675 15.3282 27.1105 14.4136C27.562 13.4989 28.191 12.7929 28.9973 12.2954C29.8198 11.7819 30.7954 11.5252 31.9243 11.5252C32.8597 11.5252 33.666 11.7819 34.3433 12.2954C35.0368 12.7929 35.569 13.5069 35.9399 14.4376C36.3108 15.3683 36.4963 16.4836 36.4963 17.7834C36.4963 18.9387 36.3108 19.9336 35.9399 20.7681C35.569 21.6025 35.0368 22.2524 34.3433 22.7177C33.666 23.167 32.8597 23.3917 31.9243 23.3917ZM33.0371 20.2626C33.5209 20.2626 33.9321 20.1503 34.2708 19.9256C34.6256 19.6849 34.8917 19.364 35.0691 18.9628C35.2626 18.5616 35.3593 18.1043 35.3593 17.5908C35.3593 17.0452 35.2626 16.5718 35.0691 16.1707C34.8755 15.7695 34.6014 15.4566 34.2466 15.2319C33.9079 15.0073 33.4967 14.895 33.0129 14.895C32.5452 14.895 32.1259 15.0073 31.755 15.2319C31.4002 15.4566 31.118 15.7695 30.9083 16.1707C30.7148 16.5718 30.61 17.0452 30.5939 17.5908C30.61 18.1043 30.7148 18.5616 30.9083 18.9628C31.118 19.364 31.4083 19.6849 31.7792 19.9256C32.1501 20.1503 32.5694 20.2626 33.0371 20.2626Z"
        fill={website.brandColor}
      />
      <path
        d="M47.5896 24.1379C46.3156 24.1379 45.1867 23.8731 44.203 23.3435C43.2353 22.798 42.4693 22.0518 41.9049 21.105C41.3566 20.1583 41.0824 19.0671 41.0824 17.8315C41.0824 16.5959 41.3566 15.5047 41.9049 14.558C42.4532 13.6112 43.2112 12.8731 44.1788 12.3435C45.1625 11.798 46.2753 11.5252 47.517 11.5252C48.791 11.5252 49.9118 11.798 50.8795 12.3435C51.8632 12.8731 52.6292 13.6112 53.1775 14.558C53.7258 15.5047 54 16.5959 54 17.8315C54 19.0671 53.7258 20.1583 53.1775 21.105C52.6292 22.0518 51.8713 22.798 50.9037 23.3435C49.9522 23.8731 48.8475 24.1379 47.5896 24.1379ZM47.5654 20.5755C48.0653 20.5755 48.4927 20.4632 48.8475 20.2385C49.2184 19.9978 49.5087 19.6769 49.7183 19.2757C49.928 18.8585 50.0328 18.3771 50.0328 17.8315C50.0328 17.2859 49.9199 16.8125 49.6941 16.4114C49.4845 15.9942 49.1942 15.6652 48.8233 15.4245C48.4524 15.1838 48.025 15.0635 47.5412 15.0635C47.0574 15.0635 46.63 15.1838 46.2591 15.4245C45.8882 15.6652 45.5979 15.9942 45.3883 16.4114C45.1786 16.8125 45.0738 17.2859 45.0738 17.8315C45.0738 18.3611 45.1786 18.8344 45.3883 19.2516C45.5979 19.6689 45.8882 19.9978 46.2591 20.2385C46.6462 20.4632 47.0816 20.5755 47.5654 20.5755Z"
        fill={website.brandColor}
      />
    </svg>
  );
};
