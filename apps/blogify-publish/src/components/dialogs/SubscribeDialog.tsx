'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import toast from 'react-hot-toast';

import {
  DialogDescription,
  DialogContent,
  DialogHeader,
  Di<PERSON>Footer,
  <PERSON>alogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { zodResolver, z } from '@ps/ui';
import { emailSchema } from '@ps/ui/utils/validations';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import FormField from '@ps/ui/form/FormField';
import useApp from '@/providers/app';

import subscribeAction from '@/actions/subscribe-action';

const subscribeSchema = z.object({
  name: z.string({ required_error: 'Name is required' }),
  email: emailSchema,
});
type SubscribeSchema = z.infer<typeof subscribeSchema>;

export default function SubscribeDialog({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: React.Dispatch<boolean>;
}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const t = useTranslations('subscribe');

  const { getInputFields, handleSubmit, reset } = useForm<SubscribeSchema>({
    resolver: zodResolver(subscribeSchema),
  });
  const { website } = useApp();

  const submit = async (values: SubscribeSchema) => {
    setError('');
    setLoading(true);
    await subscribeAction({
      ...values,
      bid: website.bid,
      websiteId: website._id,
    })
      .then(() => {
        setOpen(false);
        toast.success(`Subscribed to ${website.name}.`);
      })
      .catch((e) => setError(e.message))
      .finally(() => setLoading(false));
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(o) => {
        setError('');
        reset();
        setOpen(o);
      }}
    >
      <DialogContent className="max-w-3xl border-none p-14">
        <DialogHeader>
          <DialogTitle className="text-center text-4xl font-bold">
            {t('subscribeTitle')} {website.name}
          </DialogTitle>
          <DialogDescription className="mx-auto mt-2 max-w-xl text-center text-base text-black">
            {t('subscribeDescription')}
          </DialogDescription>
        </DialogHeader>

        <form className="mt-6" onSubmit={handleSubmit(submit)}>
          <FormField
            label={t('nameField')}
            {...getInputFields('name')}
            style={{ borderColor: website.brandColor }}
          />
          <FormField
            label={t('emailField')}
            {...getInputFields('email')}
            style={{ borderColor: website.brandColor }}
          />

          {error && <div className="min-h-6 pt-1 text-xs font-medium text-red">{error}</div>}
          <DialogFooter>
            <Button
              className="w-full"
              size="lg"
              type="submit"
              loading={loading}
              style={{ backgroundColor: website.brandColor }}
            >
              {t('subscribeSubmitBtn')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
