'use client';

import type { Blog } from '@ps/types';

import { useTranslations } from 'next-intl';
import Image from 'next/image';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { VisuallyHidden } from '@ps/ui';
import { Button } from '@ps/ui/components/button';
import Embed from '@ps/ui/components/misc/Embed';

import YouTubeIcon from '@img/icons/integrations/youtube.svg';
import RumbleIcon from '@img/icons/integrations/rumble.svg';
import VimeoIcon from '@img/icons/integrations/vimeo.svg';

const EMBED_SUPPORTED_SOURCES = ['YouTube', 'Vimeo', 'Rumble'];

export default function VideoDialog({ blog }: { blog: Blog }) {
  const t = useTranslations('VideoDialog');

  const { url, sourceName } = blog;

  if (
    !(
      url &&
      blog.embedSource &&
      !blog.embedSourceAsCover &&
      EMBED_SUPPORTED_SOURCES.includes(sourceName)
    )
  )
    return null;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="secondary" size="md" className="text-primary">
          {sourceName === 'YouTube' ? (
            <Image className="size-5" src={YouTubeIcon} alt="YouTube Icon" />
          ) : sourceName === 'Rumble' ? (
            <Image className="size-5" src={RumbleIcon} alt="Rumble Icon" />
          ) : sourceName === 'Vimeo' ? (
            <Image className="size-5" src={VimeoIcon} alt="Vimeo Icon" />
          ) : null}
          {t('PlayVideo')}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-7xl border-none p-0 sm:rounded-lg">
        <VisuallyHidden>
          <DialogHeader>
            <DialogTitle>{blog.sourceName} Video</DialogTitle>
            <DialogDescription>Source video of the blog {blog.title}.</DialogDescription>
          </DialogHeader>
        </VisuallyHidden>

        <Embed
          platform={blog.sourceName as React.ComponentProps<typeof Embed>['platform']}
          autoPlay
          {...blog}
        />
      </DialogContent>
    </Dialog>
  );
}
