'use client';

import type { Blog } from '@ps/types';

import { useTranslations } from 'next-intl';
import { FaShare } from 'react-icons/fa6';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { SharingPlatforms, getSharerURL } from '@ps/common/utils/share';
import { Button } from '@ps/ui/components/button';
import useClient from '@ps/common/hooks/useClient';

import { SharingPlatformIcon } from '../misc/SharingPlatformIcon';

export default function ShareDialog({ blog }: { blog: Blog }) {
  const [isClient] = useClient();
  const t = useTranslations('ShareBlog');

  const blogLink = isClient ? window.location.href : '';

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="secondary" size="md">
          <FaShare />
          {t('ShareBlogBtn')}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-3xl border-none p-14">
        <DialogHeader>
          <DialogTitle className="text-center text-4xl font-bold">
            {t('ShareBlogTitle')}
          </DialogTitle>
          <DialogDescription className="mx-auto mt-2 max-w-xl text-center text-base text-black">
            {t('ShareBlogDescription')}
          </DialogDescription>
        </DialogHeader>

        <section className="mt-3 flex flex-wrap justify-center gap-6">
          {SharingPlatforms.map((platform, i) => (
            <a
              className="flex w-20 cursor-pointer flex-col items-center rounded-md py-2.5 hover:bg-[#f2f2f2]"
              href={getSharerURL(platform, {
                tags: blog.keywords,
                image: blog.image,
                title: blog.title,
                content: blogLink,
                link: blogLink,
              })}
              target="_blank"
              key={i}
            >
              <SharingPlatformIcon platform={platform} className="text-[38px]" />
              <span className="mt-1.5 text-xs font-medium capitalize">{t(platform)}</span>
            </a>
          ))}
        </section>
      </DialogContent>
    </Dialog>
  );
}
