import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google';
import { cookies } from 'next/headers';
import Script from 'next/script';
import React from 'react';

import { getAppContext } from '@/providers/app/getAppContext';
import { LOCALES } from '@ps/common/constants/locale';

export default async function ThirdPartyScripts() {
  const { website } = await getAppContext();
  const cookieStore = await cookies();

  const locale = cookieStore.get('NEXT_LOCALE')?.value || 'en';
  const l = LOCALES.find((l) => l.code === locale);

  return (
    <>
      {website.googleAnalyticsId && <GoogleAnalytics gaId={website.googleAnalyticsId} />}
      {website.googleTagManagerId && <GoogleTagManager gtmId={website.googleTagManagerId} />}

      <Script
        src={`https://connect.facebook.net/${l?.code}_${l?.countryCode}/sdk.js#xfbml=1&version=v17.0`}
        strategy="afterInteractive"
        defer
        async
      />
    </>
  );
}
