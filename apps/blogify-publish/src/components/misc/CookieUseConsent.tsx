'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

import { cacheGet, cacheSet } from '@ps/common/services/local-storage-cache';
import { GATrackConsent } from '@/services/analytics/google-analytics';
import { EU_REGIONS } from '@ps/common/constants/country';
import { Button } from '@ps/ui/components/button';
import useApp from '@/providers/app';

export default function CookieUseConsent() {
  const [cookieResponse, setCookieResponse] = useState<'granted' | 'denied'>();
  const [loading, setLoading] = useState(true);
  const { website, ipInfo } = useApp();
  const t = useTranslations('CookieConsent');

  useEffect(() => {
    const cookieConsent = cacheGet<'granted' | 'denied'>('cookie_response');
    setCookieResponse(cookieConsent);
    setLoading(false);
  }, []);

  if (loading || cookieResponse) return null;

  if (ipInfo?.country && !EU_REGIONS.includes(ipInfo?.country)) {
    return null;
  }

  const onAction = (response: 'granted' | 'denied') => {
    setCookieResponse(response);
    cacheSet('cookie_response', response);
    GATrackConsent(response);
  };

  return (
    <div
      style={{ boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.1)' }}
      className="fixed bottom-0 z-50 flex w-full bg-white"
    >
      <div className="mx-auto flex max-w-6xl flex-col items-center justify-between gap-3 p-4 md:flex-row md:px-6">
        <div className="text-xs sm:text-sm">
          <div className="font-semibold uppercase leading-[1.8]">{t('Title')}</div>
          <div>
            {t('Description')}&nbsp;
            {website.privacyPolicyUrl && (
              <Link href={website.privacyPolicyUrl} className="underline">
                {t('CookieLink')}
              </Link>
            )}
          </div>
        </div>

        <div className="flex flex-row gap-2 md:flex-col">
          <Button
            size="sm"
            onClick={() => onAction('granted')}
            style={{ backgroundColor: website.brandColor }}
          >
            {t('AcceptButton')}
          </Button>
          <Button variant="secondary" size="sm" onClick={() => onAction('denied')}>
            {t('RejectButton')}
          </Button>
        </div>
      </div>
    </div>
  );
}
