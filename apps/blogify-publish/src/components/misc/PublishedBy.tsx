import type { Blog } from '@ps/types';

import { useTranslations, useFormatter } from 'next-intl';
import { Avatar } from '@ps/ui/components/avatar';
import { cn } from '@ps/ui/lib/utils';

import { getReadingTime } from '@/utils';

export default function PublishedBy({ blog, className }: { blog: Blog; className?: string }) {
  const format = useFormatter();
  const t = useTranslations();

  const dateTime = new Date(blog.createdAt || '');

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <Avatar
        alt={`${blog.user?.name}'s Profile Picture`}
        src={blog.user?.profilePicture}
        name={blog.user?.name}
      />

      <div>
        <p className="text-15 font-semibold">{blog.user?.name}</p>
        <p className="mt-0.5 text-13 text-typo-secondary">
          {format.dateTime(dateTime, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}{' '}
          • {getReadingTime(blog.wordCount || 0, t)}
        </p>
      </div>
    </div>
  );
}
