'use client';

import { useTranslations, useFormatter } from 'next-intl';
import { useRouter } from 'next/navigation';

import Pagination from '@ps/ui/components/pagination';

type Props = { total: number; limit: number; page: number; brandColor: string };
export default function BlogListPagination({ total, limit, page, brandColor }: Props) {
  const router = useRouter();
  const format = useFormatter();
  const t = useTranslations();

  return (
    <Pagination
      className="mt-14"
      onPaging={(url) => router.push(url)}
      total={total}
      limit={limit}
      page={page}
      brandColor={brandColor}
      format={format}
      t={t}
    />
  );
}
