import type { Metadata } from 'next';

import { getTranslations, getFormatter } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { redirect } from 'next/navigation';
import Image from 'next/image';
import React from 'react';
import Link from 'next/link';

import { getReadingTime } from '@/utils';
import { getAppContext } from '@/providers/app/getAppContext';
import ShareDialog from '@/components/dialogs/ShareDialog';
import VideoDialog from '@/components/dialogs/VideoDialog';
import PublishedBy from '@/components/misc/PublishedBy';
import Embed from '@ps/ui/components/misc/Embed';

import { getRecentBlogs } from './actions/getRecentBlogs';
import { getBlog } from './actions/getBlog';
import TableOfContents from './components/TableOfContents';
import BlogComments from './components/BlogComments';
import BlogContent from './components/BlogContent';
import BlogRating from './components/BlogRating';
import BlogView from './components/BlogView';

import './style.css';

type BlogPostPageParams = {
  params: Promise<{
    slug: string;
  }>;
};

const extractTldr = (content: string): string | undefined => {
  const match = content.match(/<p>\s*<strong>TL;DR:\s*<\/strong>([\s\S]*?)<\/p>/i);
  return match ? match[1]?.trim() : undefined;
};

export async function generateMetadata({ params }: BlogPostPageParams): Promise<Metadata> {
  const { slug } = await params;

  if (slug.includes('.')) {
    return redirect('/not-found');
  }

  const blog = await getBlog(slug);
  if (!blog) {
    return redirect('/not-found');
  }

  const title = blog.title;
  const description = blog.metaDescription || '';
  const images = blog.image ? [blog.image] : [];
  const keywords = blog.keywords;

  return {
    title,
    description,
    keywords,

    openGraph: {
      title,
      description,
      images,
    },

    twitter: {
      title,
      description,
      images,
    },
  };
}

export default async function Page({ params }: BlogPostPageParams) {
  const { slug } = await params;

  let blog = await getBlog(slug);
  if (!blog?._id) {
    return redirect('/not-found');
  }

  const { website } = await getAppContext();
  const blogs = await getRecentBlogs(blog._id);

  const tldr = extractTldr(blog.content) || blog.blogOutline?.tldr;

  const moreBlogT = await getTranslations('MoreBlog');
  const format = await getFormatter();
  const t = await getTranslations();

  return (
    <main className="mx-auto my-1 max-w-6xl px-6 pb-20 pt-14">
      <BlogView blog={blog} />
      <h1 className="max-w-3xl pb-5 text-2xl font-bold md:text-5xl">{blog.title}</h1>

      <section className="my-5 flex flex-wrap items-center justify-between gap-3">
        <PublishedBy blog={blog} />

        <div className="flex gap-3">
          <VideoDialog blog={blog} />

          {website.shareEnabled && <ShareDialog blog={blog} />}
        </div>
      </section>

      {blog.embedSourceAsCover && blog.url && (
        <Embed
          platform={blog.sourceName as React.ComponentProps<typeof Embed>['platform']}
          {...blog}
        />
      )}

      {!blog.embedSourceAsCover && blog.image && (
        <figure>
          <Image
            className="aspect-video w-full rounded-xl object-cover"
            alt={`${blog.title} Cover`}
            src={blog.image || ''}
            height={621}
            width={1104}
            unoptimized
            priority
          />
          <figcaption></figcaption>
        </figure>
      )}

      <div className="lg:flex">
        {website.tableOfContentsEnabled && !!blog.content && (
          <div className="sticky top-20 mr-10 mt-10 hidden h-fit w-full border-r border-border lg:block">
            <TableOfContents {...blog} />
          </div>
        )}

        <div className="mx-auto flex max-w-3xl flex-col gap-10">
          {blog.tldrPosition === 'start' && <Tldr text={tldr} />}

          <BlogContent blogContent={blog.content} />

          {blog.tldrPosition === 'end' && <Tldr text={tldr} />}
          {website.ratingsEnabled && <BlogRating blog={blog} />}
          {website.commentsEnabled && <BlogComments blog={blog} />}
        </div>
      </div>

      {!!blogs.length && (
        <section className="mb-8 mt-44">
          <h3 className="mb-5 text-21 font-semibold">
            {moreBlogT('MoreBlogTitle')} {website.name}
          </h3>

          <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
            {blogs.map((b, i) => {
              const dateTime = new Date(b.createdAt || '');

              return (
                <Link key={i} className="flex flex-col gap-3" href={`../${b.slug}`}>
                  {b.image ? (
                    <Image
                      className="aspect-video h-[198px] w-full rounded-lg object-cover"
                      src={b.image}
                      alt="Blog Image"
                      height={192}
                      width={342}
                      unoptimized
                    />
                  ) : (
                    <div className="h-[198px] w-full rounded-lg bg-bg2" />
                  )}
                  <h4 className="line-clamp-2 min-h-[52px] text-17 font-semibold">{b.title}</h4>
                  <p className="text-13 text-typo-secondary">
                    {format.dateTime(dateTime, {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                    })}{' '}
                    • {getReadingTime(b.wordCount || 0, t)}
                  </p>
                </Link>
              );
            })}
          </div>
        </section>
      )}
    </main>
  );
}

const Tldr = ({ text }: { text?: string }) => {
  const t = useTranslations('Tldr');

  if (!text) return null;

  return (
    <section className="rounded-lg bg-bg2 p-6">
      <h2 className="mb-4 text-21 font-semibold">{t('TldrTitle')}</h2>
      <p>{text}</p>
    </section>
  );
};
