'use client';

import type { Blog } from '@ps/types';

import { useEffect } from 'react';

import useClient from '@ps/common/hooks/useClient';

import './style.css';

export default function BlogComments({ blog }: { blog: Blog }) {
  const [isClient] = useClient();

  const origin =
    isClient && !window.location.origin.includes('localhost')
      ? window.location.origin
      : 'https://blogify.ai';
  const url = `${origin}/${blog.slug}`;

  useEffect(() => {
    if (isClient && window.FB) {
      window.FB.XFBML.parse();
    }
  }, [isClient, url]);

  return (
    <div
      // eslint-disable-next-line tailwindcss/no-custom-classname
      className="fb-comments"
      data-href={url}
      data-width="100%"
      data-numposts="5"
    ></div>
  );
}
