'use client';

import { prepareBlogContent } from '@ps/common/hooks/useTableOfContents';
import useClient from '@ps/common/hooks/useClient';
import useApp from '@/providers/app';

export default function BlogContent({ blogContent }: { blogContent: string }) {
  const [isClient] = useClient();
  const { website } = useApp();

  const preparedContent =
    isClient && blogContent
      ? prepareBlogContent(blogContent, website._id)
      : blogContent
        ? blogContent
        : '';

  const removeTldr = (content: string): string =>
    content.replace(/<p>\s*<strong>TL;DR:\s*<\/strong>[\s\S]*?<\/p>/i, '').trim();

  return (
    <article
      className="blog-post"
      dangerouslySetInnerHTML={{ __html: removeTldr(preparedContent) || '' }}
    />
  );
}
