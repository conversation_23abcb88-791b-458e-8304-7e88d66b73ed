'use client';

import type { Blog } from '@ps/types/resources/blog.type';

import { useTranslations } from 'next-intl';

import { useTableOfContents } from '@ps/common/hooks/useTableOfContents';
import { isWindowsOS } from '@ps/ui/utils/browser';
import { cn } from '@ps/ui/lib/utils';

export default function TableOfContents({
  content,
  tableOfContents,
}: Pick<Blog, 'content' | 'tableOfContents'>) {
  const { headings, activeId, scrollToHeading } = useTableOfContents(content, {
    initialHeadings: tableOfContents,
    throttleDelay: 100,
    scrollOffset: 100,
    offsetTop: 150,
  });

  const t = useTranslations('TableOfContent');

  return (
    <div
      className={cn('max-h-[calc(100vh-80px)] overflow-y-auto p-4 pr-10', {
        'custom-scrollbar': isWindowsOS(),
      })}
    >
      <h3 className="mb-4 text-2xl font-semibold">{t('TableOfContentTitle')}</h3>

      <nav>
        <ul>
          {headings.map((heading) => (
            <li
              key={heading.id}
              className={cn('cursor-pointer rounded-lg transition-colors ', {
                'py-2.5 px-3.5 text-17 font-semibold': heading.level === 'h2',
                'ml-3 px-3 py-2 text-15 font-medium': heading.level === 'h3',
                'bg-[#f3f3f3]': heading.id === activeId,
              })}
              onClick={() => scrollToHeading(heading.id)}
            >
              {heading.text}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
