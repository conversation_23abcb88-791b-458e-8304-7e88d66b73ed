// بسم الله الرحمن الرحيم
'use client';
import type { Blog } from '@ps/types';

import { FaRegThumbsDown, FaRegThumbsUp } from 'react-icons/fa';
import { useTranslations, useFormatter } from 'next-intl';
import { useCallback, useState } from 'react';
import { PiHandsClappingBold } from 'react-icons/pi';
import { TbHandLoveYou } from 'react-icons/tb';
import { GiHandOk } from 'react-icons/gi';
import toast from 'react-hot-toast';

import { cn } from '@ps/ui/lib/utils';

type Rating = keyof typeof RatingIcons;

const RatingIcons = {
  bad: FaRegThumbsDown,
  ok: FaRegThumbsUp,
  nice: GiHandOk,
  great: PiHandsClappingBold,
  awesome: TbHandLoveYou,
} as const;

const defaultRatings = {
  bad: 0,
  ok: 0,
  nice: 0,
  great: 0,
  awesome: 0,
};

export default function BlogRating({ blog }: { blog: Blog }) {
  const [rating, setRating] = useState<string | null>(null);
  const t = useTranslations('BlogRating');

  const incrementRating = useCallback(
    async (chosenRating: Rating) => {
      fetch(`api?blogId=${blog._id}`, {
        method: 'POST',
        body: JSON.stringify({ rating: chosenRating }),
      })
        .then((r) => r.json())
        .then((r) => {
          if (r.error) throw new Error(r.error);
          setRating(chosenRating);
          toast.success(t('RatingSuccess'));
        })
        .catch(() => {
          toast.error(t('RatingFailed'));
        });
    },
    [blog._id, t],
  );

  return (
    <div className="w-full border border-bg2 bg-white">
      <article className="m-auto space-y-4 p-2 text-center">
        <span className="font-semibold capitalize">{t('BlogRatingTitle')}</span>
        <section className="flex items-center justify-center gap-4">
          {Object.entries<number>({ ...defaultRatings, ...(blog.publishRatings || {}) }).map(
            ([name, count]) => (
              <HandIcon
                key={name}
                chosen={rating === name}
                name={name as Rating}
                count={rating === name ? count + 1 : count}
                onClick={() => {
                  if (rating === null) incrementRating(name as Rating);
                }}
                t={t}
              />
            ),
          )}
        </section>
      </article>
    </div>
  );
}

const HandIcon = ({
  name,
  count,
  onClick,
  chosen,
  t,
}: {
  chosen: boolean;
  name: Rating;
  count: number;
  onClick: () => void;
  t: ReturnType<typeof useTranslations>;
}) => {
  const Icon = RatingIcons[name];
  const format = useFormatter();

  return (
    <section className="flex w-32 flex-col place-items-center gap-2 font-semibold">
      <Icon
        onClick={onClick}
        size={40}
        className={cn(
          'cursor-pointer rounded-full py-2 text-primary ring-2 hover:bg-primary hover:fill-white',
          { 'bg-pink-100': chosen },
        )}
      />
      <span className="text-17 capitalize">{t(name)}</span>
      <span className="text-sm">{format.number(count)}</span>
    </section>
  );
};
