'use server';

import type { Blog as BlogType } from '@ps/types';

import { unstable_cache } from 'next/cache';
import { cache } from 'react';

import { getBlogQuery, mapBlog } from '@/utils';
import { BLOG_SELECTED_FIELDS } from '@/constants';
import { CACHE_TIME_SECS, env } from '@/config';
import { Blog, mongoSelect } from '@ps/db';
import { getAppContext } from '@/providers/app/getAppContext';
import MongoConnect from '@ps/db/connect';

export const getRecentBlogs: (blogId: string) => Promise<BlogType[]> = cache(async (blogId) => {
  const { website } = await getAppContext();

  const blogs = await unstable_cache(
    async () => {
      const filterQuery = {
        _id: { $ne: blogId }, // Not current blog
        ...getBlogQuery(website.bid, website._id),
      };

      await MongoConnect(env.MONGO_DB_URL);
      return await Blog.find(filterQuery)
        .select(mongoSelect(BLOG_SELECTED_FIELDS))
        .sort({ createdAt: -1 })
        .limit(3)
        .lean();
    },
    [website._id, blogId],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  )();

  return blogs.map(mapBlog);
});
