'use server';

import type { Blog as BlogType } from '@ps/types';

import { unstable_cache } from 'next/cache';
import { cache } from 'react';

import { getBlogQuery, mapBlog } from '@/utils';
import { BLOG_SELECTED_FIELDS } from '@/constants';
import { CACHE_TIME_SECS, env } from '@/config';
import { Blog, mongoSelect } from '@ps/db';
import { getAppContext } from '@/providers/app/getAppContext';
import MongoConnect from '@ps/db/connect';

export const getBlog: (slug: string) => Promise<BlogType> = cache(async (slug) => {
  const { website } = await getAppContext();

  const blog = await unstable_cache(
    async () => {
      const filterQuery = {
        slug: decodeURIComponent(slug),
        ...getBlogQuery(website.bid, website._id),
      };

      await MongoConnect(env.MONGO_DB_URL);
      return await Blog.findOne(filterQuery)
        .select(mongoSelect(BLOG_SELECTED_FIELDS))
        .populate('user', { name: 1, profilePicture: 1 })
        .lean();
    },
    [website._id, slug],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  )();

  return mapBlog(blog as BlogType);
});
