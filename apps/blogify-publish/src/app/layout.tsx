import type { Metadata, Viewport } from 'next';

import { getMessages, getLocale } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { Toaster } from 'react-hot-toast';
import { Inter } from 'next/font/google';
import NextTopLoader from 'nextjs-toploader';

import { getAppContext } from '@/providers/app/getAppContext';
import { cn } from '@ps/ui/lib/utils';
import ThirdPartyScripts from '@/components/misc/ThirdPartyScripts';
import CookieUseConsent from '@/components/misc/CookieUseConsent';
import AppProvider from '@/providers/app/AppProvider';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

import './globals.scss';

const inter = Inter({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-inter',
});

export const viewport: Viewport = {
  width: 'device-width',
  // userScalable: true,
  initialScale: 1,
  // maximumScale: 1,
};

export async function generateMetadata(): Promise<Metadata> {
  const { website } = await getAppContext();

  const title = website.metaTitle || website.name;
  const description = website.metaDescription || website.name;
  const images = website.logo ? [website.logo] : [];
  const keywords = website.metaKeywords || [];

  return {
    title,
    description,
    keywords,

    icons: {
      icon: website.favicon || 'https://blogify.ai/favicon.ico',
    },

    openGraph: {
      title,
      description,
      images,
    },

    twitter: {
      title,
      description,
      images,
    },
  };
}

export default async function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  const messages = await getMessages();
  const locale = await getLocale();
  const value = await getAppContext();

  return (
    <AppProvider value={value}>
      <html lang={locale}>
        <head>
          <ThirdPartyScripts />
        </head>
        <body>
          <NextIntlClientProvider messages={messages}>
            <div className={cn(inter.variable, 'bg-white font-inter font-normal text-black')}>
              <NextTopLoader color={value.website.brandColor} showSpinner={false} />

              <Header />
              {children}
              <Footer />

              <Toaster />
              <CookieUseConsent />
            </div>
          </NextIntlClientProvider>
        </body>
      </html>
    </AppProvider>
  );
}
