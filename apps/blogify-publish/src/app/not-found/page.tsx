import Image from 'next/image';

import { getAppContext } from '@/providers/app/getAppContext';
import { hexToRgba } from '@ps/common/utils/color';

import NotFoundIllustration from '@img/404.svg';

export default async function NotFound() {
  const { website } = await getAppContext();

  return (
    <div
      className="mb-[-87px] flex h-[calc(100vh-60px)] flex-col px-4 text-center flex-center md:mb-[-60px] md:px-6"
      style={{ backgroundColor: hexToRgba(website.brandColor, 5) }}
    >
      <Image className="-mt-16" src={NotFoundIllustration} alt="No blogs published icon" />
      <h3 className="mt-4 text-17 font-semibold">404</h3>
      <p className="mb-4 mt-2 text-15">Sorry! the page you are looking for can not be found.</p>
    </div>
  );
}
