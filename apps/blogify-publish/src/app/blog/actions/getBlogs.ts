'use server';

import type { BlogCategory, Blog as BlogType } from '@ps/types';

import { unstable_cache } from 'next/cache';
import { cache } from 'react';

import { getBlogQuery, mapBlog } from '@/utils';
import { BLOG_SELECTED_FIELDS } from '@/constants';
import { Blog, mongoSelect } from '@ps/db';
import { getAppContext } from '@/providers/app/getAppContext';
import { env } from '@/config';
import MongoConnect from '@ps/db/connect';

type GetBlogs = {
  blogs: BlogType[];
  total: number;
  recentBlogs: BlogType[];
  limit: number;
  page: number;
  category?: BlogCategory;
  selectedCategoryId: string;
};

type GetBlogsFunction = (
  params: { category?: string[] },
  searchParams: Record<string, any>,
) => Promise<GetBlogs>;

const _getBlogs: (args: string) => Promise<GetBlogs> = cache(async (args) => {
  const _args = JSON.parse(args);
  const [params, searchParams] = _args as Parameters<GetBlogsFunction>;
  const { website, categories } = await getAppContext();

  const limit = parseInt(searchParams.limit || '10', 10);
  const page = parseInt(searchParams.page || '1', 10);
  const bid = website.bid;
  const q = searchParams.q;
  const cat = decodeURIComponent(params.category?.[0] || '');

  const category = categories.find((c) => c.name.toLowerCase() === cat?.toLowerCase());
  const selectedCategoryId = String(category?._id || '');

  const filterQuery = getBlogQuery(bid, website._id);
  const blogsFilterQuery = { ...filterQuery };
  if (selectedCategoryId) {
    blogsFilterQuery.categories = { $in: [selectedCategoryId] };
  }
  if (q) {
    blogsFilterQuery.$or = [
      { title: { $regex: q, $options: 'i' } },
      { content: { $regex: q, $options: 'i' } },
    ];
  }

  // Skip First Blog for 'All Blogs' View
  const skipFirst = Object.keys(blogsFilterQuery).length === 2 ? 1 : 0;

  const [blogs, total, recentBlogs] = await unstable_cache(
    async () => {
      await MongoConnect(env.MONGO_DB_URL);
      return await Promise.all([
        Blog.find(blogsFilterQuery)
          .select(mongoSelect(BLOG_SELECTED_FIELDS))
          .sort({ createdAt: -1 })
          .populate(['user', 'categories'])
          .limit(limit)
          .skip((page - 1) * limit + skipFirst)
          .lean(),
        Blog.countDocuments(blogsFilterQuery),
        Blog.find(filterQuery)
          .select(mongoSelect(BLOG_SELECTED_FIELDS))
          .sort({ createdAt: -1 })
          .populate('user')
          .limit(1)
          .lean(),
      ]);
    },
    [website._id, JSON.stringify(params), JSON.stringify(searchParams)],
    { revalidate: 30 },
  )();

  return {
    blogs: blogs.map(mapBlog),
    total: skipFirst ? total - 1 : total,
    recentBlogs: recentBlogs.map(mapBlog),
    limit,
    page,
    category,
    selectedCategoryId,
  };
});

export const getBlogs: GetBlogsFunction = async (...args) => {
  const _args = JSON.stringify(args);
  return _getBlogs(_args);
};
