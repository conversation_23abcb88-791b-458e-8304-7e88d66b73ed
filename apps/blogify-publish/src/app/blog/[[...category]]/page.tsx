import type { BlogCategory } from '@ps/types';

import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';

import { getAppContext } from '@/providers/app/getAppContext';
import { hexToRgba } from '@ps/common/utils/color';
import { cn } from '@ps/ui/lib/utils';
import BlogListPagination from '@/components/misc/BlogListPagination';
import PublishedBy from '@/components/misc/PublishedBy';

import { getBlogs } from '../actions/getBlogs';
import BlogSearchInput from '../components/BlogSearchInput';
import WebsiteView from '../components/WebsiteView';
import BlogCard from '../components/BlogCard';

import EmptyBlogs from '@img/empty-blogs.svg';

type HomePageParams = {
  params: Promise<{
    category?: string[];
  }>;
  searchParams: Promise<{
    category: string;
    limit: string;
    page: string;
    q: string;
  }>;
};

export default async function BlogList(props: HomePageParams) {
  const searchParams = await props.searchParams;
  const params = await props.params;

  const { website, categories } = await getAppContext();
  const { blogs, total, recentBlogs, category, limit, page, selectedCategoryId } = await getBlogs(
    params,
    searchParams,
  );

  const blog = recentBlogs[0];
  const isRoot = !Object.keys(searchParams).length && !selectedCategoryId;
  const singleBlog = isRoot && total == 0;

  const blogFilterT = await getTranslations('BlogFilter');
  const blogT = await getTranslations('Blog');

  return (
    <>
      <WebsiteView />
      {blog ? (
        <main>
          <section
            id="hero-banner"
            className={cn('py-10 md:py-20', {
              'mb-[-87px] md:mb-[-60px] h-[calc(100vh-60px)] flex flex-center': singleBlog,
            })}
            style={{ backgroundColor: hexToRgba(website.brandColor, 5) }}
          >
            <Link
              className={cn({ 'mt-[-87px] md:mt-[-60px]': singleBlog })}
              href={`../${blog.slug}`}
            >
              <div className="mx-auto flex max-w-6xl flex-col items-center gap-6 px-4 md:flex-row md:gap-10 md:px-6">
                <div className="w-full md:max-w-[464px]">
                  <h1 className="text-3xl font-semibold md:text-5xl">{blog.title}</h1>
                  <p className="mt-2 line-clamp-3 text-sm md:text-base">
                    {blog.content?.replace(/<\/?[^>]+(>|$)/g, '')}
                  </p>
                  <PublishedBy className="mt-4 md:mt-6" blog={blog} />
                </div>

                {blog.image ? (
                  <Image
                    className="aspect-video w-full rounded-xl object-cover md:max-w-[632px]"
                    alt={`${blog.title} Cover`}
                    src={blog.thumbnail ?? blog.image}
                    height={356}
                    width={632}
                    unoptimized
                    priority
                  />
                ) : (
                  <div className="flex aspect-video w-full rounded-xl bg-black/5 flex-center md:min-h-[356px] md:max-w-[632px]">
                    <Image src={EmptyBlogs} alt="Default Blog Image" height={150} />
                  </div>
                )}
              </div>
            </Link>
          </section>

          {!singleBlog && (
            <div className="mx-auto max-w-6xl px-4 pb-10 md:px-6">
              <div className="my-10 flex flex-col gap-2">
                <BlogSearchInput className="md:hidden" />

                <div className="flex flex-wrap gap-2 md:gap-3">
                  <CategoryLink
                    category={{ _id: '', business: '', website: '', name: blogFilterT('AllBlog') }}
                    selectedCategoryId={selectedCategoryId}
                  />

                  <BlogSearchInput className="hidden md:block" />
                  {categories.map((category) => (
                    <CategoryLink
                      key={category._id}
                      category={category}
                      selectedCategoryId={selectedCategoryId}
                    />
                  ))}
                </div>
              </div>

              {blogs.length ? (
                <>
                  <section className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2 md:gap-x-20 md:gap-y-14">
                    {blogs.map((b, i) => (
                      <BlogCard key={i} blog={b} category={category} index={i} />
                    ))}
                  </section>

                  <BlogListPagination
                    total={total}
                    limit={limit}
                    page={page}
                    brandColor={website.brandColor}
                  />
                </>
              ) : (
                <div className="flex min-h-[calc(100vh-635px)] flex-col p-10 text-center flex-center">
                  <Image className="size-24" src={EmptyBlogs} alt="No blogs published icon" />
                  <p className="mb-4 mt-2 text-15">{blogT('NoMoreBlogs')}</p>
                </div>
              )}
            </div>
          )}
        </main>
      ) : (
        <NoBlogs />
      )}
    </>
  );
}

const CategoryLink = async ({
  category,
  selectedCategoryId,
}: {
  category: BlogCategory;
  selectedCategoryId: string;
}) => {
  const { website } = await getAppContext();

  return (
    <Link
      href={`/blog/${category._id ? encodeURIComponent(category.name.toLowerCase()) : ''}`}
      className={cn(
        'flex h-8 cursor-pointer items-center rounded-3xl border border-[#e6e6e6] px-3 text-sm shadow-sm md:h-9 md:px-4 md:text-base',
        { 'text-white shadow-none': selectedCategoryId === String(category._id) },
      )}
      style={
        selectedCategoryId === String(category._id)
          ? { backgroundColor: website.brandColor }
          : undefined
      }
    >
      {category.name}
    </Link>
  );
};

const NoBlogs = async () => {
  const { website } = await getAppContext();
  const t = await getTranslations('Blog');

  return (
    <div
      className="mb-[-87px] flex h-[calc(100vh-60px)] flex-col px-4 text-center flex-center md:mb-[-60px] md:px-6"
      style={{ backgroundColor: hexToRgba(website.brandColor, 5) }}
    >
      <Image className="-mt-16" src={EmptyBlogs} alt="No blogs published icon" />
      <h3 className="mt-4 text-17 font-semibold">{t('NothingToRead')}</h3>
      <p className="mb-4 mt-2 text-15">{t('NoBlogsYet')}</p>
    </div>
  );
};
