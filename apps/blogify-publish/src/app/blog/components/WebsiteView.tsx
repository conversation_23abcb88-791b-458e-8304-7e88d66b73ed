'use client';

import { useEffect } from 'react';

import useClient from '@ps/common/hooks/useClient';
import useApp from '@/providers/app';

export default function WebsiteView() {
  const [isClient] = useClient();
  const { website } = useApp();

  useEffect(() => {
    if (isClient) {
      fetch(
        `api?ref=${window.location.href}&site_id=${website._id}&utm_source=${website.bid}&utm_content=v`,
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isClient]);

  return <></>;
}
