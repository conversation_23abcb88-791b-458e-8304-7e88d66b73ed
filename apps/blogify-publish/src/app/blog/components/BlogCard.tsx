import type { BlogCategory, Blog } from '@ps/types';

import { useTranslations, useFormatter } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';

import { getReadingTime } from '@/utils';

export default function BlogCard({
  blog,
  index,
  category,
}: {
  blog: Blog;
  index: number;
  category?: BlogCategory;
}) {
  const format = useFormatter();
  const t = useTranslations();

  const dateTime = new Date(blog.createdAt || '');

  return (
    <Link href={`../${blog.slug}`}>
      {blog.image ? (
        <Image
          className="aspect-video w-full rounded-xl object-cover"
          priority={[0, 1].includes(index)}
          alt={`${blog.title} Cover`}
          src={blog.image}
          height={288}
          width={512}
          unoptimized
        />
      ) : (
        <div className="aspect-video w-full rounded-xl bg-bg2" />
      )}
      <p className="mt-3 text-xs font-semibold uppercase md:mt-4 md:text-11">
        {format.dateTime(dateTime, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })}
      </p>
      <h2 className="mt-1 line-clamp-2 text-xl font-semibold hover:underline md:mt-1.5 md:text-2xl">
        {blog.title}
      </h2>
      <p className="mt-1 line-clamp-2 text-sm md:text-base">
        {blog.content?.replace(/<\/?[^>]+(>|$)/g, '')}
      </p>
      <p className="mt-2 text-sm md:mt-2.5 md:text-15">
        {category?.name || (blog.categories?.[0] as BlogCategory)?.name}{' '}
        {!!(category?.name || (blog.categories?.[0] as BlogCategory)?.name) && <>&bull;</>}{' '}
        {getReadingTime(blog.wordCount || 0, t)}
      </p>
    </Link>
  );
}
