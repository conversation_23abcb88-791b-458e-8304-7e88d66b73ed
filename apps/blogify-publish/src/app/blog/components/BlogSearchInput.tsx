'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useDebounce } from 'react-use';
import { useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import { MdClose } from 'react-icons/md';

import { cn } from '@ps/ui/lib/utils';
import useApp from '@/providers/app';

export default function SearchInput({ className }: { className?: string }) {
  const [isFirstRender, toggleFirstRender] = useState(true);
  const searchParams = useSearchParams();
  const q = searchParams.get('q');
  const t = useTranslations('BlogFilter');

  const [searchQuery, setSearchQuery] = useState(q || '');
  const { website } = useApp();
  const router = useRouter();

  useDebounce(
    () => {
      if (!isFirstRender) {
        const params = new URLSearchParams(window.location.search);
        searchQuery ? params.set('q', searchQuery) : params.delete('q');
        router.push(`${window.location.pathname}?${params.toString()}`);
      }
      toggleFirstRender(false);
    },
    500,
    [router, searchQuery],
  );

  return (
    <form
      className={cn('relative w-max rounded-full shadow-sm', className)}
      onSubmit={(ev) => ev.preventDefault()}
    >
      <button
        className="pointer-events-none absolute left-0 top-0 flex size-9 flex-center"
        style={{ color: website.brandColor }}
        aria-label="Search"
      >
        <FaSearch />
      </button>

      <input
        className={cn(
          'size-9 cursor-pointer rounded-full border pl-8 text-base outline-none transition-all duration-300 placeholder:text-transparent',
          'focus:w-60 focus:cursor-text focus:placeholder:text-gray-400',
          searchQuery ? 'w-60 cursor-text' : '',
        )}
        onChange={(ev) => setSearchQuery(ev.target.value)}
        placeholder={t('SearchBlogs')}
        value={searchQuery}
        type="text"
        name="q"
      />

      {searchQuery && (
        <button
          className="absolute right-0 top-0 flex size-9 flex-center"
          style={{ color: website.brandColor }}
          onClick={() => setSearchQuery('')}
        >
          <MdClose />
        </button>
      )}
    </form>
  );
}
