import { NextResponse } from 'next/server';

import { env } from '@/config';
import API from '@ps/common/services/api';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  try {
    const userAgent = request.headers.get('user-agent');
    const ip = request.headers.get('x-forwarded-for');

    await API.get(`track/url?${searchParams.toString()}`, {
      headers: {
        'user-agent': String(userAgent),
        'x-forwarded-for': String(ip),
        'x-api-key': env.API_KEY,
      },
    });
  } catch (e) {
    console.log(e);
  }
  return NextResponse.json({ message: 'Success' });
}

export async function POST(request: Request) {
  const { searchParams } = new URL(request.url);
  const blogId = searchParams.get('blogId');
  const body = await request.json();

  const d: any = await API.post(`blogify/increment-rating/${blogId}`, body, {
    headers: { 'x-api-key': env.API_KEY },
  });

  if (d.error)
    return NextResponse.json(
      { error: d.message || 'Internal Server Error' },
      { status: d.statusCode || 500 },
    );

  return NextResponse.json({ message: 'Success' });
}
