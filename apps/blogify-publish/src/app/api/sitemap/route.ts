import { unstable_cache } from 'next/cache';
import { NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';

import { BlogCategory, Website, Blog } from '@ps/db';
import { CACHE_TIME_SECS, env } from '@/config';
import { getBlogQuery } from '@/utils';
import MongoConnect from '@ps/db/connect';

const isProd = env.NEXT_PUBLIC_RELEASE === 'production';
const defaultHost = `publish${!isProd ? '-test' : ''}.blogify.ai`;
const defaultDomain = `${!isProd ? 'test.' : ''}blogify.ai`;

type Sitemap = {
  loc: string;
  lastmod: string;
  priority: number;
};

const getWebsite = (host: string) =>
  unstable_cache(
    async () => {
      const filters = {
        status: { $in: ['active', 'checking'] },
        deleted: { $ne: true },
        url: host === 'blogify.ai' || host.includes('localhost') ? defaultHost : host,
      };

      await MongoConnect(env.MONGO_DB_URL);
      return await Website.findOne(filters).select('bid').lean();
    },
    ['website', host],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  );

const getCategories = (business: string, website: string) =>
  unstable_cache(
    async () => {
      await MongoConnect(env.MONGO_DB_URL);
      return await BlogCategory.find({
        business: new ObjectId(business),
        website: new ObjectId(website),
        deleted: { $ne: true },
      })
        .select('name')
        .lean();
    },
    ['categories', business, website],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  );

const getBlogs = (bid: string, websiteId: string) =>
  unstable_cache(
    async () => {
      await MongoConnect(env.MONGO_DB_URL);
      return Blog.find(getBlogQuery(bid, websiteId))
        .select('slug updatedAt')
        .sort({ updatedAt: -1 })
        .lean();
    },
    ['blogs', bid, websiteId],
    { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
  );

const fetchBlogifySitemap = unstable_cache(
  async () => {
    try {
      const response = await fetch(`https://${defaultDomain}/sitemap_private.xml`);
      const xmlContent = await response.text();

      // Extract URLs from the sitemap XML
      const urlRegex = /<url>\s*<loc>([^<]+)<\/loc>[\s\S]*?<\/url>/g;
      const urls: Sitemap[] = [];

      let match;
      while ((match = urlRegex.exec(xmlContent)) !== null) {
        const loc = match[1] || '';

        // Extract lastmod if available
        const lastmodMatch = match[0].match(/<lastmod>([^<]+)<\/lastmod>/);
        const lastmod =
          lastmodMatch && lastmodMatch[1] ? lastmodMatch[1] : new Date().toISOString();

        // Extract priority if available
        const priorityMatch = match[0].match(/<priority>([^<]+)<\/priority>/);
        const priority = priorityMatch && priorityMatch[1] ? parseFloat(priorityMatch[1]) : 0.41;

        urls.push({ loc, lastmod, priority });
      }

      return urls;
    } catch (error) {
      console.error('Error fetching Blogify sitemap:', error);
      return [];
    }
  },
  ['blogify-sitemap', defaultHost],
  { revalidate: CACHE_TIME_SECS.FIVE_MINUTES },
);

export async function GET(request: Request) {
  let host = request.headers.get('host') || '';

  const isBlogify = ['blogify.ai', 'localhost'].some((d) => host.includes(d));
  if (isBlogify) {
    host = defaultHost;
  }

  const website = await getWebsite(host)();
  if (!website) {
    return NextResponse.error();
  }

  const [categories, blogs] = await Promise.all([
    getCategories(website.bid, website._id)(),
    getBlogs(website.bid, website._id)(),
  ]);

  let urls: Sitemap[] = [];

  const domain = isBlogify ? defaultDomain : host;
  if (categories.length) {
    categories.forEach((category) => {
      urls.push({
        loc: `https://${domain}/${category.name.toLowerCase()}`,
        lastmod: new Date().toISOString(),
        priority: 0.3,
      });
    });
  }

  if (blogs.length) {
    blogs.forEach((blog) => {
      if (blog.slug) {
        urls.push({
          loc: `https://${domain}/${blog.slug}`,
          lastmod: new Date(blog.updatedAt || '').toISOString(),
          priority: 0.41,
        });
      }
    });
  }

  if (isBlogify) {
    const blogifyUrls = await fetchBlogifySitemap();
    const existingUrls = new Set(urls.map((url) => url.loc));

    blogifyUrls.forEach((url) => {
      if (!existingUrls.has(url.loc)) {
        urls.unshift(url);
      }
    });
  }

  urls.sort((a, b) => b.priority - a.priority);

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${
      isBlogify
        ? ''
        : `<url>
      <loc>https://${domain}</loc>
      <lastmod>${new Date().toISOString()}</lastmod>
      <priority>1.00</priority>
    </url>`
    }
    ${urls
      .map((url) => {
        return `
          <url>
            <loc>${url.loc}</loc>
            <lastmod>${url.lastmod}</lastmod>
            <priority>${(url.priority || 0.41).toFixed(2)}</priority>
          </url>
        `;
      })
      .join('')}
  </urlset>`;

  return new NextResponse(sitemap, { headers: { 'Content-Type': 'application/xml' } });
}
