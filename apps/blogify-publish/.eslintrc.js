/** @type {import("eslint").Linter.Config} */
module.exports = {
  root: true,
  extends: ['@ps/eslint-config/next.js', 'next/core-web-vitals', 'plugin:tailwindcss/recommended'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: true,
  },
  rules: {
    'react/jsx-curly-brace-presence': ['warn', { props: 'never', children: 'never' }],
  },
  settings: {
    tailwindcss: {
      config: './tailwind.config.ts',
      callees: ['classnames', 'clsx', 'ctl', 'cn'],
    },
  },
};
