import type { Config } from 'tailwindcss';

import baseConfig from '@ps/ui/tailwind.config';

const config = {
  ...baseConfig,
  plugins: [...baseConfig.plugins],
  content: [
    ...baseConfig.content,
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    ...baseConfig.theme,
    extend: {
      ...baseConfig.theme.extend,
      colors: {
        ...baseConfig.theme.extend.colors,
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
      fontFamily: {
        space: ['var(--font-space-grotesk)'],
        inter: ['var(--font-inter)'],
        ibx: ['var(--font-ibm-plex-mono)'],
      },
    },
  },
} satisfies Config;

export default config;
