import type { Config } from 'tailwindcss';

import baseConfig from '@ps/ui/tailwind.config';

import Theme from './src/styles/theme';

// Color theming
const secondary = {
  10: 'rgba(241, 237, 247, 0.15)',
  '02': 'rgba(197, 184, 224, 0.02)',
  '05': 'rgba(197, 184, 224, 0.05)',
  100: 'rgba(197, 184, 224, 0.10)',
  150: 'rgba(197, 184, 224, 0.15)',
  250: 'rgba(197, 184, 224, 0.25)',
};
const extendedColors = {
  primary: {
    DEFAULT: '#F2470D',
    50: '#F2805A',
    100: '#F06D42',
    200: '#EE5B2A',
  },
  ...Theme.colors,
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#E3DBD9',
  },
};

/** @type {import('tailwindcss').Config} */
export default {
  ...baseConfig,
  content: [...baseConfig.content, './index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    ...baseConfig.theme,
    container: {
      center: true,
    },
    fontFamily: {
      space: ['Space Grotesk', 'sans-serif'],
      figtree: ['Figtree', 'sans-serif'],
      ibx: ['IBM Plex Mono', 'monospace'],
      inter: ['Inter', 'sans-serif'],
    },
    extend: {
      ...baseConfig.theme.extend,
      colors: {
        ...baseConfig.theme.extend.colors,
        ...extendedColors,
      },
      fontSize: {
        xs: '11px',
        sm: '13px',
        md: '15px',
        lg: '17px',
        ...baseConfig.theme.extend.fontSize,
      },
      screens: {
        xs: '550px',
      },
      backgroundColor: {
        dark: '#0E071D',
      },
      backgroundImage: {
        'button-secondary': `linear-gradient(180deg, ${secondary['05']} 0%, ${secondary['100']} 100%)`,
        ...baseConfig.theme.extend.backgroundImage,
      },
      boxShadow: {
        xs: '0 1px 1px 0 rgba(168, 145, 138, 0.3)',
        sm: '0 1px 1px 0 rgba(0, 0, 0, 0.15)',
        'button-primary': `0px 0px 20px 0px ${extendedColors.primary.DEFAULT}, 0px -3px 4px 0px ${extendedColors.primary['100']} inset`,
        'button-secondary': `0px -2px 5px 0px ${secondary['10']} inset;`,
        'cards-primary': `0px 4px 12px -2px ${extendedColors.primary['50']}`,
        glow: `0px 0px 20px 0 ${extendedColors.primary['100']}, inset 0px -3px 4px 0px ${extendedColors.primary.DEFAULT}`,
        // 'outer-glow': `0px 0px 20px 0 ${extendedColors.primary['100']}`,
        // 'inner-glow': `inset 0 -2px 5px 0 rgba(241, 237, 247, 0.15)`,
        'soft-glow': `0px 0px 3px 0 ${extendedColors.primary['100']}, inset 0 1px 1px 0 yellow`,
        // box-shadow: 0 0 40px 0 rgba(242, 71, 13, 0.4);
        ...baseConfig.theme.extend.boxShadow,
      },
    },
  },
  plugins: [...baseConfig.plugins, require('@tailwindcss/typography')],
} satisfies Config;
