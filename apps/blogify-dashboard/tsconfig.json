{
  "extends": "@ps/typescript-config/vite.json",
  "compilerOptions": {
    "sourceMap": true,
    // Linting
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    // Paths
    "rootDir": "./",
    "paths": {
      "@/*": ["./src/*"],
      "@tests/*": ["./tests/*"],
      "@public/*": ["./public/*"]
    }
  },
  "include": ["src", "tests", "tailwind.config.ts", "./vite.config.ts", "vitest.config.ts"],
  "exclude": ["node_modules"]
}
