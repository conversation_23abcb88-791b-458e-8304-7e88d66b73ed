import {
  ISavedWritingSnippetList,
  ISavedWritingSnippetListItem,
} from '@/types/resources/writing-snippets.type';
import { faker } from '@faker-js/faker';

const totalTrushHold = 2;
const fakeSnippets: ISavedWritingSnippetListItem[] = [];
for (let i = 0; i < totalTrushHold; i++) {
  const fakeSnippet: ISavedWritingSnippetListItem = {
    genId: faker.string.uuid(),
    _id: faker.string.uuid(),
    bid: faker.string.uuid(),
    uid: faker.string.uuid(),
    category: faker.helpers.arrayElement(['blogging', 'social media']),
    categoryIcon: faker.image.urlPlaceholder({
      height: 50,
      width: 50,
    }),
    categoryColor: faker.internet.color(),
    language: faker.helpers.arrayElement(['english', 'hindi']),
    tone: faker.helpers.arrayElement(['engaging', 'inspirational']),
    createdAt: faker.date.past().toISOString(),
    __v: faker.number.int(),
    name: faker.lorem.words(),
    content: faker.lorem.paragraphs(),
  };
  fakeSnippets.push(fakeSnippet);
}

export const fakeSavedSnippets: ISavedWritingSnippetList = {
  snippets: fakeSnippets,
  total: totalTrushHold,
};
