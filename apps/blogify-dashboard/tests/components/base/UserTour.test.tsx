import { act, render, renderHook } from '@tests/test-utils';
import { describe, it, vi, expect } from 'vitest';

import useUserTour from '@/hooks/useUserTour';
import UserTour from '@/components/tour-guide/UserTour';

describe('Test UserTour component', () => {
  it('should render without crashing', () => {
    render(<UserTour steps={[]} pageKey="testPageKey" />);
  });
});

describe('Test useUserTour hook', () => {
  it('useUserTour hook should return the correct values', () => {
    const key = 'testPageKey';
    // Mock localStorage.getItem
    vi.mock('Storage.prototype');
    Storage.prototype.getItem = vi.fn().mockImplementationOnce(() => 'true');

    // Mock localStorage.getItem
    Storage.prototype.setItem = vi.fn();
    // render hook
    const { result } = renderHook(() => useUserTour({ key }));
    // assert that the hook returns the correct values
    const { run, stepIndex, handleJoyrideCallback } = result.current;
    expect(run).toBe(false);
    expect(stepIndex).toBe(0);
    expect(localStorage.getItem).toHaveBeenCalled();
    expect(localStorage.getItem).toHaveBeenCalledWith(key);

    // expect(result.current.run).toBe(true);
    // expect(result.current.stepIndex).toBe(0);
    // expect(localStorage.getItem).toHaveBeenCalled();
    // expect(localStorage.getItem).toHaveBeenCalledWith(key);
    // call handleJoyrideCallback with status 'finished'
    act(() => {
      handleJoyrideCallback({
        action: '',
        index: '',
        status: 'finished',
        type: '',
      });
    });
    // assert that localStorage.setItem has been called with the correct arguments
    expect(localStorage.setItem).toHaveBeenCalled();
    expect(localStorage.setItem).toHaveBeenCalledWith(key, 'true');
    expect(result.current.run).toBe(false);
    // call handleJoyrideCallback with status 'skipped'
    act(() => {
      handleJoyrideCallback({
        action: '',
        index: '',
        status: 'skipped',
        type: '',
      });
    });
    // assert that localStorage.setItem has been called with the correct arguments
    expect(localStorage.setItem).toHaveBeenCalled();
    expect(localStorage.setItem).toHaveBeenCalledWith(key, 'true');
    expect(result.current.run).toBe(false);
    // call handleJoyrideCallback with type 'step:after'
    act(() => {
      handleJoyrideCallback({
        action: '',
        index: stepIndex,
        status: '',
        type: 'step:after',
      });
    });
    // assert that current stepIndex has been incremented
    expect(result.current.stepIndex).toBe(1);
    // call handleJoyrideCallback with action 'prev'
    act(() => {
      handleJoyrideCallback({
        action: 'prev',
        index: stepIndex,
        status: '',
        type: 'step:after',
      });
    });
    // assert that current stepIndex has been decremented
    expect(result.current.stepIndex).toBe(-1);

    // call handleJoyrideCallback with type 'error:target_not_found'
    act(() => {
      handleJoyrideCallback({
        action: '',
        index: stepIndex,
        status: '',
        type: 'error:target_not_found',
      });
    });
    // assert that current stepIndex has been incremented
    expect(result.current.stepIndex).toBe(1);
    // call handleJoyrideCallback with action 'prev'
    act(() => {
      handleJoyrideCallback({
        action: 'prev',
        index: stepIndex,
        status: '',
        type: 'error:target_not_found',
      });
    });
    // assert that current stepIndex has been decremented
    expect(result.current.stepIndex).toBe(-1);
  });
});
