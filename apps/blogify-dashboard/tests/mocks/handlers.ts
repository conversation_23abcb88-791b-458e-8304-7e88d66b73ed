import { http, HttpResponse } from 'msw';
// mock data
import { snippetTypes, categories } from '@/constants/writing-snnipets.json';
import { fakeSavedSnippets } from '@tests/savedWritingSnippetsData.faker';
import config from '@/constants/config';

// Define handlers that catch the corresponding requests and returns the mock data.
export const handlers = [
  http.get(`${config.apiUrl}context/app`, () =>
    HttpResponse.json({
      snippetTypes,
      categories,
    })
  ),

  http.get(`${config.apiUrl}writing-snippets`, () => HttpResponse.json(fakeSavedSnippets)),
];
