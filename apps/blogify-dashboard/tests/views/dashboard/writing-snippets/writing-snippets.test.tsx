// import { useQuery } from '@/__mocks__/react-query';
import { renderWithRouter, screen } from '@tests/test-utils';
import WritingSnippets from '@/views/dashboard/writing-snippets';
import { useParams } from 'react-router-dom';
import { vi } from 'vitest';

vi.mock('react-router-dom', async (originalImport) => {
  const original = await originalImport<typeof import('react-router-dom')>();
  return {
    ...original,
    useParams: vi.fn(),
  };
});

const mockFn = vi.fn().mockReturnValue({ category: 'all' });

beforeEach(() => {
  // Mock useParams
  vi.mocked(useParams).mockImplementation(mockFn);
});

afterEach(() => {
  vi.mocked(useParams).mockClear();
});
// Test WritingSnippets component
describe('Test WritingSnippets layout', () => {
  it('should render the loader component when the data is loading', async () => {
    // Render WritingSnippets component
    renderWithRouter(<WritingSnippets />);
    const spinner = screen.getByRole('spinner');
    // Assert
    expect(spinner).toBeInTheDocument();
  });
  it('should render the WritingSnippets component layout correctly', async () => {
    // constants
    const headingText = 'writing snippets';
    // Render WritingSnippets component
    renderWithRouter(<WritingSnippets />);
    // screen.debug();
    const heading = screen.getByRole('heading', { name: headingText });
    // Assert
    expect(mockFn).toHaveBeenCalled();
    expect(heading).toBeInTheDocument();
  });

  it('should change the heading based on dynamic route params', async () => {
    // constants
    const headingText = 'marketing snippets';
    // Mock useParams
    const mockMerkating = vi.fn().mockReturnValue({ category: 'marketing' });
    vi.mocked(useParams).mockImplementation(mockMerkating);
    // Render WritingSnippets component
    renderWithRouter(<WritingSnippets />);
    // screen.debug();
    const heading = screen.getByRole('heading', { name: headingText });
    // Assert
    expect(mockMerkating).toHaveBeenCalled();
    expect(heading).toBeInTheDocument();
  });
});
