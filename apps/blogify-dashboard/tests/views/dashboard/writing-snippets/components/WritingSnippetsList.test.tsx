import { renderWithRouter, screen } from '@tests/test-utils';
import WritingSnippetsProvider from '@/views/dashboard/writing-snippets/components/SnippetsList/WritingSnippetsProvider';
import { snippetTypes, categories } from '@/constants/writing-snnipets.json';
import WritingSnippetsList from '@/views/dashboard/writing-snippets/components/SnippetsList/WritingSnippetsList';

// Test WritingSnippetsList component
describe('Test WritingSnippetsList component', () => {
  it('should render the WritingSnippetsList component correctly', async () => {
    renderWithRouter(
      <WritingSnippetsProvider
        snippets={{
          snippetTypes,
          categories,
        }}
      >
        <WritingSnippetsList />
      </WritingSnippetsProvider>
    );
    // screen.debug();
    const snippets = screen.getAllByRole('heading');
    // Assert
    expect(snippets).toHaveLength(snippetTypes.length);
  });

  it('should render not found message if no snippets provided', async () => {
    renderWithRouter(
      <WritingSnippetsProvider
        snippets={{
          snippetTypes: [],
          categories: [],
        }}
      >
        <WritingSnippetsList />
      </WritingSnippetsProvider>
    );
    const noSnippetsFound = screen.getByText(/no snippets found/i);
    // Assert
    expect(noSnippetsFound).toBeInTheDocument();
  });
});
