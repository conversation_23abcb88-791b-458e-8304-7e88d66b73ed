import { renderWithRouter, screen } from '@tests/test-utils';
import { useNavigate } from 'react-router-dom';
import { vi } from 'vitest';
import WritingSnippetsProvider from '@/views/dashboard/writing-snippets/components/SnippetsList/WritingSnippetsProvider';
import WritingSnippetsFilterHeader from '@/views/dashboard/writing-snippets/components/SnippetsList/WritingSnippetsFilterHeader';
import { snippetTypes, categories } from '@/constants/writing-snnipets.json';

vi.mock('react-router-dom', async (originalImport) => {
  const original = await originalImport<typeof import('react-router-dom')>();
  return {
    ...original,
    useNavigate: vi.fn(),
  };
});

afterEach(() => {
  vi.mocked(useNavigate).mockClear();
});

// Test WritingSnippetsFilterHeader component
describe('Test WritingSnippetsFilterHeader component', () => {
  it('should render the WritingSnippetsFilterHeader component correctly', async () => {
    renderWithRouter(
      <WritingSnippetsProvider
        snippets={{
          snippetTypes,
          categories,
        }}
      >
        <WritingSnippetsFilterHeader />
      </WritingSnippetsProvider>
    );
    // screen.debug();
    const menuItems = screen.getAllByTestId('filter_button');
    // Assert
    expect(menuItems).toHaveLength(categories.length + 1);
  });

  it('WritingSnippetsFilterHeader search should set search term state', async () => {
    // constants
    const searchTerm = 'test';
    // Mock
    // Mock useNavigation
    const navigate = vi.fn();
    const mocUseNavigation = vi.fn().mockReturnValue(navigate);
    vi.mocked(useNavigate).mockImplementation(mocUseNavigation);
    // Render WritingSnippetsFilterHeader component
    const { user } = renderWithRouter(
      <WritingSnippetsProvider
        snippets={{
          snippetTypes,
          categories,
        }}
      >
        <WritingSnippetsFilterHeader />
      </WritingSnippetsProvider>
    );
    // screen.debug();
    const searchInput = screen.getByRole('textbox');
    // Assert
    expect(searchInput).toBeInTheDocument();
    // Act
    await user.type(searchInput, searchTerm);
    // Assert
    expect(searchInput).toHaveValue(searchTerm);
    expect(navigate).toHaveBeenCalled();
    expect(navigate).toHaveBeenCalledTimes(searchTerm.length);
    expect(navigate).toHaveBeenCalledWith('/dashboard/writing-snippets/all');
  });
});
