import { renderWithRouter, screen, waitFor } from '@tests/test-utils';
import SavedSnippetsList from '@/views/dashboard/writing-snippets/saved';
import { fakeSavedSnippets } from '@tests/savedWritingSnippetsData.faker';

describe('Test SavedWritingSnippets layout', () => {
  it('should render the SavedWritingSnippets list correctly', async () => {
    // Render SavedWritingSnippets component
    renderWithRouter(<SavedSnippetsList />);
    const snipper = screen.getByRole('spinner');
    // Assert
    expect(snipper).toBeInTheDocument();
    // Wait for the spinner to disappear
    await waitFor(() => expect(snipper).not.toBeInTheDocument());
    // Assert
    expect(snipper).not.toBeInTheDocument();

    const noSnippetFoundAlert = screen.getByText(/No saved snippets found/i);
    // Assert
    expect(noSnippetFoundAlert).toBeInTheDocument();
    // Wait for the alert to disappear
    await waitFor(() => expect(noSnippetFoundAlert).not.toBeInTheDocument());
    // Assert
    expect(noSnippetFoundAlert).not.toBeInTheDocument();

    const countValue = screen.getByRole('count');
    // Assert
    expect(countValue).toBeInTheDocument();
    expect(countValue).toHaveTextContent(fakeSavedSnippets.snippets.length.toString());
    // screen.debug();
  });
});
