const notify = (title, body, url) => {
  const options = {
    body: body,
    icon: '/images/blogify.svg',
    dir: 'ltr',
    data: { url },
  };

  self.registration.showNotification(title, options);
};

self.addEventListener('push', (e) => {
  try {
    const { title, description, actionLink } = e.data.json();
    notify(title, description, actionLink);
  } catch (e) {
    console.log(`Couldn't push notification:`, e);
  }
});

self.addEventListener('notificationclick', (e) => {
  const urlToOpen = new URL(e.notification.data?.url || '/dashboard', self.location.origin).href;

  const promiseChain = self.clients
    .matchAll({
      type: 'window',
      includeUncontrolled: true,
    })
    .then((windowClients) => {
      let matchingClient;

      for (const windowClient of windowClients) {
        if (windowClient.url === urlToOpen) {
          matchingClient = windowClient;
          break;
        }
      }

      if (matchingClient) {
        return matchingClient.focus();
      } else {
        return self.clients.openWindow(urlToOpen);
      }
    });

  e.waitUntil(promiseChain);
});
