<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="60" cy="60" r="60" fill="url(#94xy7v55sa)"/>
    <mask id="3zro66y09c" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="120" height="120">
        <circle cx="60" cy="60" r="60" fill="url(#a3rgg24ivb)"/>
    </mask>
    <g mask="url(#3zro66y09c)">
        <mask id="8ocxv4az2d" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="26" y="36" width="80" height="92">
            <rect x="35.387" y="35.926" width="72" height="84" rx="8" transform="rotate(7 35.387 35.926)" fill="#fff"/>
        </mask>
        <g mask="url(#8ocxv4az2d)">
            <rect x="35.387" y="35.926" width="72" height="84" rx="8" transform="rotate(7 35.387 35.926)" fill="#fff" fill-opacity=".7"/>
        </g>
        <mask id="pj9n8ho24e" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="14" y="35" width="80" height="92">
            <rect x="13.148" y="43.699" width="72" height="84" rx="8" transform="rotate(-7 13.148 43.7)" fill="#fff"/>
        </mask>
        <g mask="url(#pj9n8ho24e)">
            <rect x="13.148" y="43.699" width="72" height="84" rx="8" transform="rotate(-7 13.148 43.7)" fill="#fff" fill-opacity=".7"/>
        </g>
        <mask id="lr3gvzq1vf" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="24" y="36" width="72" height="84">
            <rect x="24" y="36" width="72" height="84" rx="8" fill="#fff"/>
        </mask>
        <g mask="url(#lr3gvzq1vf)">
            <rect x="24" y="36" width="72" height="84" rx="8" fill="#fff"/>
            <rect x="27" y="117" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="112" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="107" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="102" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="97" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="92" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="87" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="82" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="27" y="77" width="66" height="2" rx="1" fill="#FDF2EE"/>
            <rect x="38" y="69" width="44" height="4" rx="2" fill="#FDF2EE"/>
            <rect x="48" y="63" width="24" height="4" rx="2" fill="#FDF2EE"/>
            <rect x="27" y="39" width="66" height="20" rx="5" fill="#FDF2EE"/>
            <rect x="27" y="39" width="66" height="20" rx="5" fill="#FDF2EE"/>
            <g filter="url(#he48vp4alg)">
                <rect x="24" y="36" width="72" height="84" rx="8" fill="#fff" fill-opacity=".01"/>
            </g>
            <g filter="url(#vm5rhd4dbh)">
                <circle cx="60" cy="81" r="12" fill="#fff"/>
            </g>
            <path d="M60 76v10M55 81h10" stroke="#FAB59E" stroke-width="3" stroke-linecap="round"/>
        </g>
        <g filter="url(#amrjntufgi)">
            <circle cx="60" cy="60" r="60" fill="#D9D9D9" fill-opacity=".01"/>
        </g>
    </g>
    <defs>
        <filter id="he48vp4alg" x="22" y="34" width="76" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="1"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2891_22101"/>
            <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_2891_22101" result="shape"/>
        </filter>
        <filter id="vm5rhd4dbh" x="44" y="69" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2891_22101"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_2891_22101" result="shape"/>
        </filter>
        <filter id="amrjntufgi" x="0" y="-10" width="120" height="130" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-10"/>
            <feGaussianBlur stdDeviation="5"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0.976471 0 0 0 0 0.713726 0 0 0 0 0.623529 0 0 0 0.15 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_2891_22101"/>
        </filter>
        <linearGradient id="94xy7v55sa" x1="60" y1="0" x2="60" y2="120" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FAB59E"/>
            <stop offset="1" stop-color="#FFF8F5"/>
        </linearGradient>
        <linearGradient id="a3rgg24ivb" x1="60" y1="0" x2="60" y2="120" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FAB59E"/>
            <stop offset="1" stop-color="#FFF8F5"/>
        </linearGradient>
    </defs>
</svg>
