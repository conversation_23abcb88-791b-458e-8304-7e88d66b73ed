<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<ellipse cx="30" cy="30" rx="30" ry="30" transform="rotate(180 30 30)" fill="url(#paint0_linear_607_2333)"/>
<path d="M0.500001 30C0.5 13.7076 13.7076 0.5 30 0.499999C46.2924 0.499997 59.5 13.7076 59.5 30C59.5 46.2924 46.2924 59.5 30 59.5C13.7076 59.5 0.500003 46.2924 0.500001 30Z" stroke="#F2470D" stroke-opacity="0.03"/>
<g filter="url(#filter0_di_607_2333)">
<path d="M15 16C15 13.7909 16.7909 12 19 12H33L37.07 14.442C39.322 15.7932 41.2068 17.678 42.558 19.93L45 24V44C45 46.2091 43.2091 48 41 48H19C16.7909 48 15 46.2091 15 44V16Z" fill="white"/>
<path d="M15.25 16C15.25 13.9289 16.9289 12.25 19 12.25H32.9308L36.9414 14.6564C39.1582 15.9865 41.0135 17.8418 42.3436 20.0586L44.75 24.0692V44C44.75 46.0711 43.0711 47.75 41 47.75H19C16.9289 47.75 15.25 46.0711 15.25 44V16Z" stroke="#F2470D" stroke-opacity="0.06" stroke-width="0.5"/>
</g>
<path d="M32.499 34.0001C32.499 33.4478 32.0513 33.0001 31.499 33.0001H23.499C22.9467 33.0001 22.499 33.4478 22.499 34.0001V41.0001C22.499 41.5524 22.9467 42.0001 23.499 42.0001H31.499C32.0513 42.0001 32.499 41.5524 32.499 41.0001V39.1876L36.8391 40.6524C37.1632 40.7618 37.499 40.5207 37.499 40.1787V34.8216C37.499 34.4795 37.1632 34.2384 36.8391 34.3478L32.499 35.8126V34.0001Z" fill="#F2470D"/>
<g filter="url(#filter1_d_607_2333)">
<path d="M33 12L37.07 14.442C39.322 15.7932 41.2068 17.678 42.558 19.93L45 24H36C34.3431 24 33 22.6568 33 21V12Z" fill="url(#paint1_linear_607_2333)" shape-rendering="crispEdges"/>
<path d="M33.25 21V12.4415L36.9414 14.6564C39.1582 15.9865 41.0135 17.8418 42.3436 20.0586L44.5585 23.75H36C34.4812 23.75 33.25 22.5188 33.25 21Z" stroke="#F2470D" stroke-opacity="0.06" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_di_607_2333" x="14" y="11.4" width="32" height="38.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.94902 0 0 0 0 0.278431 0 0 0 0 0.0509804 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_607_2333"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_607_2333" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.994375 0 0 0 0 0.890767 0 0 0 0 0.855625 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_607_2333"/>
</filter>
<filter id="filter1_d_607_2333" x="32.2" y="12" width="13.6" height="13.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.833333 0 0 0 0 0.211063 0 0 0 0 0 0 0 0 0.26 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_607_2333"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_607_2333" result="shape"/>
</filter>
<linearGradient id="paint0_linear_607_2333" x1="30" y1="-1.90735e-06" x2="30" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2470D"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_607_2333" x1="45" y1="12" x2="33" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.17"/>
</linearGradient>
</defs>
</svg>
