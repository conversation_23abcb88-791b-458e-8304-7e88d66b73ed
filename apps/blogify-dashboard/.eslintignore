# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.vscode
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
public
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
src/locales/_build/
src/locales/**/*.js

npm-debug.log*
yarn-debug.log*
yarn-error.log*


# Docz
.docz

# tailwindcss config
postcss.config.js
