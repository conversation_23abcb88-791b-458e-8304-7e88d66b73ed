{"name": "blogify-dashboard", "private": true, "version": "0.1.0", "type": "module", "homepage": "https://blogify.ai/", "scripts": {"dev": "vite", "prebuild": "cp ./index.html ./index.backup.html", "build": "tsc && vite build", "preview": "vite preview", "postinstall": "PUPPETEER_CACHE_DIR=/opt/buildhome/.cache/puppeteer npx puppeteer browsers install chrome", "test": "vitest", "coverage": "vitest run --coverage", "test:ui": "vitest --ui", "eslint": "eslint . --fix", "prettier": "prettier --write . --loglevel silent", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:quick": "tsc && eslint . --fix && pretty-quick --staged", "lint:all": "tsc && eslint . --fix && pretty-quick"}, "dependencies": {"@codemirror/lang-html": "^6.4.6", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^16.3.0", "@mui/material": "^6.4.5", "@mui/x-charts": "^7.27.1", "@ps/common": "workspace:*", "@ps/ui": "workspace:*", "@radix-ui/react-toast": "^1.1.5", "@react-spring/web": "^9.7.2", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.0", "@tiptap/core": "^2.6.6", "@tiptap/extension-bubble-menu": "^2.6.6", "@tiptap/extension-image": "^2.1.12", "@tiptap/extension-link": "^2.0.3", "@tiptap/extension-table": "^2.7.0", "@tiptap/extension-table-cell": "^2.7.0", "@tiptap/extension-table-header": "^2.7.0", "@tiptap/extension-table-row": "^2.7.0", "@tiptap/extension-text-align": "^2.0.0-beta.220", "@tiptap/extension-text-style": "^2.11.2", "@tiptap/extension-underline": "^2.0.0-beta.220", "@tiptap/pm": "^2.0.0-beta.220", "@tiptap/react": "^2.1.8", "@tiptap/starter-kit": "^2.0.0-beta.220", "@uiw/react-codemirror": "^4.21.20", "@visx/geo": "^3.0.0", "@visx/group": "^3.0.0", "@visx/scale": "^3.0.0", "@visx/shape": "^3.0.0", "axios": "^0.27.2", "axios-extensions": "^3.1.6", "class-validator": "^0.14.1", "class-variance-authority": "^0.7.0", "dayjs": "^1.11.10", "easy-peasy": "^5.2.0", "file-saver": "^2.0.5", "formik": "^2.2.9", "lodash": "^4.17.21", "prettier": "^3.5.3", "print-js": "^1.6.0", "react": "^19.0.0", "react-country-state-city": "1.1.11", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-joyride": "^2.6.2", "react-lazy-load-image-component": "^1.6.0", "react-live-chat-loader": "^2.8.2", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-router-dom": "^6.22.3", "react-select": "^5.9.0", "react-svg-worldmap": "^2.0.0-alpha.16", "react-tag-input": "^6.8.1", "react-tooltip": "^5.26.0", "react-type-animation": "^3.2.0", "react-use": "^17.4.0", "recharts": "^2.15.0", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-toc": "^9.0.0", "rxjs": "^7.8.1", "socket.io-client": "^4.7.2", "styled-components": "^5.3.6", "styled-media-query": "2.1.2", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "topojson-client": "^3.1.0", "unsplash-js": "^7.0.19", "web-vitals": "^2.1.4", "yup": "^1.0.2"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@prerenderer/renderer-puppeteer": "^1.2.4", "@prerenderer/rollup-plugin": "^0.3.12", "@ps/eslint-config": "workspace:*", "@ps/types": "workspace:*", "@ps/typescript-config": "workspace:*", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/eslint": "^8.21.1", "@types/eslint-plugin-prettier": "^3.1.3", "@types/file-saver": "^2.0.7", "@types/gtag.js": "^0.0.12", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/node": "^22.4.2", "@types/react": "^19.0.7", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19.0.3", "@types/react-lazy-load-image-component": "^1.6.3", "@types/react-tag-input": "^6.6.1", "@types/styled-components": "^5.1.26", "@types/topojson-client": "^3.1.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-istanbul": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-tailwindcss": "3.17.4", "eslint-plugin-tsdoc": "^0.3.0", "husky": "^8.0.3", "jsdom": "^22.1.0", "msw": "^2.0.2", "postcss": "^8.4.33", "pretty-quick": "^4.0.0", "puppeteer": "^24.2.0", "rollup-plugin-visualizer": "^5.11.0", "sass": "^1.72.0", "tailwindcss": "^3.4.1", "typescript": "^5.5.4", "vite": "^5.0.10", "vite-plugin-mkcert": "^1.17.4", "vite-plugin-pwa": "^0.17.4", "vite-plugin-sitemap": "^0.5.3", "vite-plugin-static-copy": "^1.0.1", "vite-tsconfig-paths": "^4.2.1", "vitest": "^0.34.6", "workbox-window": "^7.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">2%", "not dead", "not ie <= 11", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}