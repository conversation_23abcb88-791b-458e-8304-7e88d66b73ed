// import type { PluginOption } from 'vite';

import { viteStaticCopy } from 'vite-plugin-static-copy';
import { defineConfig } from 'vite';
// import { visualizer } from 'rollup-plugin-visualizer';
import { VitePWA } from 'vite-plugin-pwa';
import { resolve } from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';
import prerender from '@prerenderer/rollup-plugin';
// import sitemap from 'vite-plugin-sitemap';
import mkcert from 'vite-plugin-mkcert';
import react from '@vitejs/plugin-react';
import path from 'path';

const chromeArgs = [
  '--autoplay-policy=user-gesture-required',
  '--disable-background-networking',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-breakpad',
  '--disable-client-side-phishing-detection',
  '--disable-component-update',
  '--disable-default-apps',
  '--disable-dev-shm-usage',
  '--disable-domain-reliability',
  '--disable-extensions',
  '--disable-features=AudioServiceOutOfProcess',
  '--disable-gpu',
  '--disable-hang-monitor',
  '--disable-ipc-flooding-protection',
  '--disable-notifications',
  '--disable-offer-store-unmasked-wallet-cards',
  '--disable-popup-blocking',
  '--disable-print-preview',
  '--disable-prompt-on-repost',
  '--disable-renderer-backgrounding',
  '--disable-setuid-sandbox',
  '--disable-speech-api',
  '--disable-sync',
  '--hide-scrollbars',
  '--ignore-gpu-blacklist',
  '--metrics-recording-only',
  '--mute-audio',
  '--no-default-browser-check',
  '--no-first-run',
  '--no-pings',
  '--no-sandbox',
  '--no-zygote',
  '--password-store=basic',
  '--use-gl=swiftshader',
  '--use-mock-keychain',
  '--remote-debugging-port=9222',
];

const dynamicRoutes = ['/login/']; // , '/signup/'

const buildBranchName = process.env.CF_PAGES_BRANCH || 'main';
const isProd = buildBranchName === 'main';
// const bases: Record<string, string> = {
//   main: 'https://blogify.ai',
//   develop: 'https://test.blogify.ai',
// };
// const base = bases[buildBranchName];

const indexHTMLBackupCopyConfig = {
  src: path.resolve(__dirname, './build/index.backup.html'),
  rename: 'index.html',
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    VitePWA({
      manifest: require('./src/manifest.json'),
      includeAssets: ['images/**/*'],
      registerType: 'autoUpdate',
      injectRegister: 'inline',
      selfDestroying: true,
      // devOptions: { enabled: true },
    }),
    prerender({
      routes: dynamicRoutes,
      rendererOptions: {
        renderAfterDocumentEvent: 'app-ready',
        args: chromeArgs,
      },
    }),
    viteStaticCopy({
      targets: [
        { ...indexHTMLBackupCopyConfig, dest: './dashboard' },
        { ...indexHTMLBackupCopyConfig, dest: './payment' },
        { ...indexHTMLBackupCopyConfig, dest: './reset-password' },
        { ...indexHTMLBackupCopyConfig, dest: './forgot-password' },
        { ...indexHTMLBackupCopyConfig, dest: './accept-invitation' },
        { ...indexHTMLBackupCopyConfig, dest: './oauth2-consent' },
        { ...indexHTMLBackupCopyConfig, dest: './developer' },
        ...(isProd
          ? []
          : [
              {
                src: path.resolve(__dirname, './robots.txt.staging'),
                rename: 'robots.txt',
                dest: './',
              },
            ]),
      ],
    }),
    // visualizer({
    //   filename: './build/stats.html',
    //   template: 'flamegraph',
    //   brotliSize: true,
    //   gzipSize: true,
    // }) as PluginOption | any,
    // sitemap({ hostname: base, outDir: 'build', exclude: ['/dashboard'], generateRobotsTxt: false }),
    tsconfigPaths(),
    mkcert(),
    react(),
  ],
  optimizeDeps: {
    exclude: ['react-icons'],
  },
  server: {
    port: 7000,
  },
  define: {
    'process.env': {},
  },
  preview: {
    port: 7000,
  },
  build: {
    outDir: './build',
    emptyOutDir: true,
    sourcemap: false,
    // modulePreload: {
    //   resolveDependencies: () => [],
    // },
    rollupOptions: {
      input: {
        main: resolve(__dirname, './index.html'),
        backup: resolve(__dirname, './index.backup.html'),
      },
      output: {
        manualChunks: {
          react: ['react', 'react-dom', 'react-router-dom'],
          // TODO: Uncomment once issue https://github.com/vitejs/vite/issues/5189 is resolved.
          // tiptap: [
          //   '@tiptap/react',
          //   '@tiptap/starter-kit',
          //   '@tiptap/extension-text-align',
          //   '@tiptap/extension-underline',
          //   '@tiptap/extension-heading',
          //   '@tiptap/extension-image',
          //   '@tiptap/extension-link',
          // ],
          // codemirror: ['@codemirror/lang-html'],
          // codemirrorComp: ['@uiw/react-codemirror'],
          // prettier: ['prettier/standalone'],
        },
      },
    },
  },
});
