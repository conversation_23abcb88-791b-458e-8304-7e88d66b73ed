<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="fragment" content="!" />
    <meta name="robots" content="noodp, noydir, index, follow, archive" />
    <meta name="robots" content="max-image-preview:standard" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#ffffff" />
    <!-- <meta name="google-site-verification" content="" /> -->
    <meta name="author" content="Blogify" />
    <!-- <meta property="fb:app_id" content="" /> -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/pwa/ios/180.png" />
    <link
      rel="apple-touch-startup-image"
      href="/images/pwa/ios/512.png"
      media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
    />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Overridden by react-helmet (data-rh="true") in JS environment with Seo.js, if no JS, these are used-->
    <link rel="canonical" href="https://blogify.ai/" data-rh="true" />

    <meta property="og:url" content="https://blogify.ai/" data-rh="true" />
    <meta
      property="og:title"
      content="Turn anything into Blog with AI | YouTube Video to Blog AI"
      data-rh="true"
    />
    <meta
      property="og:image"
      content="https://images.blogify.ai/assets/turn-anything-into-a-blog.png"
      data-rh="true"
    />
    <meta
      property="og:image:alt"
      content="Turn any Media to SEO optimized blog | Repurpose your content"
      data-rh="true"
    />
    <meta
      property="og:description"
      content="From YouTube videos, Podcasts to Pdf and audio files- turn anything into SEO optimized blogs with Blogify. Start creating engaging blogs within minutes with AI!"
      data-rh="true"
    />
    <!-- end -->
    <meta property="og:site_name" content="Blogify" />
    <meta property="og:type" content="website" />

    <script>
      window.global = window;
      window.youtubeVideos = new Map();
    </script>

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <title>Turn anything into Blog with AI | YouTube Video to Blog AI</title>

    <style>
      /* Reset css */
      *,
      *::before,
      *::after {
        -webkit-box-sizing: inherit;
        box-sizing: inherit;
      }

      html {
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        line-height: 1.5;
        /* -webkit-text-size-adjust: 100%; */
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: 'Open Sans', sans-serif;
        -moz-osx-font-smoothing: grayscale;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.04);
      }

      hr {
        height: 0;
      }

      pre,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0;
        font-family: inherit;
      }

      abbr[title] {
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      b,
      strong {
        font-weight: bolder;
      }

      sub,
      sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      sub {
        bottom: -0.25em;
      }

      sup {
        top: -0.5em;
      }

      summary {
        display: list-item;
      }

      button,
      input,
      optgroup,
      select,
      textarea {
        font-family: inherit;
        font-size: 100%;
        margin: 0;
      }

      button,
      select {
        text-transform: none;
        cursor: pointer;
      }

      ::-webkit-file-upload-button {
        -webkit-appearance: button;
        -moz-appearance: button;
        appearance: button;
        font: inherit;
      }

      ::-moz-selection,
      ::-moz-selection {
        background: rgba(0, 0, 0, 0.15);
      }

      ::-moz-selection,
      ::selection {
        background: rgba(0, 0, 0, 0.15);
      }

      :root {
        -moz-tab-size: 4;
        -o-tab-size: 4;
        tab-size: 4;
      }

      button,
      [type='button'],
      [type='reset'],
      [type='submit'] {
        -webkit-appearance: button;
        -moz-appearance: button;
        appearance: button;
      }

      button::-moz-focus-inner,
      [type='button']::-moz-focus-inner,
      [type='reset']::-moz-focus-inner,
      [type='submit']::-moz-focus-inner {
        border-style: none;
        padding: 0;
      }

      button:-moz-focusring,
      [type='button']:-moz-focusring,
      [type='reset']:-moz-focusring,
      [type='submit']:-moz-focusring {
        outline: 1px dotted ButtonText;
      }

      fieldset {
        padding: 0.35em 0.75em 0.625em;
      }

      legend {
        padding: 0;
      }

      progress {
        vertical-align: baseline;
      }

      [type='number']::-webkit-inner-spin-button,
      [type='number']::-webkit-outer-spin-button {
        height: auto;
      }

      [type='search'] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        outline-offset: -2px;
        border-radius: 0;
      }

      [type='search']::-webkit-search-cancel-button,
      [type='search']::-webkit-search-decoration {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
      }

      a {
        cursor: pointer;
        color: unset;
        text-decoration: none;
        -webkit-transition: color 0.05s ease;
        -o-transition: color 0.05s ease;
        transition: color 0.05s ease;
      }

      img {
        vertical-align: middle;
      }

      input {
        color: unset;
        outline: none !important;
        padding: 4px 12px;
        background: white;
      }

      [type='radio'],
      [type='checkbox'] {
        cursor: pointer;
      }

      label,
      select {
        color: unset;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      label {
        cursor: pointer;
      }

      #root {
        max-width: 100vw;
      }

      .grecaptcha-badge {
        visibility: hidden;
      }
      .grecaptcha-badge.visible {
        visibility: visible;
      }
    </style>
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-WBZHMFS"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <noscript>
      <img
        height="1"
        width="1"
        style="display: none"
        src="https://www.facebook.com/tr?id=1449392622470908&ev=PageView&noscript=1"
      />
    </noscript>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Facebook Pixel Code -->
    <script defer>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '%VITE_META_PIXEL_ID%');
      fbq('track', 'PageView');
    </script>
    <!-- Google reCAPTCHA -->
    <script
      fetchprority="high"
      async
      type="text/javascript"
      src="https://www.google.com/recaptcha/enterprise.js?render=%VITE_RECAPTCHA_SITE_KEY%"
    ></script>
    <noscript>
      <img
        src="https://www.facebook.com/tr?id=%VITE_META_PIXEL_ID%&ev=PageView&noscript=1"
        style="display: none"
        height="1"
        width="1"
      />
    </noscript>
    <!-- End Facebook Pixel Code -->

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HG6C8JR5MZ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());
      gtag('config', 'G-HG6C8JR5MZ');
    </script>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-WBZHMFS');
    </script>
    <!-- End Google Tag Manager -->
  </body>
</html>
