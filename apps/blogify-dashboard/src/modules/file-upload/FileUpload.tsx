import { FiUploadCloud } from 'react-icons/fi';
import React from 'react';

import { Progress } from '@ps/ui/components/progress';
import { Button } from '@ps/ui/components/button';
import useFileUpload from '@/modules/file-upload/useFileUpload';

export const FileUpload: React.FC<{
  supportedFormats: string;
  maxFileSize?: number;
  className?: string;
  multiple?: boolean;
  error?: string;
}> = ({
  className,
  maxFileSize = 15 * 1024 * 1024,
  supportedFormats,
  error: parentError,
  multiple,
}) => {
  const { preventDefault, removeFiles, onChange, onDrop, error } = useFileUpload();

  return (
    <div onDragEnter={preventDefault} onDragOver={preventDefault} onDrop={onDrop}>
      <label
        className={`flex w-full flex-col justify-between rounded-md p-6 text-center hover:bg-gray-50 ${className}`}
      >
        <input
          accept={supportedFormats}
          onClick={() => !multiple && removeFiles()}
          onChange={onChange}
          className="hidden"
          type="file"
          multiple
        />
        <div className="h-0" />

        <div className="flex flex-col items-center">
          <FiUploadCloud className="text-primary" size={40} />
          <p className="mt-2 text-md">
            Drag & drop your file here or browse file from your storage
          </p>
          <Button asChild className="mt-2 min-w-32" variant="secondary">
            <div>Browse Files</div>
          </Button>
          <p className="mt-1 h-4 text-xs text-red4">{parentError || error}</p>
        </div>

        <p className="mt-1 text-sm text-gray9">
          Supported Formats: {supportedFormats}, Maximum size {maxFileSize / 1024 / 1024} MB.
        </p>
      </label>
    </div>
  );
};

export const FileUploadProgress = ({ className }: { className?: string }) => {
  const { currentlyUploading, progress } = useFileUpload();

  return (
    <div className={`flex size-full flex-col justify-between p-6 text-center ${className}`}>
      <div className="h-0" />

      <div className="flex flex-col items-center">
        <FiUploadCloud className="text-primary" size={40} />
        <p className="mt-2 text-md font-medium">Uploading your file...</p>
        <Progress className="mt-4 max-w-80" value={progress} />
        <p className="mt-1 h-5 text-xs">{currentlyUploading}</p>
      </div>

      <p className="mt-5 text-sm text-gray9">
        Please wait for the file to upload before submitting the form.
      </p>
    </div>
  );
};

export const FileUploadComplete = ({ className }: { className?: string }) => {
  const { removeFiles, files } = useFileUpload();

  return (
    <div className={`flex w-full flex-col p-6 text-center flex-center ${className}`}>
      <img className="size-14" src="/images/icons/uploaded-file.svg" alt="upload" />
      <p className="mt-2 text-md font-medium">{files[0]?.name}</p>
      <p className="mt-2 text-sm text-gray9">
        {((files[0]?.size || 0) / 1024 / 1024).toFixed(2)} MB
      </p>
      <Button
        className="mt-2 min-w-32 text-red4"
        onClick={removeFiles}
        variant="secondary"
        type="button"
      >
        Remove File
      </Button>
    </div>
  );
};
