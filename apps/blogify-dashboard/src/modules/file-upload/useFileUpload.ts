import type { ChangeEvent, DragEvent } from 'react';

import { createContext, useContext } from 'react';

export type FileType = {
  name: string;
  size: number;
  url: string;
  type: string;
  resolution: string;
};

export interface FileUploadContextType {
  currentlyUploading: string;
  isUploading: boolean;
  progress: number;
  files: FileType[];
  error: string;

  preventDefault: (e: DragEvent<HTMLDivElement>) => void;
  removeFiles: Func;
  onChange: (ev: ChangeEvent<HTMLInputElement>) => void;
  onDrop: (e: DragEvent<HTMLDivElement>) => void;
}

const defaultFn = () => {};
const defaultContext: FileUploadContextType = {
  currentlyUploading: '',
  isUploading: false,
  progress: 0,
  files: [],
  error: '',

  preventDefault: defaultFn,
  removeFiles: defaultFn,
  onChange: defaultFn,
  onDrop: defaultFn,
};

const FileUploadContext = createContext<FileUploadContextType>(defaultContext);
FileUploadContext.displayName = 'FileUploadContext';

const useFileUpload = () => {
  const context = useContext(FileUploadContext);

  if (context === undefined) {
    throw new Error('useFileUpload must be used within a FileUploadProvider.');
  }

  return context;
};

export default useFileUpload;
export { FileUploadContext };
