import type { FileUploadContextType, FileType } from './useFileUpload';
import type { ChangeEvent, DragEvent, FC } from 'react';

import { useState } from 'react';

import { getDataUrl, uploadFile } from '@/services/upload';

import { FileUploadContext } from './useFileUpload';

const ONE_MB = 1024 * 1024;
const FIFTEEN_MB = 15 * ONE_MB;

const DOCUMENT_MIME_TYPES = [
  // 'text/csv', // .csv
  'text/plain', // .txt
  'application/pdf', // .pdf
  // 'application/rtf', // .rtf
  'application/msword', // .doc
  // 'application/vnd.ms-excel', // .xls
  // 'application/vnd.ms-powerpoint', // .ppt
  // 'application/vnd.oasis.opendocument.text', // .odt
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  // 'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
];

const getImageResolution = async (file: File, tempUrl: string): Promise<string> => {
  if (file.type.startsWith('image/')) {
    return new Promise<string>((resolve) => {
      const img = new Image();
      img.onload = () => resolve(`${img.width}x${img.height}`);
      img.src = tempUrl;
    });
  }
  return '';
};

type Props = {
  children: React.ReactNode | ((_: FileUploadContextType) => React.ReactNode);
  folder: string;
  type: 'image' | 'video' | 'audio' | 'document';
  maxSize?: number;
  onPreview?: React.Dispatch<FileType[]>;
  onUploadStart?: React.Dispatch<void>;
  onUploadComplete?: React.Dispatch<FileType[]>;
};
const FileUploadProvider: FC<Props> = ({
  folder,
  children,
  type,
  maxSize = FIFTEEN_MB,
  onPreview,
  onUploadStart,
  onUploadComplete,
}) => {
  const [currentlyUploading, setCurrentlyUploading] = useState<string>('');
  const [isUploading, toggleUploading] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [files, setFiles] = useState<FileType[]>([]);
  const [error, setError] = useState<string>('');

  const onSetFiles = async (_fileList: FileList | null) => {
    if (!_fileList?.length) return;
    const _files = Array.from(_fileList);

    if (_files.some((f) => f.size > maxSize)) {
      return setError(`Maximum file size is ${maxSize / 1024 / 1024} MB.`);
    }

    if (type === 'document') {
      if (!_files.every((f) => DOCUMENT_MIME_TYPES.includes(f.type))) {
        return setError(`Invalid file format.`);
      }
    } else {
      if (!_files.every((f) => f.type.startsWith(type))) {
        return setError(`Invalid file format.`);
      }
    }

    if (onUploadStart) {
      onUploadStart();
    }
    toggleUploading(true);
    setProgress(0);

    // Generate Preview
    for (const f of _files) {
      const tempUrl = await getDataUrl(f);
      const newFile: FileType = {
        name: f.name,
        size: f.size,
        url: tempUrl,
        type: f.type,
        resolution: await getImageResolution(f, tempUrl),
      };

      setFiles((oldFiles) => {
        const newFiles = [...oldFiles, newFile];
        if (onPreview) onPreview(newFiles);
        return newFiles;
      });
    }

    // Upload
    const totalFiles = _files.length;
    for (let i = 0; i < totalFiles; i += 1) {
      const f = _files[i];
      setCurrentlyUploading(f.name);
      const s3Url = await uploadFile(f, folder, (p) =>
        setProgress((i / totalFiles) * 100 + (1 / totalFiles) * p)
      );
      setFiles((oldFiles) => {
        const newFiles = oldFiles.map((oldFile) => {
          if (oldFile.name === f.name) oldFile.url = s3Url;
          return oldFile;
        });
        if (i === totalFiles - 1 && onUploadComplete && s3Url) {
          onUploadComplete(newFiles);
        }
        return newFiles;
      });
    }

    setCurrentlyUploading('');
    toggleUploading(false);
  };

  const onChange = (ev: ChangeEvent<HTMLInputElement>) => {
    onSetFiles(ev.target.files);
  };

  const onDrop = (ev: DragEvent<HTMLDivElement>) => {
    ev.preventDefault();
    onSetFiles(ev.dataTransfer.files);
  };

  const preventDefault = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const removeFiles = () => {
    setFiles([]);
    setProgress(0);
    toggleUploading(false);
    setCurrentlyUploading('');
    if (onUploadComplete) onUploadComplete([]);
  };

  const value = {
    currentlyUploading,
    isUploading,
    progress,
    files,
    error,

    preventDefault,
    removeFiles,
    onChange,
    onDrop,
  };

  return (
    <FileUploadContext.Provider value={value}>
      <>{typeof children === 'function' ? children(value) : children}</>
    </FileUploadContext.Provider>
  );
};

export default FileUploadProvider;
