// بسم الله الرحمن الرحيم

type Keyword = string;
export type KeywordData = [count: number, minimum: number, maximum: number];

export type SEOAnalysis = Record<
  'title' | 'meta' | 'h1' | 'h2' | 'h3' | 'content',
  Record<Keyword, KeywordData>
>;

export const EmptySEOAnalysis: SEOAnalysis = {
  title: {},
  meta: {},
  h1: {},
  h2: {},
  h3: {},
  content: {},
};

export type HeadingsSuggestion = Record<
  `h${1 | 2 | 3}`,
  {
    keywords: string[];
    suggestions: Array<[text: string, score: number]>;
  }
>;
