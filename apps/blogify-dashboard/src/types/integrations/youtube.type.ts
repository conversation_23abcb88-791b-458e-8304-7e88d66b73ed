import type { YoutubeData } from '../resources/youtube-contents.type';

type YoutubeVideoThumbnail = { url: string; width: number; height: number };

export type YoutubeVideoThumbnailQuality = 'default' | 'standard' | 'medium' | 'high' | 'maxres';

export interface YoutubeBase {
  id: string;
  kind: string;
  etag: string;
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      default: YoutubeVideoThumbnail;
      standard?: YoutubeVideoThumbnail;
      medium?: YoutubeVideoThumbnail;
      high?: YoutubeVideoThumbnail;
      maxres?: YoutubeVideoThumbnail;
    };
    publishedAt: string;
    localized: { title: string; description: string };
  };
}

export interface YoutubeChannel extends YoutubeBase {
  snippet: YoutubeBase['snippet'] & { customUrl: string };
  contentDetails: {
    relatedPlaylists: {
      uploads: string;
      likes: string;
    };
  };
  brandingSettings: {
    channel: { title: string };
    image: { bannerExternalUrl: string };
  };
  statistics: {
    hiddenSubscriberCount: boolean;
    subscriberCount: string;
    videoCount: string;
    viewCount: string;
  };
}

export interface YoutubePlaylist extends YoutubeBase {
  title: string;
  snippet: YoutubeBase['snippet'] & {
    channelId: string;
    channelTitle: string;
  };
  contentDetails: {
    itemCount: number;
  };
}

export interface YoutubeVideo extends YoutubeBase {
  title: string;
  snippet: YoutubePlaylist['snippet'] & {
    tags: string[];
    categoryId: string;
    liveBroadcastContent: string;
    defaultLanguage: string;
    defaultAudioLanguage: 'en' | string;
    channelId: string;
    channelTitle: string;
  };
  statistics: {
    viewCount: string;
    likeCount: string;
    favoriteCount: string;
    commentCount: string;
  };
  contentDetails: {
    caption: 'true' | 'false';
    contentRating: Record<any, any>;
    definition: 'hd' | string;
    dimension: '2d' | string;
    duration: string;
    licensedContent: boolean;
    projection: 'rectangular' | string;
  };
  status: {
    uploadStatus: 'deleted' | 'failed' | 'processed' | 'rejected' | 'uploaded';
    privacyStatus: 'public' | 'private' | 'unlisted';
    license: 'creativeCommon' | 'youtube';
    embeddable: boolean;
    publicStatsViewable: boolean;
    madeForKids: boolean;
    selfDeclaredMadeForKids: boolean;
  };
  // Custom
  data?: YoutubeData;
}

const emptyThumbnail = { url: '', width: 0, height: 0 };
const emptyBase: YoutubeBase = {
  id: '',
  kind: '',
  etag: '',
  snippet: {
    title: '',
    description: '',
    thumbnails: {
      default: emptyThumbnail,
      standard: emptyThumbnail,
      medium: emptyThumbnail,
      high: emptyThumbnail,
      maxres: emptyThumbnail,
    },
    publishedAt: '',
    localized: { title: '', description: '' },
  },
};

export const emptyChannel: YoutubeChannel = {
  ...emptyBase,
  snippet: {
    ...emptyBase.snippet,
    customUrl: '',
  },
  contentDetails: {
    relatedPlaylists: {
      uploads: '',
      likes: '',
    },
  },
  brandingSettings: {
    channel: { title: '' },
    image: { bannerExternalUrl: '' },
  },
  statistics: {
    hiddenSubscriberCount: false,
    subscriberCount: '',
    videoCount: '',
    viewCount: '',
  },
};

export const emptyVideo: YoutubeVideo = {
  ...emptyBase,
  title: '',
  snippet: {
    ...emptyBase.snippet,
    tags: [],
    categoryId: '',
    liveBroadcastContent: '',
    defaultLanguage: '',
    defaultAudioLanguage: 'en',
    channelId: '',
    channelTitle: '',
  },
  statistics: {
    viewCount: '',
    likeCount: '',
    favoriteCount: '',
    commentCount: '',
  },
  contentDetails: {
    caption: 'false',
    contentRating: {},
    definition: 'hd',
    dimension: '2d',
    duration: '',
    licensedContent: false,
    projection: 'rectangular',
  },
  status: {
    uploadStatus: 'uploaded',
    privacyStatus: 'public',
    license: 'youtube',
    embeddable: true,
    publicStatsViewable: true,
    madeForKids: false,
    selfDeclaredMadeForKids: false,
  },
};

export interface YoutubeResponse<T> {
  kind: 'youtube#searchListResponse' | string;
  etag: string;
  nextPageToken: string;
  prevPageToken: string;
  regionCode: 'BD';
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
  items: T[];
}
