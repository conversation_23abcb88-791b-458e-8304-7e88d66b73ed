export type StatsAPIResponse = {
  blogCount: { thisMonth: number; lifeTime: number };
  website: { count: number; blogCount: number };
  credits: { remaining: number; total: number };
  wallet: { currency: string; balance: number; lastWithdraw: number };
};

const emptyStats: StatsAPIResponse = {
  blogCount: { thisMonth: 0, lifeTime: 0 },
  website: { count: 0, blogCount: 0 },
  credits: { remaining: 0, total: 0 },
  wallet: { currency: 'USD', balance: 0, lastWithdraw: 0 },
};

export default emptyStats;
