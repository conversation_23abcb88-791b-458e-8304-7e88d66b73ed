export interface Plan {
  subscriptionStatus: string;
  subscriptionPlan: string;
  subscriptionPrice: number;
  isLatestPlan: boolean;
  packages: Package[];
}

interface Package {
  id: string;
  name: string;
  price: number;
  currency: string;
  limit: Limit;
  interval: string;
  isLatest: boolean;
  isActive: boolean;
}

interface Limit {
  CREDITS: number;
  CREDIT_PRICE: number;
  MAX_UPLOAD_SIZE_IN_BYTES: number;
  MAX_DURATION_IN_SECONDS: number;
}
