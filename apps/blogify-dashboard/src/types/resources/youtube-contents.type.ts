export type YoutubeContentTypes =
  | 'title'
  | 'description'
  | 'tag'
  | 'hashtag'
  | 'chapters'
  | 'social-post'
  | 'social_post_facebook'
  | 'social_post_twitter'
  | 'social_post_linkedin'
  | 'thumbnail'
  | 'short'
  | 'transcript'
  | 'summary';

export type YoutubeShort = {
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  downloadUrl?: string;
  downloadUrlExpiry?: number;
};

export type YoutubeVideoContent<T = string> = {
  _id: string;
  contentData: T;
  contentType: YoutubeContentTypes;
  status: 'generated' | 'generating' | 'generation_failed';
  failReason: string;
  isFavorite: boolean;
  isDeleted: boolean;
};

export type YoutubeVideoChapter = {
  title: string;
  timestamp: string;
};

export type YoutubeVideoTranscript = {
  startTime: number;
  endTime: number;
  text: string;
};

export type YoutubeVideoContents = {
  titles?: YoutubeVideoContent[];
  descriptions?: YoutubeVideoContent[];
  chapters?: YoutubeVideoChapter[];
  tags?: YoutubeVideoContent[];
  hashtags?: YoutubeVideoContent[];

  // TODO: Separate it from this data;
  thumbnails?: YoutubeVideoContent[];
  socialPosts?: YoutubeVideoContent[];
  summary?: string;
  transcript?: YoutubeVideoTranscript[];
};

export type YoutubeVideoContentOverview = {
  title: string;
  description: string;
  tags: string[];
  hashtags: string[];
  chapters: YoutubeVideoChapter[];
  thumbnail: string;
};

export interface YoutubeData {
  generateTypes: YoutubeContentTypes[];
  generateTypesInProgress: YoutubeContentTypes[];
  chapters: YoutubeVideoChapter[];
  language: string;
  summary: string;
}

export interface YoutubeContentGenerateRequest {
  generateTypes: YoutubeContentTypes[];
  language?: string;
  tone?: string;
}
