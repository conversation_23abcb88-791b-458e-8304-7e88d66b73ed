import type { Base } from '@/types/resources/base.type';
import type { User } from '@/types/resources/user.type';

export interface Notification extends Base {
  title: string;
  description: string;
  actionTitle?: string;
  actionLink?: string;

  // Relations
  business?: any;
  user?: User;
}

const defaultNotification: Notification = {
  _id: '',
  title: '',
  description: '',
};

export default defaultNotification;
