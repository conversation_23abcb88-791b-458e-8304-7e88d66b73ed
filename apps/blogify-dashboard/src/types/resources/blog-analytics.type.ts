import type { DeviceType } from './analytics.type';

export interface UaInfo {
  platform: Record<DeviceType, number>;
  vendor: Record<string, number>;
  browser: Record<string, number>;
  os: Record<string, number>;
}

export interface GeoInfo {
  country: Record<string, number>;
  city: Record<string, number>;
  timezone: Record<string, number>;
  latlng: [number, number];
}

export interface BlogAnalytics {
  view: UaInfo & GeoInfo;
  click: UaInfo & GeoInfo;
}

const emptyAnalytics: BlogAnalytics = {
  view: {
    platform: {
      mobile: 0,
      laptop: 0,
      desktop: 0,
      other: 0,
    },
    vendor: {},
    browser: {},
    os: {},
    country: {},
    city: {},
    timezone: {},
    latlng: [0, 0],
  },
  click: {
    platform: {
      mobile: 0,
      laptop: 0,
      desktop: 0,
      other: 0,
    },
    vendor: {},
    browser: {},
    os: {},
    country: {},
    city: {},
    timezone: {},
    latlng: [0, 0],
  },
};

export default emptyAnalytics;
