export interface IWritingSnippetCategory {
  name: string;
  iconUrl: string;
  colorScheme: string;
}

export type IWritingSnippetExtraField = {
  fieldName: string;
  placeholder?: string;
};
export interface IWritingSnippetType {
  title: string;
  description: string;
  category: IWritingSnippetCategory;
  promptHint?: string;
  extraFields: IWritingSnippetExtraField[];
  model: string;
  iconUrl?: string;
  visibility: boolean;
  displayTone: boolean;
  displayPerspective: boolean;
  displayOutputLanguage: boolean;
  displayInputLanguage: boolean;
}

export interface IWiringSnippetsAndCategories {
  snippetTypes: IWritingSnippetType[];
  categories: IWritingSnippetCategory[];
}

export interface IWritingSnippetsGenerateForm {
  title: string;
  model?: string;
  language?: string;
  inputLanguage?: string;
  perspective?: string;
  tone: string;
  prompt: string;
  category: string;
}

export interface IGeneratedWritingSnippet {
  genId: string;
  output: string;
  output1: string;
}
export interface ISaveWritingSnippet {
  genId: string;
  name: string;
  content: string;
}
export interface ISavedWritingSnippetListItem extends ISaveWritingSnippet {
  _id: string;
  bid: string;
  uid: string;
  category: string;
  categoryIcon: string;
  categoryColor: string;
  language: string;
  tone: string;
  createdAt: string;
  __v: number;
}
export interface ISavedWritingSnippetList {
  snippets: ISavedWritingSnippetListItem[];
  total: number;
}
