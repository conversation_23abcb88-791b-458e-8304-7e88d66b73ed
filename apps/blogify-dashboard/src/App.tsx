import type { Settings } from '@/types/resources/settings.type';
import type { Package } from '@/types/misc/package.type';

// import { LiveChatLoaderProvider, Intercom } from 'react-live-chat-loader';
import { useState, useEffect } from 'react';
import { ReactQueryDevtools } from 'react-query/devtools';

import { useStoreActions, useStoreState } from '@/store';
// import { setUserForCustomerSupport } from '@/services/support';
import { initSupport } from '@/services/support';
import { parseQuery } from '@/utils';
import { cacheSet } from '@/utils/localStorageCache';
import { API } from '@/services/api';
import * as FirstPromoter from '@/services/analytics/first-promoter';
import * as Facebook from '@/services/analytics/facebook';
import SocketService from '@/services/socket';
// import config from '@/constants/config';

import Spinner from './components/misc/Spinner';

type Context = {
  context: { settings: Settings[] };
  // packages?: Package[];
};

const App = () => {
  const [isAppReady, toggleAppReady] = useState(false);

  const isAuthenticated = useStoreState((s) => s.user.isAuthenticated);
  const user = useStoreState((s) => s.user.current);

  const { setPackages, setSettings } = useStoreActions((a) => a.context);
  const fetchUser = useStoreActions((a) => a.user.fetch);

  const { irclickid } = parseQuery();

  const setupContext = ({ context }: Context) => {
    if (context?.settings) {
      setSettings(
        context.settings.reduce((obj: Record<string, any>, s: Settings) => {
          obj[s.key] = s.value;
          return obj;
        }, {})
      );
    }
  };

  const onAppReady = () => {
    const { token } = parseQuery();
    toggleAppReady(true);
    FirstPromoter.init();
    initSupport(user, token);
    // TODO: Refactor Later
    API.fetch<Context['context']>('/context').then((context) => {
      if (context) setupContext({ context });
    });
    if (user?._id) {
      SocketService.connect();
      Facebook.identify(user);
      API.fetch<Package[]>('/payments/get-packages').then((packages) => {
        if (packages) setPackages(packages);
      });
    }
  };

  useEffect(() => {
    if (irclickid) {
      cacheSet('irClickId', irclickid, 40 * 24 * 60); // 40 days
    }
    if (isAuthenticated) {
      fetchUser().then(onAppReady);
    } else {
      onAppReady();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user._id]);

  return isAppReady ? (
    // <LiveChatLoaderProvider
    //   providerKey={import.meta.env.VITE_INTERCOM}
    //   onReady={() => setUserForCustomerSupport(user)}
    //   provider="intercom"
    // >
    <ReactQueryDevtools initialIsOpen={false} />
  ) : (
    //   {config.isProd && <Intercom />}
    // </LiveChatLoaderProvider>
    <DelayedSpinner />
  );
};

const DelayedSpinner = () => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setShow(true), 700);
    return () => clearTimeout(timeout);
  }, []);

  if (!show) return null;

  return (
    <div className="mt-[2vh] flex flex-center sm:mt-[6vh]">
      <Spinner className="block text-[64px] xs:text-[38px]" />
    </div>
  );
};

export default App;
