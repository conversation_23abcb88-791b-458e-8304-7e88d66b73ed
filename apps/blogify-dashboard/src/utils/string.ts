const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

const slugify = (string: string) =>
  string
    .trim()
    // Remove html tags
    .replace(/<(?:.|\n)*?>/gm, '')
    // Remove special characters
    .replace(/[!\"#$%&'\(\)\*\+,\/:;<=>\?\@\[\\\]\^`\{\|\}~]/g, '')
    // Replace dots and spaces with a short dash
    .replace(/(\s|\.)/g, '-')
    // Replace multiple dashes with a single dash
    .replace(/-{2,}/g, '-')
    // Replace long dash with two short dashes
    .replace(/—/g, '--')
    // Make the whole thing lowercase
    .toLowerCase();

export { capitalize, slugify };
