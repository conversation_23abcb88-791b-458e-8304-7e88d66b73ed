import type { BlogSourceName } from '@ps/types';

import config from '@/constants/config';

function getVideoIdFromYouTubeUrl(url: string): string {
  const youtubeUrl = url.replace(/^(?:https?:\/\/)?(?:www\.)?/i, '');

  const patterns = [
    /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([\w-]{11})/i,
    /youtube\.com\/live\/([\w-]{11})/i,
  ];

  const match = patterns.map((pattern) => youtubeUrl.match(pattern)).find(Boolean);
  return match ? match[1] : '';
}

function getVideoIdFromVimeoUrl(url: string): string | null {
  const regex =
    /(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com\/)(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|video\/|)(\d+)(?:$|\/|\?)/;

  const match = url.match(regex);
  return match && match[2] ? match[2] : null;
}

function getPodBeanIdFromUrl(url: string): string | null {
  const regex = /\/pb-([a-zA-Z0-9-]+)$/;

  const match = url.match(regex);
  return match && match.length > 1 ? match[1] : null;
}

function getPostIdFromInstagramUrl(url: string): string {
  const postIdRegex = /\/p\/([^/?#]+)/;
  const match = url.match(postIdRegex);
  if (match && match[1]) {
    return match[1];
  }
  return '';
}

const getImageFromYouTubeUrl = (url: string) => {
  if (isValidSourceUrl(url, 'YouTube')) {
    const videoId = getVideoIdFromYouTubeUrl(url);
    const imageUrl = videoId ? `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg` : '';
    return imageUrl;
  }
  return '';
};

const getImageFromInstagramUrl = (url: string) => {
  if (isValidSourceUrl(url, 'Instagram')) {
    const postId = getPostIdFromInstagramUrl(url);
    const imageUrl = postId
      ? `${config.apiUrl}context/image-proxy?url=https://www.instagram.com/p/${postId}/media/?size=l`
      : '';
    return imageUrl;
  }
  return '';
};

const VALID_SOURCE_REGEX: Record<BlogSourceName, RegExp | null> = {
  // Video Sources
  YouTube:
    /^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtube\.com\/(?:[^/]+\?.*v=|embed\/|v\/|shorts\/|live\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
  Vimeo: /^(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com\/)([0-9]+)(?:\?|#|$)/,
  Rumble: /^(?:https?:\/\/)?(?:www\.)?(?:rumble\.com\/)(?:v\/)?([^/?#]+)/,
  TikTok: /^(?:https?:\/\/)?(?:www\.)?(?:tiktok\.com\/)(?:@[^/]+\/video\/|v\/)([0-9]+)/,
  Facebook:
    /^(?:https?:\/\/)?(?:www\.)?(?:facebook\.com\/)(?:watch\/?\?v=|[^/]+\/videos\/)([0-9]+)/,
  X: /^(?:https?:\/\/)?(?:www\.)?(?:x\.com\/)([^/?#]+)/,
  DailyMotion: /^(?:https?:\/\/)?(?:www\.)?(?:dailymotion\.com\/video\/)([^/?#]+)/,
  TED: /^(?:https?:\/\/)?(?:www\.)?(?:ted\.com\/talks\/)([^/?#]+)/,
  'Video Link': /\.(mp4|mkv|avi|mov|wmv|flv|webm)(?:\?|#|$)/i,
  'Video File': null,

  // Audio Sources
  'Apple Podcasts':
    /^(?:https?:\/\/)?(?:www\.)?(?:podcasts\.apple\.com\/)(?:[^/]+\/[^/]+\/[^/]+\/)(.*?)(?:\?|#|$)/,
  CastBox: /^(?:https?:\/\/)?(?:www\.)?(?:castbox\.fm\/)([^/?#]+)/,
  Podchaser: /^(?:https?:\/\/)?(?:www\.)?(?:podchaser\.com\/)([^/?#]+)/,
  PodBean: /^(?:https?:\/\/)?(?:\w+\.)?podbean\.com\/(?:.*\/)?(.*?)(?:\?|#|$)/,
  PodcastAddict: /^(?:https?:\/\/)?(?:www\.)?(?:podcastaddict\.com\/)([^/?#]+)/,
  SoundCloud: /^(?:https?:\/\/)?(?:www\.)?(?:on\.)?(?:soundcloud\.com\/)(.*?)(?:\?|#|$)/,
  IHeart: /^(?:https?:\/\/)?(?:www\.)?(?:iheart\.com\/)([^/?#]+)/,
  TuneIn: /^(?:https?:\/\/)?(?:www\.)?(?:tunein\.com\/)([^/?#]+)/,
  'Audio Link': /\.(mp3|wav|aac|flac|ogg|aiff|wma|m4a)(?:\?|#|$)/i,
  'Audio File': null,

  // Image Sources
  Instagram: /^(?:https?:\/\/)?(?:www\.)?(?:instagram\.com\/)([^/?#]+)/,
  Pinterest: /^(?:https?:\/\/)?(?:www\.)?(?:pinterest\.com\/)([^/?#]+)/,
  Flickr: /^(?:https?:\/\/)?(?:www\.)?(?:flickr\.com\/)([^/?#]+)/,
  'Getty Images': /^(?:https?:\/\/)?(?:www\.)?(?:gettyimages\.com\/)([^/?#]+)/,
  'Image Link': /\.(jpeg|jpg|png|gif|bmp|tiff|tif|webp|svg|heic|heif)(?:\?|#|$)/i,
  'Image File': null,

  // Document Sources
  'Google Docs': /^(?:https?:\/\/)?(?:www\.)?(?:docs\.google\.com\/document\/d\/)([^/?#]+)/,
  Notion:
    /^(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9-]+)\.(?:notion\.(?:so|site)\/)([\w-]+(?:\/[\w-]+)*)/,
  'Document Link': /\.(pdf|doc|docx|txt|rtf|odt|xls|xlsx|csv|ppt|pptx)(?:\?|#|$)/i,
  'Document File': null,

  // E-Commerce Sources
  Amazon:
    /^(?:https?:\/\/)?(?:www\.)?(?:amazon\.(?:com|ca|co\.uk|in|com\.au|de|fr|es|it|co\.jp|nl|com\.mx|com\.br|cn|ae|com\.tr|com\.eg|sg|se|com\.sa|com\.sg|com\.hk)\/)(.*?)(?:\?|#|$)/,
  Shopify: /^(?:https?:\/\/)?(?:[^/]+\.myshopify\.com\/)(.*?)(?:\?|#|$)/,
  Etsy: /^(?:https?:\/\/)?(?:www\.)?(?:etsy\.com\/)([^/?#]+)/,
  eBay: /^(?:https?:\/\/)?(?:www\.)?(?:ebay\.com\/)([^/?#]+)/,
  Ikea: /^(?:https?:\/\/)?(?:www\.)?(?:ikea\.com\/)([^/?#]+)/,
  Nike: /^(?:https?:\/\/)?(?:www\.)?(?:nike\.com\/)([^/?#]+)/,
  FlipCart: /^(?:https?:\/\/)?(?:www\.)?(?:flipkart\.com\/)([^/?#]+)/,
  Walmart: /^(?:https?:\/\/)?(?:www\.)?(?:walmart\.com\/)([^/?#]+)/,
  'e-commerce page': null,
  'Web Link': null,

  // Webpage Sources
  Blogger: /^(?:https?:\/\/)?(?:www\.)?(?:[^/]+\.blogspot\.com\/)([^/?#]+)/,
  BBC: /^(?:https?:\/\/)?(?:www\.)?(?:bbc\.com\/)([^/?#]+)/,
  Verge: /^(?:https?:\/\/)?(?:www\.)?(?:theverge\.com\/)([^/?#]+)/,
  Apple: /^(?:https?:\/\/)?(?:www\.)?(?:apple\.com\/)([^/?#]+)/,
  Reddit: /^(?:https?:\/\/)?(?:www\.)?(?:reddit\.com\/)([^/?#]+)/,
  Quora: /^(?:https?:\/\/)?(?:www\.)?(?:quora\.com\/)([^/?#]+)/,
  StackOverflow: /^(?:https?:\/\/)?(?:www\.)?(?:stackoverflow\.com\/)([^/?#]+)/,
  XDA: /^(?:https?:\/\/)?(?:www\.)?(?:xdaforums\.com\/)([^/?#]+)/,

  // Prompt
  Prompt: null,
};
const VALID_URL_REGEX =
  /^(?:(?:https?|ftp):\/\/)?(?:www\.)?(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:\/\S*)?$/;

const isValidSourceUrl = (url: string, sourceName: BlogSourceName): boolean =>
  VALID_SOURCE_REGEX[sourceName]?.test(url) || false;

const isValidUrl = (url: string, sourceName?: BlogSourceName): boolean => {
  if (!VALID_URL_REGEX.test(url)) return false;
  if (!sourceName) return true;

  if (
    ['Shopify', 'Audio Link', 'Video Link', 'Image Link', 'Document Link', 'Web Link'].includes(
      sourceName
    )
  ) {
    return true;
  }

  if (VALID_SOURCE_REGEX[sourceName]) {
    return isValidSourceUrl(url, sourceName);
  }

  return true;
};

const getSourceNameFromUrl = (url: string): BlogSourceName | void => {
  if (!VALID_URL_REGEX.test(url)) return;

  const sourceName = (Object.keys(VALID_SOURCE_REGEX) as BlogSourceName[]).find((key) =>
    VALID_SOURCE_REGEX[key]?.test(url)
  );

  return sourceName || 'Web Link';
};

export {
  getVideoIdFromYouTubeUrl,
  getImageFromInstagramUrl,
  getVideoIdFromVimeoUrl,
  getImageFromYouTubeUrl,
  getSourceNameFromUrl,
  getPodBeanIdFromUrl,
  isValidSourceUrl,
  isValidUrl,
};
