import type { StripePaymentElementOptions, StripeElementsOptions } from '@stripe/stripe-js';

import { cacheGet } from '@/utils/localStorageCache';
import { Theme } from '@/styles';
import config from '@/constants/config';

export const stripeElementsOptions: StripeElementsOptions = {
  mode: 'subscription',
  currency: 'usd',
  amount: 0,
  appearance: {
    theme: 'night',
    variables: {
      colorTextPlaceholder: Theme.colors.black5,
      colorTextSecondary: Theme.colors.white,
      colorBackground: Theme.colors.bg,
      colorPrimary: Theme.colors.white,
      iconColor: Theme.colors.white,
      borderRadius: '8px',
    },
  },
};

export const stripeElementsOptionsLight: StripeElementsOptions = {
  mode: 'subscription',
  currency: 'usd',
  amount: 0,
};

export const stripePaymentElementOptions: StripePaymentElementOptions = {
  layout: {
    spacedAccordionItems: false,
    defaultCollapsed: false,
    type: 'accordion',
    radios: true,
  },
};

export const getStripeReturnRedirect = (
  subscriptionRef: string,
  {
    packageName,
    coupon,
  }: {
    packageName: string;
    coupon: string;
  }
): string => {
  const redirectTo = new URLSearchParams(window.location.search).get('redirectTo');
  if (!subscriptionRef || packageName.toLowerCase().startsWith('lifetime')) {
    return `${config.apiUrl}payments/confirm-purchase-subscription?token=${cacheGet(
      'token'
    )}&plan=${packageName}&coupon=${coupon}${redirectTo ? `&redirectTo=${encodeURIComponent(redirectTo)}` : ''}`;
  }

  return `${config.apiUrl}subscriptions/confirm?token=${cacheGet(
    'token'
  )}&subscriptionRef=${subscriptionRef}&coupon=${coupon}${redirectTo ? `&redirectTo=${encodeURIComponent(redirectTo)}` : ''}`;
};

export const getAddonReturnRedirect = (addonId: string): string =>
  `${config.apiUrl}products/addons/purchase/confirm?token=${cacheGet('token')}&addonId=${addonId}`;
