const THOUSAND = 1000;
const MILLION = 1000000;
const BILLION = 1000000000;

export const shortenNumber = (
  value: number | string,
  func: 'floor' | 'float' = 'float',
  precision = 1
): string => {
  const num = parseInt(value as string, 10);

  if (isNaN(num) || num < THOUSAND) {
    return value as string;
  }

  let val: any =
    num >= BILLION
      ? num / BILLION
      : num >= MILLION
        ? num / MILLION
        : num >= THOUSAND
          ? num / THOUSAND
          : num;

  val = func === 'floor' ? Math.floor(val) : val.toFixed(precision);
  val += num >= BILLION ? 'B' : num >= MILLION ? 'M' : num >= THOUSAND ? 'k' : '';

  return typeof val === 'string' && /\.0+/.test(val) ? val.replace(/\.0+/, '') : val;
};

export const normalizeNumber = (input: number, precision: number = 4): number =>
  Math.round(input * 10 ** precision) / 10 ** precision;
