// بسم الله الرحمن الرحيم

/**
 * Wrap a given product URL with tracking parameters for affiliate marketing.
 *
 * @param  product - The URL of the product to be tracked.
 * @param  blogID - The ID of the blog where the product is being promoted.
 * @param  businessID - The ID of the business associated with the promotion.
 * @returns  - A new URL with the tracking parameters appended.
 */

export const makeTrackingLink = (product: URL, blogID: string, businessID: string) =>
  new URL(
    new URLSearchParams({
      ref: product.toString(),
      utm_source: blogID,
      utm_medium: businessID,
    }).toString(),
    'https://2www.net'
  );

export function generateTrackingLink(bid: string, blogId: string, affiliateLink: string): string {
  const protocol = process.env.NODE_ENV === 'dev' ? 'http' : 'https';
  const trackingDomain = '2www.net';
  return `${protocol}://${trackingDomain}?utm_source=${bid}&utm_medium=${blogId}&ref=${affiliateLink}`;
}
