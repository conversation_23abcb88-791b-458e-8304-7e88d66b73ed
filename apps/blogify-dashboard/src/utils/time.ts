import duration from 'dayjs/plugin/duration';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(duration);
dayjs.extend(utc);

type RelativeTimeFormatUnitSingular =
  | 'year'
  | 'quarter'
  | 'month'
  | 'week'
  | 'day'
  | 'hour'
  | 'minute'
  | 'second';
const divider = [1, 60, 60, 24, 31, 12];
const units: RelativeTimeFormatUnitSingular[] = [
  'second',
  'minute',
  'hour',
  'day',
  'month',
  'year',
];

const getTimeAndUnit = (
  seconds: number
): { time: number; unit: RelativeTimeFormatUnitSingular } => {
  let time = seconds;
  let unit = units[0];

  let i = 0;
  while (Math.round(time / divider[i])) {
    const _t = Math.round(time / divider[i]);
    if (unit[i] === 'day' && _t >= 7 && _t < 31) {
      return { time: Math.round(time / (31 / 7)), unit: 'week' };
    }
    time = _t;
    unit = units[i];
    i += 1;
  }

  return { time, unit };
};

const toLocalizedRelativeTime = (dateValue?: string | Date, locale = 'en'): string => {
  try {
    if (!dateValue) {
      return '';
    }

    const rtf = new window.Intl.RelativeTimeFormat(locale, {
      localeMatcher: 'best fit',
      numeric: 'auto',
      style: 'long',
    });

    const seconds = Math.floor((Number(new Date()) - Number(new Date(dateValue))) / 1000);
    const { time, unit } = getTimeAndUnit(seconds);

    return rtf.format(-time, unit);
  } catch (error) {
    return '';
  }
};

const formatSeconds = (seconds: number, endTime: number = 0): string => {
  const format = (endTime || seconds) >= 60 * 60 ? 'H:mm:ss' : 'mm:ss';

  return dayjs
    .utc(dayjs.duration(dayjs().diff(dayjs().subtract(seconds, 'second'))).asMilliseconds())
    .format(format);
};

const timeStringToSeconds = (durationString: string): number => {
  const isoRegex = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
  const isoMatches = durationString.match(isoRegex);

  let hours, minutes, seconds;
  if (isoMatches) {
    [, hours, minutes, seconds] = isoMatches.map((n) => parseInt(n, 10));
  } else {
    [seconds, minutes, hours] = durationString
      .split(':')
      .map((component) => parseInt(component, 10))
      .reverse();
  }

  return dayjs
    .duration({
      hours: hours || 0,
      minutes: minutes || 0,
      seconds: seconds || 0,
    })
    .asSeconds();
};

const timeFractionToSeconds = (time: number): number => {
  let [minutes, seconds] = time.toString().split('.');
  minutes = minutes || '0';
  seconds = seconds?.length === 1 ? `${seconds}0` : seconds || '0';
  const totalSeconds = parseInt(minutes, 10) * 60 + parseInt(seconds, 10);

  return totalSeconds;
};

export { toLocalizedRelativeTime, timeFractionToSeconds, timeStringToSeconds, formatSeconds };
