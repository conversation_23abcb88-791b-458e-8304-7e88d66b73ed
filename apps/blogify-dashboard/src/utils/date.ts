const padZero = (n: number): string => `${n < 10 ? 0 : ''}${n}`;

const formatDate = (_date?: string): string => {
  const date = _date ? new Date(_date) : new Date();
  const y = date.getFullYear();
  const m = date.getMonth() + 1;
  const d = date.getDate();

  return `${y}-${m < 10 ? '0' : ''}${m}-${padZero(d)}`;
};

const formatTime = (_date?: string): string => {
  const date = _date ? new Date(_date) : new Date();
  const h = date.getHours();
  const m = date.getMinutes();
  const s = date.getSeconds();

  return `${padZero(h)}:${padZero(m)}:${padZero(s)}`;
};

const mergeDateAndTime = (date?: string, time?: string): string => {
  let d = formatDate();

  if (date) d = date;
  if (time) d += time.length > 5 ? `T${time}` : `T${time}:00`;

  return new Date(d).toISOString();
};

const getMonthRange = (): [firstDay: Date, lastDay: Date] => {
  const date = new Date();
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  return [firstDay, lastDay];
};

const isDateInRange = (
  _startTime: string | Date,
  _endTime: string | Date,
  date?: string | Date
): boolean => {
  const startTime = new Date(_startTime);
  const endTime = new Date(_endTime);
  const targetTime = date ? new Date(date) : new Date();
  return startTime <= targetTime && targetTime <= endTime;
};

export { mergeDateAndTime, formatDate, formatTime, getMonthRange, isDateInRange };
