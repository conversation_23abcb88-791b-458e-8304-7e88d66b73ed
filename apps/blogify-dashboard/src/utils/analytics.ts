import type {
  CountryAnalytics,
  DeviceAnalytics,
  DeviceType,
} from '@/types/resources/analytics.type';
import type { BlogAnalytics } from '@/types/resources/blog-analytics.type';

import { ANALYTICS_TYPES, KNOWN_COUNTRIES, KNOWN_DEVICES } from '@/constants/analytics';
import { COUNTRY_BY_ISO2 } from '@/constants/address';

const getPercentValue = (value: number, total: number): number => {
  if (total === 0) return 0;
  return ((value || 0) / total) * 100;
};

const getCountryAnalytics = (
  blogAnalytics: BlogAnalytics
): {
  view: { total: number; data: CountryAnalytics[] };
  click: { total: number; data: CountryAnalytics[] };
} =>
  ANALYTICS_TYPES.reduce(
    (obj, type) => {
      const countries = blogAnalytics[type]?.country;
      let totalOtherCountries = 0;

      const total = Object.keys(countries).reduce((sum, country) => {
        if (!KNOWN_COUNTRIES.includes(COUNTRY_BY_ISO2[country]?.name)) {
          totalOtherCountries += countries[country];
        }
        // eslint-disable-next-line no-param-reassign
        sum += countries[country];
        return sum;
      }, 0);

      const data: CountryAnalytics[] = Object.keys(countries)
        .map((country) => ({
          country: COUNTRY_BY_ISO2[country]?.name,
          clicks: countries[country] || 0,
          ratio: getPercentValue(countries[country], total),
        }))
        .filter((c) => !!c.clicks)
        .sort((c1, c2) => c2.clicks - c1.clicks);

      if (totalOtherCountries) {
        data.push({
          country: 'other',
          clicks: totalOtherCountries,
          ratio: getPercentValue(totalOtherCountries, total),
        });
      }

      obj[type] = { total, data };

      return obj;
    },
    {
      view: { total: 0, data: [] as CountryAnalytics[] },
      click: { total: 0, data: [] as CountryAnalytics[] },
    }
  );

const getDeviceAnalytics = (
  blogAnalytics: BlogAnalytics
): {
  view: { total: number; data: DeviceAnalytics[] };
  click: { total: number; data: DeviceAnalytics[] };
} =>
  ANALYTICS_TYPES.reduce(
    (obj, type) => {
      const devices = blogAnalytics[type].platform;
      let totalOtherDevices = 0;

      const total = (Object.keys(devices) as DeviceType[]).reduce((sum, device) => {
        if (!KNOWN_DEVICES.includes(device)) {
          totalOtherDevices += devices[device];
        }
        // eslint-disable-next-line no-param-reassign
        sum += devices[device];
        return sum;
      }, 0);

      const data: DeviceAnalytics[] = KNOWN_DEVICES.map((device) => ({
        device,
        clicks: devices[device] || 0,
        ratio: getPercentValue(devices[device], total),
      }));

      if (totalOtherDevices) {
        data.push({
          device: 'other',
          clicks: totalOtherDevices,
          ratio: getPercentValue(totalOtherDevices, total),
        });
      }

      obj[type] = { total, data };

      return obj;
    },
    {
      view: { total: 0, data: [] as DeviceAnalytics[] },
      click: { total: 0, data: [] as DeviceAnalytics[] },
    }
  );

export { getCountryAnalytics, getDeviceAnalytics };
