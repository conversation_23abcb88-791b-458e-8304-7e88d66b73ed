import type {
  SocialIntegration,
  BlogIntegration,
  Integration,
} from '@/types/misc/integration.type';
import type { ThirdPartyIntegration } from '@ps/types';

import { SOCIALS, BLOGS } from '@/constants/index';

export const isSocialPlatform = (integration: ThirdPartyIntegration<Integration>) =>
  SOCIALS.includes(integration.platform as SocialIntegration);

export const isBlogPlatform = (integration: ThirdPartyIntegration<Integration>) =>
  BLOGS.includes(integration.platform as BlogIntegration);
