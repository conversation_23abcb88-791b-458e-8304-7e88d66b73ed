import type { SubscriptionPlanName, SubscriptionPeriod, SubscriptionType } from '@ps/types';

import { API } from '@/services/api';
import PACKAGES from '@/constants/packages';

function safeDecode(str: string): string {
  try {
    return decodeURIComponent(str);
  } catch (e) {
    return str;
  }
}

const parseQuery = (queryString?: string): Record<string, string> => {
  const query: Record<string, string> = {};
  ((queryString || window.location.search || '').split('?')[1] || '').replace(
    /([^?&=]+)=([^&]*)/g,
    (_, k, v) => (query[k] = safeDecode(v))
  );
  return query;
};

const getPackageName = (
  plan: SubscriptionType,
  period: SubscriptionPeriod
): SubscriptionPlanName => {
  if (plan === 'free') return 'FREE';
  return `${period}_${plan}`.toUpperCase() as SubscriptionPlanName;
};

const getPackageAndPeriod = (
  pkg: SubscriptionPlanName
): { plan: SubscriptionType; period: SubscriptionPeriod } => ({
  plan: (pkg.split('_')[1]?.toLowerCase() || 'free') as SubscriptionType,
  period: (pkg.split('_')[0]?.toLowerCase() || 'lifetime') as SubscriptionPeriod,
});

const isUrl = (str: string): boolean => {
  try {
    new URL(str);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * Calculates the percentage of words changed between the original title and the edited title
 * @param originalTitle - Original title
 * @param editedTitle - New edited title
 * @returns Percentage of words changed
 */
const calculatePercentageChanged = (originalTitle: string, editedTitle: string) => {
  // Step 1: Split titles into words
  const originalWords = originalTitle.split(' ');
  const editedWords = editedTitle.split(' ');

  // Step 2: Calculate the number of words changed
  let numChangedWords = 0;
  for (let i = 0; i < originalWords.length; i++) {
    if (!editedWords.includes(originalWords[i])) numChangedWords++;
  }

  // Step 3: Calculate the percentage
  const totalWords = originalWords.length;
  const percentageChanged = (numChangedWords / totalWords) * 100;

  return percentageChanged;
};

export const dealCodesPrefixList = [
  'BLOGIFY_DEALMIRROR_',
  'BLOGIFY_STACKCOMMERCE_',
  'BLOGIFY_DEALFUEL_',
  'BLOGIFY_APPSUMO_',
  'BLOGIFY_DEALIFY_',
  'BLOGIFY_VIEDEDINGUE_',
  'PG-',
];

const getDiscountsForCoupon = async (
  coupon?: string
): Promise<{ discount: number; valid: boolean }> => {
  const couponCode = coupon?.trim();
  if (!couponCode) return Promise.resolve({ discount: 0, valid: false });
  return API.fetch<{ discount: number }>(`payments/validate-coupon?coupon=${couponCode}`)
    .then((resp) => ({
      discount: resp?.discount || 0,
      valid: true,
    }))
    .catch(() => ({
      discount: 0,
      valid: false,
    }));
};

const getDiscountAmount = async (amount: number, coupon?: string): Promise<number> => {
  const { discount } = await getDiscountsForCoupon(coupon);
  return amount * (discount / 100);
};

const getPriceAfterDiscount = async (price: number, coupon: string): Promise<number> => {
  const amount = price / 100;
  const discountAmount = await getDiscountAmount(amount, coupon);
  return toFixedWithoutRound(amount - discountAmount, 2);
};

const getPriceForPlan = async (plan: SubscriptionPlanName, coupon: string) => {
  const { plan: planName, period } = getPackageAndPeriod(plan);

  const price = PACKAGES.reduce((acc: any, pkg) => {
    if (acc !== null) return acc;
    if (pkg.name === planName) {
      return pkg.pricing[period] !== undefined ? pkg.pricing[period] : 0;
    }
    return null;
  }, null);

  const { discount } = await getDiscountsForCoupon(coupon);

  const finalPrice = price - (price * discount) / 100 || 0;
  return toFixedWithoutRound(finalPrice, 2);
};

const toFixedWithoutRound = (n: number, fixed: number) =>
  ~~(Math.pow(10, fixed) * n) / Math.pow(10, fixed);

const isMobile = window.innerWidth < 768;
const isTablet = window.innerWidth <= 1024;
const isDesktop = window.innerWidth >= 768;
const isTouch = 'ontouchstart' in document.documentElement;
const isIOS = ['iPhone', 'iPad', 'iPod'].includes(window.navigator.platform);

const getMobileOperatingSystem = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;

  // Windows Phone must come first because its UA also contains "Android"
  if (/windows phone/i.test(userAgent)) {
    return 'Windows Phone';
  }

  if (/android/i.test(userAgent)) {
    return 'Android';
  }

  // iOS detection
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    return 'iOS';
  }

  return 'unknown';
};

const isPhone = () => getMobileOperatingSystem() !== 'unknown';

export {
  getPackageAndPeriod,
  getPackageName,
  parseQuery,
  isMobile,
  isPhone,
  isTablet,
  isDesktop,
  isTouch,
  isIOS,
  isUrl,
  calculatePercentageChanged,
  toFixedWithoutRound,
  getDiscountsForCoupon,
  getPriceForPlan,
  getPriceAfterDiscount,
  getDiscountAmount,
};
