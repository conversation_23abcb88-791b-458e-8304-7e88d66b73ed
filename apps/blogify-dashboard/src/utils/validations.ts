const checkPassword = (password: string, errors: Record<string, string>) => {
  if (!password) {
    errors.password = 'Password is required';
  } else if (password.length < 8) {
    errors.password = 'Password must have at least 8 characters';
  } else if (password.length > 256) {
    errors.password = 'Password must be less than 256 characters';
  } else if (!/\w*[a-z]\w*/.test(password)) {
    errors.password = 'Password must have a lowercase letter';
  } else if (!/\w*[A-Z]\w*/.test(password)) {
    errors.password = 'Password must have a capital letter';
  } else if (!/\d/.test(password)) {
    errors.password = 'Password must have a number';
  }
  return errors;
};

function isValidUrl(url: string) {
  const urlRegex =
    /^(https?:\/\/)?(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;

  return urlRegex.test(url);
}
export { checkPassword, isValidUrl };
