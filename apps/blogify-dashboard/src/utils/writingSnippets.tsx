import { API } from '@/services/api';
import {
  IGeneratedWritingSnippet,
  ISaveWritingSnippet,
  ISavedWritingSnippetList,
  ISavedWritingSnippetListItem,
  IWiringSnippetsAndCategories,
  IWritingSnippetsGenerateForm,
} from '@/types/resources/writing-snippets.type';
import { useMutation, useQuery, useQueryClient } from 'react-query';

export const useWritingSnippets = () =>
  useQuery<IWiringSnippetsAndCategories>([`/context/app`], {
    refetchOnWindowFocus: false,
  });

export const useSnippetGenerate = () => {
  const {
    mutateAsync: generateSnippet,
    isLoading: isGenerateSnippetLoading,
    isError: isGenerateSnippetError,
    error: generateSnippetError,
    data: generatedSnippetData,
  } = useMutation({
    mutationFn: (formPayload: IWritingSnippetsGenerateForm) =>
      API.post('/writing-snippets/generate', formPayload) as Promise<IGeneratedWritingSnippet>,
  });

  return {
    generateSnippet,
    isGenerateSnippetLoading,
    isGenerateSnippetError,
    generateSnippetError,
    generatedSnippetData,
  };
};

export const useSaveSnippet = () => {
  const {
    mutateAsync: saveSnippet,
    isLoading: isSaveSnippetLoading,
    isError: isSaveSnippetError,
    error: saveSnippetError,
    data: savedSnippetData,
  } = useMutation({
    mutationFn: (formPayload: ISaveWritingSnippet) =>
      API.post('/writing-snippets', formPayload) as Promise<ISaveWritingSnippet>,
  });

  return {
    saveSnippet,
    isSaveSnippetLoading,
    isSaveSnippetError,
    saveSnippetError,
    savedSnippetData,
  };
};

export const useGetSavedSnippetsList = ({
  pageNumber = 1,
  limit = 10,
  searchTerm,
  category,
}: {
  pageNumber?: number;
  limit?: number;
  searchTerm?: string;
  category?: string;
}) => {
  const {
    data: savedSnippetsData,
    isLoading: isSavedSnippetsLoading,
    isError: isSavedSnippetsError,
    error: savedSnippetsError,
    ...rest
  } = useQuery({
    queryKey: ['savedSnippets', pageNumber, searchTerm, category],
    queryFn: () =>
      API.fetch(
        `/writing-snippets?page=${pageNumber}&limit=${limit}&search=${searchTerm}&category=${category}`
      ) as Promise<ISavedWritingSnippetList>,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  });

  return {
    isSavedSnippetsLoading,
    isSavedSnippetsError,
    savedSnippetsError,
    savedSnippetsData,
    ...rest,
  };
};

export const useDeleteSavedSnippet = () => {
  const queryClient = useQueryClient();
  const {
    mutateAsync: deleteSavedSnippet,
    isLoading: isDeleteSavedSnippetLoading,
    isError: isDeleteSavedSnippetError,
    error: deleteSavedSnippetError,
  } = useMutation({
    mutationFn: (id: string) => API.remove(`/writing-snippets/${id}`) as Promise<void>,
    onSuccess: () => {
      queryClient.invalidateQueries('savedSnippets');
    },
  });

  return {
    deleteSavedSnippet,
    isDeleteSavedSnippetLoading,
    isDeleteSavedSnippetError,
    deleteSavedSnippetError,
  };
};

export const useGetSingleSavedSnippet = ({ snippetId }: { snippetId: string }) => {
  const {
    data: savedSnippetData,
    isLoading: isSavedSnippetLoading,
    isError: isSavedSnippetError,
    error: savedSnippetError,
  } = useQuery({
    queryKey: ['savedSnippet', snippetId],
    queryFn: () =>
      API.fetch(`/writing-snippets/${snippetId}`) as Promise<ISavedWritingSnippetListItem>,
  });

  return {
    isSavedSnippetLoading,
    isSavedSnippetError,
    savedSnippetError,
    savedSnippetData,
  };
};

export const useUpdateSavedSnippet = () => {
  const queryClient = useQueryClient();
  const {
    mutateAsync: updateSavedSnippet,
    isLoading: isUpdateSavedSnippetLoading,
    isError: isUpdateSavedSnippetError,
    error: updateSavedSnippetError,
  } = useMutation({
    mutationFn: ({ id, formPayload }: { id: string; formPayload: Partial<ISaveWritingSnippet> }) =>
      API.put(`/writing-snippets/${id}`, formPayload) as Promise<ISavedWritingSnippetListItem>,
    onSuccess: () => {
      queryClient.invalidateQueries('savedSnippets');
    },
  });

  return {
    updateSavedSnippet,
    isUpdateSavedSnippetLoading,
    isUpdateSavedSnippetError,
    updateSavedSnippetError,
  };
};
