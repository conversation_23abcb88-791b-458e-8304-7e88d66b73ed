export default function asyncScriptLoad(id: string, src: string, objInWindow?: string) {
  return new Promise<void>((resolve) => {
    if (
      document.getElementById(id) &&
      (!objInWindow || (objInWindow && window[objInWindow as any]))
    ) {
      return resolve();
    }

    const head = document.getElementsByTagName('head')[0];
    const js = document.createElement('script');
    js.type = 'text/javascript';
    js.async = true;
    js.src = src;
    js.id = id;

    const func = function (this: any) {
      if (!this.readyState || this.readyState === 'complete') {
        return setTimeout(() => resolve(), 100);
      }
    };

    js.onload = func;
    // if (js.onreadystatechange) {
    //   js.onreadystatechange = func;
    // }

    if (head) {
      head.appendChild(js);
    }
  });
}
