import type { User } from '@/types/resources/user.type';

import { redirect } from 'react-router-dom';

import config from '@/constants/config';
import Store from '@/store';

import { cacheRemove, cacheGet } from './localStorageCache';

const getCurrentUser = (): User => Store.getState().user.current;

const isAuthenticated = () => !!cacheGet('token');

const getRedirectTo = (): string => {
  const { pathname, search } = window.location;
  const queryParams = new URLSearchParams(search);

  const token = queryParams.get('token');
  if (token) queryParams.delete('token');

  const queryString = queryParams.toString();
  return `${pathname}${queryString ? `?${queryString}` : ''}`;
};

const logoutUser = () => {
  cacheRemove('token');
  window.CRISP_TOKEN_ID = null;
  window.$crisp?.push(['do', 'session:reset']);
  const redirectTo = getRedirectTo();
  window.location.href = `/login/?redirectTo=${redirectTo}`;
};

const redirectIfNoAuth = () => {
  if (!isAuthenticated()) {
    const { search } = window.location;

    const queryParams = new URLSearchParams(search);
    const token = queryParams.get('token');

    const redirectTo = encodeURIComponent(getRedirectTo());
    return redirect(`/login/?redirectTo=${redirectTo}${token ? `&token=${token}` : ''}`);
  }

  return null;
};

const redirectIfAuth = () => {
  const queryParams = new URLSearchParams(window.location.search);
  const redirectTo = decodeURIComponent(queryParams.get('redirectTo') || '');

  if (isAuthenticated()) {
    return redirect(redirectTo ? redirectTo : '/dashboard/');
  }

  return null;
};

const redirectIfNoSubscription = () => {
  if (getCurrentUser().subscriptionStatus === 'inactive') {
    return redirect('/payment/');
  }
  return null;
};

// const getState = () => {
//   const { redirectTo: _redirectTo } = parseQuery();
//   const initialPath = encodeURI(window.location.pathname);

//   const redirectTo =
//     _redirectTo ||
//     (/login|signup/.test(window.location.pathname) ? '/' : window.location.pathname) ||
//     '/';

//   return encodeURI(
//     JSON.stringify({
//       // app: 'client',
//       // initialPath,
//       // redirectTo,
//     })
//   );
// };

const getSocialAuthUrlWithRedirect = (platform: 'google' | 'facebook'): string => {
  const CLIENT_ID = import.meta.env[`VITE_${platform.toUpperCase()}`] || '';
  // const redirectUrl = `${config.apiUrl}auth/${platform}`;
  const redirectUrl = `${config.apiUrl}${platform}/login-callback`;
  const state = ''; // `state=${getState()}`;

  switch (platform) {
    case 'facebook':
      return `https://www.facebook.com/v2.10/dialog/oauth?client_id=${CLIENT_ID}&scope=email&redirect_uri=${redirectUrl}&${state}`;
    case 'google':
      return `https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=${redirectUrl}&prompt=consent&response_type=code&client_id=${CLIENT_ID}&scope=https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile&access_type=offline&${state}`;
    default:
      return '';
  }
};

export {
  getSocialAuthUrlWithRedirect,
  redirectIfNoSubscription,
  redirectIfNoAuth,
  redirectIfAuth,
  isAuthenticated,
  logoutUser,
};
