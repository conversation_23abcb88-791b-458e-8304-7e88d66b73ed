import toast from 'react-hot-toast';

const copy = async (copyContent: string, isFromHtml: boolean = false) => {
  try {
    if (isFromHtml) {
      const htmlBlob = new Blob([copyContent], { type: 'text/html' });
      const plainText = copyContent.replace(/<br>/g, '\n').replace(/<\/?[^>]+(>|$)/g, '');
      const plainTextBlob = new Blob([String(plainText)], { type: 'text/plain' });

      await navigator.clipboard.write([
        new ClipboardItem({ 'text/html': htmlBlob, 'text/plain': plainTextBlob }),
      ]);
    } else {
      await navigator.clipboard.writeText(copyContent);
    }
    toast.success('Copied!');
  } catch (_) {
    toast.error('Failed to copy!');
  }
};

export { copy };
