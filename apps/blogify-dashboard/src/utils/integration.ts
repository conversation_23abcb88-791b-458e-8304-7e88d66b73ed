import type { Integration } from '@/types/misc/integration.type';

import { cacheGet } from '@/utils/localStorageCache';
import config from '@/constants/config';

import { capitalize } from './string';

const getDisplayName = (integration: Integration): string => {
  switch (integration) {
    case 'wordpress':
      return 'WordPress.com';
    case 'wordpressorg':
      return 'WordPress.org';
    default:
      return capitalize(integration as string);
  }
};

const renameIntegration = (integration: string) => (integration === 'x' ? 'twitter' : integration);

const getConnectUrl = (integration: string) =>
  `${config.apiUrl}${renameIntegration(integration)}/connect?token=${cacheGet('token')}`;

const getDisconnectUrl = (integration: Integration) =>
  `${config.apiUrl}business/disconnect/${integration}?token=${cacheGet('token')}`;

export { getDisplayName, getConnectUrl, getDisconnectUrl };
