import type { SubscriptionType } from '@/types/misc/subscription.type';

enum PackageFeature {
  F0 = 'Create blogs from Audio / Video - Monthly Credit',
  F1 = 'Create blogs from Text / Pdf / Doc - Monthly Credit',
  F2 = 'Total Credits',
  F3 = 'Upload files to create blogs',
  F4 = 'Co-Pilot blog creation',
  F5 = 'YouTube channel integration',
  F6 = 'SEO optimized & unique blogs',
  F7 = 'Create Blog using 150+ languages',
  F8 = 'Analytics on blog views & clicks',
  F9 = 'Word count / blog',
  F10 = 'Earn commission from automated affiliate link (Beta)',
  F11 = 'Schedule blog post on WordPress, Blogger, LinkedIn & Twitter',
  F12 = 'Round the clock e-mail & chat support',
}

type PricingType = { monthly?: number; yearly?: number; lifetime?: number };

export type PackageType = {
  name: SubscriptionType;
  pricing: PricingType;
  videoCreditPricing: PricingType;
  promptCreditPricing: PricingType;
  discount: Record<keyof PricingType, number>; // Discount on percentage
  featured?: boolean;
  features: {
    name: PackageFeature;
    available: boolean;
    value?: number | string;
    optIn?: boolean;
  }[];
};

const PACKAGES: PackageType[] = [
  // {
  //   name: 'free',
  //   pricing: { monthly: 0 },
  //   videoCreditPricing: { monthly: 2, yearly: 2, lifetime: 2 },
  //   promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
  //   features: [
  //     { name: PackageFeature.F0, available: true, value: 2 },
  //     { name: PackageFeature.F1, available: true, value: 2 },
  //     { name: PackageFeature.F2, available: true },
  //     { name: PackageFeature.F3, available: true },
  //     { name: PackageFeature.F4, available: true },
  //     { name: PackageFeature.F5, available: true },
  //     { name: PackageFeature.F6, available: false },
  //   ],
  // },
  {
    name: 'starter',
    pricing: { monthly: 0.99, yearly: 9.99, lifetime: 99.99 },
    videoCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 0, yearly: 50, lifetime: 50 },
    features: [
      { name: PackageFeature.F0, available: true, value: 2 },
      { name: PackageFeature.F1, available: true, value: 2 },
      { name: PackageFeature.F2, available: true, value: 4 },
      { name: PackageFeature.F3, available: false },
      { name: PackageFeature.F4, available: false },
      { name: PackageFeature.F5, available: false },
      { name: PackageFeature.F6, available: false },
      { name: PackageFeature.F7, available: true, value: '10' },
      { name: PackageFeature.F8, available: true },
      { name: PackageFeature.F9, available: true, value: 'unlimited' },
      { name: PackageFeature.F10, available: true },
      { name: PackageFeature.F11, available: true },
      { name: PackageFeature.F12, available: false },
    ],
  },
  {
    name: 'basic',
    pricing: { monthly: 19.99, yearly: 99.99, lifetime: 499.99 },
    videoCreditPricing: { monthly: 0.5, yearly: 0.5, lifetime: 0.5 },
    promptCreditPricing: { monthly: 0.4, yearly: 0.4, lifetime: 0.4 },
    discount: { monthly: 50, yearly: 50, lifetime: 50 },
    features: [
      { name: PackageFeature.F0, available: true, value: 10 },
      { name: PackageFeature.F1, available: true, value: 10 },
      { name: PackageFeature.F2, available: true, value: 20 },
      { name: PackageFeature.F3, available: false },
      { name: PackageFeature.F4, available: false },
      { name: PackageFeature.F5, available: false },
      { name: PackageFeature.F6, available: true },
      { name: PackageFeature.F7, available: true, value: '75' },
      { name: PackageFeature.F8, available: true },
      { name: PackageFeature.F9, available: true, value: 'unlimited' },
      { name: PackageFeature.F10, available: true },
      { name: PackageFeature.F11, available: true },
      { name: PackageFeature.F12, available: false },
    ],
  },
  {
    name: 'premium',
    pricing: { monthly: 39.99, yearly: 399.99, lifetime: 999.99 },
    videoCreditPricing: { monthly: 0.4, yearly: 0.4, lifetime: 0.4 },
    promptCreditPricing: { monthly: 0.3, yearly: 0.3, lifetime: 0.3 },
    discount: { monthly: 50, yearly: 50, lifetime: 50 },
    featured: true,
    features: [
      { name: PackageFeature.F0, available: true, value: 30 },
      { name: PackageFeature.F1, available: true, value: 30 },
      { name: PackageFeature.F2, available: true, value: 60 },
      { name: PackageFeature.F3, available: true },
      { name: PackageFeature.F4, available: true },
      { name: PackageFeature.F5, available: false },
      { name: PackageFeature.F6, available: true },
      { name: PackageFeature.F7, available: true, value: '150' },
      { name: PackageFeature.F8, available: true },
      { name: PackageFeature.F9, available: true, value: 'unlimited' },
      { name: PackageFeature.F10, available: true },
      { name: PackageFeature.F11, available: true },
      { name: PackageFeature.F12, available: true },
    ],
  },
  {
    name: 'business',
    pricing: { monthly: 49.99, yearly: 499.99, lifetime: 1999.99 },
    videoCreditPricing: { monthly: 0.3, yearly: 0.3, lifetime: 0.3 },
    promptCreditPricing: { monthly: 0.2, yearly: 0.2, lifetime: 0.2 },
    discount: { monthly: 50, yearly: 50, lifetime: 50 },
    features: [
      { name: PackageFeature.F0, available: true, value: 50 },
      { name: PackageFeature.F1, available: true, value: 50 },
      { name: PackageFeature.F2, available: true, value: 100 },
      { name: PackageFeature.F3, available: true },
      { name: PackageFeature.F4, available: true },
      { name: PackageFeature.F5, available: true },
      { name: PackageFeature.F6, available: true },
      { name: PackageFeature.F7, available: true, value: '150' },
      { name: PackageFeature.F8, available: true },
      { name: PackageFeature.F9, available: true, value: 'unlimited' },
      { name: PackageFeature.F10, available: true },
      { name: PackageFeature.F11, available: true },
      { name: PackageFeature.F12, available: true },
    ],
  },
  {
    name: 'unlimited',
    pricing: { monthly: 99.99, yearly: 999.99, lifetime: 3999.99 },
    videoCreditPricing: {},
    promptCreditPricing: {},
    discount: { monthly: 50, yearly: 50, lifetime: 50 },
    features: [
      { name: PackageFeature.F0, available: true, value: 'unlimited' },
      { name: PackageFeature.F1, available: true, value: 'unlimited' },
      { name: PackageFeature.F2, available: true, value: 'unlimited' },
      { name: PackageFeature.F3, available: true },
      { name: PackageFeature.F4, available: true },
      { name: PackageFeature.F5, available: true },
      { name: PackageFeature.F6, available: true },
      { name: PackageFeature.F7, available: true, value: '150' },
      { name: PackageFeature.F8, available: true },
      { name: PackageFeature.F9, available: true, value: 'unlimited' },
      { name: PackageFeature.F10, available: true },
      { name: PackageFeature.F11, available: true },
      { name: PackageFeature.F12, available: true },
    ],
  },
];

enum NewPackageFeature {
  F0 = 'Total Credits per Month',
  F1 = 'Co-Pilot Blog Creation',
  F2 = 'SEO Optimized',
  F3 = 'Language Support',
  F4 = 'Upload Size',
  F5 = 'Duration',
  F6 = 'Word Count',
  F7 = 'Platform Support',
  F8 = 'Writing Snippets (Addon)',
  F9 = 'YouTube Connect (Addon)',
  F10 = 'YouTube Connect Pro (Addon)',
  F11 = 'Analytics',
  F12 = 'Earn commission from automated affiliate link (Beta)',
  F13 = 'Schedule blog post on WordPress, Blogger, LinkedIn & Twitter',
  F14 = 'Round the clock e-mail & chat support',
}

export type NewPricingType = { monthly?: number; yearly?: number; lifetime?: number };

export type NewPackageType = {
  name: SubscriptionType;
  pricing: NewPricingType;
  mediaCreditPricing: Record<string, number>;
  promptCreditPricing: Record<string, number>;
  discount: Record<string, number>; // Discount on percentage
  featured?: boolean;
  features: {
    name: string;
    available: boolean;
    value?: number | string | Array<any>;
    isInfo?: boolean;
    includes?: { name: string; value: string | React.ReactElement | Array<any> }[];
  }[];
};

export const NEW_PACKAGES: NewPackageType[] = [
  {
    name: 'lite',
    pricing: { monthly: 4.99, yearly: 3.99, lifetime: 199.99 },
    mediaCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 0, yearly: 0, lifetime: 0 },
    features: [
      {
        name: NewPackageFeature.F0,
        available: true,
        value: '50 Credit',
        isInfo: true,
        includes: [
          {
            name: '',
            value: 'Create ~ 10 Blogs / M',
          },
          {
            name: '',
            value: '',
          },
        ],
      },
      { name: NewPackageFeature.F1, available: false },
      { name: NewPackageFeature.F2, available: true },
      { name: NewPackageFeature.F3, available: true, value: 150 },
      { name: NewPackageFeature.F4, available: true, value: '30 MB' },
      { name: NewPackageFeature.F5, available: true, value: '30 Min' },
      { name: NewPackageFeature.F6, available: true, value: 'Unlimited' },
      {
        name: NewPackageFeature.F7,
        available: true,
        value: [
          {
            name: 'youtube',
            logo: '/images/icons/integrations/youtube.svg',
          },
          {
            name: 'google podcast',
            logo: '/images/icons/integrations/google-podcasts.svg',
          },
        ],
      },
      { name: NewPackageFeature.F8, available: false },
      { name: NewPackageFeature.F9, available: false },
      { name: NewPackageFeature.F10, available: false },
      { name: NewPackageFeature.F11, available: true },
      { name: NewPackageFeature.F12, available: true },
      { name: NewPackageFeature.F13, available: true },
      { name: NewPackageFeature.F14, available: false },
    ],
  },
  {
    name: 'basic',
    pricing: { monthly: 19.99, yearly: 15.99, lifetime: 399.99 },
    mediaCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 0, yearly: 0, lifetime: 0 },
    features: [
      {
        name: NewPackageFeature.F0,
        available: true,
        value: '150 Credit',
        isInfo: true,
        includes: [
          {
            name: '',
            value: 'Create ~ 30 Blogs / M',
          },
          {
            name: '',
            value: '',
          },
        ],
      },
      { name: NewPackageFeature.F1, available: false },
      { name: NewPackageFeature.F2, available: true },
      { name: NewPackageFeature.F3, available: true, value: 150 },
      { name: NewPackageFeature.F4, available: true, value: '50 MB' },
      { name: NewPackageFeature.F5, available: true, value: '40 Min' },
      { name: NewPackageFeature.F6, available: true, value: 'Unlimited' },
      {
        name: NewPackageFeature.F7,
        available: true,
        value: [
          {
            name: 'youtube',
            logo: '/images/icons/integrations/youtube.svg',
          },
          {
            name: 'google podcast',
            logo: '/images/icons/integrations/google-podcasts.svg',
          },
          {
            name: 'sound cloud',
            logo: '/images/icons/integrations/soundcloud.svg',
          },
        ],
      },
      { name: NewPackageFeature.F8, available: false },
      { name: NewPackageFeature.F9, available: false },
      { name: NewPackageFeature.F10, available: false },
      { name: NewPackageFeature.F11, available: true },
      { name: NewPackageFeature.F12, available: true },
      { name: NewPackageFeature.F13, available: true },
      { name: NewPackageFeature.F14, available: false },
    ],
  },
  {
    name: 'premium',
    pricing: { monthly: 39.99, yearly: 31.99, lifetime: 699.99 },
    mediaCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 50, yearly: 50, lifetime: 50 },
    featured: true,
    features: [
      {
        name: NewPackageFeature.F0,
        available: true,
        value: '300 Credit',
        isInfo: true,
        includes: [
          {
            name: '',
            value: 'Create ~ 60 Blogs / M',
          },
          {
            name: '',
            value: '',
          },
        ],
      },
      { name: NewPackageFeature.F1, available: true },
      { name: NewPackageFeature.F2, available: true },
      { name: NewPackageFeature.F3, available: true, value: 150 },
      { name: NewPackageFeature.F4, available: true, value: '100 MB' },
      { name: NewPackageFeature.F5, available: true, value: '60 Min' },
      { name: NewPackageFeature.F6, available: true, value: 'Unlimited' },
      {
        name: NewPackageFeature.F7,
        available: true,
        value: [
          {
            name: 'youtube',
            logo: '/images/icons/integrations/youtube.svg',
          },
          {
            name: 'youtube',
            logo: '/images/icons/integrations/tiktok-white.svg',
          },
          {
            name: 'vimeo',
            logo: '/images/icons/integrations/vimeo.svg',
          },
          {
            name: 'rumble',
            logo: '/images/icons/integrations/rumble.svg',
          },
          {
            name: 'sound cloud',
            logo: '/images/icons/integrations/soundcloud.svg',
          },
          {
            name: 'apple podcast',
            logo: '/images/icons/integrations/apple-podcasts.svg',
          },
          {
            name: 'google podcast',
            logo: '/images/icons/integrations/google-podcasts.svg',
          },
          {
            name: 'podbean',
            logo: '/images/icons/integrations/podbean.svg',
          },
        ],
      },
      { name: NewPackageFeature.F8, available: true },
      { name: NewPackageFeature.F9, available: false },
      { name: NewPackageFeature.F10, available: false },
      { name: NewPackageFeature.F11, available: true },
      { name: NewPackageFeature.F12, available: true },
      { name: NewPackageFeature.F13, available: true },
      { name: NewPackageFeature.F14, available: true },
    ],
  },
  {
    name: 'business',
    pricing: { monthly: 49.99, yearly: 39.99, lifetime: 999.99 },
    mediaCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 0, yearly: 0, lifetime: 0 },
    features: [
      {
        name: NewPackageFeature.F0,
        available: true,
        value: '500 Credit',
        isInfo: true,
        includes: [
          {
            name: '',
            value: 'Create ~ 100 Blogs / M',
          },
          {
            name: '',
            value: '',
          },
        ],
      },
      { name: NewPackageFeature.F1, available: true },
      { name: NewPackageFeature.F2, available: true },
      { name: NewPackageFeature.F3, available: true, value: 150 },
      { name: NewPackageFeature.F4, available: true, value: '150 MB' },
      { name: NewPackageFeature.F5, available: true, value: '120 Min' },
      { name: NewPackageFeature.F6, available: true, value: 'Unlimited' },
      {
        name: NewPackageFeature.F7,
        available: true,
        value: [
          {
            name: 'youtube',
            logo: '/images/icons/integrations/youtube.svg',
          },
          {
            name: 'youtube',
            logo: '/images/icons/integrations/tiktok-white.svg',
          },
          {
            name: 'vimeo',
            logo: '/images/icons/integrations/vimeo.svg',
          },
          {
            name: 'rumble',
            logo: '/images/icons/integrations/rumble.svg',
          },
          {
            name: 'sound cloud',
            logo: '/images/icons/integrations/soundcloud.svg',
          },
          {
            name: 'apple podcast',
            logo: '/images/icons/integrations/apple-podcasts.svg',
          },
          {
            name: 'google podcast',
            logo: '/images/icons/integrations/google-podcasts.svg',
          },
          {
            name: 'podbean',
            logo: '/images/icons/integrations/podbean.svg',
          },
        ],
      },
      { name: NewPackageFeature.F8, available: true },
      { name: NewPackageFeature.F9, available: true },
      { name: NewPackageFeature.F10, available: false },
      { name: NewPackageFeature.F11, available: true },
      { name: NewPackageFeature.F12, available: true },
      { name: NewPackageFeature.F13, available: true },
      { name: NewPackageFeature.F14, available: true },
    ],
  },
  {
    name: 'enterprise',
    pricing: { monthly: 99.99, yearly: 79.99, lifetime: 1999.99 },
    mediaCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    promptCreditPricing: { monthly: 1, yearly: 1, lifetime: 1 },
    discount: { monthly: 0, yearly: 0, lifetime: 0 },
    features: [
      {
        name: NewPackageFeature.F0,
        available: true,
        value: '1000 Credit',
        isInfo: true,
        includes: [
          {
            name: '',
            value: 'Create ~ 200 Blogs / M',
          },
          {
            name: '',
            value: '',
          },
        ],
      },
      { name: NewPackageFeature.F1, available: true },
      { name: NewPackageFeature.F2, available: true },
      { name: NewPackageFeature.F3, available: true, value: 150 },
      { name: NewPackageFeature.F4, available: true, value: '200 MB' },
      { name: NewPackageFeature.F5, available: true, value: '180 Min' },
      { name: NewPackageFeature.F6, available: true, value: 'Unlimited' },
      {
        name: NewPackageFeature.F7,
        available: true,
        value: [
          {
            name: 'youtube',
            logo: '/images/icons/integrations/youtube.svg',
          },
          {
            name: 'youtube',
            logo: '/images/icons/integrations/tiktok-white.svg',
          },
          {
            name: 'vimeo',
            logo: '/images/icons/integrations/vimeo.svg',
          },
          {
            name: 'rumble',
            logo: '/images/icons/integrations/rumble.svg',
          },
          {
            name: 'sound cloud',
            logo: '/images/icons/integrations/soundcloud.svg',
          },
          {
            name: 'apple podcast',
            logo: '/images/icons/integrations/apple-podcasts.svg',
          },
          {
            name: 'google podcast',
            logo: '/images/icons/integrations/google-podcasts.svg',
          },
          {
            name: 'podbean',
            logo: '/images/icons/integrations/podbean.svg',
          },
        ],
      },
      { name: NewPackageFeature.F8, available: true },
      { name: NewPackageFeature.F9, available: true },
      { name: NewPackageFeature.F10, available: true },
      { name: NewPackageFeature.F11, available: true },
      { name: NewPackageFeature.F12, available: true },
      { name: NewPackageFeature.F13, available: true },
      { name: NewPackageFeature.F14, available: true },
    ],
  },
];

export const NEW_UNLIMITED_PACKAGE = {
  name: 'unlimited',
  pricing: { monthly: 199.99, yearly: 1999.9, lifetime: 3999.99 },
  discount: { monthly: 0, yearly: 0, lifetime: 0 },
};

export const SHOW_MORE_INFO: PricingItem[] = [
  {
    name: 'Media to Blog',
    value: '10 Credit',
    feature: ['Video', 'Audio', 'Podcast, etc.', '/blog'],
  },
  {
    name: 'Text to Blog',
    value: '5 Credit',
    feature: ['Prompt', 'PDF', 'Doc', 'Notion, etc.', '/blog'],
  },
  {
    name: 'Additional Credits',
    value: '',
    feature: ['Buy additional credits at $CREDIT_PRICE / Credit'],
    isFooter: true,
  },
];

export interface PricingItem {
  name: string;
  value: string;
  feature: string[];
  isFooter?: boolean;
}

export default PACKAGES;
