import type {
  SocialIntegration,
  MediaIntegration,
  BlogIntegration,
  Integration,
} from '@/types/misc/integration.type';

const CACHE_TIME = {
  throttle: 2 * 1000,
  short: 5 * 60 * 1000,
  long: 15 * 60 * 1000,
};

const MEDIA: MediaIntegration[] = []; // spotify, 'apple'
const BLOGS: BlogIntegration[] = [
  'blogify',
  'wordpress',
  'wordpressorg',
  'blogger',
  'medium',
  'mailchimp',
  'zapier',
]; // , 'wix'
const BLOGS_WITH_SITES: Integration[] = [
  'wordpress',
  'blogger',
  'mailchimp',
  'facebook',
  'wordpressorg',
];
const BLOGS_WITH_DRAFT_FEATURE = ['wordpress', 'blogger', 'medium', 'wordpressorg'];
const BLOGS_WITH_TUTORIALS: Integration[] = ['wordpressorg', 'medium', 'zapier'];
const SOCIALS: SocialIntegration[] = ['twitter', 'linkedin']; // 'facebook'
const INTEGRATIONS: Integration[] = [...BLOGS, ...SOCIALS];

const INTEGRATION_DISPLAY_NAMES: Record<Integration, string> = {
  blogify: 'Blogify',
  wordpress: 'WordPress',
  wordpressorg: 'WordPress Custom Site',
  blogger: 'Blogger',
  medium: 'Medium',
  mailchimp: 'Mail Chimp',
  zapier: 'Zapier',
  wix: 'Wix',
  spotify: 'Spotify',
  facebook: 'Facebook',
  instagram: 'Instagram',
  twitter: 'X',
  linkedin: 'LinkedIn',
  youtube: 'YouTube',
};

export {
  INTEGRATION_DISPLAY_NAMES,
  BLOGS_WITH_DRAFT_FEATURE,
  BLOGS_WITH_TUTORIALS,
  BLOGS_WITH_SITES,
  INTEGRATIONS,
  CACHE_TIME,
  SOCIALS,
  BLOGS,
  MEDIA,
};
