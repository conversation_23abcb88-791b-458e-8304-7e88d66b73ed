const release: 'development' | 'staging' | 'production' = import.meta.env.VITE_RELEASE;

export default {
  release,
  isDev: release === 'development',
  isStage: release === 'staging',
  isProd: release === 'production',
  apiUrl: import.meta.env.VITE_API_URL,
  imagesUrl: import.meta.env.VITE_IMAGE_URL,
  stripeKey: import.meta.env.VITE_STRIPE_KEY,
  meta: {
    pixelId: import.meta.env.VITE_META_PIXEL_ID,
  },
} as {
  release: typeof release;
  isDev: boolean;
  isStage: boolean;
  isProd: boolean;
  apiUrl: string;
  imagesUrl: string;
  stripeKey: string;
  meta: {
    pixelId: number;
  };
};
