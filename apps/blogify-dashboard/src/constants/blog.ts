import type { BlogSourceType } from '@ps/types';

export const BLOG_TONES = [
  'Neutral',
  'Engaging',
  'Professional',
  'Informative',
  'News',
  'Promotional',
  'Conversational',
  'Storytelling',
  'Educational',
  'How-to',
  'Review',
  'Humorous',
  'Casual',
  'Inspirational',
];

const media = [
  "🕵️‍♀️ We're analyzing the media... 🔍",
  '📡 Our AI minions are decoding the media... 🤖',
  '🔮 Our team is working hard to convert the media into text... ✨',
  '🎤 The transcription process has begun... 📝',
];

const text = [
  '📝 Our wordsmiths are weaving their magic... 🪄',
  '🎩 Abracadabra! Your blog is in the making... 🎪',
  '🌟 The blog factory is buzzing with creativity... 🌟',
];

const common = [
  '🚀 Rockets are launching, and data is flowing... 🌌',
  '🌟 Crafting your blog post with care... 📜',
  '🎥 Lights, camera, AI Blogging! 🎬',
  '🚀 Blog writing can take days, we do it in few minutes... 📖',
  "✨ We're crafting your blog content... 📰",
  "🍳 We're putting the final touches on your blog... served hot and fresh, just the way you like it! 📰",
];
export const BLOG_INPROGRESS_MESSAGES: Record<BlogSourceType, string[]> = {
  video: [...media, ...common],
  audio: [...media, ...common],
  image: [...media, ...common],
  prompt: [...text, ...common],
  document: [`🕵️‍♀️ We're analyzing the document... 🔍`, ...text, ...common],
  'e-commerce': ['📡 Our AI minions are scrapping the url... 🤖', ...text, ...common],
  webLink: ['📡 Our AI minions are scrapping the url... 🤖', ...text, ...common],
};

export const BLOG_INPROGRESS_MESSAGES_BY_STATUS: Record<string, string> = {
  queued: media[0],
  request_dispatching: media[1],
  request_dispatched: media[2],
  transcribing: media[3],
  transcribed: common[0],
  summarizing: common[1],
  summarized: common[2],
  outline_generating: common[3],
  outline_generated: common[4],
  content_generating: common[5],
};

export const blogGenerationPilotMode = [
  {
    id: 1,
    title: 'Auto - Pilot',
    description:
      'Blogify will automatically generate and publish your blog. Please opt-in if you want to review first.',
    value: 'auto',
  },
  {
    id: 2,
    title: 'Co - Pilot',
    description: `You design the shape of your blog. From blog title to outline to content you'll be able to decide every step.`,
    value: 'assisted',
  },
];

export const BLOG_INPROGRESS_STATUSES = [
  'queued',
  'transcribing',
  'summarizing',
  'outline_generating',
  'content_generating',
];
export const BLOG_FAILED_STATUSES = [
  'failed',
  'transcription_failed',
  'summarization_failed',
  'outline_generation_failed',
  'content_generation_failed',
];
export const BLOG_IGNORED_STATUSES = [
  'keywords_generating',
  'keywords_generated',
  'publisher_dispatching',
  'publisher_dispatched',
];
