interface Country {
  name: string;
  iso2: string;
  iso3: string;
}
export const COUNTRIES: Country[] = [
  { iso2: '', iso3: '', name: 'Global' },
  { iso2: 'US', iso3: 'USA', name: 'United States' },
  { iso2: 'AL', iso3: 'ALB', name: 'Albania' },
  { iso2: 'DZ', iso3: 'DZA', name: 'Algeria' },
  { iso2: 'AR', iso3: 'ARG', name: 'Argentina' },
  { iso2: 'AM', iso3: 'ARM', name: 'Armenia' },
  { iso2: 'AU', iso3: 'AUS', name: 'Australia' },
  { iso2: 'AT', iso3: 'AUT', name: 'Austria' },
  { iso2: 'BH', iso3: 'BHR', name: 'Bahrain' },
  { iso2: 'BD', iso3: 'BGD', name: 'Bangladesh' },
  { iso2: 'BE', iso3: 'BEL', name: 'Belgium' },
  { iso2: 'BT', iso3: 'BTN', name: 'Bhutan' },
  { iso2: 'BR', iso3: 'BRA', name: 'Brazil' },
  { iso2: 'BN', iso3: 'BRN', name: 'Brunei' },
  { iso2: 'BG', iso3: 'BGR', name: 'Bulgaria' },
  { iso2: 'CM', iso3: 'CMR', name: 'Cameroon' },
  { iso2: 'CA', iso3: 'CAN', name: 'Canada' },
  { iso2: 'CL', iso3: 'CHL', name: 'Chile' },
  { iso2: 'CN', iso3: 'CHN', name: 'China' },
  { iso2: 'CZ', iso3: 'CZE', name: 'Czech Republic' },
  { iso2: 'DK', iso3: 'DNK', name: 'Denmark' },
  { iso2: 'EG', iso3: 'EGY', name: 'Egypt' },
  { iso2: 'FI', iso3: 'FIN', name: 'Finland' },
  { iso2: 'FR', iso3: 'FRA', name: 'France' },
  { iso2: 'DE', iso3: 'DEU', name: 'Germany' },
  { iso2: 'GR', iso3: 'GRC', name: 'Greece' },
  { iso2: 'GL', iso3: 'GRL', name: 'Greenland' },
  { iso2: 'HK', iso3: 'HKG', name: 'Hong Kong' },
  { iso2: 'HU', iso3: 'HUN', name: 'Hungary' },
  { iso2: 'IN', iso3: 'IND', name: 'India' },
  { iso2: 'ID', iso3: 'IDN', name: 'Indonesia' },
  { iso2: 'IE', iso3: 'IRL', name: 'Ireland' },
  { iso2: 'IL', iso3: 'ISR', name: 'Israel' },
  { iso2: 'IT', iso3: 'ITA', name: 'Italy' },
  { iso2: 'JP', iso3: 'JPN', name: 'Japan' },
  { iso2: 'JO', iso3: 'JOR', name: 'Jordan' },
  { iso2: 'KZ', iso3: 'KAZ', name: 'Kazakhstan' },
  { iso2: 'KE', iso3: 'KEN', name: 'Kenya' },
  { iso2: 'KW', iso3: 'KWT', name: 'Kuwait' },
  { iso2: 'LI', iso3: 'LIE', name: 'Liechtenstein' },
  { iso2: 'LU', iso3: 'LUX', name: 'Luxembourg' },
  { iso2: 'MO', iso3: 'MAC', name: 'Macau' },
  { iso2: 'MY', iso3: 'MYS', name: 'Malaysia' },
  { iso2: 'MU', iso3: 'MUS', name: 'Mauritius' },
  { iso2: 'MA', iso3: 'MAR', name: 'Morocco' },
  { iso2: 'NL', iso3: 'NLD', name: 'Netherlands' },
  { iso2: 'NZ', iso3: 'NZL', name: 'New Zealand' },
  { iso2: 'NG', iso3: 'NGA', name: 'Nigeria' },
  { iso2: 'NO', iso3: 'NOR', name: 'Norway' },
  { iso2: 'OM', iso3: 'OMN', name: 'Oman' },
  { iso2: 'PK', iso3: 'PAK', name: 'Pakistan' },
  { iso2: 'PS', iso3: 'PSE', name: 'Palestine' },
  { iso2: 'PG', iso3: 'PNG', name: 'Papua New Guinea' },
  { iso2: 'PE', iso3: 'PER', name: 'Peru' },
  { iso2: 'PH', iso3: 'PHL', name: 'Philippines' },
  { iso2: 'PL', iso3: 'POL', name: 'Poland' },
  { iso2: 'PT', iso3: 'PRT', name: 'Portugal' },
  { iso2: 'QA', iso3: 'QAT', name: 'Qatar' },
  { iso2: 'RO', iso3: 'ROU', name: 'Romania' },
  { iso2: 'RU', iso3: 'RUS', name: 'Russia' },
  { iso2: 'SA', iso3: 'SAU', name: 'Saudi Arabia' },
  { iso2: 'SC', iso3: 'SYC', name: 'Seychelles' },
  { iso2: 'SG', iso3: 'SGP', name: 'Singapore' },
  { iso2: 'ZA', iso3: 'ZAF', name: 'South Africa' },
  { iso2: 'ES', iso3: 'ESP', name: 'Spain' },
  { iso2: 'LK', iso3: 'LKA', name: 'Sri Lanka' },
  { iso2: 'SE', iso3: 'SWE', name: 'Sweden' },
  { iso2: 'CH', iso3: 'CHE', name: 'Switzerland' },
  { iso2: 'TW', iso3: 'TWN', name: 'Taiwan' },
  { iso2: 'TH', iso3: 'THA', name: 'Thailand' },
  { iso2: 'TN', iso3: 'TUN', name: 'Tunisia' },
  { iso2: 'TR', iso3: 'TUR', name: 'Turkey' },
  { iso2: 'AE', iso3: 'ARE', name: 'United Arab Emirates' },
  { iso2: 'GB', iso3: 'GBR', name: 'United Kingdom' },
  { iso2: 'US', iso3: 'USA', name: 'United States' },
  { iso2: 'UY', iso3: 'URY', name: 'Uruguay' },
];

export const COUNTRY_BY_ISO2: Record<string, Country> = COUNTRIES.reduce(
  (countries, country) => {
    countries[country.iso2] = country;
    return countries;
  },
  {} as Record<string, Country>
);

export const COUNTRY_BY_ISO3: Record<string, Country> = COUNTRIES.reduce(
  (countries, country) => {
    countries[country.iso3] = country;
    return countries;
  },
  {} as Record<string, Country>
);
