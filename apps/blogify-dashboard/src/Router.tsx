/* eslint-disable react-refresh/only-export-components */
import { createBrowserRouter } from 'react-router-dom';
import { Suspense } from 'react';

import { redirectIfNoSubscription, redirectIfNoAuth, redirectIfAuth } from '@/utils/auth';
import { RouteErrorBoundary, ErrorBoundary } from '@/components/error';
import getWritingSnippetsRoutes from '@/views/dashboard/writing-snippets/Routes';
import getYoutubeRoutes from '@/views/dashboard/youtube';
import getWebsiteRoutes from '@/views/dashboard/website';
import getBlogIdeasRoutes from '@/views/dashboard/blog-ideas/routes';
import getBlogRoutes from '@/views/dashboard/blog';
import getUserRoutes from '@/views/dashboard/user';
import safeLazy from '@/utils/safeLazy';
import Root from '@/views/Root';

import BlogifyLoader from './components/common/BlogifyLoader';

// Auth
const PasswordForgot = safeLazy(() => import('@/views/auth/PasswordForgot'));
const PasswordReset = safeLazy(() => import('@/views/auth/PasswordReset'));
const OAuth2Consent = safeLazy(() => import('@/views/auth/OAuth2Consent'));
const PaymentInfo = safeLazy(() => import('@/views/auth/PaymentInfo'));
const SignUp = safeLazy(() => import('@/views/auth/SignUp'));
const LogIn = safeLazy(() => import('@/views/auth/LogIn'));

// Dashboard
const Dashboard = safeLazy(() => import('@/views/dashboard'));
const Overview = safeLazy(() => import('@/views/dashboard/overview'));

// Other Dashboard Routes
const CreditUsageHistory = safeLazy(
  () => import('@/views/dashboard/payment/credits/CreditUsageHistory')
);
const TransactionHistory = safeLazy(
  () => import('@/views/dashboard/payment/transaction/TransactionHistory')
);
const UpcomingFeatures = safeLazy(() => import('@/views/dashboard/others/UpcomingFeatures'));
const Notifications = safeLazy(() => import('@/views/dashboard/notifications'));
const Subscription = safeLazy(() => import('@/views/dashboard/payment/subscription'));
const Analytics = safeLazy(() => import('@/views/dashboard/analytics'));
const Affiliate = safeLazy(() => import('@/views/dashboard/affiliate'));
const AffiliateBrands = safeLazy(() => import('@/views/dashboard/affiliate/Brands'));
const AffiliateLinks = safeLazy(() => import('@/views/dashboard/affiliate/Links'));
const PendingBalance = safeLazy(() => import('@/views/dashboard/affiliate/PendingBalance'));
const LinkLibrary = safeLazy(() => import('@/views/dashboard/link-library'));
const Addons = safeLazy(() => import('@/views/dashboard/addons'));
const Wallet = safeLazy(() => import('@/views/dashboard/wallet'));
const Images = safeLazy(() => import('@/views/dashboard/image/ImageList'));
// const UserList = safeLazy(() => import('@/views/dashboard/user/user-management/UserList'));

// Portal
const DeveloperPortal = safeLazy(() => import('@/views/dashboard/developer'));
const AppCreate = safeLazy(() => import('@/views/dashboard/developer/app/AppCreate'));
const AppList = safeLazy(() => import('@/views/dashboard/developer/app/AppList'));
const AppEdit = safeLazy(() => import('@/views/dashboard/developer/app/AppEdit'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<BlogifyLoader />}>
    <ErrorBoundary>
      <Component />
    </ErrorBoundary>
  </Suspense>
);

const router = createBrowserRouter([
  {
    path: '',
    element: <Root />,
    errorElement: <RouteErrorBoundary />,
    children: [
      // Auth
      { path: 'login', element: <Lazy as={LogIn} />, loader: redirectIfAuth },
      { path: 'signup', element: <Lazy as={SignUp} />, loader: redirectIfAuth },
      { path: 'payment', element: <Lazy as={PaymentInfo} />, loader: redirectIfNoAuth },
      { path: 'forgot-password', element: <Lazy as={PasswordForgot} />, loader: redirectIfAuth },
      {
        path: 'reset-password/:token',
        element: <Lazy as={PasswordReset} />,
        loader: redirectIfAuth,
      },
      {
        path: 'accept-invitation/:token',
        element: <Lazy as={PasswordReset} />,
        loader: redirectIfAuth,
      },
    ],
  },
  {
    path: 'oauth2-consent',
    element: <Lazy as={OAuth2Consent} />,
    loader: redirectIfNoAuth,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: 'dashboard',
    element: <Lazy as={Dashboard} />,
    errorElement: <RouteErrorBoundary />,
    loader: () => {
      const r = redirectIfNoAuth();
      if (r !== null) return r;
      return redirectIfNoSubscription();
    },
    children: [
      { path: '', element: <Lazy as={Overview} /> },
      // Blog
      ...getBlogRoutes(),
      // Blog Ideas
      ...getBlogIdeasRoutes(),
      // Websites
      ...getWebsiteRoutes(),
      // Other Dashboard Routes
      { path: 'analytics', element: <Lazy as={Analytics} /> },
      { path: 'affiliate', element: <Lazy as={Affiliate} /> },
      { path: 'affiliate/brands', element: <Lazy as={AffiliateBrands} /> },
      { path: 'affiliate/links', element: <Lazy as={AffiliateLinks} /> },
      { path: 'affiliate/pending-balance', element: <Lazy as={PendingBalance} /> },
      { path: 'link-library', element: <Lazy as={LinkLibrary} /> },
      { path: 'wallet', element: <Lazy as={Wallet} /> },
      { path: 'images', element: <Lazy as={Images} /> },
      { path: 'subscription', element: <Lazy as={Subscription} /> },
      { path: 'transaction-history', element: <Lazy as={TransactionHistory} /> },
      { path: 'credits-usage-history', element: <Lazy as={CreditUsageHistory} /> },
      { path: 'notifications', element: <Lazy as={Notifications} /> },
      { path: 'upcoming-features', element: <Lazy as={UpcomingFeatures} /> },
      // User & Settings
      ...getUserRoutes(),
      // Addons
      { path: 'addons', element: <Lazy as={Addons} /> },
      ...getWritingSnippetsRoutes(),
      ...getYoutubeRoutes(),
    ],
  },
  {
    // Developer Portal
    path: 'developer',
    element: <Lazy as={DeveloperPortal} />,
    errorElement: <RouteErrorBoundary />,
    loader: redirectIfNoAuth,
    children: [
      { path: '', element: <Lazy as={AppList} /> },
      { path: 'apps/create', element: <Lazy as={AppCreate} /> },
      { path: 'apps/:id/edit', element: <Lazy as={AppEdit} /> },
    ],
  },
]);

export default router;
