import React from 'react';
import { QueryClientProvider } from 'react-query';
import { HelmetProvider } from 'react-helmet-async';
import { RouterProvider } from 'react-router-dom';
import { StoreProvider } from 'easy-peasy';
import { registerSW } from 'virtual:pwa-register';
import { createRoot } from 'react-dom/client';
import { Toaster } from 'react-hot-toast';

import { ErrorBoundary } from '@/components/error';
import { queryClient } from '@/services/api';
import FullPageSpinner from '@/components/misc/FullPageSpinner';

import TourGuideProviders from './components/providers/TourGuideProviders';
import reportWebVitals from './report-web-vitals';
import BaseApp from './App';
import router from './Router';
import Store from './store';
import './index.scss';

// eslint-disable-next-line react-refresh/only-export-components
const App = () => (
  <React.StrictMode>
    <ErrorBoundary>
      <HelmetProvider>
        <StoreProvider store={Store}>
          <TourGuideProviders>
            <QueryClientProvider client={queryClient}>
              <RouterProvider
                router={router}
                fallbackElement={<FullPageSpinner />}
                future={{ v7_startTransition: true }}
              />
              <BaseApp />
            </QueryClientProvider>
          </TourGuideProviders>
        </StoreProvider>
      </HelmetProvider>
      <Toaster />
    </ErrorBoundary>
  </React.StrictMode>
);

const rootElement = document.getElementById('root') as HTMLElement;
const root = createRoot(rootElement);
if (root !== null) root.render(<App />);

registerSW({ immediate: true });

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
