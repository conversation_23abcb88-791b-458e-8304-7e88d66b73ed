import type { User } from '@/types/resources/user.type';

import asyncScriptLoad from '@/utils/asyncScriptLoad';

export async function init() {
  const scriptUrl = 'https://cdn.firstpromoter.com/fpr.js';
  window.fpr =
    window.fpr ||
    function (...args: any[]) {
      window.fpr.q = window.fpr.q || [];
      window.fpr.q[args[0] == 'set' ? 'unshift' : 'push'](args);
    };
  window.fpr('init', { cid: 'ppf9wpkq' });
  window.fpr('click');
  return asyncScriptLoad('FirstPromoter', scriptUrl);
}

export async function trackSubscription(user: User) {
  return init().then(() => window.fpr('referral', { email: user.email }));
}
