let shareASaleScript: HTMLScriptElement;

const insertTrackingImage = ({
  orderId,
  amount,
  coupon,
  transType,
}: {
  orderId: string;
  amount: string;
  coupon: string;
  transType: 'lead' | 'sale';
}) => {
  const img = document.createElement('img');
  img.id = '_SHRSL_img_1';
  img.width = 1;
  img.height = 1;
  img.src = `https://www.shareasale.com/sale.cfm?tracking=${orderId}&amount=${amount}&merchantID=${
    import.meta.env.VITE_SHARE_A_SALE_MERCHANT_ID
  }&v=dev1.5&transtype=${transType}&coupon=${coupon}`;
  document.querySelector('body')?.appendChild(img);
};

const insertTrackingScript = () => {
  if (shareASaleScript || !import.meta.env.VITE_SHARE_A_SALE_MASTER_TAG_ID) return;
  shareASaleScript = document.createElement('script');
  shareASaleScript.type = 'text/javascript';
  shareASaleScript.defer = true;
  shareASaleScript.src = `https://www.dwin1.com/${
    import.meta.env.VITE_SHARE_A_SALE_MASTER_TAG_ID
  }.js`;
  document.querySelector('head')?.appendChild(shareASaleScript);
};

export const trackLead = ({ orderId, coupon }: { orderId: string; coupon: string }) => {
  try {
    insertTrackingImage({ orderId, amount: '0.00', coupon, transType: 'lead' });
    insertTrackingScript();
  } catch (e) {}
};

export default { trackLead };
