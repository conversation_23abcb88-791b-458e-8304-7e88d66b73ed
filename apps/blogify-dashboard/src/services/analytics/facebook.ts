import type { Transaction } from '@/types/resources/transaction.type';
import type { User } from '@/types/resources/user.type';

import config from '@/constants/config';

export const identify = (user: User) => {
  if (!window.fbq) return;
  window.fbq('init', config.meta.pixelId, {
    external_id: String(user._id),
    em: user.email ? user.email.toLowerCase() : null,
    fn: user.name ? user.name.split(' ')[0]?.toLowerCase() : null,
    ln: user.name ? user.name.split(' ')[1]?.toLowerCase() : null,
  });
};

const track = (
  eventName: string,
  properties: Record<string, string | number | boolean | undefined> = {},
  eventID?: string,
  customEvent?: boolean
) => {
  if (!window.fbq) return;
  if (customEvent) {
    window.fbq('trackCustom', eventName, properties);
  } else {
    window.fbq('track', eventName, properties, { eventID });
  }
};

export const trackSubscription = (
  {
    _id,
    amount,
    description,
    coupon,
  }: Pick<Transaction, '_id' | 'description' | 'amount' | 'coupon'>,
  user: User
) =>
  track(
    'conversion',
    {
      orderId: _id,
      orderValue: amount,
      orderPromoCode: coupon,
      orderDescription: description,
      customerId: user._id,
      customerStatus: 'New',
      currencyCode: 'USD',
    },
    undefined,
    true
  );
