import type { Transaction } from '@/types/resources/transaction.type';
import type { User } from '@/types/resources/user.type';

// import * as GoogleAnalytics from '@/services/analytics/google-analytics';
import * as FirstPromoter from '@/services/analytics/first-promoter';
import * as Facebook from '@/services/analytics/facebook';
import * as Impact from '@/services/analytics/impact';
import shareASale from '@/services/analytics/share-a-sale';
import config from '@/constants/config';

export async function subscription(
  trxn: Pick<Transaction, '_id' | 'amount' | 'description' | 'coupon'>,
  user: User
) {
  if (config.isDev) return;
  window.dataLayer.push({
    event: 'conversion',
    transaction_id: trxn._id,
    coupon: trxn.coupon,
    value: trxn.amount,
    email: user.email,
    currency: 'USD',
  });

  shareASale.trackLead({
    orderId: user.business as string,
    // amount: trxn.amount.toString(),
    coupon: trxn.coupon || '',
  });

  // GoogleAnalytics.trackSubscription(trxn.amount);
  Facebook.trackSubscription(trxn, user);
  await FirstPromoter.trackSubscription(user);
  await Impact.trackSubscription(trxn, user);
}
