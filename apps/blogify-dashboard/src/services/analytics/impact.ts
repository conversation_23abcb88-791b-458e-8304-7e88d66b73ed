import type { Transaction } from '@/types/resources/transaction.type';
import type { User } from '@/types/resources/user.type';

import { toFixedWithoutRound, getDiscountAmount } from '@/utils';
import { cacheGet } from '@/utils/localStorageCache';

// Using ChatGPT
async function calculateSHA1(email: string) {
  // Convert the string to an ArrayBuffer
  const encoder = new TextEncoder();
  const data = encoder.encode(email);

  // Calculate the SHA-1 hash
  const hashBuffer = await crypto.subtle.digest('SHA-1', data);

  // Convert the hash to a hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((byte) => byte.toString(16).padStart(2, '0')).join('');

  return hashHex;
}

export async function trackSubscription(
  { amount, description, coupon }: Pick<Transaction, 'description' | 'amount' | 'coupon'>,
  user: User
) {
  const clickid = cacheGet('irClickId');
  const payload = {
    ...(clickid ? { clickid } : {}),
    // orderId: user.business,
    customerId: user.business,
    customerEmail: await calculateSHA1(user.email),
    customerStatus: 'New',
    currencyCode: 'USD',
    orderPromoCode: coupon,
    orderDiscount: toFixedWithoutRound(await getDiscountAmount(amount, coupon), 2),
    items: [
      {
        category: 'Trial',
        sku: description,
        subTotal: 0,
        quantity: 1,
      },
    ],
  };

  window.ire('trackConversion', 42784, payload, { verifySiteDefinitionMatch: true });
}
