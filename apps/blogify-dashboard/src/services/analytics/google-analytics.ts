import type { Location } from 'react-router-dom';

import ReactGA from 'react-ga4';

import { getUTMParams } from '@/utils/getUTMParams';

const init = () => {
  ReactGA.initialize('G-HG6C8JR5MZ', { gaOptions: { debug_mode: true } });
};

const trackPageView = (location: Location) => {
  ReactGA.send({ hitType: 'pageview', page: location.pathname + location.search });
};

const trackSignup = () => {
  const utms = getUTMParams();
  console.log('UTM Params:', utms.utm_source, utms.utm_campaign);
  ReactGA.event('sign_up', {
    method: 'Email',
    ...utms,
  });
};

const trackLogin = (method: 'Email' | 'Google' | string = 'Email') => {
  const utms = getUTMParams();
  ReactGA.gtag('event', 'login', {
    method,
    ...utms,
    debud_mode: true,
  });
};

const trackBlogCreate = (sourceTitle: string) => {
  const utms = getUTMParams();

  ReactGA.event('blog_create', {
    blog_title: sourceTitle,
    value: 1,
    ...utms,
  });
};

const trackSubscription = (subscriptionPrice: number, subscriptionPlan: string) => {
  console.log('Subscription price:', subscriptionPrice);
  const utms = getUTMParams();

  ReactGA.event('subscribe', {
    value: subscriptionPrice,
    plan: subscriptionPlan,
    currency: 'USD',
    ...utms,
  });
};

const trackFeaturesView = () => {
  ReactGA.event('view_feature_section', {
    section: 'Features',
  });
};

const trackPricingView = () => {
  ReactGA.event('view_pricing_section', {
    section: 'Pricing',
  });
};

const trackReviewsView = () => {
  ReactGA.event('view_reviews_section', {
    section: 'Reviews',
  });
};

const trackSiteBrowse = (duration: number) => {
  ReactGA.event('site_browse_duration', {
    duration_seconds: duration,
  });
};

const trackPaymentClick = (source: string = 'unknown') => {
  const utms = getUTMParams();

  console.log('Tracking payment click:', { source, ...utms });

  ReactGA.event('payment_click', {
    source,
    ...utms,
  });
};

export {
  trackSubscription,
  trackFeaturesView,
  trackPricingView,
  trackReviewsView,
  trackSiteBrowse,
  trackPageView,
  trackSignup,
  trackLogin,
  trackBlogCreate,
  trackPaymentClick,
  init,
};
