import { API } from '@/services/api';

const urlBase64ToUint8Array = (base64String: string) => {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/\-/g, '+').replace(/_/g, '/');

  const rawData = atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }

  return outputArray;
};

const onStateChange = async (worker: ServiceWorker) =>
  new Promise((resolve, reject) =>
    worker?.addEventListener('statechange', (event) => {
      const target = event.target as ServiceWorker;
      if (target?.state === 'activated') {
        return resolve(true);
      } else if (target?.state === 'redundant') {
        return reject(new Error('Service worker installation failed.'));
      }
    })
  );

export default async function subscribeForPushNotification() {
  if (!('Notification' in window && 'serviceWorker' in navigator)) return;

  const permission = await Notification.requestPermission();
  if (permission !== 'granted') return;

  try {
    const registration = await navigator.serviceWorker.register('/sw/push.js');

    if (registration.installing) {
      await onStateChange(registration.installing);
    } else if (registration.waiting) {
      await onStateChange(registration.waiting);
    }

    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(import.meta.env.VITE_VAPID_PUBLIC_KEY || ''),
    });

    await API.post('/notifications/push/subscription', subscription);
    console.log('Subscribed for Push Notifications.');
  } catch (e) {
    console.log('Failed to Subscribe for Push Notifications: ', e);
  }
}
