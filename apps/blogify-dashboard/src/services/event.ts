import type { YoutubeVideoContent } from '@/types/resources/youtube-contents.type';
import type { BlogEvents, Blog } from '@ps/types';
import type { Notification } from '@/types/resources/notification.type';

import { Subscription, Subject } from 'rxjs';

class EventRegistry<Events, Data> {
  private events: Map<Events, Subject<Data>>;

  constructor() {
    this.events = new Map();
  }

  emit(event: Events, data: Data): void {
    const subject = this.getEvent(event);
    subject.next(data);
  }

  subscribe(event: Events, callback: (_: Data) => unknown): Subscription {
    const subject = this.getEvent(event);
    return subject.subscribe(callback);
  }

  private getEvent(event: Events): Subject<Data> {
    if (!this.events.has(event)) {
      this.events.set(event, new Subject());
    }
    const subject = this.events.get(event) || new Subject();
    return subject;
  }
}

const events = new EventRegistry<'NOTIFICATION', Notification>();

const blogEvents = new EventRegistry<
  BlogEvents,
  Pick<
    Blog,
    | '_id'
    | 'status'
    | 'seoAnalysisStatus'
    | 'affiliateLinkGenerationStatus'
    | 'generationMode'
    | 'failReason'
  > & {
    percentComplete?: number;
  }
>();

const youtubeEvents = new EventRegistry<'YOUTUBE_CONTENT_UPDATE', YoutubeVideoContent>();

export { events, blogEvents, youtubeEvents };
