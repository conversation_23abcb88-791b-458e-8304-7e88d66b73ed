import config from '@/constants/config';

let stripe: any;
let stripeScript: HTMLScriptElement;
let stripeCardElement: any;
let elements: any;

const insertStripeInput = (onChange?: (_: any) => void) => {
  elements = stripe.elements({
    mode: 'subscription',
    currency: 'usd',
    amount: 0,
  });
  const paymentElement = elements.create('payment', {
    layout: {
      type: 'accordion',
      defaultCollapsed: false,
      radios: true,
      spacedAccordionItems: false,
    },
  });
  stripeCardElement = paymentElement;
  stripeCardElement.mount(`#card-element`);
  stripeCardElement.addEventListener('change', function (event: any) {
    if (onChange) onChange(event);
    ['card-element-errors', 'card-element-errors-2'].forEach((id) => {
      const el = document.getElementById(id);
      if (el) {
        el.textContent = event.error ? event.error.message : '';
      }
    });
  });
};

const initStripe = (callback: () => void = insertStripeInput) => {
  if (stripe) return callback();

  if (!stripeScript) {
    stripeScript = document.createElement('script');
    stripeScript.src = 'https://js.stripe.com/v3/';
    document.getElementsByTagName('head')[0].appendChild(stripeScript);

    stripeScript.addEventListener('load', () => {
      if (stripe) return callback();
      stripe = window.Stripe(config.stripeKey);
      callback();
    });
  } else {
    stripeScript.addEventListener('load', () => {
      if (stripe) return callback();
      stripe = window.Stripe(config.stripeKey);
      callback();
    });
  }
};

export { insertStripeInput, initStripe, stripe, elements, stripeCardElement };
