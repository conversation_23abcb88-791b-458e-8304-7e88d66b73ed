import type { Socket } from 'socket.io-client';

import { io } from 'socket.io-client';

import config from '@/constants/config';

import { youtubeEvents, blogEvents, events } from './event';

class SocketService {
  public socket: Socket | null = null;

  connect() {
    this.socket = io(config.apiUrl, {
      transports: ['websocket'],
      auth: {
        token: `Bearer ${localStorage.getItem('token')}`,
      },
    });

    this.socket.on('connect', () => {
      if (this.socket) {
        this.socket.on('NOTIFICATION', (data) => {
          events.emit('NOTIFICATION', data);
        });

        this.socket.on('BLOG_STATUS_UPDATE', (data) => {
          blogEvents.emit('BLOG_STATUS_UPDATE', data);
        });
        this.socket.on('BLOG_SEO_ANALYSIS_STATUS_UPDATE', (data) => {
          blogEvents.emit('BLOG_SEO_ANALYSIS_STATUS_UPDATE', data);
        });
        this.socket.on('BLOG_AFFILIATE_LINK_GENERATION_STATUS_UPDATE', (data) => {
          blogEvents.emit('BLOG_AFFILIATE_LINK_GENERATION_STATUS_UPDATE', data);
        });

        this.socket.on('YOUTUBE_CONTENT_UPDATE', (data) => {
          youtubeEvents.emit('YOUTUBE_CONTENT_UPDATE', data);
        });
      }
    });

    // this.socket.on('disconnect', () => {});

    // this.socket.on('error', (error) => {});
  }
}

export default new SocketService();
