export type APIQueryParamsType = Record<string, string | number | boolean | undefined>;

const buildQueryString = (params: APIQueryParamsType) =>
  Object.keys(params)
    .filter((key) => params[key] !== undefined)
    .map((key: string) => `${key}=${encodeURIComponent(String(params[key]))}`)
    .join('&');

function getQueryString(path: string, params: APIQueryParamsType = {}) {
  let query = /\?/.test(path) ? `${path}&` : `${path}?`;

  query = query + buildQueryString(params);

  return query;
}

function getQuery(path = window.location.search): Record<string, string> {
  const params = new URLSearchParams(path);

  const query: Record<string, string> = {};
  for (const [key, value] of params.entries()) {
    query[key] = value;
  }

  return query;
}

function appendToQuery(records: Record<string, any>, reset: boolean = false): string {
  const query = { ...(reset ? {} : getQuery()), ...records };
  delete query.current;

  const newQuery = Object.keys(query).reduce(
    (q, r) => {
      if (
        (!Array.isArray(query[r]) && query[r] && query[r] !== 'all') ||
        (Array.isArray(query[r]) && !!query[r].length)
      ) {
        q[r] = encodeURI(query[r]);
      }
      return q;
    },
    {} as Record<string, any>
  );

  return new URLSearchParams(newQuery).toString();
}

function removeFromQuery(param: string): string {
  const query = getQuery();
  delete query.current;
  delete query[param];

  return new URLSearchParams(query).toString();
}

export default getQueryString;
export { removeFromQuery, appendToQuery, getQuery };
