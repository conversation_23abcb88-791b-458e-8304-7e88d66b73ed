import type {
  AxiosRequestConfig,
  AxiosResponse,
  AxiosAdapter,
  AxiosPromise,
  AxiosError,
} from 'axios';

import axios from 'axios';
import { throttleAdapterEnhancer } from 'axios-extensions';
import { QueryClient } from 'react-query';

import { CACHE_TIME } from '@/constants/index';
import { logoutUser } from '@/utils/auth';
import { cacheGet, cacheSet } from '@/utils/localStorageCache';
import config from '@/constants/config';

import getQuery, { APIQueryParamsType } from './query';

// these errors happen on expired or malformed token, remove token from cache and reload page
const errorsRequiringReload = ['token', 'signature', 'unauthorized'];
const ERROR_MSG_DEFAULT = 'Something went wrong! Please try again.';

const errorHandler = (err: AxiosError) => {
  try {
    // @ts-ignore null check present
    const message = err?.response?.data?.message || err?.message;

    if (message && typeof message === 'object') {
      return Promise.reject(message);
    }
    if (message) {
      for (const _error of errorsRequiringReload) {
        const _message = message.toLowerCase();
        if (_message.includes(_error) && !_message.includes('cannot')) {
          logoutUser();
          return;
        }
      }
      return Promise.reject(message);
    }
    return Promise.reject(ERROR_MSG_DEFAULT);
  } catch (_) {
    return Promise.reject(ERROR_MSG_DEFAULT);
  }
};

const headers = {};

const axiosInstance = axios.create({
  baseURL: config.apiUrl,
  // timeout: 25 * 1000,
  headers,
  adapter: throttleAdapterEnhancer(axios.defaults.adapter as AxiosAdapter, {
    threshold: CACHE_TIME.throttle,
  }),
});

// Refresh token interceptor
let isRefreshing = false;
let failedQueue: Array<{ resolve: (value: any) => void; reject: (reason: any) => void }> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest: AxiosRequestConfig & { _retry?: boolean } = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          })
          .catch((err) => {
            console.error('Interceptor: Queued request failed:', err);
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = cacheGet('refresh_token');
        if (!refreshToken) {
          logoutUser();
          return await Promise.reject(new Error('No refresh token'));
        }
        const response = await axiosInstance.post('/auth/refresh', { refresh_token: refreshToken });
        const { access_token, refresh_token: newRefreshToken } = response.data;

        cacheSet('token', access_token);
        cacheSet('refresh_token', newRefreshToken);
        axiosInstance.defaults.headers.common.Authorization = `Bearer ${access_token}`;
        originalRequest.headers = originalRequest.headers || {};
        originalRequest.headers.Authorization = `Bearer ${access_token}`;

        processQueue(null, access_token);
        return await axiosInstance(originalRequest);
      } catch (refreshError: any) {
        console.error(
          'Interceptor: Refresh failed:',
          refreshError.response?.data || refreshError.message
        );
        processQueue(refreshError, null);
        const errorMessage = refreshError.response?.data?.message?.toLowerCase() || '';
        if (
          refreshError.response?.status === 400 &&
          (errorMessage.includes('refresh token not provided') ||
            errorMessage.includes('invalid refresh token'))
        ) {
          console.log('Interceptor: Invalid or missing refresh token, logging out');
          logoutUser();
        } else if (refreshError.response?.status === 401) {
          console.log('Interceptor: Unauthorized refresh attempt, logging out');
          logoutUser();
        }
        return await Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
        console.log('Interceptor: Refresh process completed');
      }
    }

    console.error('Interceptor: Non-401 error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

const api = () => {
  const token = cacheGet('token');
  if (token) {
    axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
  } else {
    delete axiosInstance.defaults.headers.common.Authorization;
  }
  return axiosInstance;
};

const isExternalAPI = (path: string): boolean => /:\/\//.test(path);
const responseHandler = <T>(res: AxiosPromise<T>) =>
  res.then((resp: AxiosResponse<T>) => resp.data).catch((err: AxiosError<T>) => errorHandler(err));

// API Methods
const fetch = <T>(path: string, params: APIQueryParamsType = {}) =>
  responseHandler<T>(isExternalAPI(path) ? axios.get(path) : api().get(getQuery(path, params)));

const fetchBlob = <T>(path: string, params: APIQueryParamsType = {}) =>
  responseHandler<T>(
    isExternalAPI(path)
      ? axios.get(path, { responseType: 'blob' }) // Set responseType to 'blob'
      : api().get(getQuery(path, params), { responseType: 'blob' }) // Set responseType to 'blob'
  );

const post = <T>(
  path: string,
  body: unknown = {},
  options: AxiosRequestConfig<unknown> | undefined = {}
) =>
  responseHandler<T>(
    isExternalAPI(path) ? axios.post(path, body, options) : api().post(path, body)
  );

const put = <T>(
  path: string,
  body: unknown = {},
  options: AxiosRequestConfig<unknown> | undefined = {}
) =>
  responseHandler<T>(isExternalAPI(path) ? axios.put(path, body, options) : api().put(path, body));

const patch = <T>(
  path: string,
  body: unknown = {},
  options: AxiosRequestConfig<unknown> | undefined = {}
) =>
  responseHandler<T>(
    isExternalAPI(path) ? axios.patch(path, body, options) : api().patch(path, body)
  );

const remove = <T>(path: string) =>
  responseHandler<T>(isExternalAPI(path) ? axios.delete(path) : api().delete(path));

const API = { fetch, post, put, patch, remove, fetchBlob };

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // @ts-ignore override
      queryFn: ({
        queryKey: [path, params],
        pageParam,
      }: {
        queryKey: [path: string, params: APIQueryParamsType];
        pageParam: { pageToken: string };
      }) => {
        // if (pageParam !== undefined && params && params?.page === undefined) {
        //   params.page = pageParam;
        // }
        if (pageParam && pageParam.pageToken) {
          // eslint-disable-next-line no-param-reassign
          params = params || {};
          params.pageToken = pageParam.pageToken;
        }
        return API.fetch(path, params);
      },
      staleTime: CACHE_TIME.throttle,
      cacheTime: CACHE_TIME.short,
      keepPreviousData: true,
      retry: 3,
    },
  },
});

export default API;

export { queryClient, errorHandler, ERROR_MSG_DEFAULT };
