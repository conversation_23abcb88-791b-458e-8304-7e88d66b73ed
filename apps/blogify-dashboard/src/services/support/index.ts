import type { User } from '@/types/resources/user.type';

// import { setIntercomUser, initIntercom } from '@/services/support/intercom';
import { setCrispUser, initCrisp } from '@/services/support/crisp';

const setUserForCS = (user: User) => {
  // setIntercomUser(user);
  setCrispUser(user);
};

const initSupport = (user: User, token?: string) => {
  // initIntercom(user);
  initCrisp(user, token);
};

export { setUserForCS, initSupport };
