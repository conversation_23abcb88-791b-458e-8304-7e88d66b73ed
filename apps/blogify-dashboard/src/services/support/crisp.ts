import type { User } from '@/types/resources/user.type';

import { decodeJWT } from '@ps/common/utils/jwt';
import { cacheGet } from '@/utils/localStorageCache';
import asyncScriptLoad from '@/utils/asyncScriptLoad';
import config from '@/constants/config';
import Store from '@/store';

const crispWebsiteId = import.meta.env.VITE_SUPPORT_PARTNER_ID || '';

const setCrispUser = (user: User) => {
  const { $crisp } = window;

  if (user._id) {
    const avatar = user.profilePicture ? { type: 'avatar', image_url: user.profilePicture } : null;
    $crisp.push(['set', 'user:nickname', [user.name]]);
    $crisp.push(['set', 'user:email', [user.email, user.crispEmailDigest]]);
    $crisp.push(['set', 'user:avatar', [avatar]]);

    // Set custom user data
    $crisp.push([
      'set',
      'session:data',
      [
        [
          ['plan_name', user.subscriptionPlan],
          ['plan_status', user.subscriptionStatus],
          ['credits', user.credits.toString()],
          ['monthly_credits', user.monthlyCredits.toString()],
          ['user_status', user.status],
          ['user_role', user.role],
          ['uid', user._id],
          ['bid', user.business],
        ],
      ],
    ]);

    window.CRISP_TOKEN_ID = user._id;
    $crisp.push(['do', 'session:reset']);
  } else {
    $crisp.push(['set', 'user:nickname', ['']]);
    $crisp.push(['set', 'user:email', ['']]);
    $crisp.push(['set', 'user:avatar', ['']]);
  }
  $crisp.push([
    'on',
    'chat:initiated',
    () => $crisp.push(['set', 'message:text', ['I have a question']]),
  ]);
};

const initCrisp = (user: User = Store.getState().user.current, _token?: string) => {
  if (!config.isProd) return;
  const token = _token || cacheGet<string>('token');
  if (token) {
    const { impersonation } = decodeJWT(token);
    if (impersonation) return;
  }

  window.$crisp = [];
  window.CRISP_WEBSITE_ID = crispWebsiteId;
  window.CRISP_RUNTIME_CONFIG = {
    session_merge: true,
  };
  asyncScriptLoad('crisp-script', `https://client.crisp.chat/l.js`, '$crisp').then(() => {
    setCrispUser(user);
  });
};

export { setCrispUser, initCrisp };
