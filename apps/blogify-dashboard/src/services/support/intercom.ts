// Unused
// import type { User } from '@/types/resources/user.type';

// import Store from '@/store';

// import asyncScriptLoad from '@/utils/asyncScriptLoad';

// const intercomAppId = import.meta.env.VITE_INTERCOM || '';

// const hideIntercom = (hide: boolean) => {
//   const body = document.querySelector('body');
//   if (body) {
//     body.classList.toggle('hide-intercom', hide);
//   }
// };

// const setIntercomUser = (user: User) => {
//   if (window.Intercom) {
//     if (user._id) {
//       window.Intercom('update', {
//         avatar: user.profilePicture ? { type: 'avatar', image_url: user.profilePicture } : null,
//         name: user.name,
//         email: user.email,
//         user_id: user._id,
//       });
//     } else {
//       window.Intercom('update', {
//         avatar: null,
//         name: null,
//         email: null,
//         user_id: null,
//       });
//     }
//   }
// };

// const initIntercom = ({
//   user = Store.getState().user.current,
//   forceShow,
// }: {
//   user?: User;
//   forceShow?: boolean;
// }) => {
//   if (!config.isProd) return;

//   asyncScriptLoad(
//     'intrcm-scrpt',
//     `https://widget.intercom.io/widget/${intercomAppId}`,
//     'Intercom'
//   ).then(() => {
//     const { Intercom } = window;

//     Intercom('reattach_activator');
//     Intercom('boot', { app_id: intercomAppId });

//     if (user._id) {
//       Intercom('update', {
//         avatar: user.profilePicture ? { type: 'avatar', image_url: user.profilePicture } : null,
//         name: user.name,
//         email: user.email,
//         user_id: user._id,
//       });
//     } else {
//       Intercom('update', {
//         avatar: null,
//         name: null,
//         email: null,
//         user_id: null,
//       });
//     }

//     if (forceShow) {
//       hideIntercom(false);
//       Intercom('show');
//     }
//   });
// };

// export { setIntercomUser };
