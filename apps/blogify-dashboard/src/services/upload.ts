import { API } from './api';

export const getDataUrl = (file: File): Promise<string> => {
  if (!file.type.startsWith('image')) return Promise.resolve('');
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (ev: any) => resolve(ev.target.result);
    reader.readAsDataURL(file);
  });
};

export const uploadFile = async (
  file: File,
  folder: string,
  setProgress: (_: number) => void
): Promise<string> => {
  const body = {
    folderName: folder,
    fileName: file.name,
    fileType: file.type,
  };

  const data = await API.post<{ url: string; fields: Record<string, string> }>(
    '/context/upload',
    body
  );
  if (!data) return '';
  const formData = new FormData();
  Object.keys(data.fields).forEach((key) => formData.append(key, data.fields[key]));
  formData.append('Content-Type', file.type || '');
  formData.append('file', file as unknown as Blob);

  return API.post(data.url, formData, {
    onUploadProgress: (progressEvent) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      setProgress(percentCompleted);
    },
  }).then(() => {
    const url = `${data.url}/${encodeURIComponent(data.fields.key)}`;
    return url;
  });
};
