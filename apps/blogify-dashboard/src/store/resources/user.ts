import type { UserAddonInfo, User } from '@/types/resources/user.type';

import { Computed, computed, Action, action, Thunk, thunk } from 'easy-peasy';
import dayjs from 'dayjs';

import { cacheGet, cacheSet } from '@/utils/localStorageCache';
import { setUserForCS } from '@/services/support';
import { logoutUser } from '@/utils/auth';
import { Addon } from '@/types/resources/product.type';
import { API } from '@/services/api';
import anonymousUser from '@/types/resources/user.type';

export type EmailPassword = {
  email: string;
  password: string;
};

export type UserStore = {
  isAuthenticated: Computed<UserStore, boolean>;
  current: User;
  set: Action<UserStore, User>;

  fetch: Thunk<UserStore>;
  update: Thunk<UserStore, Partial<User>>;
  signUp: Thunk<UserStore, Partial<User> & EmailPassword>;
  login: Thunk<UserStore, EmailPassword>;
  logout: Thunk<UserStore>;
  onAuthorize: Thunk<UserStore, { token: string; refresh_token?: string }>;
};

const isAuthenticated = () => !!cacheGet('token');
const fetchUser = () => API.fetch<User>('users/me');

const findAddon = (addons: User['addons'], name: Addon): UserAddonInfo => {
  const id = Object.keys(addons).find((addonId) => addons[addonId].name === name);
  return id ? addons[id] : { addon: '' };
};

const isAddOnActive = (addons: User['addons'], id: string) => {
  const addon = addons?.[id];
  return !!(
    ['active', 'deactivated'].includes(addon?.status as string) &&
    dayjs(addon?.expirationDate).diff(dayjs(), 'days') >= 0
  );
};

const hasAddon = (user: User, name: Addon) => {
  const addon = user.addons && findAddon(user.addons, name);
  return !!addon && isAddOnActive(user.addons, addon.addon as string);
};

const userStore: UserStore = {
  isAuthenticated: computed(isAuthenticated),
  current: anonymousUser,
  set: action((store, user) => {
    try {
      user.hasSnippetsAddon = hasAddon(user, Addon.Snippet);
      user.snippetsAddon = user.hasSnippetsAddon ? findAddon(user.addons, Addon.Snippet) : {};

      user.hasYouTubeProAddon = hasAddon(user, Addon.YouTubePro);
      user.hasYouTubeAddon = user.hasYouTubeProAddon || hasAddon(user, Addon.YouTube);
      user.youtubeAddon = user.hasYouTubeProAddon
        ? findAddon(user.addons, Addon.YouTubePro)
        : user.hasYouTubeAddon
          ? findAddon(user.addons, Addon.YouTube)
          : {};

      setUserForCS(user);
    } catch (e) {}
    store.current = user;
  }),

  fetch: thunk((actions) =>
    fetchUser()
      .catch((err) => Promise.reject(err))
      .then((user) => {
        if (user) {
          actions.set(user);
        }
        return user;
      })
      .catch(() => import.meta.env.NODE_ENV === 'production' && actions.logout())
  ),
  update: thunk(async (actions, payload) =>
    API.put('users/me', payload).then(() => actions.fetch())
  ),
  onAuthorize: thunk((actions, { token, refresh_token }) => {
    cacheSet('token', token);
    if (refresh_token) {
      cacheSet('refresh_token', refresh_token);
    }
    return actions.fetch();
  }),
  login: thunk(async (actions, payload) =>
    API.post<{ access_token: string; refresh_token: string }>('auth/login', payload).then((res) => {
      if (res?.access_token) {
        actions.onAuthorize({ token: res.access_token, refresh_token: res.refresh_token });
      }
    })
  ),
  signUp: thunk((actions, payload) =>
    API.post('auth/signup', payload).then(() =>
      actions.login({ email: payload.email, password: payload.password })
    )
  ),
  logout: thunk((actions) => {
    logoutUser();
    actions.set(anonymousUser);
  }),
};

export default userStore;
