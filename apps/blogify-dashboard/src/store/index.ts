import { createStore, createTypedHooks } from 'easy-peasy';
import model, { StoreModel } from './store.model';

type CustomNodeModule = NodeModule & { hot: { accept: (_: () => void) => void } };
const typedHooks = createTypedHooks<StoreModel>();

export const { useStoreState } = typedHooks;
export const { useStoreActions } = typedHooks;
export const { useStoreDispatch } = typedHooks;

const Store = createStore<StoreModel>(model);

// https://easy-peasy.now.sh/docs/recipes/hot-reloading.html
if (import.meta.env.NODE_ENV === 'development') {
  if ((module as CustomNodeModule).hot) {
    (module as CustomNodeModule).hot.accept(() => {
      Store.reconfigure(model);
    });
  }
}

export default Store;
