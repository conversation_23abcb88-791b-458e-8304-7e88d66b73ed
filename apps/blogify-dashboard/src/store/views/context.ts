import type { Package } from '@/types/misc/package.type';

import { Action, action } from 'easy-peasy';

export type ContextStore = {
  packages: Package[];
  packageByName: Record<string, Package>;
  setPackages: Action<ContextStore, Package[]>;

  settings: Record<string, any>;
  setSettings: Action<ContextStore, Record<string, any>>;
};

export const defaultPackage: Package = {
  name: 'MONTHLY_BASIC',
  interval: 'month',
  currency: 'usd',
  price: 0,
  id: '',
};

const contextStore: ContextStore = {
  packages: [defaultPackage],
  packageByName: { MONTHLY_BASIC: defaultPackage },
  setPackages: action((store, packages) => {
    store.packages = packages;
    store.packageByName = packages.reduce(
      (pkgs, p) => ((pkgs[p.name] = p), pkgs),
      {} as Record<string, Package>
    );
  }),

  settings: {},
  setSettings: action((store, settings) => {
    store.settings = settings;
  }),
};

export default contextStore;
