import { FormikValues, useFormik } from 'formik';
import { ObjectSchema } from 'yup';
import { useRef } from 'react';

const useForm = <T>({
  initialValues,
  submit,
  validationSchema,
  validateOnBlur,
}: {
  initialValues: T;
  submit: (arg: FormikValues) => void;
  validationSchema?: ObjectSchema<FormikValues>;
  validateOnBlur?: boolean;
}) => {
  const {
    setFieldValue,
    validateField,
    handleChange,
    handleBlur,
    submitForm,
    setErrors,
    isValid,
    touched,
    errors,
    values,
  } = useFormik({
    initialValues: initialValues as FormikValues,
    onSubmit: submit,
    validationSchema,
    validateOnBlur,
  });
  const isSubmitRequested = useRef(false);

  const getInputFields = (field: keyof T) => {
    const _field = field as keyof FormikValues;
    return {
      name: field,
      value: values[_field],
      error: errors[_field] as string,
      touched: !!touched[_field],
      checked: !!values[_field],
      hasError: !!(errors[_field] && touched[_field]),
      onChange: handleChange(_field),
      onBlur: handleBlur(_field),
      autocomplete: field,
    };
  };

  // Show this error only on submit press
  const formValidationError =
    isSubmitRequested.current && Object.keys(errors).length !== 0
      ? 'Please correct the errors before saving.'
      : false;

  return {
    submitForm: (ev: React.FormEvent<HTMLFormElement>) => {
      if (ev) ev.preventDefault();
      isSubmitRequested.current = true;
      return submitForm();
    },
    getInputFields,
    setFieldValue,
    validateField,
    setErrors,
    isValid,
    values,
    formValidationError,
  };
};

export default useForm;
