import { ACTIONS, EVENTS, STATUS } from 'react-joyride';
import { useCallback, useEffect, useState } from 'react';

const useUserTour = ({ key }: { key: string }) => {
  const [run, setRun] = useState(true);
  useEffect(() => {
    // check if tour has been completed already
    if (typeof window === 'undefined') return;
    const isTourComplete = window.localStorage.getItem(key);
    if (isTourComplete) {
      setRun(false);
    }
  }, [key, run]);
  const [stepIndex, setStepIndex] = useState(0);
  const handleJoyrideCallback = useCallback(
    (data: any) => {
      const { action, index, status, type } = data;

      if ([EVENTS.STEP_AFTER, EVENTS.TARGET_NOT_FOUND].includes(type)) {
        // Update state to advance the tour
        setStepIndex(index + (action === ACTIONS.PREV ? -1 : 1));
      } else if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
        // Need to set our running state to false, so it's not re-mounted
        setRun(false);
        localStorage.setItem(key, 'true');
      }
    },
    [key]
  );
  return { run, stepIndex, handleJoyrideCallback };
};
export default useUserTour;
