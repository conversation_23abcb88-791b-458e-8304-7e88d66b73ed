import { useNavigate } from 'react-router-dom';

import { useStoreActions } from '@/store';
import { cacheRemove } from '@/utils/localStorageCache';
import { parseQuery } from '@/utils';
import anonymousUser from '@/types/resources/user.type';

const useAuth = () => {
  const setUser = useStoreActions((s) => s.user.set);
  const navigate = useNavigate();

  const logoutUser = () => {
    cacheRemove('token');
    cacheRemove('refresh_token');
    setUser(anonymousUser);
    window.CRISP_TOKEN_ID = null;
    window.$crisp?.push(['do', 'session:reset']);
    return navigate('/login');
  };

  const redirectAfterAuth = (_redirectTo?: string) => {
    const redirectTo =
      _redirectTo || decodeURIComponent(parseQuery().redirectTo || '') || '/dashboard';

    if (/javascript/.test(redirectTo.toLowerCase())) return; // |http|:

    return navigate(redirectTo, { replace: true });
  };

  return { redirectAfterAuth, logoutUser };
};

export default useAuth;
