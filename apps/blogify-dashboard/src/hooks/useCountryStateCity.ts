import type { Country, State, City } from 'react-country-state-city/dist/esm/types';

import { GetCountries, GetState, GetCity } from 'react-country-state-city';
import { useEffect, useState } from 'react';

export default function useCountryStateCity(deps?: [string, string]): {
  countries: Country[];
  states: State[];
  cities: City[];
} {
  const [selectedCountry, selectedState] = deps || [];
  const [countries, setCountries] = useState<Country[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [cities, setCities] = useState<City[]>([]);

  useEffect(() => {
    GetCountries().then((result) => {
      setCountries(result);
    });
  }, []);

  useEffect(() => {
    if (selectedCountry) {
      const countryId = countries.find((c) => c.iso3 === selectedCountry)?.id;
      if (countryId)
        GetState(countryId).then((result) => {
          setStates(result);
        });
    }
  }, [countries, selectedCountry]);

  useEffect(() => {
    if (selectedState) {
      const countryId = countries.find((c) => c.iso3 === selectedCountry)?.id;
      const stateId = states.find((s) => s.name === selectedState)?.id;
      if (countryId && stateId)
        GetCity(countryId, stateId).then((result) => {
          setCities(result);
        });
    }
  }, [selectedCountry, selectedState, countries, states]);

  return { countries, states, cities };
}
