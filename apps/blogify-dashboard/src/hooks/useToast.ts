import { createContext, useState } from 'react';

type ToastName = 'blogLoaderUnload';

export type ToastContextType = {
  isToastOpen: Record<ToastName, boolean>;
  toggleToast: (_: ToastName, __: boolean) => void;
};

const defaultValue = {
  isToastOpen: { blogLoaderUnload: false },
  toggleToast: () => {},
};

export const ToastContext = createContext<ToastContextType>(defaultValue);

export const useToastContext = (): ToastContextType => {
  const [isToastOpen, setToastOpen] = useState<Record<string, boolean>>(defaultValue.isToastOpen);

  const toggleToast = (key: string, value: boolean) =>
    setToastOpen({ ...isToastOpen, [key]: value });

  return { isToastOpen, toggleToast };
};
