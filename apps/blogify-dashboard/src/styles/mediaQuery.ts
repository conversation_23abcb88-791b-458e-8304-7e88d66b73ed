import { generateMedia } from 'styled-media-query';

const lg = '1280px';
const md = '1024px';
const mdMinus1 = '1023px';
const sm = '768px';
const smMinus1 = '767px';
const xs = '550px';
const xsMinus1 = '549px';

const customMedia = generateMedia({
  xsMinus1,
  xs,
  smMinus1,
  sm,
  mdMinus1,
  md,
  lg,
});

// Define media queries here
const mediaQuery = {
  xs: customMedia.lessThan('xsMinus1'),
  xs_above: customMedia.greaterThan('xs'),
  sm: customMedia.lessThan('smMinus1'),
  sm_above: customMedia.greaterThan('sm'),
  md: customMedia.lessThan('mdMinus1'),
  lg: customMedia.greaterThan('md'),
  hg: customMedia.greaterThan('lg'),
};

export default mediaQuery;
