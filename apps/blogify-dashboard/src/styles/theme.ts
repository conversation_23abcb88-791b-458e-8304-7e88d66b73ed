// TODO: Replace with Tailwind
// Breakpoints & Colors
export default {
  breakpoints: ['550px', '768px', '1024px', '1280px'],
  colors: {
    // Background Colors
    white: '#FFFFFF',
    bg: '#0E071D',
    bg2: '#F4F1F0',
    bg3: '#EEF6F7',
    bg4: '#FCEBEB',
    cta: '#111827',
    // Blacks
    black0: '#000000',
    black1: '#202337',
    black2: '#3C4057',
    black4: '#141414',
    black5: '#948AA8',
    // Grays
    gray1: '#48484C',
    gray2: '#7287AB',
    gray3: '#8F8F96',
    gray4: '#A7A7AE',
    gray5: '#D9E2E8',
    gray6: '#E0E7F3',
    gray7: '#F8FBFC',
    gray8: '#FAFAFC',
    gray9: '#93766C',
    gray10: '#E3DBD9',
    gray11: '#E9E4E2',
    gray12: '#A8918A',
    gray13: '#4C7B80',
    gray14: '#C0C0C0',
    gray15: '#ECECEC',
    gray16: '#FBEDF0',
    // Others
    prelude: '#C5B8E0',
    martinique: '#2F334B',
    cornflowerBlue: '#f8f7f6',
    blue: '#F2470D',
    blue2: '#144CB8',
    cyan: '#08CEF5',
    gold: '#F8B72D',
    green: '#41D490',
    success: '#66B700',
    olive: '#A4B259',
    olive1: '#48540E',
    olive2: '#BBCC66',
    grannyApple: '#D2F4E6',
    red: '#D40124',
    red2: '#D0021B',
    red3: '#D94C4C',
    red4: '#D40124',
    red5: '#cc0022',
    tangelo: '#FFB36D',
    // Third Party
    facebook: '#3B5998',
    linkedin: '#3077B7',
    weibo: '#E6162D',
    blogify: '#F2470D',
    wordpress: '#21759B',
    wordpressorg: '#23282D',
    mailchimp: '#FFE01B',
    blogger: '#FF5722',
    medium: '#000000',
    wix: '#F8BF72',
    zapier: '#FF4F00',
    instagram: '#E1306C',
    twitter: '#00ACEE',
    google: '#4285F4',
    googleRed: '#DB4437',
    youtube: '#FF0100',
    pinterest: '#E60023',
    tiktok: '#090909',
    vimeo: '#26B8F1',
    productHunt: '#FF6154',
    g2: '#FE492C',
    podcast: '#FF9800',
    spotify: '#444444',
    // brand colors
    brandColor2: '#FDE3DB',
  },
};
