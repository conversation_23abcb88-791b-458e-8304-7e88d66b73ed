/// <reference types="vite/client" />

type YoutubeVideo = import('./types/integrations/youtube.type').YoutubeVideo;

type Func = () => void;

interface Window {
  Stripe: any;
  Intercom: any;
  $crisp: any[];
  CRISP_WEBSITE_ID: string;
  CRISP_TOKEN_ID?: string | null;
  CRISP_RUNTIME_CONFIG: object;
  YT: any;
  ire: any;
  fbq: any;
  youtubeVideos: Map<string, YoutubeVideo>;
  dataLayer: any[];
  navigation: any;
  fpr: any;
  MSStream: any;
  opera: any;
}

declare module '@prerenderer/rollup-plugin';
declare module '@faker-js/faker';
declare module 'virtual:pwa-register' {
  import type { RegisterSWOptions } from 'vite-plugin-pwa/types';

  export type { RegisterSWOptions };
  export function registerSW(options?: RegisterSWOptions): (reloadPage?: boolean) => Promise<void>;
}
