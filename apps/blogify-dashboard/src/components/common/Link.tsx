import { NavLink, NavLinkProps } from 'react-router-dom';

const Link = ({
  to,
  children,
  ignoreExternal,
  noEncode,
  className,
  ...props
}: NavLinkProps & { noEncode?: boolean; ignoreExternal?: boolean }) => {
  const { origin, href } = window.location;
  const url = to && to.toString().replace(origin, '');
  const isExternal = (url && /:\/\/|\/test-api\/|\/api\//.test(url)) || undefined;

  return (
    <NavLink
      className={`${({ isActive }: { isActive: boolean }) => (isActive ? 'active' : '')} ${className}`}
      target={isExternal && !ignoreExternal ? '_blank' : undefined}
      rel={isExternal && 'nofollow noopener noreferrer'}
      state={{ referrer: href.replace(origin, '') }}
      to={noEncode ? url : encodeURI(url)}
      {...props}
    >
      {children}
    </NavLink>
  );
};

export default Link;
