import { FiAlertCircle } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';

import { Button } from '@ps/ui/components/button';

interface ErrorFallbackProps {
  error: Error | null;
  resetErrorBoundary?: () => void;
  message?: string;
}

export default function ErrorFallback({
  error,
  resetErrorBoundary,
  message = 'Something went wrong',
}: ErrorFallbackProps) {
  const navigate = useNavigate();

  const handleReset = () => {
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  const handleGoHome = () => {
    navigate('/dashboard');
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  return (
    <div className="flex h-screen justify-center bg-white">
      <div className="flex flex-col items-center justify-center gap-4 p-6">
        <div className="text-6xl text-red/50">
          <FiAlertCircle />
        </div>

        <h2 className="text-center text-xl font-bold text-primary">{message}</h2>

        {error?.message && (
          <div className="mb-4 max-w-lg rounded-lg border px-6 py-3 text-center text-red4">
            <p className="font-medium">Error: {error.message}</p>
          </div>
        )}

        <div className="flex gap-4">
          {resetErrorBoundary && (
            <Button variant="outline" onClick={handleReset}>
              Try Again
            </Button>
          )}

          <Button onClick={handleGoHome}>Go to Dashboard</Button>
        </div>
      </div>
    </div>
  );
}
