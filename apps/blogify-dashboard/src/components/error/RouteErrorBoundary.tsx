import { isRouteErrorResponse, useRouteError, useNavigate, Link } from 'react-router-dom';
import { FiAlertCircle } from 'react-icons/fi';

import { Button } from '@ps/ui/components/button';

export default function RouteErrorBoundary() {
  const navigate = useNavigate();
  const error = useRouteError();

  let errorMessage = 'An unexpected error occurred';
  let statusText = 'Error';
  let status = '';

  if (isRouteErrorResponse(error)) {
    errorMessage = error.data?.message || error.statusText;
    statusText = error.statusText;
    status = String(error.status);
  } else if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  }

  return (
    <div className="flex h-[calc(100vh-60px)] justify-center bg-white">
      <div className="flex flex-col items-center justify-center gap-4 p-6">
        <div className="text-6xl text-red/50">
          <FiAlertCircle />
        </div>

        {status && <h1 className="text-2xl font-bold text-primary">{status}</h1>}

        <h2 className="text-center text-xl font-bold text-primary">{statusText}</h2>

        <div className="mb-4 max-w-lg rounded-lg border px-6 py-3 text-center text-red4">
          <p>{errorMessage}</p>
        </div>

        <div className="flex gap-4">
          <Button variant="outline" onClick={() => navigate(-1)}>
            Go Back
          </Button>

          <Link to="/dashboard">
            <Button>Go to Dashboard</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
