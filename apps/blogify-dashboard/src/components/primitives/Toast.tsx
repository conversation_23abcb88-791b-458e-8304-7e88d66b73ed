import type { ToastProviderProps, ToastProps } from '@radix-ui/react-toast';

import * as RadixToast from '@radix-ui/react-toast';
import styled from 'styled-components';

import { Button } from '@ps/ui/components/button';
import { Theme } from '@/styles';
import Link from '@/components/common/Link';

const Toast = ({
  title,
  description,
  actionTitle = 'Close',
  actionLink,
  open,
  onOpenChange,
  ...props
}: {
  title: string;
  description?: string;
  actionTitle?: string;
  actionLink?: string;
  open: boolean;
  onOpenChange: ToastProps['onOpenChange'];
} & ToastProviderProps) => (
  <RadixToast.Provider duration={3000} {...props}>
    <ToastRoot open={open} onOpenChange={onOpenChange}>
      <div className="flex items-center justify-between">
        <div>
          <ToastTitle>{title}</ToastTitle>
          {description && <ToastDescription>{description}</ToastDescription>}
        </div>
        <RadixToast.Action asChild altText="Action">
          <Link to={actionLink || window.location.href}>
            {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
            <Button variant="secondary" className="sm">
              {actionTitle}
            </Button>
          </Link>
        </RadixToast.Action>
      </div>
    </ToastRoot>
    <ToastViewport />
  </RadixToast.Provider>
);

const ToastViewport = styled(RadixToast.Viewport)`
  padding: var(--toast-viewport-padding);
  z-index: **********;
  max-width: 100vw;
  list-style: none;
  position: fixed;
  outline: none;
  width: 420px;
  margin: 0;
  bottom: 0;
  left: 0;

  @keyframes slideIn {
    from {
      transform: translateX(calc(100% + var(--toast-viewport-padding)));
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes swipeOut {
    from {
      transform: translateX(var(--radix-toast-swipe-end-x));
    }
    to {
      transform: translateX(calc(100% + var(--toast-viewport-padding)));
    }
  }
`;

const ToastRoot = styled(RadixToast.Root)`
  /* box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px, hsl(206 22% 7% / 20%) 0px 10px 20px -15px; */
  box-shadow: 0 10px 32px 0 rgba(122, 142, 178, 0.5);
  background-color: white;
  border-radius: 3px;
  padding: 20px 16px;
  &[data-state='open'] {
    animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
  &[data-state='closed'] {
    animation: hide 100ms ease-in;
  }
  &[data-swipe='move'] {
    transform: translateX(var(--radix-toast-swipe-move-x));
  }
  &[data-swipe='cancel'] {
    transition: transform 200ms ease-out;
    transform: translateX(0);
  }
  &[data-swipe='end'] {
    animation: swipeOut 100ms ease-out;
  }

  @keyframes hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
`;

const ToastTitle = styled(RadixToast.Title)`
  color: ${Theme.colors.black1};
  font-weight: 600;
  font-size: 14px;
`;

const ToastDescription = styled(RadixToast.Description)`
  color: ${Theme.colors.gray2};
  font-size: 12px;
  margin-top: 6px;
`;

export default Toast;
