import { Link } from 'react-router-dom';

import { Card } from '@/components/layout';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const NotFoundContainer = ({ children }: { children: React.ReactNode }) => (
  <div className="flex h-[486px] min-h-[58.6vh] justify-center bg-bg2 p-4 pt-[140px]">
    <div className="w-full md:w-1/2">{children}</div>
  </div>
);

const NotFoundCard = () => (
  <Card className="p-5">
    <div className="text-left">
      <img className="object-contain" src="/images/icons/sad.svg" height={64} />
    </div>

    <div className="mt-2 text-2xl font-bold">Not Found</div>

    <div className="mt-4 text-sm">
      Unfortunately the page you are looking for has been moved or deleted.
      <br />
      Go back to{' '}
      <Link to="/" className="underline hover:text-primary">
        Home
      </Link>
      .
    </div>
  </Card>
);

const NotFound = () => (
  <>
    <Header />
    <NotFoundContainer>
      <NotFoundCard />
    </NotFoundContainer>
    <Footer />
  </>
);

export { NotFoundCard };
export default NotFound;
