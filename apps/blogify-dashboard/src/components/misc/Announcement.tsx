import type { Dispatch } from 'react';

import React, { useEffect, useState } from 'react';
import { IoMdCloseCircleOutline } from 'react-icons/io';

import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';

const Announcement = () => {
  const settings = useStoreState((s) => s.context.settings);
  const [show, setShow] = useState(true);

  const announcements = settings.ANNOUNCEMENT as string[];
  const announcementEnd: string = settings.ANNOUNCEMENT_END;

  if (
    !announcements?.length ||
    !settings.ANNOUNCEMENT_VISIBLE ||
    new Date() > new Date(announcementEnd)
  ) {
    return null;
  }

  return show ? (
    <div className="container relative py-3">
      <div className="text-sm font-semibold uppercase text-white">
        <div className="flex flex-col flex-center lg:flex-row lg:gap-5">
          {announcements.map((s: string) => (
            <React.Fragment key={s}>
              <p>{s}</p>
              <p className="text-sm">✦</p>
            </React.Fragment>
          ))}
          {announcementEnd && (
            <p>
              Offer ENDS - <CountdownTimer endDate={announcementEnd} setShow={setShow} />
            </p>
          )}

          <Button
            className="right-5 min-w-10 lg:absolute"
            variant="icon"
            onClick={() => setShow(false)}
          >
            <IoMdCloseCircleOutline />
          </Button>
        </div>
      </div>
    </div>
  ) : null;
};

const CountdownTimer = ({ endDate, setShow }: { endDate: string; setShow: Dispatch<boolean> }) => {
  const calculateTimeLeft = () => {
    const difference = Number(new Date(endDate)) - Number(new Date());

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return {
        days: days,
        hours: hours,
        minutes: minutes,
        seconds: seconds,
      };
    } else {
      setShow(false);

      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      };
    }
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeLeft, endDate]);

  return (
    <span>
      {timeLeft.days}D : {timeLeft.hours}H : {timeLeft.minutes}M : {timeLeft.seconds}S
    </span>
  );
};

export default Announcement;
