import { Progress as ProgressBar } from '@ps/ui/components/progress';

type PropsType = {
  progress: number;
  title: string;
  subtitle: string;
};

export default function Progress({ progress, title, subtitle }: PropsType) {
  return (
    <div>
      <h3 className="text-17 font-semibold">{title}</h3>
      <p className="text-15">{subtitle}</p>
      <div className="mb-2 mt-4 flex items-center justify-between">
        <span className="text-15 font-semibold">{Math.floor(progress)}%</span>
        {/* <span className="text-gray9 font-semibold text-11">6/17</span> */}
      </div>

      <ProgressBar value={progress} key={progress} />
    </div>
  );
}
