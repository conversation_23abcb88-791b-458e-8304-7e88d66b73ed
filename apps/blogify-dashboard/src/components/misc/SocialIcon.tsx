import type { Integration } from '@/types/misc/integration.type';

import Theme from '@/styles/theme';
import Link from '@/components/common/Link';

const SocialIcon = ({
  to,
  integration,
  className,
}: {
  to?: string;
  integration: Integration;
} & React.HTMLProps<HTMLDivElement>) => {
  const Icon = (
    <div
      className={`flex size-6 rounded p-1.5 flex-center ${className}`}
      style={{ backgroundColor: Theme.colors[integration] }}
    >
      <img
        className="size-full"
        src={`/images/third-party-logo/${integration}-white.png`}
        alt={integration}
      />
    </div>
  );

  return to ? (
    <Link to={to} aria-label={`Go to ${integration}`}>
      {Icon}
    </Link>
  ) : (
    Icon
  );
};

export default SocialIcon;
