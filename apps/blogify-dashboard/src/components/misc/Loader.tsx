import Spinner from './Spinner';

const Loader = ({
  status = 'loading',
  size = 200,
  className,
}: {
  status?: 'loading' | 'success' | 'error';
  size?: number;
  className?: string;
}) => (
  <div
    className={`flex size-[200px] rounded-full flex-center ${className}`}
    style={{ background: `url('/images/loader-bg.svg') no-repeat center`, backgroundSize: size }}
  >
    {status === 'loading' ? (
      <div className="flex size-12 rounded-full bg-primary text-white flex-center" role="spinner">
        <Spinner className="size-[28px]" />
      </div>
    ) : (
      <img className="size-12" src={`/images/icons/icon-${status}.svg`} />
    )}
  </div>
);

export default Loader;
