import { useEffect, useState } from 'react';
import { FaMinus, FaPlus } from 'react-icons/fa';

import { isTouch } from '@/utils';
import { cn } from '@ps/ui/lib/utils';

type Props = {
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: number;
  onChange?: (_: number) => void;
  placeholder?: string;
  className?: string;
  text?: string;
};
const Counter: React.FC<Props> = ({
  min = Number.NEGATIVE_INFINITY,
  max = Number.POSITIVE_INFINITY,
  defaultValue,
  step = 1,
  onChange,
  placeholder,
  className,
  text,
}) => {
  const [operator, setOperator] = useState<'+' | '-' | ''>('');
  const [count, setCount] = useState<number>(defaultValue || 0);

  const increment = () => setOperator('+');
  const decrement = () => setOperator('-');
  const onLeave = () => setOperator('');

  const updateValue = (change: number, reset: boolean = false) => {
    setCount((c) => {
      let newCount = reset ? change : c + change;
      newCount = newCount < min ? min : newCount;
      newCount = newCount > max ? max : newCount;
      return newCount;
    });
  };

  useEffect(() => {
    if (operator) {
      const change = operator === '+' ? step : -step;
      updateValue(change);
      const interval = setInterval(() => updateValue(change), 150);

      return () => clearInterval(interval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [operator]);

  useEffect(() => {
    if (onChange) onChange(count);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [count]);

  useEffect(() => {
    if (defaultValue) setCount(defaultValue);
  }, [defaultValue]);

  return (
    <div
      className={cn(
        'flex w-full items-center justify-between gap-4 rounded-lg border border-gray10 p-1.5',
        className
      )}
    >
      <button
        className="flex size-7 rounded border border-gray10 text-gray12 shadow-xs flex-center focus:outline-none"
        disabled={!!min && count - 1 < min}
        aria-label="Decrement"
        type="button"
        {...(isTouch
          ? { onTouchStart: decrement, onTouchEnd: onLeave }
          : { onMouseDown: decrement, onMouseUp: onLeave })}
      >
        <FaMinus size={10} />
      </button>

      {text ? (
        <span className="text-15 font-medium">
          {count} {text}
        </span>
      ) : (
        <input
          type="number"
          // eslint-disable-next-line tailwindcss/no-custom-classname
          className="no-spin-button w-14 text-center text-15"
          onChange={(ev) => updateValue(parseInt(ev.target.value, 10) || min || 0, true)}
          value={count ? count : ''}
          placeholder={placeholder}
          step={step}
          min={min}
          max={max}
        />
      )}

      <button
        className="flex size-7 rounded border border-gray10 text-gray12 shadow-xs flex-center focus:outline-none"
        disabled={!!max && count + 1 > max}
        aria-label="Increment"
        type="button"
        {...(isTouch
          ? { onTouchStart: increment, onTouchEnd: onLeave }
          : { onMouseDown: increment, onMouseUp: onLeave })}
      >
        <FaPlus size={10} />
      </button>
    </div>
  );
};

export default Counter;
