import { useEffect, useState } from 'react';

import { cn } from '@ps/ui/lib/utils';

interface FadeMessageProps {
  message: string;
  className?: string;
  fadeDuration?: number; // in ms, default 500
}

export default function FadeMessage({
  message,
  className = '',
  fadeDuration = 500,
}: FadeMessageProps) {
  const [displayedMessage, setDisplayedMessage] = useState(message);
  const [opacityClass, setOpacityClass] = useState('opacity-100');

  useEffect(() => {
    if (message === displayedMessage) return;

    setOpacityClass('opacity-0');

    const timeout = setTimeout(() => {
      setDisplayedMessage(message);
      setOpacityClass('opacity-100');
    }, fadeDuration);

    return () => clearTimeout(timeout);
  }, [message, displayedMessage, fadeDuration]);

  return (
    <div
      className={cn('transition-opacity ease-in-out', opacityClass, className)}
      style={{ transitionDuration: `${fadeDuration}ms` }}
    >
      {displayedMessage}
    </div>
  );
}
