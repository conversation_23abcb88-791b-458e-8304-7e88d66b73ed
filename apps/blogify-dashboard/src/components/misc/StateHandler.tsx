import { FiAlertCircle } from 'react-icons/fi';
import { TbMoodEmpty } from 'react-icons/tb';

import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import Spinner from '@/components/misc/Spinner';
import Link from '@/components/common/Link';

interface StateHandlerProps {
  loading?: boolean;
  error?: string | null;
  isEmpty?: boolean;
  emptyMsg?: string;
  actionLabel?: string;
  actionUrl?: string;
  className?: string;
  icon?: React.ComponentType;
}

const StateHandler = ({
  loading = false,
  error = null,
  isEmpty = false,
  emptyMsg = 'No data available',
  actionLabel = 'Create New',
  actionUrl,
  className = '',
  icon: Icon = TbMoodEmpty,
}: StateHandlerProps) => {
  if (loading) {
    return (
      <div className={`flex min-h-[65vh] flex-col gap-4 p-5 flex-center ${className}`}>
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex min-h-[65vh] flex-col gap-4 p-5 flex-center ${className}`}>
        <div className="text-6xl text-red/50">
          <FiAlertCircle />
        </div>
        <div className="text-center text-lg text-red/60">{error}</div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className={cn(`flex min-h-[65vh] flex-col gap-4 p-5 flex-center`, className)}>
        <div className="text-6xl text-gray-500">
          <Icon />
        </div>
        <div className="text-center text-lg">{emptyMsg}</div>
        {actionUrl && (
          <Link to={actionUrl}>
            <Button>{actionLabel}</Button>
          </Link>
        )}
      </div>
    );
  }

  return null;
};

export default StateHandler;
