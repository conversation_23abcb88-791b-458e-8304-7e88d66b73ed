import type { VariantProps } from 'class-variance-authority';
import type { Integration } from '@/types/misc/integration.type';

import { cva } from 'class-variance-authority';

import { Theme } from '@/styles';
import { cn } from '@ps/ui/lib/utils';

const containerVariants = cva('flex flex-center', {
  variants: {
    size: {
      6: 'size-6 rounded-md',
    },
  },
  defaultVariants: {
    size: 6,
  },
});

const imgVariants = cva('', {
  variants: {
    size: {
      6: 'size-3.5',
    },
  },
  defaultVariants: {
    size: 6,
  },
});

interface IntegrationLogoProps extends VariantProps<typeof containerVariants> {
  integration: Integration;
  className?: string;
}

const IntegrationLogo: React.FC<IntegrationLogoProps> = ({ integration, size, className }) => (
  <div
    className={cn(containerVariants({ size }), className)}
    style={{ backgroundColor: Theme.colors[integration] }}
  >
    <img
      className={cn(imgVariants({ size }))}
      src={`/images/icons/integrations/${integration}.svg`}
    />
  </div>
);

export default IntegrationLogo;
