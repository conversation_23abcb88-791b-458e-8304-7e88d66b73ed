import { Fragment } from 'react';

export type Stats = {
  name: string;
  amount: number;
  type?: 'currency';
};
export default function StatsBox({ stats }: { stats: Stats[] }) {
  return (
    <section className="my-6 grid grid-cols-2 gap-4 sm:flex sm:gap-6 md:gap-7">
      {stats.map(({ name, amount, type }, i) => (
        <Fragment key={i}>
          {i !== 0 && <div className="hidden h-6 border-r border-gray10 sm:block" />}

          <div className="text-center sm:text-left">
            <div className="text-sm font-medium capitalize sm:text-13">{name}</div>
            <div className="mt-0.5 text-lg font-semibold sm:text-21">
              {type === 'currency' ? '$' : ''}
              {amount || 0}
            </div>
          </div>
        </Fragment>
      ))}
    </section>
  );
}
