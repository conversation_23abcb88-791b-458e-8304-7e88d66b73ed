import { Helmet } from 'react-helmet-async';

export default function Seo({
  title,
  description,
  image,
  imageWidth,
  imageHeight,
  keywords,
  children,
}: {
  title: string;
  image?: string;
  imageWidth?: number;
  imageHeight?: number;
  description?: string;
  keywords?: string;
  children?: any;
}) {
  const url = window.location.href.replace(window.location.origin, 'https://blogify.ai');

  return (
    <Helmet
      title={title}
      defaultTitle={title}
      // titleTemplate={title.includes('Blogify AI') ? '' : `%s - Blogify AI`}
      htmlAttributes={{ lang: 'en' }}
      onChangeClientState={() => document.dispatchEvent(new Event('app-ready'))}
    >
      <meta name="title" content={title} />
      <meta property="og:title" content={title} />
      {description && <meta name="description" content={description} />}
      {description && <meta property="og:description" content={description} />}
      {image && <meta property="og:image" content={image} />}
      {image && <meta property="og:image:alt" content={title} />}
      {imageWidth && <meta property="og:image:width" content={String(imageWidth)} />}
      {imageHeight && <meta property="og:image:height" content={String(imageHeight)} />}
      {keywords && <meta name="keywords" content={keywords} />}
      <meta property="og:url" content={url} />
      <link rel="canonical" href={url} />
      {children}
    </Helmet>
  );
}
