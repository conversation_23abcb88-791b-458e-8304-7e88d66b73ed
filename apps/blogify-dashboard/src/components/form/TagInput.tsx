import { WithContext as ReactTags } from 'react-tag-input';
import { MdClose } from 'react-icons/md';
import styled from 'styled-components';

import Theme from '@/styles/theme';

export interface Tag {
  id: string;
  className: string;
  [key: string]: string;
}

const KeyCodes = {
  comma: 188,
  enter: 13,
};

const delimiters = [KeyCodes.comma, KeyCodes.enter];

const TagInput = ({
  tags,
  setTags,
  placeholder = '',
  suggestions,
  suggestionsOnly,
  setError,
  className,
  ...props
}: React.HTMLProps<HTMLDivElement> & {
  tags: string[];
  setTags: React.Dispatch<string[]>;
  placeholder?: string;
  suggestions?: string[];
  suggestionsOnly?: boolean;
  setError?: (e: string) => void;
}) => {
  const handleDelete = (i: number) => {
    setTags(tags.filter((_, index) => index !== i));
  };

  const handleAddition = (tag: Tag) => {
    if (setError) setError('');
    if (tags.find((t) => t === tag.text) && setError) {
      return setError('Duplicate name is not allowed.');
    }
    if (!suggestionsOnly || (suggestionsOnly && suggestions?.find((s) => s === tag.text))) {
      setTags([...tags, tag.text]);
    }
  };

  const handleDrag = (tag: Tag, currPos: number, newPos: number) => {
    const newTags = tags.slice();

    newTags.splice(currPos, 1);
    newTags.splice(newPos, 0, tag.text);

    setTags(newTags);
  };

  return (
    <div className={className} {...props}>
      <StyledReactTags>
        <ReactTags
          handleAddition={handleAddition}
          handleDelete={handleDelete}
          handleDrag={handleDrag}
          placeholder={placeholder}
          suggestions={suggestions?.map((s) => ({ id: s, text: s, className: '' }))}
          delimiters={delimiters}
          tags={tags.map((t) => ({ id: t, text: t, className: '' }))}
          inputFieldPosition="inline"
          allowUnique={false}
          autofocus={false}
          autocomplete
          removeComponent={({ index }: { index: number }) => (
            <MdClose
              className="ml-1 cursor-pointer text-primary"
              onClick={() => handleDelete(index)}
              size={14}
            />
          )}
        />
      </StyledReactTags>
    </div>
  );
};

const StyledReactTags = styled.div`
  border: 1px solid ${Theme.colors.gray10};
  background-color: ${Theme.colors.white};
  color: ${Theme.colors.black4};
  padding: 5px 16px 0px;
  border-radius: 8px;
  min-height: 38px;
  font-size: 14px;
  width: 100%;
  .ReactTags__selected {
    flex-wrap: wrap;
    display: flex;
  }
  .ReactTags__tag {
    border: 1px solid ${Theme.colors.blue};
    color: ${Theme.colors.blue};
    align-items: center;
    border-radius: 3px;
    margin-bottom: 5px;
    padding-right: 8px;
    padding-left: 8px;
    margin-right: 8px;
    font-size: 12px;
    display: flex;
    height: 24px;
  }
  .ReactTags__tagInputField {
    background: transparent;
    min-width: 220px;
    padding: 0px;
    height: 24px;
    ::placeholder {
      color: ${Theme.colors.gray14};
      font-size: 12px;
    }
  }
  .ReactTags__suggestions {
    box-shadow: 0 2px 3px 0 rgba(122, 142, 178, 0.2);
    background: ${Theme.colors.white};
    position: absolute;
    border-radius: 3;
    font-size: 12px;
    ul {
      list-style: none;
      padding: 0px;
      li {
        padding: 6px 12px;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .ReactTags__activeSuggestion {
    background: ${Theme.colors.bg2};
  }
`;

export default TagInput;
