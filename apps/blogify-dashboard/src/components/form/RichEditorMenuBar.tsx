import type { ImageSelectionTabs } from '@/views/dashboard/image/context/ImageDialogProvider';
import type { Editor } from '@tiptap/react';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useToggle } from 'react-use';
import { FaLink } from 'react-icons/fa6';

import { Button } from '@ps/ui/components/button';
import { isUrl } from '@/utils';
import { cn } from '@ps/ui/lib/utils';
import FontSizeSelector from '@/components/rich-editor/components/FontSizeSelector';
import HeadingSelector from '@/components/rich-editor/components/HeadingSelector';
import useImageDialog from '@/views/dashboard/image/context/useImageDialog';
import FormField from '@ps/ui/form/FormField';
import SvgIcon from '@ps/ui/components/svg-icon';
import Dialog from '@ps/ui/components/dialog';
import Theme from '@/styles/theme';

import {
  TextAlignJustifyIcon,
  TextAlignCenterIcon,
  TextAlignRightIcon,
  StrikethroughIcon,
  TextAlignLeftIcon,
  UnorderedListIcon,
  CodeSnippetIcon,
  OrderedListIcon,
  GoBackwardIcon,
  AiContentIcon,
  GoForwardIcon,
  UnderlineIcon,
  AiImageIcon,
  ItalicIcon,
  SearchIcon,
  QuotesIcon,
  ImageIcon,
  BoldIcon,
  LinkIcon,
  CtaIcon,
} from '../rich-editor/icons';
import SearchAndReplace from '../rich-editor/components/SearchAndReplace';
import TextHtmlToggle from '../rich-editor/components/TextHtmlToggle';
import CtaDialog from '../rich-editor/CtaDialog';

type ActionType = 'toggle' | 'set';
type ButtonType =
  | 'Undo'
  | 'Redo'
  | 'Search'
  | 'Bold'
  | 'Italic'
  | 'Underline'
  | 'Strike'
  | 'Code'
  | 'Link'
  | 'BulletList'
  | 'OrderedList'
  | 'Blockquote'
  | 'TextAlign'
  | 'Cta'
  | 'Image'
  | 'HTML'
  // | 'YoutubeLink'
  | 'Divider'
  | 'AiContent'
  | 'AiImage'
  | 'AiLink';

type MenuItemType = {
  title: string;
  icon?: React.ReactNode;
  name: ButtonType;
  action?: ActionType;
  params?: 'left' | 'center' | 'right' | 'justify' | { level: number } | any;
};

const MENU: MenuItemType[] = [
  { icon: <SvgIcon icon={SearchIcon} />, name: 'Search', title: 'Search' },
  { icon: <SvgIcon icon={GoBackwardIcon} />, name: 'Undo', title: 'Undo' },
  {
    icon: <SvgIcon icon={GoForwardIcon} />,
    name: 'Redo',
    action: 'set',
    params: 'left',
    title: 'Redo',
  },
  { name: 'Divider', title: 'Divider' },
  { icon: <SvgIcon icon={BoldIcon} />, name: 'Bold', title: 'Bold' },
  { icon: <SvgIcon icon={ItalicIcon} />, name: 'Italic', title: 'Italic' },
  { icon: <SvgIcon icon={UnderlineIcon} />, name: 'Underline', title: 'Underline' },
  { icon: <SvgIcon icon={StrikethroughIcon} />, name: 'Strike', title: 'Strikethrough' },
  { name: 'Divider', title: 'Divider' },
  {
    icon: <SvgIcon icon={TextAlignLeftIcon} />,
    name: 'TextAlign',
    action: 'set',
    params: 'left',
    title: 'Align Left',
  },
  {
    icon: <SvgIcon icon={TextAlignCenterIcon} />,
    name: 'TextAlign',
    action: 'set',
    params: 'center',
    title: 'Align Center',
  },
  {
    icon: <SvgIcon icon={TextAlignRightIcon} />,
    name: 'TextAlign',
    action: 'set',
    params: 'right',
    title: 'Align Right',
  },
  {
    icon: <SvgIcon icon={TextAlignJustifyIcon} />,
    name: 'TextAlign',
    action: 'set',
    params: 'justify',
    title: 'Justify',
  },
  { name: 'Divider', title: 'Divider' },
  { icon: <SvgIcon icon={UnorderedListIcon} />, name: 'BulletList', title: 'Bullet List' },
  { icon: <SvgIcon icon={OrderedListIcon} />, name: 'OrderedList', title: 'Ordered List' },
  { name: 'Divider', title: 'Divider' },
  { icon: <SvgIcon icon={CodeSnippetIcon} />, name: 'Code', title: 'Code' },
  { icon: <SvgIcon icon={QuotesIcon} />, name: 'Blockquote', title: 'Blockquote' },
  { icon: <SvgIcon icon={LinkIcon} />, name: 'Link', title: 'Link' },
  { icon: <SvgIcon icon={CtaIcon} />, name: 'Cta', title: 'CTA' },
  { icon: <SvgIcon icon={ImageIcon} />, name: 'Image', title: 'Image' },
  { name: 'Divider', title: 'Divider' },
  // { icon: <SvgIcon icon={AiContentIcon} />, name: 'AiContent', title: 'AI Content' },
  { icon: <SvgIcon icon={AiImageIcon} />, name: 'AiImage', title: 'AI Image' },
  // { icon: <SvgIcon icon={AiLinkIcon} />, name: 'AiLink', title: 'AI Link' },
  // { icon: <AiOutlineYoutube />, name: 'YoutubeLink', title: 'YouTube Link' },
];

const toCamelCase = (s: string) => s.charAt(0).toLowerCase() + s.substring(1);

const RichEditorMenuBar = ({
  editor,
  isHtml,
  toggleHtml,
  setSelectedImgTab,
}: {
  editor: Editor;
  isHtml: boolean;
  toggleHtml: () => void;
  setSelectedImgTab: (tab: ImageSelectionTabs) => void;
}) => {
  const [isCtaDialogOpen, toggleCtaDialog] = useToggle(false);
  const isDisabled = (action: ActionType, button: ButtonType, params?: string) =>
    // @ts-ignore handled;
    !editor.can().chain().focus()[`${action}${button}`](params).run();

  const toggle = (action: ActionType, button: ButtonType, params?: string) =>
    // @ts-ignore handled;
    editor.chain().focus()[`${action}${button}`](params).run();

  const isActive = (name: ButtonType, params?: string) => {
    const isParamsObject = !!(params && Object.keys(params).length);
    return editor.isActive(
      (isParamsObject ? { [toCamelCase(name)]: params } : toCamelCase(name)) as string,
      isParamsObject ? params : {}
    );
  };

  const [displaySearchTools, toggleDisplaySearchTools] = useToggle(false);

  const undo = () => editor.chain().focus().undo().run();
  const redo = () => editor.chain().focus().redo().run();

  return (
    <>
      <div className="sticky top-[60px] z-10 space-y-1 border-2 border-[#e3dbd9] bg-bg2 p-2 font-ibx lg:px-4">
        <div className="flex flex-wrap items-center justify-between gap-2 font-semibold">
          <div className="flex items-center">
            <HeadingSelector editor={editor} />
            <div className="mx-2 h-3 w-px bg-[#e3dbd9] lg:mx-3" />
            <FontSizeSelector editor={editor} />
          </div>

          <TextHtmlToggle isHtml={isHtml} toggleHtml={toggleHtml} />
        </div>

        <div className="flex min-h-10 flex-wrap items-center gap-1.5 pt-2 md:pt-0">
          {MENU.map(({ title, name, action, params, icon }, i) => (
            <React.Fragment key={i}>
              {name === 'Divider' ? (
                <div className="mx-2 h-3 w-px bg-[#e3dbd9] lg:mx-3" />
              ) : name === 'Search' ? (
                <>
                  <button
                    className={cn('flex size-5 rounded text-base flex-center hover:bg-gray10', {
                      'bg-white text-primary ': displaySearchTools,
                    })}
                    onClick={() => toggleDisplaySearchTools()}
                    type="button"
                    title={title}
                  >
                    <SvgIcon icon={SearchIcon} />
                  </button>
                </>
              ) : name === 'Image' ? (
                <RichMenuImageButton
                  icon={icon}
                  editor={editor}
                  clickCallback={() => setSelectedImgTab('')}
                  title={title}
                />
              ) : name === 'AiImage' ? (
                <RichMenuImageButton
                  editor={editor}
                  icon={icon}
                  clickCallback={() => setSelectedImgTab('ai-generated')}
                  title={title}
                />
              ) : name === 'Link' ? (
                <RichMenuLinkButton editor={editor} title={title} />
              ) : name === 'HTML' ? (
                <RichMenuHtmlButton isHtml={isHtml} toggleHtml={toggleHtml} />
              ) : name === 'AiContent' ? (
                <button
                  className={cn('flex size-5 rounded text-base flex-center  hover:bg-gray10')}
                  type="button"
                  title={title}
                >
                  {icon}
                </button>
              ) : name === 'AiLink' ? (
                <button
                  className={cn('flex size-5 rounded text-base flex-center  hover:bg-gray10', {
                    '!bg-white !text-primary': isActive(name, params),
                  })}
                  type="button"
                  title={title}
                >
                  {icon}
                </button>
              ) : name === 'Undo' ? (
                <button
                  className="flex size-5 rounded text-base flex-center  hover:bg-gray10"
                  onClick={undo}
                  type="button"
                  title={title}
                >
                  <SvgIcon icon={GoBackwardIcon} />
                </button>
              ) : name === 'Redo' ? (
                <button
                  className={cn('flex size-5 rounded text-base flex-center ', {
                    'text-gray12': !editor.can().redo(),
                    'hover:bg-gray10': editor.can().redo(),
                  })}
                  onClick={redo}
                  type="button"
                  title={title}
                  disabled={!editor.can().redo()}
                >
                  <SvgIcon icon={GoForwardIcon} />
                </button>
              ) : name === 'Cta' ? (
                <button
                  type="button"
                  title={title}
                  className={cn('flex size-5 rounded text-base flex-center hover:bg-gray10', {
                    'bg-white text-primary': isCtaDialogOpen,
                  })}
                  // disabled={!editor.can().chain().focus().setImage({ src: '' }).run()}
                  onClick={() => {
                    toggleCtaDialog(true);
                    // clickCallback();
                  }}
                >
                  <SvgIcon icon={CtaIcon} />
                </button>
              ) : (
                <button
                  className={cn('flex size-5 rounded text-base flex-center hover:bg-gray10', {
                    '!bg-white !text-primary': isActive(name, params),
                  })}
                  onClick={() => toggle(action || 'toggle', name, params)}
                  disabled={isDisabled(action || 'toggle', name, params)}
                  type="button"
                  title={title}
                >
                  {icon}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>
        <div>{displaySearchTools && <SearchAndReplace editor={editor} />}</div>
      </div>

      {isCtaDialogOpen &&
        createPortal(
          <CtaDialog
            isOpen={isCtaDialogOpen}
            onClose={() => toggleCtaDialog(false)}
            editor={editor}
          />,
          document.body
        )}
    </>
  );
};

const RichMenuImageButton = ({
  editor,
  icon,
  clickCallback,
  title,
}: {
  editor: Editor;
  icon: React.ReactNode;
  clickCallback: () => void;
  title: string;
}) => {
  const { isOpen, toggleDialog } = useImageDialog();

  return (
    <button
      className={cn('flex size-5 rounded text-base flex-center hover:bg-gray10', {
        'bg-white text-primary': isOpen,
      })}
      disabled={!editor.can().chain().focus().setImage({ src: '' }).run()}
      onClick={() => {
        toggleDialog(true);
        clickCallback();
      }}
      type="button"
      title={title}
    >
      {icon}
    </button>
  );
};

const RichMenuLinkButton = ({ editor, title }: { editor: Editor; title: string }) => {
  const [isDialogOpen, toggleDialog] = useToggle(false);
  const [error, setError] = useState('');
  const [url, setUrl] = useState('');

  const getColor = () => (editor.isActive('link') ? Theme.colors.blue : Theme.colors.black2);

  const removeLink = () => {
    editor.chain().focus().extendMarkRange('link').unsetLink().run();
    toggleDialog();
  };

  const attachLink = () => {
    if (url) {
      if (isUrl(url)) {
        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
        toggleDialog();
      } else {
        setError('Invalid Link!');
      }
    }
  };

  useEffect(() => {
    if (isDialogOpen) {
      setUrl(editor.getAttributes('link').href);
    }

    return () => {
      setError('');
      setUrl('');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  return (
    <>
      <button
        className="flex size-5 text-base flex-center hover:bg-gray10"
        onClick={toggleDialog}
        color={getColor()}
        type="button"
        title={title}
      >
        <SvgIcon icon={LinkIcon} />
      </button>

      {isDialogOpen && (
        <Dialog
          open={isDialogOpen}
          onOpenChange={toggleDialog}
          Icon={FaLink}
          title="Link"
          description="Add, update or remove a link."
          actions={
            <>
              <Button onClick={attachLink}>Attach Link</Button>

              {editor.isActive('link') && (
                <Button onClick={removeLink} variant="secondary">
                  Remove Link
                </Button>
              )}
            </>
          }
          error={error}
        >
          <FormField
            type="url"
            value={url}
            label="URL"
            onChange={(ev) => setUrl(ev.target.value)}
            placeholder="Enter URL"
          />
        </Dialog>
      )}
    </>
  );
};

const RichMenuHtmlButton = ({
  isHtml,
  toggleHtml,
}: {
  isHtml: boolean;
  toggleHtml: () => void;
}) => (
  <>
    <button
      color={isHtml ? Theme.colors.blue : Theme.colors.black2}
      className={cn('flex h-5 w-8 px-1 text-base flex-center', {
        'rounded !bg-white !text-primary': isHtml,
      })}
      onClick={toggleHtml}
      type="button"
    >
      <SvgIcon icon={AiContentIcon} />
    </button>
  </>
);

export default RichEditorMenuBar;
