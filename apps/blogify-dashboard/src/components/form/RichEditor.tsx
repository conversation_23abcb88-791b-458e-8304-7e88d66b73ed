import type { ImageSelectionTabs } from '@/views/dashboard/image/context/ImageDialogProvider';
import type { Blog } from '@ps/types';

import { B<PERSON><PERSON><PERSON>enu, EditorContent, useEditor } from '@tiptap/react';
import { Ref, useEffect, useImperativeHandle, useState } from 'react';
import { MdDelete, MdOutlineAddLink } from 'react-icons/md';
import { useToggle } from 'react-use';
import { FaExpandArrowsAlt, FaImage, FaLink } from 'react-icons/fa';
import { format } from 'prettier/standalone';
import { html } from '@codemirror/lang-html';
import TableHeader from '@tiptap/extension-table-header';
import CodeMirror from '@uiw/react-codemirror';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import TableCell from '@tiptap/extension-table-cell';
import TextStyle from '@tiptap/extension-text-style';
import TableRow from '@tiptap/extension-table-row';
import styled from 'styled-components';
import Table from '@tiptap/extension-table';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import * as htmlParser from 'prettier/parser-html';

import { ImageDialogProvider } from '@/views/dashboard/image/context/ImageDialogProvider';
import { EditorAccessContext } from '@/context/EditorAccessContext';
import { mediaQuery } from '@/styles';
import { Button } from '@ps/ui/components/button';
import ImageInsertDialog from '@/views/dashboard/image/ImageInsertDialog';
import Theme from '@/styles/theme';

import { FontSizeExtension } from '../rich-editor/fontSizeExtension';
import { SearchAndReplace } from '../rich-editor/extensions/tiptap-search-and-replace';
import AffiliateLinkInserterDialog from '../rich-editor/AffiliateLinkInserterDialog';
import { BlogExtendDialog, BlogRephraseDialog } from '../rich-editor/BlogRewriteDialog';
import RichEditorMenuBar from './RichEditorMenuBar';
import { CTAExtension } from '../rich-editor/CtaButtonExtension';
import CtaDialog from '../rich-editor/CtaDialog';
import { AiFillEdit } from 'react-icons/ai';
import { ImageWithLinkExtension } from '../rich-editor/ImageWithLinkExtention';
import { cleanHtmlStructure } from '../rich-editor/cleanHtmlStructure';
import { Iframe } from '../rich-editor/IframeExtension';

const parseHtml = (htmlString?: string): Promise<string> => {
  // clean the HTML to prevent invalid nesting
  const cleanedHtml = cleanHtmlStructure(String(htmlString || ''));

  return format(cleanedHtml, {
    htmlWhitespaceSensitivity: 'ignore',
    plugins: [htmlParser],
    parser: 'html',
    tabWidth: 2,
  });
};

export type Inserter = (text: string) => void;
export type Imperivatives = { inserter: Inserter };

const RichEditor = ({
  ref,
  value,
  blog,
  onChange,
  onUpdate,
}: {
  ref?: Ref<Imperivatives>;
  value?: string;
  blogLanguage: string | undefined;
  blog?: Blog;
  onChange?: (_: any) => void;
  onUpdate?: (_: any) => void;
}) => {
  const [isAffiliateLinkDialogOpen, toggleAffiliateLinkDialog] = useToggle(false);
  const [isRephraseDialogOpen, toggleRephraseDialog] = useToggle(false);
  const [isExtendDialogOpen, toggleExtendDialog] = useToggle(false);
  const [highlightedText, setHighlightedText] = useState('');
  const [selectedImgTab, setSelectedImgTab] = useState<ImageSelectionTabs>('library');
  const [htmlCode, setHtmlCode] = useState(value || '');
  const [isHtml, toggleHtml] = useToggle(false);
  const [isCtaDialogOpen, toggleCtaDialog] = useToggle(false);
  const [selectedButtonData, setSelectedButtonData] = useState<any>(null);

  const editor = useEditor({
    extensions: [
      // Heading.configure({
      //   HTMLAttributes: {},
      // }),
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Link.configure({
        protocols: ['mailto'],
        autolink: true,
        linkOnPaste: true,
        openOnClick: false,
        validate: (href) => /^https?:\/\//.test(href),
      }),
      Underline,
      Image.configure({
        allowBase64: true,
        HTMLAttributes: {
          class: 'content-image',
        },
      }),
      Iframe,
      SearchAndReplace.configure({
        currentSearchResultClass: 'bg-yellow-500',
        searchResultClass: 'bg-yellow-200',
        disableRegex: false,
      }),
      TableHeader.configure(),
      TableCell.configure(),
      TableRow.configure(),
      Table.configure(),
      TextStyle,
      FontSizeExtension,
      CTAExtension,
      ImageWithLinkExtension,
    ],
    onSelectionUpdate: () => {
      if (!editor) return;
      setTimeout(() => {
        const isCTA = editor.isActive('ctaButton');
        if (isCTA) {
          const ctaAttributes = editor.getAttributes('ctaButton');
          setSelectedButtonData(ctaAttributes);
        } else {
          setSelectedButtonData(null);
        }
      }, 0);
    },
    content: value || '',
    onBlur: async ({ editor: _editor }) => {
      if (_editor) {
        setHtmlCode(await parseHtml(_editor.getHTML()));
        if (onChange) onChange(_editor.getHTML());
      }
    },
    onUpdate: ({ editor: _editor }) => {
      if (_editor) {
        if (onUpdate) onUpdate(_editor.getHTML());
        if (onChange) onChange(_editor.getHTML());
      }
    },
  });

  const getHighlights = (): string => {
    if (!editor) return '';
    else {
      const { view, state } = editor;
      const { from, to } = view.state.selection;
      return state.doc.textBetween(from, to, '');
    }
  };

  useEffect(() => {
    if (editor && value) {
      if (editor.getHTML() !== value) {
        editor.commands.setContent(value);
        parseHtml(value).then(setHtmlCode);
      }
    }
  }, [editor, value]);

  useImperativeHandle(
    ref,
    () => ({
      inserter(text: string) {
        editor!.chain().focus().insertContent(text).run();
      },
    }),
    [editor]
  );

  if (!editor) return null;

  return (
    <EditorContainer className="prose prose-base max-w-full rounded-lg bg-bg2">
      <EditorAccessContext.Provider
        value={{
          editor,
          blog,
        }}
      >
        <ImageDialogProvider
          onImageSelect={(url) => {
            const { to } = editor.state.selection;
            editor
              .chain()
              .focus()
              .insertContentAt(to, { type: 'image', attrs: { src: url } })
              .run();
          }}
        >
          {({ toggleDialog }) => (
            <>
              <RichEditorMenuBar
                editor={editor}
                isHtml={isHtml}
                toggleHtml={toggleHtml}
                setSelectedImgTab={setSelectedImgTab}
              />
              <BubbleMenu
                editor={editor}
                tippyOptions={{ duration: 100 }}
                className="flex items-center rounded-lg border border-gray10 bg-bg2 px-2 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)]"
              >
                {selectedButtonData ? (
                  <>
                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={() => window.open(selectedButtonData.href, '_blank')}
                      variant="ghost"
                      size="sm"
                    >
                      <FaLink />
                      <span className="font-ibx text-xs uppercase">Open Link</span>
                    </Button>

                    <div className="h-2 w-px bg-gray12/50" />

                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={() => toggleCtaDialog(true)}
                      variant="ghost"
                      size="sm"
                    >
                      <AiFillEdit />
                      <span className="font-ibx text-xs uppercase">Edit CTA</span>
                    </Button>

                    <div className="h-2 w-px bg-gray12/50" />

                    <Button
                      className="flex items-center gap-2 rounded-lg text-red5 hover:bg-transparent hover:text-red5"
                      onClick={() => {
                        editor.chain().focus().deleteSelection().run();
                      }}
                      variant="ghost"
                      size="sm"
                    >
                      <MdDelete />
                      <span className="font-ibx text-xs uppercase">Delete CTA</span>
                    </Button>
                  </>
                ) : (
                  <>
                    {' '}
                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={() => {
                        setHighlightedText(getHighlights());
                        toggleRephraseDialog();
                      }}
                      variant="ghost"
                      size="sm"
                    >
                      <img src="/images/icons/stars.svg" className="m-0 size-3 grayscale" />
                      <span className="font-ibx text-xs uppercase">Rephrase</span>
                    </Button>
                    <div className="h-2 w-px bg-gray12/50" />
                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={() => {
                        setHighlightedText(getHighlights());
                        toggleExtendDialog();
                      }}
                      variant="ghost"
                      size="sm"
                    >
                      <FaExpandArrowsAlt />
                      <span className="font-ibx text-xs uppercase">Extend</span>
                    </Button>
                    <div className="h-2 w-px bg-gray12/50" />
                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={toggleAffiliateLinkDialog}
                      variant="ghost"
                      size="sm"
                    >
                      <MdOutlineAddLink />
                      <span className="font-ibx text-xs uppercase">Affiliate-Link</span>
                    </Button>
                    <div className="h-2 w-px bg-gray12/50" />
                    <Button
                      className="flex items-center gap-2 rounded-lg hover:bg-transparent"
                      onClick={() => {
                        setHighlightedText(getHighlights());
                        toggleDialog(true);
                      }}
                      variant="ghost"
                      size="sm"
                    >
                      <FaImage />
                      <span className="font-ibx text-xs uppercase">AI-Image</span>
                    </Button>
                  </>
                )}
              </BubbleMenu>

              <ImageInsertDialog prompt={highlightedText} defaultTab={selectedImgTab} />
            </>
          )}
        </ImageDialogProvider>

        <div className="rounded-b-lg border border-t-0 border-gray10 text-black4">
          {isHtml ? (
            <CodeMirror
              onBlur={() => {
                editor.commands.setContent(htmlCode);
                if (onChange) onChange(htmlCode);
              }}
              onChange={(v) => setHtmlCode(v)}
              value={htmlCode}
              extensions={[
                html({
                  matchClosingTags: true,
                  selfClosingTags: true,
                  autoCloseTags: true,
                }),
              ]}
              minHeight="300px"
            />
          ) : (
            <EditorContent editor={editor} />
          )}
        </div>

        <CtaDialog
          isOpen={isCtaDialogOpen}
          onClose={toggleCtaDialog}
          editor={editor}
          updatedData={selectedButtonData}
        />

        <AffiliateLinkInserterDialog
          text={getHighlights()}
          isOpen={isAffiliateLinkDialogOpen}
          onClose={toggleAffiliateLinkDialog}
        />
        {isRephraseDialogOpen && (
          <BlogRephraseDialog
            selectedText={highlightedText}
            isOpen={isRephraseDialogOpen}
            onClose={toggleRephraseDialog}
          />
        )}
        {isExtendDialogOpen && (
          <BlogExtendDialog
            selectedText={highlightedText}
            isOpen={isExtendDialogOpen}
            onClose={toggleExtendDialog}
          />
        )}
      </EditorAccessContext.Provider>
    </EditorContainer>
  );
};

const EditorContainer = styled.div`
  border-radius: 0px 0px 3px 3px;
  background: transparent;
  .ProseMirror,
  .cm-editor {
    border-radius: 0px 0px 8px 8px;
    border: 1px solid transparent;
    min-height: 30vh;
    a {
      color: ${Theme.colors.blue};
      text-decoration: underline;
    }
    &.ProseMirror-focused,
    &.cm-focused {
      background-color: ${Theme.colors.gray7};
      border-color: ${Theme.colors.blue};
      outline: none;
    }
  }
  .ProseMirror {
    background-color: white;
    padding: 24px 40px;
    border-top: 0px;
    a {
      color: ${Theme.colors.blue};
      text-decoration: underline;
    }
    &.ProseMirror-focused {
      background-color: ${Theme.colors.gray7};
      border-color: ${Theme.colors.blue};
      outline: none;
    }
  }
  .cm-editor {
    background: transparent;
    outline: none;
    .cm-gutters {
      background: ${Theme.colors.gray7} !important;
    }
  }
  .content-image {
    max-width: -webkit-fill-available !important;
    max-width: -moz-available !important;
    max-width: stretch !important;
    max-width: 100%;
    display: block;
    height: auto;
    width: 100%;
    ${mediaQuery.sm`
      max-width: 100%;
    `}
  }
`;

export default RichEditor;
