import type { Props as ReactSelectProps, DropdownIndicatorProps, StylesConfig } from 'react-select';

import { PiCaretUpDownFill } from 'react-icons/pi';
import ReactSelect, { components } from 'react-select';

import Theme from '@/styles/theme';

const DropdownIndicator = (props: DropdownIndicatorProps) => (
  <components.DropdownIndicator {...props}>
    <PiCaretUpDownFill className="text-gray14" size={16} />
  </components.DropdownIndicator>
);

type SelectProps = ReactSelectProps & { label: string; hint?: string };
const Select: React.FC<SelectProps> = ({ label, hint, className, ...props }) => (
  <label className={className}>
    <span className="mb-2 flex items-center gap-1">
      <span className="text-md font-medium">{label}</span>
      {hint && <span className="text-sm text-gray9">({hint})</span>}
    </span>

    <ReactSelect
      placeholder={props.placeholder}
      value={
        (props.options as ReactSelectOptionType[])?.find((item) => item.value === props.value) ||
        props.defaultValue ||
        props.options?.[0]
      }
      styles={selectStyles}
      name={props.name}
      // @ts-ignore For Now
      onChange={(option: ReactSelectProps, action: any) => {
        if (props.onChange && option.value) {
          props.onChange(
            {
              target: {
                value: (option as ReactSelectProps).value,
                name: props.name,
              },
            },
            action
          );
        }
      }}
      components={{ IndicatorSeparator: null, DropdownIndicator }}
      options={props.options}
    />
  </label>
);

const selectStyles: StylesConfig = {
  container: (styles: Record<string, unknown>) => ({
    ...styles,
    zIndex: 10,
  }),
  control: (styles: Record<string, unknown>) => ({
    ...styles,
    borderColor: Theme.colors.gray10,
    color: Theme.colors.black4,
    boxShadow: 'transparent',
    borderRadius: '8px',
    paddingLeft: '6px',
    borderWidth: '1px',
    fontSize: '15px',
    fontWeight: 500,
    '&:hover': {
      boxShadow: `0 0 0 2px ${Theme.colors.brandColor2}`,
      borderColor: Theme.colors.blue,
    },
  }),
  option: (styles: Record<string, unknown>) => ({
    ...styles,
    backgroundColor: Theme.colors.white,
    color: Theme.colors.black4,
    '&:hover': {
      backgroundColor: Theme.colors.brandColor2,
    },
  }),
  placeholder: (styles: Record<string, unknown>) => ({
    ...styles,
    color: Theme.colors.gray14,
  }),
};

export type ReactSelectOptionType = {
  label: string;
  value: string;
};
export default Select;
