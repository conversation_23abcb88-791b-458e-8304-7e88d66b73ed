import { scaleQuantize } from '@visx/scale';
import { Mercator } from '@visx/geo';
import * as topoJson from 'topojson-client';

import { COUNTRY_BY_ISO3 } from '@/constants/address';
import topology from '@/constants/world-topo.json';
import Theme from '@/styles/theme';

export const background = '#ffffff';

export type GeoMercatorProps = {
  width?: number;
  height?: number;
  events?: boolean;
};

interface FeatureShape {
  type: 'Feature';
  id: string;
  geometry: { coordinates: [number, number][][]; type: 'Polygon' };
  properties: { name: string };
  value: number;
}

// @ts-expect-error Error
const world = topoJson.feature(topology, topology.objects.units) as {
  type: 'FeatureCollection';
  features: FeatureShape[];
};

const color = scaleQuantize({
  domain: [0, 1],
  range: [Theme.colors.gray5, Theme.colors.blue],
});

const WorldMap = ({
  width = 700,
  height = 360,
  // events = false,
  clickByCountry,
}: GeoMercatorProps & { clickByCountry: Record<string, number> }) => {
  const centerX = width / 2;
  const centerY = height / 2;
  const scale = (width / 630) * 100;

  world.features = world.features.map((f) => {
    f.value = clickByCountry[COUNTRY_BY_ISO3[f.id]?.iso2] || 0;
    return f;
  });

  return width < 10 ? null : (
    <svg width={width} height={height}>
      <rect x={0} y={0} width={width} height={height} fill={background} rx={14} />
      <Mercator<FeatureShape>
        data={world.features}
        scale={scale}
        translate={[centerX, centerY + 50]}
      >
        {(mercator) => (
          <g>
            {mercator.features.map(({ feature, path }, i) => (
              <path
                key={`map-feature-${i}`}
                d={path || ''}
                fill={color(feature.value ? 1 : 0)}
                stroke={background}
                strokeWidth={0.5}
                // onClick={() => {
                //   if (events) alert(`Clicked: ${feature.properties.name} (${feature.id})`);
                // }}
              />
            ))}
          </g>
        )}
      </Mercator>
    </svg>
  );
};

export default WorldMap;
