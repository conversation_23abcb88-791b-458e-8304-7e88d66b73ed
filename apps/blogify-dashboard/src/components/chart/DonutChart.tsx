import type { PieDonutChartData } from '@/types/components/chart';

import Pie, { ProvidedProps, PieArcDatum } from '@visx/shape/lib/shapes/Pie';
import { useTransition, animated, to } from '@react-spring/web';
import { scaleOrdinal } from '@visx/scale';
import { Group } from '@visx/group';

const defaultMargin = { top: 20, right: 20, bottom: 20, left: 20 };

export type PieProps = {
  data: PieDonutChartData[];
  colors: string[];
  domain: PieDonutChartData['label'][];
  width?: number;
  height?: number;
  margin?: typeof defaultMargin;
  animate?: boolean;
};

const DonutChart = ({
  data,
  colors,
  domain,
  width = 200,
  height = 200,
  margin = defaultMargin,
  animate = true,
}: PieProps) => {
  if (width < 10) return null;

  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;
  const radius = Math.min(innerWidth, innerHeight) / 2;
  const centerY = innerHeight / 2;
  const centerX = innerWidth / 2;
  const donutThickness = 20;

  const getBrowserColor = scaleOrdinal({ domain, range: colors });

  return (
    <svg width={width} height={height}>
      <Group top={centerY + margin.top} left={centerX + margin.left}>
        <Pie
          innerRadius={radius - donutThickness}
          pieValue={(d) => d.value}
          outerRadius={radius}
          cornerRadius={0}
          padAngle={0}
          data={data}
        >
          {(pie) => (
            <AnimatedPie<PieDonutChartData>
              onClickDatum={({ data: { label } }) => animate && console.log(label)}
              getColor={(arc) => getBrowserColor(arc.data.label)}
              getKey={(arc) => arc.data.label}
              animate={animate}
              {...pie}
            />
          )}
        </Pie>
      </Group>
    </svg>
  );
};

type AnimatedStyles = { startAngle: number; endAngle: number; opacity: number };

const fromLeaveTransition = ({ endAngle }: PieArcDatum<any>) => ({
  // enter from 360° if end angle is > 180°
  startAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  endAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  opacity: 0,
});
const enterUpdateTransition = ({ startAngle, endAngle }: PieArcDatum<any>) => ({
  startAngle,
  endAngle,
  opacity: 1,
});

type AnimatedPieProps<Datum> = ProvidedProps<Datum> & {
  animate?: boolean;
  getKey: (d: PieArcDatum<Datum>) => string;
  getColor: (d: PieArcDatum<Datum>) => string;
  onClickDatum: (d: PieArcDatum<Datum>) => void;
  delay?: number;
  showText?: boolean;
};

function AnimatedPie<Datum>({
  animate,
  arcs,
  path,
  getKey,
  getColor,
  onClickDatum,
  showText,
}: AnimatedPieProps<Datum>) {
  const transitions = useTransition<PieArcDatum<Datum>, AnimatedStyles>(arcs, {
    from: animate ? fromLeaveTransition : enterUpdateTransition,
    enter: enterUpdateTransition,
    update: enterUpdateTransition,
    leave: animate ? fromLeaveTransition : enterUpdateTransition,
    keys: getKey,
  });
  return transitions((props, arc, { key }) => {
    const [centroidX, centroidY] = path.centroid(arc);
    const hasSpaceForLabel = arc.endAngle - arc.startAngle >= 0.1;

    return (
      <g key={key}>
        <animated.path
          // @ts-ignore Fix Later
          d={to([props.startAngle, props.endAngle], (startAngle, endAngle) =>
            path({ ...arc, startAngle, endAngle })
          )}
          onTouchStart={() => onClickDatum(arc)}
          onClick={() => onClickDatum(arc)}
          fill={getColor(arc)}
        />
        {hasSpaceForLabel && (
          // @ts-ignore Fix Later
          <animated.g style={{ opacity: props.opacity }}>
            {showText && (
              <text
                pointerEvents="none"
                textAnchor="middle"
                x={centroidX}
                y={centroidY}
                fontSize={9}
                fill="white"
                dy=".33em"
              >
                {getKey(arc)}
              </text>
            )}
          </animated.g>
        )}
      </g>
    );
  });
}

export default DonutChart;
