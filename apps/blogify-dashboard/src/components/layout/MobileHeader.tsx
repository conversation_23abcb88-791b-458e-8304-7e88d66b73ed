import { IoMdClose } from 'react-icons/io';
import { useState } from 'react';
import { FiMenu } from 'react-icons/fi';

import {
  SheetDescription,
  SheetContent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  SheetClose,
  Sheet,
} from '@ps/ui/components/sheet';
import { VisuallyHidden } from '@ps/ui';
import { Button } from '@ps/ui/components/button';

type Props = { Logo: React.FC<{ className?: string }>; children: React.ReactNode };
export default function MobileHeader({ Logo, children }: Props) {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button className="-mr-4" variant="icon">
          <span className="sr-only">Open Main Menu</span>
          <FiMenu className="size-6" aria-hidden="true" />
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full overflow-y-auto border-none bg-dark px-4 py-3 text-white">
        <VisuallyHidden>
          <SheetHeader>
            <SheetTitle>Main Menu Sidebar</SheetTitle>
            <SheetDescription>
              Menu Sidebar with options to navigate the current page along with login, signup
              buttons.
            </SheetDescription>
          </SheetHeader>
        </VisuallyHidden>

        <header className="flex items-center justify-between">
          <Logo />

          <SheetClose asChild>
            <Button className="-mr-2" variant="icon">
              <span className="sr-only">Open Main Menu</span>
              <IoMdClose className="size-6" aria-hidden="true" />
            </Button>
          </SheetClose>
        </header>

        <nav onClick={() => setOpen(false)}>{children}</nav>
      </SheetContent>
    </Sheet>
  );
}
