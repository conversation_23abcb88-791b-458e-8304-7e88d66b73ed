import type { SocialIntegration } from '@/types/misc/integration.type';

import SocialIcon from '@/components/misc/SocialIcon';

const SOCIAL_LINKS: Record<
  SocialIntegration | 'pinterest' | 'tiktok' | 'vimeo' | 'productHunt' | 'g2',
  string
> = {
  facebook: 'https://www.facebook.com/Blogifyai',
  instagram: 'https://www.instagram.com/blogifyaiinc',
  twitter: 'https://twitter.com/blogify_ai',
  linkedin: 'https://www.linkedin.com/company/blogifyai',
  youtube: 'https://www.youtube.com/channel/UCbvx0rVwgMJyhen81NQ225w',
  pinterest: 'https://www.pinterest.com/blogifyai',
  tiktok: 'https://www.tiktok.com/@blogifyinc',
  vimeo: 'https://vimeo.com/user204989682',
  productHunt: 'https://www.producthunt.com/products/blogify',
  g2: 'https://www.g2.com/products/blogify/reviews',
};

const SocialLinks = () => (
  <>
    {(Object.keys(SOCIAL_LINKS) as SocialIntegration[]).map((p, i) => (
      <SocialIcon key={i} integration={p} to={SOCIAL_LINKS[p]} />
    ))}
  </>
);

export default SocialLinks;
