import type { Product } from '@/types/resources/product.type';

import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import AddonCard from '@/views/dashboard/addons/components/AddonCard';

export default function AddonPurchaseLayout({
  imageSrc,
  addon,
  title,
}: {
  imageSrc: string;
  addon: Product;
  title: string;
}) {
  return (
    <DashboardContainer title={title}>
      <div className="flex flex-col gap-10 rounded bg-white p-6 md:flex-row">
        <div className="order-2 w-full md:order-1 md:w-8/12">
          <img src={imageSrc} width="100%" />
        </div>
        <div className="order-1 w-full md:order-2 md:w-4/12">
          {!!addon.price && <AddonCard className="!shadow-none" addon={addon as Product} />}
        </div>
      </div>
    </DashboardContainer>
  );
}
