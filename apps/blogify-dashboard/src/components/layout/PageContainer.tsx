const PageContainer = ({ children, className, ...props }: React.HTMLProps<HTMLDivElement>) => (
  <div className={`!mx-auto w-full max-w-[1654px] ${className}`} {...props}>
    {children}
  </div>
);
const PageContainerMini = ({ children, className, ...props }: React.HTMLProps<HTMLDivElement>) => (
  <div className={`!mx-auto w-full max-w-[740px] ${className}`} {...props}>
    {children}
  </div>
);

export default PageContainer;
export { PageContainerMini };
