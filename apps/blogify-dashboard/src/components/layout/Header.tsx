import { useWindowScroll } from 'react-use';
import { Link } from 'react-router-dom';
import React from 'react';

import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import useAuth from '@/hooks/useAuth';

import MobileHeader from './MobileHeader';

const Header = () => {
  const { y } = useWindowScroll();

  return (
    <header
      className={cn('sticky top-0 z-50 text-white transition duration-150 ease-in-out', {
        'bg-bg/15 backdrop-blur-md': y > 50,
      })}
    >
      <nav
        className="relative z-50 mx-auto flex h-16 max-w-[1600px] items-center justify-between px-6 xl:px-8"
        aria-label="Global"
      >
        <Logo className="flex-1" />

        <div className="hidden lg:flex lg:gap-12">
          <NavLinks className="text-sm font-medium hover:text-primary" />
        </div>

        <NavCtaButtons className="hidden lg:flex lg:flex-1 lg:justify-end" />

        <div className="lg:hidden">
          <MobileHeader Logo={Logo}>
            <div className="flex flex-col gap-1 py-5">
              <NavLinks className="py-3 text-base" />
            </div>
            <NavCtaButtons className="py-5" />
          </MobileHeader>
        </div>
      </nav>
    </header>
  );
};

const Logo: React.FC<{ className?: string }> = ({ className }) => (
  <a href="/" className={className}>
    <span className="sr-only">Blogify</span>
    <img
      className="h-[26px] w-[100px]"
      src="/images/blogify-logo-with-white-text.png"
      alt="Blogify Logo"
    />
  </a>
);

const navigation = [
  { name: 'Features', href: '/#blog-generation-features' },
  { name: 'Review', href: '/#user-reviews' },
  { name: 'Pricing', href: '/#pricing-table' },
  // { name: 'Plugins', href: '#' },
  { name: 'Partner', href: '/partner-program/' },
  { name: 'Blog', href: '/blog', target: '_blank' },
];
const NavLinks = ({ className }: { className?: string }) => (
  <>
    {navigation.map(({ name, ...item }, index) => (
      <a key={index} {...item} className={cn('text-sm font-medium', className)}>
        {name}
      </a>
    ))}
  </>
);

const NavCtaButtons = ({ className }: React.ComponentProps<'div'>) => {
  const isAuthenticated = useStoreState((s) => s.user.isAuthenticated);
  const { logoutUser } = useAuth();

  return (
    <div className={cn('flex gap-4', className)}>
      {isAuthenticated ? (
        <>
          <Button size="sm" variant="glow-secondary" asChild>
            <Link to="/dashboard">Dashboard</Link>
          </Button>
          <Button size="sm" variant="glow" asChild onClick={logoutUser}>
            <Link to="#">Logout</Link>
          </Button>
        </>
      ) : (
        <>
          <Button size="sm" variant="glow-secondary" asChild>
            <Link to={`/login/${window.location.search || ''}`}>Log In</Link>
          </Button>
          <Button size="sm" variant="glow" asChild>
            <Link to={`/signup/${window.location.search || ''}`}>Sign Up</Link>
          </Button>
        </>
      )}
    </div>
  );
};

export default Header;
