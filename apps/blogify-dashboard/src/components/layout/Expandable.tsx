import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { useToggle } from 'react-use';
import { BiCopy } from 'react-icons/bi';

import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';

const Expandable = ({
  children,
  copyContent,
  initialLines = 2,
  isExpandable = true,
}: {
  children: React.ReactNode;
  copyContent?: string;
  initialLines?: number;
  isExpandable?: boolean;
}) => {
  const [expanded, toggleExpanded] = useToggle(false);

  return (
    <div>
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <div className="ellipsis" style={{ WebkitLineClamp: expanded ? 'unset' : initialLines }}>
        {children}
      </div>
      {isExpandable && (
        <div className="mt-6 flex cursor-pointer items-center justify-between gap-4">
          {copyContent ? (
            <Button variant="secondary" onClick={() => copy(copyContent)}>
              <BiCopy />
              Copy All
            </Button>
          ) : (
            <>&nbsp;</>
          )}

          <Button variant="ghost" className="!text-15 text-primary" onClick={toggleExpanded}>
            <span>Show {expanded ? 'Less' : 'More'}</span>
            {expanded ? <FaChevronUp /> : <FaChevronDown />}
          </Button>
        </div>
      )}
    </div>
  );
};

export default Expandable;
