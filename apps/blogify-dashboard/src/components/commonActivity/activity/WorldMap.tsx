// بسم الله الرحمن الرحيم

import { ComponentProps, useMemo } from 'react';
import SVGWorldMap from 'react-svg-worldmap';
import { Country } from './constants';

function WorldMap({ data }: { data: { country: keyof typeof Country; value: number }[] }) {
  const allCountriesData = useMemo(() => {
    if (!data || data.length === 0) {
      return Object.keys(Country).map((country) => ({
        country,
        value: 0,
      }));
    }

    const defaultCountries = Object.keys(Country).map((country) => ({
      country,
      value: 0,
    }));

    const mergedData = [...defaultCountries];
    data.forEach((item) => {
      const index = mergedData.findIndex((d) => d.country === item.country);
      if (index !== -1) {
        mergedData[index] = item;
      }
    });

    return mergedData;
  }, [data]);

  return (
    <div className="h-auto w-full max-w-full overflow-hidden">
      <SVGWorldMap
        strokeOpacity={0}
        backgroundColor="white"
        borderColor="#e5e7eb"
        color="#fdded7"
        valueSuffix="clicks"
        size="xl"
        richInteraction
        frame
        frameColor="white"
        data={allCountriesData}
        styleFunction={(country) => {
          const countryData =
            data && data.length > 0 ? data.find((d) => d.country === country.countryCode) : null;

          return {
            fill: countryData && countryData.value > 0 ? '#f5704f' : '#fdded7',
            hover: {
              fill: countryData && countryData.value > 0 ? '#f2470d' : '#fce8e3',
            },
          };
        }}
      />
    </div>
  );
}

export function ClicksWorldMap({ data }: { data: ComponentProps<typeof WorldMap>['data'] }) {
  return (
    <section className="flex flex-col justify-around gap-4 p-4 md:flex-row">
      <div className="w-full ">
        <WorldMap data={data} />
      </div>
      <TopClicksByCountry data={data} />
    </section>
  );
}

function TopClicksByCountry({
  data,
}: {
  data: { country: keyof typeof Country; value: number }[];
}) {
  const [percentagesClick] = useMemo(() => {
    const descending = data.sort((a, b) => b.value - a.value);
    const total = descending.reduce((prev, current) => prev + current.value, 0);
    const percentages = descending.map(({ country, value }) => ({
      country,
      percentage: value / total,
      click: value,
    }));
    return [percentages];
  }, [data]);

  // Handle empty state
  if (!data.length || !percentagesClick.length) {
    return (
      <section className="flex flex-col gap-4 md:w-1/2">
        <section className="flex flex-col gap-2">
          <span className="text-lg text-gray-700">Most Clicked</span>
          <span className="text-2xl font-semibold">-</span>
          <span>- Clicks | Share: -</span>
        </section>
        <section className="space-y-3">
          <span className="text-sm text-gray-600">Top Countries</span>
          <div className="flex flex-col gap-2">
            <div className="flex justify-between gap-6 rounded-lg bg-gray-100 p-2">
              <span className="truncate">No data available</span>
              <span>-</span>
            </div>
          </div>
        </section>
      </section>
    );
  }

  return (
    <section className="flex flex-col gap-4 md:w-1/2">
      <section className="flex flex-col gap-2">
        <span className="text-lg text-gray-700">Most Clicked</span>
        <span className="text-2xl font-semibold">
          {Country[percentagesClick[0]?.country] || '-'}
        </span>
        <span>
          {percentagesClick[0]?.click || '-'} Clicks | Share:{' '}
          {percentagesClick[0]?.percentage
            ? percentagesClick[0].percentage.toLocaleString(undefined, { style: 'percent' })
            : '-'}
        </span>
      </section>
      <section className="space-y-3">
        <span className="text-sm text-gray-600">Top Countries</span>
        <div className="flex flex-col gap-2">
          {percentagesClick.map(({ country, percentage }) => (
            <div
              className="flex justify-between gap-6 rounded-lg p-2 odd:bg-gray-100"
              key={country}
            >
              <span className="truncate">{Country[country]}</span>
              <span>{percentage.toLocaleString(undefined, { style: 'percent' })}</span>
            </div>
          ))}
        </div>
      </section>
    </section>
  );
}
