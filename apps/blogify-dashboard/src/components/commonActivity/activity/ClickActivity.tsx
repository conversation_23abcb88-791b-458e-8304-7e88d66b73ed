import { LuMousePointerClick } from 'react-icons/lu';
import './index.css';
export default function ClickActivity({
  title = 'Activity',
  count,
  labels = { clicks: 'Clicks', actions: 'Actions', conversionRate: 'Conversion Rate' },
}: {
  title?: string;
  count: { clicks: number; actions: number; cr: number };
  labels?: {
    clicks?: string;
    actions?: string;
    conversionRate?: string;
  };
}) {
  return (
    <article className="flex w-full flex-col gap-3 border-gray10  p-4">
      <span className="font-semibold">{title}</span>
      <div className="relative flex size-32 items-center justify-center self-center rounded-full ">
        <div className="custom-circle-border3 absolute inset-0 rounded-full"></div>
        <div className="custom-circle-border2 absolute size-24 rounded-full "></div>
        <div className="custom-circle-border1 absolute size-16 rounded-full"></div>
        <LuMousePointerClick size={56} className="text-primary" />{' '}
      </div>
      <div className="flex justify-between gap-2">
        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">{labels.clicks}</span>
          <span className="text-2xl font-semibold">{count.clicks}</span>
        </div>
        <div className="flex flex-col gap-2">
          <span className="text-sm font-medium">{labels.actions}</span>
          <span className="text-2xl font-semibold">{count.actions}</span>
        </div>
      </div>
      <span className="text-center text-gray9">
        {labels.conversionRate}: {count.cr.toLocaleString(undefined, { style: 'percent' })}
      </span>
    </article>
  );
}
