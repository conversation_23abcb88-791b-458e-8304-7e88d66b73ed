import { AiFillAppstore } from 'react-icons/ai';
import { FaLaptop, FaMobileAlt } from 'react-icons/fa';
import { IoMdDesktop } from 'react-icons/io';

type Device = 'mobile' | 'laptop' | 'desktop' | 'other';

const deviceIcon = {
  mobile: FaMobileAlt,
  laptop: FaLaptop,
  desktop: IoMdDesktop,
  other: AiFillAppstore,
} as const;

export default function DeviceClickActivity({
  counts,
  title = 'Device Used',
}: {
  counts: Record<Device, number>;
  title?: string;
}) {
  return (
    <section className="flex w-full flex-col gap-3 border-gray10 bg-white p-4">
      <span className="font-semibold">{title}</span>
      <div className="justify-around gap-2 *:w-full sm:flex-col lg:flex lg:flex-row-reverse">
        <ClicksCount counts={counts} />
      </div>
      <span className="mt-4 self-center text-gray9 ">
        Total: {Object.values(counts).reduce((a, b) => a + b, 0)}
      </span>
    </section>
  );
}

function ClicksCount({ counts }: { counts: Record<Device, number> }) {
  return (
    <section className="flex flex-col gap-4">
      <span className="flex justify-between gap-2 text-gray9">
        <span>Device</span>
        <span>Clicks</span>
      </span>
      {Object.entries(counts).map(([name, count]) => {
        const Icon = deviceIcon[name as Device];
        return (
          <span key={name} className="flex justify-between gap-2">
            <span className="flex items-center gap-2 capitalize">
              <Icon />
              <span className="font-medium">{name}</span>
            </span>
            <span>{count}</span>
          </span>
        );
      })}
    </section>
  );
}
