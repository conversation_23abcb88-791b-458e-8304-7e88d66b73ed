import { PeriodType } from '@/views/dashboard/analytics/Analytics';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { useState } from 'react';
import { MdCalendarMonth } from 'react-icons/md';

dayjs.extend(advancedFormat);
dayjs.extend(localizedFormat);

interface DateSelectorProps {
  selectedRange: PeriodType;
  onRangeChange: (range: PeriodType) => void;
  dateRange: dayjs.Dayjs[];
  onDateRangeChange: (range: dayjs.Dayjs[]) => void;
  showRangePicker?: boolean;
}

export default function DateSelector({
  selectedRange,
  onRangeChange,
  dateRange,
  onDateRangeChange,
  showRangePicker = true,
}: DateSelectorProps) {
  const [showCalendar, setShowCalendar] = useState(false);
  const [localDateRange, setLocalDateRange] = useState(dateRange);

  const predefinedRanges = [
    { label: 'Today', range: [dayjs(), dayjs()] },
    { label: '7 Days', range: [dayjs().subtract(7, 'day'), dayjs()] },
    { label: '30 Days', range: [dayjs().subtract(30, 'day'), dayjs()] },
  ];

  const handleRangeChange = (label: PeriodType) => {
    onRangeChange(label);
    const selectedPreset = predefinedRanges.find((r) => r.label === label);
    if (selectedPreset) {
      onDateRangeChange(selectedPreset.range);
    }
  };

  const handleApply = () => {
    onDateRangeChange(localDateRange);
    setShowCalendar(false);
    onRangeChange('custom');
  };

  return (
    <div>
      <div className="flex flex-wrap gap-4">
        <div className="flex w-full rounded-lg sm:w-max">
          <div className="flex flex-wrap rounded-lg bg-[#f4f1f0]">
            {predefinedRanges.map(({ label }) => (
              <button
                key={label}
                onClick={() => handleRangeChange(label as PeriodType)}
                className={`m-[3px] rounded-lg px-3 py-1 text-sm font-medium sm:text-base ${
                  selectedRange === label
                    ? 'items-center bg-white text-primary shadow-[0px_1px_1px_rgba(168,145,138,0.3)]'
                    : 'text-gray-700'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        {showRangePicker && (
          <div className="relative">
            <button
              onClick={() => setShowCalendar(!showCalendar)}
              className="flex items-center justify-center gap-2 rounded-[6px] border bg-white p-2 text-sm font-medium shadow-[0px_1px_1px_rgba(168,145,138,0.3)] sm:px-4 sm:text-base"
            >
              <MdCalendarMonth />
              <span>
                {dayjs(dateRange[0]).format('MMM D, YYYY')} -{' '}
                {dayjs(dateRange[1]).format('MMM D, YYYY')}
              </span>
            </button>

            {showCalendar && (
              <div className="absolute top-full z-10 mt-2 rounded-lg border bg-white p-4 shadow-lg">
                <div className="flex gap-4">
                  <div>
                    <div className="mb-2 font-medium">Start Date</div>
                    <input
                      type="date"
                      value={dayjs(localDateRange[0]).format('YYYY-MM-DD')}
                      onChange={(e) =>
                        setLocalDateRange([dayjs(e.target.value), localDateRange[1]])
                      }
                      className="rounded border p-2"
                    />
                  </div>
                  <div>
                    <div className="mb-2 font-medium">End Date</div>
                    <input
                      type="date"
                      value={dayjs(localDateRange[1]).format('YYYY-MM-DD')}
                      onChange={(e) =>
                        setLocalDateRange([localDateRange[0], dayjs(e.target.value)])
                      }
                      className="rounded border p-2"
                    />
                  </div>
                </div>
                <div className="mt-4 flex justify-end ">
                  <button
                    onClick={handleApply}
                    className="rounded bg-primary px-4 py-2 text-white hover:bg-primary/90"
                  >
                    Apply
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
