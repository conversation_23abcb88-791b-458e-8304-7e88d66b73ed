import { GoSquareFill, GoDash } from 'react-icons/go';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';
import { FaRegUser } from 'react-icons/fa';

const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload) return null;

  const metrics = payload.filter((p: any) => ['Traffic', 'Clicks', 'Actions'].includes(p.dataKey));
  const blogData = payload.find((p: any) => p.dataKey === 'blogScatters')?.payload.blogs;
  const hasMetrics = metrics.length > 0;
  const hasBlogs = blogData?.length > 0;

  if (!hasMetrics && !hasBlogs) return null;

  const iconColor = {
    Traffic: '#a39e9d',
    Clicks: '#f2470d',
    Actions: '#10967e',
  };
  const textColor = {
    Traffic: '#141414',
    Clicks: '#f2470d',
    Actions: '#10967e',
  };
  const icons = {
    Traffic: GoSquareFill,
    Clicks: GoDash,
    Actions: GoDash,
  };

  return (
    <div
      className="relative mr-4 max-w-sm rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
      // style={{
      //   position: 'relative',
      //   right: '100%',
      //   marginRight: '20px',
      // }}
    >
      <p className="mb-2 border-b pb-2 text-sm font-semibold">
        {dayjs(label).format('MMM DD, YYYY')}
      </p>

      {hasMetrics && (
        <div className="mb-3">
          {metrics.map((entry: any) => {
            const Icon = icons[entry.dataKey as keyof typeof icons];
            return (
              <div
                key={entry.dataKey}
                className="mb-1 flex items-center justify-between space-x-3 font-medium"
              >
                <span
                  className="flex items-center text-sm"
                  style={{ color: textColor[entry.dataKey as keyof typeof textColor] }}
                >
                  <Icon
                    size={14}
                    className="mr-2 rounded border-2"
                    style={{ color: iconColor[entry.dataKey as keyof typeof iconColor] }}
                  />
                  {entry.dataKey}:
                </span>

                <span>{entry.dataKey === 'Actions' ? `${entry.value}` : entry.value}</span>
              </div>
            );
          })}
        </div>
      )}

      {hasBlogs && (
        <div className={hasMetrics ? 'border-t pt-3' : ''}>
          <p className="mb-2 text-sm font-medium text-[#4d5281]">Blog Posts ({blogData.length})</p>
          {blogData.map((blog: any) => (
            <div key={blog._id} className="mb-2 flex items-center gap-2">
              {blog?.uid?.profilePicture ? (
                <img
                  src={blog.uid?.profilePicture}
                  alt={blog.uid?.name}
                  className="size-8 rounded-full object-cover"
                />
              ) : (
                <div className="rounded-full bg-gray-200 p-2">
                  <FaRegUser className="size-5" />
                </div>
              )}

              <div>
                <Link
                  to={`/dashboard/blogs/${blog._id}`}
                  className="hover:text-indigo-600 hover:underline"
                >
                  <p className=" max-w-[150px] truncate text-sm font-medium" title={blog.title}>
                    {blog.title}
                  </p>
                </Link>
                <p className="text-xs text-gray-600">{blog.uid.name}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomTooltip;
