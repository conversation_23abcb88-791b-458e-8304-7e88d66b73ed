import {
  Responsive<PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON>sed<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
  Line,
  Bar,
} from 'recharts';
import { GoDash, GoDotFill, GoSquareFill } from 'react-icons/go';
import dayjs from 'dayjs';

import { DNSActivityType, BlogDataType } from '@/views/dashboard/analytics/Analytics';

import { transformDNSActivityData } from './utils';
import CustomTooltip from './CustomTooltip';

type DnsActivityProps = {
  dnsActivityData?: DNSActivityType;
  blogsData?: BlogDataType[];
  showTraffic?: boolean;
  showBlog?: boolean;
};

const DnsActivity = ({
  dnsActivityData,
  blogsData,
  showTraffic = true,
  showBlog = true,
}: DnsActivityProps) => {
  const transformedData = transformDNSActivityData(
    dnsActivityData,
    blogsData,
    showTraffic,
    showBlog
  );

  const renderScatterPoints = (props: any) => {
    const { cx, cy, payload } = props;
    if (!payload?.blogScatters?.length || !showBlog) return null;

    const getCY = () => {
      if (cy != null) return cy;
      if (props.yAxis?.scale && payload.Traffic != null) {
        return props.yAxis.scale(payload.Traffic);
      }
      return 0;
    };

    return payload.blogScatters.map((scatter: any, index: number) => (
      <circle
        key={index}
        cx={cx + scatter.x * 3}
        cy={getCY() + scatter.y}
        r={6}
        fill="#4d5281"
        stroke="#fff"
        strokeWidth={1.5}
      />
    ));
  };

  const totalTraffic = showTraffic
    ? dnsActivityData?.views.reduce((sum, [, value]) => sum + Number(value), 0)
    : 0;
  const totalClicks = dnsActivityData?.clicks.reduce((sum, [, value]) => sum + Number(value), 0);
  const totalActions = dnsActivityData?.sales.reduce((sum, [, value]) => sum + Number(value), 0);

  const chartLabels = [
    ...(showTraffic ? [{ name: 'Traffic', Icon: GoSquareFill, color: '#e9e4e2' }] : []),
    { name: 'Clicks', Icon: GoDash, color: '#f2470d' },
    { name: 'Actions', Icon: GoDash, color: '#10967e' },
    ...(showBlog ? [{ name: 'Blog Posts', Icon: GoDotFill, color: '#4d5281' }] : []),
  ];

  return (
    <div className="pb-5">
      {/* Chart Container */}
      <div className="h-64 w-full sm:h-96">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={transformedData}
            margin={{
              top: 20,
              bottom: 15,
              right: 0,
              left: 0,
            }}
          >
            <CartesianGrid stroke="#e3dbd980" vertical={true} />
            <XAxis
              dataKey="date"
              tickFormatter={(date) => dayjs(date).format('DD')}
              stroke="#999"
              tickLine={false}
              axisLine={{ stroke: '#e3dbd980' }}
            />
            <YAxis stroke="#999" tickLine={false} axisLine={false} />
            <Tooltip content={<CustomTooltip />} />

            {showTraffic && (
              <Bar
                dataKey="Traffic"
                fill="rgba(244, 241, 240, 1)"
                name="Traffic"
                barSize={50}
                activeBar={{ fill: '#feede7' }}
              />
            )}

            {showBlog && (
              <Scatter
                dataKey="blogScatters"
                fill="#4d5281"
                name="Blog Posts"
                shape={renderScatterPoints}
              />
            )}

            <Line
              type="linear"
              dataKey="Clicks"
              stroke="#f2470d"
              name="Clicks"
              strokeWidth={1.5}
              dot={{ r: 0, fill: '#f2470d' }}
              activeDot={{ r: 4 }}
            />
            <Line
              type="linear"
              dataKey="Actions"
              stroke="#10967e"
              name="Actions"
              strokeWidth={1.5}
              dot={{ r: 0, fill: '#10967e' }}
              activeDot={{ r: 4 }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>

      {/* Chart Labels */}
      <div className="flex flex-wrap gap-4 pl-4 sm:gap-7 sm:pl-10">
        {chartLabels.map(({ name, Icon, color }) => (
          <div key={name} className="flex items-center gap-2">
            <Icon size={20} color={color} className="rounded-md border-2" />
            <span className="text-sm font-semibold uppercase">{name}</span>
          </div>
        ))}
      </div>

      {/* Statistics Section */}
      <div className="mt-6 flex justify-start gap-4 pl-4 sm:gap-16 sm:pl-10">
        {Object.entries({
          ...(showTraffic ? { Traffic: totalTraffic } : {}),
          Clicks: totalClicks,
          Actions: totalActions,
        }).map(([name, value]) => (
          <div key={name} className="mb-4 w-full text-center sm:mb-0 sm:w-auto">
            <span className="text-sm font-medium capitalize">{name}</span>
            <br />
            <span className="text-lg font-semibold">{value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DnsActivity;
