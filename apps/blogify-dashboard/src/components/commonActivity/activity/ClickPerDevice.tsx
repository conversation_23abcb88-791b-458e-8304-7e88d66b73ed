import DonutChart from '@/components/chart/DonutChart';
import { MdLens } from 'react-icons/md';

type Device = 'mobile' | 'laptop' | 'desktop' | 'other';

const deviceColor: Record<Device, string> = {
  mobile: '#f2470d',
  laptop: 'rgba(242, 71, 13, 0.75)',
  desktop: 'rgba(242, 71, 13, 0.5)',
  other: 'rgba(242, 71, 13, 0.25)',
} as const;

const deviceIcon = {
  mobile: MdLens,
  laptop: MdLens,
  desktop: MdLens,
  other: MdLens,
} as const;

export default function ClickPerDevice({
  counts,
  title = 'Device Ratio',
}: {
  counts: Record<Device, number>;
  title?: string;
}) {
  return (
    <section className="flex w-full flex-col gap-3 border-gray10  p-4">
      <span className="font-semibold">{title}</span>
      <div className="8*:w-full justify-between sm:flex-col lg:flex lg:flex-row-reverse">
        <ClicksCount counts={counts} />
        <DonutChart
          colors={Object.values(deviceColor)}
          domain={Object.keys(deviceColor)}
          data={Object.entries<number>(counts).map(([name, count]) => ({
            label: name,
            value: count,
          }))}
        />
      </div>
    </section>
  );
}

function ClicksCount({ counts }: { counts: Record<Device, number> }) {
  const total = Object.values(counts).reduce((a, b) => a + b, 0);
  return (
    <section className="flex flex-col gap-2">
      {Object.entries(counts).map(([name, count]) => {
        const Icon = deviceIcon[name as Device];
        return (
          <span key={name} className=" gap-2">
            <span className="flex items-center gap-2 capitalize">
              <Icon color={deviceColor[name as Device]} />
              <span className="font-medium text-gray9">{name}</span>
            </span>
            <span className="mx-6">
              ({total > 0 ? (count / total).toLocaleString(undefined, { style: 'percent' }) : '0%'})
            </span>
          </span>
        );
      })}
    </section>
  );
}
