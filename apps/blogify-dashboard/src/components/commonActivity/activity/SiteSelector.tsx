/* eslint-disable @typescript-eslint/no-shadow */
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@ps/ui/components/select';
import { FaGlobe } from 'react-icons/fa';

const SITES = [
  { label: 'All Sites', value: 'all', Icon: FaGlobe },
  // { label: 'Website 1', value: 'website1', Icon: FaLaptop },
  // { label: 'Website 2', value: 'website2', Icon: FaMobileAlt },
] as const;

export type SiteValue = (typeof SITES)[number]['value'];

interface SiteSelectorProps {
  value: SiteValue;
  onValueChange: (value: SiteValue) => void;
  className?: string;
}

const SiteSelector = ({ value, onValueChange, className = 'w-48' }: SiteSelectorProps) => {
  const selectedSite = SITES.find((site) => site.value === value);

  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select Site">
          {selectedSite && (
            <span className="flex items-center font-semibold">
              <selectedSite.Icon className="mr-2 size-4 text-primary" />
              {selectedSite.label}
            </span>
          )}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Sites</SelectLabel>
          {SITES.map(({ value, label, Icon }) => (
            <SelectItem key={value} value={value}>
              <span className="flex items-center">
                <Icon className="mr-2 size-4" />
                {label}
              </span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default SiteSelector;
