import { BlogDataType, DNSActivityType } from '@/views/dashboard/analytics/Analytics';

export function calculateBarPosition(blogifyPublishTime: any, barHeight: number) {
  const hours = new Date(blogifyPublishTime).getUTCHours();
  const minutes = new Date(blogifyPublishTime).getUTCMinutes();
  const totalMinutes = hours * 60 + minutes;
  const fractionOfDay = totalMinutes / 1440;
  const position = barHeight - fractionOfDay * barHeight;
  return Math.min(Math.max(position, 0), barHeight);
}

export function transformDNSActivityData(
  dnsActivityData: DNSActivityType | undefined,
  blogsData: BlogDataType[] | undefined,
  showTraffic: boolean = true,
  showBlog: boolean = true
) {
  // Get all unique dates from all metrics
  const allDates = Array.from(
    new Set([
      ...(dnsActivityData?.sales.map((item) => item[0].split('T')[0]) ?? []),
      ...(dnsActivityData?.clicks.map((item) => item[0].split('T')[0]) ?? []),
      ...(dnsActivityData?.views.map((item) => item[0].split('T')[0]) ?? []),
    ])
  ).sort();

  return allDates.map((date) => {
    // Find matching records for this date
    const salesRecord = dnsActivityData?.sales.find(
      ([itemDate]) => itemDate.split('T')[0] === date
    );
    const clicksRecord = dnsActivityData?.clicks.find(
      ([itemDate]) => itemDate.split('T')[0] === date
    );

    // Sum all views for this date
    const viewsCount =
      dnsActivityData?.views
        .filter(([itemDate]) => itemDate.split('T')[0] === date)
        .reduce((sum, [, value]) => sum + Number(value), 0) ?? 0;

    // Get blogs for this date if showBlog is true
    const blogsOnDate = showBlog
      ? (blogsData?.filter(
          (blog) => new Date(blog.blogifyPublishTime).toISOString().split('T')[0] === date
        ) ?? [])
      : [];

    // Calculate blog scatter points
    const blogScatters = showBlog
      ? blogsOnDate.map((blog, i) => {
          const timeWisePosition = calculateBarPosition(
            blog.blogifyPublishTime,
            viewsCount * 2 - 15
          );
          return {
            x: i - (blogsOnDate.length - 1) / 2,
            y: timeWisePosition,
          };
        })
      : [];

    return {
      date: new Date(date),
      Traffic: showTraffic ? viewsCount : 0,
      Clicks: clicksRecord?.[1] ?? 0,
      Actions: salesRecord?.[1] ?? 0,
      blogs: showBlog ? blogsOnDate : [],
      blogScatters: showBlog ? blogScatters : [],
    };
  });
}
