import { ComponentProps } from 'react';
import { ClicksWorldMap } from './WorldMap';

interface ClicksByCountryProps {
  title?: string;
  data: ComponentProps<typeof ClicksWorldMap>['data'];
}

export default function ClicksByCountry({ title = 'Visitors From', data }: ClicksByCountryProps) {
  return (
    <article className="p-3">
      <span className="font-semibold text-medium">{title}</span>
      <ClicksWorldMap data={data} />
    </article>
  );
}
