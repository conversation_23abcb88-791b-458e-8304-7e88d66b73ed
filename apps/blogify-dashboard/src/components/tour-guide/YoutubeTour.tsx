import Joyride, { ACTIONS, BeaconRenderProps, CallBackProps, Step } from 'react-joyride';
// import { useNavigate } from 'react-router-dom';

import { useYoutubeTourGuideContext } from '@/context/YoutubeTourGuide';
import { getConnectUrl } from '@/utils/integration';
import { Button } from '@ps/ui/components/button';
import theme from '@/styles/theme';

import { Beacon } from './UserTour';

// User onboard tour guide steps
const steps: Step[] = [
  {
    title: 'Welcome!',
    content:
      'We have prepared a guided tour to help you get started with our feature, YouTube Connect!',
    target: 'body',
    floaterProps: {
      autoOpen: true,
      hideArrow: true,
    },
    placement: 'center',
  },
  {
    title: 'Connect your YouTube channel',
    content:
      'Connect your YouTube channel by signing in with your Google account. Only one account can be connected at a time.',
    target: '#youtubeConnectButton',
    placement: 'bottom',
  },
  {
    title: 'Video blog credit',
    content: 'You can see the available credits video blog credits here.',
    target: '.youtubeCreditInfo',
    placement: 'bottom',
    hideBackButton: true,
  },
  {
    title: 'Buy additional credits',
    content: 'You can also purchase more credits by clicking on the "Buy Credit" button.',
    target: '#youtubeBuyCredit',
    placement: 'bottom',
  },
  {
    title: 'Search youtube video',
    content: 'You can search for any YouTube video by typing in the search input field.',
    target: '#youtubeSearch',
    placement: 'bottom',
  },
  {
    title: 'Filter your videos',
    content:
      'You can filter your videos by clicking on the filter button and selecting the desired filter.',
    target: '#youtubeFilter',
    placement: 'bottom',
  },
  {
    title: 'Explore other options',
    content: (
      <div className="flex flex-col gap-2">
        You can also explore other options such as Generate content for each video by clicking on
        the following button:
        <Button variant="secondary" className="w-full">
          <img className="size-[14px]" src="/images/starburst.svg" />
          <div className="ml-2">Generate</div>
        </Button>
        Beside each video.
      </div>
    ),
    target: 'body',
    placement: 'center',
  },
];

export default function YoutubeTour() {
  const {
    setState,
    state: { run, stepIndex },
  } = useYoutubeTourGuideContext();
  // const navigate = useNavigate();

  const handleCallback = (data: CallBackProps) => {
    const { action, index, type, status } = data; // lifecycle

    if (type === 'step:after' && index === 1 && action === 'next') {
      setState({ run: false });
      window.open(getConnectUrl('youtube'));
    }
    // else if (type === 'step:after' && index === 4) {
    //   if (action === 'next') {
    //     setState({ run: false, stepIndex: 5 });
    //   } else if (action == 'prev') {
    //     setState({
    //       stepIndex: index - 1,
    //     });
    //   }
    // }
    else if (type === 'step:after') {
      setState({
        stepIndex: index + (action === ACTIONS.PREV ? -1 : 1),
      });
    } else if (status === 'finished' || status === 'skipped') {
      setState({ run: false, stepIndex: 0, tourActive: false });
      localStorage.setItem('youtubeTourGuide', 'true');
    }
  };

  return (
    <Joyride
      callback={handleCallback}
      steps={steps}
      run={run}
      stepIndex={stepIndex}
      continuous
      showSkipButton
      scrollOffset={300}
      hideCloseButton
      beaconComponent={Beacon as React.ElementType<BeaconRenderProps>}
      locale={{
        last: 'Finish',
      }}
      styles={{
        options: {
          backgroundColor: theme.colors.white,
          primaryColor: theme.colors.success,
          textColor: theme.colors.black0,
          zIndex: 1000,
        },
      }}
    />
  );
}
