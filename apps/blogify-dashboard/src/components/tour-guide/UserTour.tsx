import Joyride, { BeaconRenderProps, Step } from 'react-joyride';
import styled, { keyframes } from 'styled-components';
import React from 'react';

import useUserTour from '@/hooks/useUserTour';
import theme from '@/styles/theme';

const BeaconFlick = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
`;

export const Beacon = styled.span`
  animation: ${BeaconFlick} 1s infinite;
  display: inline-block;
  border-radius: 50%;
  border: 3px solid #27ba7c;
  padding: 4px;

  &:before {
    background-color: #27ba7c;
    border-radius: 50%;
    content: '';
    display: block;
    height: 20px;
    width: 20px;
  }
`;

export default function UserTour({ steps, pageKey }: { steps: Step[]; pageKey: string }) {
  const { run, stepIndex, handleJoyrideCallback } = useUserTour({
    key: pageKey,
  });
  return (
    <Joyride
      callback={handleJoyrideCallback}
      steps={steps}
      run={run}
      stepIndex={stepIndex}
      continuous={true}
      showSkipButton={true}
      scrollOffset={300}
      hideCloseButton
      beaconComponent={Beacon as React.ElementType<BeaconRenderProps>}
      locale={{
        last: 'Close',
      }}
      styles={{
        options: {
          backgroundColor: theme.colors.white,
          primaryColor: theme.colors.success,
          textColor: theme.colors.black0,
        },
      }}
    />
  );
}
