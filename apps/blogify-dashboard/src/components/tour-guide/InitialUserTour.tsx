import Joyride, { ACTIONS, BeaconRenderProps, CallBackProps, Step } from 'react-joyride';
import { useNavigate } from 'react-router-dom';

import { useInitialUserTourGuideContext } from '@/context/InitialUserTourGuide';
import theme from '@/styles/theme';

import { Beacon } from './UserTour';

// User onboard tour guide steps
const steps: Step[] = [
  {
    title: 'Welcome!',
    content: 'We have prepared for you a guided tour to help you get started with Blogify.ai.',
    target: 'body',
    floaterProps: {
      autoOpen: true,
      hideArrow: true,
    },
    placement: 'center',
  },
  {
    title: 'Click Subscriptions',
    content:
      'If You Have Multiple codes, Redeem them one by one, from lowest to highest tier/plan.',
    target: '#subscription',
    placement: 'right-start',
  },
  {
    title: 'Click The Setting',
    content:
      'Click Settings and Connect Your Social Media/Blogging accounts, to publish your blog directly or schedule it.',
    target: '#settings',
    placement: 'right',
  },
  {
    title: 'Click Blogs Menu',
    content: 'Here magic happens :) Create your first Blog!',
    target: '#blogs',
    placement: 'right-start',
  },
  {
    title: 'Click Create a New Blog',
    content: 'Here you are going to create Blogs!',
    target: '.userTour-create-blog',
    placement: 'left-start',
    hideBackButton: true,
  },
  {
    title: 'First Create a Blog from TEXT/PDF/DOC',
    content:
      'To learn quickly, we recommend creating your first blog by entering a title/short description in the text box or you can upload your PDF/doc',
    target: '.userTour-create-blog-from-text',
    hideBackButton: true,
  },
  {
    title: 'Finalize Blog Setting',
    content: 'Set up your Blog Size, Tone, Input language, and Output Language.',
    target: '#blog-settings',
    placement: 'left-end',
  },
  {
    title: 'Upload Image',
    content: `Write your content Blog cover Image and If you select Medium or Large size Blog, upload the content image, This will help you to place the content in the blog's subheading.`,
    target: '.userTour-create-blog-from-text-cover-image',
    placement: 'top',
  },
  {
    title: 'Opt-in/out Publish After Review',
    content: `Opt-in, If want to take a look before we publish it to your connected account. If you want to directly publish the Opt-out.`,
    target: '.userTour-create-blog-from-text-publish-checkbox',
    placement: 'top',
  },
  {
    title: 'Generate Blog',
    content: `Hit Generate Blog to experience the AI magic! Give us a few min. and you will be done with your First blog generation!`,
    target: '#generate-blog',
    placement: 'top',
  },
  {
    title: 'Now create a blog from Audio/Video/Podcast',
    content: `Let's work like a pro now - Create a blog,  just paste your Public and Downloadable YouTube/Vimeo/Google Podcast link, or upload any video/audio file format. The rest of the steps are the same.`,
    target: '.userTour-create-blog-from-video',
  },
  {
    title: 'Final Call',
    content: `We believe you are on your own now for the rest of the things, Explore the YouTube Channel section, You can connect your YouTube channel and convert 30+ videos into a Blog at a time. After you are done with blog generation, you can Edit it, Drag your uploaded image anywhere, Save it, Download it as PDF, or DOC, click copy of TEXT/HTML, One Click copy of Summery, Meta Tag, Hash Tags, Tags.
    Happy Blogging!!!!`,
    target: 'body',
    placement: 'center',
  },
];

export default function InitialUserTour({
  isSidebarVisible,
  toggleSidebar,
}: {
  isSidebarVisible: boolean;
  toggleSidebar: () => void;
}) {
  const {
    setState,
    state: { run, stepIndex },
  } = useInitialUserTourGuideContext();
  const navigate = useNavigate();

  const handleCallback = (data: CallBackProps) => {
    const { action, index, type, status } = data; // lifecycle
    if (
      type === 'step:after' &&
      (index === 3 /* or step.target === '#home' */ || (action === 'prev' && index === 4))
    ) {
      if (isSidebarVisible) toggleSidebar();
      setState({ run: false });

      navigate('/dashboard/blogs');
    } else if (type === 'step:after' && index === 4) {
      if (action === 'next') {
        setState({ run: false });
        navigate('/dashboard/blogs/select-source');
      }
    } else if (type === 'step:after') {
      if (index === 0) {
        if (!isSidebarVisible) toggleSidebar();
      }
      setState({
        stepIndex: index + (action === ACTIONS.PREV ? -1 : 1),
      });
    } else if (status === 'finished' || status === 'skipped') {
      setState({ run: false, stepIndex: 0, tourActive: false });
      localStorage.setItem('initialUserTourGuide', 'true');
    }
  };

  return (
    <Joyride
      callback={handleCallback}
      steps={steps}
      run={run}
      stepIndex={stepIndex}
      continuous
      showSkipButton
      scrollOffset={300}
      hideCloseButton
      beaconComponent={Beacon as React.ElementType<BeaconRenderProps>}
      locale={{
        last: 'Finish',
      }}
      styles={{
        options: {
          backgroundColor: theme.colors.white,
          primaryColor: theme.colors.success,
          textColor: theme.colors.black0,
          zIndex: 1000,
        },
      }}
    />
  );
}
