import Joyride, { ACTIONS, BeaconRenderProps, CallBackProps, Step } from 'react-joyride';
import { useNavigate } from 'react-router-dom';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';
import theme from '@/styles/theme';

import { Beacon } from './UserTour';

// User onboard tour guide steps
const steps: Step[] = [
  {
    title: 'Welcome!',
    content:
      'We have prepared a guided tour to help you get started with our newest feature, writing snippets!',
    target: 'body',
    floaterProps: {
      autoOpen: true,
      hideArrow: true,
    },
    placement: 'center',
  },
  {
    title: 'Choose the desired snippet type',
    content:
      'Choose the desired snippet from all the options available or from different categories.',
    target: '#writingSnippetsFilterHeader',
    placement: 'bottom',
  },
  {
    title: 'Fill in the required details',
    content:
      'Type in your prompt and if you wish, you can add additional details such as name, title, and company as well.',
    target: '#writingSnippetsGenerateInputForm',
    placement: 'right',
    hideBackButton: true,
  },
  {
    title: 'Set the tone and language',
    content: 'Choose the tone and the language of the email.',
    target: '#writingSnippetsGenerateOtherInput',
    placement: 'right',
  },
  {
    title: 'Generate snippet type',
    content:
      'Click on generate to experience the AI magic! In a short while, the snippet type will be generated!',
    target: '#writingSnippetsGenerateSubmitButton',
    placement: 'top',
  },
  {
    title: 'Evaluate the generated result',
    content:
      'The generated result will be shown here. You can evaluate the result and make changes to it if desired. Click on the expand button to see the full result.',
    target: '#writingSnippetsResults',
    placement: 'left',
  },
  {
    title: 'Copy the generated snippet',
    content: 'You can copy the generated result by clicking on the copy button.',
    target: '#writingSnippetsResultsCopyButton',
    placement: 'left',
    hideBackButton: true,
  },
  {
    title: 'Save the final snippet',
    content: `You can save the final snippet by clicking on the save button and giving the snippet a name for ease-of-access.`,
    target: '#writingSnippetsResultsSavedDialogForm',
    placement: 'top',
  },
  {
    title: 'Check out other snippets',
    content: `To take a look at the other snippets available under the marketing category, you can click on the ‘More marketing snippets’
    Or, access other snippet types and categories from the slash-separated list.`,
    target: '#breadCrumb',
    placement: 'bottom',
  },
];

export default function WritingSnippetsTour() {
  const {
    setState,
    state: { run, stepIndex },
  } = useWritingSnippetsTourGuideContext();
  const navigate = useNavigate();

  const handleCallback = (data: CallBackProps) => {
    const { action, index, type, status } = data; // lifecycle

    if (type === 'step:after' && index === 1 && action === 'next') {
      setState({ run: false });
      navigate('/dashboard/writing-snippets/Announcement%20Email/generate');
    } else if (type === 'step:after' && index === 4) {
      if (action === 'next') {
        setState({ run: false, stepIndex: 5 });
      } else if (action == 'prev') {
        setState({
          stepIndex: index - 1,
        });
      }
    } else if (type === 'step:after') {
      setState({
        stepIndex: index + (action === ACTIONS.PREV ? -1 : 1),
      });
    } else if (status === 'finished' || status === 'skipped') {
      setState({ run: false, stepIndex: 0, tourActive: false });
      localStorage.setItem('writingSnippetsTourGuide', 'true');
    }
  };

  return (
    <Joyride
      callback={handleCallback}
      steps={steps}
      run={run}
      stepIndex={stepIndex}
      continuous
      showSkipButton
      scrollOffset={300}
      hideCloseButton
      beaconComponent={Beacon as React.ElementType<BeaconRenderProps>}
      locale={{
        last: 'Finish',
      }}
      styles={{
        options: {
          backgroundColor: theme.colors.white,
          primaryColor: theme.colors.success,
          textColor: theme.colors.black0,
          zIndex: 1000,
        },
      }}
    />
  );
}
