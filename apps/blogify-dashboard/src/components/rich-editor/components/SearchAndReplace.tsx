import type { Editor } from '@tiptap/react';

import { IoMdArrowRoundDown, IoMdArrowRoundUp } from 'react-icons/io';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

import { cn } from '@ps/ui/lib/utils';

export default function SearchAndReplace({ editor }: { editor: Editor }) {
  const [searchTerm, setSearchTerm] = useState('');

  const { searchAndReplace } = editor.storage;
  const hasSearchResults = searchAndReplace.results.length > 0;

  const scrollIntoView = () => {
    const { results, resultIndex } = searchAndReplace;
    const position = results[resultIndex];

    if (!position) return;

    editor.commands.setTextSelection(position);

    const { node } = editor.view.domAtPos(editor.state.selection.anchor);
    if (node instanceof HTMLElement) node.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };

  const search = () => {
    editor.commands.resetIndex();
    editor.commands.setSearchTerm(searchTerm);
    scrollIntoView();
  };

  useEffect(() => {
    editor.setOptions({ editable: false });
    return () => {
      editor.setOptions({ editable: true });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div id="search tools" className="flex h-10 items-center gap-3 px-4 font-inter text-xs shadow">
      <div
        id="search"
        className="flex gap-1 rounded bg-white p-1 focus-within:ring-1 focus-within:ring-primary"
      >
        <input
          type="text"
          id="search-term"
          className="py-0 placeholder:text-gray9"
          placeholder="Find word..."
          onChange={(event) => setSearchTerm(event.target.value)}
          onKeyDown={(ev) => {
            if (ev.keyCode === 13) {
              ev.preventDefault();
              search();
            }
          }}
        />

        <Button disabled={searchTerm.length === 0} onClick={search}>
          <span className="p-1">Search</span>
        </Button>
      </div>

      <span>{searchAndReplace.results.length + ' Results'}</span>

      <button
        type="button"
        disabled={!hasSearchResults}
        className="cursor-pointer enabled:text-primary"
        onClick={() => {
          editor.commands.nextSearchResult();
          scrollIntoView();
        }}
      >
        <IoMdArrowRoundDown />
      </button>

      <button
        type="button"
        disabled={!hasSearchResults}
        className="cursor-pointer enabled:text-primary"
        onClick={() => {
          editor.commands.previousSearchResult();
          scrollIntoView();
        }}
      >
        <IoMdArrowRoundUp />
      </button>

      <div
        id="replace"
        className="flex gap-1 rounded bg-white p-1 focus-within:ring-1 focus-within:ring-primary"
      >
        <input
          type="text"
          id="replace-term"
          className="py-0 placeholder:text-gray9"
          placeholder="Replace with..."
          onChange={(event) => editor.commands.setReplaceTerm(event.target.value)}
          onKeyDown={(ev) => ev.keyCode === 13 && ev.preventDefault()}
        />
        <Button
          disabled={!hasSearchResults || searchAndReplace.replaceTerm.length === 0}
          onClick={() => {
            editor.commands.replace(searchAndReplace.resultIndex);
            toast.success('Replaced!');
          }}
        >
          <span className="p-1 uppercase">Replace</span>
        </Button>
        <Button
          disabled={!hasSearchResults || searchAndReplace.replaceTerm.length === 0}
          onClick={() => {
            editor.commands.replaceAll();
            toast.success('All instances replaced!');
          }}
        >
          <span className="p-1 uppercase">Replace All</span>
        </Button>
      </div>
    </div>
  );
}

const Button = ({ className, ...props }: React.ComponentProps<'button'>) => (
  <button
    className={cn(
      'h-5 cursor-pointer rounded bg-gray9 px-1.5 uppercase text-white enabled:bg-primary'
    )}
    type="button"
    {...props}
  />
);
