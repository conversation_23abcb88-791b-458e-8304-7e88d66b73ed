import type { Editor } from '@tiptap/react';

import React from 'react';

import { SelectContent, SelectTrigger, SelectItem, Select } from '@ps/ui/components/select';

interface FontSizeSelectorProps {
  editor: Editor;
}

const FONT_SIZES = [
  { value: '12px', label: '12' },
  { value: '14px', label: '14' },
  { value: '16px', label: '16' },
  { value: '18px', label: '18' },
  { value: '20px', label: '20' },
  { value: '24px', label: '24' },
  { value: '28px', label: '28' },
  { value: '32px', label: '32' },
  { value: '36px', label: '36' },
  { value: '42px', label: '42' },
  { value: '48px', label: '48' },
  { value: '56px', label: '56' },
  { value: '64px', label: '64' },
  { value: '72px', label: '72' },
];

const DEFAULT_FONT_SIZE = '16px';

const FontSizeSelector: React.FC<FontSizeSelectorProps> = ({ editor }) => {
  if (!editor) {
    return null;
  }

  const applyFontSize = (size: string) => {
    editor.chain().focus().setFontSize(size).focus().run();
  };

  const getCurrentFontSize = (): string => {
    const attrs = editor.getAttributes('textStyle');
    return attrs.fontSize || DEFAULT_FONT_SIZE;
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setTimeout(() => {
        editor.commands.focus();
      }, 0);
    }
  };

  return (
    <label className="flex items-center gap-1.5">
      <span className="shrink-0 text-11 font-semibold">Font Size</span>

      <Select
        defaultValue={DEFAULT_FONT_SIZE}
        value={getCurrentFontSize()}
        onValueChange={applyFontSize}
        onOpenChange={handleOpenChange}
      >
        <SelectTrigger
          className="h-5 w-10 rounded px-1 !text-11"
          aria-label="Font Size"
          id="font-size-select"
        >
          {getCurrentFontSize().replace('px', '')}
        </SelectTrigger>

        <SelectContent className="min-w-20" position="popper" sideOffset={5}>
          {FONT_SIZES.map(({ value, label }) => (
            <SelectItem key={value} value={value}>
              {label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </label>
  );
};

export default FontSizeSelector;
