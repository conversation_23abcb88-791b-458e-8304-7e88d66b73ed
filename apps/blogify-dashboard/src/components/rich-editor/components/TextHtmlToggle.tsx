import { cn } from '@ps/ui/lib/utils';

const TextHtmlToggle = ({ isHtml, toggleHtml }: { isHtml: boolean; toggleHtml: () => void }) => {
  const states = ['TEXT', 'HTML'];

  return (
    <div
      className="flex cursor-pointer items-center justify-center rounded-lg bg-gray10 p-0.5 text-gray9"
      onClick={toggleHtml}
    >
      {states.map((state) => (
        <span
          key={state}
          className={cn('rounded-lg px-1 !text-xxs lg:px-2 lg:py-[2px]', {
            'bg-white text-[#f2470d]':
              (!isHtml && state === 'TEXT') || (isHtml && state === 'HTML'),
          })}
        >
          {state}
        </span>
      ))}
    </div>
  );
};

export default TextHtmlToggle;
