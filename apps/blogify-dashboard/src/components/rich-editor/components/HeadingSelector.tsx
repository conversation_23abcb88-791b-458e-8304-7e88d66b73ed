import type { Editor } from '@tiptap/react';

import { SelectContent, SelectTrigger, SelectItem, Select } from '@ps/ui/components/select';

type Level = 1 | 2 | 3 | 4 | 5 | 6;

const OPTIONS = [
  { label: 'Heading 1', value: 1, className: 'text-3xl' },
  { label: 'Heading 2', value: 2, className: 'text-2xl' },
  { label: 'Heading 3', value: 3, className: 'text-xl' },
  { label: 'Heading 4', value: 4, className: 'text-lg' },
  { label: 'Heading 5', value: 5, className: 'text-md' },
  { label: 'Heading 6', value: 6, className: 'text-sm' },
  { label: 'Paragraph', value: 0, className: 'text-base' },
];

const HeadingSelector = ({ editor }: { editor: Editor }) => {
  const toggle = (level: Level | 0) => {
    if ((level as number) === 0) {
      editor
        .chain()
        .focus()
        .toggleHeading({ level: getSelectedOption() as Level })
        .run();
    } else {
      editor
        .chain()
        .focus()
        .toggleHeading({ level: level as Level })
        .run();
    }
  };

  const getSelectedOption = (): Level | 0 =>
    ([1, 2, 3, 4, 5, 6] as Level[]).find((level) => editor.isActive('heading', { level })) || 0;

  return (
    <Select
      onValueChange={(v) => toggle(parseInt(v, 10) as Level)}
      value={String(getSelectedOption())}
      defaultValue="0"
    >
      <SelectTrigger
        className="h-5 border-none bg-transparent !text-11 uppercase outline-none"
        aria-label="Font Style"
      >
        {OPTIONS.find((o) => o.value === getSelectedOption())?.label}
      </SelectTrigger>

      <SelectContent>
        {OPTIONS.map(({ label, value, className }, i) => (
          <SelectItem className={`my-1 ${className}`} value={String(value)} key={i}>
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default HeadingSelector;
