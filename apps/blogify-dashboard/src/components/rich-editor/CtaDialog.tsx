import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import { Input } from '@ps/ui/components/input';
import { useState, useEffect } from 'react';
import { BsLink45Deg } from 'react-icons/bs';
import { CgMathMinus, CgMathPlus } from 'react-icons/cg';
import { IoColorPalette } from 'react-icons/io5';
import { TbBorderCorners } from 'react-icons/tb';
import { LuSquareMousePointer } from 'react-icons/lu';
import { Editor } from '@tiptap/react';

const initialState = {
  label: '',
  color: '000000',
  borderRadius: 10,
  href: '',
};

type PropsType = {
  isOpen: boolean;
  onClose: () => void;
  editor: Editor;
  updatedData?: {
    label: string;
    color: string;
    borderRadius: number;
    href: string;
  };
};

const CtaDialog = ({ isOpen, onClose, editor, updatedData }: PropsType) => {
  const [data, setData] = useState(updatedData ? updatedData : initialState);

  useEffect(() => {
    if (updatedData) {
      setData(updatedData);
    }
  }, [updatedData]);

  const handleRadiusChange = (action: 'minus' | 'plus') => {
    setData((prevData) => ({
      ...prevData,
      borderRadius:
        action === 'minus'
          ? Math.max(prevData.borderRadius - 2, 0)
          : Math.min(prevData.borderRadius + 2, 50),
    }));
  };

  const handleSubmit = () => {
    if (!editor) return;
    if (updatedData) {
      editor
        .chain()
        .focus()
        .updateAttributes('ctaButton', {
          label: data.label || 'Click Here',
          href: data.href,
          color: data.color,
          borderRadius: data.borderRadius,
        })
        .run();
    } else {
      editor
        .chain()
        .focus()
        .insertContent({
          type: 'ctaButton',
          attrs: {
            label: data.label || 'Click Here',
            href: data.href,
            color: data.color,
            borderRadius: data.borderRadius,
          },
        })
        .run();
    }

    handleClose();
  };

  const handleClose = () => {
    onClose();
    if (updatedData) {
      setData(updatedData);
    } else {
      setData(initialState);
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={handleClose}
      title={updatedData ? 'Update CTA' : 'Add CTA'}
      description={
        updatedData
          ? 'Update your call to action button information.'
          : `Add a call to action button in your blog.`
      }
      PrimaryIcon={LuSquareMousePointer}
      primaryButton={{
        label: updatedData ? 'Update CTA' : 'Add CTA',
        onClick: handleSubmit,
        className: 'mt-5',
        disabled: !data.href || !data.label,
      }}
    >
      <div className="space-y-7">
        <div className="space-y-1.5">
          <label className="block text-sm font-medium text-gray-700">Title</label>
          <div className="relative">
            <Input
              value={data.label}
              onChange={(e) => setData({ ...data, label: e.target.value })}
              placeholder="eg. Click here to buy"
            />
          </div>
        </div>

        <div className="space-y-1.5">
          <label className="block text-sm font-medium text-gray-700">Color</label>
          <div className="relative">
            <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
              <IoColorPalette className="text-gray-500" />
            </div>
            <div className="absolute right-0 top-0 flex h-full items-center px-3">
              <input
                type="color"
                value={`#${data.color}`}
                onChange={(e) => setData({ ...data, color: e.target.value.replace('#', '') })}
                className="size-6 cursor-pointer rounded p-0"
              />
            </div>
            <Input
              value={data.color}
              onChange={(e) => setData({ ...data, color: e.target.value.replace('#', '') })}
              placeholder="Color code (e.g., FF0000)"
              className="pl-12 pr-10 uppercase"
            />
          </div>
        </div>

        <div className="space-y-1.5">
          <label className="block text-sm font-medium text-gray-700">Corner Radius</label>
          <div className="relative">
            <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
              <TbBorderCorners className="text-gray-500" />
            </div>
            <div className="absolute right-0 top-0 flex h-full items-center space-x-2 px-3">
              <CgMathMinus
                className="size-6 cursor-pointer rounded border border-[#e3dbd9] p-[2px] shadow active:border-gray12"
                onClick={() => handleRadiusChange('minus')}
                style={{ opacity: data.borderRadius === 0 ? 0.5 : 1 }}
              />
              <CgMathPlus
                className="size-6 cursor-pointer rounded border border-[#e3dbd9] p-[2px] shadow active:border-gray12"
                onClick={() => handleRadiusChange('plus')}
                style={{ opacity: data.borderRadius === 50 ? 0.5 : 1 }}
              />
            </div>
            <Input
              value={`${data.borderRadius}px`}
              placeholder="Radius"
              className="pl-12 pr-20"
              readOnly
            />
          </div>
        </div>

        <div className="space-y-1.5">
          <label className="block text-sm font-medium text-gray-700">URL</label>
          <div className="relative">
            <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
              <BsLink45Deg className="text-gray-500" />
            </div>
            <Input
              value={data.href}
              onChange={(e) => setData({ ...data, href: e.target.value })}
              placeholder="Enter URL (e.g., https://example.com)"
              className="pl-12"
            />
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default CtaDialog;
