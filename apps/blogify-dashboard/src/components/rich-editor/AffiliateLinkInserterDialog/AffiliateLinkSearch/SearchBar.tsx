import { FaSearch } from 'react-icons/fa';
import React from 'react';

import { Button } from '@ps/ui/components/button';

interface SearchBarProps {
  defaultValue: string;
  onChange: (value: string) => void;
  onSearch: () => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ defaultValue, onChange, onSearch }) => (
  <section className="flex w-full items-start justify-between rounded-lg border border-gray10">
    <div className="flex w-full items-center divide-x-2">
      <FaSearch className="m-2 text-primary" />
      <input
        type="text"
        inputMode="search"
        required
        minLength={1}
        maxLength={100}
        defaultValue={defaultValue}
        onChange={({ target: { value } }) => onChange(value)}
        className="w-full p-2 font-figtree text-sm"
      />
    </div>
    <Button type="button" onClick={onSearch}>
      Search
    </Button>
  </section>
);

export default SearchBar;
