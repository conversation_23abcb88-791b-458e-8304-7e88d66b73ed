/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useReducer } from 'react';

import { TabsContent, TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { API } from '@/services/api';

import SearchResults, { Product } from './SearchResults';
import RegionMultiInput from './RegionSelector';
import SearchBar from './SearchBar';
import StateHandler from '@/components/misc/StateHandler';

const TABS = [
  { name: 'Blogify Links', value: 'blogify' },
  { name: 'My Links', value: 'personal' },
];

const AffiliateLinkSearch: React.FC<{ query: string; onClose: () => void }> = ({
  query: text,
  onClose,
}) => {
  const [currentTab, dispatchTabChange] = useReducer((_, action) => action, 'blogify');

  return (
    <div className="space-y-4">
      <Tabs
        defaultValue="blogify"
        className="space-y-4"
        onValueChange={(value) => dispatchTabChange(value)}
      >
        <TabsList variant="nav">
          {TABS.map((tab) => (
            <TabsTrigger variant="nav" key={tab.value} value={tab.value}>
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {TABS.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            <AffiliateSearchForm
              text={currentTab === 'blogify' ? text : ''}
              currentTab={currentTab}
              onClose={onClose}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

const AffiliateSearchForm: React.FC<{
  text: string;
  currentTab: string;
  onClose: () => void;
}> = ({ text, currentTab, onClose }) => {
  const [state, dispatch] = useReducer(
    (
      oldState: {
        readonly query: string;
        readonly regions: string[];
        readonly products: Product[];
        readonly loading: boolean;
      },
      action:
        | { type: 'setRegion'; regions: string[] }
        | { type: 'setProducts'; products: Product[] }
        | { type: 'setQuery'; query: string }
        | { type: 'setLoading'; loading: boolean }
    ) => {
      switch (action.type) {
        case 'setRegion':
          return { ...oldState, regions: action.regions };
        case 'setProducts':
          return { ...oldState, products: action.products };
        case 'setQuery':
          return { ...oldState, query: action.query };
        case 'setLoading':
          return { ...oldState, loading: action.loading };
        default:
          return oldState;
      }
    },
    {
      query: text,
      regions: [] as string[],
      products: [] as Product[],
      loading: false,
    } as const
  );

  const handleSearch = async () => {
    dispatch({ type: 'setLoading', loading: true });
    await API.fetch<Product[]>(
      'affiliate/products?' +
        new URLSearchParams({
          query: state.query.trim(),
          ...(state.regions.length && { regions: state.regions.join(',') }),
          ...(currentTab === 'personal' && { source: 'personal' }),
        }).toString()
    )
      .then((products) => dispatch({ type: 'setProducts', products: products ?? [] }))
      .finally(() => dispatch({ type: 'setLoading', loading: false }));
  };

  useEffect(() => {
    handleSearch();
  }, []);

  return (
    <div className="space-y-4">
      <SearchBar
        defaultValue={text}
        onChange={(value) => dispatch({ type: 'setQuery', query: value })}
        onSearch={handleSearch}
      />
      {currentTab === 'blogify' && (
        <RegionMultiInput
          onChange={(values) =>
            dispatch({ type: 'setRegion', regions: values.map((v) => v.value) })
          }
        />
      )}

      {state.loading || !!!state.products.length ? (
        <StateHandler
          loading={state.loading}
          isEmpty={state.products.length === 0}
          emptyMsg="No links available"
          className="min-h-[20vh]"
        />
      ) : (
        <SearchResults products={state.products} onClose={onClose} />
      )}
    </div>
  );
};

export default AffiliateLinkSearch;
