import React, { useContext } from 'react';
// import { DialogContext } from '@/context/DialogContext';
import { EditorAccessContext } from '@/context/EditorAccessContext';
import { generateTrackingLink } from '@/utils/affiliate';
import { Link } from 'react-router-dom';
import { FaArrowDown } from 'react-icons/fa';
import { TbExternalLink } from 'react-icons/tb';
import { ImLink } from 'react-icons/im';

export type Product = {
  name: string;
  image: string;
  category: string;
  link: string;
};

const SearchResults: React.FC<{ products: Product[]; onClose: () => void }> = ({
  products,
  onClose,
}) => {
  const { editor, blog } = useContext(EditorAccessContext);
  // const { onClose } = useContext(DialogContext);

  const openLinkInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener noreferrer');
  };

  return (
    <div className="overflow-x-auto">
      <span className="text-xs font-semibold uppercase text-gray9">
        Search Result ({products.length})
      </span>
      <table className="w-full min-w-[600px] table-auto text-sm text-gray-700">
        <thead className="hidden h-6 bg-bg2">
          <tr className="text-left text-xs uppercase text-gray9 *:p-2">
            <th className="px-2 font-medium">Product</th>
            <th className="px-2 font-medium">Category</th>
            <th className="px-2 text-right font-medium">Link</th>
            <th className="px-2 text-right font-medium">Actions</th>
          </tr>
        </thead>
        <tbody>
          {products.map((product, index) => (
            <tr key={product.name + index} className="hover:bg-gray-50">
              <td className="mt-2 flex gap-2 p-2">
                <Link to={product.link} target="_blank" rel="noopener noreferrer">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="size-5 rounded-lg object-cover"
                  />
                </Link>
                <span className="max-w-[150px] truncate font-semibold">{product.name}</span>
              </td>
              <td className="max-w-[150px] truncate p-2 text-gray9">{product.category}</td>
              <td className="max-w-[150px] truncate p-2">{product.link}</td>

              <td className="p-2 text-right">
                <div className="flex items-center gap-2">
                  <div
                    className="cursor-pointer rounded-lg border p-1.5 text-gray9"
                    onClick={() => openLinkInNewTab(product.link)}
                  >
                    <TbExternalLink />
                  </div>
                  <button
                    className="flex items-center gap-2 rounded-lg border p-1 px-2 uppercase text-gray9 "
                    type="button"
                    onClick={() => {
                      const href = generateTrackingLink(blog!.bid, blog!._id, product.link);
                      const isImage = editor?.isActive('image');

                      if (isImage && editor) {
                        const imageAttrs = editor.getAttributes('image');

                        editor
                          .chain()
                          .deleteSelection()
                          .insertContent({
                            type: 'imageWithLink',
                            attrs: {
                              ...imageAttrs,
                              href,
                            },
                          })
                          .run();
                      } else {
                        editor!.chain().setLink({ href }).run();
                      }

                      onClose();
                    }}
                  >
                    <div className="relative text-gray9">
                      <ImLink />

                      <FaArrowDown className="absolute left-2 top-2 size-2 font-bold" />
                    </div>
                    Insert
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SearchResults;
