import { FaMapMarkerAlt } from 'react-icons/fa';
import makeAnimated from 'react-select/animated';
import Select from 'react-select';

import useCountryStateCity from '@/hooks/useCountryStateCity';

const RegionMultiInput: React.FC<{
  onChange: (values: Readonly<Record<'value' | 'label', string>[]>) => void;
}> = ({ onChange }) => {
  const { countries } = useCountryStateCity();

  const countryOptions = countries.map((country) => ({
    label: country.name,
    value: country.iso2,
  }));

  return (
    <Select
      className="w-full"
      options={countryOptions}
      isMulti
      onChange={onChange as any}
      components={{ ...makeAnimated() }}
      placeholder="Select regions..."
      styles={{
        multiValue: (base: any) => ({
          ...base,
          backgroundColor: '#f8f7f6',
        }),
        multiValueLabel: (base: any) => ({
          ...base,
          color: '#93766c',
        }),
      }}
    />
  );
};

const RegionSelector: React.FC<React.ComponentPropsWithoutRef<typeof RegionMultiInput>> = ({
  onChange,
}) => (
  <section className="flex items-center rounded-lg border border-gray10">
    <FaMapMarkerAlt className="m-2 text-primary" />
    <RegionMultiInput onChange={onChange} />
  </section>
);

export default RegionSelector;
