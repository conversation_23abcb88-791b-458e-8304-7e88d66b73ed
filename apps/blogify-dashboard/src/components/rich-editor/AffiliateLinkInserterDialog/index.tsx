// بسم الله الرحمن الرحيم
import { FaArrowDown } from 'react-icons/fa';
import { ImLink } from 'react-icons/im';

import Dialog from '@ps/ui/components/dialog';

import AffiliateLinkSearch from './AffiliateLinkSearch';

const AffiliateLinkInserterDialog = ({
  text,
  onClose,
  isOpen,
}: {
  text: string;
  onClose: () => void;
  isOpen: boolean;
}) => (
  <Dialog
    open={isOpen}
    onOpenChange={onClose}
    Icon={() => (
      <div className="relative mb-4">
        <ImLink className="size-10 text-primary" />
        <FaArrowDown className="absolute left-6 top-7 font-bold text-primary" />
      </div>
    )}
    title="Insert Affiliate Link"
    description="Find & insert an affiliate from link library."
    className="min-w-fit"
  >
    <AffiliateLinkSearch query={text} onClose={onClose} />
  </Dialog>
);

export default AffiliateLinkInserterDialog;
