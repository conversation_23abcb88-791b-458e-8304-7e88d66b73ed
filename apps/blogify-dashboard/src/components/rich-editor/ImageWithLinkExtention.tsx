import { Node } from '@tiptap/core';

export const ImageWithLinkExtension = Node.create({
  name: 'imageWithLink',
  group: 'block',
  atom: true,

  addAttributes() {
    return {
      src: { default: null },
      alt: { default: null },
      title: { default: null },
      href: { default: null },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'a[href] > img',
        getAttrs: (dom) => {
          const a = dom;
          const img = dom.querySelector('img');
          return {
            href: a.getAttribute('href'),
            src: img ? img.getAttribute('src') : null,
            alt: img ? img.getAttribute('alt') : null,
            title: img ? img.getAttribute('title') : null,
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'a',
      { href: HTMLAttributes.href, target: '_blank' },
      ['img', { src: HTMLAttributes.src, alt: HTMLAttributes.alt, title: HTMLAttributes.title }],
    ];
  },
});
