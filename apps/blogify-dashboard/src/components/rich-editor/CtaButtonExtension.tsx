import { cn } from '@ps/ui/lib/utils';
import { mergeAttributes, Node } from '@tiptap/core';
import { NodeViewWrapper, ReactNodeViewRenderer, NodeViewProps } from '@tiptap/react';

export const CTAButton = ({
  node: {
    attrs: { href, label, color, borderRadius },
  },
}: NodeViewProps) => {
  const handleClick = (e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) return;
    e.preventDefault();
  };

  return (
    <NodeViewWrapper>
      <div
        contentEditable={false}
        data-type="cta-button"
        className={cn('flex items-center justify-center')}
      >
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block no-underline"
          onClick={handleClick}
          style={{
            backgroundColor: color.startsWith('#') ? color : `#${color}`,
            borderRadius: `${borderRadius}px`,
            color: '#ffffff',
            padding: '10px 20px',
            margin: '10px 0',
            transition: 'opacity 0.2s',
          }}
        >
          {label}
        </a>
      </div>
    </NodeViewWrapper>
  );
};

export const CTAExtension = Node.create({
  name: 'ctaButton',
  group: 'block',
  atom: true,
  inline: false,
  content: '',

  addAttributes() {
    return {
      label: {
        default: 'Click Here',
      },
      href: {
        default: '#',
      },
      color: {
        default: '000000',
        parseHTML: (element) => {
          const link = element.querySelector('a');
          if (!link) return '000000';
          const style = link.getAttribute('style');
          if (!style) return '000000';
          const match = style.match(/background-color:\s*(#[a-f0-9]{6})/i);
          return match ? match[1].replace('#', '') : '000000';
        },
      },
      borderRadius: {
        default: 10,
        parseHTML: (element) => {
          const link = element.querySelector('a');
          if (!link) return 10;
          const style = link.getAttribute('style');
          if (!style) return 10;
          const match = style.match(/border-radius:\s*(\d+)px/);
          return match ? parseInt(match[1]) : 10;
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="cta-button"]',
        getAttrs: (element) => {
          if (!(element instanceof HTMLElement)) {
            return false;
          }

          const link = element.querySelector('a');
          if (!link) {
            return false;
          }

          const style = link.getAttribute('style') || '';
          const colorMatch = style.match(/background-color:\s*(#[a-f0-9]{6})/i);
          const borderRadiusMatch = style.match(/border-radius:\s*(\d+)px/);

          return {
            href: link.getAttribute('href') || '#',
            label: link.textContent || 'Click Here',
            color: colorMatch ? colorMatch[1].replace('#', '') : '000000',
            borderRadius: borderRadiusMatch ? parseInt(borderRadiusMatch[1]) : 10,
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    const { href, label, color, borderRadius, ...rest } = HTMLAttributes;

    return [
      'div',
      mergeAttributes({ 'data-type': 'cta-button' }, rest),
      [
        'a',
        {
          href,
          target: '_blank',
          rel: 'noopener noreferrer',
          style: `background-color: #${color}; border-radius: ${borderRadius}px; color: #ffffff; padding: 10px 20px; margin: 10px 0;`,
        },
        label || 'Click Here',
      ],
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(CTAButton);
  },
});
