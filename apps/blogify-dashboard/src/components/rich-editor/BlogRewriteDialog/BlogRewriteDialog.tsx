// بسم الله الرحمن الرحيم
import { useReducer } from 'react';
import { GiQuill } from 'react-icons/gi';

import Dialog from '@ps/ui/components/dialog';

import { ExpandableTextBlock } from './components/ExpandableTextBlock';
import { IconType } from 'react-icons';
import { FaExpandArrowsAlt } from 'react-icons/fa';

import { TextLimitExceeded } from './components/TextLimitExceeded';
import { RewriteForm } from './components/RewriteForm';
import { RewriteOutput } from './components/RewriteOutput';

export type BlogRewriteState = {
  status: 'input' | 'output';
  output?: string;
};

export type BlogRewriteAction = { type: 'SET_OUTPUT'; payload: string };
interface BlogRewriteDialogProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  selectedText: string;
  title: string;
  description: string;
  icon: IconType;
  mode: 'extend' | 'rephrase';
}

function blogRewriteReducer(state: BlogRewriteState, action: BlogRewriteAction): BlogRewriteState {
  switch (action.type) {
    case 'SET_OUTPUT':
      return { status: 'output', output: action.payload };
    default:
      return state;
  }
}

function BlogRewriteDialog({
  isOpen,
  onClose,
  selectedText,
  title,
  description,
  icon: Icon,
  mode,
}: BlogRewriteDialogProps) {
  const [state, dispatch] = useReducer(blogRewriteReducer, {
    status: 'input',
  });

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={Icon}
      title={title}
      description={description}
    >
      <div className="max-w-md space-y-5">
        <ExpandableTextBlock text={selectedText} />
        {selectedText.length > 1000 ? (
          <TextLimitExceeded />
        ) : state.status === 'input' ? (
          <RewriteForm mode={mode} selectedText={selectedText} dispatch={dispatch} />
        ) : state.status === 'output' && state.output ? (
          <RewriteOutput output={state.output} />
        ) : null}
      </div>
    </Dialog>
  );
}

export function BlogRephraseDialog({
  isOpen,
  onClose,
  selectedText,
}: Omit<BlogRewriteDialogProps, 'title' | 'description' | 'icon' | 'mode'>) {
  return (
    <BlogRewriteDialog
      isOpen={isOpen}
      onClose={onClose}
      selectedText={selectedText}
      title="Rephrase"
      description="Rephrase the selected text."
      icon={GiQuill}
      mode="rephrase"
    />
  );
}

export function BlogExtendDialog({
  isOpen,
  onClose,
  selectedText,
}: Omit<BlogRewriteDialogProps, 'title' | 'description' | 'icon' | 'mode'>) {
  return (
    <BlogRewriteDialog
      isOpen={isOpen}
      onClose={onClose}
      selectedText={selectedText}
      title="Extend"
      description="Extend the selected text."
      icon={FaExpandArrowsAlt}
      mode="extend"
    />
  );
}
