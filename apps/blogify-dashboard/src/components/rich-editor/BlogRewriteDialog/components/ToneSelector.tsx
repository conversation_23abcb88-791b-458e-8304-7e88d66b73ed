import Select from 'react-select';
import { BLOG_TONES } from '@/constants/blog';

type ToneSelectorProps = {
  value: string;
  onChange: (value: string) => void;
};

export function ToneSelector({ value, onChange }: ToneSelectorProps) {
  return (
    <section className="space-y-2">
      <label className="block text-sm font-medium">Blog Tone</label>
      <Select
        value={{ value, label: value }}
        options={BLOG_TONES.map((tone) => ({
          value: tone,
          label: tone,
        }))}
        onChange={(option) => onChange(option?.value ?? BLOG_TONES[0])}
        classNames={{
          control: () => 'border-2 rounded py-1',
        }}
      />
    </section>
  );
}
