// بسم الله الرحمن الرحيم

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ps/ui/components/accordion';
import { BsTextareaT } from 'react-icons/bs';

export function ExpandableTextBlock({ text }: { text: string }) {
  return (
    <Accordion collapsible type="single" className="rounded-lg bg-slate-100">
      <AccordionItem value="content">
        <AccordionTrigger className="px-4 py-2 hover:no-underline">
          <div className="m-1 flex w-full items-center justify-between gap-3">
            <BsTextareaT />
            <span className="text-sm">Selected Text</span>
            <span className="text-xs">{text.length}/1000</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4">
          <span className="text-pretty">{text}</span>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
