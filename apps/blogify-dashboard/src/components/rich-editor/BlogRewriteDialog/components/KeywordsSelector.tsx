// بسم الله الرحمن الرحيم

import Keyword from '@/components/seo/components/elements/Keyword';
import { KeywordCategory } from '@/components/seo/components/SEOTags/contexts/SEOTagContext';
import { EditorAccessContext } from '@/context/EditorAccessContext';
import { EmptySEOAnalysis, SEOAnalysis } from '@/types/components/seo';
import { ComponentProps, use, useMemo } from 'react';
import { useQuery } from 'react-query';
import Select from 'react-select';

const useKeywords = (blogID: string) => {
  const { data = EmptySEOAnalysis } = useQuery<SEOAnalysis>(`blogs/seo/${blogID}`);
  const keywords = useMemo(
    () =>
      Object.entries(data.content).map(
        ([word, [count, minimum, maximum]]) =>
          ({
            word,
            count,
            minimum,
            maximum,
          }) as const
      ),
    [data]
  );
  return keywords;
};

type KeywordsSelectorProps = {
  onChange: (keywords: string[]) => void;
};
export function KeywordsSelector({ onChange }: KeywordsSelectorProps) {
  const { blog } = use(EditorAccessContext);
  const keywords = useKeywords(blog!._id);
  return <KeywordsSelect keywords={keywords} label="content" onChange={onChange} />;
}

type KeywordsSelectProps = {
  onChange: (keywords: string[]) => void;
  keywords: ComponentProps<typeof Keyword>[];
  label: KeywordCategory;
};
function KeywordsSelect({ onChange, keywords, label }: KeywordsSelectProps) {
  return (
    <section className="space-y-2">
      <label className="font-medium">Keywords</label>
      <Select
        onChange={(options) => {
          onChange(options ? options.map((option) => option.value) : []);
        }}
        placeholder="Select SEO keywords"
        isMulti
        isClearable
        options={keywords.map((keyword) => ({
          value: keyword.word,
          label: <Keyword label={label} {...keyword} />,
        }))}
        styles={{
          multiValue: (base) => ({
            ...base,
            border: 'solid 1px orange',
            backgroundColor: 'transparent',
          }),
          multiValueLabel: (base) => ({
            ...base,
            backgroundColor: 'transparent',
          }),
        }}
      />
    </section>
  );
}
