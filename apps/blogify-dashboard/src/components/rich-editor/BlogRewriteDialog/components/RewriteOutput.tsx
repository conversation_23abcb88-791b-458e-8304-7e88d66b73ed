import { useContext } from 'react';
import { Button } from '@ps/ui/components/button';
import { DialogClose } from '@ps/ui/components/dialog';
import { EditorAccessContext } from '@/context/EditorAccessContext';

interface BlogRewriteOutputProps {
  output: string;
}

export function RewriteOutput({ output }: BlogRewriteOutputProps) {
  const { editor } = useContext(EditorAccessContext);

  const handleInsert = () => {
    if (editor && output) {
      editor.chain().focus().deleteSelection().insertContent(output).run();
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <span className="mb-2 block font-medium">Output:</span>
        <span className="block text-pretty rounded border-2 border-bg2 p-3">{output}</span>
      </div>
      <DialogClose asChild>
        <Button onClick={handleInsert}>Insert</Button>
      </DialogClose>
    </div>
  );
}
