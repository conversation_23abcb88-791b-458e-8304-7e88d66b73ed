import { memo } from 'react';
import { <PERSON><PERSON> } from '@ps/ui/components/button';
import { DialogClose } from '@ps/ui/components/dialog';

export const TextLimitExceeded = memo(function TextLimitExceeded() {
  return (
    <section className="flex flex-col gap-3">
      <span className="text-md font-medium text-red">
        Character limit exceeded! Maximum allowed character limit is 1000. You can close this dialog
        and select a smaller range.
      </span>
      <DialogClose asChild>
        <Button type="button" className="w-full rounded border-2">
          Close
        </Button>
      </DialogClose>
    </section>
  );
});
