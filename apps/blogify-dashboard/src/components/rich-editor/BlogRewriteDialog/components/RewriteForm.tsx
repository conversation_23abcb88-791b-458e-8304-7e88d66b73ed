import { useContext } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@ps/ui';
import { Button } from '@ps/ui/components/button';
import { EditorAccessContext } from '@/context/EditorAccessContext';
import { API } from '@/services/api';
import toast from 'react-hot-toast';
import { ToneSelector } from './ToneSelector';
import { KeywordsSelector } from './KeywordsSelector';
import { BLOG_TONES } from '@/constants/blog';
import { z } from '@ps/ui';
import { BlogRewriteAction } from '../BlogRewriteDialog';

interface BlogRewriteFormProps {
  mode: 'extend' | 'rephrase';
  selectedText: string;
  dispatch: React.Dispatch<BlogRewriteAction>;
}

const formSchema = z.object({
  keywords: z.array(z.string()),
  tone: z.enum(BLOG_TONES as [string, ...string[]]),
  text: z.string().max(1000),
  mode: z.enum(['extend', 'rephrase']),
});

export type FormData = z.infer<typeof formSchema>;

export function RewriteForm({ mode, selectedText, dispatch }: BlogRewriteFormProps) {
  const { blog } = useContext(EditorAccessContext);
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      keywords: [],
      tone: blog?.blogTone ?? BLOG_TONES[0],
      text: selectedText,
      mode,
    },
  });

  const onSubmit = (data: FormData) => {
    toast.promise(
      API.post<{ content: string }>('/blog-generation/rewrite', data)
        .then((response) => {
          if (!response?.content) {
            throw new Error('No content received from the server');
          }
          dispatch({ type: 'SET_OUTPUT', payload: response.content });
        })
        .catch((error) => {
          console.log('Error in rewriting blog content:', error);
        }),
      {
        loading: 'Generating content...',
        success: 'Content generated successfully!',
        error: 'Error generating content!',
      }
    );
  };

  return (
    <div className="space-y-3">
      <form className="flex flex-col gap-2">
        <ToneSelector
          value={form.watch('tone')}
          onChange={(value) => form.setValue('tone', value)}
        />
        <KeywordsSelector onChange={(keywords) => form.setValue('keywords', keywords)} />
      </form>
      <Button
        type="button"
        className="w-full capitalize"
        onClick={() => form.handleSubmit(onSubmit)()}
      >
        {mode}
      </Button>
    </div>
  );
}
