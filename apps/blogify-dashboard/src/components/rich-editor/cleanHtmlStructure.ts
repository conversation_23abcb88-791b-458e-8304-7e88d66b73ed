export const cleanHtmlStructure = (html: string): string => {
  if (!html) return '';

  try {
    // Create a DOM parser
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Find all paragraph tags
    const paragraphs = doc.querySelectorAll('p');

    // Check each paragraph for invalid nested block elements
    paragraphs.forEach((p) => {
      const blockElements = p.querySelectorAll(
        'blockquote, h1, h2, h3, h4, h5, h6, div, ul, ol, table'
      );

      if (blockElements.length > 0) {
        // For each block element found inside paragraph
        blockElements.forEach((block) => {
          // Extract block element and insert after paragraph
          if (p.parentNode) {
            // Get text content before the block
            let beforeText = '';
            let currentNode = block.previousSibling;

            while (currentNode) {
              if (
                currentNode.nodeType === 3 ||
                currentNode.nodeName.toLowerCase() !== 'blockquote'
              ) {
                beforeText = currentNode.textContent + beforeText;
                const prevNode = currentNode.previousSibling;
                p.removeChild(currentNode);
                currentNode = prevNode;
              } else {
                currentNode = currentNode.previousSibling;
              }
            }

            // Get text content after the block
            let afterText = '';
            currentNode = block.nextSibling;

            while (currentNode) {
              if (
                currentNode.nodeType === 3 ||
                currentNode.nodeName.toLowerCase() !== 'blockquote'
              ) {
                afterText += currentNode.textContent;
                const nextNode = currentNode.nextSibling;
                p.removeChild(currentNode);
                currentNode = nextNode;
              } else {
                currentNode = currentNode.nextSibling;
              }
            }

            // Create a new paragraph for text before block if needed
            if (beforeText.trim()) {
              const beforeP = doc.createElement('p');
              beforeP.textContent = beforeText.trim();
              p.parentNode.insertBefore(beforeP, p);
            }

            // Move the block element after the current paragraph
            p.parentNode.insertBefore(block, p.nextSibling);

            // Create a new paragraph for text after block if needed
            if (afterText.trim()) {
              const afterP = doc.createElement('p');
              afterP.textContent = afterText.trim();
              p.parentNode.insertBefore(afterP, block.nextSibling);
            }

            // If paragraph is now empty, remove it
            if ((!p.textContent || !p.textContent.trim()) && p.children.length === 0) {
              p.parentNode.removeChild(p);
            }
          }
        });
      }
    });

    return doc.body.innerHTML;
  } catch (error) {
    console.error('Error cleaning HTML structure:', error);
    return html;
  }
};
