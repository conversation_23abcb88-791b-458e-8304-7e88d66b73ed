import { InitialUserTourGuideProvider } from '@/context/InitialUserTourGuide';
import { WritingSnippetsTourGuideProvider } from '@/context/WritingSnippetsTourGuide';
import { YoutubeTourGuideProvider } from '@/context/YoutubeTourGuide';

export default function TourGuideProviders({ children }: { children: React.ReactNode }) {
  return (
    <InitialUserTourGuideProvider>
      <WritingSnippetsTourGuideProvider>
        <YoutubeTourGuideProvider>{children}</YoutubeTourGuideProvider>
      </WritingSnippetsTourGuideProvider>
    </InitialUserTourGuideProvider>
  );
}
