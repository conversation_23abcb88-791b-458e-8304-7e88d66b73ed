import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import FormField from '@ps/ui/form/FormField';
import Dialog from '@ps/ui/components/dialog';

export default function DeactivateFeedbackDialog({
  description,
  loading,
  error,
  open,
  onSubmit,
  onOpenChange,
}: {
  description: string;
  loading: boolean;
  error: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (comment: string) => void;
}) {
  const [comment, setComment] = useState<string>('');

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      Icon={() => null}
      title="Help us improve!"
      description={description}
      actions={
        <Button className="mt-10 w-full" loading={loading} onClick={() => onSubmit(comment)}>
          Submit
        </Button>
      }
    >
      <FormField
        type="textarea"
        name="comment"
        placeholder="Tell us more "
        label="Tell us more..."
        onChange={(ev) => setComment(ev.target.value)}
        error={error}
      />
    </Dialog>
  );
}
