import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';
import toast from 'react-hot-toast';

import { useStoreActions, useStoreState } from '@/store';
import { API } from '@/services/api';

import ConfirmationDialog from './ConfirmationDialog';

const FreeTrialDialog = ({
  isOpen,
  onClose,
  toggleUpgrading,
}: {
  isOpen: boolean;
  onClose: () => void;
  toggleUpgrading: () => void;
}) => {
  const [saving, toggleSaving] = useToggle(false);
  const fetchUser = useStoreActions((a) => a.user.fetch);
  const user = useStoreState((s) => s.user.current);
  const navigate = useNavigate();

  const isFreePlan = user.subscriptionPlan === 'FREE';

  const upgradeSubscription = () => {
    toggleSaving();
    toggleUpgrading();
    API.put(`payments/end-trial-and-upgrade`)
      .then(() => {
        onClose();
        fetchUser();
      })
      .catch((e) => {
        onClose();
        toast.error(e?.error || e?.message || e);
      })
      .finally(() => {
        toggleUpgrading();
        toggleSaving();
      });
  };

  return (
    <ConfirmationDialog
      isOpen={isOpen}
      confirmationTitle={isFreePlan ? 'Upgrade to Paid Plan' : 'End Trial and Upgrade'}
      confirmationMessage={`Ready to unlock the full potential of Blogify? Upgrade ${isFreePlan ? 'to a paid plan' : 'your trial now'} to access all features and maximize your blogging experience.${isFreePlan ? '' : ' Please be aware that your payment method will be charged immediately upon upgrading.'}`}
      confirmText="Upgrade"
      cancelText="Later"
      onClose={onClose}
      confirming={saving}
      onConfirm={isFreePlan ? () => navigate('/dashboard/subscription') : upgradeSubscription}
    />
  );
};

export default FreeTrialDialog;
