import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/dialog';
import Link from '@/components/common/Link';

const ConfirmationDialog = ({
  isOpen,
  confirmationTitle = 'Confirmation',
  confirmationMessage = 'Are you sure you want to perform this action?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmRedirect,
  confirming,
  error,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  confirmationTitle?: string;
  confirmationMessage?: string;
  confirmText?: string;
  cancelText?: string;
  confirming?: boolean;
  confirmRedirect?: string;
  error?: string;
  onClose: () => void;
  onConfirm?: () => void;
}) => (
  <Dialog
    open={isOpen}
    onOpenChange={onClose}
    Icon={() => null}
    title={confirmationTitle}
    description={confirmationMessage}
    error={error}
    actions={
      <>
        <Button variant="secondary" onClick={onClose}>
          {cancelText}
        </Button>
        {confirmRedirect ? (
          <Link ignoreExternal to={confirmRedirect}>
            <Button className="bg-red2">{confirmText}</Button>
          </Link>
        ) : !!onConfirm ? (
          <Button className="min-w-24 bg-red2" loading={confirming} onClick={onConfirm}>
            {confirmText}
          </Button>
        ) : null}
      </>
    }
  />
);

export default ConfirmationDialog;
