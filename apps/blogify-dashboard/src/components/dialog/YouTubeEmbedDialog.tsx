import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { VisuallyHidden } from '@ps/ui';

export default function YouTubeEmbedDialog({
  url,
  title,
  children,
}: {
  url: string;
  title: string;
  children: React.ReactNode;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent className="max-w-7xl border-none p-0">
        <VisuallyHidden>
          <DialogHeader>
            <DialogTitle>YouTube Video</DialogTitle>
            <DialogDescription>{title}</DialogDescription>
          </DialogHeader>
        </VisuallyHidden>

        <iframe
          className="aspect-video"
          allow="autoplay; encrypted-media"
          allowFullScreen
          title={title}
          width="100%"
          src={url}
        ></iframe>
      </DialogContent>
    </Dialog>
  );
}
