// بسم الله الرحمن الرحيم

import { Gauge, gaugeClasses, useGaugeState } from '@mui/x-charts';

function GaugePointer({
  circleOffsetX = 0,
  circleOffsetY = 0,
  pointerLengthTop = 1,
  pointerLengthBottom = 1,
}: {
  circleOffsetX?: number;
  circleOffsetY?: number;
  pointerLengthTop?: number;
  pointerLengthBottom?: number;
}) {
  const { valueAngle, outerRadius, cx, cy } = useGaugeState();

  if (valueAngle === null) {
    return null;
  }

  // Calculate target positions for top and bottom pointer ends
  const topTarget = {
    x: cx + outerRadius * pointerLengthTop * Math.sin(valueAngle),
    y: cy - outerRadius * pointerLengthTop * Math.cos(valueAngle),
  };

  const bottomTarget = {
    x: cx - outerRadius * pointerLengthBottom * Math.sin(valueAngle),
    y: cy + outerRadius * pointerLengthBottom * Math.cos(valueAngle),
  };

  return (
    <g>
      {/* Circle with offset */}
      <circle cx={cx + circleOffsetX} cy={cy + circleOffsetY} r={5} fill="black" />

      {/* Top Pointer */}
      <path
        d={`M ${cx + circleOffsetX} ${cy + circleOffsetY} L ${topTarget.x} ${topTarget.y}`}
        stroke="black"
        strokeWidth={1}
      />

      {/* Bottom Pointer */}
      <path
        d={`M ${cx + circleOffsetX} ${cy + circleOffsetY} L ${bottomTarget.x} ${bottomTarget.y}`}
        stroke="black"
        strokeWidth={2}
      />
    </g>
  );
}

export default function SEOGauge({
  percentage,
  labelFontSize,
  labelFontWeight,
}: {
  percentage: number;
  label: string;
  labelFontSize: number;
  labelFontWeight: number;
}) {
  // Gradient color stops for the reference arc
  const gradientStops = [
    { offset: '0%', color: '#ff0000' }, // Red
    { offset: '25%', color: '#eda528' }, // Orange
    { offset: '50%', color: '#faff09' }, // Yellow
    { offset: '75%', color: '#9fd344' }, // Light Green
    { offset: '100%', color: '#00a35e' }, // Green
  ];

  return (
    <Gauge
      value={100 - percentage}
      height={200}
      cornerRadius={'20%'} // Set corner radius to 50% for more rounded shape
      startAngle={110}
      endAngle={-110} // Set end angle to -90 to finish on the left
      sx={{
        [`& .${gaugeClasses.valueArc}`]: {
          fill: '#f4f1f0',
          strokeLinecap: 'round',
          strokeWidth: 2,
          stroke: '#f4f1f0',
          rx: 8,
          ry: 8,
          cornerRadius: '20%', // Added corner radius to match the parent Gauge
        },
        [`& .${gaugeClasses.referenceArc}`]: {
          fill: 'url(#dynamic-reference-gradient)', // Apply gradient to reference arc
        },
        [`& .${gaugeClasses.valueText}`]: {
          fontWeight: labelFontWeight,
          fontSize: labelFontSize,
        },
      }}
      text={({ valueMax }) => `${percentage} / ${valueMax}`}
    >
      {/* Dynamic Gradient Definition */}
      <defs>
        {/* Reference Arc Gradient Definition */}
        <linearGradient id="dynamic-reference-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          {gradientStops.map((stop, index) => (
            <stop key={index} offset={stop.offset} stopColor={stop.color} />
          ))}
        </linearGradient>
      </defs>
      <GaugePointer
        circleOffsetX={0}
        circleOffsetY={-20}
        pointerLengthTop={1}
        pointerLengthBottom={-1}
      />
    </Gauge>
  );
}
