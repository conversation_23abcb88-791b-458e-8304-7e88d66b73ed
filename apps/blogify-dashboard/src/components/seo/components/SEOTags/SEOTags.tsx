// بسم الله الرحمن الرحيم

import { SEOAnalysis } from '@/types/components/seo';
import { SEOTag } from './components/SEOTag';
import { KeywordCategory, SEOTagContext, SEOTagContextType } from './contexts/SEOTagContext';

type SEOTagsProps = Pick<SEOTagContextType, 'blogInfo' | 'inserter'> & {
  data: Record<KeywordCategory, { score: number; keywords: SEOAnalysis[keyof SEOAnalysis] }>;
};

export function SEOTags({ data, inserter, blogInfo }: SEOTagsProps) {
  return (
    <section className="divide-y divide-gray-900">
      {Object.entries(data).map(([label, { score, keywords }]) => (
        <SEOTagContext.Provider
          key={label}
          value={{
            label: label as KeywordCategory,
            keywords: Object.entries(keywords).map(
              ([word, [count, minimum, maximum]]) =>
                ({
                  word,
                  count,
                  minimum,
                  maximum,
                }) as const
            ),
            blogInfo,
            inserter,
          }}
        >
          <SEOTag score={score} />
        </SEOTagContext.Provider>
      ))}
    </section>
  );
}
