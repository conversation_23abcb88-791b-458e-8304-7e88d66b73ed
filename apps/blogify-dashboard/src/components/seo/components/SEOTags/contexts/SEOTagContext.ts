// بسم الله الرحمن الرحيم

import { Inserter } from '@/components/form/RichEditor';
import { ComponentProps, createContext } from 'react';
import Keyword from '../../elements/Keyword';
import { Blog } from '@ps/types';

export type KeywordCategory = 'headings' | 'content';
export type SEOTagContextType = {
  label: KeywordCategory;
  inserter: Inserter;
  keywords: ComponentProps<typeof Keyword>[];
  blogInfo: Pick<Blog, '_id' | 'blogTone'>;
};
export const SEOTagContext = createContext<SEOTagContextType | null>(null);
