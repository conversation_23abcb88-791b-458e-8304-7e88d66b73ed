// بسم الله الرحمن الرحيم

import { useState } from 'react';
import { AllKeywordsDialog } from '../../dialogs/AllKeywordsDialog';
import Keyword from '../../elements/Keyword';
import Expandable from '../../elements/Expandable';
import { Button } from '@ps/ui/components/button';
import { ImproveTag } from './ImproveTag';
import { useLocation } from 'react-router-dom';
import { useSEOTagContext } from '../contexts/SEOTagContext.hook';

export const SEOTag: React.FC<{
  score: number;
}> = ({ score }) => {
  const { keywords, label } = useSEOTagContext();
  const { pathname } = useLocation();
  const [showModal, setShowModal] = useState(false);
  return (
    <Expandable label={label} percentage={score}>
      <div className={`flex flex-wrap gap-1.5 rounded-lg p-2`}>
        {keywords.slice(0, 25).map((props) => (
          <Keyword key={props.word} {...props} />
        ))}
        {keywords.length > 25 && (
          <div className="flex w-full justify-center">
            <Button
              variant="ghost"
              onClick={() => setShowModal(true)}
              className="mt-2 w-full border"
            >
              View More Keywords
            </Button>
          </div>
        )}
        {pathname.endsWith('edit') && <ImproveTag />}
      </div>
      {showModal && <AllKeywordsDialog onClose={() => setShowModal(false)} data={keywords} />}
    </Expandable>
  );
};
