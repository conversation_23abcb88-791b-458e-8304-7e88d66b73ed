// بسم الله الرحمن الرحيم

import { Textarea } from '@ps/ui/components/textarea';
import { Dispatch, Ref, SetStateAction, useImperativeHandle, useState } from 'react';
import Select from 'react-select';
import { BLOG_TONES } from '@/constants/blog';
import { Slider } from '@ps/ui/components/slider';
import Keyword from '@/components/seo/components/elements/Keyword';
import { useSEOTagContext } from '@/components/seo/components/SEOTags/contexts/SEOTagContext.hook';
import { API } from '@/services/api';

export default function Form({ ref }: { ref: Ref<{ submit: () => Promise<string> }> }) {
  const {
    blogInfo: { blogTone, _id: blogID },
  } = useSEOTagContext();
  const defaultWordCount = 80;
  const [prompt, setPrompt] = useState('');
  const [selectedKeywords, setSelectedKeywords] = useState([] as string[]);
  const [tone, setTone] = useState(blogTone);
  const [wordCount, setWordCount] = useState(defaultWordCount);

  useImperativeHandle(
    ref,
    () => ({
      submit: () =>
        API.post<string>(`blogs/seo/generate/${blogID}/content`, {
          wordCount,
          keywords: selectedKeywords,
          prompt,
          tone,
        }) as Promise<string>,
    }),
    [blogID, prompt, selectedKeywords, tone, wordCount]
  );

  return (
    <form className="space-y-3">
      <Form.Prompt onChange={setPrompt} />
      <Form.KeywordsSelect onChange={setSelectedKeywords} />
      <Form.Tone value={tone} onChange={setTone} />
      <Form.WordCount value={wordCount} onChange={setWordCount} />
    </form>
  );
}

type PromptProps = {
  onChange: Dispatch<SetStateAction<string>>;
};
Form.Prompt = function Prompt({ onChange }: PromptProps) {
  return (
    <section className="space-y-2">
      <label htmlFor="prompt" className="font-medium">
        Prompt
      </label>
      <Textarea
        id="prompt"
        placeholder="Tell us what to write"
        onChange={(e) => onChange(e.target.value)}
      />
    </section>
  );
};

type KeywordsSelectProps = {
  onChange: (keywords: string[]) => void;
};
Form.KeywordsSelect = function KeywordsSelect({ onChange }: KeywordsSelectProps) {
  const { keywords } = useSEOTagContext();
  return (
    <section className="space-y-2">
      <label className="font-medium">Keywords</label>
      <Select
        onChange={(options) => {
          onChange(options ? options.map((option) => option.value) : []);
        }}
        placeholder="Select SEO keywords"
        isMulti
        isClearable
        options={keywords.map((keyword) => ({
          value: keyword.word,
          label: <Keyword {...keyword} />,
        }))}
        styles={{
          multiValue: (base) => ({
            ...base,
            border: 'solid 1px orange',
            backgroundColor: 'transparent',
          }),
          multiValueLabel: (base) => ({
            ...base,
            backgroundColor: 'transparent',
          }),
        }}
      />
    </section>
  );
};

type ToneProps = {
  value: string;
  onChange: Dispatch<SetStateAction<string>>;
};
Form.Tone = function Tone({ value, onChange }: ToneProps) {
  const {
    blogInfo: { blogTone },
  } = useSEOTagContext();
  return (
    <section className="space-y-2">
      <label className="font-medium">Tone</label>
      <Select
        value={{ value, label: value }}
        onChange={(option) => onChange(option ? option.value : blogTone)}
        isClearable
        options={BLOG_TONES.map((tone) => ({
          value: tone,
          label: tone,
        }))}
      />
    </section>
  );
};

type WordCountProps = {
  value: number;
  onChange: Dispatch<SetStateAction<number>>;
};
Form.WordCount = function WordCount({ value, onChange }: WordCountProps) {
  const [min, max] = [10, 300];

  return (
    <section className="space-y-2">
      <label className="font-medium">Word Count - {value} words</label>
      <Slider
        className="h-5"
        max={max}
        min={min}
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
      />
      <div className="flex justify-between text-sm text-gray-500">
        <span>{min}</span>
        <span>{max}</span>
      </div>
    </section>
  );
};
