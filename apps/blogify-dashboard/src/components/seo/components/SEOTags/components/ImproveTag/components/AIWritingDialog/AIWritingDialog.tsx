// بسم الله الرحمن الرحيم

import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/dialog';
import { ImLeaf } from 'react-icons/im';
import Form from './components/Form';
import { ComponentProps, useRef, useState } from 'react';
import { useSEOTagContext } from '../../../../contexts/SEOTagContext.hook';
import toast from 'react-hot-toast';

type AIWritingDialogProps = Required<Pick<ComponentProps<typeof Dialog>, 'open' | 'onOpenChange'>>;

export default function AIWritingDialog({ open, onOpenChange }: AIWritingDialogProps) {
  const { inserter } = useSEOTagContext();
  const formRef = useRef<{ submit: () => Promise<string> }>({ submit: () => Promise.resolve('') });
  const [content, setContent] = useState('');

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      Icon={ImLeaf}
      title="AI Writing"
      description="Write new content using SEO keywords"
      actions={
        <div className="flex w-full flex-col gap-2">
          <Button
            type="submit"
            variant="secondary"
            onClick={() => {
              toast.promise(
                formRef.current.submit().then((text) => setContent(text)),
                {
                  loading: 'Generating content...',
                  success: 'Content generated successfully!',
                  error: 'Failed to generate content!',
                }
              );
            }}
          >
            Generate
          </Button>
          <Button
            disabled={content.length === 0}
            onClick={() => {
              inserter(content);
              setContent('');
              onOpenChange(false);
            }}
          >
            Insert
          </Button>
        </div>
      }
    >
      <div className="space-y-5">
        <AIWritingDialog.Form ref={formRef} />
        {content.length > 0 && <AIWritingDialog.Content content={content} />}
      </div>
    </Dialog>
  );
}

AIWritingDialog.Form = Form;

AIWritingDialog.Content = function Content({ content }: { content: string }) {
  return (
    <article className="flex flex-col gap-1">
      <span className="font-medium">Generated Content</span>
      <span className="text-pretty rounded-lg border border-gray10 p-4">{content}</span>
    </article>
  );
};
