// بسم الله الحمن الرحيم

import { useCallback, useState } from 'react';
import { SuggestCard } from './components/SuggestCard';
import { SuggestedHeadings } from './components/SuggestedHeadings';
import { API } from '@/services/api';
import { HeadingsSuggestion } from '@/types/components/seo';
import toast from 'react-hot-toast';
import { useSEOTagContext } from '../../contexts/SEOTagContext.hook';
import { useToggle } from 'react-use';
import AIWritingDialog from './components/AIWritingDialog';

export function ImproveTag() {
  const { label } = useSEOTagContext();
  return ImproveTag[label]();
}

ImproveTag.content = function ImproveContent() {
  const [openDialog, toggleOpenDialog] = useToggle(false);
  return (
    <>
      <SuggestCard label="content" onClick={toggleOpenDialog} />
      <AIWritingDialog open={openDialog} onOpenChange={toggleOpenDialog} />
    </>
  );
};

ImproveTag.headings = function ImproveHeadings() {
  const {
    blogInfo: { _id: blogID },
  } = useSEOTagContext();
  const [data, setData] = useState<HeadingsSuggestion>();
  const generateSuggestions = useCallback(
    () =>
      toast.promise(
        API.post<HeadingsSuggestion>(`blogs/seo/generate/${blogID}/headings`).then((suggestions) =>
          setData(suggestions)
        ),
        {
          loading: 'Generating suggestions...',
          success: 'Suggestions generated successfully',
          error: 'Failed to generate suggestions',
        }
      ),
    [blogID]
  );

  return !data ? (
    <SuggestCard label="headings" onClick={generateSuggestions} />
  ) : (
    <SuggestedHeadings data={data} />
  );
};
