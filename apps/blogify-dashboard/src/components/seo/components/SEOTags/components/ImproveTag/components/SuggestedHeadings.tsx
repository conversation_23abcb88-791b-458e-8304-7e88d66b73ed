// بسم الله الرحمن الرحيم
import { copy } from '@/utils/clipboard';
import { Button } from '@ps/ui/components/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ps/ui/components/tabs';
import React from 'react';
import { cn } from '@ps/ui/lib/utils';
import Percentage from '../../../../elements/Percentage';
import { HeadingsSuggestion } from '@/types/components/seo';

export const SuggestedHeadings: React.FC<{
  data: HeadingsSuggestion;
}> = ({ data }) => (
  <Tabs defaultValue="h1">
    <TabsList variant="nav">
      {Object.keys(data).map((tag) => (
        <TabsTrigger value={tag} key={tag} variant="nav" className="uppercase">
          <span className="px-1">{tag}</span>
        </TabsTrigger>
      ))}
    </TabsList>
    {Object.entries(data).map(([tag, { suggestions, keywords }]) => (
      <TabsContent key={tag} value={tag}>
        {suggestions.map(([text, score]) => (
          <SuggestedHeading key={text} text={text} keywords={keywords} percentage={score} />
        ))}
      </TabsContent>
    ))}
  </Tabs>
);
const SuggestedHeading: React.FC<{ text: string; keywords: string[]; percentage: number }> = ({
  text,
  percentage,
  keywords,
}) => (
  <section className="space-y-1 p-1">
    <div className="flex flex-row flex-wrap gap-1">
      {text.split(' ').map((word, index) => (
        <span key={word + index} className={cn({ 'font-medium': keywords.includes(word) })}>
          {word}
        </span>
      ))}
    </div>
    <div className="flex items-center justify-start gap-2">
      <Percentage value={percentage} />
      <Button onClick={() => copy(text)} variant="ghost" className="h-0 bg-slate-100 text-sm">
        Copy
      </Button>
    </div>
  </section>
);
