// بسم الله الرحمن الرحيم

import { Button } from '@ps/ui/components/button';
import { BsStars } from 'react-icons/bs';

export const SuggestCard = ({ label, onClick }: { label: string; onClick: () => void }) => (
  <section className="flex flex-col gap-2 rounded-lg border-gray15 bg-[#eef6f7] p-4">
    <h2 className="text-lg font-semibold">Improve Blog {label}</h2>
    <span className="text-sm">
      You can improve your {label} SEO Score by using the words above or you can even generate new
      ones using AI suggestions!
    </span>
    <div className="flex justify-between gap-4">
      <Button
        className="bg-[#2e838d] text-sm capitalize text-white"
        variant="secondary"
        onClick={onClick}
      >
        Suggest {label}
      </Button>
      <BsStars color="#2e838d" size={24} />
    </div>
  </section>
);
