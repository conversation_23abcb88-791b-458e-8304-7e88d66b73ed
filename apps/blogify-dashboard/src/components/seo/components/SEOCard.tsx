// بسم الله الرحمن الرحيم
import { IoMdRefresh } from 'react-icons/io';
import { ReactNode } from 'react';

import { Button } from '@ps/ui/components/button';
import useAnimate from '@ps/ui/hooks/useAnimate';

import SEOGauge from './SEOGauge';

export default function SEOCard({ children, score }: { score: number; children: ReactNode }) {
  const [animatedScore] = useAnimate({ value: score });

  return (
    <article className="rounded-lg border border-gray-900">
      <div>
        <span className="flex items-center justify-between gap-2 px-3 pt-3">
          <span className="text-lg font-bold">SEO Metrics</span>
          <Button variant="ghost" className="text-primary ">
            <IoMdRefresh className="text-xl font-bold" />
            Refresh
          </Button>
        </span>
        <span className="flex items-center justify-between gap-2 text-md font-medium *:hidden">
          <span>Average: {70}</span>
          <span>Top: {90}</span>
        </span>
        <section className="m-auto">
          <SEOGauge
            percentage={Math.ceil(animatedScore)}
            label={Math.ceil(animatedScore) + '%'}
            labelFontSize={15}
            labelFontWeight={500}
          />
        </section>
      </div>

      <hr className="mt-4" />

      <div className="rounded-lg p-4">{children}</div>
    </article>
  );
}
