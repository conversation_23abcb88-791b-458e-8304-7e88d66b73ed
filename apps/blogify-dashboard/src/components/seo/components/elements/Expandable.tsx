import { ReactNode, useRef } from 'react';
import { FaCaretDown, FaCaretRight } from 'react-icons/fa6';
import { useToggle } from 'react-use';
import Percentage from './Percentage';

export default function Expandable({
  label,
  percentage,
  children,
  className,
}: {
  label: string;
  percentage: number;
  children: ReactNode;
  className?: string;
}) {
  const [open, toggleOpen] = useToggle(true); // ✅ Start open
  const detailsRef = useRef<HTMLDetailsElement>(null);

  const handleToggle = () => {
    if (detailsRef.current) {
      toggleOpen(detailsRef.current.open);
    }
  };

  return (
    <details ref={detailsRef} open={open} className={className} onToggle={handleToggle}>
      <summary className="flex items-center justify-between gap-2 py-2">
        <span className="flex items-center gap-2 capitalize">
          {label}
          {open ? (
            <FaCaretDown className="w-5 rounded" />
          ) : (
            <FaCaretRight className="min-w-5 rounded-lg" />
          )}
        </span>
        <Percentage value={percentage} />
      </summary>
      {children}
    </details>
  );
}
