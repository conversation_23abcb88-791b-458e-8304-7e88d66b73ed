// بسم الله الرحمن الرحيم

import { use } from 'react';
import { scorer } from '../../functions/score';
import { KeywordCategory, SEOTagContext } from '../SEOTags/contexts/SEOTagContext';

interface KeywordProps {
  word: string;
  count: number;
  minimum: number;
  maximum: number;
  label?: KeywordCategory;
}

export default function Keyword({ word, count: value, minimum, maximum, label }: KeywordProps) {
  const keywordCategory = label ?? use(SEOTagContext)?.label ?? 'content';
  const [color, backgroundColor] = scorer[keywordCategory](value, [minimum, maximum] as const);
  return (
    <div
      style={{ color, backgroundColor }}
      className="flex flex-wrap items-center justify-between gap-1 rounded-lg p-1.5 text-sm font-medium"
    >
      <span className="whitespace-normal break-words capitalize">{word}</span>
      <span className="rounded bg-white px-0.5 text-xs text-gray-500">
        {`${value} / ${minimum} - ${maximum}`}
      </span>
    </div>
  );
}
