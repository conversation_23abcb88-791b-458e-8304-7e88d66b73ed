// بسم الله الرحمن الرحيم
import { IoClose } from 'react-icons/io5';
import Keyword from '../elements/Keyword';

type AllKeywordsDialogProps = {
  onClose: () => void;
  data: React.ComponentProps<typeof Keyword>[];
};

export const AllKeywordsDialog: React.FC<AllKeywordsDialogProps> = ({ onClose, data }) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div className="max-h-[80vh] w-[600px] overflow-y-auto rounded-lg bg-white p-6">
      <div className="mb-4 flex justify-between">
        <h2 className="text-xl font-bold">All Keywords</h2>
        <button
          onClick={() => {
            onClose();
          }}
        >
          <IoClose size={24} />
        </button>
      </div>
      <div className="flex flex-wrap gap-4">
        {data?.map((props) => <Keyword {...props} key={props.word} />)}
      </div>
    </div>
  </div>
);
