import { useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import toast from 'react-hot-toast';

import { EmptySEOAnalysis, KeywordData, SEOAnalysis } from '@/types/components/seo';
import { blogEvents } from '@/services/event';
import useAnimate from '@ps/ui/hooks/useAnimate';

import { combineHeadingKeywords, score } from './functions/score';
import { SEOTags } from './components/SEOTags/SEOTags';
import Progress from '../misc/Progress';
import SEOCard from './components/SEOCard';
import { Inserter } from '../form/RichEditor';

function filterKeywords(keywords: Record<string, KeywordData>) {
  return Object.fromEntries(
    Object.entries(keywords)
      .filter(([, [, min, max]]) => min !== 0 || max !== 0)
      .toSorted(([a], [b]) => a.localeCompare(b))
  );
}

const processKeywords = ({ content, h1, h2, h3 }: SEOAnalysis) => {
  const headingKeywords = filterKeywords(combineHeadingKeywords({ h1, h2, h3 }));
  const contentKeywords = filterKeywords(content);
  const contentScore = score('content')(contentKeywords);
  const headingsScore = score('headings')(headingKeywords);
  const totalScore = 50 + 10 * headingsScore + 40 * contentScore;
  return {
    totalScore,
    tags: {
      headings: {
        score: headingsScore,
        keywords: headingKeywords,
      },
      content: {
        score: contentScore,
        keywords: contentKeywords,
      },
    },
  };
};

type SEOScoreProps = {
  blogTone: string;
  blogID: string;
  inserter?: Inserter;
};
export function SEOScore({ blogID, blogTone, inserter = () => {} }: SEOScoreProps) {
  const { data = EmptySEOAnalysis, refetch } = useQuery<SEOAnalysis>(`blogs/seo/${blogID}`);
  const [progress, setProgress] = useState<number>(0);
  const [animatedProgress] = useAnimate({ value: progress });
  const processedData = useMemo(() => processKeywords(data), [data]);

  useEffect(() => {
    const subscription = blogEvents.subscribe(
      'BLOG_SEO_ANALYSIS_STATUS_UPDATE',
      ({ _id, seoAnalysisStatus }) => {
        if (blogID !== _id || !seoAnalysisStatus) return;
        const progressMap: { [key: string]: number } = {
          seo_search_content_inprogress: 25,
          seo_search_content_completed: 50,
          seo_keyword_analysis_inprogress: 75,
          seo_keyword_analysis_completed: 100,
        };

        if (seoAnalysisStatus in progressMap) {
          setProgress(progressMap[seoAnalysisStatus]);
          if (progressMap[seoAnalysisStatus] === 100) refetch();
        }

        if (seoAnalysisStatus === 'seo_keyword_analysis_failed') {
          toast.error('Failed to analyze SEO keywords');
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [blogID, refetch]);

  return (
    <>
      <SEOCard score={processedData.totalScore}>
        {progress !== 100 && progress !== 0 ? (
          <Progress
            progress={animatedProgress}
            title="Analyzing Content.."
            subtitle="Once analyzed we'll show your content score and suggest keywords to optimize you blog"
          />
        ) : (
          <SEOTags
            inserter={inserter}
            blogInfo={{ _id: blogID, blogTone }}
            data={processedData.tags}
          />
        )}
      </SEOCard>
    </>
  );
}
