// بسم الله الرحمن الرحيم

export function colorEncode(value: number) {
  return value < 20
    ? colors.red
    : value < 40
      ? colors.orange
      : value < 60
        ? colors.yellow
        : value < 80
          ? colors.olive
          : colors.green;
}

export type Scorer = (
  count: number,
  minmax: [min: number, max: number]
) => [...(typeof colors)[keyof typeof colors], score: number];

export const scoreCount: Scorer = (count: number) =>
  count > 0 ? [...colors.green, 1] : [...colors.grey, 0];

export const scoreNormal: Scorer = (
  count: number,
  minmax: [min: number, max: number]
): [...(typeof colors)[keyof typeof colors], score: number] => {
  const [min, max] = minmax;

  if (count === 0) return [...colors.grey, 0];

  // Ensure the range is valid
  if (min > max) {
    throw new Error('Invalid range: min cannot be greater than max.');
  }

  if (min === 0 && max === 1) {
    // If the range is binary (0 to 1), treat it as a binary classification
    return count > 0 ? [...colors.green, 1] : [...colors.grey, 0];
  } else if (max - min < 4) {
    // If the range is too small, treat it as a binary classification
    if (count < min) {
      return [...colors.grey, 0]; // Outside range
    } else if (count > max) {
      return [...colors.red, 0]; // Outside range
    } else {
      return [...colors.green, 1]; // Within range
    }
  } else {
    // Calculate quartiles
    const q1 = min + (max - min) / 4;
    const q3 = max - (max - min) / 4;

    if (count < min) {
      return [...colors.grey, 0]; // Outside range
    } else if (count > max) {
      return [...colors.red, 0]; // Outside range
    } else if (count >= q1 && count <= q3) {
      return [...colors.green, 1]; // Within first and third quartiles
    } else {
      return [...colors.yellow, 0.75]; // Within range but not within quartiles
    }
  }
};

const colors: Record<string, [fontColor: string, backgroundColor: string]> = {
  grey: ['#93766c', '#f8f7f6'],
  green: ['#009933', 'rgba(0, 153, 51, 0.15)'],
  olive: ['#9dbf02', 'rgba(157, 191, 2, 0.1)'],
  yellow: ['#e69019', 'rgba(237, 165, 40, 0.15)'],
  orange: ['#fb711c', 'rgba(251, 113, 28, 0.1)'],
  red: ['#e72f2f', 'rgba(204, 0, 34, 0.15)'],
} as const;
