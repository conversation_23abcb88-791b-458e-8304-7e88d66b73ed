// بسم الله الرحمن الرحيم

import { SEOAnalysis } from '@/types/components/seo';
import { scoreCount, scoreNormal, Scorer } from './color';

export const combineHeadingKeywords = (data: Pick<SEOAnalysis, 'h1' | 'h2' | 'h3'>) =>
  Object.fromEntries(
    Object.values(data)
      .map((keywords) => Object.entries(keywords))
      .flat()
  );

const computeScore = (_scorer: Scorer) => (data: SEOAnalysis[keyof SEOAnalysis]) =>
  Object.values(data).reduce((acc, [count, min, max]) => acc + _scorer(count, [min, max])[2], 0) /
  (Object.keys(data).length || 1);

export const scorer = {
  headings: scoreCount,
  content: scoreNormal,
} as const;

export const score = (category: keyof typeof scorer) => computeScore(scorer[category]);
