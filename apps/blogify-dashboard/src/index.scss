@use '@ps/ui/globals.scss';

@import url('https://fonts.googleapis.com/css2?family=Figtree:wght@700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }
  input {
    @apply p-2;
  }
  body {
    @apply mx-auto my-0 overflow-y-scroll bg-bg2 text-black2;
  }
}

:root {
  --toast-viewport-padding: 24px;
}

/* - - - - - - */
/* Text */
/* - - - - - */
@layer components {
  .ellipsis {
    transition: -webkit-line-clamp 0.5s;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    display: -webkit-box;
    overflow: hidden;
    &.r1 {
      -webkit-line-clamp: 1;
    }
    &.r2 {
      -webkit-line-clamp: 2;
    }
    &.r3 {
      -webkit-line-clamp: 3;
    }
  }
}

/* - - - - - - */
/* Inputs */
/* - - - - - */
input[type='number'] {
  &.no-spin-button {
    appearance: textfield;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      @apply appearance-none;
    }
  }
}

/* design */

button[data-state='active'] .off-active {
  box-shadow: none;
  color: #f2805a;
  background-color: white;
}

/* width */
.sidebar::-webkit-scrollbar {
  width: 5px;
  height: 5px !important;
}

/* Track */
.sidebar::-webkit-scrollbar-track {
  background: #e3dbd9;
}

/* Handle */
.sidebar::-webkit-scrollbar-thumb {
  background: #93766c;
  border-radius: 5px;
}

/* Handle on hover */
.sidebar::-webkit-scrollbar-thumb:hover {
  background: #f06d42;
}

input[type='radio']:checked + span {
  display: block;
}

// Color Input
input[type='color']::-moz-color-swatch {
  border: none;
}

input[type='color']::-webkit-color-swatch-wrapper {
  padding: 0;
  border-radius: 0;
}

input[type='color']::-webkit-color-swatch {
  border: none;
}
