// PlanContext.tsx
import React, { createContext, useContext } from 'react';
import { useQuery, UseQueryResult } from 'react-query';

// Define types for plans
interface Plan {
  id: string;
  name: string;
  price: number;
  currency: string;
  limit?: {
    [key: string]: number | string;
  };
  interval?: 'month' | 'year';
  isLatest: boolean;
  isActive: boolean;
}

// Define types for plan context
interface PlanContextType {
  monthlyPlans: Plan[];
  yearlyPlans: Plan[];
  lifetimePlans: Plan[];
}

// Create a context for the plans
const PlanContext = createContext<PlanContextType | undefined>(undefined);

// Custom hook to use the plan context
// eslint-disable-next-line react-refresh/only-export-components
export const usePlanContext = () => {
  const context = useContext(PlanContext);
  if (!context) {
    throw new Error('usePlanContext must be used within a PlanProvider');
  }
  return context;
};

// Function to separate plans into monthly, yearly, and lifetime based on package name
const separatePlansByName = (plans: Plan[]): PlanContextType => {
  const monthlyPlans = plans.filter((plan) => plan.name.startsWith('MONTHLY_') && plan.isLatest);
  const yearlyPlans = plans.filter((plan) => plan.name.startsWith('YEARLY_') && plan.isLatest);
  const lifetimePlans = plans.filter((plan) => plan.name.startsWith('LIFETIME_') && plan.isLatest);
  return { monthlyPlans, yearlyPlans, lifetimePlans };
};

// Provider component for the plan context
export const PlanProvider = ({ children }: { children: React.ReactNode }) => {
  const { data, isLoading }: UseQueryResult<Plan[]> = useQuery('payments/plans');

  // Separate plans into categories when data is fetched
  const { monthlyPlans, yearlyPlans, lifetimePlans } = separatePlansByName(data || []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <PlanContext.Provider value={{ monthlyPlans, yearlyPlans, lifetimePlans }}>
      {children}
    </PlanContext.Provider>
  );
};
