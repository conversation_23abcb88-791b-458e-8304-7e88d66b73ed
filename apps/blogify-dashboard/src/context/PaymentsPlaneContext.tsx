// PlanProvider.tsx
import React, { createContext, useContext } from 'react';
import { useQuery } from 'react-query';

import { API } from '@/services/api';

type Plan = {
  id: string;
  name: string;
  price: number;
  currency: string;
  limit: {
    [key: string]: number | string;
  };
  isLatest: boolean;
};

type ProcessedPlan = {
  name: string;
  featured: boolean;
  pricing: { monthly: number; yearly: number; lifetime: number };
  mediaCreditPricing: { monthly: string | number; yearly: number; lifetime: number };
  promptCreditPricing: { monthly: string | number; yearly: number; lifetime: number };
  discount: { monthly: number; yearly: number; lifetime: number };
  features: {
    name: string;
    available: boolean;
    value: string | number;
    includes: { name: string; value: string }[];
  }[];
};

type PlanContextType = {
  plans: ProcessedPlan[];
};

const PlanContext = createContext<PlanContextType | undefined>(undefined);

// eslint-disable-next-line react-refresh/only-export-components
export const usePlanContext = () => {
  const context = useContext(PlanContext);
  if (!context) {
    throw new Error('usePlanContext must be used within a PlanProvider');
  }
  return context;
};

export const PlanProvider = ({ children }: { children: React.ReactNode }) => {
  const { data } = useQuery<Plan[]>('plans', async () => {
    const response = await API.fetch<{ packages: any[] }>('payments/plans');
    return (response?.packages || [])?.filter((plan: Plan) => plan.isLatest === true);
  });

  let processedPlans: ProcessedPlan[] = [];

  if (data) {
    // Pricing mapping based on provided data
    const pricingMapping: { [key: string]: { monthly: number; yearly: number; lifetime: number } } =
      {};

    data.forEach((plan) => {
      const name = plan.name.replace(/^(MONTHLY_|YEARLY_|LIFETIME_)/, '');
      const pricingKey = plan.name.startsWith('MONTHLY')
        ? 'monthly'
        : plan.name.startsWith('YEARLY')
          ? 'yearly'
          : 'lifetime';

      pricingMapping[name] = {
        monthly: pricingKey === 'monthly' ? plan.price : 0,
        yearly: pricingKey === 'yearly' ? plan.price : 0,
        lifetime: pricingKey === 'lifetime' ? plan.price : 0,
      };
    });

    processedPlans = data
      .slice(0, 5) // Select the first 5 plans
      .map((plan) => {
        const name = plan.name.replace(/^(MONTHLY_|YEARLY_|LIFETIME_)/, '');

        const processedPlan: ProcessedPlan = {
          name: name,
          featured: false,
          pricing: pricingMapping[name] || { monthly: 0, yearly: 0, lifetime: 0 },
          mediaCreditPricing: { monthly: 0, yearly: 0, lifetime: 0 },
          promptCreditPricing: { monthly: 0, yearly: 0, lifetime: 0 },
          discount: { monthly: 0, yearly: 0, lifetime: 0 },
          features: [],
        };

        const featureMapping: { [key: string]: string } = {
          MAX_BLOGS_FROM_URL: 'Max blogs from URL',
          MAX_BLOGS_FROM_PROMPT: 'Max blogs from Prompt',
          MAX_UPLOAD_SIZE_IN_BYTES: 'Max upload size',
          MAX_DURATION_IN_SECONDS: 'Max duration',
        };

        processedPlan.mediaCreditPricing = {
          monthly: plan.limit.BLOGS_FROM_URL_PRICE,
          yearly: 0,
          lifetime: 0,
        };
        processedPlan.promptCreditPricing = {
          monthly: plan.limit.BLOGS_FROM_PROMPT_PRICE,
          yearly: 0,
          lifetime: 0,
        };
        processedPlan.discount = { monthly: 0, yearly: 0, lifetime: 0 };

        for (const [planKey, planValue] of Object.entries(plan.limit)) {
          const featureName = featureMapping[planKey] || planKey.replace(/_/g, ' ').toLowerCase();
          processedPlan.features.push({
            name: featureName,
            available: true,
            value: planValue,
            includes: [],
          });
        }

        return processedPlan;
      });
  }

  const value = {
    plans: processedPlans,
  };

  return <PlanContext.Provider value={value}>{children}</PlanContext.Provider>;
};
