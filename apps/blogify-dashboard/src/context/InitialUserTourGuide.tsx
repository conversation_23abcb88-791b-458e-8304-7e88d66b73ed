import { createContext, useContext, useEffect, useMemo } from 'react';
import { useLocalStorage, useSetState } from 'react-use';

export interface IInitialUserTourGuideContext {
  run: boolean;
  stepIndex: number;
  tourActive: boolean;
}

const appState = {
  run: false,
  stepIndex: 0,
  tourActive: true,
};

// eslint-disable-next-line react-refresh/only-export-components
export const InitialUserTourGuideContext = createContext({
  state: appState,
  setState: () => undefined,
});
InitialUserTourGuideContext.displayName = 'InitialUserTourGuideContext';

export function InitialUserTourGuideProvider(props: any) {
  const [state, setState] = useSetState(appState);
  const [isTourDone] = useLocalStorage('initialUserTourGuide');

  useEffect(() => {
    if (isTourDone) {
      setState({ tourActive: false });
    }
  }, [isTourDone, setState]);

  const value = useMemo(
    () => ({
      state,
      setState,
    }),
    [setState, state]
  );

  return <InitialUserTourGuideContext.Provider value={value} {...props} />;
}

// Custom hook that shorthands the context!
// eslint-disable-next-line react-refresh/only-export-components
export function useInitialUserTourGuideContext(): {
  setState: (
    patch:
      | Partial<IInitialUserTourGuideContext>
      | ((previousState: IInitialUserTourGuideContext) => Partial<IInitialUserTourGuideContext>)
  ) => void;
  state: IInitialUserTourGuideContext;
} {
  const context = useContext(InitialUserTourGuideContext);
  if (!context) {
    throw new Error(
      'useInitialUserTourGuideContext must be used within a InitialUserTourGuideProvider'
    );
  }

  return context;
}
