import { createContext, useContext, useEffect, useMemo } from 'react';
import { useLocalStorage, useSetState } from 'react-use';

export interface IYoutubeTourGuideContext {
  run: boolean;
  stepIndex: number;
  tourActive: boolean;
}

const appState = {
  run: false,
  stepIndex: 0,
  tourActive: true,
};

// eslint-disable-next-line react-refresh/only-export-components
export const YoutubeTourGuideContext = createContext({
  state: appState,
  setState: () => undefined,
});
YoutubeTourGuideContext.displayName = 'YoutubeTourGuideContext';

export function YoutubeTourGuideProvider(props: any) {
  const [state, setState] = useSetState(appState);
  const [isTourDone] = useLocalStorage('youtubeTourGuide');

  useEffect(() => {
    if (isTourDone) {
      setState({ tourActive: false });
    }
  }, [isTourDone, setState]);

  const value = useMemo(
    () => ({
      state,
      setState,
    }),
    [setState, state]
  );

  return <YoutubeTourGuideContext.Provider value={value} {...props} />;
}

// Custom hook that shorthands the context!
// eslint-disable-next-line react-refresh/only-export-components
export function useYoutubeTourGuideContext(): {
  setState: (
    patch:
      | Partial<IYoutubeTourGuideContext>
      | ((previousState: IYoutubeTourGuideContext) => Partial<IYoutubeTourGuideContext>)
  ) => void;
  state: IYoutubeTourGuideContext;
} {
  const context = useContext(YoutubeTourGuideContext);
  if (!context) {
    throw new Error('useYoutubeTourGuideContext must be used within a YoutubeTourGuideProvider');
  }

  return context;
}
