// بسم الله الرحمن الرحيم

import { IntegrationDetail } from '@/types/misc/integration.type';
import { createContext, ReactNode } from 'react';
import { useQuery } from 'react-query';

// eslint-disable-next-line react-refresh/only-export-components
export const IntegrationDetailsContext = createContext<Record<string, IntegrationDetail>>({});

export function IntegrationDetailsContextProvider({ children }: Readonly<{ children: ReactNode }>) {
  const { data: integrations = {} } = useQuery<Record<string, IntegrationDetail>>('integrations');
  return (
    <IntegrationDetailsContext.Provider value={integrations}>
      {children}
    </IntegrationDetailsContext.Provider>
  );
}
