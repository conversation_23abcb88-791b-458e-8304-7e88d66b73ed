import { createContext, useContext, useEffect, useMemo } from 'react';
import { useLocalStorage, useSetState } from 'react-use';

export interface IWritingSnippetsTourGuideContext {
  run: boolean;
  stepIndex: number;
  tourActive: boolean;
}

const appState = {
  run: false,
  stepIndex: 0,
  tourActive: true,
};

// eslint-disable-next-line react-refresh/only-export-components
export const WritingSnippetsTourGuideContext = createContext({
  state: appState,
  setState: () => undefined,
});
WritingSnippetsTourGuideContext.displayName = 'WritingSnippetsTourGuideContext';

export function WritingSnippetsTourGuideProvider(props: any) {
  const [state, setState] = useSetState(appState);
  const [isTourDone] = useLocalStorage('writingSnippetsTourGuide');

  useEffect(() => {
    if (isTourDone) {
      setState({ tourActive: false });
    }
  }, [isTourDone, setState]);

  const value = useMemo(
    () => ({
      state,
      setState,
    }),
    [setState, state]
  );

  return <WritingSnippetsTourGuideContext.Provider value={value} {...props} />;
}

// Custom hook that shorthands the context!
// eslint-disable-next-line react-refresh/only-export-components
export function useWritingSnippetsTourGuideContext(): {
  setState: (
    patch:
      | Partial<IWritingSnippetsTourGuideContext>
      | ((
          previousState: IWritingSnippetsTourGuideContext
        ) => Partial<IWritingSnippetsTourGuideContext>)
  ) => void;
  state: IWritingSnippetsTourGuideContext;
} {
  const context = useContext(WritingSnippetsTourGuideContext);
  if (!context) {
    throw new Error(
      'useWritingSnippetsTourGuideContext must be used within a WritingSnippetsTourGuideProvider'
    );
  }

  return context;
}
