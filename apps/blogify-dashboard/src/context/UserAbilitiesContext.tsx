// بسم الله الرحمن الرحيم

import { UserRole } from '@/types/resources/user.type';
import { createContext, ReactNode } from 'react';

export const UserAbilitiesContextProvider = ({
  role,
  children,
}: {
  role: UserRole;
  children: ReactNode;
}) => (
  <UserAbilitiesContext.Provider value={computeAbilities(role)}>
    {children}
  </UserAbilitiesContext.Provider>
);

// eslint-disable-next-line react-refresh/only-export-components
export const UserAbilitiesContext = createContext<ReturnType<typeof computeAbilities>>(
  computeAbilities(UserRole.admin)
);

function computeAbilities(role: UserRole) {
  return {
    analytics: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
    },
    affiliate: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
      payout: [UserRole.superadmin, UserRole.admin].includes(role),
    },
    blog: {
      create: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(
        role
      ),
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      edit: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      publish: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
      delete: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
    },
    credits: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      buy: [UserRole.superadmin, UserRole.admin].includes(role),
    },
    defaults: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      edit: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
    },
    integrations: {
      connect: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
      disconnect: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
    },
    interests: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      edit: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
    },
    profile: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
      edit: [UserRole.superadmin, UserRole.admin, UserRole.editor, UserRole.writer].includes(role),
    },
    subscription: {
      view: [UserRole.superadmin, UserRole.admin].includes(role),
      edit: [UserRole.superadmin, UserRole.admin].includes(role),
    },
    user: {
      view: [UserRole.superadmin, UserRole.admin, UserRole.editor].includes(role),
      invite: [UserRole.superadmin, UserRole.admin].includes(role),
      deactivate: [UserRole.superadmin, UserRole.admin].includes(role),
    },
  } as const;
}
