import type { Product } from '@/types/resources/product.type';

import { useToggle } from 'react-use';

import { getVideoIdFromYouTubeUrl } from '@/utils/url';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import Chip from '@/components/layout/Chip';
import Link from '@/components/common/Link';

import AddonDeactivateDialog from './AddonDeactivateDialog';

export default function AddonCard({
  addon,
  imgHeight,
  refetch,
  selectAddon,
  togglePurchaseDialog,
  className,
}: {
  addon: Product;
  imgHeight?: number | number[];
  refetch?: () => void;
  selectAddon?: React.Dispatch<Product>;
  togglePurchaseDialog?: React.Dispatch<boolean>;
} & React.HTMLProps<HTMLDivElement>) {
  const [isDeactivateDialog, toggleDeactivateDialog] = useToggle(false);

  return (
    <>
      <Card className={`h-full justify-between border border-gray11 p-3 text-black1 ${className}`}>
        <div>
          <AddonCardImage addon={addon} imgHeight={imgHeight} />

          <Link to={addon.productUrl}>
            <div className="mb-1 font-semibold">{addon.name}</div>
          </Link>

          <div className="mb-1 text-sm">{addon.description}</div>

          {addon.learnMoreUrl && (
            <Link to={addon.learnMoreUrl}>
              <Button className="mb-3 !px-0" color="primary" variant="ghost">
                <div className="text-sm font-semibold">Learn More</div>
              </Button>
            </Link>
          )}
        </div>

        <div>
          <div className="mb-2 flex items-end gap-1">
            <div className="-mb-px text-xl font-bold">${(addon.price / 100).toFixed(2)}</div>
            <span className="text-sm">/ month</span>
          </div>

          <div className="mb-3 text-xs text-gray2">
            Learn more about our{' '}
            <Link
              className="underline hover:text-primary"
              color="blue"
              to="/dashboard/subscription"
            >
              Subscription Plans
            </Link>
            .
          </div>

          {!!addon.trialPeriod && (
            <div className="mb-2 text-xs text-gray2">
              Enjoy 3 days free trial, ${(addon.price / 100).toFixed(2)} / month afterwards.
            </div>
          )}
          {!(selectAddon && togglePurchaseDialog) ? (
            <Link to={`/dashboard/addons?addonId=${addon._id}`}>
              <Button className="w-full">
                {!!addon.trialPeriod ? 'Start Your Free Trial*' : 'Purchase Addon'}
              </Button>
            </Link>
          ) : addon.isActive ? (
            <Button variant="secondary" className="w-full" onClick={toggleDeactivateDialog}>
              Deactivate
            </Button>
          ) : (
            <Button
              className="w-full"
              onClick={() => {
                selectAddon(addon);
                togglePurchaseDialog(true);
              }}
            >
              {!!addon.trialPeriod ? 'Start Your Free Trial*' : 'Purchase Addon'}
            </Button>
          )}
        </div>
      </Card>

      {!!refetch && (
        <AddonDeactivateDialog
          addon={addon}
          refetch={refetch}
          open={isDeactivateDialog}
          onOpenChange={toggleDeactivateDialog}
        />
      )}
    </>
  );
}

const AddonCardImage = ({
  addon,
  imgHeight,
}: {
  addon: Product;
  imgHeight?: number | number[];
}) => {
  const minHeight = Array.isArray(imgHeight)
    ? imgHeight[0]
    : Number.isInteger(imgHeight)
      ? imgHeight
      : 176;

  return (
    <div className="relative mb-3">
      {addon.videoUrl ? (
        <iframe
          className="block rounded"
          src={`https://www.youtube.com/embed/${getVideoIdFromYouTubeUrl(addon.videoUrl)}`}
          style={{ minHeight }}
          title="Demo Video"
          allowFullScreen
          width="100%"
        ></iframe>
      ) : (
        <Link to={addon.productUrl}>
          <img
            className="w-full rounded object-cover"
            src={addon.imageUrl || 'https://placehold.co/312x176'}
            height={`${imgHeight || 176}px`}
            alt={addon.name}
          />
        </Link>
      )}

      {addon.isActive ? <Chip className="absolute bottom-0 left-0">Active</Chip> : null}
    </div>
  );
};
