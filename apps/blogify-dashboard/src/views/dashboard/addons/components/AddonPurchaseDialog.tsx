import type { StripePaymentElementChangeEvent } from '@stripe/stripe-js';
import type { Product } from '@/types/resources/product.type';

import { PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { useEffect, useState } from 'react';
import { LuBlocks } from 'react-icons/lu';
import * as Yup from 'yup';

import { stripePaymentElementOptions, getAddonReturnRedirect } from '@/utils/stripe';
import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { normalizeNumber } from '@/utils/number';
import { useStoreActions } from '@/store';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';
import Dialog from '@ps/ui/components/dialog';

export default function AddonPurchaseDialog({
  addon,
  ...props
}: {
  addon: Product;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [priceAfterDiscount, setPriceAfterDiscount] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stripeEvent, setStripeEvent] = useState<StripePaymentElementChangeEvent | null>(null);
  const [stripeError, setStripeError] = useState('');
  const [discount, applyDiscount] = useState(0);
  const [error, setError] = useState('');
  const elements = useElements();
  const stripe = useStripe();

  const fetchUser = useStoreActions((a) => a.user.fetch);

  const submit = async () => {
    if (!elements || !stripe) return;

    if (!stripeEvent?.complete && addon.price !== 0) {
      return setStripeError(`Your card number is incomplete.`);
    }

    setIsSubmitting(true);
    setStripeError('');
    setError('');

    const resp = await API.post<{ clientSecret: string; subscriptionRef: string }>(
      `products/addons/purchase`,
      { id: addon._id, coupon: coupon?.trim() }
    ).catch((e) => {
      setError(e?.error || e?.message || e);
      setIsSubmitting(false);
    });

    try {
      if (resp?.clientSecret && addon.price !== 0) {
        elements.submit();
        const result = await stripe[!!addon.trialPeriod ? 'confirmSetup' : 'confirmPayment']({
          elements,
          clientSecret: resp?.clientSecret,
          confirmParams: {
            return_url: getAddonReturnRedirect(addon._id),
          },
        });
        if (result.error?.message) {
          const paymentRef =
            result.error[addon.trialPeriod ? 'setup_intent' : 'payment_intent']?.id;
          if (paymentRef && resp?.subscriptionRef) {
            await API.patch(`products/addons/purchase/cancel`, {
              paymentRef,
              subscriptionRef: resp?.subscriptionRef,
            });
          }
          throw new Error(result.error.message);
        }

        await fetchUser();
        setIsSubmitting(false);
        if (props.onOpenChange) {
          props.onOpenChange(false);
        }
      }
    } catch (e: any) {
      elements.getElement('payment')?.clear();
      setStripeError(e?.error || e?.message || e);
      setIsSubmitting(false);
    }
  };

  const PaymentSchema = Yup.object().shape({
    id: Yup.string(),
    coupon: Yup.lazy(() =>
      Yup.string().test(
        'is-coupon-valid',
        `Invalid coupon provided.`,
        async (value, testContext) => {
          if (value) {
            return API.fetch<{ discount: number; priceAfterDiscount?: number }>(
              `payments/verify/coupon/${value?.trim()}?price=${addon.price}`
            )
              .then((resp) => {
                applyDiscount(resp?.discount || 0);
                setPriceAfterDiscount(resp?.priceAfterDiscount || 0);
                return true;
              })
              .catch((message) => {
                applyDiscount(0);
                setPriceAfterDiscount(0);
                testContext.createError({ message });
                return false;
              });
          }
          return true;
        }
      )
    ),
  });
  const initialValues = {
    id: addon._id,
    coupon: '',
  };
  const form = useForm<typeof initialValues>({
    validationSchema: PaymentSchema,
    initialValues,
    submit,
  });
  const { coupon } = form.values;

  useEffect(() => {
    if (coupon) {
      form.validateField('coupon');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (elements && addon.price >= 0) {
      elements.update({
        amount: priceAfterDiscount || addon.price || 0,
        mode: 'subscription',
        currency: 'usd',
      });
    }
  }, [elements, addon.price, priceAfterDiscount]);

  return (
    <Dialog
      open={props.open}
      onOpenChange={props.onOpenChange}
      Icon={LuBlocks}
      title="Buy Addon"
      description="Addons are monthly recurring. You can comeback to addon page and cancel anytime you want."
      actions={
        <Button className="w-full" type="button" loading={isSubmitting} onClick={submit}>
          {!!addon.trialPeriod ? 'Start Your Free Trial*' : 'Purchase Addon'}
        </Button>
      }
    >
      {/* Stripe resets the first input for some reason, this prevents required fields from getting reset */}
      <input hidden />

      <label>
        <div className="rounded border border-gray5">
          <div className="flex items-center justify-between p-3">
            <div>
              <div className="mb-1 font-semibold">{addon.name}</div>
              <div className="text-sm text-gray2">Addon</div>
            </div>
            <RadioGroup defaultValue="add-on">
              <RadioGroupItem value="add-on" />
            </RadioGroup>
          </div>

          <div className="flex items-center justify-between border-t border-[#D7E1F1] bg-gray7 px-3 py-2">
            <div className="flex items-center">
              <div
                className={cn('ml-1 text-xl font-semibold', {
                  'text-gray3 line-through': discount > 0,
                  'text-black1': discount <= 0,
                })}
              >
                ${normalizeNumber(addon.price / 100, 2)}
              </div>
              {discount > 0 && priceAfterDiscount >= 0 && (
                <div className="ml-1 text-xl font-semibold">
                  ${normalizeNumber(priceAfterDiscount / 100, 2)}
                </div>
              )}
            </div>
            <div className="text-sm">Recurring Monthly</div>
          </div>
        </div>
      </label>

      <div className={cn('mt-10 rounded-md border pb-0.5', { 'border-red': stripeError })}>
        <PaymentElement onChange={setStripeEvent} options={stripePaymentElementOptions} />
      </div>
      <ErrorMessage className="mt-2">{stripeError}</ErrorMessage>

      {addon.price !== 0 && (
        <div className="mt-0">
          {/* @ts-ignore For Now */}
          <FormField
            label="Coupon"
            {...form.getInputFields('coupon')}
            // message={discount > 0 ? 'Coupon Applied' : ''}
          />
        </div>
      )}

      <ErrorMessage className="my-2">{error}</ErrorMessage>
    </Dialog>
  );
}
