import type { APIResponseType } from '@/types/resources';
import type { Product } from '@/types/resources/product.type';

import { useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { useQuery } from 'react-query';

import { stripeElementsOptionsLight } from '@/utils/stripe';
import { parseQuery } from '@/utils';
import { Card } from '@/components/layout';
import emptyProduct from '@/types/resources/product.type';
import Loader from '@/components/misc/Loader';
import config from '@/constants/config';

import AddonPurchaseDialog from './AddonPurchaseDialog';
import AddonCard from './AddonCard';

const stripePromise = loadStripe(config.stripeKey);

export default function AddonList() {
  const [isPurchaseDialog, togglePurchaseDialog] = useState<boolean>(false);
  const [selectedAddon, selectAddon] = useState<Product>(emptyProduct);

  const addonId = parseQuery().addonId;

  const {
    data: { data: addons } = { data: [] },
    isFetching,
    refetch,
  } = useQuery<APIResponseType<Product>>('products/addons', {
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (addonId) {
      const addon = addons.find((a) => a._id === addonId);
      if (addon) {
        selectAddon(addon);
        togglePurchaseDialog(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addonId, addons.length]);

  return (
    <>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {isFetching ? (
          <Card className="flex-center">
            <Loader />
          </Card>
        ) : (
          addons.map((addon, index) => (
            <AddonCard
              key={index}
              addon={addon}
              refetch={refetch}
              selectAddon={selectAddon}
              togglePurchaseDialog={togglePurchaseDialog}
            />
          ))
        )}
      </div>

      <Elements stripe={stripePromise} options={stripeElementsOptionsLight}>
        {selectedAddon?._id && isPurchaseDialog && (
          <AddonPurchaseDialog
            addon={selectedAddon}
            open={isPurchaseDialog}
            onOpenChange={togglePurchaseDialog}
          />
        )}
      </Elements>
    </>
  );
}
