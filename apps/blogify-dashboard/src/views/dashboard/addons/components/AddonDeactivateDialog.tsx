import type { Product } from '@/types/resources/product.type';

import { useToggle } from 'react-use';
import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import DeactivateFeedbackDialog from '@/components/dialog/DeactivateFeedbackDialog';
import Dialog from '@ps/ui/components/dialog';

export default function AddonDeactivateDialog({
  addon,
  refetch,
  ...props
}: {
  addon: Product;
  refetch: () => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [isFeedbackDialog, toggleFeedbackDialog] = useToggle(false);
  const [loading, toggleLoading] = useToggle(false);
  const [error, setError] = useState<string>('');
  const onOpenChange = props.onOpenChange || (() => {});

  const onDeactivate = () => {
    toggleFeedbackDialog();
    onOpenChange(false);
  };

  const onSubmit = (comment: string) => {
    toggleLoading();
    API.post(`products/addons/deactivate`, { id: addon._id, comment })
      .then(() => {
        refetch();
        if (props.onOpenChange) props.onOpenChange(false);
      })
      .catch(setError)
      .finally(toggleLoading);
  };

  return (
    <>
      <Dialog
        open={props.open}
        onOpenChange={props.onOpenChange}
        Icon={() => null}
        title="Deactivate Addon?"
        description="Once deactivated we will keep your addon data and settings for the next three month. After that it'll be deleted forever."
        actions={
          <>
            <Button className="bg-green" type="submit" onClick={() => props.onOpenChange(false)}>
              Keep Active
            </Button>

            <Button className="bg-red4" type="submit" onClick={onDeactivate}>
              Deactivate
            </Button>
          </>
        }
      />

      <DeactivateFeedbackDialog
        open={isFeedbackDialog}
        onOpenChange={toggleFeedbackDialog}
        description="Please take a min and tell us more about why you have chosen to deactivate this addon. This wil help us to create better a better product for you."
        loading={loading}
        error={error}
        onSubmit={onSubmit}
      />
    </>
  );
}
