import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { useContext } from 'react';
import { useParams } from 'react-router-dom';
import { useToggle } from 'react-use';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { SEOScore } from '@/components/seo';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import AuthorSelect from '@/views/dashboard/user/components/AuthorSelect';
import Publish from '@/views/dashboard/blog/publish';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { BlogSummary } from './components/BlogSummary';
import BlogAffiliateLinkGenProgress from './components/BlogAffiliateLinkGenProgress';
import BlogDetailsActions from './components/BlogDetailsActions';
import BlogPublishInfo from './components/BlogPublished';
import BlogKeywords from './components/BlogKeywords';
import BlogMetaTags from './components/BlogMetaTags';
import BlogSource from './components/BlogSource';
import BlogRating from './components/BlogRating';
import useBlog from '../hooks/useBlog';
import { BlogShare } from './components/BlogShare';

const BlogDetails = () => {
  const [isFullView, toggleFullView] = useToggle(false);
  const { slug } = useParams() as { slug: string };
  const {
    isAffiliateLinkGenInProgress,
    isProgressMessage,
    isRecoverable,
    genericError,
    isInProgress,
    isCompleted,
    isFailed,
    isPaused,
    redirect,
    refetch,
    blog,
  } = useBlog(slug);

  const abilities = useContext(UserAbilitiesContext);
  return (
    <DashboardContainer
      title="Blog Details"
      actions={
        <BlogDetailsActions
          blog={blog}
          canUseActions={isCompleted}
          isRecoverable={isRecoverable}
          isAffiliateLinkGenInProgress={isAffiliateLinkGenInProgress}
        />
      }
    >
      {isCompleted ? (
        <div className="mt-6 grid w-full grid-cols-12 gap-6 pb-2">
          <div className="col-span-full flex flex-col gap-6 lg:col-span-8">
            {blog.image && (
              <div className="flex aspect-video w-full overflow-hidden rounded-lg bg-dark flex-center">
                <img className="w-full object-cover" src={blog.image || '/images/temp-bg.jpg'} />
              </div>
            )}

            {blog.title && <div className="text-3xl font-bold">{blog.title}</div>}

            <div className="relative">
              <div
                className={cn(
                  'prose prose-base max-w-full overflow-hidden pb-5 text-black4 [&_a]:text-primary [&_a]:underline',
                  { 'h-96': !isFullView }
                )}
                dangerouslySetInnerHTML={{ __html: blog.content }}
              />
              {!isFullView && (
                <div className="absolute bottom-0 h-56 w-full bg-gradient-to-t from-white to-transparent" />
              )}
            </div>

            <div className="mb-14 flex h-14 flex-center">
              <Button variant="secondary" onClick={toggleFullView}>
                {isFullView ? <FaChevronUp /> : <FaChevronDown size={14} />}{' '}
                {isFullView ? 'Collapse' : 'Read Full Blog'}
              </Button>
            </div>

            {blog.blogOutline?.summary && <BlogSummary text={blog.blogOutline?.summary} />}
            <BlogKeywords title="Keywords" keywords={blog.keywords || []} />

            <BlogKeywords
              title="HashTags"
              keywords={blog.hashtags || blog.keywords || []}
              isHashtag
            />

            <BlogMetaTags
              title={blog.title}
              description={blog.metaDescription || ''}
              image={blog.image}
              keywords={blog.keywords || []}
            />
          </div>

          <div className="col-span-full flex flex-col gap-6 lg:col-span-4">
            <BlogAffiliateLinkGenProgress blogId={blog._id} refetch={refetch} />
            <BlogRating blog={blog} refetch={refetch} />
            <SEOScore blogTone={blog.blogTone} blogID={blog._id} />
            <div className="rounded-lg border  border-gray11  p-4">
              {abilities.blog.publish && <Publish blog={blog} refetchBlog={refetch} />}
              <BlogPublishInfo blog={blog} refetchBlog={refetch} />
            </div>
            <BlogShare blog={blog} refetchBlog={refetch} />

            <BlogSource blog={blog} />
            <AuthorSelect blogId={blog._id} selectedUserId={String(blog.uid?._id || blog.uid)} />
          </div>
        </div>
      ) : (
        <div className="flex min-h-[60vh] w-full flex-col flex-center">
          <Loader status={isFailed ? 'error' : isInProgress ? 'loading' : 'success'} />
          {isFailed ? (
            <>
              <h2 className="mb-2 text-md">Blog Generation Failed</h2>
              <span className="text-sm text-red">{blog.failReason || genericError}</span>
            </>
          ) : isInProgress ? (
            <>
              {blog._id && (
                <h2 className="text-md">
                  {isProgressMessage || 'Blog content is being generated...'}
                </h2>
              )}
            </>
          ) : isPaused ? (
            <>
              <p>
                Your blog {blog.status.includes('outline') ? 'outline' : 'content'} is ready.
                Continue with co-pilot to generate your blog content...
              </p>
              <Link to={redirect} className="mt-3">
                <Button variant="secondary">Continue</Button>
              </Link>
            </>
          ) : (
            <></>
          )}
        </div>
      )}
    </DashboardContainer>
  );
};

export default BlogDetails;
