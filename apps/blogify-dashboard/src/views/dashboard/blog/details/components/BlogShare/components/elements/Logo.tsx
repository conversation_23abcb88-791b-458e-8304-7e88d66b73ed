// بسم الله الرحمن الرحيم
import { IconType } from 'react-icons';
import { AiOutlineWeiboSquare } from 'react-icons/ai';
import { BsWhatsapp } from 'react-icons/bs';
import {
  FaFacebookSquare,
  FaTelegram,
  FaLinkedin,
  FaPinterest,
  FaTumblrSquare,
} from 'react-icons/fa';
import { FaSquareXTwitter } from 'react-icons/fa6';
import { MdEmail } from 'react-icons/md';
import { SiVk } from 'react-icons/si';
import { SharingPlatform } from '../../constants/platforms';

export type LogoProps = {
  platform: SharingPlatform;
  size?: React.ComponentPropsWithoutRef<IconType>['size'];
  className?: string;
};
export const Logo: React.FC<LogoProps> = ({ platform, size, className }) => {
  const { icon: Icon, color } = platformIcons[platform];
  return <Icon size={size} className={`${color} ${className}`} />;
};

const platformIcons: Record<SharingPlatform, { icon: IconType; color: string }> = {
  facebook: { icon: FaFacebookSquare, color: 'text-facebook' },
  whatsapp: { icon: BsWhatsapp, color: 'text-emerald-600' },
  x: { icon: FaSquareXTwitter, color: '' },
  email: { icon: MdEmail, color: 'text-primary-200' },
  telegram: { icon: FaTelegram, color: 'text-sky-500' },
  linkedin: { icon: FaLinkedin, color: 'text-linkedin' },
  pinterest: { icon: FaPinterest, color: 'text-red' },
  vk: { icon: SiVk, color: 'text-sky-500' },
  tumblr: { icon: FaTumblrSquare, color: 'text-sky-800' },
  weibo: { icon: AiOutlineWeiboSquare, color: 'text-[#e6162d]' },
} as const;
