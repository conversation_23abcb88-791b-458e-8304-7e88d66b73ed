import type { Blog } from '@ps/types';

import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';

const MAX_FEEDBACK_LENGTH = 500;

const RATINGS = ['bad', 'poor', 'okay', 'good', 'great'];

const BlogRating = ({ blog, refetch }: { blog: Blog; refetch: () => void }) => {
  const [rating, setRating] = useState(blog.rating);
  const [feedback, setFeedback] = useState('');

  const hasRating = !!rating;

  const rate = (r: number) => {
    API.patch(`blogs/${blog._id}`, { rating: r }).then(() => setRating(r));
  };

  const submitFeedback = () => {
    API.patch(`blogs/${blog._id}`, { ratingFeedback: feedback }).then(() => refetch());
  };

  return (
    <div className="rounded-lg border border-gray11">
      <div className={cn('px-4', { 'py-3': hasRating, 'py-4': !hasRating })}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h2 className="text-md font-semibold">
              {!hasRating ? 'Rate This Blog' : rating > 2 ? 'Thank You!' : 'Sorry!'}
            </h2>
            {hasRating && !blog.rating && (
              <Button
                className="!h-5 !rounded px-1.5 text-xs text-gray9"
                onClick={() => rate(0)}
                variant="gray"
              >
                UNDO
              </Button>
            )}
          </div>

          {!!rating && (
            <img className="size-8" src={`/images/icons/emojis/${RATINGS[rating - 1]}.svg`} />
          )}
        </div>
        {!hasRating && (
          <>
            <p className="mb-6 mt-2 text-md">
              Help Blogify by letting us know about the quality of this blog.
            </p>

            <div className="flex justify-around">
              {RATINGS.map((img, i) => (
                <button
                  key={i}
                  className="group flex flex-col items-center"
                  onClick={() => rate(i + 1)}
                >
                  <img
                    className="size-8 rounded-full border-2 border-transparent transition-all group-hover:hidden"
                    src={`/images/icons/emojis/${img}-gray.svg`}
                  />
                  <img
                    className="hidden size-8 rounded-full border-2 border-gray15 transition-all group-hover:block"
                    src={`/images/icons/emojis/${img}.svg`}
                  />
                  <span className="mt-1 text-xs font-medium uppercase text-gray9">{img}</span>
                </button>
              ))}
            </div>
          </>
        )}
      </div>

      {hasRating && rating < 3 && !blog.ratingFeedback && (
        <div className="border-t border-gray11 p-4">
          <p className="pb-4 text-md">Please let us know what went wrong.</p>

          <div className="relative">
            <textarea
              className="block h-40 w-full rounded-lg border border-gray10 bg-bg2 p-3 outline-none focus:border-primary"
              onChange={(ev) => setFeedback(ev.target.value)}
              maxLength={MAX_FEEDBACK_LENGTH}
            />
            <span className="absolute bottom-3 right-3 text-xs text-gray9">
              {feedback.length} / {MAX_FEEDBACK_LENGTH}
            </span>
          </div>

          <Button className="mt-4 w-full" onClick={submitFeedback}>
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

export default BlogRating;
