// بسم الله الرحمن الرحيم

import { IntegrationSite } from '@ps/types/misc/integration.type';
import { createContext, ReactNode, useContext } from 'react';
import { SharingPlatform, SharingPlatforms } from '../constants/platforms';
import { useQuery } from 'react-query';

type SocialPlatformDetailsContextType = Record<
  SharingPlatform,
  {
    isActive: null | boolean;
    sites?: IntegrationSite[];
    characterLimit?: number;
  }
>;

const initialContext = Object.fromEntries(
  SharingPlatforms.map(
    (platform) =>
      [
        platform,
        {
          isActive: null,
          characterLimit: 1200,
        },
      ] as const
  )
) as SocialPlatformDetailsContextType;

const SocialPlatformDetailsContext =
  createContext<SocialPlatformDetailsContextType>(initialContext);

// eslint-disable-next-line react-refresh/only-export-components
export function useSocialPlatformDetailsContext() {
  return useContext(SocialPlatformDetailsContext);
}

export function SocialPlatformDetailsContextProvider({ children }: { children: ReactNode }) {
  const { data } =
    useQuery<Pick<SocialPlatformDetailsContextType, 'linkedin' | 'x'>>('/integrations/socials');
  const value = { ...initialContext, ...data };
  return (
    <SocialPlatformDetailsContext.Provider value={value}>
      {children}
    </SocialPlatformDetailsContext.Provider>
  );
}
