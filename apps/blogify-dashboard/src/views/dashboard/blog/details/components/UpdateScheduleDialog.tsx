import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import DateTimePicker from '../../publish/DateTimePicker';
import dayjs from 'dayjs';
import { useState } from 'react';
import { FaRegClock } from 'react-icons/fa';
import { useMutation } from 'react-query';
import { API } from '@/services/api';
import { useParams } from 'react-router-dom';
import toast from 'react-hot-toast';

type PropsType = {
  isOpen: boolean;
  onClose: () => void;
  dateTime: string;
  platform: string;
  refetchBlog: () => void;
};

const UpdateScheduleDialog = ({ isOpen, onClose, dateTime, platform, refetchBlog }: PropsType) => {
  const [publishAt, setPublishAt] = useState(dayjs(dateTime).format('YYYY-MM-DDTHH:mm'));
  const { slug } = useParams();

  const payload = {
    platform: platform,
    oldTimestamp: dateTime,
    newTimestamp: publishAt,
  };

  const { mutateAsync: updateSchedule, isLoading: updating } = useMutation({
    mutationFn: () =>
      API.patch(`blogs/${slug}/publish-schedule`, payload).then(() => {
        onClose();
        refetchBlog();
        toast.success('Schedule updated successfully');
      }),
  });

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      PrimaryIcon={FaRegClock}
      title="Update Schedule"
      description="Here, you can update your scheduled time and date. Note that you can not set a time before your current time and date."
      primaryButton={{
        label: updating ? 'Updating..' : 'Update Schedule',
        onClick: updateSchedule,
        disabled: updating,
        loading: updating,
      }}
      secondaryButton={{
        label: 'Cancel',
        onClick: onClose,
      }}
      className="max-w-sm"
    >
      <DateTimePicker
        value={publishAt}
        minimum={dayjs().add(10, 'minutes').format('YYYY-MM-DDTHH:mm')}
        onChange={setPublishAt}
        className="pb-4"
      />
    </Dialog>
  );
};

export default UpdateScheduleDialog;
