// بسم الله الرحمن الرحيم

import { Textarea } from '@ps/ui/components/textarea';
import { ComponentPropsWithoutRef, Fragment, ReactNode } from 'react';
import { useSocialPlatformDetailsContext } from '../../../../../../contexts/SocialPlatformDetailsContext';
import { useScreenContext } from '../../Screens';
import Header from '../elements/Header';
import Preview from './Preview';
import GrayText from '../../../../../elements/GrayText';
import { Button } from '@ps/ui/components/button';
import { GrPowerCycle } from 'react-icons/gr';
import { MdEdit } from 'react-icons/md';
import { IoMdEye } from 'react-icons/io';
import { generatePost } from '../../../../../../functions';
import { useBlogContext } from '@/context/BlogContext';
import toast from 'react-hot-toast';

type PostScreenProps = {
  children: ReactNode;
  className?: string;
};
export default function PostScreen({ children, className }: PostScreenProps) {
  return <section className={`divide-y-2 p-2 *:p-2 ` + className}>{children}</section>;
}

PostScreen.Header = function Head() {
  return <Header showConnect={false} />;
};

PostScreen.Preview = Preview;

PostScreen.Editor = function Editor() {
  const [{ platform, text }, dispatch] = useScreenContext();
  const { characterLimit } = useSocialPlatformDetailsContext()[platform];
  return (
    <Textarea
      maxLength={characterLimit}
      value={text}
      onChange={({ target: { value } }) => dispatch({ type: 'setText', payload: value })}
    ></Textarea>
  );
};

PostScreen.Caption = function Caption() {
  const [{ text }] = useScreenContext();
  return (
    <p className="text-pretty">
      {text.split('\n').map((shard) => (
        <Fragment key={shard}>
          {shard}
          <br />
        </Fragment>
      ))}
    </p>
  );
};

type ScheduleProps = {
  timestamp: string;
  setTimestamp: React.Dispatch<React.SetStateAction<string>>;
};
PostScreen.Schedule = function Schedule({ timestamp, setTimestamp }: ScheduleProps) {
  return (
    <div className="space-y-1">
      <GrayText text="schedule" />
      <input
        className="h-10 w-full rounded-lg border border-solid border-gray10 px-4 invalid:border-red invalid:text-red"
        type="datetime-local"
        value={timestamp}
        onChange={({ target: { value } }) => setTimestamp(value)}
      />
    </div>
  );
};

PostScreen.RegenerateButton = function RegenerateButton() {
  const [{ platform, form }, dispatch] = useScreenContext();
  const { _id: blogID } = useBlogContext();
  const onClick = () =>
    toast.promise(
      generatePost({ blogID, ...form, platform }).then((data) =>
        dispatch({ type: 'setText', payload: data!.socialMediaPost })
      ),
      {
        loading: 'Regenerating post...',
        success: 'Generated!',
        error: 'Failed to regenerate post',
      }
    );
  return (
    <Button variant="outline" onClick={onClick}>
      <GrPowerCycle />
      <GrayText text="Regenerate" />
    </Button>
  );
};

PostScreen.EditButton = function EditButton({
  onClick,
}: {
  onClick: ComponentPropsWithoutRef<typeof Button>['onClick'];
}) {
  return (
    <Button variant="outline" onClick={onClick}>
      <MdEdit />
      <GrayText text="Edit" />
    </Button>
  );
};

PostScreen.ViewButton = function ViewButton({
  onClick,
}: {
  onClick: ComponentPropsWithoutRef<typeof Button>['onClick'];
}) {
  return (
    <Button variant="outline" onClick={onClick}>
      <IoMdEye />
      <GrayText text="View" />
    </Button>
  );
};

PostScreen.CharacterCount = function CharacterCount() {
  const [{ text, platform }] = useScreenContext();
  const { characterLimit } = useSocialPlatformDetailsContext()[platform];
  return <GrayText text={`${text.length} / ${characterLimit}`} />;
};
