// بسم الله الرحمن الرحيم

import type { Integration } from '@/types/misc/integration.type';
import type { Blog } from '@ps/types';
import { useContext, useEffect, useState } from 'react';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { getQueued } from '../../publish/utils';
import BlogPublishInfoCard from './BlogPublishInfoCard';

type PublishedStateData = {
  integration: string;
  siteID?: string;
  visibility?: 'visible' | 'hidden';
  outcome:
    | {
        link: string;
        time: string;
      }
    | {
        error: string;
        time: string;
      };
};
type QueuedStateData = {
  integration: string;
  siteID?: string;
  timestamp: string;
};
type PublishState = {
  state:
    | { kind: 'published'; data: PublishedStateData }
    | { kind: 'publishing' | 'scheduled'; data: QueuedStateData };
};

const BlogPublishInfo: React.FC<{ blog: Blog; refetchBlog: () => void }> = ({
  blog,
  refetchBlog,
}) => {
  const integrationDetails = useContext(IntegrationDetailsContext);
  const [publishStatesData, setPublishStatesData] = useState<PublishState[]>([]);

  useEffect(() => {
    let publishStates: PublishState[] = [];

    if (blog.publishResult) {
      publishStates = publishStates
        .concat(
          blog.publishResult.map((result) => ({ state: { kind: 'published', data: result } }))
        )
        .concat(
          getQueued(blog.platforms, blog!.publishResult).map((request) => ({
            state: {
              kind:
                request.timestamp === '' || new Date(request.timestamp) < new Date()
                  ? ('publishing' as const)
                  : ('scheduled' as const),
              data: request,
            },
          }))
        );
    } else {
      const isPublished = (platform: Integration) => !!blog[`${platform}PublishTime`];
      const publishedPlatforms = Object.keys(integrationDetails).filter((platform) =>
        isPublished(platform as Integration)
      ) as Integration[];
      publishStates = publishStates.concat(
        publishedPlatforms.map((platform): (typeof publishStates)[number] => ({
          state: {
            kind: 'published',
            data: {
              integration: platform,
              outcome: { link: blog[`${platform}Link`], time: blog[`${platform}PublishTime`] },
            },
          },
        }))
      );
    }
    setPublishStatesData(publishStates);
  }, [blog, integrationDetails]);

  return (
    <>
      <div className="mt-8 space-y-4">
        <div className="space-y-2">
          {publishStatesData
            .filter(({ state }) => state.kind !== 'publishing')
            .map((s) => (
              <BlogPublishInfoCard
                state={s.state}
                key={JSON.stringify(s.state)}
                refetchBlog={refetchBlog}
              />
            ))}
        </div>
      </div>
    </>
  );
};

export default BlogPublishInfo;
