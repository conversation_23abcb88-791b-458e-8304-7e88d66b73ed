// بسم الله الرحمن الرحيم

import { useCallback, useState } from 'react';
import PostScreen from './PostScreen';
import { GrShare } from 'react-icons/gr';
import { Button } from '@ps/ui/components/button';
import { getSharerURL, publishSocial } from '../../../../../../functions';
import { useBlogContext } from '@/context/BlogContext';
import { useScreenContext } from '../../Screens';
import { MdSchedule } from 'react-icons/md';
import { useSocialPlatformDetailsContext } from '../../../../../../contexts/SocialPlatformDetailsContext';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

export default function DisplayPost() {
  const [{ platform }] = useScreenContext();
  const { isActive } = useSocialPlatformDetailsContext()[platform];

  return isActive ? <DisplayPost.Publish /> : <DisplayPost.WebShare />;
}

function useWebShare() {
  const [
    {
      platform,
      text,
      form: { link },
    },
  ] = useScreenContext();
  const blog = useBlogContext();
  return () => {
    window.open(
      getSharerURL(platform, {
        content: text,
        image: blog.image,
        link: link,
        tags: blog.keywords,
        title: blog.title,
      }),
      '_blank'
    );
  };
}

DisplayPost.WebShare = function WebShare() {
  const webShare = useWebShare();
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  return (
    <PostScreen>
      <PostScreen.Header />
      <div className="rounded-lg border border-gray10">
        {mode === 'view' ? <PostScreen.Caption /> : <PostScreen.Editor />}
      </div>
      <div className="flex items-center justify-between gap-2">
        <div className="space-x-2">
          <PostScreen.RegenerateButton />
          {mode === 'view' ? (
            <PostScreen.EditButton onClick={() => setMode('edit')} />
          ) : (
            <PostScreen.ViewButton onClick={() => setMode('view')} />
          )}
        </div>
        <div>
          <PostScreen.CharacterCount />
        </div>
      </div>
      <Button className="w-full" onClick={webShare}>
        <GrShare />
        Share
      </Button>
    </PostScreen>
  );
};

DisplayPost.Publish = function Publish() {
  const { _id: blogID } = useBlogContext();
  const [{ platform, text }] = useScreenContext();
  const [timestamp, setTimestamp] = useState(() => dayjs().format('YYYY-MM-DDTHH:mm'));
  const onClick = useCallback(() => {
    toast.promise(publishSocial({ blogID, platform: platform as any, text, timestamp }), {
      loading: 'Processing...',
      success: 'Complete!',
      error: 'Failed',
    });
  }, [blogID, platform, text, timestamp]);
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  return (
    <PostScreen>
      <PostScreen.Header />
      <div className="rounded-lg border border-gray10">
        {mode === 'view' ? <PostScreen.Preview /> : <PostScreen.Editor />}
      </div>
      <div className="flex items-center justify-between gap-2">
        <div className="space-x-2">
          <PostScreen.RegenerateButton />
          {mode === 'view' ? (
            <PostScreen.EditButton onClick={() => setMode('edit')} />
          ) : (
            <PostScreen.ViewButton onClick={() => setMode('view')} />
          )}
        </div>
        <div>
          <PostScreen.CharacterCount />
        </div>
      </div>
      <PostScreen.Schedule timestamp={timestamp} setTimestamp={setTimestamp} />
      <Button className="w-full" onClick={onClick}>
        <MdSchedule />
        Publish
      </Button>
    </PostScreen>
  );
};
