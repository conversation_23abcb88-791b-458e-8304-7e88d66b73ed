import type { Integration } from '@/types/misc/integration.type';
import type { Blog } from '@ps/types';

import { FaPaperPlane } from 'react-icons/fa6';
import { useToggle } from 'react-use';
import { Formik } from 'formik';
import toast from 'react-hot-toast';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Tabs from '@ps/ui/components/tabs';

import {
  IntegrationTrigger,
  PublishSettings,
} from '../../create/components/BlogCreatePublishSettings';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { useContext } from 'react';

const BlogPublish: React.FC<{ blog: Blog }> = ({ blog }) => {
  const integrationsData = useContext(IntegrationDetailsContext);

  const disconnected = Object.keys(integrationsData ?? {}).filter(
    (int) => !integrationsData[int].connected
  );
  const connected = Object.keys(integrationsData ?? {}).filter(
    (int) => integrationsData[int].connected
  );
  const integrations = [...connected, ...disconnected];
  const [isPublishing, togglePublishing] = useToggle(false);

  const isPublished = (platform: Integration) => !!blog[`${platform}PublishTime`];

  const publish = ({ platforms }: { platforms: Blog['platforms'] }) => {
    const promises = platforms.map((p) => API.post(`blogs/${blog._id}/publish`, { platform: p }));

    togglePublishing();
    Promise.all(promises)
      .then(() => toast.success('Queued for publishing!'))
      .catch(() => toast.error('Failed to publish.'))
      .finally(togglePublishing);
  };

  return (
    <div className="rounded-lg border border-gray11 p-4">
      <h2 className="text-md font-semibold">Publish</h2>

      <p className="mt-2 text-md">Publish this blog to your connected platforms.</p>

      <h5 className="mb-2 mt-6 text-xs font-semibold uppercase tracking-widest text-gray9">
        CONNECTED PLATFORMS
      </h5>

      <Formik initialValues={{ platforms: [] }} onSubmit={publish}>
        {(form) => (
          <form
            onSubmit={(ev) => {
              ev.preventDefault();
              form.submitForm();
            }}
          >
            <Tabs.Root>
              {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
              <Tabs.List className="h-scroll mb-4 flex gap-3 pb-1">
                {integrations.map((integration, i) => (
                  <IntegrationTrigger
                    key={i}
                    integration={integration}
                    isConnected={connected.includes(integration)}
                  />
                ))}
              </Tabs.List>

              {integrations.map((integration, i) => (
                <PublishSettings
                  key={i}
                  integration={integration}
                  isConnected={connected.includes(integration)}
                >
                  <Button type="submit" className="w-full" loading={isPublishing}>
                    <FaPaperPlane />
                    {isPublished(integration as Integration) ? 'Republish' : 'Publish'}
                  </Button>
                </PublishSettings>
              ))}
            </Tabs.Root>
          </form>
        )}
      </Formik>
    </div>
  );
};

export default BlogPublish;
