// بسم الله الرحمن الرحيم
import { BiCopy } from 'react-icons/bi';

import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';

export function BlogSummary({ text }: { text: string }) {
  return (
    <div className="rounded-lg border border-gray15 bg-bg2 p-6">
      <h3 className="text-lg font-semibold">Summary</h3>

      <div className="my-6 text-pretty text-justify text-md">{text}</div>

      <Button className="flex gap-2" onClick={() => copy(text ?? '')}>
        <BiCopy />
        Copy
      </Button>
    </div>
  );
}
