// بسم الله الرحمن الرحيم

import { IconType } from 'react-icons';
import { AiOutlineShareAlt } from 'react-icons/ai';
import { BiLike, BiComment } from 'react-icons/bi';
import { useScreenContext } from '../../Screens';

export default function Preview() {
  const [{ text }] = useScreenContext();
  return (
    <article className="space-y-2 p-2">
      <section className="flex items-center gap-2">
        <img src="/images/blogify.svg" className="size-12" />
        <div className="*:block">
          <span className="font-medium">Blogify</span>
          <span className="text-xs text-gray9">225 Followers</span>
        </div>
      </section>
      <p className="text-pretty text-sm">{text}</p>
      <section className="flex items-center justify-around gap-2">
        {Object.entries(icons).map(([name, icon]) => (
          <Action key={name} kind={name} icon={icon} />
        ))}
      </section>
    </article>
  );
}

const icons: Record<string, IconType> = {
  like: BiLike,
  comment: BiComment,
  share: AiOutlineShareAlt,
} as const;
type ActionProps = { kind: string; icon: IconType };
function Action({ kind, icon: Icon }: ActionProps) {
  return (
    <button type="button" disabled className="flex items-center justify-center gap-1">
      <Icon />
      <span className="text-xs capitalize">{kind}</span>
    </button>
  );
}
