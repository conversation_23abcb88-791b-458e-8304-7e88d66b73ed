import { useEffect, useState } from 'react';

import { blogEvents } from '@/services/event';
import { Progress } from '@ps/ui/components/progress';
import useAnimate from '@ps/ui/hooks/useAnimate';

export default function BlogAffiliateLinkGenProgress({
  blogId,
  refetch,
}: {
  blogId: string;
  refetch: () => void;
}) {
  const [progress, setProgress] = useState<number>(0);
  const [animatedProgress] = useAnimate({ value: progress, step: 3 });

  useEffect(() => {
    const subscription = blogEvents.subscribe(
      'BLOG_AFFILIATE_LINK_GENERATION_STATUS_UPDATE',
      ({ _id, percentComplete, affiliateLinkGenerationStatus }) => {
        if (blogId !== _id || !affiliateLinkGenerationStatus) return;

        // Use percentComplete if available, otherwise fallback to the status-based progress
        if (percentComplete !== undefined) {
          setProgress(percentComplete);
          if (percentComplete === 10) refetch();
        } else {
          // Fallback to previous implementation for backward compatibility
          const progressMap: { [key: string]: number } = {
            affiliate_keywords_generating: 15,
            affiliate_keywords_generated: 30,
            affiliate_product_matching: 45,
            affiliate_product_matched: 60,
            affiliate_link_generating: 80,
            affiliate_link_generated: 100,
          };

          if (affiliateLinkGenerationStatus in progressMap) {
            setProgress(progressMap[affiliateLinkGenerationStatus]);
            if (progressMap[affiliateLinkGenerationStatus] === 100) refetch();
          }
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [blogId, refetch]);

  if (progress === 100 || progress === 0) return null;

  return (
    <div className="rounded-lg bg-primary/5 p-4">
      <h3 className="text-17 font-semibold">Generating Affiliate Link...</h3>
      <p className="text-15">
        You'll be able to edit & publish your blog once all the links are generated.
      </p>
      <div className="mb-2 mt-4 flex items-center justify-between">
        <span className="text-15 font-semibold">{progress}%</span>
      </div>
      <Progress value={animatedProgress} />
    </div>
  );
}
