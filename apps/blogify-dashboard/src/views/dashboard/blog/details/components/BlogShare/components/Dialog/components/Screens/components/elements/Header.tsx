// بسم الله الرحمن الرحيم

import { Logo } from '../../../../../elements/Logo';
import PlatformName from '../../../../../elements/PlatformName';
import ConnectStatus from '../ShareForm/components/ConnectStatus';
import { useScreenContext } from '../../Screens';

type HeaderProps = {
  showConnect: boolean;
};

export default function Header({ showConnect }: HeaderProps) {
  const [{ platform }] = useScreenContext();
  return (
    <section className="flex items-center justify-between gap-2">
      <div className="flex items-center justify-start gap-2">
        <Logo className="size-6" platform={platform} />
        <PlatformName name={platform} />
      </div>
      <ConnectStatus showConnectButton={showConnect} platform={platform} />
    </section>
  );
}
