// بسم الله الرحمن الرحيم
import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/dialog';
import { AiFillWarning } from 'react-icons/ai';

export default function UnpublishedDialog({
  toggleOpen,
  isOpen,
}: {
  toggleOpen: (state: boolean) => void;
  isOpen: boolean;
}) {
  return (
    <Dialog
      open={isOpen}
      onOpenChange={toggleOpen}
      Icon={AiFillWarning}
      title="You can't share unless you publish first!"
      description="Kindly publish your blog to one of the publishing platforms above before sharing it."
      actions={<Button onClick={() => toggleOpen(false)}>Close</Button>}
    />
  );
}
