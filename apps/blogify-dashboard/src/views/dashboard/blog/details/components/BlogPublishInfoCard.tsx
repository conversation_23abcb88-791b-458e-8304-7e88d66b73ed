// بسم الله الرحمن الرحيم

import { Button } from '@ps/ui/components/button';
import UpdateScheduleDialog from './UpdateScheduleDialog';
import { formatDate, formatTime } from '@/utils/date';
import { FaClock, FaPaperPlane } from 'react-icons/fa';
import { LuExternalLink } from 'react-icons/lu';
import { IntegrationIcon } from '../../publish/PlatformIcon';
import { useContext } from 'react';
import { Blog } from '@ps/types';
import { getQueued } from '../../publish/utils';
import { useToggle } from 'react-use';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import DeleteDialog from '@ps/ui/components/common/dialogs/DeleteDialog';
import { useMutation } from 'react-query';
import { useParams } from 'react-router-dom';
import { API } from '@/services/api';
import toast from 'react-hot-toast';

const BlogPublishInfoCard = ({
  state,
  refetchBlog,
}: Readonly<{
  state:
    | { kind: 'published'; data: NonNullable<Blog['publishResult']>[number] }
    | { kind: 'publishing' | 'scheduled'; data: ReturnType<typeof getQueued>[number] };
  refetchBlog: () => void;
}>) => {
  const [updateScheduleDialogOpen, toggleScheduleDialog] = useToggle(false);
  const [deleteDialogOpen, toggleDeleteDialog] = useToggle(false);
  const { integration, siteID } = state.data;
  const { name, sites } = useContext(IntegrationDetailsContext)?.[integration] || {};
  const site = sites?.find(({ id }) => id === siteID);
  const timeZone = new Date()
    .toLocaleTimeString(undefined, { timeZoneName: 'short' })
    .split(' ')[2];
  const { slug } = useParams();

  const payload = {
    platform: state.data.integration,
    ...(state.kind !== 'published' && { timestamp: state.data.timestamp }),
  };

  const { mutateAsync: deleteSchedule, isLoading: deleting } = useMutation({
    mutationFn: () =>
      API.post(`blogs/${slug}/delete-publish-schedule`, payload).then(() => {
        toggleDeleteDialog();
        refetchBlog();
        toast.success('Schedule deleted successfully');
      }),
  });

  return (
    <article className="flex flex-col gap-2 rounded-lg border border-gray11 p-2">
      <section className="flex place-items-center justify-between gap-2">
        <div className="flex place-items-center gap-2">
          <IntegrationIcon integration={integration} className="size-6 rounded-lg p-1" />
          <div className="flex flex-col justify-around">
            <span className="text-sm font-semibold uppercase">{name}</span>
          </div>
        </div>
        {state.kind === 'published'
          ? 'link' in state.data.outcome && (
              <a
                className="flex items-center gap-1 text-xs text-gray9 hover:underline"
                href={state.data.outcome.link}
                target="_blank"
                rel="noopener noreferrer"
              >
                <LuExternalLink size={15} className="text-gray-500 hover:text-gray-700" />
              </a>
            )
          : null}
      </section>
      {site && (
        <section className="flex flex-col gap-1 px-1">
          <span className="text-wrap font-semibold">{site.name}</span>
          <a
            className="truncate text-sm text-gray9 hover:underline"
            href={`https://${site.url}`}
            target="_blank"
          >
            {site.url}
          </a>
        </section>
      )}
      <div className="px-1">
        {state.kind === 'scheduled' ? (
          <section className="flex flex-col gap-1">
            <label className="space-x-1">
              <span className="text-sm uppercase text-gray9">Scheduled For</span>
              {timeZone && <span className="text-xs text-gray9">({timeZone})</span>}
            </label>
            <div className="rounded-lg border border-solid border-gray10 bg-gray-100 p-2 px-4">
              <span className="flex place-items-center gap-4">
                <FaClock />
                <span className="text-sm font-semibold">
                  {formatDate(state.data.timestamp) + ' ' + formatTime(state.data.timestamp)}
                </span>
              </span>
            </div>
            <div className="mb-1 mt-2 flex gap-2">
              <UpdateScheduleDialog
                isOpen={updateScheduleDialogOpen}
                onClose={toggleScheduleDialog}
                dateTime={state.data.timestamp}
                platform={state.data.integration}
                refetchBlog={refetchBlog}
              />
              <DeleteDialog
                isOpen={deleteDialogOpen}
                onClose={toggleDeleteDialog}
                title="Delete Schedule"
                description="If deleted the blog will not be published as scheduled. You can again set a new schedule or publish directly to your connected platform."
                primaryButton={{
                  label: 'Delete Schedule',
                  onClick: deleteSchedule,
                  disabled: deleting,
                  loading: deleting,
                }}
              />

              <Button className="w-1/2" variant="outline" onClick={toggleScheduleDialog}>
                Update
              </Button>
              <Button className="w-1/2" variant="danger" onClick={toggleDeleteDialog}>
                Delete
              </Button>
            </div>
          </section>
        ) : state.kind === 'publishing' ? (
          <span className="flex animate-pulse gap-4 text-primary">
            <FaPaperPlane />
            <span className="font-semibold">Publishing...</span>
          </span>
        ) : state.kind === 'published' ? (
          <section className="flex flex-col gap-2">
            {!('error' in state.data.outcome) ? (
              <p className="truncate font-inter text-sm text-[#144cb8]">
                <a href={state.data.outcome.link} target="_blank">
                  {state.data.outcome.link}
                </a>
              </p>
            ) : (
              <span className="truncate font-ibx text-red">Error: {state.data.outcome.error}</span>
            )}
            {/* <span className="text-right text-xs text-gray-500">
              Completed {toLocalizedRelativeTime(state.data.outcome.time)}
            </span> */}
          </section>
        ) : null}
      </div>
    </article>
  );
};

export default BlogPublishInfoCard;
