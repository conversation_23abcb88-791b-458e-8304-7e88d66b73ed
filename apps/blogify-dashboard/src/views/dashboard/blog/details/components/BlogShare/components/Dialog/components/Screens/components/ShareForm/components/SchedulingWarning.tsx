// بسم الله الرحمن الرحيم

import { IoMdWarning } from 'react-icons/io';
import { useSocialPlatformDetailsContext } from '@/views/dashboard/blog/details/components/BlogShare/contexts/SocialPlatformDetailsContext';
import { SharingPlatform } from '@/views/dashboard/blog/details/components/BlogShare/constants/platforms';
import { capitalize } from '@/utils/string';
import { useScreenContext } from '../../../Screens';
import { ConnectButton } from './ConnectStatus';

const message = (isActive: null | false, platform: SharingPlatform) =>
  (isActive === false
    ? `To schedule a post please connect your ${capitalize(platform)} account with Blogify.`
    : "Sorry! We don't yet support post scheduling for the social platform you've selected.") +
  ' However, you can still generate a post & share it via web share.';

export default function SchedulingWarning() {
  const [{ platform }] = useScreenContext();
  const { isActive } = useSocialPlatformDetailsContext()[platform];
  return isActive === true ? null : (
    <section className="flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <IoMdWarning className="size-4 text-gold" />
        <span className="font-medium">Post Scheduling</span>
      </div>
      <span>{message(isActive, platform)}</span>
      {isActive === false && <ConnectButton platform={platform} />}
    </section>
  );
}
