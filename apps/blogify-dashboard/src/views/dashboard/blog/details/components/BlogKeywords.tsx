import { capitalize } from '@/utils/string';
import Expandable from '@/components/layout/Expandable';

const capitalizeAndFormat = (inputString = '', isHashtag = false) => {
  const words = inputString.split(' ');
  const capitalizedWords = words.map(capitalize);
  const outputString = capitalizedWords.join('');

  return isHashtag ? `#${outputString}` : outputString;
};

const BlogKeywords = ({
  title,
  isHashtag = false,
  keywords = [],
}: {
  title: string;
  keywords: string[];
  isHashtag?: boolean;
}) => {
  const formattedKeywords = keywords
    .sort(() => Math.random() - 0.5)
    .map((k) => capitalizeAndFormat(k, isHashtag));

  const copyContent = formattedKeywords.join(', ');
  const isGenerating = !keywords.length;

  return (
    <div className="rounded-lg border border-gray10 p-6">
      <h3 className="mb-6 text-lg font-semibold">
        {title} {!isGenerating && <span>({keywords.length})</span>}
      </h3>

      {!isGenerating ? (
        <Expandable copyContent={copyContent}>
          {formattedKeywords.map((k, i) => (
            <Chip key={i} value={k} />
          ))}
        </Expandable>
      ) : (
        <div className="text-md">Generating Keywords...</div>
      )}
    </div>
  );
};

const Chip: React.FC<{ value: string }> = ({ value }) => (
  <div className="mb-2 mr-2 inline-block rounded-md bg-bg2 px-3 py-1">{value}</div>
);

export default BlogKeywords;
