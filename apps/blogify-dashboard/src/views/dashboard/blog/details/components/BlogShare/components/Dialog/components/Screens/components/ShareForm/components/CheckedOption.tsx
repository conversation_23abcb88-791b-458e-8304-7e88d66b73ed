// بسم الله الرحمن الرحيم

import { Checkbox } from '@ps/ui/components/checkbox';

type CheckedOptionProps = {
  label: string;
  onChange: (b: boolean) => void;
};
export default function CheckedOption({ label, onChange }: CheckedOptionProps) {
  return (
    <label className="flex gap-2">
      <Checkbox onCheckedChange={(v) => onChange(v as boolean)} />
      <span className="font-medium capitalize">{label}</span>
    </label>
  );
}
