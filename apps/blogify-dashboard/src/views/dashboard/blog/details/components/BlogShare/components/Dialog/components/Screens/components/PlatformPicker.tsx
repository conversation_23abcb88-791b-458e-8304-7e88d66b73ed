// بسم الله الرحمن الرحيم

import { useCallback } from 'react';
import { SharingPlatform, SharingPlatforms } from '../../../../../constants/platforms';
import { Logo } from '../../../../elements/Logo';
import { useScreenContext } from '../Screens';

export default function PlatformPicker() {
  const [, dispatch] = useScreenContext();
  const onSelect = useCallback(
    (platform: SharingPlatform) => {
      dispatch({ type: 'setPlatform', payload: platform });
      dispatch({ type: 'setScreen', payload: 'form' });
    },
    [dispatch]
  );
  return (
    <article className="flex flex-wrap gap-5">
      {SharingPlatforms.map((platform) => (
        <button
          onClick={() => onSelect(platform)}
          type="button"
          key={platform}
          className="flex flex-col place-items-center gap-0.5"
        >
          <Logo platform={platform} className="size-12 rounded-md py-1.5" />
          <span className="w-14 truncate text-center text-xs font-medium capitalize">
            {platform}
          </span>
        </button>
      ))}
    </article>
  );
}
