// بسم الله الرحمن الرحيم

import { SharingPlatform } from '@/views/dashboard/blog/details/components/BlogShare/constants/platforms';
import { getConnectUrl } from '@/utils/integration';
import { FaRegDotCircle } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { Button } from '@ps/ui/components/button';
import { useSocialPlatformDetailsContext } from '@/views/dashboard/blog/details/components/BlogShare/contexts/SocialPlatformDetailsContext';

type ConnectedStatusProps = {
  showConnectButton: boolean;
  platform: SharingPlatform;
};
export default function ConnectStatus({
  showConnectButton: showConnect = false,
  platform,
}: ConnectedStatusProps) {
  const { isActive } = useSocialPlatformDetailsContext()[platform];
  return isActive === null ? null : isActive === true ? (
    <ConnectedStatus />
  ) : showConnect ? (
    <ConnectButton platform={platform} />
  ) : null;
}
type ConnectButtonProps = {
  platform: SharingPlatform;
};
export function ConnectButton({ platform }: ConnectButtonProps) {
  return (
    <Link to={getConnectUrl(platform)}>
      <Button variant="outline" className="text-xs uppercase text-gray9">
        Connect
      </Button>
    </Link>
  );
}

function ConnectedStatus() {
  return (
    <span className="flex items-center gap-1 text-sm font-medium uppercase text-primary">
      <FaRegDotCircle />
      Connected
    </span>
  );
}
