// بسم الله الرحمن الرحيم
import type { Blog } from '@ps/types';

import { useNavigate } from 'react-router-dom';
import { BsStars } from 'react-icons/bs';

import {
  getVideoIdFromYouTubeUrl,
  getVideoIdFromVimeoUrl,
  getPodBeanIdFromUrl,
  isValidSourceUrl,
} from '@/utils/url';
import { cacheSet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import { isUrl } from '@/utils';
import Expandable from '@/components/layout/Expandable';
import Link from '@/components/common/Link';

import useBlogSource from '../../context/useBlogSource';

const BLOG_FIELD_MAP: Partial<Record<keyof Blog, string>> = {
  generationMode: 'Mode',
  pov: 'POV',
  blogTone: 'Tone',
  blogSize: 'Size',
  inputLanguage: 'Source Language',
  blogLanguage: 'Blog Language',
};

const BLOG_FIELDS = Object.keys(BLOG_FIELD_MAP) as (keyof Blog)[];

const BlogSource = ({ blog }: { blog: Blog }) => {
  const { sources } = useBlogSource();
  const navigate = useNavigate();

  const { url, prompt, sourceName, sourceType } = blog;
  const source = url ? url : prompt;
  const isSourceUrl = isUrl(source);
  const title = isSourceUrl ? 'Source' : 'Prompt';

  const toBlogCreate = () => {
    cacheSet('new-blog', {
      url: blog.url,
      prompt: blog.prompt,
      sourceType: blog.sourceType,
      sourceName: blog.sourceName,
      customInstruction: blog.customInstruction,
      ...BLOG_FIELDS.reduce((r, f) => ((r[f] = blog[f]), r), {} as Record<string, any>),
    });

    return navigate('/dashboard/blogs/select-source/create', { state: blog.sourceName });
  };

  return (
    <div className="rounded-lg border border-gray10 p-4">
      <div className="flex items-center gap-2">
        <img className="size-6" src="/images/icons/stars.svg" />
        <h2 className="text-md font-semibold">Prompt</h2>
      </div>

      <p className="mt-2 text-md">Source and settings you used to generate this blog.</p>

      <h5 className="mt-6 text-xs font-semibold uppercase tracking-widest text-gray9">{title}</h5>

      <div className="mt-2 overflow-auto text-sm">
        {isSourceUrl ? (
          <>
            {sourceName === 'YouTube' || isValidSourceUrl(source, 'YouTube') ? (
              <iframe
                className="aspect-video rounded-lg border-none"
                src={`https://www.youtube.com/embed/${getVideoIdFromYouTubeUrl(source)}`}
                allowFullScreen
                width="100%"
              />
            ) : sourceName === 'Vimeo' || isValidSourceUrl(source, 'Vimeo') ? (
              <>
                <iframe
                  className="aspect-video rounded-lg border-none bg-dark"
                  src={`https://player.vimeo.com/video/${getVideoIdFromVimeoUrl(source)}?h=366bb26e97&title=0&byline=0&portrait=0`}
                  allow="autoplay; fullscreen; picture-in-picture"
                  allowFullScreen
                  width="100%"
                />
                <script src="https://player.vimeo.com/api/player.js"></script>
              </>
            ) : sourceName === 'Rumble' && url.includes('/embed/') ? (
              <iframe
                className="aspect-video rounded-lg border-none bg-dark"
                allowFullScreen
                width="100%"
                src={url}
              />
            ) : sourceName === 'Apple Podcasts' ? (
              <iframe
                className="w-full overflow-hidden rounded-lg"
                sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-storage-access-by-user-activation allow-top-navigation-by-user-activation"
                src={`https://embed.${url.replace('https://', '')}`}
                allow="autoplay *; encrypted-media *; fullscreen *; clipboard-write"
                height="175px"
              />
            ) : sourceName === 'PodBean' ? (
              <iframe
                className="aspect-video rounded-lg border-none bg-dark"
                src={`https://www.podbean.com/player-v2/?from=embed&i=${getPodBeanIdFromUrl(url)}-pb&square=1&share=1&download=1&fonts=Arial&skin=1&font-color=&rtl=0&logo_link=&btn-skin=7&size=300`}
                data-name="pb-iframe-player"
                allowFullScreen
                loading="lazy"
                width="100%"
              ></iframe>
            ) : sourceName === 'SoundCloud' ? (
              <iframe
                className="aspect-video rounded-lg border-none bg-dark"
                src={`https://w.soundcloud.com/player/?url=${url}&color=%23ff5500&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true&visual=true`}
                width="100%"
              />
            ) : sourceType === 'video' && ['Link', 'Upload'].includes(sourceName) ? (
              <video
                className="aspect-video rounded-lg border-none bg-dark"
                width="100%"
                src={url}
                controls
              />
            ) : sourceType === 'audio' && ['Link', 'Upload'].includes(sourceName) ? (
              <audio className="w-full" src={url} controls />
            ) : (
              <Link className="block truncate underline hover:text-primary" to={source} noEncode>
                {source}
              </Link>
            )}
          </>
        ) : (
          source?.length && (
            <Expandable isExpandable={source.length > 140}>
              <div className="text-sm">{source}</div>
            </Expandable>
          )
        )}
      </div>

      <div className="pt-3">
        {BLOG_FIELDS.filter((f) => !!blog[f]).map((f) => (
          <div key={f} className="mt-3 flex justify-between">
            <span className="text-md font-semibold">{BLOG_FIELD_MAP[f]}</span>
            <span className="text-md capitalize">{blog[f] as string}</span>
          </div>
        ))}

        {!!blog.sourceName &&
          !['Link', 'Upload'].includes(blog.sourceName) &&
          sources.some((s) => s.name === blog.sourceName) && (
            <Button className="mt-6 w-full" onClick={toBlogCreate}>
              <BsStars />
              Reuse this prompt
            </Button>
          )}
      </div>
    </div>
  );
};

export default BlogSource;
