// بسم الله الرحمن الرحيم

import { createContext, useContext, useReducer } from 'react';
import DisplayPost from './components/DisplayPost';
import PlatformPicker from './components/PlatformPicker';
import ShareForm from './components/ShareForm';
import { SharingPlatform } from '../../../../constants/platforms';

const screens = {
  picker: PlatformPicker,
  form: ShareForm,
  view: DisplayPost,
} as const;
type Screen = keyof typeof screens;

const ScreenContext = createContext<null | ReturnType<typeof useReducer<State, [action: Action]>>>(
  null
);

// eslint-disable-next-line react-refresh/only-export-components
export function useScreenContext() {
  const context = useContext(ScreenContext);
  if (!context) {
    throw new Error(
      "ScreenContext is not valid, please ensure you have set up it's provider upstream"
    );
  } else return context;
}

type State = {
  screen: Screen;
  platform: SharingPlatform;
  form: {
    emoji: boolean;
    hashtag: boolean;
    link: string;
  };
  text: string;
};

const initialState = {
  screen: 'picker',
  platform: 'email',
  form: {
    emoji: false,
    hashtag: false,
    link: '',
  },
  text: '',
} as State;

type Action =
  | { type: 'setScreen'; payload: State['screen'] }
  | { type: 'setPlatform'; payload: State['platform'] }
  | { type: 'setForm'; payload: State['form'] }
  | { type: 'setText'; payload: State['text'] };

const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case 'setScreen':
      return { ...state, screen: action.payload };
    case 'setPlatform':
      return { ...state, platform: action.payload };
    case 'setForm':
      return { ...state, form: { ...state.form, ...action.payload } };
    case 'setText':
      return { ...state, text: action.payload };
    default:
      return state;
  }
};

export default function Screens() {
  const [state, dispatch] = useReducer(reducer, initialState);
  const DialogScreens = screens[state.screen];
  return (
    <ScreenContext.Provider value={[state, dispatch]}>
      <DialogScreens />
    </ScreenContext.Provider>
  );
}
