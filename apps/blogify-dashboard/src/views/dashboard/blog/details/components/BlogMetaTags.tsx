import { BiCopy } from 'react-icons/bi';

import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';

interface BlogMetaTagsProps {
  title?: string;
  image?: string;
  description?: string;
  keywords?: string[];
}
const BlogMetaTags: React.FC<BlogMetaTagsProps> = ({
  title,
  image,
  description,
  keywords = [],
}) => {
  const metaTags = [
    { name: 'description', content: description || '' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    { name: 'keywords', content: keywords.join(',') || '' },
    { name: 'og:title', content: title },
    { name: 'og:description', content: description || '' },
    { name: 'og:image', content: image || '' },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description || '' },
    { name: 'twitter:image', content: image || '' },
  ];

  let metaTagText: string = metaTags
    .map(
      (tag) =>
        `<meta ${tag.name.includes('og:') ? 'property' : 'name'}="${tag.name}" content="${
          tag.content
        }" />`
    )
    .join('\n');

  metaTagText = `<title>${title}</title>\n${metaTagText}`;
  const isGenerating = !description?.length;
  const copyContent = isGenerating ? undefined : metaTagText;

  return (
    <div className="rounded-lg border border-gray15 bg-bg2 p-6">
      <h3 className="text-lg font-semibold">Meta Tags</h3>

      <div className="my-6 text-md">
        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <pre className="sidebar overflow-x-auto whitespace-pre">{metaTagText}</pre>
      </div>

      <Button variant="secondary" onClick={() => copy(copyContent ?? '')}>
        <BiCopy />
        Copy All
      </Button>
    </div>
  );
};

export default BlogMetaTags;
