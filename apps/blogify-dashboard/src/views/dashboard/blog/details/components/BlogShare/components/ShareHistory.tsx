// بسم الله الرحمن الرحيم

import { Blog } from '@ps/types';
import { ComponentPropsWithoutRef, useMemo } from 'react';
import { LuExternalLink } from 'react-icons/lu';
import { MdEdit } from 'react-icons/md';
import { SharingPlatform } from './../constants/platforms';
import GrayText from './elements/GrayText';
import { Logo } from './elements/Logo';
import PlatformName from './elements/PlatformName';
import { Link } from 'react-router-dom';

type ShareHistoryProps = {
  data: Blog['socials'];
};

export default function ShareHistory({ data }: ShareHistoryProps) {
  const entries = useMemo(
    () =>
      data.map(
        ({ platform, timestamp, outcome }) =>
          ({
            platform,
            info:
              outcome && 'link' in outcome
                ? { status: 'published', link: outcome.link }
                : { status: 'scheduled', timestamp },
          }) as ComponentPropsWithoutRef<typeof Entry>
      ),
    [data]
  );

  return (
    <article className="divide-y-2 rounded-lg border border-gray10 p-1">
      {entries.map((props, index) => (
        <Entry key={`${props.platform}-${index}`} {...props} />
      ))}
    </article>
  );
}

type EntryProps = {
  platform: SharingPlatform;
  info:
    | {
        status: 'published';
        link: string;
      }
    | {
        status: 'scheduled';
        timestamp: string;
      };
};
function Entry({ platform, info }: EntryProps) {
  const Icon = info.status === 'scheduled' ? MdEdit : LuExternalLink;
  return (
    <section className="flex items-center justify-between gap-2 p-1">
      <div className="flex items-center gap-2">
        <Logo platform={platform} className="size-6 rounded-full" />
        <PlatformName name={platform} fontSize={17} />
      </div>
      <div className="flex items-center gap-2">
        <GrayText text={info.status} />
        <Link
          to={info.status === 'published' ? info.link : {}}
          target="_blank"
          className="rounded-lg border-2 border-gray10 p-1"
        >
          <Icon size={14} className="text-gray9 " />
        </Link>
      </div>
    </section>
  );
}
