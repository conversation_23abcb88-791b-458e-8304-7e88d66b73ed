import { API } from '@/services/api';
import { getSharerURL, SharingPlatform } from '@ps/common/utils/share';
import { Blog } from '@ps/types';

type GeneratePostInput = {
  blogID: string;
  platform: SharingPlatform;
  link: string;
  hashtag: boolean;
  emoji: boolean;
};
const generatePost = ({ blogID, ...payload }: GeneratePostInput) =>
  API.post<{ socialMediaPost: string }>(`/blogs/${blogID}/share`, payload);

const publishSocial = ({
  blogID,
  ...payload
}: { blogID: string } & Omit<Blog['socials'][number], 'outcome'>) =>
  API.post(`/blogs/${blogID}/publish-social`, {
    social: { ...payload, timestamp: new Date(payload.timestamp).toISOString() },
  });

export { publishSocial, generatePost, getSharerURL };
