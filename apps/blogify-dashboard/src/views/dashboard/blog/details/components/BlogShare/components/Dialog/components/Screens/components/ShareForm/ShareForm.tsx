// بسم الله الرحمن الرحيم

import LinkPicker from './components/LinkPicker';
import Header from '../elements/Header';
import CheckedOption from './components/CheckedOption';
import SchedulingWarning from './components/SchedulingWarning';
import { Button } from '@ps/ui/components/button';
import { ComponentPropsWithoutRef, useCallback, useMemo, useState } from 'react';
import { useBlogContext } from '@/context/BlogContext';
import toast from 'react-hot-toast';
import { generatePost } from '../../../../../../functions';
import { useScreenContext } from '../../Screens';

export default function ShareForm() {
  const { values, setFieldValue, handleSubmit, publishedData } = useShareForm();
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        handleSubmit();
      }}
      className="divide-y-2 rounded-lg border border-gray10 p-2 *:p-2"
    >
      <Header showConnect />
      <LinkPicker
        data={publishedData}
        index={values.index}
        setIndex={(value: number) => {
          setFieldValue('index', value);
        }}
      />
      <CheckedOption label="add hashtags" onChange={(v) => setFieldValue('hashtag', v)} />
      <CheckedOption label="add emojis" onChange={(v) => setFieldValue('emoji', v)} />
      <SchedulingWarning />
      <Button type="submit" variant="secondary" className="w-full">
        Generate Post
      </Button>
    </form>
  );
}
type PublishedOutcomes = ComponentPropsWithoutRef<typeof LinkPicker>['data'];
function useShareForm() {
  const [[{ platform }, dispatch], blog] = [useScreenContext(), useBlogContext()];
  const publishedData: PublishedOutcomes = useMemo(
    () =>
      (blog.publishResult ?? []).filter(
        (result): result is PublishedOutcomes[number] => 'link' in result.outcome
      ),
    [blog.publishResult]
  );

  const [values, setValues] = useState({
    index: 0,
    hashtag: false,
    emoji: false,
  });

  const setFieldValue = useCallback((field: string, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
  }, []);

  const handleSubmit = useCallback(() => {
    const { index, hashtag, emoji } = values;
    const link = publishedData?.[index]?.outcome?.link;
    toast.promise(
      generatePost({ blogID: blog._id, platform, link, hashtag, emoji }).then((data) => {
        dispatch({ type: 'setForm', payload: { link, hashtag, emoji } });
        dispatch({ type: 'setText', payload: data!.socialMediaPost });
        dispatch({ type: 'setScreen', payload: 'view' });
      }),
      {
        loading: 'Generating your social media post...',
        success: 'Social media post generated successfully!',
        error: 'Failed to generate the social media post.',
      }
    );
  }, [values, publishedData, blog._id, platform, dispatch]);

  return { values, setFieldValue, handleSubmit, publishedData };
}
