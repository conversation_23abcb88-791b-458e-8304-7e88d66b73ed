import type { Blog } from '@ps/types';

import { BsThreeDots, BsCopy } from 'react-icons/bs';
import { MdArrowDropDown } from 'react-icons/md';
import { PiDownloadThin } from 'react-icons/pi';
import { useNavigate } from 'react-router-dom';
import { RiEdit2Fill } from 'react-icons/ri';
import { useContext } from 'react';
import { useToggle } from 'react-use';
import { FaRepeat } from 'react-icons/fa6';
import { FaTrash } from 'react-icons/fa6';
import toast from 'react-hot-toast';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { API } from '@/services/api';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import Link from '@/components/common/Link';

import { downloadDocx, downloadPdf } from '../../utils/document';
import { getCopyContent } from '../../utils';

const BlogDetailsActions: React.FC<{
  blog: Blog;
  isRecoverable: boolean;
  canUseActions: boolean;
  isAffiliateLinkGenInProgress: boolean;
}> = ({ blog, isRecoverable, canUseActions, isAffiliateLinkGenInProgress }) => {
  const abilities = useContext(UserAbilitiesContext);
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [regenerating, toggleRegenerate] = useToggle(false);
  const navigate = useNavigate();

  const deleteBlog = async () => {
    await API.remove(`blogs/${blog._id}`);
    toggleConfirmationDialogOpen();
    return navigate('../blogs');
  };

  const regenerate = () => {
    toggleRegenerate();
    API.fetch<any>(`blogs/${blog._id}/regenerate`)
      .then((resp) => {
        if (resp?.status === 'failed') {
          toast.error(resp.message);
        } else {
          toast.success('Blog is queued for regeneration');
        }
      })
      .catch(() => toast.error('Something went wrong, please try again later'))
      .finally(toggleRegenerate);
  };

  return (
    <>
      {/* <Link to="/">
        <Button variant="secondary">
          <TbEyeFilled />
          Preview
        </Button>
      </Link> */}

      {canUseActions ? (
        <>
          <Link to="./edit">
            <Button variant="secondary" disabled={isAffiliateLinkGenInProgress}>
              <RiEdit2Fill />
              Edit Blog
            </Button>
          </Link>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary" className="!gap-1">
                Download
                <MdArrowDropDown size={25} />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => downloadPdf(blog)}>
                <PiDownloadThin />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => downloadDocx(blog._id, blog.title)}>
                <PiDownloadThin />
                Download DOC
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary" className="!gap-1">
                Copy
                <MdArrowDropDown size={25} />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => copy(getCopyContent(blog), true)}>
                <BsCopy />
                Copy Text
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => copy(getCopyContent(blog))}>
                <BsCopy />
                Copy HTML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      ) : (
        <Button
          variant="secondary"
          onClick={regenerate}
          loading={regenerating}
          disabled={!isRecoverable}
        >
          <FaRepeat size={12} />
          Retry
        </Button>
      )}

      {abilities.blog.delete && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button asChild variant="secondary" className="!gap-1">
              <div>
                <BsThreeDots />
              </div>
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent>
            <DropdownMenuItem className="text-red4" onClick={toggleConfirmationDialogOpen}>
              <FaTrash />
              <span className="font-semibold">Delete Blog</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to delete this blog?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={deleteBlog}
      />
    </>
  );
};

export default BlogDetailsActions;
