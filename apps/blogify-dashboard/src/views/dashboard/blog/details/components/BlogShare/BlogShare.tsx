// بسم الله الرحمن الرحيم

import CardTitle from '@/components/common/CardTitle';
import { Blog } from '@ps/types';
import { MdShare } from 'react-icons/md';
import { SharingPlatforms } from './constants/platforms';
import { Logo } from './components/elements/Logo';
import { Button } from '@ps/ui/components/button';
import ShareHistory from './components/ShareHistory';
import { BlogShareDialog } from './components/Dialog';
import { useToggle } from 'react-use';
import { useMemo } from 'react';
import UnpublishedDialog from './components/UnpublishedDialog';

export function BlogShare({
  blog,
  refetchBlog,
}: Readonly<{ blog: Blog; refetchBlog: () => void }>) {
  const [isDialogOpen, toggleDialogOpen] = useToggle(false);
  const enableSharing = useMemo(
    () => blog.publishResult?.filter(({ outcome }) => 'link' in outcome)?.length ?? 0 > 0,
    [blog.publishResult]
  );
  return (
    <article className="flex flex-col gap-4 rounded-lg border border-gray11 p-4">
      <CardTitle Icon={<MdShare className="size-8 text-primary" />} title={'Share your Blog'} />
      <span>Share your blog instantly or schedule it to share on a specific time & date.</span>
      <section className="flex -space-x-2.5">
        {SharingPlatforms.map((name) => (
          <Logo
            size={32}
            key={name}
            platform={name}
            className="rounded-full border-2 border-white bg-white"
          />
        ))}
      </section>

      <ShareHistory data={blog.socials} />
      <Button onClick={toggleDialogOpen}>Share</Button>
      {enableSharing ? (
        <BlogShareDialog
          blog={blog}
          onClose={() => {
            toggleDialogOpen();
            refetchBlog();
          }}
          isOpen={isDialogOpen}
        />
      ) : (
        <UnpublishedDialog toggleOpen={toggleDialogOpen} isOpen={isDialogOpen} />
      )}
    </article>
  );
}
