// بسم الله الرحمن الرحيم

import Select from 'react-select';
import PlatformName from '../../../../../../elements/PlatformName';
import GrayText from '../../../../../../elements/GrayText';
import { useContext } from 'react';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { formatDate } from '@/utils/date';

type LinkPickerProps = {
  data: {
    integration: string;
    outcome: {
      link: string;
      time: string;
    };
  }[];
  index: number;
  setIndex: (v: number) => void;
};
export default function LinkPicker({ index, setIndex, data }: LinkPickerProps) {
  const integrationDetails = useContext(IntegrationDetailsContext);
  const options = data.map(({ integration, outcome: { time } }, i) => ({
    value: i,
    label: (
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <img
            style={{
              backgroundColor: integrationDetails[integration].theme,
            }}
            src={integrationDetails[integration].logoURL}
            className="size-6 p-1"
          />
          <PlatformName name={integration} />
        </div>
        <GrayText text={`Published ${formatDate(time)}`} />
      </div>
    ),
  }));
  const link = data?.[index]?.outcome?.link;
  return (
    <section className="flex flex-col gap-2 bg-gray10">
      <GrayText text="link" />
      <span>{link}</span>
      <Select
        options={options}
        defaultValue={options[0]}
        isSearchable={false}
        components={{ SingleValue: (sv) => sv.data.label }}
        onChange={(x) => setIndex(x!.value)}
      />
    </section>
  );
}
