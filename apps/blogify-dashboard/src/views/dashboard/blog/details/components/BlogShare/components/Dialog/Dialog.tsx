// بسم الله الرحمن الرحيم
import type { Blog } from '@ps/types';

import { MdShare } from 'react-icons/md';

import Dialog from '@ps/ui/components/dialog';
import Screens from './components/Screens';
import { BlogContext } from '@/context/BlogContext';
import { SocialPlatformDetailsContextProvider } from '../../contexts/SocialPlatformDetailsContext';

type BlogShareDialogProps = {
  blog: Blog;
  onClose: () => void;
  isOpen: boolean;
};
export function BlogShareDialog({ blog, onClose, isOpen }: BlogShareDialogProps) {
  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={MdShare}
      title="Share your insights effortlessly!"
      description="Amplify your reach! Select a social platform, grab your blog link, and let Blogify craft a captivating social post in seconds."
    >
      <SocialPlatformDetailsContextProvider>
        <BlogContext.Provider value={blog}>
          <Screens />
        </BlogContext.Provider>
      </SocialPlatformDetailsContextProvider>
    </Dialog>
  );
}
