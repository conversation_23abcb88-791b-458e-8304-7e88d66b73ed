// بسم الله الرحمن الرحيم

import { Checkbox } from '@ps/ui/components/checkbox';
import { cn } from '@ps/ui/lib/utils';
import React, { FC, useCallback } from 'react';
import Select, { MultiValue, SingleValue } from 'react-select';

type Option = { id: string; name: string };

type IntegrationSite = {
  name: string;
  url: string;
  id: string;
  author?: Option[];
  category?: Option[];
};

export type SelectedSite = {
  site: string;
  author?: string;
  category?: string[];
};

type SiteSelectorProps = {
  sites: IntegrationSite[];
  selectedSites: SelectedSite[];
  setIntegrationSite: React.Dispatch<React.SetStateAction<SelectedSite[]>>;
};

const isSiteSelected = (selectedSites: SelectedSite[], siteId: string) =>
  selectedSites.some((s) => s.site === siteId);

const getSelectedSite = (selectedSites: SelectedSite[], siteId: string) =>
  selectedSites.find((s) => s.site === siteId);

const toSelectOption = (item: Option) => ({
  value: item.id,
  label: item.name,
});

const getSelectedOptions = (ids: string[] | undefined, options: Option[] | undefined) =>
  ids && options
    ? ids
        .map((id) => options.find((opt) => opt.id === id))
        .filter(Boolean)
        .map((opt) => toSelectOption(opt!))
    : [];

const SiteSelector: FC<SiteSelectorProps> = ({ sites, selectedSites, setIntegrationSite }) => {
  const handleSiteCheck = useCallback(
    (siteId: string, checked: boolean) => {
      setIntegrationSite((prev) =>
        checked ? [...prev, { site: siteId }] : prev.filter((s) => s.site !== siteId)
      );
    },
    [setIntegrationSite]
  );

  const handleAuthorChange = useCallback(
    (siteId: string, author: SingleValue<{ value: string; label: string }>) => {
      setIntegrationSite((prev) =>
        prev.map((s) => (s.site === siteId ? { ...s, author: author?.value } : s))
      );
    },
    [setIntegrationSite]
  );

  const handleCategoryChange = useCallback(
    (siteId: string, categoriesSelected: MultiValue<{ value: string; label: string }>) => {
      setIntegrationSite((prev) =>
        prev.map((s) =>
          s.site === siteId ? { ...s, category: categoriesSelected.map((c) => c.value) } : s
        )
      );
    },
    [setIntegrationSite]
  );
  return (
    <div>
      <h3 className="mb-2 font-semibold">Websites</h3>
      <div className="flex flex-col gap-2">
        {sites.map((site) => {
          const checked = isSiteSelected(selectedSites, site.id);
          const selected = getSelectedSite(selectedSites, site.id);
          return (
            <section
              key={site.id}
              className={cn('relative flex w-full items-start rounded border p-2 transition-all', {
                'bg-bg1 border-primary': checked,
              })}
            >
              <Checkbox
                checked={checked}
                onCheckedChange={(e) => handleSiteCheck(site.id, e.valueOf() as boolean)}
                className="mt-1 accent-primary"
                id={`site-checkbox-${site.id}`}
              />
              <div className="w-full px-3 text-md">
                <section className="flex flex-col gap-2 p-1">
                  <label
                    htmlFor={`site-checkbox-${site.id}`}
                    className="cursor-pointer font-medium"
                  >
                    {site.name}
                  </label>
                  <a
                    className="truncate text-gray9 hover:underline"
                    href={site.url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {site.url}
                  </a>
                  {checked && (
                    <div className="mt-2 flex flex-col gap-2">
                      {site.author && site.author.length > 0 && (
                        <Select
                          placeholder="Select author"
                          options={site.author.map(toSelectOption)}
                          value={
                            selected?.author
                              ? toSelectOption(
                                  site.author.find((a) => a.id === selected.author) ?? {
                                    id: '',
                                    name: '',
                                  }
                                )
                              : null
                          }
                          onChange={(val) => handleAuthorChange(site.id, val)}
                          className="mb-2"
                        />
                      )}
                      {site.category && site.category.length > 0 && (
                        <Select
                          isMulti
                          placeholder="Select categories"
                          options={site.category.map(toSelectOption)}
                          value={getSelectedOptions(selected?.category, site.category)}
                          onChange={(vals) => handleCategoryChange(site.id, vals)}
                        />
                      )}
                    </div>
                  )}
                </section>
              </div>
            </section>
          );
        })}
      </div>
    </div>
  );
};

export default SiteSelector;
