// بسم الله الرحمن الرحيم

import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { ReactNode, useContext } from 'react';

export const PlatformIcon = ({
  src,
  backgroundColor,
  className,
}: {
  className?: string;
  src: string;
  backgroundColor?: string;
}) => <img src={src} className={className} style={{ backgroundColor }} />;

export function IntegrationIcon({
  integration,
  className,
}: {
  integration: string;
  className?: string;
}) {
  const { logoURL, theme } = useContext(IntegrationDetailsContext)[integration] || {};
  return <PlatformIcon src={logoURL} backgroundColor={theme} className={className} />;
}

export const IntegrationIconWithAction = ({
  integration,
  Icon,
  className,
}: {
  integration: string;
  Icon?: ReactNode;
  className: string;
}) => (
  <div className="relative">
    <IntegrationIcon integration={integration} className={className} />
    <span className="absolute -bottom-1 -right-1 rounded-full bg-white ring-2 ring-white">
      {Icon}
    </span>
  </div>
);
