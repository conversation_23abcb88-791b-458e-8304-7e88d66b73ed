// بسم الله الرحمن الرحيم

export default function DateTimePicker({
  value,
  onChange,
  minimum,
  className,
}: {
  minimum: string;
  value: string;
  onChange: (newValue: string) => void;
  className?: string;
}) {
  const timeZone = new Date()
    .toLocaleTimeString(undefined, { timeZoneName: 'short' })
    .split(' ')[2];

  return (
    <section className={className}>
      <label className="space-x-1">
        <span className="text-sm font-semibold">Date & Time</span>
        {timeZone && <span className="text-xs text-gray9">({timeZone})</span>}
      </label>
      <input
        className="peer h-10 w-full rounded-lg border border-solid border-gray10 px-4"
        min={minimum}
        type="datetime-local"
        onChange={({ target }) => onChange(target.value)}
        value={value}
      />
      <span className="mt-1 hidden text-sm font-medium text-red peer-invalid:block">
        You can not set a time before your current time & date.
      </span>
    </section>
  );
}
