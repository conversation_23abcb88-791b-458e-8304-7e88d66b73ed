// بسم الله الرحمن الرحيم
import { Dispatch, use, useContext, useMemo, useState } from 'react';
import { IoCloseCircle } from 'react-icons/io5';
import { FaPaperPlane } from 'react-icons/fa6';
import dayjs from 'dayjs';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { Button } from '@ps/ui/components/button';

import { IntegrationIcon } from './PlatformIcon';
import DateTimePicker from './DateTimePicker';
import { useBlogContext } from '@/context/BlogContext';
import toast from 'react-hot-toast';
import SiteSelector, { type SelectedSite } from '@/views/dashboard/blog/publish/SiteSelector';

function IntegrationBanner({
  integration,
  setCurrentTab,
}: {
  integration: string;
  setCurrentTab: Dispatch<React.SetStateAction<string>>;
}) {
  const { name, website } = useContext(IntegrationDetailsContext)[integration];
  return (
    <article className="m-2 flex items-center justify-between">
      <section className="flex gap-2">
        <IntegrationIcon integration={integration} className="size-10 rounded-lg p-1" />
        <div className="flex flex-col justify-around">
          <span className="text-md font-semibold capitalize">{name}</span>
          <span className="text-sm text-gray9">{new URL(website).origin}</span>
        </div>
      </section>
      <IoCloseCircle
        className="rounded-full text-red4 ring-4 ring-gray16"
        size={16}
        onClick={() => setCurrentTab('none')}
      />
    </article>
  );
}

export default function PublishForm({
  integration,
  setCurrentTab,
  onSubmit,
}: {
  integration: string;
  setCurrentTab: Dispatch<React.SetStateAction<string>>;
  onSubmit: (data: {
    publishOption: 'draft' | 'publish-auto' | 'publish-schedule';
    timestamp: string;
    sites: Array<SelectedSite>;
  }) => void;
}) {
  const { drafts, sites } = use(IntegrationDetailsContext)[integration];
  const { content } = useBlogContext();
  const publishOptions = useMemo(
    () =>
      ({
        ...(drafts && {
          draft: {
            label: 'Publish as Draft',
            description: 'The blog will be published as a draft on your site.',
          },
        }),
        'publish-auto': {
          label: 'Publish Now',
          description: 'The blog will be published directly on your sites.',
        },
        'publish-schedule': {
          label: 'Schedule to Publish',
          description:
            'The blog will be published directly on your sites at a scheduled time & date.',
        },
      }) as const,
    [drafts]
  );
  const [publishAt, setPublishAt] = useState(dayjs().add(15, 'minutes').format('YYYY-MM-DDTHH:mm'));
  const [publishOption, setPublishOption] = useState<keyof typeof publishOptions>('publish-auto');
  const [publishSites, setPublishSites] = useState<SelectedSite[]>([]);
  return (
    <article className="space-y-5">
      <IntegrationBanner setCurrentTab={setCurrentTab} integration={integration} />
      <form
        className="flex flex-col gap-4"
        onSubmit={(event) => {
          event.preventDefault();

          if (integration === 'linkedin' && content.length > 3000) {
            toast.error(
              'LinkedIn mandates that the post must be under 3000 characters.\n Please edit your blog to make it shorter.'
            );
            return;
          }

          onSubmit({
            publishOption,
            timestamp: publishAt,
            sites: publishSites,
          });
          setCurrentTab('none');
        }}
      >
        <label className="text-sm font-semibold">Publish Option</label>
        <RadioGroup
          onValueChange={(v) => {
            setPublishOption(v as typeof publishOption);
          }}
          defaultValue={'publish-auto' as keyof typeof publishOptions}
          className="divide-y-2 rounded-lg border-2 border-gray-100"
        >
          {Object.entries(publishOptions).map(([value, { description, label }]) => (
            <label
              key={value}
              className={`m-0 flex items-center gap-4 px-4 py-3 transition-all hover:bg-bg2`}
            >
              <RadioGroupItem value={value} />
              <div>
                <h5 className="text-md font-medium">{label}</h5>
                <p className="text-sm text-gray9">{description}</p>
              </div>
            </label>
          ))}
        </RadioGroup>
        {publishOption === 'publish-schedule' && (
          <DateTimePicker
            value={publishAt}
            minimum={dayjs().add(10, 'minutes').format('YYYY-MM-DDTHH:mm')}
            onChange={setPublishAt}
          />
        )}
        {sites && (
          <SiteSelector
            sites={sites}
            selectedSites={publishSites}
            setIntegrationSite={setPublishSites}
          />
        )}
        <Button type="submit" className="flex gap-2" disabled={sites && publishSites.length === 0}>
          <FaPaperPlane />
          <span>Publish</span>
        </Button>
      </form>
    </article>
  );
}
