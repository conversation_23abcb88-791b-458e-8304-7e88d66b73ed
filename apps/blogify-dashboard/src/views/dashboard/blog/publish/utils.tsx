// بسم الله الرحمن الرحيم
import type { Blog } from '@ps/types';

import { differenceWith, isEqual } from 'lodash';

const convert = (
  record: Blog['platforms'][number]
): { integration: string; siteID?: string; timestamp: string }[] => {
  const { sites, platform: integration, timestamp } = record;
  if (sites)
    return sites.map(({ site }) => ({
      integration,
      siteID: site,
      timestamp,
    }));
  else return [{ integration, timestamp }];
};

export function getQueued(
  platforms: Blog['platforms'],
  publishResult: NonNullable<Blog['publishResult']>
) {
  const requests = platforms.flatMap(convert);
  const published = publishResult.map(({ integration, siteID }) => ({ integration, siteID }));
  const queued = differenceWith(
    requests.map(({ integration, siteID }) => ({ integration, siteID })),
    published,
    isEqual
  );
  // console.log({ requests });
  // console.log({ published });
  // console.log({ queued });

  return requests.filter((r) =>
    queued.some((q) => q.integration === r.integration && r.siteID === q.siteID)
  );
}
