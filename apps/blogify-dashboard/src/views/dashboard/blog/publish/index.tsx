// بسم الله الرحمن الرحيم
import type { Blog } from '@ps/types';

import { ComponentProps, useContext, useEffect, useState } from 'react';
import { FaCircleCheck, FaCirclePlus } from 'react-icons/fa6';
import { FaPaperPlane } from 'react-icons/fa';
import { Link } from 'react-router-dom';

import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { getConnectUrl } from '@/utils/integration';
import { cn } from '@ps/ui/lib/utils';
import CardTitle from '@/components/common/CardTitle';
import Tabs from '@ps/ui/components/tabs';

import { IntegrationIconWithAction } from './PlatformIcon';
import PublishForm from './PublishForm';
import { blogEvents } from '@/services/event';
import { API } from '@/services/api';
import toast from 'react-hot-toast';
import { BlogContext } from '@/context/BlogContext';

const TabIcon = ({
  integration,
  className,
  state = 'normal',
}: {
  integration: string;
  className: string;
  state?: 'not connected' | 'already published' | 'normal';
}) => {
  const { tutorial } = useContext(IntegrationDetailsContext)[integration];
  let content;
  if (state === 'not connected') {
    content = (
      <Link to={tutorial || getConnectUrl(integration)}>
        <IntegrationIconWithAction
          integration={integration}
          className={cn(className, 'opacity-50 saturate-0')}
          Icon={<FaCirclePlus size={14} className="text-primary" />}
        />
      </Link>
    );
  } else {
    content = (
      <IntegrationIconWithAction
        integration={integration}
        className={className}
        Icon={
          state === 'already published' ? (
            <FaCircleCheck size={14} className="text-[#66b700]" />
          ) : null
        }
      />
    );
  }

  return content;
};

export function PublishTabs({ blog, refetchBlog }: { blog: Blog; refetchBlog: () => void }) {
  const integrations = useContext(IntegrationDetailsContext);
  const [currentTab, setCurrentTab] = useState<keyof typeof integrations | 'none'>('none');

  const handlePublish =
    (integration: string) =>
    (data: Parameters<ComponentProps<typeof PublishForm>['onSubmit']>[0]) => {
      const payload = {
        platform: {
          platform: integration,
          timestamp:
            data.publishOption === 'publish-schedule'
              ? new Date(data.timestamp).toISOString()
              : new Date().toISOString(),
          sites: data.sites.length > 0 ? data.sites : undefined,
          draft: data.publishOption === 'draft',
        } as Blog['platforms'][number],
      };
      toast.promise(API.post(`/blogs/${blog._id}/publish`, payload).then(refetchBlog), {
        loading: 'Processing...',
        success: 'Complete!',
        error: 'Failed to publish!',
      });
    };

  useEffect(() => {
    const blogEventsSubscription = blogEvents.subscribe('BLOG_STATUS_UPDATE', (updates) => {
      if (
        blog._id !== updates._id &&
        (updates.status === 'blog_publishing' || updates.status === 'blog_published')
      ) {
        refetchBlog();
      }
    });

    return () => {
      blogEventsSubscription.unsubscribe();
    };
  }, [blog._id, refetchBlog]);

  return (
    <Tabs.Root className="flex flex-col gap-2" value={currentTab}>
      <span className="text-xs font-semibold uppercase tracking-widest">Connected Platforms</span>
      <Tabs.List className="flex flex-wrap gap-2">
        {Object.entries(integrations).map(([name, { connected }]) => {
          const isPublished = blog.publishResult?.some((result) => result.integration === name);
          return (
            <Tabs.Trigger
              value={name}
              key={name}
              disabled={!connected}
              onClick={() => setCurrentTab(name)}
            >
              <TabIcon
                integration={name}
                className={cn(
                  'size-8 rounded-lg p-1.5',
                  name === currentTab && 'size-[30px] ring-2 ring-primary'
                )}
                state={isPublished ? 'already published' : connected ? 'normal' : 'not connected'}
              />
            </Tabs.Trigger>
          );
        })}
      </Tabs.List>
      {Object.keys(integrations).map((integration) => (
        <Tabs.Content value={integration} key={integration}>
          <BlogContext.Provider value={blog}>
            <PublishForm
              onSubmit={handlePublish(integration)}
              integration={integration}
              setCurrentTab={setCurrentTab}
            />
          </BlogContext.Provider>
        </Tabs.Content>
      ))}
    </Tabs.Root>
  );
}

export default function Publish(props: ComponentProps<typeof PublishTabs>) {
  return (
    <article className="space-y-3">
      <CardTitle
        Icon={<FaPaperPlane className="size-5 text-primary" />}
        title={'Publish your Blog'}
      />
      <PublishTabs {...props} />
    </article>
  );
}
