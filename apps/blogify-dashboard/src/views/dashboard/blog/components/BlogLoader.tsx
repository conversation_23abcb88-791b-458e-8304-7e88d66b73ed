import type { BlogSourceType, Blog } from '@ps/types';

import { useContext, useEffect, useState, useRef } from 'react';
import { useNavigate, useBlocker } from 'react-router-dom';

import { BLOG_INPROGRESS_MESSAGES } from '@/constants/blog';
import { ToastContext } from '@/hooks/useToast';
import { blogEvents } from '@/services/event';
import { Progress } from '@ps/ui/components/progress';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import FadeMessage from '@/components/misc/FadeMessage';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import useBlog from '../hooks/useBlog';

type StatusType = 'loading' | 'success' | 'error';

const progressMap: { [key: string]: number } = {
  request_dispatched: 45,
  transcribed: 55,
  summarized: 65,
  outline_generated: 95,
  content_generated: 100,
};

export const BlogLoader = ({ mode, blog }: { mode: BlogSourceType; blog: Blog }) => {
  const [progressMessage, setProgressMessage] = useState(BLOG_INPROGRESS_MESSAGES[mode][0]);
  const [redirectingIn, setRedirectingIn] = useState(3);
  const [nextProgress, setNextProgress] = useState(5);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<StatusType>('loading');
  const [error, setError] = useState('');
  const progs = useRef(0);

  const { toggleToast } = useContext(ToastContext);
  const navigate = useNavigate();

  const { genericError, isAssisted } = useBlog(blog._id, { initialBlog: blog });

  const isLoading = status === 'loading';
  const isSuccess = status === 'success';

  const getSuccessRedirect = () =>
    isAssisted ? `/dashboard/blogs/${blog?._id}/co-pilot` : `/dashboard/blogs/${blog?._id}`;

  useEffect(() => {
    const subscription = blogEvents.subscribe(
      'BLOG_STATUS_UPDATE',
      ({ status: _status, failReason, _id }) => {
        if (blog._id !== _id) return;

        if (_status?.endsWith('failed')) {
          setStatus('error');
          if (failReason) {
            setError(failReason);
          }
        }

        if (_status && _status in progressMap) {
          if (progs.current < nextProgress) {
            progs.current = nextProgress;
            setProgress(progs.current);
          }

          setNextProgress(progressMap[_status]);

          if (_status === 'content_generated' || (isAssisted && _status === 'outline_generated')) {
            setProgress(100);
            setTimeout(() => setStatus('success'), 1000);
          }
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [blog._id, isAssisted, nextProgress]);

  useEffect(() => {
    let i = progs.current;
    const interval = setInterval(() => {
      if (i <= nextProgress) {
        setProgress(i);
        const j = Math.floor(i / 10);
        if (BLOG_INPROGRESS_MESSAGES[mode][j]) {
          setProgressMessage(BLOG_INPROGRESS_MESSAGES[mode][j]);
        }
        i += 1;
        progs.current = i;
      }
    }, 3000);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [mode, nextProgress]);

  useEffect(() => {
    if (status === 'success' && blog?._id) {
      const interval = setInterval(() => setRedirectingIn((c) => c - 1), 1000);
      return () => clearInterval(interval);
    }
  }, [blog?._id, status]);

  useEffect(() => {
    if (redirectingIn === 0) {
      return navigate(getSuccessRedirect());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [redirectingIn]);

  useBlocker(() => {
    toggleToast('blogLoaderUnload', true);
    return false;
  });

  return (
    <Card className="flex-col">
      <div className="mt-[15%] flex flex-col px-5 flex-center">
        <Loader status={status} />

        <div className="mt-[-60px] text-xl font-semibold text-black1">
          {isLoading
            ? `Generating your blog${isAssisted ? ' outlines' : ''}...`
            : isSuccess
              ? `Blog ${isAssisted ? 'outlines ' : ''}generation successful 😇`
              : 'Blog generation failed 😢'}
        </div>

        <div className="mt-2 text-center text-sm text-black1">
          {status === 'error' ? (
            <div className={error ? 'text-red' : 'text-black1'}>{error || genericError}</div>
          ) : isSuccess ? (
            <div>
              Your blog {isAssisted ? 'outlines are' : 'is'} ready. You will be automatically
              redirected to {isAssisted ? 'blog generate with co-pilot' : 'the generated blog'} in{' '}
              {redirectingIn}...
            </div>
          ) : (
            <FadeMessage message={progressMessage} />
          )}
        </div>

        {status !== 'loading' && (
          <Link to={isSuccess ? getSuccessRedirect() : `/dashboard/blogs/select-source`}>
            <Button className="mt-5" type="button">
              {isSuccess
                ? isAssisted
                  ? 'Continue with Co-Pilot...'
                  : 'View Generated Blog'
                : 'Create Another Blog'}
            </Button>
          </Link>
        )}

        {progress <= 100 && isLoading && (
          <>
            <Progress className="mt-1.5 w-[350px]" value={progress} />
            <div className="mt-2 text-gray2">{progress.toFixed(0)}%</div>
          </>
        )}

        {status === 'loading' && (
          <div className="mt-6 flex flex-col items-center text-center">
            <p className="text-sm">
              You can browse away from this page. We'll notify you when the blog is ready.
            </p>
            <div className="mt-3 flex gap-3">
              <Link to="/dashboard/">
                <Button size="sm" variant="secondary">
                  Go To Dashboard
                </Button>
              </Link>
              <Link to="/dashboard/blogs/select-source">
                <Button size="sm">Create Another Blog</Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
