import type { Blog } from '@ps/types';

import { MdContentCopy, MdShare } from 'react-icons/md';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { useMutation } from 'react-query';
import { useContext } from 'react';
import { FiDownload } from 'react-icons/fi';
import { useToggle } from 'react-use';
import { FaTrash } from 'react-icons/fa';

import {
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { API } from '@/services/api';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';

import { downloadDocx, downloadPdf } from '../utils/document';
import { BlogShareDialog } from '../details/components/BlogShare/components/Dialog';
import { getCopyContent } from '../utils';

const BlogListContextMenu: React.FC<{
  blog: Blog;
  isCompleted: boolean;
  siteId?: string;
  variant?: 'blog-list' | 'website-blog-list';
  refetch: () => void;
  children?: React.ReactNode;
}> = ({ blog, isCompleted, siteId, variant, refetch, children }) => {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [isBlogShareDialogOpen, toggleBlogShareDialogOpen] = useToggle(false);
  const abilities = useContext(UserAbilitiesContext);

  const { mutateAsync: deleteBlog, isLoading: deleting } = useMutation({
    mutationFn: () =>
      API.remove(
        variant === 'blog-list' ? `blogs/${blog._id}` : `blogify/${siteId}/blogs/${blog._id}`
      ).then(() => {
        toggleConfirmationDialogOpen();
        refetch();
      }),
  });

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="w-8" variant="gray">
            <BsThreeDotsVertical className="shrink-0" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          {isCompleted && (
            <>
              {abilities.blog.publish && blog.publishStatus === 'published' && (
                <DropdownMenuItem onClick={toggleBlogShareDialogOpen}>
                  <MdShare />
                  Share
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => copy(getCopyContent(blog), true)}>
                <MdContentCopy />
                Copy Text
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => copy(getCopyContent(blog))}>
                <MdContentCopy />
                Copy HTML
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => downloadPdf(blog)}>
                <FiDownload />
                Download PDF
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => downloadDocx(blog._id, blog.title)}>
                <FiDownload />
                Download DOC
              </DropdownMenuItem>
            </>
          )}

          {children}

          <DropdownMenuItem className="text-red4" onClick={toggleConfirmationDialogOpen}>
            <FaTrash />
            <span className="font-semibold">Delete Blog</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <BlogShareDialog
        blog={blog}
        onClose={toggleBlogShareDialogOpen}
        isOpen={isBlogShareDialogOpen}
      />
      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to delete this blog?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={deleteBlog}
        confirming={deleting}
      />
    </>
  );
};

export default BlogListContextMenu;
