import { AiFillWarning } from 'react-icons/ai';

import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/dialog';

const BlogCreditLimitReachedDialog = ({
  hasReachedLimit,
  toggleReachedLimit,
  toggleAddBlogCreditDialog,
}: {
  hasReachedLimit: boolean;
  toggleReachedLimit: React.Dispatch<boolean>;
  toggleAddBlogCreditDialog: React.Dispatch<void>;
}) => (
  <Dialog
    open={hasReachedLimit}
    onOpenChange={toggleReachedLimit}
    Icon={AiFillWarning}
    title="Blog Credit Limit Reached"
    description="You have used up all your blog credits for this month. Please add more credits to generate blog."
    actions={
      <Button
        className="w-full"
        onClick={() => {
          toggleReachedLimit(false);
          toggleAddBlogCreditDialog();
        }}
      >
        Add Credits
      </Button>
    }
  >
    <div className="flex-center">
      <img src="/images/package-limit-reach.png" />
    </div>
  </Dialog>
);

export default BlogCreditLimitReachedDialog;
