import type { Blog } from '@ps/types/resources/blog.type';

import {
  AccordionContent,
  AccordionTrigger,
  AccordionItem,
  Accordion,
} from '@ps/ui/components/accordion';
import { extractHeadings } from '@ps/common/hooks/useTableOfContents';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';

export default function TableOfContents({
  content,
  tableOfContents,
}: Pick<Blog, 'content' | 'tableOfContents'>) {
  const headings = tableOfContents?.length ? tableOfContents : extractHeadings(content);

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem className="border-none" value="toc">
        <AccordionTrigger className="px-4 hover:no-underline">
          <div className="flex items-center gap-3">
            <h3 className="text-17 font-semibold">Table of Contents</h3>
            <Badge className="bg-primary/10 text-primary">Auto Generated</Badge>
          </div>
        </AccordionTrigger>

        <AccordionContent className="border-t border-gray10 p-4">
          <nav>
            <ul className="space-y-4">
              {headings.map((heading) => (
                <li
                  key={heading.id}
                  className={cn({
                    'pl-3 text-17 font-semibold': heading.level === 'h2',
                    'pl-6 text-15 font-medium': heading.level === 'h3',
                  })}
                >
                  {heading.text}
                </li>
              ))}
            </ul>
          </nav>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
