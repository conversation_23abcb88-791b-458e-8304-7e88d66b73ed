import { But<PERSON> } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import Spinner from '@/components/misc/Spinner';
import Link from '@/components/common/Link';

const BlogNoEntries = ({ loading, className }: { loading?: boolean; className?: string }) => (
  <div className={cn('flex h-[55vh] flex-col gap-4 p-5 flex-center', className)}>
    {loading ? (
      <Spinner />
    ) : (
      <>
        <img src="/images/icons/sad.svg" />
        <div className="text-center text-lg">
          Looks like you don't have any blogs that matches your filter.
        </div>
        <Link to="/dashboard/blogs/select-source">
          <Button>Create New Blog</Button>
        </Link>
      </>
    )}
  </div>
);

export { BlogNoEntries };
