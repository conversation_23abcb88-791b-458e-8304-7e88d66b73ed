import type { BlogIntegration } from '@/types/misc/integration.type';
import type { Blog } from '@ps/types';

import { LuMousePointerClick, LuCircleDollarSign, LuArrowLeftRight } from 'react-icons/lu';
import { FaRegFileWord, FaPaperPlane, FaLink } from 'react-icons/fa';
import { useEffect, useState } from 'react';
import { FaArrowTrendUp } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

import { toLocalizedRelativeTime } from '@/utils/time';
import { DropdownMenuItem } from '@ps/ui/components/dropdown-menu';
import { Avatar } from '@ps/ui/components/avatar';
import { Button } from '@ps/ui/components/button';
import { Theme } from '@/styles';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import Dialog from '@ps/ui/components/dialog';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { extractLinkUsage, LinkInfo } from '../edit/utils';
import { calculateMetrics } from '../../affiliate/utils';
import BlogListContextMenu from './BlogListContextMenu';
import BlogSource from '../details/components/BlogSource';
import useBlog from '../hooks/useBlog';

const BlogCard = ({
  blog: propBlog,
  menu,
  siteId,
  variant = 'blog-list',
  visibility,
  refetch,
  className,
}: {
  blog: Blog;
  menu?: React.ReactNode;
  siteId?: string;
  variant?: 'blog-list' | 'website-blog-list';
  visibility?: 'hidden' | 'visible';
  refetch: () => void;
  selectedTab?: 'All Blogs' | 'Drafts' | 'Scheduled' | 'Published';
  className?: string;
}) => {
  const navigate = useNavigate();
  const [isReusePromptDialogOpen, toggleReusePromptDialogOpen] = useToggle(false);
  const {
    getBlogContent,
    publishedPlatforms,
    scheduledPlatforms,
    isRecoverable,
    isInProgress,
    isCompleted,
    isAssisted,
    isFailed,
    isPaused,
    redirect,
    blog,
  } = useBlog(propBlog._id, { initialBlog: propBlog, isListView: true });
  const [links, setLinks] = useState<LinkInfo[]>([]);
  const [loadingTime, setLoadingTime] = useState<number | null>(null);

  const regenerateBlog = (slug: string) => {
    API.fetch<any>(`blogs/${slug}/regenerate`)
      .then((resp) => {
        if (resp?.status === 'failed') {
          toast.error(resp.message);
        } else {
          toast.success('Blog is queued for regeneration');
        }
        navigate('../blogs');
      })
      .catch(() => toast.error('Something went wrong, please try again later'));
  };

  useEffect(() => {
    if (blog.content) {
      const startTime = performance.now();
      const extractedLinks = extractLinkUsage(blog.content as string);
      const endTime = performance.now();
      setLoadingTime(endTime - startTime);
      setLinks(extractedLinks);
    }
  }, [blog.content]);

  const { actions = 0, clicks = 0, earnings = 0, views = 0 } = blog.analytics ?? {};

  const { CR } = calculateMetrics({
    totalEarnings: earnings,
    totalClickCount: clicks,
    totalActionCount: actions,
  });

  return (
    <div
      className={cn(
        'flex flex-wrap gap-6 border-b border-bg2 py-6 md:flex-nowrap',
        { 'opacity-50': visibility === 'hidden' },
        className
      )}
    >
      <Link className="h-40 w-full shrink-0 md:w-72" to={redirect}>
        {(isInProgress || isFailed) && !isCompleted ? (
          <div className="flex size-full rounded-lg bg-gray6 flex-center">
            <Loader status={isInProgress ? 'loading' : 'error'} />
          </div>
        ) : (
          <img
            className="size-full rounded-lg object-cover"
            src={propBlog.thumbnail || propBlog.image || '/images/temp-bg.jpg'}
            onError={(e) => (e.currentTarget.src = '/images/temp-bg.jpg')}
          />
        )}
      </Link>

      <div className="flex w-full flex-col justify-between gap-3 overflow-hidden">
        <div className="flex flex-col gap-2">
          <Link to={redirect}>
            {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
            <div className="ellipsis r2 text-lg font-semibold">
              {(blog.title &&
                !blog.title.startsWith('https://storage.googleapis.com/blogify_yt')) ||
              blog.content ? (
                <>{blog.title || `${blog.content?.substring(0, 50).replace(/<[^>]*>/gi, '')}...`}</>
              ) : isFailed ? (
                <>Blog generation failed 😢</>
              ) : (
                <>Generating your blog...</>
              )}
            </div>
          </Link>

          <div className="flex gap-2 text-sm font-medium text-gray9">
            <div className="flex gap-1">
              <Avatar
                className="size-5 text-xxs"
                src={blog.uid?.profilePicture}
                name={blog.uid?.name}
              />
              <span>{blog.uid?.name}</span>
            </div>

            <span>&#183;</span>
            <span>{dayjs(blog.createdAt).format('DD MMM YYYY')}</span>

            <span>&#183;</span>
            <span>Last edited {toLocalizedRelativeTime(blog.updatedAt)}</span>

            {/* {!!blog?.wordCount && (
              <>
                <span>&#183;</span>
                <span>{blog?.wordCount} Words</span>
              </>
            )} */}

            {/* <span>&#183;</span> */}
            {/* <span>124 Links</span> */}
          </div>

          {/* {(blog.publishTime || blog.publishAt) && (
            <div className="text-sm text-gray2">
              {toLocalizedRelativeTime(blog.publishTime || blog.publishAt).startsWith('in')
                ? `Publish ${toLocalizedRelativeTime(blog.publishTime || blog.publishAt)}`
                : `Published ${toLocalizedRelativeTime(blog.publishTime || blog.publishAt)}`}
            </div>
          )} */}

          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className={cn('ellipsis r2 text-md', { 'text-red4': isFailed })}>
            {getBlogContent()}
          </div>
        </div>

        {isCompleted && loadingTime !== null && (
          <div className="flex flex-wrap gap-4">
            {[
              { icon: <FaRegFileWord />, text: `Words : ${blog?.wordCount}` },
              { icon: <FaLink />, text: `Links : ${links.length}` },
              { icon: <LuMousePointerClick />, text: `Clicks : ${clicks}` },
              { icon: <LuCircleDollarSign />, text: `Earnings : $${earnings}` },
              {
                icon: <LuArrowLeftRight />,
                text: `CR : ${CR.toLocaleString(undefined, {
                  style: 'percent',
                  minimumFractionDigits: 2,
                })}`,
              },
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-1 text-[12px] ">
                {item.icon}
                <p className="mt-px uppercase">{item.text}</p>
              </div>
            ))}
          </div>
        )}

        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex w-full items-center gap-3 sm:w-auto">
            {!!publishedPlatforms?.length && (
              <div className="flex gap-1">
                <div className="flex items-center gap-2 rounded-md bg-primary px-2 py-[2px] text-xs text-white">
                  <FaPaperPlane />
                  <p>PUBLISHED</p>
                </div>

                <BlogPlatformList platforms={publishedPlatforms} blog={blog} />
              </div>
            )}

            {!!scheduledPlatforms?.length && (
              <>
                <div className="h-4 w-px bg-gray6"></div>

                <div className="flex gap-1">
                  <div className="flex items-center gap-2 rounded-md border border-primary bg-primary/10 px-2 py-[2px] text-xs text-primary">
                    <FaPaperPlane />
                    <p>SCHEDULED</p>
                  </div>

                  <BlogPlatformList platforms={scheduledPlatforms} blog={blog} />
                </div>
              </>
            )}

            {isCompleted && (clicks > 0 || views > 0) && (
              <>
                {(scheduledPlatforms?.length > 0 || publishedPlatforms?.length > 0) && (
                  <div className="h-4 w-px bg-gray6"></div>
                )}
                <div className="flex items-center gap-1">
                  <FaArrowTrendUp className="size-6 rounded bg-[#009933] p-[3px] text-white" />
                  <span className="text-[14px]">
                    {views > clicks ? `Views : ${views}` : `Clicks : ${clicks}`}
                  </span>
                </div>
              </>
            )}
          </div>

          <div className="flex flex-wrap items-center gap-3">
            <ReusePromptDialog
              isOpen={isReusePromptDialogOpen}
              onClose={() => toggleReusePromptDialogOpen(false)}
              blog={blog}
            />
            {isFailed ? (
              isRecoverable &&
              !isAssisted && (
                <>
                  <Button variant="gray" onClick={() => toggleReusePromptDialogOpen(true)}>
                    Show Prompt
                  </Button>
                  <Button variant="gray" onClick={() => regenerateBlog(blog._id as string)}>
                    Retry
                  </Button>
                </>
              )
            ) : isCompleted || isPaused ? (
              <Link to={redirect}>
                {isPaused ? (
                  <Button variant="gray">Continue with Co-Pilot</Button>
                ) : isCompleted ? (
                  <Button variant="gray">View Details</Button>
                ) : (
                  <Button variant="gray" disabled>
                    Generating Blog...
                  </Button>
                )}
              </Link>
            ) : (
              <></>
            )}

            {isCompleted && (
              <Link to={`/dashboard/blogs/${blog._id}/edit`}>
                <Button variant="gray">Edit Blog</Button>
              </Link>
            )}

            <BlogListContextMenu
              blog={blog}
              siteId={siteId}
              variant={variant}
              isCompleted={isCompleted}
              refetch={refetch}
            >
              {!isFailed && (
                <DropdownMenuItem onClick={() => toggleReusePromptDialogOpen(true)}>
                  <LuMousePointerClick />
                  Show Prompt
                </DropdownMenuItem>
              )}
              {menu}
            </BlogListContextMenu>
          </div>
        </div>
      </div>
    </div>
  );
};

const BlogPlatformList = ({ platforms, blog }: { platforms: BlogIntegration[]; blog: Blog }) => (
  <div className="flex gap-1">
    {platforms.map((p) => (
      <div key={p} className="rounded" style={{ backgroundColor: Theme.colors[p] }}>
        {blog[`${p}Link`] ? (
          <Link className="flex size-6 flex-center" to={blog[`${p}Link`]}>
            <img className="size-4" src={`/images/icons/integrations/${p}.svg`} />
          </Link>
        ) : (
          <div className="flex size-6 flex-center">
            <img className="size-4" src={`/images/icons/integrations/${p}.svg`} />
          </div>
        )}
      </div>
    ))}
  </div>
);

const ReusePromptDialog: React.FC<{ blog: Blog; isOpen: boolean; onClose: () => void }> = ({
  blog,
  isOpen,
  onClose,
}) => (
  <Dialog
    open={isOpen}
    onOpenChange={onClose}
    Icon={() => null}
    title="Blog Source Info"
    description=""
  >
    <BlogSource blog={blog} />
  </Dialog>
);

export default BlogCard;
