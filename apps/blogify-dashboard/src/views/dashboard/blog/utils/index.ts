import type { Blog } from '@ps/types';

const addImage = (imageLink: string, content: string) =>
  imageLink ? `<img src="${imageLink}" width="800" /><br>${content}` : content;

const addTitle = (title: string, content: string) => `<h1>${title}</h1><br>${content}`;

export const getCopyContent = ({
  title,
  content,
  image,
}: Pick<Blog, 'title' | 'image' | 'content'>) => addTitle(title, addImage(image, content));
