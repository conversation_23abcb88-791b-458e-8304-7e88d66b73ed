import type { Blog } from '@ps/types';

import printJS from 'print-js';
import toast from 'react-hot-toast';

import { API } from '@/services/api';

const downloadDocx = async (blogId: string | undefined, title: string) => {
  try {
    toast.loading('Processing docx file to download!', { duration: 4000 });
    const data: any = await API.fetchBlob(`blogs/${blogId}/download-docx`);

    const blob = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    // Create a URL for the Blob data
    const blobUrl = URL.createObjectURL(blob);

    // Create a temporary link and click it to trigger download
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `${title}.docx`; // Change this to the desired file name
    link.click();

    // Clean up by revoking the Blob URL
    URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error('Error while downloading the file:', error);
  }
};

const downloadPdf = async (blog: Blog) => {
  try {
    const printable = document.createElement('article');
    printable.id = 'printable';

    const img = document.createElement('img');
    img.src = blog.image || '/images/temp-bg.jpg';
    printable.appendChild(img);

    const title = document.createElement('h1');
    title.innerText = blog.title;
    printable.appendChild(title);

    const article = document.createElement('article');
    article.innerHTML = blog.content;
    printable.appendChild(article);

    printJS({
      documentTitle: blog.title,
      css: '/styles/print.css',
      scanStyles: false,
      type: 'html',
      printable,
    });
  } catch (error) {
    console.error('Error while downloading the file:', error);
  }
};

export { downloadDocx, downloadPdf };
