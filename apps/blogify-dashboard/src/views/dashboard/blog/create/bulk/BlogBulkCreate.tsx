import { useContext } from 'react';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { PageContainerMini } from '@/components/layout/PageContainer';

import BlogBulkCreateSourcesInput from './components/BlogBulkCreateSourcesInput';
import BlogCreatePublishSettings from '../components/BlogCreatePublishSettings';
import BlogBulkCreateProvider from './utils/BlogBulkCreateProvider';
import BlogAffiliateSettings from '../components/BlogAffiliateSettings';
import BlogContentSettings from '../components/BlogContentSettings';
import BlogCreateSettings from '../components/BlogCreateSettings';
import DashboardContainer from '../../../layout/DashboardContainer';

export default function BlogBulkCreate() {
  const abilities = useContext(UserAbilitiesContext);

  return (
    <DashboardContainer title="Bulk Blog Generation" cancelUrl="/dashboard/blogs/select-source">
      <PageContainerMini className="pb-2 pt-6">
        <BlogBulkCreateProvider>
          {({ contentSettings, setContentSettings }) => (
            <>
              <h2 className="mb-6 text-17 font-semibold">Blog Source</h2>

              <BlogBulkCreateSourcesInput />
              <BlogCreateSettings canUseCoPilot={false} />

              <BlogContentSettings
                contentSettings={contentSettings}
                setContentSettings={setContentSettings}
              />

              <div className="mt-20 flex flex-col gap-6">
                <BlogAffiliateSettings />
                {abilities.blog.publish && <BlogCreatePublishSettings />}
              </div>
            </>
          )}
        </BlogBulkCreateProvider>
      </PageContainerMini>
    </DashboardContainer>
  );
}
