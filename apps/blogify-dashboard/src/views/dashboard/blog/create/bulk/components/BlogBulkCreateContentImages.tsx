import type { BlogBulkCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import { useState } from 'react';
import { FaCog } from 'react-icons/fa';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { defaultValues } from '@/views/dashboard/image/utils';
import { Button } from '@ps/ui/components/button';

import BlogCreateImageGenerateDialog from './BlogCreateImageGenerateDialog';

export default function BlogBulkCreateContentImages() {
  const [selectedOption, selectOption] = useState<string>('none');
  const form = useFormikContext<BlogBulkCreate>();

  const numberOfImages = form.values.aiGeneratedContentImagesConfig?.count || 3;
  const costPerImage = form.values.aiGeneratedContentImagesConfig?.cost || 0;
  const sourceCount = form.values.sources?.length || 0;

  const handleOptionChange = (v: string) => {
    selectOption(v);
    if (v === 'ai-generated') {
      form.setFieldValue('aiGeneratedContentImagesConfig', { count: 3, cost: 1, ...defaultValues });
    }
  };

  return (
    <div>
      <label className="mb-2 text-15 font-medium">Content Image</label>

      <RadioGroup
        className="mt-1.5 rounded-lg border border-gray10"
        onValueChange={handleOptionChange}
        value={selectedOption}
      >
        <RadioGroupItem className="hidden" value="none" />

        <label className="flex gap-2.5 p-4">
          <RadioGroupItem value="ai-generated" />
          <div className="-mt-1">
            <h4 className="text-15 font-medium">Generate AI Image</h4>
            <p className="mt-0.5 text-13">
              Blogify will analyze your content & generate a content images.
            </p>

            <div className="mt-2 flex items-center justify-between">
              <BlogCreateImageGenerateDialog
                onApply={(v) => {
                  selectOption(v?.count ? 'ai-generated' : 'none');
                  form.setFieldValue('aiGeneratedContentImagesConfig', v);
                }}
                defaultCount={numberOfImages}
                multiple
              >
                <Button className="h-6 rounded-md px-1.5 !text-11 text-gray9" variant="secondary">
                  <FaCog />
                  CONFIGURE
                </Button>
              </BlogCreateImageGenerateDialog>
              {!!(sourceCount && numberOfImages && costPerImage) && (
                <p className="text-11 font-medium text-gray9">
                  ({sourceCount}x{numberOfImages}x{costPerImage}) ={' '}
                  {sourceCount * numberOfImages * costPerImage} Credits
                </p>
              )}
            </div>
          </div>
        </label>
      </RadioGroup>
    </div>
  );
}
