import type { BlogBulkCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import { FaCog } from 'react-icons/fa';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { defaultValues } from '@/views/dashboard/image/utils';
import { Button } from '@ps/ui/components/button';

import BlogCreateImageGenerateDialog from './BlogCreateImageGenerateDialog';

export default function BlogBulkCreateCoverImage() {
  const form = useFormikContext<BlogBulkCreate>();

  const costPerImage = form.values.aiGeneratedCoverImageConfig?.cost || 0;
  const sourceCount = form.values.sources?.length || 0;

  const handleOptionChange = (v: string) => {
    form.setFieldValue('coverImageType', v);
    if (v === 'thumbnail') {
      form.setFieldValue('aiGeneratedCoverImageConfig', undefined);
    } else {
      form.setFieldValue('aiGeneratedCoverImageConfig', { count: 1, cost: 1, ...defaultValues });
    }
  };

  return (
    <div>
      <label className="text-15 font-medium">Cover Image</label>

      <RadioGroup
        className="mt-1.5 rounded-lg border border-gray10"
        onValueChange={handleOptionChange}
        value={form.values.coverImageType}
      >
        <label className="flex gap-2.5 p-4">
          <RadioGroupItem value="thumbnail" />
          <div className="-mt-1">
            <h4 className="text-15 font-medium">Use source thumbnail</h4>
            <p className="mt-0.5 text-13">
              Blogify will use the thumbnail image of your source as cover image.
            </p>
          </div>
        </label>

        <label className="flex gap-2.5 border-t border-gray10 p-4">
          <RadioGroupItem value="ai-generated" />
          <div className="-mt-1">
            <h4 className="text-15 font-medium">Generate AI Image</h4>
            <p className="mt-0.5 text-13">
              Blogify will analyze your content & generate a cover image.
            </p>

            <div className="mt-2 flex items-center justify-between">
              <BlogCreateImageGenerateDialog
                onApply={(v) => {
                  form.setFieldValue('aiGeneratedCoverImageConfig', v);
                  form.setFieldValue('coverImageType', 'ai-generated');
                }}
              >
                <Button className="h-6 rounded-md px-1.5 !text-11 text-gray9" variant="secondary">
                  <FaCog />
                  CONFIGURE
                </Button>
              </BlogCreateImageGenerateDialog>
              {!!(sourceCount && costPerImage) && (
                <p className="text-11 font-medium text-gray9">
                  ({sourceCount}x1x{costPerImage}) = {sourceCount * costPerImage} Credits
                </p>
              )}
            </div>
          </div>
        </label>
      </RadioGroup>
    </div>
  );
}
