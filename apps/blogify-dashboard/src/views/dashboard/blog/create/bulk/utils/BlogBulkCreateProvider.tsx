import type { BlogBulkCreateContextType, BlogContentSetting } from './useBlogBulkCreate';
import type { BlogBulkCreate, BlogCreate, Blog } from '@ps/types';
import type { FormikHelpers, FormikProps } from 'formik';
import type { BlogIntegration } from '@ps/types/misc/integration.type';

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';
import { Formik } from 'formik';

import { useStoreActions, useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import BlogCreditLimitReachedDialog from '@/views/dashboard/blog/components/BlogCreditLimitReachedDialog';
import IntegrationExpiredDialog from '@/views/dashboard/blog/create/components/IntegrationExpiredDialog';
import AddBlogCredit from '@/views/dashboard/payment/subscription/AddBlogCredit';
import ErrorMessage from '@/components/form/ErrorMessage';
import Spinner from '@/components/misc/Spinner';
import config from '@/constants/config';

import { BlogBulkCreateContext, emptyBlog } from './useBlogBulkCreate';
import useBlogSource from '../../../context/useBlogSource';

type BlogCreateError =
  | string
  | { message: string[] }
  | { message: Record<BlogIntegration, boolean> }
  | Record<BlogIntegration, boolean>;

type Props = {
  children: React.ReactNode | ((v: BlogBulkCreateContextType) => React.ReactNode);
};

const defaultContentSettings: BlogContentSetting[] = [
  { label: 'Cover Image', checked: true },
  { label: 'Title', checked: true, disabled: true },
  // { label: 'Embed Source', checked: true },
  {
    label: 'Content',
    checked: true,
    disabled: true,
    draggable: true,
    children: [
      { label: 'Data Table', checked: false },
      { label: 'Data Charts', checked: false },
      { label: 'Quotation', checked: true },
      { label: 'Content Images', checked: true },
      { label: 'CTA', checked: false },
    ],
  },
  { label: 'TLDR', checked: true, draggable: true },
];

export default function BlogBulkCreateProvider({ children }: Props) {
  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const [isSocialErrorDialogOpen, toggleSocialErrorDialogOpen] = useToggle(false);
  const [contentSettings, setContentSettings] = useState(defaultContentSettings);
  const [socialError, setSocialError] = useState<Record<string, boolean>>({});
  const [detectedThumbnail, setDetectedThumbnail] = useState('');
  const [hasReachedLimit, toggleReachedLimit] = useToggle(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { sources: sourceList } = useBlogSource();

  const fetchUser = useStoreActions((s) => s.user.fetch);
  const user = useStoreState((s) => s.user.current);

  const initialValues: BlogBulkCreate = {
    ...emptyBlog,
    sources: [
      {
        ...emptyBlog.sources[0],
        inputLanguage: user.preferredInputLanguage || emptyBlog.sources[0].inputLanguage,
      },
    ],
    blogLanguage: user.preferredOutputLanguage || 'english',
  };

  const onSubmit = async (
    { sources, ...values }: BlogBulkCreate,
    actions: FormikHelpers<BlogBulkCreate>
  ) => {
    // console.log('Blog Create:', JSON.stringify({ sources, ...values }, null, 2));

    const blogGenerateCost = sources.reduce(
      (acc, source) => (['video', 'audio'].includes(source.sourceType) ? 10 : 5) + acc,
      0
    );
    const coverImageGenerationCost =
      sources.length * (values.aiGeneratedCoverImageConfig?.cost || 0);
    const contentImagesGenerationCost =
      sources.length *
      (values.aiGeneratedContentImagesConfig?.count || 0) *
      (values.aiGeneratedContentImagesConfig?.cost || 0);

    const creditCost = blogGenerateCost + coverImageGenerationCost + contentImagesGenerationCost;
    if (creditCost > user.credits) {
      return toggleReachedLimit();
    }

    const promises = [];
    for (const sourceInfo of sources) {
      if (!sourceInfo.url) continue;
      const source = sourceList.find((s) => s.name === sourceInfo.sourceName);
      if (!source) continue;

      const platforms = sourceInfo.platforms
        .map((sourceInfoPlatform) => {
          const platform = values.platforms.find((p) => p.platform === sourceInfoPlatform.platform);
          if (!platform || !sourceInfoPlatform.platform) return null;
          return {
            ...platform,
            timestamp: sourceInfoPlatform.timestamp,
          };
        })
        .filter((p) => !!p);

      const body: Omit<
        BlogCreate,
        'isBulk' | 'contentImages' | 'seoOption' | 'embedSourceAsCover' | 'seoInputKeywords'
      > = {
        ...sourceInfo,
        ...values,
        prompt: '',
        isPartOfBulkGeneration: true,
        platforms,
      };

      if (source.type === 'image') {
        const imageUrl =
          source.name === 'Instagram'
            ? body.image.replace(`${config.apiUrl}context/image-proxy?url=`, '')
            : body.image;
        body.url = source.inputType === 'upload' ? body.url : imageUrl;
        if (body.title) {
          body.prompt = body.title;
        }
      }
      // TLDR
      const contentPosition = contentSettings.findIndex((s) => s.label === 'Content');
      const tldrPosition = contentSettings.findIndex((s) => s.label === 'TLDR');
      const tldr = contentSettings.find((s) => s.label === 'TLDR');
      if (tldr?.checked) {
        body.tldrPosition = tldrPosition < contentPosition ? 'start' : 'end';
      }
      // Blog Size
      body.blogSize = getBlogSizeFromWordCount(body.wordCountApprox);
      const promise = API.post<Blog>(`blogs`, body)
        .then(() => {
          // setBlog(resp as Blog);
          // if (onBlogCreateSuccess && resp) {
          //   onBlogCreateSuccess(resp);
          // }
          fetchUser();
        })
        .catch((err: BlogCreateError) => {
          if (typeof err === 'string') setError(err);
          if (Array.isArray(err) && typeof err[0] === 'string') setError(err.join(', '));
          const hasPlatformError = (integrations: BlogIntegration[]) =>
            integrations.some((k) => body.platforms.map((i) => i.platform).includes(k));
          const err1 = err as { message: Record<BlogIntegration, boolean> };
          const err1Keys = Object.keys(err1?.message || {}) as BlogIntegration[];
          if (typeof err1?.message === 'object' && err1Keys.some((k) => !err1?.message[k])) {
            if (hasPlatformError(err1Keys)) {
              setSocialError(err1?.message);
              toggleSocialErrorDialogOpen();
            }
          }
          const err2 = err as Record<BlogIntegration, boolean>;
          const err2Keys = Object.keys(err2 || {}) as BlogIntegration[];
          if (typeof err2 === 'object' && err2Keys.some((k) => !err2[k])) {
            if (hasPlatformError(err2Keys)) {
              setSocialError(err2);
              toggleSocialErrorDialogOpen();
            }
          }
        });

      promises.push(promise);
    }

    await Promise.allSettled(promises).finally(() => {
      actions.setSubmitting(false);
      navigate('/dashboard/blogs');
    });
  };

  const value = {
    detectedThumbnail,
    setDetectedThumbnail,

    contentSettings,
    setContentSettings,
  };

  return (
    <BlogBulkCreateContext.Provider value={value}>
      <Formik
        initialValues={initialValues}
        validateOnChange={false}
        validateOnBlur={false}
        onSubmit={onSubmit}
      >
        {(form) => (
          <form
            onSubmit={(ev) => {
              ev.preventDefault();
              form.submitForm();
            }}
          >
            {typeof children === 'function' ? children(value) : children}

            <BlogCreateFooter error={error} form={form} />
          </form>
        )}
      </Formik>

      <BlogCreditLimitReachedDialog
        hasReachedLimit={hasReachedLimit}
        toggleReachedLimit={toggleReachedLimit}
        toggleAddBlogCreditDialog={toggleAddBlogCreditDialog}
      />

      <IntegrationExpiredDialog
        socialError={socialError}
        isSocialErrorDialogOpen={isSocialErrorDialogOpen}
        toggleSocialErrorDialogOpen={toggleSocialErrorDialogOpen}
      />

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
    </BlogBulkCreateContext.Provider>
  );
}

const BlogCreateFooter: React.FC<{
  form: FormikProps<BlogBulkCreate>;
  error: string;
}> = ({ form, error }) => (
  <div className="pt-10">
    {form.isValidating ? (
      <ErrorMessage className="pb-1">
        Please wait for the input validations to complete.
      </ErrorMessage>
    ) : (
      (!form.isValid || error) && (
        <ErrorMessage className="pb-1">
          {error || 'Please correct the errors before saving.'}
        </ErrorMessage>
      )
    )}
    <Button
      className="!h-10 w-full"
      disabled={form.isSubmitting || form.isValidating || !form.isValid}
      type="submit"
    >
      {form.isSubmitting ? (
        <Spinner />
      ) : (
        <span>
          Generate {form.values.sources.length > 1 ? `${form.values.sources.length} Blogs` : 'Blog'}
        </span>
      )}
    </Button>
  </div>
);

const getBlogSizeFromWordCount = (wordCount: number): string => {
  if (wordCount <= 1200) {
    return 'mini';
  }

  if (wordCount > 1200 && wordCount <= 2000) {
    return 'small';
  }

  if (wordCount > 2000 && wordCount <= 3000) {
    return 'medium';
  }

  if (wordCount > 3000 && wordCount <= 4000) {
    return 'large';
  }

  return 'x-large';
};
