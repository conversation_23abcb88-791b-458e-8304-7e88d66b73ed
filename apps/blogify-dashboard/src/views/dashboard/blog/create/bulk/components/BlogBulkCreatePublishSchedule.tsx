import type { BlogBulkCreate } from '@ps/types';
import type { ManipulateType } from 'dayjs';

import { useFormikContext } from 'formik';
import { useState } from 'react';
import dayjs from 'dayjs';

import { cn } from '@ps/ui/lib/utils';
import FormField from '@ps/ui/form/FormField';
import Counter from '@/components/misc/Counter';
import Select from '@ps/ui/form/Select';

export default function BlogBulkCreatePublishSchedule({ integration }: { integration: string }) {
  const [intervalType, setIntervalType] = useState<ManipulateType>('days');
  const [startDate, setStartDate] = useState(dayjs().add(15, 'minutes').format('YYYY-MM-DDTHH:mm'));
  const [interval, setInterval] = useState(3);

  const form = useFormikContext<BlogBulkCreate>();
  const timeZone = new Date()
    .toLocaleTimeString(undefined, { timeZoneName: 'short' })
    .split(' ')[2];

  const { platforms, sources } = form.values;
  const index = platforms.findIndex((i) => i.platform === integration);

  const handleSettingsChange = (
    _startDate: string,
    _interval: number,
    _intervalType: ManipulateType
  ) => {
    setStartDate(_startDate);
    setInterval(_interval);
    setIntervalType(_intervalType);

    if (_startDate && _interval && _intervalType) {
      sources.forEach((_, i) => {
        form.setFieldValue(
          `sources.${i}.platforms.${index}.timestamp`,
          dayjs(_startDate)
            .add(_interval * (i + 1), _intervalType)
            .format('YYYY-MM-DDTHH:mm')
        );
      });
    }
  };

  return (
    <div className="py-6">
      <div className="grid grid-cols-1 gap-x-4 md:grid-cols-2">
        <FormField
          className="block"
          label={
            <div className="flex items-center gap-1">
              Start Publishing From <span className="!text-13 text-gray9">({timeZone})</span>
            </div>
          }
          onChange={(ev) => handleSettingsChange(ev.target.value, interval, intervalType)}
          type="datetime-local"
          value={startDate}
        />

        <FormField label="Interval" type="custom">
          <div className="flex">
            <Counter
              className="w-2/3 rounded-r-none py-0"
              onChange={(v) => handleSettingsChange(startDate, v, intervalType)}
              defaultValue={interval}
              min={1}
            />
            <Select
              className="w-1/3 rounded-l-none border-l-0"
              onChange={(ev) =>
                handleSettingsChange(startDate, interval, ev.target.value as ManipulateType)
              }
              value={intervalType}
            >
              <option value="days">Day</option>
              <option value="hours">Hour</option>
              <option value="minutes">Minute</option>
            </Select>
          </div>
        </FormField>
      </div>

      <FormField label="Timeline" type="custom" noError>
        {sources.map((source, i) => (
          <div key={i} className="grid grid-cols-1 md:grid-cols-2">
            <div
              className={cn(
                'flex h-10 items-center gap-4 border border-b-0 border-gray10 px-4 md:border-b md:border-r-0',
                {
                  'rounded-t-lg md:rounded-t-none md:rounded-tl-lg': i === 0,
                  'md:rounded-bl-lg': sources.length - 1 === i,
                  'border-t-0': i !== 0,
                }
              )}
            >
              <span className="w-1/4 whitespace-nowrap text-15 font-medium">
                {getOrdinal(i + 1)} Blog
              </span>
              <a
                className="w-3/4 truncate text-gray9/70 hover:underline"
                href={source.url}
                target="_blank"
              >
                <span className="text-13 text-gray9">{source.url}</span>
              </a>
            </div>

            <div
              className={cn(
                'overflow-hidden border border-gray10 focus-within:z-10 focus-within:border-t focus-within:border-primary focus-within:shadow-[0_0_0_3px_rgba(242,71,13,0.1)]',
                {
                  'rounded-b-lg md:rounded-b-none md:rounded-br-lg': sources.length - 1 === i,
                  'md:rounded-tr-lg': i === 0,
                  'border-t-0': i !== 0,
                }
              )}
            >
              <input
                className="h-10 w-full px-4 text-15 font-medium md:h-9"
                type="datetime-local"
                {...form.getFieldProps(`sources.${i}.platforms.${index}.timestamp`)}
              />
            </div>
          </div>
        ))}
      </FormField>
    </div>
  );
}

const SUFFIXES: { [key: number]: string } = { 1: 'st', 2: 'nd', 3: 'rd' };
const getOrdinal = (num: number): string => {
  if (num % 100 >= 11 && num % 100 <= 13) {
    return `${num}th`;
  }

  const suffix = SUFFIXES[num % 10] || 'th';

  return `${num}${suffix}`;
};
