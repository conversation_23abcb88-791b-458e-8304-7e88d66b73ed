import type { BlogSourceName, BlogBulkCreate, BlogSource } from '@ps/types';

import { MdMoreHoriz } from 'react-icons/md';
import { useToggle } from 'react-use';
import { useState } from 'react';
import { ImBin2 } from 'react-icons/im';

import { useFormikContext, FieldArray } from 'formik';
import { getSourceNameFromUrl } from '@/utils/url';
import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';
import FormField from '@ps/ui/form/FormField';
import TagInput from '@/components/form/TagInput';
import Spinner from '@/components/misc/Spinner';
import Select from '@ps/ui/form/Select';

import { BlogUrlInputSchema } from '../../components/BlogCreateUrlInput';
import { LANGUAGE_CODES } from '../../utils/languages';
import { fetchUrlMeta } from '../../utils/UrlService';
import useBlogSource from '../../../context/useBlogSource';

export default function BlogBulkCreateSourcesInput() {
  const { values } = useFormikContext<BlogBulkCreate>();
  const user = useStoreState((s) => s.user.current);

  return (
    <FormField label="Add Source Links" type="custom">
      <FieldArray name="sources">
        {({ push, remove }) => (
          <>
            {values.sources.map((source, i) => (
              <SourceInput
                key={i}
                index={i}
                removeSource={() => remove(i)}
                isLast={values.sources.length - 1 === i}
                urlState={source.urlState}
              />
            ))}

            <Button
              className="mt-2 h-6 rounded-md px-2 !text-11 text-primary"
              onClick={() =>
                push({
                  url: '',
                  title: '',
                  image: '',
                  sourceType: 'video',
                  sourceName: 'YouTube',
                  inputLanguage: user.preferredInputLanguage || 'global english',
                  embedSource: false,
                  authorsToGiveCredit: [],
                  urlState: undefined,

                  // Publish Settings
                  platforms: [],
                })
              }
              variant="secondary"
            >
              +1 MORE
            </Button>
          </>
        )}
      </FieldArray>
    </FormField>
  );
}

const SourceInput = ({
  index,
  isLast,
  removeSource,
  urlState,
}: {
  index: number;
  isLast: boolean;
  removeSource: (_: number) => void;
  urlState: 'validating' | 'valid' | 'invalid' | undefined;
}) => {
  const [creditsInputVisible, toggleCreditsInput] = useToggle(false);
  const [allOptionsVisible, showAll] = useToggle(false);
  const [source, setSource] = useState<BlogSource | undefined>();
  const form = useFormikContext<BlogBulkCreate>();
  const { sources } = useBlogSource();

  const validateSource = async (url: string, _source?: BlogSource) => {
    if (!url || !_source) return;

    form.setFieldValue(`sources.${index}.urlState`, 'validating');
    const isValid = await BlogUrlInputSchema(_source).isValid(url);
    form.setFieldValue(`sources.${index}.urlState`, isValid ? 'valid' : 'invalid');

    if (isValid) {
      const meta = await fetchUrlMeta(url, _source.name);
      if (meta.image) {
        form.setFieldValue(`sources.${index}.image`, meta.image);
      }
      if (meta.title) {
        form.setFieldValue(`sources.${index}.title`, meta.title);
      }
    }
  };

  const handleUrlChange = async (url: string): Promise<BlogSource | undefined> => {
    form.setFieldValue(`sources.${index}.url`, url);

    const sourceName = getSourceNameFromUrl(url);
    if (sourceName && sourceName !== source?.name) {
      return handleSourceChange(sourceName);
    }
  };

  const handleSourceChange = (sourceName: BlogSourceName): BlogSource | undefined => {
    const _source = sources.find((s) => s.name === sourceName);

    if (_source) {
      setSource(_source);
      form.setFieldValue(`sources.${index}.sourceName`, _source.name);
      form.setFieldValue(`sources.${index}.sourceType`, _source.type);
    }

    return _source;
  };

  const handleEmbedChange = (ev: React.ChangeEvent<HTMLSelectElement>) => {
    form.setFieldValue(`sources.${index}.embedSource`, ev.target.value === 'yes');
  };

  const handleGiveCreditChange = (ev: React.ChangeEvent<HTMLSelectElement>) => {
    toggleCreditsInput(ev.target.value === 'yes');
  };

  return (
    <label
      className={cn(
        'block border border-b-0 border-gray10 p-4 focus-within:border-b focus-within:border-primary focus-within:shadow-[0_0_0_3px_rgba(242,71,13,0.1)] hover:bg-cornflowerBlue',
        {
          'rounded-t-lg': index === 0,
          'border-b rounded-b-lg': isLast,
        }
      )}
    >
      <div className="relative">
        <Input
          className="h-5 w-11/12 border-none p-0 hover:bg-cornflowerBlue"
          placeholder="Insert your link here..."
          {...form.getFieldProps(`sources.${index}.url`)}
          onChange={async (ev) => {
            const _source = await handleUrlChange(ev.target.value);
            validateSource(ev.target.value, _source);
          }}
        />

        {urlState === 'validating' && <Spinner className="absolute -right-1 -top-1" />}
        {urlState === 'valid' && (
          <img className="absolute right-0 top-0 size-5" src="/images/icons/icon-success.svg" />
        )}
        {urlState === 'invalid' && (
          <img className="absolute right-0 top-0 size-5" src="/images/icons/icon-error.svg" />
        )}
      </div>

      <div className="mt-2.5 flex items-end justify-between gap-2">
        <div className="flex flex-wrap items-center gap-2">
          <Select
            className="w-fit min-w-[116px]"
            onChange={(ev) => {
              const _source = handleSourceChange(ev.target.value as BlogSourceName);
              validateSource(form.values.sources[index].url, _source);
            }}
            value={source?.name}
            variant="secondary"
            height="sm"
          >
            <option value="">Select Source</option>
            {sources
              .filter((s) => s.inputType === 'url')
              .map((s) => (
                <option key={s.name} value={s.name}>
                  {s.name}
                </option>
              ))}
          </Select>

          <Select
            className="min-w-[130px]"
            variant="secondary"
            height="sm"
            {...form.getFieldProps(`sources.${index}.inputLanguage`)}
          >
            <option>Select Language</option>
            {LANGUAGE_CODES.map((l) => (
              <option key={l} value={l.toLowerCase()}>
                {l}
              </option>
            ))}
          </Select>

          {allOptionsVisible && (
            <>
              {source?.isEmbeddable && (
                <Select
                  className="min-w-[96px]"
                  variant="secondary"
                  height="sm"
                  onChange={handleEmbedChange}
                >
                  <option value="no">Embed : No</option>
                  <option value="yes">Embed : Yes</option>
                </Select>
              )}

              <Select
                className="w-[140px]"
                variant="secondary"
                height="sm"
                onChange={handleGiveCreditChange}
              >
                <option value="no">Credit Source : No</option>
                <option value="yes">Credit Source : Yes</option>
              </Select>
            </>
          )}

          {!allOptionsVisible && (
            <Button className="size-6 rounded-md p-0" variant="secondary" onClick={showAll}>
              <MdMoreHoriz className="text-gray9" />
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            onClick={() => validateSource(form.values.sources[index].url, source)}
            className="h-6 rounded-md !text-11 text-gray9"
            variant="secondary"
          >
            RECHECK
          </Button>
          {!(isLast && index === 0) && (
            <Button
              onClick={() => removeSource(index)}
              className="size-6 rounded-md p-0"
              variant="secondary"
            >
              <ImBin2 className="text-gray9" />
            </Button>
          )}
        </div>
      </div>

      {creditsInputVisible && (
        <TagInput
          className="mt-2.5"
          setTags={(tags) => form.setFieldValue(`sources.${index}.authorsToGiveCredit`, tags)}
          setError={(message) => {
            if (message)
              form.getFieldHelpers(`sources.${index}.authorsToGiveCredit`).setError(message);
          }}
          tags={form.getFieldProps(`sources.${index}.authorsToGiveCredit`).value}
          placeholder="eg. MKBHD, Everyday Astronaud, Vsauce"
          name={`sources.${index}.authorsToGiveCredit`}
          type="tags"
        />
      )}
    </label>
  );
};
