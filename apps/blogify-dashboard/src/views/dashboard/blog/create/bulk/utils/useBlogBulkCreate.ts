import type { BlogBulkCreate } from '@ps/types';

import { createContext, useContext } from 'react';

export const emptyBlog: BlogBulkCreate = {
  isBulk: true,
  // Sources
  sources: [
    {
      url: '',
      title: '',
      image: '',
      sourceType: 'video',
      sourceName: 'YouTube',
      inputLanguage: 'global english',
      embedSource: false,
      authorsToGiveCredit: [],

      // Publish Settings
      platforms: [],
    },
  ],

  // Settings
  generationMode: 'auto',
  pov: 'First Person',
  blogTone: 'Neutral',
  blogSize: 'medium',
  wordCountApprox: 2000,
  sourceSimilarity: 70,
  blogLanguage: 'english',
  tldrPosition: 'end',
  affiliateCommissionOptIn: false,
  affiliateTargetLocation: 'United States',

  // Images
  coverImageType: 'thumbnail',

  // Publish Settings
  platforms: [],

  // Others
  publishAt: '',
  cta: {
    text: '',
    bgColor: '000000',
    borderRadius: 10,
    link: '',
  },

  tableOption: 'No Table',
  chartOption: 'No Chart',
};

export type BlogContentSetting = {
  label: string;
  checked: boolean;
  disabled?: boolean;
  draggable?: boolean;
  children?: BlogContentSetting[];
};

export interface BlogBulkCreateContextType {
  detectedThumbnail: string;
  setDetectedThumbnail: React.Dispatch<string>;

  contentSettings: BlogContentSetting[];
  setContentSettings: (_: BlogContentSetting[]) => void;
}

const defaultFn = () => {};
const defaultContext: BlogBulkCreateContextType = {
  detectedThumbnail: '',
  setDetectedThumbnail: defaultFn,

  contentSettings: [],
  setContentSettings: defaultFn,
};

const BlogBulkCreateContext = createContext<BlogBulkCreateContextType>(defaultContext);
BlogBulkCreateContext.displayName = 'BlogBulkCreateContext';

const useBlogBulkCreate = () => {
  const context = useContext(BlogBulkCreateContext);

  if (context === undefined) {
    throw new Error('useBlogBulkCreate must be used within a BlogBulkCreate.');
  }

  return context;
};

export default useBlogBulkCreate;
export { BlogBulkCreateContext };
