import type { BlogBulkCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import { RiImageAiLine } from 'react-icons/ri';

import { imageGenerateCommonSchema, defaultValues, MODELS } from '@/views/dashboard/image/utils';
import Dialog, { DialogClose } from '@ps/ui/components/dialog';
import { zodResolver, z } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import ImageModelSizeSelector from '@/views/dashboard/image/components/ImageModelSizeSelector';
import ImageStyleSelector from '@/views/dashboard/image/components/ImageStyleSelector';
import FormField from '@ps/ui/form/FormField';
import Counter from '@/components/misc/Counter';

const imageGenerateSchema = z.object({
  count: z.number({ required_error: 'Image count is required' }).min(1).max(6),
  cost: z.number().optional().default(0),
  ...imageGenerateCommonSchema,
});
type ImageGenerateSchema = z.infer<typeof imageGenerateSchema>;

export default function BlogCreateImageGenerateDialog({
  defaultCount,
  multiple,
  children,
  onApply,
}: {
  defaultCount?: number;
  multiple?: boolean;
  children: React.ReactNode;
  onApply: (_: BlogBulkCreate['aiGeneratedCoverImageConfig']) => void;
}) {
  const { getInputFields, getValues, setValue, watch } = useForm<ImageGenerateSchema>({
    resolver: zodResolver(imageGenerateSchema),
    defaultValues: { count: 1, ...defaultValues, style: 'Photorealistic' },
  });
  const form = useFormikContext<BlogBulkCreate>();

  const submit = async () => {
    const data = getValues();
    const _id = parseInt(data.sizeId, 10);
    const size = MODELS[data.model as keyof typeof MODELS].resolutions.find((r) => r.id === _id);
    data.quality = size?.quality || 'standard';
    data.size = size?.size || '1024x1024';
    data.cost = size?.cost || 0;
    onApply(data);
  };

  return (
    <Dialog
      trigger={children}
      Icon={RiImageAiLine}
      title="Generate AI Image"
      description={
        multiple
          ? 'Blogify will analyze the content of your blog and generate a few images for each content section.'
          : 'Blogify will analyze the title of your blog and generate a cover image for it.'
      }
      actions={
        <DialogClose asChild>
          <Button className="w-full" onClick={() => submit()}>
            Apply
          </Button>
        </DialogClose>
      }
    >
      <FormField label="Number of images" type="custom">
        {multiple ? (
          <Counter
            className="mb-10"
            {...getInputFields('count')}
            onChange={(v) => getInputFields('count').onChange({ target: { value: v } })}
            defaultValue={defaultCount || Number(getInputFields('count').defaultValue)}
            text={form.values.isBulk ? 'Image per blog' : 'Images'}
            min={0}
            max={6}
          />
        ) : (
          <div className="mb-10 flex h-10 rounded-lg border border-gray10 flex-center">
            <span className="text-15 font-medium">
              1 {form.values.isBulk ? 'Image per blog' : 'Image'}
            </span>
          </div>
        )}
      </FormField>

      <ImageStyleSelector value={watch('style')} onChange={(s) => setValue('style', s)} />

      <ImageModelSizeSelector
        defaultModel={getValues('model')}
        model={watch('model')}
        setModel={(v) => setValue('model', v)}
        defaultSizeId={getValues('sizeId')}
        setSizeId={(v) => setValue('sizeId', v)}
      />
    </Dialog>
  );
}
