import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import { GiSteeringWheel } from 'react-icons/gi';
import { BsStars } from 'react-icons/bs';
import React from 'react';

import BlogCreateRadioBox from './BlogCreateRadioBox';

const BlogCreateModeSelector: React.FC<{ canUseCoPilot: boolean }> = ({ canUseCoPilot }) => {
  const form = useFormikContext<BlogCreate>();

  return (
    <div className="grid gap-4 sm:grid-cols-2">
      <BlogCreateRadioBox
        icon={<BsStars />}
        title="Auto - Pilot"
        description="Blogify will automatically generate your blog outline & content."
        {...form.getFieldProps('generationMode')}
        value="auto"
      />
      {canUseCoPilot && (
        <BlogCreateRadioBox
          icon={<GiSteeringWheel />}
          title="Co - Pilot"
          description="You decide the outline & content of your blog."
          {...form.getFieldProps('generationMode')}
          value="assisted"
        />
      )}
    </div>
  );
};

export default BlogCreateModeSelector;
