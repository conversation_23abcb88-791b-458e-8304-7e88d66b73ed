import { BlogCreate } from '@ps/types';
import { Input } from '@ps/ui/components/input';
import { useFormikContext } from 'formik';
import { useEffect, useState } from 'react';
import { BsLink45Deg } from 'react-icons/bs';
import { CgMathMinus, CgMathPlus } from 'react-icons/cg';
import { IoColorPalette } from 'react-icons/io5';
import { TbBorderCorners } from 'react-icons/tb';

const Cta = () => {
  const form = useFormikContext<BlogCreate>();
  const [color, setColor] = useState('000000');
  const [radius, setRadius] = useState(10);

  useEffect(() => {
    const ctaData = form.values.cta;
    if (ctaData) {
      setColor(ctaData.bgColor || '000000');
      setRadius(ctaData.borderRadius || 10);
    }
  }, [form.values.cta]);

  const handleRadiusChange = (action: 'minus' | 'plus') => {
    const newRadius = action === 'minus' ? Math.max(0, radius - 2) : Math.min(50, radius + 2);
    setRadius(newRadius);
    form.setFieldValue('cta.borderRadius', newRadius);
  };

  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value.replace('#', '');
    setColor(newColor);
    form.setFieldValue('cta.bgColor', newColor);
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    form.setFieldValue('cta.link', event.target.value);
  };

  return (
    <>
      <p className="mb-2 text-15 font-medium">CTA</p>
      <div className="space-y-2">
        <div className="relative">
          <Input placeholder="eg. Click here to buy" {...form.getFieldProps('cta.text')} />
        </div>

        <div className="flex space-x-2">
          <div className="relative w-1/2">
            <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
              <IoColorPalette className="text-gray-500" />
            </div>
            <div className="absolute right-0 top-0 flex h-full items-center px-3">
              <input
                type="color"
                value={`#${color}`}
                onChange={handleColorChange}
                className="size-6 cursor-pointer rounded p-0"
              />
            </div>
            <Input
              value={color}
              onChange={handleColorChange}
              placeholder="Color code"
              className="pl-12 pr-10 uppercase"
            />
          </div>

          <div className="relative w-1/2">
            <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
              <TbBorderCorners className="text-gray-500" />
            </div>
            <div className="absolute right-0 top-0 flex h-full items-center space-x-2 px-3">
              <CgMathMinus
                className="size-6 cursor-pointer rounded border border-[#e3dbd9] p-[2px] shadow active:border-gray12"
                onClick={() => handleRadiusChange('minus')}
                style={{ opacity: radius === 0 ? 0.5 : 1 }}
              />
              <CgMathPlus
                className="size-6 cursor-pointer rounded border border-[#e3dbd9] p-[2px] shadow active:border-gray12"
                onClick={() => handleRadiusChange('plus')}
                style={{ opacity: radius === 50 ? 0.5 : 1 }}
              />
            </div>
            <Input value={`${radius}px`} placeholder="Radius" className="pl-12 pr-20" readOnly />
          </div>
        </div>

        <div className="relative">
          <div className="absolute left-0 top-0 flex h-full items-center rounded-l-lg border border-[#e3dbd9] bg-cornflowerBlue px-3">
            <BsLink45Deg className="text-gray-500" />
          </div>
          <Input
            placeholder="eg. https://www.amazon.com/Apple-iPhone-Version-512GB-Titanium/dp/B0DNTK5373/ref=sr_1_6..."
            className="pl-12"
            {...form.getFieldProps('cta.link')}
            onChange={handleUrlChange}
          />
        </div>
      </div>
    </>
  );
};

export default Cta;
