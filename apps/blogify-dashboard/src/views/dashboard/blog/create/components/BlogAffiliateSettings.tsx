import type { ReactSelectOptionType } from '@/components/form/SelectOption';
import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import { COUNTRIES } from '@/constants/address';
import { Switch } from '@ps/ui/components/switch';
import SelectOption from '@/components/form/SelectOption';

const countries: ReactSelectOptionType[] = COUNTRIES.map((c) => ({
  value: c.iso2,
  label: c.name,
}));

export default function BlogAffiliateSettings() {
  const form = useFormikContext<BlogCreate>();

  return (
    <div className="rounded-lg border border-gray10">
      <div className="relative z-0 flex items-center overflow-hidden p-4">
        <div className="absolute right-0 top-0 h-full w-2/3  bg-gradient-to-r from-white from-10% to-[#e2d9ee]" />

        <div className="relative z-10">
          <label className="flex items-center gap-3 text-17 font-semibold">
            <Switch onCheckedChange={(v) => form.setFieldValue('affiliateCommissionOptIn', v)} />
            Affiliate Monetization
          </label>

          <div className="mt-3 text-md">
            Earn money by adding affiliate links to your blog. Blogify will automatically analyze
            your content & place appropriate affiliate links.
          </div>
        </div>

        <div className="relative z-10 hidden h-20 w-32 shrink-0 bg-[#d1c4e5]" />
      </div>

      {form.values.affiliateCommissionOptIn && (
        <div className="px-4 pb-4 pt-3">
          <SelectOption
            label="Target Location"
            options={countries}
            {...form.getFieldProps('affiliateTargetLocation')}
          />
        </div>
      )}
    </div>
  );
}
