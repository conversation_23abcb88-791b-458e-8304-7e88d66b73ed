import type { BlogSourceName, BlogCreate, BlogSource } from '@ps/types';
import type { UrlMeta } from '../utils/UrlService';

import { useFormikContext } from 'formik';
import { useDebounce } from 'react-use';
import { useState } from 'react';
import * as Yup from 'yup';

import { isValidUrl } from '@/utils/url';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';

import { fetchUrlMeta, validateUrl } from '../utils/UrlService';
import BlogCreateUrlPreview from './BlogCreateUrlPreview';
import useBlogCreate from '../utils/useBlogCreate';

type BlogUrlInputStatus = 'blank' | 'analyzing' | 'invalid' | 'valid';

const BlogUrlInput: React.FC = () => {
  const [urlMeta, setUrlMeta] = useState<UrlMeta>({ image: '', title: '', description: '' });
  const form = useFormikContext<BlogCreate>();
  const {
    source: { iconUrl, name, type },
    setDetectedThumbnail,
  } = useBlogCreate();

  const onURLInput = async (url: string) => {
    form.setFieldError('url', '');
    form.validateField('url');

    if (!isValidUrl(url)) return;

    const meta = await fetchUrlMeta(url, name as BlogSourceName);
    setUrlMeta(meta);

    if (meta.image) {
      setDetectedThumbnail(meta.image);
      // if (!form.values.image) {
      form.setFieldValue('image', meta.image);
      // }
    }
    if (meta.title) {
      form.setFieldValue('title', meta.title);
    }
  };

  const urlInputStatus: BlogUrlInputStatus = form.isValidating
    ? 'analyzing'
    : form.values.url?.length && form.isValid
      ? 'valid'
      : form.errors.url
        ? 'invalid'
        : 'blank';

  const commonErrorMessage = (
    <>
      Make sure the {type === 'webLink' ? '' : type} link is publicly accessible
      {['audio', 'video'].includes(type) ? ' and stream able' : ''}.
    </>
  );

  useDebounce(() => form.values.url && onURLInput(form.values.url), 500, [form.values.url]);

  return (
    <>
      <h3 className="mb-2 text-15 font-medium">Source Link</h3>
      <div
        className={`rounded-lg border border-gray10 p-1 ${cn({
          'bg-bg3': urlInputStatus === 'valid',
          'bg-bg4': urlInputStatus === 'invalid',
        })}`}
      >
        <div className="relative">
          <img src={iconUrl} className="absolute left-3 top-2 block size-5" />
          <Input
            className="w-full rounded-lg pl-10"
            placeholder={name ? `Insert your ${name} link here` : 'Insert your link here'}
            type="url"
            {...form.getFieldProps('url')}
            onBlur={(e) => {
              form.validateField('url');
              form.getFieldProps('url').onBlur(e);
            }}
          />
        </div>

        {urlInputStatus === 'blank' && (
          <div className="flex min-h-[154px] flex-center">
            <p className="max-w-xs text-center text-sm xs:max-w-full">
              <img className="size-[100px]" src="/images/icons/link-blank.png" />
            </p>
          </div>
        )}

        {urlInputStatus === 'analyzing' && (
          <BlogUrlStatusBox title="Analyzing your link" image="analyze.gif" isLoading>
            {commonErrorMessage}
          </BlogUrlStatusBox>
        )}

        {urlInputStatus === 'invalid' && (
          <BlogUrlStatusBox
            title={form.values.url ? 'Invalid URL' : 'URL is required'}
            image="sad.svg"
          >
            {form.errors.url ? (
              <>{form.errors.url}</>
            ) : (
              <>
                The link you shared do not have a {type} or the {type} is not accessible.
                <br />
                Please review your {type} link or report an issue.
              </>
            )}
          </BlogUrlStatusBox>
        )}

        {urlInputStatus === 'valid' && (
          <>
            {(urlMeta.title || urlMeta.image) && urlMeta.title !== 'Video Not Available' ? (
              <BlogCreateUrlPreview {...urlMeta} />
            ) : (
              <BlogUrlStatusBox title="Valid URL" image="happy.svg">
                We can generate blog from this URL.
              </BlogUrlStatusBox>
            )}
          </>
        )}
      </div>
    </>
  );
};

type BlogUrlStatusBoxProps = {
  title: string;
  image: string;
  children: React.ReactNode;
  isLoading?: boolean;
};
const BlogUrlStatusBox: React.FC<BlogUrlStatusBoxProps> = ({
  title,
  image,
  children,
  isLoading,
}) => (
  <div className="flex min-h-[154px] flex-center">
    <div className="flex flex-col flex-center">
      <img className={isLoading ? 'size-10 animate-spin' : ''} src={`/images/icons/${image}`} />
      <h4 className="mt-2 text-sm font-semibold">{title}</h4>
      <p className="mt-1 max-w-xs text-center text-xs font-normal xs:max-w-full">{children}</p>
    </div>
  </div>
);

const validUrls = new Set();
export const BlogUrlInputSchema = ({ inputType, type, name }: BlogSource) =>
  Yup.lazy(() =>
    inputType === 'url'
      ? Yup.string()
          .url('Please enter a valid URL')
          .required(`Please enter a ${name} URL to generate blog`)
          .test('is-correct-source', `The provided link is not a valid ${name} url.`, (url) =>
            isValidUrl(url, name)
          )
          .test('is-valid-url', 'Invalid URL provided.', async (url, tc) => {
            if (validUrls.has(url)) return true;
            const isValidSourceUrl = isValidUrl(url, name);
            if (!isValidSourceUrl) return false;

            const message = await validateUrl(url, type, name);
            if (message) {
              return tc.createError({ message });
            } else {
              validUrls.add(url);
            }
            return true;
          })
      : inputType === 'upload'
        ? Yup.string().required('File is required')
        : Yup.string()
  );

export default BlogUrlInput;
