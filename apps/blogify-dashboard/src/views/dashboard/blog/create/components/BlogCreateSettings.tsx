import BlogCreateModeSelector from './BlogCreateModeSelector';
import BlogCreateMiscSettings from './BlogCreateMiscSettings';
import BlogCreatePovSelector from './BlogCreatePovSelector';

type Props = { canUseCoPilot?: boolean };
const BlogCreateSettings: React.FC<Props> = ({ canUseCoPilot = true }) => (
  <>
    <h2 className="mt-12 text-lg font-semibold">Blog setting</h2>

    <h3 className="pb-3 pt-5 text-md font-medium">Generation Mode</h3>
    <BlogCreateModeSelector canUseCoPilot={canUseCoPilot} />

    <h3 className="mt-5 pb-3 pt-5 text-md font-medium">Blog's Point of View</h3>
    <BlogCreatePovSelector />

    <BlogCreateMiscSettings />
  </>
);

export default BlogCreateSettings;
