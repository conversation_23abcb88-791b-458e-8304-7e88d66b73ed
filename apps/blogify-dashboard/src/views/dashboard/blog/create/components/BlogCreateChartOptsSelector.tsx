import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import SelectOption from '@/components/form/SelectOption';

const chartOptions = [
  { label: 'Create a single chart', value: 'One Chart' },
  { label: 'Create multiple charts', value: 'Two Charts' },
  { label: 'Create charts for each section', value: 'Charts for Each Section' },
  { label: 'No Charts', value: 'No Chart' },
];

const BlogCreateChartOptsSelector: React.FC = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <SelectOption
      className="mt-5"
      label="Generate Chart / Data Illustration"
      options={chartOptions}
      {...form.getFieldProps('chartOption')}
    />
  );
};

export default BlogCreateChartOptsSelector;
