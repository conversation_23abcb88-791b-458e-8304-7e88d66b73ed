import type { BlogBulkCreate, BlogCreate } from '@ps/types';

import { FaCheckCircle, FaRegCircle } from 'react-icons/fa';
import { use, useContext, useState } from 'react';
import { useFormikContext } from 'formik';
import { IoCloseCircle } from 'react-icons/io5';
import { FaCirclePlus } from 'react-icons/fa6';
import { useToggle } from 'react-use';
import dayjs from 'dayjs';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { getConnectUrl } from '@/utils/integration';
import { Button } from '@ps/ui/components/button';
import { Switch } from '@ps/ui/components/switch';
import { cn } from '@ps/ui/lib/utils';
import Link from '@/components/common/Link';
import Tabs from '@ps/ui/components/tabs';

import BlogBulkCreatePublishSchedule from '../bulk/components/BlogBulkCreatePublishSchedule';
import SiteSelector, { type SelectedSite } from '@/views/dashboard/blog/publish/SiteSelector';

type PublishOption = 'draft' | 'publish-auto' | 'publish-schedule';

const PUBLISH_OPTIONS: {
  value: PublishOption;
  label: string;
  description: string;
}[] = [
  {
    value: 'draft',
    label: 'Publish as Draft',
    description: 'The blog will be published as a draft on your site.',
  },
  {
    value: 'publish-auto',
    label: 'Publish Now',
    description: 'The blog will be published directly on your sites.',
  },
  {
    value: 'publish-schedule',
    label: 'Schedule to Publish',
    description: 'The blog will be published directly on your sites at a scheduled time & date.',
  },
];

type CommonProps = { integration: string; isConnected?: boolean };

const BlogCreatePublishSettings = () => {
  const integrationsData = useContext(IntegrationDetailsContext);
  const [shouldPublish, setShouldPublish] = useToggle(false);

  const disconnected = Object.keys(integrationsData ?? {}).filter(
    (int) => !integrationsData[int].connected
  );
  const connected = Object.keys(integrationsData ?? {}).filter(
    (int) => integrationsData[int].connected
  );
  const integrations = [...connected, ...disconnected];

  return (
    <div className="overflow-hidden rounded-lg border border-gray10">
      <div className="relative z-0 flex items-center p-4">
        <div className="absolute right-0 top-0 h-full w-2/3 bg-gradient-to-r from-white from-10% to-[#d2e7fc]" />

        <div className="relative z-10">
          <label className="flex items-center gap-3 text-17 font-semibold">
            <Switch onCheckedChange={setShouldPublish} />
            Publish
          </label>

          <div className="mt-3 text-md">
            Publish this blog to your connected platforms automatically. You can also schedule for a
            specific time for each platform.
          </div>
        </div>

        <div className="relative z-10 hidden h-20 w-32 shrink-0 bg-[#bddaf4]" />
      </div>

      {shouldPublish && (
        <>
          <h5 className="px-4 pt-4 text-xs font-semibold tracking-widest text-primary">
            CONNECTED PLATFORMS
          </h5>

          <Tabs.Root>
            <Tabs.List className="flex gap-3 p-4 pt-1.5">
              {integrations.map((integration, i) => (
                <IntegrationTrigger
                  key={i}
                  integration={integration}
                  isConnected={connected.includes(integration)}
                />
              ))}
            </Tabs.List>

            <div className="px-4 pb-4">
              {integrations.map((integration, i) => (
                <PublishSettings
                  key={i}
                  integration={integration}
                  isConnected={connected.includes(integration)}
                />
              ))}
            </div>
          </Tabs.Root>
        </>
      )}
    </div>
  );
};

const IntegrationTrigger: React.FC<CommonProps & { onClick?: () => void }> = ({
  integration,
  isConnected,
  onClick,
}) => {
  const form = useFormikContext<BlogCreate>();
  const details = useContext(IntegrationDetailsContext);

  const isSelected = form.values.platforms?.some((p) => p.platform === integration);

  const onSelectIntegration = () => {
    if (!isConnected) return;
    const platforms = form.values.platforms;
    if (platforms.some((i) => i.platform === integration)) return;

    form.setFieldValue('platforms', [
      ...platforms,
      { platform: integration, timestamp: '', draft: details[integration]?.drafts },
    ]);

    // For Bulk
    if (form.values.isBulk) {
      const sources = (form.values as unknown as BlogBulkCreate)?.sources || [];
      sources.forEach((source, i) => {
        if (source.platforms.some((j) => j.platform === integration)) return;

        form.setFieldValue(`sources.${i}.platforms`, [
          ...source.platforms,
          { platform: integration, timestamp: '', draft: details[integration]?.drafts },
        ]);
      });
    }

    if (onClick) onClick();
  };

  return (
    <Tabs.Trigger className="relative" onClick={onSelectIntegration} value={integration}>
      <div
        className={`flex size-10 rounded-lg flex-center ${cn({
          'opacity-50 saturate-0': !isConnected,
        })}`}
        style={{ backgroundColor: details[integration]?.theme }}
      >
        <img className="size-6" src={details[integration]?.logoURL} />
      </div>

      <span className="absolute -bottom-1 -right-1 rounded-full bg-white ring-2 ring-white">
        {isSelected ? (
          <FaCheckCircle size={14} className="text-success" />
        ) : !isConnected ? (
          <FaCirclePlus size={14} className="text-primary" />
        ) : (
          <FaRegCircle size={14} className="text-success" />
        )}
      </span>
    </Tabs.Trigger>
  );
};

// const getPublishOption = (integration: IntegrationDetail) =>
//   !integration.drafts && !integration.canSchedule
//     ? [PUBLISH_OPTIONS[1]]
//     : !integration.drafts
//       ? PUBLISH_OPTIONS.slice(1)
//       : !integration.canSchedule
//         ? PUBLISH_OPTIONS.slice(0, 2)
//         : PUBLISH_OPTIONS;

const PublishSettings: React.FC<CommonProps & { children?: React.ReactNode }> = ({
  integration,
  isConnected,
  children,
}) => {
  const integrationDetails = useContext(IntegrationDetailsContext);
  // const publishOptions = getPublishOption(integrationDetails[integration]);
  const publishOptions = PUBLISH_OPTIONS;

  const [publishOption, setPublishOption] = useState<PublishOption>(
    publishOptions.length === 3 ? 'draft' : 'publish-auto'
  );
  const form = useFormikContext<BlogCreate>();

  const onPublishOptionChange = (option: PublishOption) => {
    setPublishOption(option);
    const platforms = form.values.platforms;
    const index = platforms.findIndex((i) => i.platform === integration);
    if (index >= 0) {
      platforms[index].draft = option === 'draft';
      form.setFieldValue('platforms', platforms);
    }
  };

  return (
    <Tabs.Content value={integration} className="border-t border-gray11">
      {isConnected ? (
        <>
          <PublishSettingsHeader integration={integration} />

          <div className="mb-6 ml-[52px] mt-2">
            <div className="overflow-hidden rounded-lg border border-gray11">
              <RadioGroup
                className="gap-0"
                onValueChange={(po: PublishOption) => onPublishOptionChange(po)}
                defaultValue={publishOption}
              >
                {publishOptions.map((option, i) => (
                  <label
                    key={i}
                    className={cn('flex items-center gap-4 px-4 py-3 transition-all hover:bg-bg2', {
                      'border-t border-gray10': i != 0,
                    })}
                  >
                    <RadioGroupItem {...option} />

                    <div>
                      <h5 className="text-md font-medium">{option.label}</h5>
                      <p className="text-sm text-gray9">
                        {option.description.replace('WordPress', integration)}
                      </p>
                    </div>
                  </label>
                ))}
              </RadioGroup>
            </div>

            {['draft', 'publish-auto'].includes(publishOption) && (
              <PublishSiteSelector integration={integration} />
            )}

            {publishOption === 'publish-schedule' && (
              <>
                {form.values.isBulk ? (
                  <BlogBulkCreatePublishSchedule integration={integration} />
                ) : (
                  <PublishDateTimeSelector integration={integration} />
                )}
                <PublishSiteSelector integration={integration} />
              </>
            )}
          </div>
        </>
      ) : (
        <div className="flex min-h-72 w-full flex-col flex-center">
          <h2 className="mb-2 text-md font-medium">
            {integration === 'blogify'
              ? `You haven't added any website yet.`
              : `${integrationDetails[integration]?.website} is not connected`}
          </h2>
          <Link to={integrationDetails[integration]?.tutorial || getConnectUrl(integration)}>
            <Button className="mt-2 min-w-32" variant="secondary" type="button">
              {integration === 'blogify' ? 'Host a Blog Site' : 'Connect'}
            </Button>
          </Link>
        </div>
      )}

      {children}
    </Tabs.Content>
  );
};

const PublishSettingsHeader: React.FC<CommonProps> = ({ integration }) => {
  const form = useFormikContext<BlogCreate>();
  const integrationDetails = useContext(IntegrationDetailsContext);

  const onRemoveIntegration = () => {
    const platforms = form.values.platforms;
    form.setFieldValue(
      'platforms',
      platforms.filter((i) => i.platform !== integration)
    );

    // For Bulk
    if (form.values.isBulk) {
      const sources = (form.values as unknown as BlogBulkCreate)?.sources || [];
      sources.forEach((source, i) => {
        form.setFieldValue(
          `sources.${i}.platforms`,
          source.platforms.filter((j) => j.platform !== integration)
        );
      });
    }
  };

  return (
    <div className="flex items-center justify-between py-4">
      <div className="flex items-center gap-3">
        <div
          style={{ backgroundColor: integrationDetails?.[integration].theme }}
          className="flex size-10 rounded-lg flex-center"
        >
          <img
            className="size-6"
            alt={integrationDetails?.[integration]?.name + ' Logo'}
            src={integrationDetails?.[integration]?.logoURL}
          />
        </div>

        <div className="leading-5">
          <div className="text-md font-semibold capitalize">
            {integrationDetails?.[integration].name}
          </div>
          <a
            className="!-mt-2 text-sm text-gray9 hover:underline"
            href={integrationDetails?.[integration].website}
          >
            {integrationDetails?.[integration].website}
          </a>
        </div>
      </div>

      <button
        className="-mr-1.5 flex size-9 flex-center"
        onClick={onRemoveIntegration}
        type="button"
      >
        <IoCloseCircle className="rounded-full text-red4 ring-4 ring-gray16" size={16} />
      </button>
    </div>
  );
};

const PublishSiteSelector: React.FC<CommonProps> = ({ integration }) => {
  const { sites } = use(IntegrationDetailsContext)[integration];
  const form = useFormikContext<BlogCreate>();
  if (!sites?.length) return null;

  const selectedSites = form.values.platforms?.find((p) => p.platform === integration)?.sites || [];

  const setIntegrationSite: React.Dispatch<React.SetStateAction<SelectedSite[]>> = (value) => {
    const newSites = typeof value === 'function' ? value(selectedSites) : value;
    const platforms = form.values.platforms.map((p) =>
      p.platform === integration ? { ...p, sites: newSites } : p
    );
    form.setFieldValue('platforms', platforms);
  };

  return (
    <SiteSelector
      sites={sites}
      selectedSites={selectedSites}
      setIntegrationSite={setIntegrationSite}
    />
  );
};

const PublishDateTimeSelector: React.FC<CommonProps> = ({ integration }) => {
  const form = useFormikContext<BlogCreate>();
  const timeZone = new Date()
    .toLocaleTimeString(undefined, { timeZoneName: 'short' })
    .split(' ')[2];

  const onPublishAtChange = (publishAt: string) => {
    const platforms = form.values.platforms;
    const index = platforms.findIndex((i) => i.platform === integration);
    if (index >= 0) {
      platforms[index].timestamp = dayjs(publishAt).toISOString();
      form.setFieldValue('platforms', platforms);
    }
  };

  return (
    <div className="mt-10">
      <span className="space-x-1">
        <span className="mb-2 text-md font-medium">Date & Time</span>
        {timeZone && <span className="text-xs text-gray9">({timeZone})</span>}
      </span>
      <input
        className="h-10 w-full rounded-lg border border-solid border-gray10 px-4"
        defaultValue={dayjs().add(10, 'minutes').format('YYYY-MM-DDTHH:mm')}
        onChange={(ev) => onPublishAtChange(ev.target.value)}
        type="datetime-local"
      />
    </div>
  );
};

export default BlogCreatePublishSettings;
export { IntegrationTrigger, PublishSettings };
