import type { BlogContentSetting } from '../utils/useBlogCreate';
import type { BlogCreate } from '@ps/types';

import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useFormikContext } from 'formik';
import { PiDotsSixBold } from 'react-icons/pi';

import { Checkbox } from '@ps/ui/components/checkbox';
import { cn } from '@ps/ui/lib/utils';

import BlogBulkCreateContentImages from '../bulk/components/BlogBulkCreateContentImages';
import BlogCreateTableOptsSelector from './BlogCreateTableOptsSelector';
import BlogCreateChartOptsSelector from './BlogCreateChartOptsSelector';
import BlogBulkCreateCoverImage from '../bulk/components/BlogBulkCreateCoverImage';
import BlogCreateContentImage from './BlogCreateContentImage';
import BlogCreateCover from './BlogCreateCover';
import Cta from './CTA';

export default function BlogContentSettings({
  contentSettings,
  setContentSettings,
}: {
  contentSettings: BlogContentSetting[];
  setContentSettings: (_: BlogContentSetting[]) => void;
}) {
  const { values } = useFormikContext<BlogCreate>();

  const contentSettingsByLabel = contentSettings.reduce(
    (obj, s) => {
      obj[s.label] = s;
      return obj;
    },
    {} as Record<string, (typeof contentSettings)[0]>
  );

  return (
    <div className="mt-20">
      <h3 className="mb-6 text-17 font-semibold">Blog Contents</h3>

      <h4 className="mb-2 text-15 font-medium">Select Template</h4>

      <div className="rounded-lg border border-gray10">
        <header className="flex items-center justify-between px-4 py-3">
          <div className="flex w-1/2 flex-col items-center">
            <h5 className="text-15 font-medium">Default</h5>
            <p className="text-13 text-gray9">Basics blog options</p>
          </div>

          <div className="h-4 w-px bg-gray10" />

          <div className="flex w-1/2 flex-col items-center">
            <h5 className="text-15 font-medium text-gray9">My Template</h5>
            <p className="text-13 text-gray9">(Coming Soon)</p>
          </div>
        </header>

        <div className="flex border-t border-gray10">
          <div className="w-full">
            <BlogContentSelector
              contentSettings={contentSettings}
              setContentSettings={setContentSettings}
            />
          </div>
        </div>

        {/* <div className="p-2 flex gap-2">
          <Button className="h-6 !text-11 text-gray9 px-2 rounded-md" variant="secondary">
            RESET TEMPLATE
          </Button>
          <Button className="h-6 !text-11 text-gray9 px-2 rounded-md" variant="secondary">
            SAVE AS MY TEMPLATE
          </Button>
        </div> */}
      </div>

      <div className="mt-10 flex gap-4">
        {contentSettingsByLabel['Cover Image'].checked && (
          <div className="w-1/2">
            {!values.isBulk ? <BlogCreateCover /> : <BlogBulkCreateCoverImage />}
          </div>
        )}
        {contentSettingsByLabel.Content.children?.[3].checked && (
          <div className="w-1/2">
            {!values.isBulk ? <BlogCreateContentImage /> : <BlogBulkCreateContentImages />}
          </div>
        )}
      </div>

      {contentSettingsByLabel.Content.children?.[4].checked && (
        <div className="mt-4 pt-0.5 ">
          <Cta />
        </div>
      )}

      <div className="mt-4 grid gap-4 pt-0.5 sm:grid-cols-2">
        {contentSettingsByLabel.Content.children?.[0].checked && <BlogCreateTableOptsSelector />}
        {contentSettingsByLabel.Content.children?.[1].checked && <BlogCreateChartOptsSelector />}
      </div>
    </div>
  );
}

const reorder = (list: Array<any>, startIndex: number, endIndex: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

const BlogContentSelector = ({
  contentSettings,
  setContentSettings,
}: {
  contentSettings: BlogContentSetting[];
  setContentSettings: (_: BlogContentSetting[]) => void;
}) => {
  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // Prevent moving the first two items
    if (source.index < 2 || destination.index < 2) return;

    // Allow items from index 2 onward to move freely
    const items = reorder(contentSettings, source.index, destination.index);
    setContentSettings(items);
  };

  const select = (checked: boolean, index: number, childIndex?: number) => {
    const items = Array.from(contentSettings);
    if (typeof childIndex !== 'undefined') {
      if (items[index].children) {
        items[index].children[childIndex].checked = checked;
      }
    } else {
      items[index].checked = checked;
    }
    setContentSettings(items);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="list">
        {(provided) => (
          <ul {...provided.droppableProps} ref={provided.innerRef}>
            {contentSettings.map((setting, i) => (
              <Draggable
                key={i}
                index={i}
                draggableId={String(i)}
                isDragDisabled={!setting.draggable}
              >
                {(draggableProvided, snapshot) => (
                  <li
                    className={cn('border-gray10 px-2.5 py-1.5', {
                      'border-t': i !== 0,
                      'bg-white border': snapshot.isDragging,
                    })}
                    {...draggableProvided.dragHandleProps}
                    {...draggableProvided.draggableProps}
                    ref={draggableProvided.innerRef}
                  >
                    <div className="flex items-center justify-between">
                      <label className="flex items-center gap-2.5 text-15 font-medium">
                        <Checkbox
                          disabled={setting.disabled}
                          defaultChecked={setting.checked}
                          onCheckedChange={(v) => select(v as boolean, i)}
                        />
                        {setting.label}
                      </label>

                      {setting.draggable && <PiDotsSixBold className="cursor-grab text-gray12" />}
                    </div>

                    {!!setting.children?.length && (
                      <ul className="ml-[26px] flex flex-col gap-2.5 py-2.5">
                        {setting.children.map((set, j) => (
                          <li key={j}>
                            <label className="flex items-center gap-2.5 text-13 font-medium">
                              <Checkbox
                                disabled={set.disabled}
                                defaultChecked={set.checked}
                                onCheckedChange={(v) => select(v as boolean, i, j)}
                              />
                              {set.label}
                            </label>
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                )}
              </Draggable>
            ))}

            {provided.placeholder}
          </ul>
        )}
      </Droppable>
    </DragDropContext>
  );
};
