import type { ReactSelectOptionType } from '@/components/form/SelectOption';
import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import { Fragment, useState } from 'react';
import { BiSolidLeftArrow, BiSolidRightArrow } from 'react-icons/bi';

import SelectOption from '@/components/form/SelectOption';
import TagInput from '@/components/form/TagInput';
import { BLOG_TONES } from '@/constants/blog';
import { useStoreState } from '@/store';
import { Checkbox } from '@ps/ui/components/checkbox';
import { RadioGroup, RadioGroupItem } from '@ps/ui/components/radio-group';
import { Slider } from '@ps/ui/components/slider';
import { cn } from '@ps/ui/lib/utils';

import { BLOG_LANGUAGES } from '../utils/languages';

const blogToneOptions: ReactSelectOptionType[] = BLOG_TONES.map((tone) => ({
  value: tone,
  label: tone,
}));

const blogLanguageOptions: ReactSelectOptionType[] = BLOG_LANGUAGES.map((language) => ({
  value: language.value,
  label: `${language.name} - ${language.translation}`,
}));

const BlogCreateMiscSettings = () => {
  const { preferredInputLanguage, preferredOutputLanguage } = useStoreState((s) => s.user.current);
  const form = useFormikContext<BlogCreate>();

  return (
    <div>
      <BlogCreateToneInput />
      <BlogCreateSizeInput />
      <BlogCreateSEOOptimization />

      <SelectOption
        className="mt-5"
        label="Blog Language"
        options={blogLanguageOptions}
        {...form.getFieldProps('blogLanguage')}
        defaultValue={
          preferredInputLanguage &&
          blogLanguageOptions.find((lang) => lang.value === preferredOutputLanguage)
        }
      />
    </div>
  );
};

const BlogCreateToneInput = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <div className="my-9">
      <label className="text-15 font-medium">Blog Tone</label>
      <div className="mt-2 flex flex-wrap gap-2">
        {blogToneOptions.map((tone, i) => (
          <button
            className={cn(
              'flex h-10 items-center gap-2 rounded-lg border border-gray10 px-4 text-15 font-medium',
              {
                'shadow-[0_0_0_2px_rgb(253,227,219,1)] border-primary':
                  form.values.blogTone === tone.value,
              }
            )}
            onClick={() => form.setFieldValue('blogTone', tone.value)}
            type="button"
            key={i}
          >
            <img className="size-6" src={`/images/icons/tones/${tone.value.toLowerCase()}.png`} />
            {tone.label}
          </button>
        ))}
      </div>
    </div>
  );
};

const BlogCreateSizeInput = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <div className="mb-10">
      <label className="text-15 font-medium">Blog Size - {form.values.wordCountApprox} Words</label>
      <div className="mt-3">
        <div className="flex justify-between">
          {[500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500].map((size, i) => (
            <Fragment key={i}>
              <div key={size} className="flex w-4 flex-col items-center">
                <span className="text-11 font-medium text-gray9 ">{size}</span>
                <div className="h-[3px] w-px bg-gray9" />
              </div>
            </Fragment>
          ))}
        </div>

        <div className="px-2">
          <Slider
            className="my-1 "
            onValueChange={(v) => form.setFieldValue('wordCountApprox', v[0])}
            defaultValue={[form.values.wordCountApprox]}
            variant="blogSize"
            max={4500}
            min={500}
          />
        </div>

        <div className="flex justify-between px-2">
          {['MINI', 'SMALL', 'MEDIUM', 'LARGE', 'X-LARGE'].map((size, i) => (
            <Fragment key={i}>
              <div
                key={size}
                className={cn('relative flex items-end gap-1.5')}
                style={
                  size === 'SMALL'
                    ? { right: '3rem' }
                    : size === 'MEDIUM'
                      ? { right: '5.7rem' }
                      : size === 'LARGE'
                        ? { right: '5.7rem' }
                        : {}
                }
              >
                <div className="absolute -bottom-1 right-1.5 whitespace-nowrap text-11 font-medium text-gray9">
                  {size}
                </div>
                <div className="h-4 w-px bg-gray9" />
              </div>
            </Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

const BlogCreateSEOOptimization = () => {
  const [keywordType, setKeywordType] = useState<'ai' | 'custom'>('ai');
  const form = useFormikContext<BlogCreate>();

  const { isBulk, sourceName } = form.values;
  const isPromptSource = sourceName === 'Prompt';

  return (
    <div className={cn({ 'pb-10': !isBulk })}>
      {!isBulk && <h4 className="mb-2 text-15 font-medium">Blog Optimization</h4>}
      <div className="rounded-lg border border-gray10 p-4">
        <div className="flex flex-col gap-8">
          {!isPromptSource && (
            <div>
              <h5 className="mb-4 text-15 font-medium">Similar to Source</h5>
              <div className="mb-2">
                <label className="text-15 font-medium">
                  Source Similarity - {form.values.sourceSimilarity}%
                </label>
                <div className="mt-3">
                  <div className="flex justify-between">
                    {[10, 20, 30, 40, 50, 60, 70, 80, 90].map((size, i) => (
                      <Fragment key={i}>
                        <div key={size} className="flex flex-col items-center">
                          <span className="text-11 font-medium text-gray9">{size}%</span>
                          <div className="h-[3px] w-px bg-gray9" />
                        </div>
                      </Fragment>
                    ))}
                  </div>

                  <div className="px-1">
                    <Slider
                      className="my-1"
                      onValueChange={(v) => form.setFieldValue('sourceSimilarity', v[0])}
                      defaultValue={[form.values.sourceSimilarity]}
                      variant="blogSize"
                      max={90}
                      min={10}
                    />
                  </div>

                  <div className="mt-1.5 flex justify-between text-11 font-medium text-gray9">
                    <div className="flex items-center gap-1.5">
                      <BiSolidLeftArrow />
                      <div className="mt-0.5">LESS LIKELY</div>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <div className="mt-0.5">MORE LIKELY</div>
                      <BiSolidRightArrow />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div>
            <h5 className="mb-4 text-15 font-medium">SEO Optimized</h5>
            <div className="mb-4 flex items-center gap-2">
              <label className="flex items-center gap-2 text-15 font-medium">
                <Checkbox
                  checked={form.values.seoOption === 'ACTIVE'}
                  onCheckedChange={(checked) => {
                    form.setFieldValue('seoOption', checked ? 'ACTIVE' : 'PASSIVE');
                  }}
                />
                Enable SEO Optimization
              </label>
            </div>

            {form.values.seoOption === 'ACTIVE' && (
              <div>
                <p className="my-4 text-15">
                  Your blog will be SEO optimized but it may be less similar to your blog source.
                  Blogify will automatically identify and use SEO keywords to optimize your blog or
                  you can add your own.
                </p>

                <RadioGroup
                  className="mb-2 flex gap-10"
                  onValueChange={(v) => {
                    setKeywordType(v as 'ai' | 'custom');
                    if (v === 'ai') {
                      form.setFieldValue('seoInputKeywords', []);
                    }
                  }}
                  defaultValue={keywordType}
                >
                  <label className="flex items-center gap-2 text-15 font-medium">
                    <RadioGroupItem value="ai" />
                    Use AI Generated SEO Keywords
                  </label>
                  <label className="flex items-center gap-2 text-15 font-medium">
                    <RadioGroupItem value="custom" />
                    Add SEO Keywords
                  </label>
                </RadioGroup>

                {keywordType === 'custom' && (
                  <TagInput
                    setTags={(tags) => form.setFieldValue('seoInputKeywords', tags)}
                    setError={(message) => {
                      if (message) form.getFieldHelpers('seoInputKeywords').setError(message);
                    }}
                    tags={form.getFieldProps('seoInputKeywords').value}
                    placeholder="eg. watch, luxury watch, rolex submariner..."
                    name="seoInputKeywords"
                    type="tags"
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCreateMiscSettings;
