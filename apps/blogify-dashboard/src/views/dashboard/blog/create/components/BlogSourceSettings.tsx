import type { ReactSelectOptionType } from '@/components/form/SelectOption';
import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import { useStoreState } from '@/store';
import { Checkbox } from '@ps/ui/components/checkbox';
import SelectOption from '@/components/form/SelectOption';
import FormField from '@ps/ui/form/FormField';
import TagInput from '@/components/form/TagInput';

import { LANGUAGE_CODES } from '../utils/languages';
import BlogCreatePromptInput from './BlogCreatePromptInput';
import BlogCreateFileUpload from './BlogCreateFileUpload';
import BlogCreateUrlInput from './BlogCreateUrlInput';
import EmbedSourceInput from './BlogEmbedSourceInput';
import useBlogCreate from '../utils/useBlogCreate';

const inputLanguageOptions: ReactSelectOptionType[] = LANGUAGE_CODES.map((lang) => ({
  value: lang.toLowerCase(),
  label: lang,
}));

const BlogSourceSettings = () => {
  const { preferredInputLanguage } = useStoreState((s) => s.user.current);
  const { source } = useBlogCreate();
  const form = useFormikContext<BlogCreate>();

  return (
    <div>
      {source.inputType === 'url' && <BlogCreateUrlInput />}
      {source.inputType === 'upload' && <BlogCreateFileUpload />}
      {source.inputType === 'textarea' && <BlogCreatePromptInput />}

      <div className="mb-2 mt-6 flex flex-wrap">
        {['video', 'audio'].includes(source.type) && (
          <div className="mb-5 w-full md:w-1/2 md:pr-2">
            <SelectOption
              className="mt-5"
              label="Source Language"
              options={inputLanguageOptions}
              {...form.getFieldProps('inputLanguage')}
              defaultValue={
                preferredInputLanguage &&
                inputLanguageOptions.find(
                  (lang) => lang.value === preferredInputLanguage.toLowerCase()
                )
              }
            />
            {/* {['video', 'audio'].includes(source.type) && (
      <p className="font-small mt-2 flex items-center gap-2 text-xs">
        <div>
          <MdWarning className="text-gold" size={20} />
        </div>
        Select the correct source language based on the {source.type}
      </p>
    )} */}
          </div>
        )}

        {source.isEmbeddable && (
          <>
            <div className="w-full md:w-1/2 md:pl-2">
              <label className="text-md font-medium">Embed Source</label>
              <EmbedSourceInput />
            </div>

            <div className="mb-6 w-full md:w-1/2">
              <label className="text-md font-medium">
                Embed as Cover <span className="text-xs text-gray9">(Blogify Sites Only)</span>
              </label>

              <div className="mt-1.5 flex h-[38px] justify-between rounded-lg border border-gray10 px-3">
                <label className="relative flex w-full items-center !text-15 font-medium">
                  <Checkbox
                    onCheckedChange={(checked) => form.setFieldValue('embedSourceAsCover', checked)}
                  />
                  <div className="ml-3 w-full text-md">Embed this content as your blog cover</div>
                </label>
              </div>
            </div>
          </>
        )}
      </div>

      <FormField
        containerClass="min-h-8"
        className="w-full"
        label="Custom Instructions (Optional)"
        placeholder="eg. Use simple & easy to understand sentences"
        type="text"
        toggleAble
        {...form.getFieldProps('customInstruction')}
      />

      <FormField
        containerClass="min-h-8"
        className="w-full"
        label="Give Credit to Content Creator"
        type="custom"
        toggleAble
        {...form.getFieldProps('authorsToGiveCredit')}
      >
        <TagInput
          setTags={(tags) => form.setFieldValue('authorsToGiveCredit', tags)}
          setError={(message) => {
            if (message) form.getFieldHelpers('authorsToGiveCredit').setError(message);
          }}
          tags={form.getFieldProps('authorsToGiveCredit').value}
          placeholder="eg. MKBHD, Everyday Astronaud, Vsauce"
          name="authorsToGiveCredit"
          type="tags"
        />
      </FormField>
    </div>
  );
};

export default BlogSourceSettings;
