import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import SelectOption from '@/components/form/SelectOption';

const tableOptions = [
  { label: 'Create a single table', value: 'One Table' },
  { label: 'Create multiple tables', value: 'Two Table' },
  { label: 'Create tables for each section', value: 'Table for Each Section' },
  { label: 'No Table', value: 'No Table' },
];

const BlogCreateTableOptsSelector: React.FC = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <SelectOption
      className="mt-5"
      label="Generate Data Table Settings"
      options={tableOptions}
      {...form.getFieldProps('tableOption')}
    />
  );
};

export default BlogCreateTableOptsSelector;
