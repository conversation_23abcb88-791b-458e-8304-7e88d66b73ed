import type { BlogCreate } from '@ps/types';

import { GoInfo } from 'react-icons/go';

import {
  TooltipProvider,
  TooltipContent,
  TooltipTrigger,
  TooltipArrow,
  Tooltip,
} from '@ps/ui/components/tooltip';
import { useFormikContext } from 'formik';
import { Checkbox } from '@ps/ui/components/checkbox';

const EmbedSourceInput = ({ className }: { className?: string }) => {
  const form = useFormikContext<BlogCreate>();

  return (
    <div
      className={`mt-1.5 flex h-[38px] justify-between rounded-lg border border-gray10 px-3 ${className}`}
    >
      <label className="relative flex w-full items-center !text-15 font-medium">
        <Checkbox onCheckedChange={(checked) => form.setFieldValue('embedSource', checked)} />
        <div className="ml-3 w-full text-md">Embed this content in your blog</div>
      </label>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button type="button">
              <GoInfo className="hidden text-gray13" size={16} />
            </button>
          </TooltipTrigger>
          <TooltipContent className="max-w-80 text-center">
            This video will be embedded into your blog when you publish to external sites e.g.
            Blogger or WordPress or Medium
            <TooltipArrow />
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default EmbedSourceInput;
