import type { UrlMeta } from '../utils/UrlService';

import { useRef } from 'react';

import Link from '@/components/common/Link';

import useBlogCreate from '../utils/useBlogCreate';

const BlogCreateUrlPreview: React.FC<UrlMeta> = ({ title, description, image, duration }) => {
  const imageRef = useRef<HTMLImageElement>(null);

  const {
    source: { type },
  } = useBlogCreate();

  return (
    <div className="flex min-h-[154px] flex-col px-3 pb-2 pt-4">
      <div className="flex gap-4">
        <img
          className="max-h-[96px] w-[130px] rounded-md object-cover drop-shadow xs:min-w-[166px]"
          src={image || '/images/temp-bg.jpg'}
          onError={() => {
            if (imageRef.current) imageRef.current.src = '/images/temp-bg.jpg';
          }}
          ref={imageRef}
        />
        <div className="flex flex-col overflow-hidden">
          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <h1 className="ellipsis r1 text-md font-semibold lg:text-base">
            {title || `Sorry, couldn't fetch title. 🥺`}
          </h1>

          {duration && <div className="mt-1 text-sm font-medium lg:text-sm">{duration} Min</div>}

          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="ellipsis r2 mt-2 text-sm">{description}</div>
        </div>
      </div>

      <div className="mt-5 flex flex-col justify-between xs:flex-row">
        <p className="text-xs">
          Not the {type} you wanted? Review your {type} link or report an issue.{' '}
        </p>
        <Link className="text-xs text-gray13 hover:underline" to="mailto:<EMAIL>">
          Report issues
        </Link>
      </div>
    </div>
  );
};

export default BlogCreateUrlPreview;
