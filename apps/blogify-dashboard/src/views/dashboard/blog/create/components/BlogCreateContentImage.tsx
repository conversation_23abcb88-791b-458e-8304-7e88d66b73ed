import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import { ImageDialogProvider } from '@/views/dashboard/image/context/ImageDialogProvider';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import ImageInsertDialog from '@/views/dashboard/image/ImageInsertDialog';

import BlogCreateImageGenerateDialog from '../bulk/components/BlogCreateImageGenerateDialog';

const BlogCreateContentImage: React.FC = () => {
  const form = useFormikContext<BlogCreate>();
  const numberOfImages = form.values.aiGeneratedContentImagesConfig?.count || 3;
  const costPerImage = form.values.aiGeneratedContentImagesConfig?.cost || 0;

  return (
    <ImageDialogProvider
      onImagesSelect={(urls) => form.setFieldValue('contentImages', urls)}
      multiple
      max={6}
    >
      {({ toggleDialog }) => (
        <>
          <div>
            <label className="mb-2 text-15 font-medium">Content Images</label>
            <div
              className={cn(
                'flex w-full flex-col gap-4 overflow-hidden rounded-lg border border-dashed border-gray-200 bg-cover bg-center pb-4 flex-center',
                form.values.contentImages.length
                  ? 'h-48 bg-none'
                  : "h-32 bg-[url('/images/bg-add-cover.svg')]"
              )}
            >
              <div
                className={cn('grid grid-cols-3 gap-2 bg-gray-100', {
                  'h-48': form.values.contentImages.length,
                })}
              >
                {form.values.contentImages.map((img, i) => (
                  <img key={i} src={img} className="aspect-video w-full rounded-md object-cover" />
                ))}
              </div>
              <div className=" flex flex-wrap justify-center gap-3 sm:gap-3">
                <Button
                  className="!h-6 rounded-md px-2 !text-11 text-gray9"
                  onClick={() => toggleDialog(true)}
                  variant="secondary"
                >
                  {!!form.values.contentImages.length ? 'CHANGE' : 'SELECT'} IMAGE
                </Button>

                <BlogCreateImageGenerateDialog
                  onApply={(v) => form.setFieldValue('aiGeneratedContentImagesConfig', v)}
                  defaultCount={numberOfImages}
                  multiple
                >
                  <Button className="!h-6 rounded-md px-2 !text-11 text-gray9" variant="secondary">
                    GENERATE IMAGE
                  </Button>
                </BlogCreateImageGenerateDialog>

                {form.values.contentImages && form.values.contentImages.length > 0 && (
                  <Button
                    className="!h-6 rounded-md px-2 !text-11 text-red5"
                    onClick={() => toggleDialog(true)}
                    variant="secondary"
                  >
                    REMOVE IMAGE
                  </Button>
                )}
              </div>

              {!!(numberOfImages && costPerImage) && (
                <p className="text-11 font-medium text-gray9">
                  ({numberOfImages}x{costPerImage}) = {numberOfImages * costPerImage} Credits
                </p>
              )}
            </div>
          </div>
          <ImageInsertDialog
            title="Content Images"
            description="Add up to 6 content images for your blog. Blogify will distribute them automatically in your blog. You can later move them as you wish."
          />
        </>
      )}
    </ImageDialogProvider>
  );
};

export default BlogCreateContentImage;
