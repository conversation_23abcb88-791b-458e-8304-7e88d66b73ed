import type { Integration } from '@/types/misc/integration.type';

import { AiFillWarning } from 'react-icons/ai';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@ps/ui/components/button';
import SocialIcon from '@/components/misc/SocialIcon';
import Dialog from '@ps/ui/components/dialog';

const IntegrationExpiredDialog = ({
  socialError,
  isSocialErrorDialogOpen,
  toggleSocialErrorDialogOpen,
}: {
  socialError: Record<string, boolean>;
  isSocialErrorDialogOpen: boolean;
  toggleSocialErrorDialogOpen: React.Dispatch<void>;
}) => (
  <Dialog
    open={isSocialErrorDialogOpen}
    onOpenChange={() => toggleSocialErrorDialogOpen()}
    Icon={AiFillWarning}
    title="Blog or Social Connection Expired"
    description="The following blog or social connections has expired:"
    actions={
      <Link to="/dashboard/settings">
        <Button className="mt-5 w-full">Go To Settings</Button>
      </Link>
    }
  >
    <div className="my-5 flex flex-center">
      {(Object.keys(socialError) as Integration[])
        .filter((k) => !socialError[k])
        .map((platform, i) => (
          <SocialIcon key={i} className="mr-3" integration={platform} />
        ))}
    </div>

    <div className="text-sm">
      Please go to settings and connect them again to post on your selected blog or social platform.
    </div>
  </Dialog>
);

export default IntegrationExpiredDialog;
