import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import React from 'react';

import { slugify } from '@/utils/string';
import BlogCreateRadioBox from './BlogCreateRadioBox';

const perspectiveOptions = [
  { title: 'First Person', description: '(I, <PERSON>, We, Our)' },
  { title: 'Second Person', description: '(You, Your, Yourself)' },
  { title: 'Third Person', description: '(<PERSON>, She, It, They)' },
];

const BlogCreatePovSelector: React.FC = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <div className="grid gap-4 sm:grid-cols-3">
      {perspectiveOptions.map((option, i) => (
        <BlogCreateRadioBox
          key={i}
          title={option.title}
          description={option.description}
          {...form.getFieldProps('pov')}
          value={option.title}
          icon={<img className="size-10 " src={`/images/icons/${slugify(option.title)}.svg`} />}
          variant="sm"
        />
      ))}
    </div>
  );
};

export default BlogCreatePovSelector;
