import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import { ImageDialogProvider } from '@/views/dashboard/image/context/ImageDialogProvider';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import ImageInsertDialog from '@/views/dashboard/image/ImageInsertDialog';

import useBlogCreate from '../utils/useBlogCreate';
import BlogCreateImageGenerateDialog from '../bulk/components/BlogCreateImageGenerateDialog';

const BlogCreateCover: React.FC = () => {
  const { detectedThumbnail } = useBlogCreate();
  const form = useFormikContext<BlogCreate>();
  const costPerImage = form.values.aiGeneratedCoverImageConfig?.cost || 0;

  return (
    <ImageDialogProvider
      onImageSelect={(url, info) => {
        form.setFieldValue('image', url);
        form.setFieldValue('coverImageType', info.selectedFrom);
        form.setFieldValue('aiGeneratedCoverImageConfig', null);
      }}
    >
      {({ toggleDialog }) => (
        <>
          <div>
            <label className="mb-2 text-15 font-medium">Cover Image</label>

            <div
              className={cn(
                'flex w-full flex-col gap-4 overflow-hidden rounded-lg border border-dashed border-gray-200 bg-cover bg-center pb-4 flex-center',
                form.values.image ? 'h-48 bg-none' : "h-32 bg-[url('/images/bg-add-cover.svg')]"
              )}
            >
              <div
                className={cn('flex w-full items-center justify-center bg-gray-100', {
                  'h-48': form.values.image,
                })}
              >
                {form.values.image && (
                  <img className="h-32 object-cover" src={form.values.image} alt="Cover" />
                )}
              </div>

              <div className="flex flex-wrap justify-center gap-3 sm:gap-3">
                <Button
                  className="!h-6 rounded-md px-2 !text-11 text-gray9"
                  onClick={() => toggleDialog(true)}
                  variant="secondary"
                >
                  {form.values.image ? 'CHANGE' : 'SELECT'} IMAGE
                </Button>

                {!form.values.image && (
                  <BlogCreateImageGenerateDialog
                    onApply={(v) => {
                      form.setFieldValue('aiGeneratedCoverImageConfig', v);
                      form.setFieldValue('coverImageType', 'ai-generated');
                    }}
                  >
                    <Button
                      className="!h-6 rounded-md px-2 !text-11 text-gray9"
                      variant="secondary"
                    >
                      GENERATE IMAGE
                    </Button>
                  </BlogCreateImageGenerateDialog>
                )}

                {form.values.image && (
                  <Button
                    className="!h-6 rounded-md px-2 !text-11 text-red5"
                    onClick={() => form.setFieldValue('image', '')}
                    variant="secondary"
                  >
                    REMOVE IMAGE
                  </Button>
                )}
              </div>

              {!!costPerImage && (
                <p className="text-center text-11 font-medium text-gray9">
                  (1x{costPerImage}) = {costPerImage} Credits
                </p>
              )}
            </div>
          </div>

          <ImageInsertDialog title="Cover Image" thumbnail={detectedThumbnail} />
        </>
      )}
    </ImageDialogProvider>
  );
};

export default BlogCreateCover;
