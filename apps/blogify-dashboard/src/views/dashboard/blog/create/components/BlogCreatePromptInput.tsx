import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';
import React from 'react';

import ErrorMessage from '@/components/form/ErrorMessage';

const MAX_LENGTH = 50000;

const BlogCreatePromptInput: React.FC = () => {
  const form = useFormikContext<BlogCreate>();

  return (
    <>
      <h3 className="mb-2 text-15 font-medium">Prompt</h3>
      <div className="relative">
        <textarea
          className="w-full rounded-xl border border-gray10 p-4 text-sm outline-none placeholder:text-gray9 focus:border-primary focus:shadow-cards-primary"
          placeholder="Write your thoughts here..."
          maxLength={MAX_LENGTH}
          rows={5}
          {...form.getFieldProps('prompt')}
        ></textarea>

        <span className="absolute bottom-3 right-3 text-xs text-gray9">
          {form.values.prompt?.length} / {MAX_LENGTH}
        </span>
      </div>
      {form.errors.prompt && <ErrorMessage>{form.errors.prompt}</ErrorMessage>}
    </>
  );
};

export default BlogCreatePromptInput;
