import type { FormikProps } from 'formik';
import type { BlogCreate } from '@ps/types';

import { useState, useEffect } from 'react';
import { IoMdRefresh } from 'react-icons/io';
import { MdClose } from 'react-icons/md';

import { cacheRemove, cacheGet, cacheSet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';

const BlogCreateDataRestored: React.FC<{
  form: FormikProps<BlogCreate>;
  defaultBlog: BlogCreate;
}> = ({ defaultBlog, form }) => {
  const localBlog = cacheGet('new-blog', defaultBlog);
  const hasOldData = !!(localBlog.url || localBlog.prompt);
  const [dataRestored, setDataRestored] = useState(hasOldData);

  useEffect(() => {
    if (form.values) {
      try {
        cacheSet('new-blog', JSON.stringify(form.values));
      } catch {}
    }
  }, [form.values]);

  return dataRestored ? (
    <div className="mb-6 flex items-center justify-between rounded-lg bg-[rgba(242,71,13,0.05)] p-4">
      <div className="flex items-center gap-2 text-sm">
        <IoMdRefresh className="size-5 -scale-x-100 text-primary" />
        We've restored your previously inserted data.
      </div>
      <div className="flex gap-3 flex-center">
        <Button
          className="w-max"
          variant="secondary"
          onClick={() => {
            form.resetForm();
            form.setValues(defaultBlog);
            setDataRestored(false);
            cacheRemove('new-blog');
          }}
        >
          Clear & Start Fresh
        </Button>
        <Button
          type="button"
          variant="secondary"
          className="!px-2"
          onClick={() => setDataRestored(false)}
        >
          <MdClose size={20} />
        </Button>
      </div>
    </div>
  ) : (
    <></>
  );
};

export default BlogCreateDataRestored;
