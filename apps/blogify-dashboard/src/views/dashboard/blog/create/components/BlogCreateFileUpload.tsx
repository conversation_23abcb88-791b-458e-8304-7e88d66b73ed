import type { BlogCreate } from '@ps/types';

import { useFormikContext } from 'formik';

import {
  FileUploadProgress,
  FileUploadComplete,
  FileUpload,
} from '@/modules/file-upload/FileUpload';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';

import useBlogCreate from '../utils/useBlogCreate';

const BlogCreateFileUpload: React.FC = () => {
  const { setFileUploading, setDetectedThumbnail, maxFileSize, source } = useBlogCreate();
  const form = useFormikContext<BlogCreate>();

  const error =
    source.type !== 'document' && form.errors.url
      ? form.errors.url
      : source.type === 'document' && form.errors.prompt
        ? form.errors.prompt
        : '';

  return (
    <FileUploadProvider
      type={source.type as 'image' | 'audio' | 'video' | 'document'}
      folder={`blog/sources/${source.type}s`}
      maxSize={maxFileSize}
      onUploadStart={() => setFileUploading(true)}
      onUploadComplete={(files) => {
        const { url } = files[0];
        form.setFieldValue('url', url);
        setTimeout(() => {
          if (source.type === 'image') {
            setDetectedThumbnail(url);
            form.setFieldValue('image', url);
          }
          setFileUploading(false);
        }, 1000);
      }}
    >
      {({ isUploading, files }) => (
        <div className="w-full rounded-lg border border-dashed border-gray12">
          {!files.length && (
            <FileUpload
              className="min-h-52 rounded-lg"
              supportedFormats={source.footer}
              maxFileSize={maxFileSize}
              error={error}
            />
          )}
          {isUploading && <FileUploadProgress className="min-h-52" />}
          {!!files.length && !isUploading && <FileUploadComplete className="min-h-52" />}
        </div>
      )}
    </FileUploadProvider>
  );
};

export default BlogCreateFileUpload;
