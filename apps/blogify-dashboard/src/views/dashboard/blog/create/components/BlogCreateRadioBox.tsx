import type { HTMLProps } from 'react';

import { useField } from 'formik';
import React from 'react';

import { cn } from '@ps/ui/lib/utils';

interface BlogCreateRadioBoxProps extends HTMLProps<HTMLInputElement> {
  icon?: React.ReactNode;
  title: string;
  description: string;
  variant?: 'sm' | 'lg';
}

const variantStyles = {
  lg: {
    container: '',
    title: 'text-lg',
    description: 'text-md',
  },
  sm: {
    container: 'px-5 py-3',
    title: 'text-md',
    description: 'text-sm text-gray9',
  },
};

const BlogCreateRadioBox: React.FC<BlogCreateRadioBoxProps> = ({
  className,
  icon,
  title,
  description,
  variant = 'lg',
  ...props
}) => {
  const [field, meta] = useField({ name: props.name || '', ...props });
  const isSelected = meta.value === props.value;

  return (
    <label className={`relative flex ${className}`}>
      <input type="radio" className="hidden" {...field} {...props} />

      <div
        className={`flex w-full items-start gap-4 rounded-lg border p-4 hover:border-primary hover:shadow-[0_0_0_2px_rgb(253,227,219,1)] ${isSelected ? 'border-primary shadow-[0_0_0_2px_rgb(253,227,219,1)]' : 'border-gray10'} ${variantStyles[variant].container}`}
      >
        {icon && (
          <div
            className={cn(
              'flex size-10 shrink-0 rounded-md border border-gray11 p-2 text-2xl flex-center',
              {
                'text-primary shadow-sm': isSelected,
                'text-gray12': !isSelected,
              }
            )}
          >
            {icon}
          </div>
        )}

        <div>
          <div className={`font-medium ${variantStyles[variant].title}`}>{title}</div>
          <div className={variantStyles[variant].description}>{description}</div>
        </div>
      </div>
    </label>
  );
};

export default BlogCreateRadioBox;
