import type { BlogSource, Blog } from '@ps/types';

import { useNavigate } from 'react-router-dom';
import { useMutation } from 'react-query';
import { GrMultiple } from 'react-icons/gr';
import { TfiWrite } from 'react-icons/tfi';
import { useState } from 'react';
import { BiBlock } from 'react-icons/bi';

import { Tabs<PERSON>ontent, TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { useStoreState } from '@/store';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import FreeTrialDialog from '@/components/dialog/FreeTrialDialog';
import Dialog from '@ps/ui/components/dialog';
import Link from '@/components/common/Link';

import { tabNames } from './utils/constants';
import useBlogSource from '../context/useBlogSource';

const BlogCreateSelectSource: React.FC = () => {
  const { tabName: queryTabName } = parseQuery();
  const { sourcesByType } = useBlogSource();
  const navigate = useNavigate();

  const { mutateAsync: createBlogFromScratch, isLoading } = useMutation({
    mutationFn: () =>
      API.post<Blog>(`blogs/create-from-scratch`).then((res) => {
        if (res) {
          navigate(`/dashboard/blogs/${res._id}/edit`);
        }
      }),
  });

  return (
    <DashboardContainer
      title="Create New Blog"
      cancelUrl="/dashboard/blogs"
      actions={
        <>
          <Button
            className="min-w-36"
            variant="secondary"
            loading={isLoading}
            disabled={isLoading}
            onClick={() => createBlogFromScratch()}
          >
            <TfiWrite />
            Write a Blog
          </Button>

          <Link to="/dashboard/blogs/select-source/bulk-create">
            <Button variant="secondary">
              <GrMultiple />
              Bulk Generation
            </Button>
          </Link>
        </>
      }
    >
      <div className="py-5">
        <h2 className="text-lg font-semibold">Select a Source</h2>
        <p className="mt-2">
          To start creating your blog let's first select how you want to generate a blog.
        </p>
      </div>

      <Tabs defaultValue={!queryTabName || queryTabName === 'undefined' ? 'video' : queryTabName}>
        <TabsList className="grid grid-cols-2 sm:grid-cols-4 xl:inline-flex">
          {Object.entries(tabNames).map(([name, { Icon }]) => (
            <TabsTrigger
              onClick={() => navigate({ search: `tabName=${name}` })}
              value={name}
              key={name}
            >
              <div className="flex gap-2 flex-center">
                <Icon size={15} />
                <span className="font-semibold capitalize">{name}</span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>

        {Object.entries(sourcesByType).map(([tabName, particularSources]) => (
          <TabsContent className="pb-2 pt-10" value={tabName} key={tabName}>
            <div className="grid gap-4 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {particularSources.map((source) => (
                <BlogSourceCard key={source.name} source={source} />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </DashboardContainer>
  );
};

const BlogSourceCard: React.FC<{ source: BlogSource }> = ({ source }) => {
  const [showInactiveDialog, setShowInactiveDialog] = useState(false);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  const user = useStoreState((s) => s.user.current);

  const isSourceAvailable =
    !source.isInactive &&
    ((!['trial', 'cancelled'].includes(user.subscriptionStatus) &&
      !['FREE', 'NOPLAN'].includes(user.subscriptionPlan)) ||
      (user.subscriptionStatus === 'trial' && source.availableOnTrial) ||
      (user.subscriptionPlan === 'FREE' && source.availableOnFree));

  return (
    <>
      <Link
        className={cn(
          'flex flex-col justify-between rounded-lg border border-gray11 px-5 py-4 transition-all hover:border-primary-200 hover:shadow-cards-primary',
          { 'opacity-40': !isSourceAvailable }
        )}
        onClick={
          isSourceAvailable
            ? () => {}
            : source.isInactive
              ? () => setShowInactiveDialog(true)
              : () => setShowUpgradeDialog(true)
        }
        to={isSourceAvailable ? '/dashboard/blogs/select-source/create' : ''}
        state={source.name}
      >
        <div>
          <div className="flex size-10 items-center">
            <img src={source.iconUrl} alt={source.displayName} />
          </div>

          <h1 className="mt-4 text-lg font-semibold">{source.displayName}</h1>
          <p className="mt-2">{source.description}</p>
        </div>

        <p className="mt-4 text-sm">
          {source.footer.startsWith('.') ? 'Supported Files: ' : ''}
          {source.footer}
        </p>
      </Link>

      <FreeTrialDialog
        isOpen={showUpgradeDialog}
        toggleUpgrading={() => {}}
        onClose={() => setShowUpgradeDialog(false)}
      />

      <Dialog
        open={showInactiveDialog}
        onOpenChange={() => setShowInactiveDialog(false)}
        Icon={BiBlock}
        title="Not available"
        description={`${source.displayName} is currently not available. We are sorry for the inconvenience. Please try again later.`}
      />
    </>
  );
};

export default BlogCreateSelectSource;
