import type { BlogSource } from '@ps/types';

import { FaRegFilePdf, FaImage } from 'react-icons/fa';
import { IoDocumentTextOutline } from 'react-icons/io5';
import { HiOutlineSpeakerWave } from 'react-icons/hi2';
import { FaRegCirclePlay } from 'react-icons/fa6';
import { MdShoppingCart } from 'react-icons/md';
import { TfiWorld } from 'react-icons/tfi';

import config from '@/constants/config';

export const tabNames = {
  video: { type: 'video', Icon: FaRegCirclePlay },
  audio: { type: 'audio', Icon: HiOutlineSpeakerWave },
  ...(!config.isProd && { image: { type: 'image', Icon: FaImage } }),

  'e-commerce': { type: 'webLink', Icon: MdShoppingCart },
  webpage: { type: 'webLink', Icon: TfiWorld },
  document: { type: 'document', Icon: FaRegFilePdf },
  prompt: { type: 'prompt', Icon: IoDocumentTextOutline },
} as const;

export const YOUTUBE_SOURCE: BlogSource = {
  iconUrl: '/images/icons/integrations/youtube.svg',
  name: 'YouTube',
  displayName: 'YouTube',
  title: 'YouTube to Blog',
  description: 'Turn any YouTube video link into a blog!',
  footer: 'YouTube.com',
  type: 'video',
  inputType: 'url',
  status: 'active',
  isEmbeddable: true,
  availableOnTrial: true,
  availableOnFree: true,
};
