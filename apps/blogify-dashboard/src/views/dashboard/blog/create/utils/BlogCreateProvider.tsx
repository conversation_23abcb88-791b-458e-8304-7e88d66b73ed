import type { BlogIntegration } from '@/types/misc/integration.type';
import type { Blog, BlogCreate, BlogSource, SubscriptionPlanName } from '@ps/types';
import type { FormikHelpers, FormikProps } from 'formik';
import type { BlogContentSetting, BlogCreateContextType } from './useBlogCreate';

import { Formik } from 'formik';
import { useMemo, useState } from 'react';
import { useToggle } from 'react-use';
import * as Yup from 'yup';

import { useStoreActions, useStoreState } from '@/store';
import { emptyBlog as fullEmptyBlog } from '@ps/types';
import { cacheRemove, cacheGet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import BlogCreditLimitReachedDialog from '@/views/dashboard/blog/components/BlogCreditLimitReachedDialog';
import IntegrationExpiredDialog from '@/views/dashboard/blog/create/components/IntegrationExpiredDialog';
import AddBlogCredit from '@/views/dashboard/payment/subscription/AddBlogCredit';
import ErrorMessage from '@/components/form/ErrorMessage';
import Spinner from '@/components/misc/Spinner';
import config from '@/constants/config';

import { BlogCreateContext, emptyBlog } from './useBlogCreate';
import { BlogUrlInputSchema } from '../components/BlogCreateUrlInput';
import { BlogLoader } from '../../components/BlogLoader';
import BlogCreateDataRestored from '../components/BlogCreateDataRestored';

const ONE_MB = 1024 * 1024;

const getMaxFileSize = (subscriptionPlan: SubscriptionPlanName) => {
  const plan = subscriptionPlan.toLowerCase();
  return plan.includes('enterprise') || plan.includes('unlimited')
    ? 200 * ONE_MB // 200 MB
    : plan.includes('business')
      ? 150 * ONE_MB // 150 MB
      : plan.includes('premium')
        ? 100 * ONE_MB // 100 MB
        : plan.includes('basic')
          ? 50 * ONE_MB // 50 MB
          : 30 * ONE_MB; // 30 MB for Lite or Free Plan Users
};

type BlogCreateError =
  | string
  | { message: string[] }
  | { message: Record<BlogIntegration, boolean> }
  | Record<BlogIntegration, boolean>;

type Props = {
  children: React.ReactNode | ((v: BlogCreateContextType) => React.ReactNode);
  onBlogCreateSuccess?: React.Dispatch<Blog>;
  payloadOverride?: Partial<BlogCreate>;
  saveLocally?: boolean;
  source: BlogSource;
  isBulk?: boolean;
};

const defaultContentSettings: BlogContentSetting[] = [
  { label: 'Cover Image', checked: true },
  { label: 'Title', checked: true, disabled: true },
  // { label: 'Embed Source', checked: true },
  {
    label: 'Content',
    checked: true,
    disabled: true,
    draggable: true,
    children: [
      { label: 'Data Table', checked: false },
      { label: 'Data Charts', checked: false },
      { label: 'Quotation', checked: true },
      { label: 'Content Images', checked: true },
      { label: 'CTA', checked: false },
    ],
  },
  { label: 'TLDR', checked: true, draggable: true },
];

const BlogCreateProvider: React.FC<Props> = ({
  onBlogCreateSuccess,
  payloadOverride,
  saveLocally,
  children,
  source,
  isBulk,
}) => {
  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const [isSocialErrorDialogOpen, toggleSocialErrorDialogOpen] = useToggle(false);
  const [contentSettings, setContentSettings] = useState(defaultContentSettings);
  const [socialError, setSocialError] = useState<Record<string, boolean>>({});
  const [isContentImageUploading, setContentImageUploading] = useState(false);
  const [detectedThumbnail, setDetectedThumbnail] = useState('');
  const [hasReachedLimit, toggleReachedLimit] = useToggle(false);
  const [isCoverUploading, setCoverUploading] = useState(false);
  const [isFileUploading, setFileUploading] = useState(false);
  const [error, setError] = useState('');
  const [blog, setBlog] = useState<Blog>(fullEmptyBlog);

  const fetchUser = useStoreActions((s) => s.user.fetch);
  const user = useStoreState((s) => s.user.current);
  const maxFileSize = useMemo(() => getMaxFileSize(user.subscriptionPlan), [user.subscriptionPlan]);

  const defaultBlog: BlogCreate = {
    ...emptyBlog,
    inputLanguage: user.preferredInputLanguage || 'Global English',
    blogLanguage: user.preferredOutputLanguage || 'english',
  };

  const localBlog = cacheGet('new-blog', defaultBlog);
  const hasOldData = !!(localBlog.url || localBlog.prompt);
  const initialValues: BlogCreate = {
    ...defaultBlog,
    ...(hasOldData ? localBlog : {}),
    image: '',
    contentImages: [],
    platforms: [],
    ...(payloadOverride?.url ? { url: payloadOverride.url } : {}),
  };

  const validationSchema = Yup.object().shape({
    url: BlogUrlInputSchema(source),
    prompt: Yup.lazy(() => {
      if (source.inputType === 'textarea') return Yup.string().required(`Text prompt is required`);
      return Yup.string();
    }),
  });

  const onSubmit = async (values: BlogCreate, actions: FormikHelpers<BlogCreate>) => {
    // console.log('Blog Create:', JSON.stringify(values, null, 2));
    if (
      (['video', 'audio'].includes(source.type) && user.credits < 10) ||
      (!['video', 'audio'].includes(source.type) && user.credits < 5)
    ) {
      return toggleReachedLimit();
    }

    const body: BlogCreate = {
      ...values,
      title: values.title || values.prompt,
      sourceType: source.type,
      sourceName: source.name,
      ...(payloadOverride || {}),
    };

    if (isBulk) {
      delete body.url;
      delete body.title;
    }
    if (source.name !== 'Prompt') body.prompt = '';
    if (source.name === 'Prompt') body.url = '';

    if (source.type === 'image') {
      const imageUrl =
        source.name === 'Instagram'
          ? body.image.replace(`${config.apiUrl}context/image-proxy?url=`, '')
          : body.image;

      body.url = source.inputType === 'upload' ? body.url : imageUrl;
      if (body.title) {
        body.prompt = body.title;
      }
    }

    // TLDR
    const contentPosition = contentSettings.findIndex((s) => s.label === 'Content');
    const tldrPosition = contentSettings.findIndex((s) => s.label === 'TLDR');
    const tldr = contentSettings.find((s) => s.label === 'TLDR');
    if (tldr?.checked) {
      body.tldrPosition = tldrPosition < contentPosition ? 'start' : 'end';
    }

    // Quotation
    const contentSetting = contentSettings.find((s) => s.label === 'Content');
    const quotationSetting = contentSetting?.children?.find((c) => c.label === 'Quotation');
    if (quotationSetting) {
      body.includeQuotation = quotationSetting.checked;
    }

    // Blog Size
    body.blogSize = getBlogSizeFromWordCount(body.wordCountApprox);

    return API.post<Blog>(`blogs${isBulk ? '/bulk' : ''}`, body)
      .then((resp) => {
        cacheRemove('new-blog');
        setBlog(resp as Blog);
        if (onBlogCreateSuccess && resp) {
          onBlogCreateSuccess(resp);
        }
        fetchUser();
      })
      .catch((err: BlogCreateError) => {
        if (typeof err === 'string') setError(err);
        if (Array.isArray(err) && typeof err[0] === 'string') setError(err.join(', '));

        const hasPlatformError = (integrations: BlogIntegration[]) =>
          integrations.some((k) => body.platforms.map((i) => i.platform).includes(k));

        const err1 = err as { message: Record<BlogIntegration, boolean> };
        const err1Keys = Object.keys(err1?.message || {}) as BlogIntegration[];
        if (typeof err1?.message === 'object' && err1Keys.some((k) => !err1?.message[k])) {
          if (hasPlatformError(err1Keys)) {
            setSocialError(err1?.message);
            toggleSocialErrorDialogOpen();
          }
        }

        const err2 = err as Record<BlogIntegration, boolean>;
        const err2Keys = Object.keys(err2 || {}) as BlogIntegration[];
        if (typeof err2 === 'object' && err2Keys.some((k) => !err2[k])) {
          if (hasPlatformError(err2Keys)) {
            setSocialError(err2);
            toggleSocialErrorDialogOpen();
          }
        }
      })
      .finally(() => {
        actions.setSubmitting(false);
      });
  };

  const value = {
    maxFileSize,
    source,

    detectedThumbnail,
    setDetectedThumbnail,

    contentSettings,
    setContentSettings,

    setContentImageUploading,
    setCoverUploading,
    setFileUploading,
  };

  const fileUploading = isContentImageUploading || isCoverUploading || isFileUploading;

  return (
    <BlogCreateContext.Provider value={value}>
      <Formik
        validationSchema={validationSchema}
        initialValues={initialValues}
        validateOnChange={false}
        validateOnBlur={false}
        onSubmit={onSubmit}
      >
        {(form) => (
          <>
            {!blog._id ? (
              <form
                onSubmit={(ev) => {
                  ev.preventDefault();
                  form.submitForm();
                }}
              >
                {saveLocally && <BlogCreateDataRestored form={form} defaultBlog={defaultBlog} />}

                {typeof children === 'function' ? children(value) : children}

                <BlogCreateFooter fileUploading={fileUploading} error={error} form={form} />
              </form>
            ) : (
              <BlogLoader mode={source.type} blog={blog} />
            )}
          </>
        )}
      </Formik>

      <BlogCreditLimitReachedDialog
        hasReachedLimit={hasReachedLimit}
        toggleReachedLimit={toggleReachedLimit}
        toggleAddBlogCreditDialog={toggleAddBlogCreditDialog}
      />

      <IntegrationExpiredDialog
        socialError={socialError}
        isSocialErrorDialogOpen={isSocialErrorDialogOpen}
        toggleSocialErrorDialogOpen={toggleSocialErrorDialogOpen}
      />

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
    </BlogCreateContext.Provider>
  );
};

const BlogCreateFooter: React.FC<{
  form: FormikProps<BlogCreate>;
  error: string;
  fileUploading: boolean;
}> = ({ form, error, fileUploading }) => (
  <div className="pt-10">
    {fileUploading ? (
      <ErrorMessage className="pb-1">Please wait for the file upload to complete.</ErrorMessage>
    ) : form.isValidating ? (
      <ErrorMessage className="pb-1">
        Please wait for the input validations to complete.
      </ErrorMessage>
    ) : (
      (!form.isValid || error) && (
        <ErrorMessage className="pb-1">
          {error || 'Please correct the errors before saving.'}
        </ErrorMessage>
      )
    )}
    <Button
      className="!h-10 w-full"
      disabled={fileUploading || form.isSubmitting || form.isValidating || !form.isValid}
      type="submit"
    >
      {form.isSubmitting ? <Spinner /> : <span>Generate Blog</span>}
    </Button>
  </div>
);

const getBlogSizeFromWordCount = (wordCount: number): string => {
  if (wordCount <= 1200) {
    return 'mini';
  }

  if (wordCount > 1200 && wordCount <= 2000) {
    return 'small';
  }

  if (wordCount > 2000 && wordCount <= 3000) {
    return 'medium';
  }

  if (wordCount > 3000 && wordCount <= 4000) {
    return 'large';
  }

  return 'x-large';
};

export default BlogCreateProvider;
