import type { BlogSourceType, BlogSourceName, Blog } from '@ps/types';

import { getImageFromInstagramUrl, getImageFromYouTubeUrl, isValidSourceUrl } from '@/utils/url';
import { timeStringToSeconds, formatSeconds } from '@/utils/time';
import { API } from '@/services/api';

export type UrlMeta = {
  title: string;
  description: string;
  image?: string;
  duration?: string;
  durationSeconds?: number;
};

const validateUrl = async (
  url: string,
  sourceType: BlogSourceType,
  sourceName: BlogSourceName
): Promise<string> => {
  try {
    const body = { url, sourceType, sourceName };
    if (
      sourceName !== 'YouTube' ||
      (sourceName === 'YouTube' && url === 'https://www.youtube.com/watch?v=RQebX6nKBxk')
    ) {
      await API.post(`blogs/validate-url`, body);
    }
    return '';
  } catch (e: unknown) {
    return (e as string) || `Invalid Url provided.`;
  }
};

const emptyMeta = {
  image: '',
  title: '',
  description: '',
  duration: '',
  durationSeconds: 0,
};
const metaCache = new Map<string, UrlMeta>();
const fetchUrlMeta = async (url: string, sourceName: Blog['sourceName']): Promise<UrlMeta> => {
  if (metaCache.has(url)) {
    return metaCache.get(url) || emptyMeta;
  }
  try {
    const meta = (await API.post<UrlMeta>('context/url-meta', { url, sourceName })) || emptyMeta;

    if (!meta.image && isValidSourceUrl(url, 'YouTube')) {
      meta.image = getImageFromYouTubeUrl(url);
    }

    if (isValidSourceUrl(url, 'Instagram')) {
      meta.image = getImageFromInstagramUrl(url);
    }

    if (meta.duration) {
      meta.durationSeconds = timeStringToSeconds(meta.duration);
      meta.duration = formatSeconds(meta.durationSeconds);
    }

    metaCache.set(url, meta);
    return meta;
  } catch (error) {
    console.log('Error Fetching URL Meta:', error);
    return emptyMeta;
  }
};

export { fetchUrlMeta, validateUrl };
