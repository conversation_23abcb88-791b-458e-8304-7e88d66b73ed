import type { BlogCreate, BlogSource } from '@ps/types';

import { createContext, useContext } from 'react';

import { YOUTUBE_SOURCE } from './constants';

export const emptyBlog: BlogCreate = {
  isBulk: false,
  // Source
  url: '',
  prompt: '',
  sourceType: 'video',
  sourceName: 'YouTube',

  //
  seoOption: 'ACTIVE',
  seoInputKeywords: [],

  // Settings
  generationMode: 'auto',
  pov: 'First Person',
  blogTone: 'Neutral',
  blogSize: 'medium',
  wordCountApprox: 2000,
  sourceSimilarity: 70,
  inputLanguage: 'Global English',
  blogLanguage: 'english',
  tldrPosition: 'end',
  embedSource: false,
  embedSourceAsCover: false,
  affiliateCommissionOptIn: false,
  affiliateTargetLocation: 'United States',
  authorsToGiveCredit: [],
  includeQuotation: true,

  // Images
  image: '',
  contentImages: [],
  coverImageType: 'thumbnail',

  // Publish Settings
  platforms: [],

  // Others
  title: '',
  publishAt: '',
  cta: {
    text: '',
    bgColor: '000000',
    borderRadius: 10,
    link: '',
  },

  tableOption: 'No Table',
  chartOption: 'No Chart',
};

export type BlogContentSetting = {
  label: string;
  checked: boolean;
  disabled?: boolean;
  draggable?: boolean;
  children?: BlogContentSetting[];
};

export interface BlogCreateContextType {
  maxFileSize: number;
  source: BlogSource;

  detectedThumbnail: string;
  setDetectedThumbnail: React.Dispatch<string>;

  contentSettings: BlogContentSetting[];
  setContentSettings: (_: BlogContentSetting[]) => void;

  setContentImageUploading: React.Dispatch<boolean>;
  setCoverUploading: React.Dispatch<boolean>;
  setFileUploading: React.Dispatch<boolean>;
}

const defaultFn = () => {};
const defaultContext: BlogCreateContextType = {
  source: YOUTUBE_SOURCE,
  maxFileSize: 0,

  detectedThumbnail: '',
  setDetectedThumbnail: defaultFn,

  contentSettings: [],
  setContentSettings: defaultFn,

  setContentImageUploading: defaultFn,
  setCoverUploading: defaultFn,
  setFileUploading: defaultFn,
};

const BlogCreateContext = createContext<BlogCreateContextType>(defaultContext);
BlogCreateContext.displayName = 'BlogCreateContext';

const useBlogCreate = () => {
  const context = useContext(BlogCreateContext);

  if (context === undefined) {
    throw new Error('useBlogCreate must be used within a BlogCreate.');
  }

  return context;
};

export default useBlogCreate;
export { BlogCreateContext };
