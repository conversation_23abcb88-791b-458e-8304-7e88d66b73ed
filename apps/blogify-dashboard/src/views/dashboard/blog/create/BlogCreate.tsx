import { useLocation, useNavigate } from 'react-router-dom';
import { useContext, useState } from 'react';
// import { FiRefreshCcw } from 'react-icons/fi';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { PageContainerMini } from '@/components/layout/PageContainer';
import { trackBlogCreate } from '@/services/analytics/google-analytics';
import { cacheRemove } from '@/utils/localStorageCache';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

import BlogCreatePublishSettings from './components/BlogCreatePublishSettings';
import BlogAffiliateSettings from './components/BlogAffiliateSettings';
import BlogContentSettings from './components/BlogContentSettings';
import BlogCreateSettings from './components/BlogCreateSettings';
import BlogSourceSettings from './components/BlogSourceSettings';
import BlogCreateProvider from './utils/BlogCreateProvider';
import useBlogSource from '../context/useBlogSource';

const BlogCreate = () => {
  const { sources } = useBlogSource();
  const navigate = useNavigate();
  const location = useLocation();

  const sourceName = location.state;
  const source = sources.find((s) => s.name === sourceName);

  const abilities = useContext(UserAbilitiesContext);
  const initialCancelUrl = `/dashboard/blogs/select-source`;
  const [cancelLink, setCancelLink] = useState(initialCancelUrl);

  if (!source) {
    cacheRemove('new-blog');
    setTimeout(() => navigate(initialCancelUrl));
    return null;
  }
  const onBlogCreateSuccess = () => {
    setCancelLink('');
    trackBlogCreate(source.title);
  };

  return (
    <DashboardContainer
      title="Create New Blog"
      cancelUrl={cancelLink}
      // actions={
      //   <Button variant="secondary">
      //     <FiRefreshCcw />
      //     Use Last Settings
      //   </Button>
      // }
    >
      <PageContainerMini className="pb-2 pt-6">
        <BlogCreateProvider
          onBlogCreateSuccess={onBlogCreateSuccess}
          saveLocally={source.inputType !== 'upload'}
          source={source}
        >
          {({ contentSettings, setContentSettings }) => (
            <>
              <h2 className="mb-6 text-17 font-semibold">{source.title}</h2>

              <BlogSourceSettings />
              <BlogCreateSettings />
              <BlogContentSettings
                contentSettings={contentSettings}
                setContentSettings={setContentSettings}
              />

              <div className="mt-20 flex flex-col gap-6">
                <BlogAffiliateSettings />
                {abilities.blog.publish && <BlogCreatePublishSettings />}
              </div>
            </>
          )}
        </BlogCreateProvider>
      </PageContainerMini>
    </DashboardContainer>
  );
};

export default BlogCreate;
