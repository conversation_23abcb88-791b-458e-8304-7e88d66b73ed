import type { BlogCreateCoPilotOutlineContextProps } from '../context/BlogCreateCoPilotOutlineProvider';

import { useContext } from 'react';

import { BlogCreateCoPilotOutlineContext } from '../context/BlogCreateCoPilotOutlineProvider';

// Custom hook for using BlogCreateCoPilotOutlineContext
export function useBlogCreateCoPilotOutline() {
  const context = useContext(
    BlogCreateCoPilotOutlineContext
  ) as BlogCreateCoPilotOutlineContextProps;
  if (context === undefined) {
    throw new Error(
      'useBlogCreateCoPilotOutline must be used within a BlogCreateCoPilotOutlineProvider'
    );
  }
  return context;
}
