import type { CoPilotContextType, BlogOutLine } from '../context/co-pilot';
import type { Blog } from '@ps/types';

import { useEffect, useState } from 'react';
import { useQuery } from 'react-query';

import { blogEvents } from '@/services/event';
import { emptyBlog } from '@ps/types';

const useCoPilotContextValue = (slug: string): CoPilotContextType => {
  const { data: blog = emptyBlog, refetch } = useQuery<Blog>(`blogs/${slug}`);
  const [blogOutline, setBlogOutline] = useState<BlogOutLine[]>([]);

  const sectionCount = blog.blogOutline?.sections.length;
  const hasConclusion = blog.blogOutline?.sections[sectionCount - 1]?.heading
    .toLowerCase()
    .includes('conclusion');

  useEffect(() => {
    if (blog?._id) {
      const blogOutlineSections: BlogOutLine[] =
        blog.blogOutline?.sections.map((section, i) => ({
          id: `s-${i}`,
          title: section.heading,
          talkingPoints: section.bulletPoints.map((b, j) => ({ id: `tp-${i}${j}`, title: b })),
        })) || [];
      setBlogOutline(blogOutlineSections);

      const subscription = blogEvents.subscribe('BLOG_STATUS_UPDATE', (updates) => {
        if (updates?._id === blog._id && updates.status === 'outline_generated') {
          refetch();
        }
      });

      return () => subscription.unsubscribe();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blog?._id]);

  return { blog, refetchBlog: refetch, hasConclusion, blogOutline, setBlogOutline };
};

export default useCoPilotContextValue;
