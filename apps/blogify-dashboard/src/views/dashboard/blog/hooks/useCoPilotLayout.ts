import { BlogCreateCoPilotLayoutContextProps } from '../context/BlogCreateCoPilotLayoutProvider';

import { useContext } from 'react';

import { BlogCreateCoPilotLayoutContext } from '../context/BlogCreateCoPilotLayoutProvider';

export function useBlogCreateCoPilotLayout() {
  const context = useContext(BlogCreateCoPilotLayoutContext) as BlogCreateCoPilotLayoutContextProps;
  if (context === undefined) {
    throw new Error(
      'useBlogCreateCoPilotLayout must be used within a BlogCreateCoPilotLayoutProvider'
    );
  }
  return context;
}
