import type { BlogIntegration } from '@/types/misc/integration.type';
import type { Blog } from '@ps/types';

import { useCallback, useEffect, useState } from 'react';
import dayjs from 'dayjs';

import {
  BLOG_INPROGRESS_MESSAGES_BY_STATUS,
  BLOG_INPROGRESS_STATUSES,
  BLOG_IGNORED_STATUSES,
  BLOG_FAILED_STATUSES,
} from '@/constants/blog';
import { blogEvents } from '@/services/event';
import { emptyBlog } from '@ps/types';
import { BLOGS } from '@/constants';
import { API } from '@/services/api';

const AFFILIATE_LINK_GEN_IN_PROGRESS_STATUSES = [
  'affiliate_keywords_generating',
  'affiliate_product_matching',
  'affiliate_link_generating',
];

type UseBlogOptions = {
  initialBlog?: Blog;
  isListView?: boolean;
};
type UseBlogOutput = {
  getBlogContent: () => string;
  blog: Blog;
  refetch: () => void;
  // Flags
  isAffiliateLinkGenInProgress: boolean;
  isRecoverable: boolean;
  isInProgress: boolean;
  isCompleted: boolean;
  isAssisted: boolean;
  isFailed: boolean;
  isPaused: boolean;
  // Misc
  publishedPlatforms: BlogIntegration[];
  scheduledPlatforms: BlogIntegration[];
  isProgressMessage: string;
  genericError: string;
  redirect: string;
};

const useBlog = (
  slug: string,
  options?: UseBlogOptions,
  onChange?: (data: Partial<Blog>) => void
): UseBlogOutput => {
  const [blog, setBlog] = useState(options?.initialBlog || emptyBlog);

  const isAssisted = blog.generationMode === 'assisted';
  const hasContent = blog.content?.length > 500;
  const isCompleted = ['completed', 'publisher_dispatched'].includes(blog.status) || hasContent;
  const isPaused = isAssisted && ['outline_generated', 'content_generated'].includes(blog.status); // 'keywords_generated'
  const isTimedOut =
    !isCompleted &&
    !isAssisted &&
    blog.createdAt !== undefined &&
    dayjs().diff(dayjs(blog.createdAt), 'minutes') > 20;
  const isFailed = BLOG_FAILED_STATUSES.includes(blog?.status) || isTimedOut;

  const isRecoverable =
    !(isFailed && ['transcription_failed'].includes(blog?.status)) && !blog.errorCode;

  const isInProgress =
    (!isCompleted && !isFailed && !isPaused) ||
    (!isFailed && BLOG_INPROGRESS_STATUSES.includes(blog.status));

  const isAffiliateLinkGenInProgress = AFFILIATE_LINK_GEN_IN_PROGRESS_STATUSES.includes(
    blog.status
  );

  const type = !!blog.url ? 'link or media file' : 'prompt or document file';
  const genericError = `Blogify couldn't generate a blog based on the given ${type}. Try creating blog with a different ${type}.`;
  const isProgressMessage = BLOG_INPROGRESS_MESSAGES_BY_STATUS[blog.status];

  const redirect = isPaused
    ? `/dashboard/blogs/${blog._id}/co-pilot${hasContent ? '/content' : ''}`
    : isCompleted || isFailed
      ? `/dashboard/blogs/${blog._id}`
      : '';

  const publishedPlatforms = BLOGS.filter((b) => !!blog[`${b}Link`]);
  const scheduledPlatforms = (blog.platforms || [])
    .filter((p) => !publishedPlatforms.includes(p.platform))
    .map((p) => p.platform);

  const getBlogContent = () => {
    if (isCompleted) {
      return blog.content.substring(0, 500).replace(/<[^>]*>/gi, '');
    }

    if (isPaused) {
      return `Your blog ${
        blog.status.includes('outline') ? 'outline' : 'content'
      } is ready. Continue with co-pilot to generate your blog content...`;
    }

    if (isTimedOut) {
      return 'Failed to generate blog within the time limit';
    }

    if (isInProgress && isProgressMessage) {
      return isProgressMessage;
    }

    if (!isRecoverable) {
      return blog.failReason
        ? blog.failReason
        : `We were unable to generate blog in ${blog.inputLanguage || 'English'} to ${
            blog.blogLanguage || 'English'
          } ${blog.url ? 'from this media url ' + blog.url : 'from the short description'}. ${
            blog.url
              ? 'Please try again and ensure the media is downloadable or it is a valid YouTube, Vimeo or Podcast link'
              : 'Please try again or try different short description'
          }`;
    }

    if (isFailed) {
      return blog.failReason ? blog.failReason.replace('undefined: ', '') : genericError;
    }

    return `Generating blog content... ${
      blog.url
        ? 'this may take a few minutes to transcribe and generate content from the video'
        : 'this may take a few minutes to generate content from the short description'
    }`;
  };

  const refetch = useCallback(
    () =>
      API.fetch<Blog>(`blogs/${slug}`)
        .then((resp) => setBlog(resp as Blog))
        .catch((e) => e),
    [slug]
  );

  useEffect(() => {
    if (slug && !blog._id) {
      refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blog._id]);

  useEffect(() => {
    const subscription = blogEvents.subscribe('BLOG_STATUS_UPDATE', (updates) => {
      if (updates?._id !== blog._id) return;
      if (updates.status === 'blog_published' || updates.status === 'blog_publication_failed') {
        refetch();
      }
      if (
        (isAssisted && updates.status === 'outline_generated') ||
        updates.status === 'content_generated'
      ) {
        refetch();
      } else if (updates?.status && !BLOG_IGNORED_STATUSES.includes(updates?.status)) {
        setBlog({ ...blog, status: updates?.status });
      }

      if (onChange) {
        onChange(updates);
      }
    });

    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (options?.isListView && options?.initialBlog?.title) {
      setBlog(options?.initialBlog);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options?.initialBlog?.title]);

  return {
    getBlogContent,
    refetch,
    blog,
    // Flags
    isAffiliateLinkGenInProgress,
    isRecoverable,
    isInProgress,
    isCompleted,
    isAssisted,
    isPaused,
    isFailed,
    // Misc
    publishedPlatforms,
    scheduledPlatforms,
    isProgressMessage,
    genericError,
    redirect,
  };
};

export default useBlog;
