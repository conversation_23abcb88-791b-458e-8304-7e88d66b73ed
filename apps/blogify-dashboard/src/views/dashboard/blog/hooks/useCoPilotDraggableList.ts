import type { BlogCreateCoPilotDraggableListProps } from '../context/BlogCreateCoPilotDraggableListProvider';

import { useContext } from 'react';

import { BlogCreateCoPilotDraggableListContext } from '../context/BlogCreateCoPilotDraggableListProvider';

// custom hook for using BlogCreateCoPilotDraggableListContext
export function useBlogCreateCoPilotDraggableList() {
  const context = useContext(
    BlogCreateCoPilotDraggableListContext
  ) as BlogCreateCoPilotDraggableListProps;
  if (context === undefined) {
    throw new Error(
      'useBlogCreateCoPilotDraggableList must be used within a BlogCreateCoPilotDraggableListProvider'
    );
  }
  return context;
}
