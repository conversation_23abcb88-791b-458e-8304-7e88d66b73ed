import { DragDropContext, DropResult, Droppable } from '@hello-pangea/dnd';
import React, { createContext, useCallback } from 'react';

import { useBlogCreateCoPilotDraggableList } from '../hooks/useCoPilotDraggableList';
import { useBlogCreateCoPilotOutline } from '../hooks/useCoPilotOutline';

export interface BlogCreateCoPilotDraggableListProps {
  onDragEnd: (result: DropResult) => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const BlogCreateCoPilotDraggableListContext =
  createContext<BlogCreateCoPilotDraggableListProps | null>(null);
BlogCreateCoPilotDraggableListContext.displayName = 'BlogCreateCoPilotDraggableListContext';

// reorder items
const reorder = (list: any[], startIndex: number, endIndex: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

export default function BlogCreateCoPilotDraggableListProvider({
  children,
  ...props
}: {
  children: React.ReactNode;
}) {
  const { outlines, setOutlines } = useBlogCreateCoPilotOutline();
  // Drag and drop function for outline titles
  const onDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) {
        return;
      }

      if (result.destination.index === result.source.index) {
        return;
      }
      if (result.type == 'outlines') {
        // Reorder outline titles
        const newOutlines = reorder(outlines, result.source.index, result.destination.index);

        setOutlines(newOutlines);
      } else if (result.type == 'talkingPoints') {
        // Reorder talking points
        const outline = outlines.filter((item) => item.id === result.source.droppableId)[0];

        const newTalkingPoints = reorder(
          outline.talkingPoints,
          result.source.index,
          result.destination.index
        );

        const outlineIndex = outlines.findIndex((item) => item.id === outline.id);
        const newOutlines = [
          ...outlines.slice(0, outlineIndex),
          {
            ...outline,
            talkingPoints: newTalkingPoints,
          },
          ...outlines.slice(outlineIndex + 1),
        ];

        setOutlines(newOutlines);
      }
    },
    [outlines, setOutlines]
  );
  const value = { onDragEnd };
  return (
    <BlogCreateCoPilotDraggableListContext.Provider value={value} {...props}>
      {children}
    </BlogCreateCoPilotDraggableListContext.Provider>
  );
}

export const BlogCreateCoPilotOutlineDraggableList = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { onDragEnd } = useBlogCreateCoPilotDraggableList();
  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="list" type="outlines">
        {(provided) => (
          <div ref={provided.innerRef} {...provided.droppableProps}>
            {children}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export const BlogCreateCoPilotTalkingPointsDraggableList = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { onDragEnd } = useBlogCreateCoPilotDraggableList();
  return <DragDropContext onDragEnd={onDragEnd}>{children}</DragDropContext>;
};
