/**
 * Compound Component provides a way to share stateful logic between components.
 * https://kentcdodds.com/blog/compound-components-with-react-hooks
 * This provider is only having business logic and no UI.
 */
import type { SetStateAction, ReactNode, Dispatch } from 'react';

import { createContext, useCallback, useState, useMemo } from 'react';

export type Step = {
  title: string;
  href: string;
  current: boolean;
  done: boolean;
};

const initialTabs: Step[] = [
  {
    title: 'Title',
    href: '/co-pilot',
    done: false,
    current: true,
  },
  {
    title: 'Outline',
    href: '/co-pilot/outline',
    done: false,
    current: false,
  },
  {
    title: 'Talking Points',
    href: '/co-pilot/talking-points',
    done: false,
    current: false,
  },
  {
    title: 'Content',
    href: '/co-pilot/content',
    done: false,
    current: false,
  },
];

export interface BlogCreateCoPilotLayoutContextProps {
  tabs: Step[];
  setTabs: Dispatch<SetStateAction<Step[]>>;
  handleTabChange: (href: string) => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const BlogCreateCoPilotLayoutContext =
  createContext<BlogCreateCoPilotLayoutContextProps | null>(null);
BlogCreateCoPilotLayoutContext.displayName = 'BlogCreateCoPilotLayoutContext';

// Provider component that wraps your app
export default function BlogCreateCoPilotLayoutProvider({
  children,
  ...props
}: {
  children: ReactNode;
}) {
  const [tabs, setTabs] = useState(initialTabs);

  const handleTabChange = useCallback(
    (href: string) => {
      const currentTabIndex = tabs.findIndex((tab) => href.endsWith(tab.href));
      const newTabs = tabs.map((tab, i) => ({
        ...tab,
        done: i < currentTabIndex,
        current: i === currentTabIndex,
      }));
      setTabs(newTabs);
    },
    [tabs]
  );

  const value = useMemo(
    () => ({
      tabs,
      setTabs,
      handleTabChange,
    }),
    [tabs, setTabs, handleTabChange]
  );

  return (
    <BlogCreateCoPilotLayoutContext.Provider value={value} {...props}>
      {children}
    </BlogCreateCoPilotLayoutContext.Provider>
  );
}
