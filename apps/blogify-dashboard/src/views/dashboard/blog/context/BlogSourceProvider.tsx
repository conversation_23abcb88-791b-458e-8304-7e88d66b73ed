import type { APIResponse, BlogSource } from '@ps/types';

import { CACHE_TIME } from '@/constants';
import emptyAPIResponse from '@/types/resources';
import config from '@/constants/config';

import { createContext } from 'react';
import { useQuery } from 'react-query';

interface BlogSourceContextType {
  sources: BlogSource[];
  sourcesByType: Record<string, BlogSource[]>;
}

const BlogSourceContext = createContext<BlogSourceContextType | undefined>(undefined);

interface BlogSourceProviderProps {
  children: React.ReactNode | ((props: BlogSourceContextType) => React.ReactNode);
}

export const BlogSourceProvider: React.FC<BlogSourceProviderProps> = ({ children }) => {
  const { data: { data: response } = emptyAPIResponse } = useQuery<APIResponse<BlogSource>>(
    'blog-sources?limit=100&sort[0]=sequence,ASC',
    { staleTime: config.isProd ? CACHE_TIME.long : CACHE_TIME.throttle }
  );

  const sourcesByType: Record<string, BlogSource[]> = {
    video: response.filter((s) => s.type === 'video'),
    audio: response.filter((s) => s.type === 'audio'),
    prompt: response.filter((s) => s.type === 'prompt'),
    document: response.filter((s) => s.type === 'document'),
    image: response.filter((s) => s.type === 'image'),
    'e-commerce': response.filter((s) => s.type === 'e-commerce'),
    webpage: response.filter((s) => s.type === 'webLink'),
  };
  const sources = Object.values(sourcesByType).flat();

  const value = { sources, sourcesByType };

  return (
    <BlogSourceContext.Provider value={value}>
      {typeof children === 'function' ? children(value) : children}
    </BlogSourceContext.Provider>
  );
};

export { BlogSourceContext };
