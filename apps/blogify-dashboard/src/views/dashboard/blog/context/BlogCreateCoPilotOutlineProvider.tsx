/**
 * BlogCreateCoPilotOutlineProvider component which is a wrapper for the BlogCreateCoPilotOutlineContext
 * Here we can define all the business logic for the outline
 */

import type { SetStateAction, Dispatch } from 'react';
import type { BlogOutLine } from '@/views/dashboard/blog/context/co-pilot';

import { createContext, useCallback, useEffect, useState, useMemo, useRef } from 'react';
import { calculatePercentageChanged } from '@/utils';

// constants
const percentageChangedThreshold = 60;

export interface BlogCreateCoPilotOutlineContextProps {
  outlines: BlogOutLine[];
  setOutlines: Dispatch<SetStateAction<BlogOutLine[]>>;
  removeOutline: (id: string) => void;
  addOutline: (beforeItemId: string) => void;
  removeTalkingPoint: (outlineId: string, talkingPointId: string) => void;
  addTalkingPoint: (outlineId: string, beforeItemId?: string) => void;
  handleTalkingPointTitleChange: (
    e: React.ChangeEvent<HTMLInputElement>,
    outlineId: string,
    talkingPointId: string
  ) => void;
  handleOutlineTitleChange: (e: React.ChangeEvent<HTMLInputElement>, id: string) => void;
}

const defaultFn = () => {};

// eslint-disable-next-line react-refresh/only-export-components
export const BlogCreateCoPilotOutlineContext = createContext<BlogCreateCoPilotOutlineContextProps>({
  outlines: [],
  setOutlines: defaultFn,
  removeOutline: defaultFn,
  addOutline: defaultFn,
  removeTalkingPoint: defaultFn,
  addTalkingPoint: defaultFn,
  handleTalkingPointTitleChange: defaultFn,
  handleOutlineTitleChange: defaultFn,
});
BlogCreateCoPilotOutlineContext.displayName = 'BlogCreateCoPilotOutlineContext';

// Provider component that wraps your app
export default function BlogCreateCoPilotOutlineProvider({
  initialOutlines,
  children,
  ...props
}: {
  initialOutlines: BlogOutLine[];
  children: React.ReactNode;
}) {
  const [outlines, setOutlines] = useState<
    BlogOutLine[] & {
      customOutline?: boolean;
    }
  >(initialOutlines);
  // store old outlines
  const oldOutlines = useRef(outlines);

  // Remove outline

  useEffect(() => {
    setOutlines(initialOutlines);
  }, [initialOutlines]);

  const removeOutline = useCallback(
    (id: string) => {
      const newOutlines = outlines.filter((outline) => outline.id !== id);
      setOutlines(newOutlines);
    },
    [outlines]
  );

  // Add outline
  const addOutline = useCallback(
    (beforeItemId: string) => {
      const findIndex = outlines.findIndex((outline) => outline.id === beforeItemId);
      const newOutlines = [
        ...outlines.slice(0, findIndex + 1),
        {
          id: `id-${Date.now()}`,
          title: '',
          customOutline: true,
          talkingPoints: [],
        },
        ...outlines.slice(findIndex + 1),
      ];
      setOutlines(newOutlines);
    },
    [outlines]
  );

  // remove talking point
  const removeTalkingPoint = useCallback(
    (outlineId: string, talkingPointId: string) => {
      const outline = outlines.filter((item) => item.id === outlineId)[0];
      const newTalkingPoints = outline.talkingPoints.filter(
        (talkingPoint) => talkingPoint.id !== talkingPointId
      );
      const outlineIndex = outlines.findIndex((item) => item.id === outline.id);
      const newOutlines = [
        ...outlines.slice(0, outlineIndex),
        {
          ...outline,
          talkingPoints: newTalkingPoints,
        },
        ...outlines.slice(outlineIndex + 1),
      ];
      setOutlines(newOutlines);
    },
    [outlines]
  );

  // add talking point
  const addTalkingPoint = useCallback(
    (outlineId: string, beforeItemId?: string) => {
      const outline = outlines.filter((item) => item.id === outlineId)[0];
      const findIndex = beforeItemId
        ? outline.talkingPoints.findIndex((talkingPoint) => talkingPoint.id === beforeItemId)
        : 0;
      const newTalkingPoints = [
        ...outline.talkingPoints.slice(0, findIndex + 1),
        {
          id: `id-${Date.now()}`,
          title: '',
        },
        ...outline.talkingPoints.slice(findIndex + 1),
      ];
      const outlineIndex = outlines.findIndex((item) => item.id === outline.id);
      const newOutlines = [
        ...outlines.slice(0, outlineIndex),
        {
          ...outline,
          talkingPoints: newTalkingPoints,
        },
        ...outlines.slice(outlineIndex + 1),
      ];
      setOutlines(newOutlines);
    },
    [outlines]
  );

  const handleOutlineTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, id: string) => {
      const outline = outlines.filter((item) => item.id === id)[0];
      const indexOfOutline = outlines.findIndex((item) => item.id === id);
      const oldOutline = oldOutlines.current.filter((item) => item.id === id)[0];
      const changes = oldOutline ? calculatePercentageChanged(oldOutline.title, e.target.value) : 0;
      const newOutlines = [
        ...outlines.slice(0, indexOfOutline),
        {
          ...outline,
          title: e.target.value,
          talkingPoints: changes >= percentageChangedThreshold ? [] : outline.talkingPoints,
        },
        ...outlines.slice(indexOfOutline + 1),
      ];
      setOutlines(newOutlines);
    },
    [outlines]
  );

  // handle talking point title change
  const handleTalkingPointTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, outlineId: string, talkingPointId: string) => {
      const outline = outlines.filter((item) => item.id === outlineId)[0];
      const newTalkingPoints = outline.talkingPoints.map((talkingPoint) => {
        if (talkingPoint.id === talkingPointId) {
          return {
            ...talkingPoint,
            title: e.target.value,
          };
        }
        return talkingPoint;
      });
      const outlineIndex = outlines.findIndex((item) => item.id === outline.id);
      const newOutlines = [
        ...outlines.slice(0, outlineIndex),
        {
          ...outline,
          talkingPoints: newTalkingPoints,
        },
        ...outlines.slice(outlineIndex + 1),
      ];
      setOutlines(newOutlines);
    },
    [outlines]
  );

  // Context value that will be passed to components that are wrapped with the Provider
  const value = useMemo(
    () => ({
      outlines,
      setOutlines,
      removeOutline,
      addOutline,
      removeTalkingPoint,
      addTalkingPoint,
      handleOutlineTitleChange,
      handleTalkingPointTitleChange,
    }),
    [
      outlines,
      removeOutline,
      addOutline,
      removeTalkingPoint,
      addTalkingPoint,
      handleOutlineTitleChange,
      handleTalkingPointTitleChange,
    ]
  );

  return (
    <BlogCreateCoPilotOutlineContext.Provider value={value} {...props}>
      {children}
    </BlogCreateCoPilotOutlineContext.Provider>
  );
}
