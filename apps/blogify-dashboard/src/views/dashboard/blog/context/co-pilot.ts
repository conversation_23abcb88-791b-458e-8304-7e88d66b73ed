import type { Blog } from '@ps/types';

import { createContext } from 'react';

import { emptyBlog } from '@ps/types';

export type BlogOutLine = {
  id: string;
  title: string;
  talkingPoints: {
    id: string;
    title: string;
  }[];
};

export type CoPilotContextType = {
  blog: Blog;
  refetchBlog: () => void;
  hasConclusion: boolean;
  blogOutline: BlogOutLine[];
  setBlogOutline: (blog: BlogOutLine[]) => void;
};

const defaultFn = () => {};

const CoPilotContext = createContext<CoPilotContextType>({
  blog: emptyBlog,
  refetchBlog: defaultFn,
  hasConclusion: false,
  blogOutline: [],
  setBlogOutline: defaultFn,
});

export default CoPilotContext;
