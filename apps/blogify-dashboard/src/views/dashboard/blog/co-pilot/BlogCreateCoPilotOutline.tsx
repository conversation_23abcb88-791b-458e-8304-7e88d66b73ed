import { useContext } from 'react';

import { PageContainerMini } from '@/components/layout/PageContainer';

import BlogCreateCoPilotLayout, {
  BorderedBox,
  StepHeading,
} from './components/BlogCreateCoPilotLayout';
import { BlogCreateCoPilotOutlineDraggableList } from '../context/BlogCreateCoPilotDraggableListProvider';
import BlogCreateCoPilotOutlineTitleList from './components/BlogCreateCoPilotOutlineTitleList';
import CoPilotContext from '../context/co-pilot';

export default function BlogCreateCoPilotOutline() {
  const { blog, hasConclusion } = useContext(CoPilotContext);

  return (
    <>
      <BlogCreateCoPilotLayout
        primaryAction={{
          title: 'Create Talking Points',
          href: `/dashboard/blogs/${blog?._id}/co-pilot/talking-points`,
        }}
        secondaryAction={{
          title: 'Back',
          href: `/dashboard/blogs/${blog?._id}/co-pilot`,
        }}
      >
        <PageContainerMini>
          <div className="px-0 sm:px-6">
            <StepHeading
              title="Create an outline"
              subTitle="Here is your blog outline. You can add or remove a heading. You can also drag them and
              sort at your will. Once you are happy with the outline continue with 'Create Talking
              Points'."
            />
          </div>

          <div className="mt-6 flex flex-col gap-3">
            <BorderedBox className="mx-4 sm:mx-6">Intro</BorderedBox>

            <BlogCreateCoPilotOutlineDraggableList>
              <BlogCreateCoPilotOutlineTitleList />
            </BlogCreateCoPilotOutlineDraggableList>

            {!hasConclusion && <BorderedBox className="mx-4 sm:mx-6">Conclusion</BorderedBox>}
          </div>
        </PageContainerMini>
      </BlogCreateCoPilotLayout>
    </>
  );
}
