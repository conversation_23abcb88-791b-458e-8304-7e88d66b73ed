import type { Blog } from '@ps/types';

import { useNavigate } from 'react-router-dom';
import { useContext } from 'react';
import { useToggle } from 'react-use';

import { PageContainerMini } from '@/components/layout/PageContainer';
import { API } from '@/services/api';
import Loader from '@/components/misc/Loader';

import BlogCreateCoPilotLayout, {
  BorderedBox,
  StepHeading,
} from './components/BlogCreateCoPilotLayout';
import { BlogCreateCoPilotOutlineContext } from '../context/BlogCreateCoPilotOutlineProvider';
import BlogCreateCoPilotTalkingPointsList from './components/BlogCreateCoPilotTalkingPointsList';
import CoPilotContext from '../context/co-pilot';
import useBlog from '../hooks/useBlog';

export default function BlogCreateCoPilotTalkingPoints() {
  const [isLoading, toggleLoading] = useToggle(false);
  const { outlines } = useContext(BlogCreateCoPilotOutlineContext);
  const { blog, refetchBlog, hasConclusion } = useContext(CoPilotContext);
  const navigate = useNavigate();

  const submit = () => {
    if (blog.content?.length > 500) {
      return navigate(`/dashboard/blogs/${blog?._id}/co-pilot/content`);
    }
    toggleLoading();
    const body: Partial<Blog> = {
      blogOutline: {
        ...blog.blogOutline,
        sections: outlines.map((bo) => ({
          heading: bo.title,
          bulletPoints: bo.talkingPoints.map((tp) => tp.title),
        })),
      },
    };
    return API.patch(`blogs/${blog?._id}/assisted`, body);
  };

  useBlog(blog._id, { initialBlog: blog }, ({ status }) => {
    if (status === 'content_generated') {
      refetchBlog();
      toggleLoading();
      return navigate(`/dashboard/blogs/${blog?._id}/co-pilot/content`);
    }
  });

  return (
    <BlogCreateCoPilotLayout
      primaryAction={{
        title: 'Generate Content',
        href: '',
        loading: isLoading,
        action: submit,
      }}
      secondaryAction={{
        title: 'Back',
        href: `/dashboard/blogs/${blog?._id}/co-pilot/outline`,
      }}
    >
      {isLoading ? (
        <PageContainerMini className="min-h-[45vh]">
          <div className="flex min-h-[45vh] flex-col flex-center">
            <Loader />
            <div className="mt-[-60px] text-xl font-semibold text-black1">
              Generating your blog...
            </div>

            <div className="mt-2 text-center text-sm text-black1">
              "🍳 We're putting the final touches on your blog... served hot and fresh, just the way
              you like it! 📰"
            </div>
          </div>
        </PageContainerMini>
      ) : (
        <PageContainerMini className="px-0 sm:px-6">
          <StepHeading
            title="Create Talking Points"
            subTitle="We've generated some talking points for each of the heading. Check, modify or reorder
            the talking points to you likings and then continue with 'Generate Content' and we'll
            take care of your blog."
          />

          <div className="mt-6 flex flex-col gap-3">
            <BorderedBox>Intro</BorderedBox>

            <BlogCreateCoPilotTalkingPointsList />

            {!hasConclusion && <BorderedBox>Conclusion</BorderedBox>}
          </div>
        </PageContainerMini>
      )}
    </BlogCreateCoPilotLayout>
  );
}
