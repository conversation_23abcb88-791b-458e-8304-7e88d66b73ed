import { useContext } from 'react';

import { PageContainerMini } from '@/components/layout/PageContainer';

import BlogCreateCoPilotLayout, {
  BorderedBox,
  StepHeading,
} from './components/BlogCreateCoPilotLayout';
import { BlogLoader } from '../components/BlogLoader';
import CoPilotContext from '../context/co-pilot';

export default function BlogCreateCoPilotTitle() {
  const { blog } = useContext(CoPilotContext);
  const hasOutline = !!blog.blogOutline?.sections?.length;

  return hasOutline ? (
    <BlogCreateCoPilotLayout
      primaryAction={{
        title: 'Create Outline',
        href: `/dashboard/blogs/${blog?._id}/co-pilot/outline`,
      }}
    >
      <PageContainerMini className="px-0 sm:px-6">
        <StepHeading
          title="Select Blog Title"
          subTitle="We have generated a blog title for you. We are working hard to generate multiple blog
          titles in future so that you may select the one you like most. For now continue using
          the 'Create Outline'."
        />

        <div className="mt-6 flex flex-col gap-3">
          <label>
            <BorderedBox>
              <div className="flex items-center gap-3">
                <input type="radio" name="blogTitle" checked readOnly />
                <div className="text-black1">{blog?.title}</div>
              </div>
            </BorderedBox>
          </label>
        </div>
      </PageContainerMini>
    </BlogCreateCoPilotLayout>
  ) : (
    <BlogLoader mode={blog.sourceType} blog={blog} />
  );
}
