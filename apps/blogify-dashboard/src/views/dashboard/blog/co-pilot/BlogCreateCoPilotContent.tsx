import type { Blog } from '@ps/types';

import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';
import * as Yup from 'yup';

import { API } from '@/services/api';
import ErrorMessage from '@/components/form/ErrorMessage';
import RichEditor from '@/components/form/RichEditor';
import useForm from '@/hooks/useForm';

import BlogCreateCoPilotLayout, {
  BorderedBox,
  StepHeading,
} from './components/BlogCreateCoPilotLayout';
import CoPilotContext from '../context/co-pilot';

const BlogEditSchema = Yup.object().shape({
  content: Yup.string().required(`Blog content is required`),
});

export default function BlogCreateCoPilotContent() {
  const [isLoading, toggleLoading] = useToggle(false);
  const [error, setError] = useState('');
  const { blog } = useContext(CoPilotContext);
  const navigate = useNavigate();

  const initialValues: Partial<Blog> = {
    title: blog.title,
    content: blog.content,
  };

  const submit = async (body: Partial<Blog>) => {
    toggleLoading();

    try {
      if (blog?.status !== 'publisher_dispatched') {
        await API.patch(`blogs/${blog?._id}`, body);
        await API.patch(`blogs/${blog?._id}/assisted/publish`, {});
      }
      toggleLoading();
      return navigate(`/dashboard/blogs/${blog?._id}`);
    } catch (e: any) {
      setError(e?.message);
      toggleLoading();
    }
  };

  useEffect(() => {
    if (blog.content && !form.values.content) {
      form.setFieldValue('content', blog.content);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blog.content]);

  const form = useForm({ initialValues, submit, validationSchema: BlogEditSchema });

  return (
    <form>
      <BlogCreateCoPilotLayout
        primaryAction={{
          title: 'Save',
          href: '',
          loading: isLoading,
          action: form.submitForm,
        }}
      >
        <div className="px-0 sm:px-5 md:px-10">
          <StepHeading
            title="Review Contents"
            subTitle="Your blog is ready hot & fresh, just the way you wanted it! Review, edit, customize the blog to your liking. And when ready hit 'Save'"
          />

          <div className="mt-9">
            <div className="mb-2 text-sm font-semibold">Cover Image</div>
            <img
              className="h-[200px] w-full rounded object-cover sm:h-[144px] sm:w-[260px]"
              src={blog.image || '/images/temp-bg.jpg'}
            />
          </div>

          <div className="mt-9">
            <div className="mb-2 text-sm font-semibold">Tile</div>
            <BorderedBox>
              <div className="text-black1">{blog.title}</div>
            </BorderedBox>
          </div>

          <div className="mt-9">
            <RichEditor
              blogLanguage={blog.blogLanguage}
              value={blog.content}
              onChange={form.getInputFields('content').onChange}
            />
            <ErrorMessage>{error}</ErrorMessage>
          </div>
        </div>
      </BlogCreateCoPilotLayout>
    </form>
  );
}
