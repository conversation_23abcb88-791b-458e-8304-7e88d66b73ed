import { PiDotsSixBold } from 'react-icons/pi';
import { Droppable } from '@hello-pangea/dnd';

import { BlogCreateCoPilotTalkingPointsDraggableList } from '../../context/BlogCreateCoPilotDraggableListProvider';
import { useBlogCreateCoPilotOutline } from '../../hooks/useCoPilotOutline';
import { OutlineTitleActionButton } from './BlogCreateCoPilotOutlineTitle';
import BlogCreateCoPilotTalkingPointsItem from './BlogCreateCoPilotTalkingPointsItem';

export default function BlogCreateCoPilotTalkingPointsList() {
  const { outlines, addTalkingPoint } = useBlogCreateCoPilotOutline();

  return (
    <>
      {outlines.length
        ? outlines.map((item, index) => (
            <div key={item.id} className="flex w-full flex-col rounded-sm border border-[#D7E1F1]">
              {/* Outline title */}
              <div className="flex w-full items-center gap-5 border border-[#D7E1F1] bg-white p-4">
                {/* Index number */}
                <OutlineTitleActionButton>
                  <div className="text-xs font-semibold text-gray2">
                    {index < 9 ? `0${index + 1}` : index + 1}
                  </div>
                </OutlineTitleActionButton>
                {/* Outline title */}
                <div className="font-semibold text-black1">{item.title}</div>
              </div>

              <BlogCreateCoPilotTalkingPointsDraggableList>
                {/* Talking points list */}
                <Droppable droppableId={item.id} type="talkingPoints">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      {/* Render list item */}
                      {item.talkingPoints.length ? (
                        item.talkingPoints.map((talkingPoint, i) => (
                          <BlogCreateCoPilotTalkingPointsItem
                            key={talkingPoint.id}
                            outline={item}
                            talkingPoint={talkingPoint}
                            index={i}
                          />
                        ))
                      ) : (
                        <div className="flex w-full items-center justify-between bg-white px-3 py-2">
                          <div className="flex gap-3">
                            {/* Draggable icon */}
                            <div className="flex cursor-grab flex-col justify-center text-base sm:text-2xl">
                              <PiDotsSixBold className="text-gray2" />
                            </div>
                            {/* Talking point */}
                            <button
                              className="m-0 cursor-pointer border-none bg-transparent p-0 text-sm font-semibold text-success outline-none"
                              onClick={() => addTalkingPoint(item.id)}
                            >
                              + Add points
                            </button>
                          </div>
                        </div>
                      )}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </BlogCreateCoPilotTalkingPointsDraggableList>
            </div>
          ))
        : null}
    </>
  );
}
