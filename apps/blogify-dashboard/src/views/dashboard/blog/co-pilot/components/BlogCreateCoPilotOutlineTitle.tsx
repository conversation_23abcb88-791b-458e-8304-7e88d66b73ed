import type { BlogOutLine } from '@/views/dashboard/blog/context/co-pilot';

import { memo, useEffect, useRef, useState } from 'react';
import { AiOutlineMinus, AiOutlinePlus } from 'react-icons/ai';
import { PiDotsSixVerticalBold } from 'react-icons/pi';
import { Draggable } from '@hello-pangea/dnd';
import styled from 'styled-components';

import { cn } from '@ps/ui/lib/utils';
import theme from '@/styles/theme';

import { useBlogCreateCoPilotOutline } from '../../hooks/useCoPilotOutline';

function BlogCreateCoPilotOutlineTitle({
  outline,
  index,
  disableRemove,
}: {
  outline: BlogOutLine & { customOutline?: boolean };
  index: number;
  disableRemove?: boolean;
}) {
  const { removeOutline, addOutline, handleOutlineTitleChange } = useBlogCreateCoPilotOutline();
  const [isError, setIsError] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus({ preventScroll: true });
    }
  }, [inputRef, index]);

  return (
    <>
      <Draggable draggableId={outline.id} index={index}>
        {(provided) => (
          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
            <div className="flex items-center justify-between">
              {/* Draggable icon */}
              <div className="cursor-grab text-base sm:text-2xl">
                <PiDotsSixVerticalBold className="text-inherit" color={theme.colors.gray2} />
              </div>
              {/* Outline title contents */}
              <div className="flex w-full flex-col gap-1">
                {/* Outline title */}
                <div
                  className={cn(
                    'flex w-full items-center justify-between gap-2 rounded-sm border px-6 py-4',
                    {
                      'bg-gray7': outline.customOutline,
                      'bg-white': !outline.customOutline,
                      'border-red2': isError,
                      'border-[#D7E1F1]': !isError,
                    }
                  )}
                >
                  <div className="flex w-full gap-5">
                    {/* Index number */}
                    <OutlineTitleActionButton>
                      <div className="text-xs font-semibold text-gray2">
                        {index < 9 ? `0${index + 1}` : index + 1}
                      </div>
                    </OutlineTitleActionButton>
                    {/* Input field */}
                    <input
                      className="w-full bg-transparent p-0 text-base font-semibold text-black1"
                      type="text"
                      name="title1"
                      color={theme.colors.black1}
                      defaultValue={outline.title}
                      onChange={(e) => {
                        handleOutlineTitleChange(e, outline.id);
                        setIsError(e.target.value === '');
                      }}
                      ref={inputRef}
                      onFocus={(e) => setIsError(e.target.value === '')}
                    />
                  </div>
                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    {/* add */}
                    <OutlineTitleActionButton variant="add" onClick={() => addOutline(outline.id)}>
                      <AiOutlinePlus size="12px" />
                    </OutlineTitleActionButton>
                    {/* remove */}
                    <OutlineTitleActionButton
                      variant="remove"
                      onClick={() => removeOutline(outline.id)}
                      disabled={disableRemove}
                    >
                      <AiOutlineMinus size="12px" />
                    </OutlineTitleActionButton>
                  </div>
                </div>
                {/* Error message */}
                {isError && <div className="text-xs text-red2">Please add a heading.</div>}
              </div>
              {/* Draggable icon */}
              <div className="cursor-grab text-base sm:text-2xl">
                <PiDotsSixVerticalBold className="text-inherit" color={theme.colors.gray2} />
              </div>
            </div>
          </div>
        )}
      </Draggable>
    </>
  );
}

export default memo(BlogCreateCoPilotOutlineTitle);

export const OutlineTitleActionButton = styled.button<{ variant?: string }>`
  background: ${theme.colors.white};
  border: 1px solid #d7e1f1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 3px;
  color: ${theme.colors.gray2};
  ${({ variant }) =>
    variant === 'add' &&
    `
    :hover{
      background: #27BA7C;
      color : ${theme.colors.white};
    }
  `}
  ${({ variant }) =>
    variant === 'remove' &&
    `
    :hover{
      background: #D0021B;
      color : ${theme.colors.white};
    }
  `}
  :disabled {
    cursor: not-allowed;
    background: ${theme.colors.gray7};
    border: 1px solid ${theme.colors.gray7};
    color: ${theme.colors.gray2};
  }
`;
