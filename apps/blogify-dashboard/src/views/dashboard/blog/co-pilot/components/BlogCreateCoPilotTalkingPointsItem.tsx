import type { BlogOutLine } from '@/views/dashboard/blog/context/co-pilot';

import { AiOutlineMinus, AiOutlinePlus } from 'react-icons/ai';
import { useEffect, useRef } from 'react';
import { PiDotsSixBold } from 'react-icons/pi';
import { Draggable } from '@hello-pangea/dnd';

import theme from '@/styles/theme';

import { useBlogCreateCoPilotOutline } from '../../hooks/useCoPilotOutline';
import { OutlineTitleActionButton } from './BlogCreateCoPilotOutlineTitle';

export default function BlogCreateCoPilotTalkingPointsItem({
  outline,
  talkingPoint,
  index,
}: {
  outline: BlogOutLine & { customOutline?: boolean };
  talkingPoint: {
    id: string;
    title: string;
  };
  index: number;
}) {
  const { removeTalkingPoint, addTalkingPoint, handleTalkingPointTitleChange } =
    useBlogCreateCoPilotOutline();

  const talkingPointInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (talkingPointInputRef.current) {
      talkingPointInputRef.current.focus({ preventScroll: true });
    }
  }, [index, talkingPointInputRef]);

  return (
    <>
      <Draggable draggableId={talkingPoint.id} index={index}>
        {(provided) => (
          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
            <div className="flex w-full items-center justify-between bg-white px-3 py-2">
              <div className="flex w-full gap-3">
                {/* Draggable icon */}
                <div className="flex cursor-grab flex-col justify-center text-base sm:text-2xl">
                  <PiDotsSixBold className="text-inherit" color={theme.colors.gray2} />
                </div>
                {/* Talking point */}
                <input
                  className="w-full bg-transparent p-0 text-sm font-semibold text-black1"
                  name={`talkingPoint-${talkingPoint.id}`}
                  defaultValue={talkingPoint.title}
                  onChange={(e) => handleTalkingPointTitleChange(e, outline.id, talkingPoint.id)}
                  ref={talkingPointInputRef}
                  onBlur={() => {
                    if (!talkingPoint.title) {
                      removeTalkingPoint(outline.id, talkingPoint.id);
                    }
                  }}
                />
              </div>
              {/* Actions */}
              <div className="flex items-center gap-1">
                {/* add */}
                <OutlineTitleActionButton
                  variant="add"
                  onClick={() => addTalkingPoint(outline.id, talkingPoint.id)}
                >
                  <AiOutlinePlus size="12px" />
                </OutlineTitleActionButton>
                {/* remove */}
                <OutlineTitleActionButton
                  variant="remove"
                  onClick={() => removeTalkingPoint(outline.id, talkingPoint.id)}
                >
                  <AiOutlineMinus size="12px" />
                </OutlineTitleActionButton>
              </div>
            </div>
          </div>
        )}
      </Draggable>
    </>
  );
}
