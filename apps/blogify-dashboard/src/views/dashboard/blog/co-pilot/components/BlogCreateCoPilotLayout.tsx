import type { BlogCreateCoPilotNavigationProps } from './BlogCreateCoPilotNavigation';

import React from 'react';

import { Card } from '@/components/layout';

import BlogCreateCoPilotNavigation from './BlogCreateCoPilotNavigation';

interface BlogCreateCoPilotLayoutProps extends BlogCreateCoPilotNavigationProps {
  children?: React.ReactNode;
}

export default function BlogCreateCoPilotLayout({
  primaryAction,
  secondaryAction,
  children,
}: BlogCreateCoPilotLayoutProps) {
  return (
    <>
      <BlogCreateCoPilotNavigation
        primaryAction={primaryAction}
        secondaryAction={secondaryAction}
      />

      <Card className="mt-0.5 min-h-[67vh] p-5 sm:px-5 sm:py-10 md:px-10">{children}</Card>
      <div className="h-10" />
    </>
  );
}

const BorderedBox = ({ children, className, ...props }: React.HTMLProps<HTMLDivElement>) => (
  <div className={`rounded border border-gray5 px-4 py-5 ${className}`} {...props}>
    <div className="text-sm font-semibold text-gray2 sm:text-base">{children}</div>
  </div>
);

const StepHeading = ({ title, subTitle }: { title: string; subTitle: string }) => (
  <>
    <div className="font-semibold text-black1">{title}</div>
    <div className="mt-2 text-sm text-black1">{subTitle}</div>
  </>
);

export { BorderedBox, StepHeading };
