import { useBlogCreateCoPilotOutline } from '../../hooks/useCoPilotOutline';
import BlogCreateCoPilotOutlineTitle from './BlogCreateCoPilotOutlineTitle';

export default function BlogCreateCoPilotOutlineTitleList() {
  const { outlines } = useBlogCreateCoPilotOutline();
  const disableRemove = outlines.length === 1;
  return (
    <>
      <div className="flex flex-col gap-3">
        {/* Outline title draggable list items */}
        {outlines.length
          ? outlines.map((item, index) => (
              <BlogCreateCoPilotOutlineTitle
                outline={item}
                index={index}
                key={item.id}
                disableRemove={disableRemove}
              />
            ))
          : null}
      </div>
    </>
  );
}
