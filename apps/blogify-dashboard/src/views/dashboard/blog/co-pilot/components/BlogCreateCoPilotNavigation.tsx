import type { Step } from '@/views/dashboard/blog/context/BlogCreateCoPilotLayoutProvider';

import { MdKeyboardArrowLeft, MdChe<PERSON> } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import styled from 'styled-components';

import { useBlogCreateCoPilotLayout } from '@/views/dashboard/blog/hooks/useCoPilotLayout';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { cn } from '@ps/ui/lib/utils';
import mediaQuery from '@/styles/mediaQuery';
import Theme from '@/styles/theme';
import Link from '@/components/common/Link';

interface ActionProps {
  title: string;
  href: string;
  loading?: boolean;
  action?: (_?: any) => void;
}
export interface BlogCreateCoPilotNavigationProps {
  primaryAction?: ActionProps | null;
  secondaryAction?: ActionProps | null;
}

export default function BlogCreateCoPilotNavigation({
  primaryAction,
  secondaryAction,
}: BlogCreateCoPilotNavigationProps) {
  const { tabs, handleTabChange } = useBlogCreateCoPilotLayout();
  const navigate = useNavigate();
  // Change tabs on page refresh/change
  const pathname = window.location.pathname;

  useEffect(() => {
    handleTabChange(pathname);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  return (
    <Card className="!flex-row flex-wrap justify-between px-2 sm:flex-nowrap">
      <div className="flex w-full justify-center gap-6 px-4 py-5 sm:w-auto sm:justify-start sm:gap-5 sm:py-3 md:gap-6 lg:gap-10">
        {tabs.map((step, index) => (
          <CoPilotStep key={index} step={step} stepNo={index + 1} />
        ))}
      </div>

      <Actions className="flex w-full gap-3 px-4 py-5 sm:w-auto sm:py-3">
        {secondaryAction ? (
          <Link to={secondaryAction.href}>
            <Button variant="secondary" type="button">
              <MdKeyboardArrowLeft size={22} />
              <div className="-mt-px text-sm">{secondaryAction.title}</div>
            </Button>
          </Link>
        ) : null}
        {primaryAction ? (
          <Button
            className="w-full min-w-[182px]"
            onClick={() => {
              if (primaryAction.action) primaryAction.action();
              if (primaryAction.href) {
                return navigate(primaryAction.href);
              }
            }}
            loading={primaryAction.loading}
            type="button"
          >
            <div className="capitalize">{primaryAction.title}</div>
          </Button>
        ) : null}
      </Actions>
    </Card>
  );
}

const CoPilotStep = ({ step, stepNo }: { step: Step; stepNo: number }) => (
  <div className="flex w-max items-center">
    <div
      className={cn('flex size-4 min-w-4 rounded-full text-xs font-bold text-white flex-center', {
        'bg-primary': step.current || step.done,
        'bg-gray2': !(step.current || step.done),
      })}
    >
      {step.done ? <MdCheck size={11} color={Theme.colors.white} /> : stepNo}
    </div>
    <div
      className={cn('ml-2 whitespace-nowrap text-sm font-semibold text-gray2', {
        'hidden md:inline': !step.current,
        '!text-primary': step.current || step.done,
      })}
    >
      {step.title}
    </div>
  </div>
);

const Actions = styled.div`
  ${mediaQuery.xs`
    box-shadow: 0 -2px 3px 0 rgba(122, 142, 178, 0.1);
    background: white;
    position: fixed;
    z-index: 1000;
    bottom: 0;
    left: 0;
  `}
`;
