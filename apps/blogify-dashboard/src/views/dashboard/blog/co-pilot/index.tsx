import { useParams, Outlet } from 'react-router-dom';

import { DashboardContainer } from '../../layout';
import BlogCreateCoPilotDraggableListProvider from '../context/BlogCreateCoPilotDraggableListProvider';
import BlogCreateCoPilotOutlineProvider from '../context/BlogCreateCoPilotOutlineProvider';
import BlogCreateCoPilotLayoutProvider from '../context/BlogCreateCoPilotLayoutProvider';
import useCoPilotContextValue from '../hooks/useCoPilotContextValue';
import CoPilotContext from '../context/co-pilot';
import PageContainer from '@/components/layout/PageContainer';

export default function BlogCreateCoPilot() {
  const { slug = '' } = useParams();
  const coPilotContextValue = useCoPilotContextValue(slug);

  return (
    <CoPilotContext.Provider value={coPilotContextValue}>
      <PageContainer>
        <DashboardContainer title="Generating blog in Co-Pilot...">
          <BlogCreateCoPilotLayoutProvider>
            <BlogCreateCoPilotOutlineProvider initialOutlines={coPilotContextValue.blogOutline}>
              <BlogCreateCoPilotDraggableListProvider>
                <Outlet />
              </BlogCreateCoPilotDraggableListProvider>
            </BlogCreateCoPilotOutlineProvider>
          </BlogCreateCoPilotLayoutProvider>
        </DashboardContainer>
      </PageContainer>
    </CoPilotContext.Provider>
  );
}
