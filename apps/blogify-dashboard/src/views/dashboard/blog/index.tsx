/* eslint-disable react-refresh/only-export-components */
import type { RouteObject } from 'react-router-dom';

import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { RouteErrorBoundary } from '@/components/error';
import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';

import { BlogSourceProvider } from './context/BlogSourceProvider';

// Blogs
const BlogCreateCoPilotTalkingPoints = safeLazy(
  () => import('./co-pilot/BlogCreateCoPilotTalkingPoints')
);
const BlogCreateCoPilotContent = safeLazy(() => import('./co-pilot/BlogCreateCoPilotContent'));
const BlogCreateCoPilotOutline = safeLazy(() => import('./co-pilot/BlogCreateCoPilotOutline'));
const BlogCreateCoPilotTitle = safeLazy(() => import('./co-pilot/BlogCreateCoPilotTitle'));
const BlogCreateSelectSource = safeLazy(() => import('./create/BlogCreateSelectSource'));
const BlogBulkCreate = safeLazy(() => import('./create/bulk/BlogBulkCreate'));
const BlogDetails = safeLazy(() => import('./details/BlogDetails'));
const BlogCoPilot = safeLazy(() => import('./co-pilot'));
const BlogCreate = safeLazy(() => import('./create/BlogCreate'));
const BlogList = safeLazy(() => import('./list/BlogList'));
const BlogEdit = safeLazy(() => import('./edit/BlogEdit'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);

function BlogRoot() {
  return (
    <BlogSourceProvider>
      <Outlet />
    </BlogSourceProvider>
  );
}

const getRoutes = (): RouteObject[] => [
  {
    path: 'blogs',
    element: <BlogRoot />,
    errorElement: <RouteErrorBoundary />,
    children: [
      { path: '', element: <Lazy as={BlogList} /> },
      { path: 'select-source', element: <Lazy as={BlogCreateSelectSource} /> },
      { path: 'select-source/create', element: <Lazy as={BlogCreate} /> },
      { path: 'select-source/bulk-create', element: <Lazy as={BlogBulkCreate} /> },
      { path: ':slug', element: <Lazy as={BlogDetails} /> },
      { path: ':slug/edit', element: <Lazy as={BlogEdit} /> },
      {
        path: ':slug/co-pilot',
        element: <Lazy as={BlogCoPilot} />,
        children: [
          { path: '', element: <Lazy as={BlogCreateCoPilotTitle} /> },
          { path: 'outline', element: <Lazy as={BlogCreateCoPilotOutline} /> },
          { path: 'talking-points', element: <Lazy as={BlogCreateCoPilotTalkingPoints} /> },
          { path: 'content', element: <Lazy as={BlogCreateCoPilotContent} /> },
        ],
      },
    ],
  },
];

export default getRoutes;
