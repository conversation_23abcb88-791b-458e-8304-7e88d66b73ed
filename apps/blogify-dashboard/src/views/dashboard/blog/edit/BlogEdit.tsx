/* eslint-disable react-hooks/exhaustive-deps */
import type { Imperivatives } from '@/components/form/RichEditor';
import type { Blog } from '@ps/types';

import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { RiImageEditFill } from 'react-icons/ri';
import { IoMdCloudDone } from 'react-icons/io';
import { BsThreeDots } from 'react-icons/bs';
import { useToggle } from 'react-use';
import { FaUnlink } from 'react-icons/fa';
import { useQuery } from 'react-query';
import { FiCopy } from 'react-icons/fi';
import debounce from 'lodash/debounce';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import {
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { mergeDateAndTime, formatDate, formatTime } from '@/utils/date';
import { ImageDialogProvider } from '@/views/dashboard/image/context/ImageDialogProvider';
import { SEOScore } from '@/components/seo';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { API } from '@/services/api';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import ImageInsertDialog from '@/views/dashboard/image/ImageInsertDialog';
import ErrorMessage from '@/components/form/ErrorMessage';
import RichEditor from '@/components/form/RichEditor';
import useForm from '@/hooks/useForm';

import { getCopyContent } from '../utils';
import TableOfContents from '../components/TableOfContents';
import BlogLinks from './BlogLinks';

const BlogEditSchema = Yup.object().shape({
  title: Yup.string().required(`Blog title is required`),
  image: Yup.string(),
  content: Yup.string().required(`Blog content is required`),
});

const BlogEdit = () => {
  const inserterRef = useRef<Imperivatives>({ inserter: () => {} });
  const { slug } = useParams();

  const { data: savedBlog = { _id: undefined }, isLoading } = useQuery<Blog>(`blogs/${slug}`, {
    staleTime: 0,
    cacheTime: 0,
  });
  const initialBlog = savedBlog;

  const [isSubmitting, setIsSubmitting] = useToggle(false);
  const [formError, setFormError] = useState('');
  const [blog, setBlog] = useState<Partial<Blog>>(initialBlog);
  const navigate = useNavigate();

  const blogTitle = blog?.title || '';
  const blogImage = blog?.image || '';
  const blogContent = blog?.content || '';

  const initialValues: Partial<Blog> = {
    title: blogTitle,
    image: blogImage,
    content: blogContent,
    publishDate: formatDate(blog?.publishAt as string) || '',
    publishTime: formatTime(blog?.publishAt as string) || '',
  };

  const onUpdate = debounce((content: string) => {
    save({ ...form.values, content });
  }, 500);

  const save = (values: Partial<Blog>) => {
    const body: Partial<Blog> = {
      title: values.title,
      content: values.content,
      publishAt: mergeDateAndTime(values.publishDate, String(values.publishTime)),
    };

    return API.patch(`blogs/${slug}`, body);
  };

  const submit = (values: Partial<Blog>) => {
    setIsSubmitting();
    setFormError('');

    save(values)
      .then(() => {
        setIsSubmitting();
        return navigate(`/dashboard/blogs/${slug}`);
      })
      .catch((e: string) => {
        setIsSubmitting();
        setFormError(e);
      });
  };

  useEffect(() => {
    if (blogTitle) {
      form.setFieldValue('title', blogTitle);
      form.setFieldValue('content', blogContent);
      form.setFieldValue('image', blogImage);
    }
  }, [blogTitle]);

  useEffect(() => {
    if (initialBlog?._id) {
      setBlog(initialBlog);
    }
  }, [initialBlog?._id]);

  const form = useForm({ initialValues, submit, validationSchema: BlogEditSchema });

  return (
    <form onSubmit={form.submitForm}>
      <DashboardContainer
        title="Edit Blog"
        actions={
          <BlogEditActions isSubmitting={isSubmitting} blogContent={blogContent} form={form} />
        }
      >
        <div className="mx-auto w-full">
          <ImageDialogProvider
            onImageSelect={async (url) => {
              setIsSubmitting(true);
              form.setFieldValue('image', url);
              await API.patch(`blogs/${slug}`, { image: url });
              setIsSubmitting(false);
            }}
          >
            {({ toggleDialog }) => (
              <>
                <div className="relative mt-4 flex justify-center">
                  <div
                    className="absolute inset-0 bg-cover bg-center blur-lg"
                    style={{
                      backgroundImage: `url(${form.getInputFields('image').value || blog?.image || '/images/temp-bg.jpg'})`,
                    }}
                  ></div>

                  <div className="relative max-h-[500px] rounded-xl lg:w-[70%]">
                    <img
                      className="size-full rounded-xl object-cover"
                      src={
                        form.getInputFields('image').value || blog?.image || '/images/temp-bg.jpg'
                      }
                    />

                    <Button
                      type="button"
                      className="absolute bottom-3 right-3"
                      variant="secondary"
                      onClick={() => {
                        toggleDialog(true);
                      }}
                    >
                      <RiImageEditFill /> {blog?.image ? 'Change' : 'Upload'} Cover
                    </Button>
                  </div>
                </div>

                <ImageInsertDialog
                  title={blog?.image ? 'Change Cover' : 'Upload Cover'}
                  description={blog?.image ? 'Change blog cover image.' : 'Upload blog cover image'}
                />
              </>
            )}
          </ImageDialogProvider>

          <div className="mb-8 mt-10 rounded-lg border border-gray10">
            <h3 className="border-b border-gray10 px-3 py-2.5 text-xl font-semibold">Title</h3>
            <div className="px-10 py-6">
              <input
                type="text"
                className="w-full text-3xl font-bold focus:outline-none"
                placeholder="My Awesome Blog Title"
                defaultValue={form.getInputFields('title').value}
                onChange={form.getInputFields('title').onChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div className="col-span-3 lg:col-span-2">
              <div className="mb-8 rounded-lg border border-gray10">
                <TableOfContents content={blogContent} tableOfContents={blog.tableOfContents} />
              </div>

              <RichEditor
                ref={inserterRef}
                blogLanguage={blog.blogLanguage}
                value={blog.content}
                blog={blog as Blog}
                onChange={form.getInputFields('content').onChange}
                onUpdate={onUpdate}
              />
            </div>
            <div className="relative col-span-3 lg:col-span-1">
              {blog._id && (
                <div
                  className="top-16 max-h-[90vh] overflow-y-auto  rounded-lg bg-white shadow-sm lg:sticky"
                  style={{
                    zIndex: 10, // Ensures it stays above other content
                  }}
                >
                  <SEOScore
                    inserter={inserterRef.current.inserter}
                    blogTone={blog.blogTone as string}
                    blogID={blog._id}
                  />
                </div>
              )}
            </div>
          </div>

          <BlogLinks
            isLoading={isLoading}
            blog={blog}
            setBlog={setBlog}
            onUpdate={onUpdate}
            setFormFieldValue={form.setFieldValue}
          />
          {(formError || form.formValidationError) && (
            <ErrorMessage className="pb-4">{formError || form.formValidationError}</ErrorMessage>
          )}
        </div>
      </DashboardContainer>
    </form>
  );
};

const BlogEditActions = ({
  isSubmitting,
  blogContent,
  form,
}: {
  isSubmitting: boolean;
  blogContent: string;
  form: any;
}) => {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);

  // const undoAllChange = () => {
  //   cacheSet('blog-edit', {});
  //   setBlog(savedBlog as Blog);
  // };

  const removeAffiliateLinks = () => {
    const pattern = /<a.*?utm_source=.*?utm_medium=.*?<\/a>/g;
    // Replace matched patterns with the text content of the link
    const contentWithoutAffiliateLinks = blogContent.replace(pattern, (match) => {
      const tempElement = document.createElement('div');
      tempElement.innerHTML = match;
      return tempElement.textContent || tempElement.innerText || '';
    });
    form.setFieldValue('content', contentWithoutAffiliateLinks);
    toast.success('Removed affiliate links. Please save the blog to apply changes.');
  };

  return (
    <>
      {/* <Button variant="secondary">
      <IoMdEye /> Preview
    </Button> */}
      <Button className="w-36" type="submit" loading={isSubmitting}>
        <IoMdCloudDone /> Save Changes
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="secondary" className="!gap-1">
            <BsThreeDots />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          <DropdownMenuItem onClick={toggleConfirmationDialogOpen}>
            <FaUnlink />
            Remove Affiliate Links
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => copy(getCopyContent(form.values as Blog))}>
            <FiCopy className="mr-2" />
            Copy HTML
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => copy(getCopyContent(form.values as Blog), true)}>
            <FiCopy className="mr-2" />
            Copy Text
          </DropdownMenuItem>
          {/* <DropdownMenuItem  onClick={undoAllChange}>
          <MdUndo />
          Undo All Change
        </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={
          'Are you sure you want to remove affiliate links? This action cannot be undone. You need to save the blog after removing affiliate links.'
        }
        onClose={toggleConfirmationDialogOpen}
        onConfirm={removeAffiliateLinks}
      />
    </>
  );
};

export default BlogEdit;
