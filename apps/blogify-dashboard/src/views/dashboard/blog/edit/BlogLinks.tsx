import type { Blog } from '@ps/types';

import { useEffect, useState } from 'react';
import { BiSolidEditAlt } from 'react-icons/bi';
import { MdDelete } from 'react-icons/md';
import { BsCopy } from 'react-icons/bs';
import toast from 'react-hot-toast';

import { isValidUrl } from '@/utils/validations';
import { copy } from '@/utils/clipboard';
import StateHandler from '@/components/misc/StateHandler';

import { extractLinkUsage, LinkInfo } from './utils';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';

type BlogLinksProps = {
  isLoading: boolean;
  blog: Partial<Blog>;
  setBlog: React.Dispatch<React.SetStateAction<Partial<Blog>>>;
  onUpdate: (content: string) => void;
  setFormFieldValue: (field: string, value: string) => void;
};

function BlogLinks({ isLoading, blog, setBlog, onUpdate, setFormFieldValue }: BlogLinksProps) {
  const [deleteItem, setDeleteItem] = useState<LinkInfo | null>(null);
  const [editItem, setEditItem] = useState<LinkInfo | null>(null);
  const [editedLinkValue, setEditedLinkValue] = useState('');
  const [links, setLinks] = useState<LinkInfo[]>([]);
  const [loadingTime, setLoadingTime] = useState<number | null>(null);

  useEffect(() => {
    if (blog.content) {
      const startTime = performance.now();
      const extractedLinks = extractLinkUsage(blog.content as string);
      const endTime = performance.now();
      setLoadingTime(endTime - startTime);
      setLinks(extractedLinks);
    }
  }, [blog.content]);

  const handleUpdate = (updatedContent: string) => {
    setBlog((prev) => {
      const updated = { ...prev, content: updatedContent };
      setFormFieldValue('content', updatedContent);
      return updated;
    });
    onUpdate(updatedContent);
  };

  const handleDeleteConfirm = async () => {
    if (deleteItem) {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = blog.content || '';
      const anchorTags = tempDiv.getElementsByTagName('a');

      Array.from(anchorTags).forEach((anchor) => {
        if (
          anchor.textContent &&
          anchor.textContent.toLowerCase() === deleteItem.keyword.toLowerCase()
        ) {
          const textNode = document.createTextNode(anchor.textContent);
          if (anchor.parentNode) {
            anchor.parentNode.replaceChild(textNode, anchor);
          }
        }
      });
      const updatedContent = tempDiv.innerHTML;
      handleUpdate(updatedContent);
      setDeleteItem(null);
      toast.success('Link deleted successfully');
    }
  };

  const handleLinkSave = () => {
    if (!isValidUrl(editedLinkValue)) {
      toast.error('Invalid URL');
    } else {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = blog.content || '';
      const anchorTags = tempDiv.getElementsByTagName('a');
      Array.from(anchorTags).forEach((anchor) => {
        if (anchor.href === editItem?.link && anchor.textContent === editItem?.keyword) {
          anchor.href = editedLinkValue;
        }
      });
      const updatedContent = tempDiv.innerHTML;
      handleUpdate(updatedContent);
      toast.success('Link updated successfully');
    }
    setEditItem(null);
    setEditedLinkValue('');
  };

  return (
    <section className="mt-4 rounded-lg border p-8">
      <h2 className="font-semibold">Blog Links</h2>
      <p className="mt-2 text-base ">
        All of your blog links are listed below. You can quickly update or remove them from here.
      </p>

      <DeleteConfirmationDialog
        isOpen={!!deleteItem}
        onClose={() => setDeleteItem(null)}
        deleteItem={deleteItem}
        handleDeleteConfirm={handleDeleteConfirm}
      />

      <div className="w-full overflow-x-auto">
        {isLoading || loadingTime === null || !links.length ? (
          <StateHandler
            loading={isLoading || loadingTime === null}
            isEmpty={!links.length}
            emptyMsg="No links found"
            className="min-h-[30vh]"
          />
        ) : (
          <table className="mt-4 w-full rounded-lg border ">
            <thead>
              <tr className="bg-bg2 text-left">
                <th className="w-[30%] px-3 py-2 text-xs font-medium uppercase text-gray9">
                  Keyword
                </th>
                <th className="w-[55%] px-3 py-2 text-xs font-medium uppercase text-gray9">Link</th>
                <th className="w-[15%] px-3 py-2 text-xs font-medium uppercase text-gray9">
                  Settings
                </th>
              </tr>
            </thead>
            <tbody>
              {links.map((item, index) => (
                <tr key={index} className="*:whitespace-nowrap *:p-3 even:bg-cornflowerBlue">
                  <td className="flex items-center gap-2 text-sm font-medium">
                    <h1 className="text-lg">{item.keyword}</h1>
                    {item.usageCount > 1 && (
                      <div className="rounded border bg-[#f4f1f0]">
                        <p className="px-[3px] text-xs text-[#93766c]">x{item.usageCount}</p>
                      </div>
                    )}
                  </td>
                  <td className="max-w-[300px] truncate text-sm" title={item.link}>
                    {JSON.stringify(editItem) === JSON.stringify(item) ? (
                      <input
                        type="text"
                        className="w-full rounded-lg border border-[#f2470d] p-1 shadow-[0_0_0_3px_rgba(242,71,13,0.1)]"
                        value={editedLinkValue}
                        onChange={(e) => setEditedLinkValue(e.target.value)}
                        onBlur={handleLinkSave}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleLinkSave();
                          }
                        }}
                        autoFocus
                      />
                    ) : (
                      item.link
                    )}
                  </td>
                  <td className="flex gap-2 text-sm">
                    <button
                      type="button"
                      className="rounded-md border p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] hover:bg-gray-100"
                      onClick={() => copy(item.link)}
                    >
                      <BsCopy color="#a8918a" />
                    </button>
                    <button
                      type="button"
                      className="rounded-md border p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] hover:bg-gray-100"
                      onClick={() => {
                        setEditItem(item);
                        setEditedLinkValue(item.link);
                      }}
                    >
                      <BiSolidEditAlt color="#a8918a" />
                    </button>
                    {/*<button
                  type="button"
                  className={`border rounded-md p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] ${item.links.length > 1 ? 'hover:bg-gray-100' : 'opacity-50 cursor-not-allowed'}`}
                  onClick={(e) =>
                    item.links.length > 1 &&
                    handleIconClick(e, () => handleRefreshLink(item.keyword))
                  }
                >
                  <LuRefreshCw color="#a8918a" />
                </button> */}
                    <button
                      type="button"
                      className="rounded-md border p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] hover:bg-gray-100"
                      onClick={() => setDeleteItem(item)}
                    >
                      <MdDelete color="#cc0022" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </section>
  );
}

export default BlogLinks;
