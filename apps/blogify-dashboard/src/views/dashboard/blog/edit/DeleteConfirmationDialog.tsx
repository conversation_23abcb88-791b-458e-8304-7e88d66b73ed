import { MdDelete } from 'react-icons/md';
import { IoClose } from 'react-icons/io5';

import { DialogContent, Dialog } from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';

type PropTypes = {
  isOpen: boolean;
  onClose: () => void;
  deleteItem: any;
  handleDeleteConfirm: () => void;
};

const DeleteConfirmationDialog = ({
  isOpen,
  onClose,
  deleteItem,
  handleDeleteConfirm,
}: PropTypes) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="min-w-fit overflow-hidden p-0">
      <div className="relative z-10 w-[500px] rounded-lg bg-white p-4 shadow-lg">
        <button
          type="button"
          onClick={onClose}
          className="absolute right-4 top-4 rounded-full border border-gray-300 p-1 hover:bg-gray-100"
        >
          <IoClose size={16} />
        </button>

        <div className="flex flex-col flex-wrap">
          <MdDelete size={48} color="#cc0022" />
          <h1 className="mt-4 text-xl font-bold">Delete Link</h1>
          <p className="mt-2 text-base text-gray-600 ">
            You would not be able to restore the links if deleted. Please confirm if you want to
            delete this link for keyword "{deleteItem?.keyword}"
          </p>
          <div className="mt-6 flex gap-4">
            <Button
              type="button"
              className="!bg-red5 hover:!bg-[#aa0022]"
              onClick={handleDeleteConfirm}
            >
              Delete Link
            </Button>
            <Button type="button" variant="secondary" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
);

export default DeleteConfirmationDialog;
