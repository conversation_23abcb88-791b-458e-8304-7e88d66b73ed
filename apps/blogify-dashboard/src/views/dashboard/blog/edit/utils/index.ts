export interface LinkInfo {
  keyword: string;
  link: string;
  usageCount: number;
}

export const extractLinkUsage = (htmlContent: string): LinkInfo[] => {
  const parser = new DOMParser();
  const document = parser.parseFromString(htmlContent, 'text/html');
  const anchorTags = Array.from(document.querySelectorAll('a'));

  const linkUsageMap = new Map<string, LinkInfo>();

  anchorTags.forEach((anchor) => {
    const keyword = anchor.textContent?.trim() || '';
    const link = anchor.href;

    const key = JSON.stringify({ keyword, link });

    if (linkUsageMap.has(key)) {
      linkUsageMap.get(key)!.usageCount += 1;
    } else {
      linkUsageMap.set(key, { keyword, link, usageCount: 1 });
    }
  });

  return Array.from(linkUsageMap.values());
};
