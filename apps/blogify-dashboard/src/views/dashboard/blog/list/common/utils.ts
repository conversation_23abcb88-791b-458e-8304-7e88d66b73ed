import { parseQuery } from '@/utils';

import { AVAILABLE_FILTERS } from '../common/constants';

const hasAppliedFilters = (): boolean => {
  const query = parseQuery();
  return AVAILABLE_FILTERS.some((f) => !!query[f]);
};

const getAppliedFilters = (): Record<string, any> => {
  const query = parseQuery();
  return AVAILABLE_FILTERS.reduce(
    (filters, k) => {
      if (!!query[k]) {
        filters[k] = query[k];
      }
      return filters;
    },
    {} as Record<string, any>
  );
};

export { hasAppliedFilters, getAppliedFilters };
