import type { Integration } from '@/types/misc/integration.type';

import { useNavigate } from 'react-router-dom';
import { MdClose } from 'react-icons/md';

import { INTEGRATION_DISPLAY_NAMES } from '@/constants';
import { removeFromQuery } from '@/services/api/query';

import { getAppliedFilters } from '../common/utils';
import { FILTER_KEY_MAP } from '../common/constants';
import emptyAPIResponse, { APIResponseType } from '@/types/resources';
import { useQuery } from 'react-query';
import { User } from '@ps/types';

const BlogFilterBar = () => {
  const navigate = useNavigate();
  const { data: { data: users } = emptyAPIResponse } = useQuery<APIResponseType<User>>([
    'users',
    { limit: 50 },
  ]);

  const filters = getAppliedFilters();
  delete filters.publishStatus;
  const appliedFilters = Object.keys(filters);

  if (!appliedFilters.length) {
    return null;
  }

  // console.log(appliedFilters);
  // console.log(filters);

  return (
    <div className="mt-4 flex min-h-10 items-center justify-between rounded-lg bg-cornflowerBlue px-4 py-2">
      <div className="flex items-center gap-3">
        <span className="text-md font-medium text-primary">Filter:</span>

        <div className="flex gap-2">
          {appliedFilters.map((k) => (
            <div
              key={k}
              className="flex items-center rounded-md border border-gray10 bg-white p-0.5 pl-2 shadow-xs"
            >
              <span className="text-xs uppercase text-gray9">
                {FILTER_KEY_MAP[k]}
                {k === 'uid'
                  ? ' : ' + users.find((item) => item._id === filters[k])?.name
                  : filters[k].includes(',') && k === 'publishedOn'
                    ? ` : ${(filters[k].split(',') as Integration[])
                        .map((p) => INTEGRATION_DISPLAY_NAMES[p])
                        .join(', ')}`
                    : ` : ${decodeURI(filters[k])}`}
              </span>
              <button
                className="mx-1 text-gray9"
                onClick={() => navigate({ search: removeFromQuery(k) })}
              >
                <MdClose size={13} />
              </button>
            </div>
          ))}
        </div>
      </div>

      <button className="text-sm font-medium text-[#C02]" onClick={() => navigate({ search: '' })}>
        Clear All
      </button>
    </div>
  );
};

export default BlogFilterBar;
