import type { CheckboxProps, CheckedState } from '@ps/ui/components/checkbox';
import type { User } from '@/types/resources/user.type';

import { TbAdjustmentsHorizontal } from 'react-icons/tb';
import { IoMdCloseCircle } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';

import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import {
  Sheet<PERSON>ontent,
  Sheet<PERSON>rigger,
  SheetHeader,
  SheetTitle,
  SheetClose,
  Sheet,
} from '@ps/ui/components/sheet';
import { BLOGS, INTEGRATION_DISPLAY_NAMES } from '@/constants';
import { RadioGroup, RadioGroupItem } from '@ps/ui/components/radio-group';
import { removeFromQuery } from '@/services/api/query';
import { useStoreState } from '@/store';
import { parseQuery } from '@/utils';
import { Checkbox } from '@ps/ui/components/checkbox';
import { UserRole } from '@/types/resources/user.type';
import { cn } from '@ps/ui/lib/utils';
import IntegrationLogo from '@/components/misc/IntegrationLogo';
import Counter from '@/components/misc/Counter';

import useBlogSource from '../../context/useBlogSource';

type Props = { users: User[]; onChange: (f: Record<string, any>) => void };
const BlogFilterSidebar: React.FC<Props> = ({ users, onChange }) => {
  const user = useStoreState((s) => s.user.current);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const { sources } = useBlogSource();
  const navigate = useNavigate();

  const query = parseQuery();
  const { wordCountMax, wordCountMin, publishedOn, sourceName, uid } = query;

  const multiSelectFilter = (field: string, value: string, checked: CheckedState) => {
    setFilters((f) => {
      let newFilter: string[] = [];
      if (Array.isArray(f[field])) {
        newFilter = checked ? [...f[field], value] : f[field].filter((v: string) => v !== value);
      } else {
        newFilter = checked ? [value] : [];
      }
      const allFilter = [...(publishedOn?.split(',') || []), ...newFilter];
      return { ...f, [field]: allFilter };
    });
  };

  const getCounterError = (minCount: number, maxCount: number) => {
    if (!minCount && !maxCount) return null;
    if (minCount && !maxCount) return { max: 'Please insert a max. number' };
    if (!minCount && maxCount) return { min: 'Please insert a min. number' };
    if (minCount > maxCount) return { min: 'Min. number can’t exceed max. number' };
    return null;
  };
  const counterError = getCounterError(filters.wordCountMin, filters.wordCountMax);

  const singleSelectFilter = (field: string, value: string) => {
    setFilters((f) => ({
      ...f,
      [field]: value,
    }));
  };

  return (
    <Sheet
      onOpenChange={(isOpen) => {
        navigate({ search: removeFromQuery('page') });
        if (!isOpen) {
          onChange(filters);
          setFilters({});
        }
      }}
    >
      <SheetTrigger>
        <TbAdjustmentsHorizontal className="relative z-10" size={20} />
      </SheetTrigger>

      <SheetContent className="fixed m-2 h-auto max-h-screen w-5/6 overflow-hidden rounded-xl bg-white p-0 sm:w-3/4">
        <SheetHeader className="h-16 flex-row items-center justify-between bg-bg2 px-6">
          <SheetTitle>Filter</SheetTitle>

          <SheetClose className="absolute right-6 top-[14px]">
            <IoMdCloseCircle className="text-red4" size={20} />
          </SheetClose>
        </SheetHeader>

        <div className="h-full overflow-y-auto p-6 pb-20 font-inter text-black4">
          <section className="mb-10">
            <h4 className="mb-2 text-md font-medium">Blog Source</h4>
            <Select
              onValueChange={(sn) => setFilters((f) => ({ ...f, sourceName: sn }))}
              defaultValue={decodeURI(sourceName || '')}
              name="sourceName"
              required={false}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a source" />
              </SelectTrigger>

              <SelectContent className="w-full">
                <SelectItem value="all">All</SelectItem>
                {sources.map((s) => (
                  <SelectItem key={s.name} value={s.name}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </section>

          {user.role !== UserRole.writer && (
            <section className="mb-10">
              <h4 className="mb-2 text-md font-medium">Author</h4>

              <RadioGroup
                defaultValue={uid}
                onValueChange={(value) => singleSelectFilter('uid', value)}
                className="w-full gap-0"
              >
                {users.map((u, i) => (
                  <RadioWithLabel
                    key={i}
                    value={u._id}
                    label={u.name}
                    isFirst={i === 0}
                    isLast={i === users.length - 1}
                  >
                    <img
                      className="size-6 rounded-full"
                      src={u.profilePicture || `https://ui-avatars.com/api/?name=${u?.name}`}
                      alt={`${u.name}'s profile`}
                    />
                  </RadioWithLabel>
                ))}
              </RadioGroup>

              {/* {users.map((u, i) => (
                <CheckBoxWithLabel
                  key={i}
                  label={u.name}
                  isFirst={i === 0}
                  isLast={i === users.length - 1}
                  defaultChecked={uid?.includes(u._id)}
                  onCheckedChange={(c) => multiSelectFilter('uid', u._id, c)}
                >
                  <img
                    className="size-6 rounded-full"
                    src={u.profilePicture || `https://ui-avatars.com/api/?name=${u?.name}`}
                  />
                </CheckBoxWithLabel>
              ))} */}
            </section>
          )}

          {/* <section className="mb-10">
            <h4 className="mb-2 text-md font-medium">Blog Status</h4>
            <CheckBoxWithLabel
              isFirst
              label="Draft"
              value="draft"
              defaultChecked={publishStatus?.includes('draft')}
              onCheckedChange={(c) => multiSelectFilter('publishStatus', 'draft', c)}
            >
              <BiSolidEditAlt />
            </CheckBoxWithLabel>

            <CheckBoxWithLabel
              label="Scheduled"
              value="scheduled"
              defaultChecked={publishStatus?.includes('scheduled')}
              onCheckedChange={(c) => multiSelectFilter('publishStatus', 'scheduled', c)}
            >
              <FaClockRotateLeft />
            </CheckBoxWithLabel>

            <CheckBoxWithLabel
              isLast
              label="Published"
              value="published"
              defaultChecked={publishStatus?.includes('published')}
              onCheckedChange={(c) => multiSelectFilter('publishStatus', 'published', c)}
            >
              <FaTelegramPlane />
            </CheckBoxWithLabel>
          </section> */}

          <section className="mb-10">
            <h4 className="mb-2 text-md font-medium">Word Count</h4>
            <div className="flex justify-between gap-4">
              <Counter
                min={0}
                step={100}
                defaultValue={parseInt(wordCountMin, 10)}
                onChange={(c) => setFilters((f) => ({ ...f, wordCountMin: c }))}
                placeholder="min"
                className={counterError?.min ? 'border-red4' : ''}
              />
              <Counter
                min={0}
                step={100}
                defaultValue={parseInt(wordCountMax, 10)}
                onChange={(c) => setFilters((f) => ({ ...f, wordCountMax: c }))}
                placeholder="max"
                className={counterError?.max ? 'border-red4' : ''}
              />
            </div>
            {counterError && (
              <p className="mt-1 text-sm text-red4">{counterError?.min ?? counterError?.max}</p>
            )}
          </section>

          <section className="mb-10">
            <h4 className="mb-2 text-md font-medium">Published To</h4>
            {BLOGS.map((b, i) => (
              <CheckBoxWithLabel
                key={b}
                isFirst={i === 0}
                isLast={i === BLOGS.length - 1}
                label={INTEGRATION_DISPLAY_NAMES[b]}
                defaultChecked={publishedOn?.includes(b)}
                onCheckedChange={(c) => multiSelectFilter('publishedOn', b, c)}
              >
                <IntegrationLogo integration={b} />
              </CheckBoxWithLabel>
            ))}
          </section>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const CheckBoxWithLabel = ({
  label,
  isFirst,
  isLast,
  children,
  ...props
}: {
  label: string;
  children: React.ReactNode;
  isFirst?: boolean;
  isLast?: boolean;
} & CheckboxProps) => (
  <label
    className={cn(`flex h-10 items-center justify-between border-x border-b border-gray10 px-2.5`, {
      'rounded-t-lg border-t': isFirst,
      'rounded-b-lg border-b': isLast,
    })}
  >
    <div className="flex gap-2.5 flex-center">
      {children}
      <span className="text-md font-medium">{label}</span>
    </div>
    <Checkbox {...props} />
  </label>
);

const RadioWithLabel = ({
  label,
  value,
  isFirst,
  isLast,
  children,
}: {
  label: string;
  value: string;
  children: React.ReactNode;
  isFirst?: boolean;
  isLast?: boolean;
}) => (
  <label
    className={cn(`flex h-10 items-center justify-between border-x border-b border-gray10 px-2.5`, {
      'rounded-t-lg border-t': isFirst,
      'rounded-b-lg border-b': isLast,
    })}
  >
    <div className="flex items-center gap-2.5">
      {children}
      <span className="text-md font-medium">{label}</span>
    </div>
    <RadioGroupItem value={value} />
  </label>
);

export default BlogFilterSidebar;
