import type { APIResponseType } from '@/types/resources';
import type { User } from '@/types/resources/user.type';

import { useEffect, useState, useRef } from 'react';
import { IoMdCloseCircle } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';
import { FaSearch } from 'react-icons/fa';
import { useQuery } from 'react-query';
import { debounce } from 'lodash';

import { appendToQuery } from '@/services/api/query';
import { parseQuery } from '@/utils';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';

import { getAppliedFilters, hasAppliedFilters } from '../common/utils';
import BlogFilterSidebar from './BlogFilterSidebar';

const BlogSearchAndFilter = () => {
  const navigate = useNavigate();

  const { data: { data: users } = emptyAPIResponse } = useQuery<APIResponseType<User>>([
    'users',
    { limit: 50 },
  ]);

  return (
    <div className="flex items-end gap-3 border-b border-bg2 py-[19px] pl-2">
      <BlogSearchBar onChange={debounce((q) => navigate({ search: appendToQuery({ q }) }), 500)} />
      <BlogFilterSidebar
        users={users}
        onChange={(filters) => navigate({ search: appendToQuery(filters) })}
      />
    </div>
  );
};

const BlogSearchBar: React.FC<{ onChange: (q: string) => void }> = ({ onChange }) => {
  const [searchVisible, toggleSearch] = useToggle(false);
  const [q, setSearchQuery] = useState<string>(parseQuery().q || '');
  const ref = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const hasFilters = hasAppliedFilters();
  const filters = getAppliedFilters();

  useEffect(() => {
    // toggleSearch(hasFilters);
    if (!hasFilters) {
      setSearchQuery('');
    }
  }, [hasFilters]);

  useEffect(() => {
    if (filters.q === undefined) setSearchQuery('');
  }, [filters.q]);

  return (
    <div className="flex overflow-hidden">
      <div
        className={cn(
          `absolute -top-2.5 left-0 flex w-[calc(100%-28px)] items-center border-b border-bg2 bg-white pb-3 pt-5 transition-all duration-300`,
          {
            'invisible animate-out fade-out-0 slide-out-to-right-10': !searchVisible,
            'animate-in fade-in-0 slide-in-from-right-10': searchVisible,
          }
        )}
      >
        <FaSearch className="text-primary" size={20} />
        <input
          ref={ref}
          autoFocus
          className="w-11/12 text-sm font-semibold placeholder:font-normal placeholder:text-gray9"
          placeholder="Type keywords from your blog or source title..."
          value={q}
          onChange={(ev) => {
            setSearchQuery(ev.target.value);
            onChange(ev.target.value);
          }}
          onBlur={() => {
            if (!q && !hasFilters) toggleSearch(false);
          }}
        />

        {!!q && (
          <button
            className="absolute right-2"
            onClick={() => {
              if (!hasFilters) toggleSearch();
              setSearchQuery('');
              onChange('');
            }}
          >
            <IoMdCloseCircle className="text-red4" size={20} />
          </button>
        )}
      </div>

      <button
        className={cn({ invisible: searchVisible })}
        onClick={() => {
          navigate({ search: '' });
          toggleSearch();
          setTimeout(() => {
            if (ref.current) ref.current.focus();
          }, 100);
        }}
      >
        <FaSearch size={20} />
      </button>
    </div>
  );
};

export default BlogSearchAndFilter;
