import type { APIResponseType } from '@/types/resources';
import type { Blog } from '@ps/types';
import type { Plan } from '@/types/misc/plan.type';

import { useEffect, useState } from 'react';
import { HiOutlineLightBulb } from 'react-icons/hi';
import { useNavigate, Link } from 'react-router-dom';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';

import { Tabs<PERSON>ontent, TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { useInitialUserTourGuideContext } from '@/context/InitialUserTourGuide';
import { appendToQuery } from '@/services/api/query';
import { useStoreState } from '@/store';
import { parseQuery } from '@/utils';
import { UserRole } from '@/types/resources/user.type';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import emptyAPIResponse from '@/types/resources';
import FreeTrialDialog from '@/components/dialog/FreeTrialDialog';
import Pagination from '@ps/ui/components/pagination';

import { getAppliedFilters } from './common/utils';
import { BlogNoEntries } from '../components/BlogLayout';
import BlogSearchAndFilter from './components/BlogSearchAndFilter';
import DashboardContainer from '../../layout/DashboardContainer';
import BlogFilterBar from './components/BlogFilterBar';
import BlogCard from '../components/BlogCard';
export type TabType = { name: string; status: Blog['publishStatus'] | '' };

const TABS: TabType[] = [
  { name: 'All Blogs', status: '' },
  { name: 'Drafts', status: 'draft' },
  { name: 'Scheduled', status: 'scheduled' },
  { name: 'Published', status: 'published' },
];

const BlogList = () => {
  const { limit = '10', page = '1', publishStatus } = parseQuery();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [upgrading, toggleUpgrading] = useToggle(false);
  const [plan, setPlan] = useState<Plan>();
  const user = useStoreState((s) => s.user.current);
  const navigate = useNavigate();

  //NOTE: Find subscription plan name and credits number
  const currentPackage = plan?.packages.find((pkg) => pkg.name == plan.subscriptionPlan);
  const totalCreditsNumber = currentPackage?.limit?.CREDITS;
  const gotoBlogCreate = () => {
    const difference: number | undefined =
      totalCreditsNumber !== undefined ? totalCreditsNumber - user.credits : undefined;

    if (plan?.subscriptionStatus === 'trial' && difference !== undefined) {
      if (difference >= 50) {
        setShowUpgradeDialog(true);
      } else {
        navigate('./select-source');
      }
    } else {
      navigate('./select-source');
    }
  };

  const {
    data: { data: blogs, total } = emptyAPIResponse,
    isFetching,
    refetch,
  } = useQuery<APIResponseType<Blog>>([
    'blogs',
    {
      ...getAppliedFilters(),
      limit,
      page,
      ...(user.role === UserRole.writer && { uid: user._id }),
    },
  ]);

  const defaultBlogCounts = { draft: 0, scheduled: 0, published: 0, total: 0 };
  const { data: blogCounts = defaultBlogCounts } = useQuery<
    Record<Blog['publishStatus'] | 'total', number>
  >(['blogs/counts', { ...(user.role === UserRole.writer && { uid: user._id }) }]);

  const fetchPlans = async () => {
    API.fetch<Plan>('payments/plans').then((data: Plan | void) => setPlan(data as Plan));
  };
  useEffect(() => {
    fetchPlans();
  }, []);

  // Initial user tour guide
  const {
    setState,
    state: { tourActive },
  } = useInitialUserTourGuideContext();

  useEffect(() => {
    if (tourActive && !!blogs.length) {
      setState({ run: true, stepIndex: 4 });
    }
  }, [blogs.length, setState, tourActive]);

  const selectedTab = TABS.find((t) => t.status === publishStatus)?.name || 'All Blogs';

  return (
    <DashboardContainer
      title="My Blogs"
      actions={
        <>
          {user?.email?.endsWith('@blogify.ai') && (
            <Link to="/dashboard/blog-ideas">
              <Button variant="secondary">
                <HiOutlineLightBulb />
                Get Ideas
              </Button>
            </Link>
          )}

          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <Button className="userTour-create-blog" onClick={gotoBlogCreate}>
            <img className="size-4" src={`/images/icons/stars-white.svg`} alt="Stars Icon" />
            {!upgrading ? 'Create New Blog' : 'Upgrading...'}
          </Button>
        </>
      }
    >
      <Tabs defaultValue={selectedTab} value={selectedTab}>
        <div className="relative flex items-center justify-between">
          <TabsList variant="nav">
            {TABS.map((t) => (
              <TabsTrigger
                key={t.name}
                variant="nav"
                value={t.name}
                onClick={() =>
                  navigate({ search: appendToQuery({ publishStatus: t.status }, true) })
                }
              >
                {t.name}{' '}
                {blogCounts[t.status || 'total'] ? `(${blogCounts[t.status || 'total']})` : ''}
              </TabsTrigger>
            ))}
          </TabsList>
          <BlogSearchAndFilter />
        </div>

        <BlogFilterBar />

        {TABS.map((t) => (
          <TabsContent value={t.name} key={t.name}>
            {!blogs.length ? (
              <BlogNoEntries loading={isFetching} />
            ) : (
              <>
                {blogs.map((blog) => (
                  <BlogCard key={blog._id} blog={blog} refetch={refetch} />
                ))}
                <Pagination
                  className="mt-6"
                  onPaging={(url) => navigate(url)}
                  limit={parseInt(limit, 10)}
                  page={parseInt(page, 10)}
                  total={total}
                />
              </>
            )}
          </TabsContent>
        ))}
      </Tabs>

      <FreeTrialDialog
        isOpen={showUpgradeDialog}
        toggleUpgrading={toggleUpgrading}
        onClose={() => setShowUpgradeDialog(false)}
      />
    </DashboardContainer>
  );
};

export default BlogList;
