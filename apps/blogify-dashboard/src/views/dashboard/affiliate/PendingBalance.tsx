import { useQuery } from 'react-query';
import { Link, useNavigate } from 'react-router-dom';

import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import StatsBox from '@/components/misc/StatsBox';

import PendingBalanceTable from './components/PendingBalanceTable';
import { parseQuery } from '@/utils';
import emptyAPIResponse from '@/types/resources';
import { AffiliateLinkTracking, APIResponse } from '@ps/types';
import Pagination from '@ps/ui/components/pagination';
import PayoutRequestDialog from '../wallet/components/PayoutRequestDialog';
import { useToggle } from 'react-use';
import { HiDotsHorizontal } from 'react-icons/hi';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ps/ui/components/dropdown-menu';

const ContextMenu = ({ navigate }: { navigate: any }) => {
  const menu = {
    'Affiliate Analytics': '/dashboard/analytics',
    'Active Links': '/dashboard/affiliate/links',
    'Active Brands': '/dashboard/affiliate/brands',
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" className="px-2.5">
          <HiDotsHorizontal />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {Object.entries(menu).map(([label, url], index) => (
          <DropdownMenuItem key={index} onClick={() => navigate(url)}>
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default function PendingBalance() {
  const navigate = useNavigate();
  const [isPayoutDialogOpen, togglePayoutDialog] = useToggle(false);

  const { data: stats = {} } = useQuery<Record<string, number>>([
    'affiliate-link-trackings/pending-balance/stats',
  ]);

  const { limit = '10', page = '1' } = parseQuery();

  const { data: { data: affiliateLinkTrackings, total } = emptyAPIResponse, isLoading } = useQuery<
    APIResponse<AffiliateLinkTracking>
  >([`affiliate-link-trackings/pending-balance?page=${page}&limit=${limit}`]);

  return (
    <DashboardContainer
      title="Payable"
      actions={
        <>
          <Link to="/dashboard/wallet">
            <Button variant="secondary">Wallet</Button>
          </Link>
          <Button onClick={togglePayoutDialog}>Request Payout</Button>
          <ContextMenu navigate={navigate} />
        </>
      }
    >
      <PayoutRequestDialog isOpen={isPayoutDialogOpen} onClose={togglePayoutDialog} />

      <StatsBox
        stats={[
          { name: 'Total Payable', amount: stats.totalPayable, type: 'currency' },
          { name: 'In Review', amount: stats.inReview, type: 'currency' },
          { name: 'Approved', amount: stats.approved, type: 'currency' },
        ]}
      />

      <PendingBalanceTable
        affiliateLinkTrackings={affiliateLinkTrackings}
        total={total}
        isLoading={isLoading}
      />

      <Pagination
        className="mt-6"
        onPaging={(url) => navigate(url)}
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
      />
    </DashboardContainer>
  );
}
