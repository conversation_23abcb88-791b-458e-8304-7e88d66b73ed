// بسم الله الرحمن الرحيم
import type { AffiliateBrand, APIResponse } from '@ps/types';

import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';

import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import emptyAPIResponse from '@/types/resources';
import StateHandler from '@/components/misc/StateHandler';
import Pagination from '@ps/ui/components/pagination';

import BrandsTable from './components/BrandsTable';
import { useToggle } from 'react-use';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ps/ui/components/dropdown-menu';
import { HiDotsHorizontal } from 'react-icons/hi';
import PayoutRequestDialog from '../wallet/components/PayoutRequestDialog';

const ContextMenu = ({ navigate }: { navigate: any }) => {
  const [isPayoutDialogOpen, togglePayoutDialog] = useToggle(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="secondary" className="px-2.5">
            <HiDotsHorizontal />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => navigate('/dashboard/affiliate/pending-balance')}>
            Payables
          </DropdownMenuItem>
          <DropdownMenuItem onClick={togglePayoutDialog}>Request Payout</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <PayoutRequestDialog isOpen={isPayoutDialogOpen} onClose={togglePayoutDialog} />
    </>
  );
};

export default function Brands() {
  const { limit = '20', page = '1' } = parseQuery();
  const navigate = useNavigate();

  const {
    data: { data, total } = emptyAPIResponse,
    isLoading,
    error,
  } = useQuery<APIResponse<AffiliateBrand>>(
    `/affiliate-link-trackings/brands?limit=${limit}&page=${page}`
  );
  const brands = data;

  return (
    <DashboardContainer
      title="Active Brands"
      actions={
        <>
          <Link to="/dashboard/affiliate">
            <Button variant="secondary">Affiliate Analytics</Button>
          </Link>
          <Link to="/dashboard/affiliate/links">
            <Button variant="secondary">Active Links</Button>
          </Link>
          <ContextMenu navigate={navigate} />
        </>
      }
    >
      {isLoading || error || !brands?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!brands?.length}
          emptyMsg="No active brand found."
        />
      ) : (
        <>
          <BrandsTable data={brands} />
          <Pagination
            className="mt-6"
            onPaging={(url) => navigate(url)}
            limit={parseInt(limit, 10)}
            page={parseInt(page, 10)}
            total={total}
          />
        </>
      )}
    </DashboardContainer>
  );
}
