// بسم الله الرحمن الرحيم
import type {
  AffiliateBrand,
  AffiliateLink,
  AffiliateLinkTracking,
  AffiliateStats,
} from '@ps/types';

import { ComponentProps, useState } from 'react';
import { useQuery } from 'react-query';
import { Link, useNavigate } from 'react-router-dom';
import StateHandler from '@/components/misc/StateHandler';
import { useStoreState } from '@/store';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import { APIResponse, defaultAffiliateStats } from '@ps/types';
import { Button } from '@ps/ui/components/button';
import BrandsTable from './components/BrandsTable';
import PayoutRequestDialog from '@/views/dashboard/wallet/components/PayoutRequestDialog';
import AffiliateTerms from './AffiliateTerms';
import DataCard from './components/DataCard';
import LinksTable from './components/LinksTable';
import Performance from './components/Performance';
import BlogAnalyticsWrapper from './components/BlogAnalyticsWrapper';
import emptyAPIResponse from '@/types/resources';
import PendingBalanceTable from './components/PendingBalanceTable';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ps/ui/components/dropdown-menu';
import { HiDotsHorizontal } from 'react-icons/hi';

function DataCards({ data }: { data: ComponentProps<typeof DataCard>[] }) {
  return (
    <div className="grid grid-cols-2 gap-4 *:w-full sm:grid-cols-2 lg:grid-cols-4">
      {data.map((props, i) => (
        <DataCard key={props.title + i} {...props} />
      ))}
    </div>
  );
}

function Payables() {
  const {
    data: { data: affiliateLinkTrackings, total } = emptyAPIResponse,
    isLoading,
    error,
  } = useQuery<APIResponse<AffiliateLinkTracking>>([
    `affiliate-link-trackings/pending-balance?page=${1}&limit=${10}`,
  ]);

  return (
    <article className="space-y-4 rounded-lg border border-gray10 ">
      <div className="flex justify-between gap-2 p-4">
        <div>
          <div className="text-lg font-semibold">Payables</div>
          <div className="mt-2 h-[18px] w-[688px] grow-0  text-left text-15 font-normal text-black4">
            List of actions or sells came from your blog.
          </div>
        </div>
        <Link to="/dashboard/affiliate/pending-balance">
          <Button variant="secondary" className="text-sm">
            See all Payables
          </Button>
        </Link>
      </div>

      {isLoading || error || !affiliateLinkTrackings?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!affiliateLinkTrackings?.length}
          className="!min-h-[30vh]"
        />
      ) : (
        <PendingBalanceTable
          affiliateLinkTrackings={affiliateLinkTrackings}
          total={total}
          isLoading={isLoading}
          containerClass="border-none rounded-none"
        />
      )}
    </article>
  );
}

function ActiveLinks() {
  const { data, isLoading, error } = useQuery<APIResponse<AffiliateLink>>(
    '/affiliate-link-trackings/active?limit=10&page=1'
  );
  const AffiliateLinkTrackings = data?.data;

  return (
    <article className="space-y-4 rounded-lg border border-gray10 ">
      <div className="flex justify-between gap-2 p-4">
        <div>
          <div className="text-lg font-semibold">Active Links</div>
          <div className="mt-2 h-[18px] w-[688px] grow-0  text-left text-15 font-normal text-black4">
            List of links that you have used in your blog.
          </div>
        </div>
        <Link to="/dashboard/affiliate/links">
          <Button variant="secondary" className="text-sm">
            See all Active Links
          </Button>
        </Link>
      </div>

      {isLoading || error || !AffiliateLinkTrackings?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!AffiliateLinkTrackings?.length}
          emptyMsg="No active link found."
          className="!min-h-[40vh]"
        />
      ) : (
        <LinksTable data={AffiliateLinkTrackings} />
      )}
    </article>
  );
}

function ActiveBrands() {
  const {
    data: { data } = emptyAPIResponse,
    isLoading,
    error,
  } = useQuery<APIResponse<AffiliateBrand>>('/affiliate-link-trackings/brands?limit=10&page=1');
  const brands = data;

  return (
    <article className="space-y-4 rounded-lg border border-gray10 ">
      <div className="flex flex-col justify-between gap-4 p-4 sm:flex-row sm:items-center sm:gap-2">
        <div className="flex-1">
          <div className="text-lg font-semibold">Active Brands</div>
          <div className="mt-2 text-left text-sm font-normal text-black4 sm:text-base">
            List of brands whose link you have used in your blog.
          </div>
        </div>

        <Link to="/dashboard/affiliate/brands" className="flex-none">
          <Button variant="secondary" className="w-full text-sm sm:w-auto">
            See all Active Brands
          </Button>
        </Link>
      </div>

      {isLoading || error || !brands?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!brands?.length}
          emptyMsg="No active brands found."
          className="!min-h-[40vh]"
        />
      ) : (
        <BrandsTable data={brands} />
      )}
    </article>
  );
}

const ContextMenu = ({ navigate }: { navigate: any }) => {
  const menu = {
    Payables: '/dashboard/affiliate/pending-balance',
    'Active Links': '/dashboard/affiliate/links',
    'Active Brands': '/dashboard/affiliate/brands',
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" className="px-2.5">
          <HiDotsHorizontal />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {Object.entries(menu).map(([label, url], index) => (
          <DropdownMenuItem key={index} onClick={() => navigate(url)}>
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default function Affiliate() {
  const [isPayoutModalOpen, setIsPayoutModalOpen] = useState(false);
  const { acceptedAffiliateTerms, ...user } = useStoreState((s) => s.user.current);
  const { data: affiliateStats = defaultAffiliateStats } =
    useQuery<AffiliateStats>('/affiliate/stats');
  const navigate = useNavigate();

  const dataCardsData = [
    {
      title: "Today's Earnings",
      value: `$${affiliateStats.earnings.today.toFixed(2)}`,
      info: `Total Earnings: $${affiliateStats.earnings.total.toFixed(2)}`,
    },
    {
      title: 'Active Links',
      value: affiliateStats.links.active.toString(),
      info: `Total Links: ${affiliateStats.links.total}`,
    },
    {
      title: 'Pending Balance',
      value: '$' + affiliateStats.balance.pending.toFixed(2),
      info: `Total Links: ${affiliateStats.links.total}`,
    },
    {
      title: 'Balance',
      value: `$${affiliateStats.balance.current.toFixed(2)}`,
      info: `Last Payout: $${affiliateStats.balance.lastPayout.toFixed(2)}`,
    },
  ];

  return (
    <DashboardContainer
      className="flex flex-col gap-5 overflow-hidden"
      title="Affiliate"
      actions={
        <>
          {acceptedAffiliateTerms ? (
            <Button key="payout" onClick={() => setIsPayoutModalOpen(true)}>
              Request Payout
            </Button>
          ) : null}
          <ContextMenu navigate={navigate} />
        </>
      }
    >
      {!user._id ? (
        <></>
      ) : acceptedAffiliateTerms ? (
        <>
          <PayoutRequestDialog
            isOpen={isPayoutModalOpen}
            onClose={() => setIsPayoutModalOpen(false)}
          />
          <DataCards data={dataCardsData} />
          <Performance />

          <Payables />
          <ActiveLinks />
          <ActiveBrands />
          <BlogAnalyticsWrapper />
        </>
      ) : (
        <AffiliateTerms />
      )}
    </DashboardContainer>
  );
}
