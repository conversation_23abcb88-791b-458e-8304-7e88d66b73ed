// بسم الله الرحمن الرحيم

import { APIResponse, type AffiliateLink } from '@ps/types';

import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';

import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import StateHandler from '@/components/misc/StateHandler';
import Pagination from '@ps/ui/components/pagination';

import LinksTable from './components/LinksTable';
import emptyAPIResponse from '@/types/resources';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ps/ui/components/dropdown-menu';
import { HiDotsHorizontal } from 'react-icons/hi';
import { useToggle } from 'react-use';
import PayoutRequestDialog from '../wallet/components/PayoutRequestDialog';

const ContextMenu = ({ navigate }: { navigate: any }) => {
  const [isPayoutDialogOpen, togglePayoutDialog] = useToggle(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="secondary" className="px-2.5">
            <HiDotsHorizontal />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => navigate('/dashboard/affiliate/pending-balance')}>
            Payables
          </DropdownMenuItem>
          <DropdownMenuItem onClick={togglePayoutDialog}>Request Payout</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <PayoutRequestDialog isOpen={isPayoutDialogOpen} onClose={togglePayoutDialog} />
    </>
  );
};

export default function Links() {
  const { limit = '20', page = '1' } = parseQuery();
  const navigate = useNavigate();

  const {
    data: { data, total } = emptyAPIResponse,
    isLoading,
    error,
  } = useQuery<APIResponse<AffiliateLink>>(
    `affiliate-link-trackings/active?limit=${limit}&page=${page}`
  );
  const AffiliateLinkTrackings = data;

  return (
    <DashboardContainer
      title="Active Links"
      actions={
        <>
          <Link to="/dashboard/affiliate">
            <Button variant="secondary">Affiliate Analytics</Button>
          </Link>
          <Link to="/dashboard/affiliate/brands">
            <Button variant="secondary">Active Brands</Button>
          </Link>
          <ContextMenu navigate={navigate} />
        </>
      }
    >
      {isLoading || error || !AffiliateLinkTrackings?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!AffiliateLinkTrackings?.length}
          emptyMsg="No active link found."
        />
      ) : (
        <>
          <LinksTable data={AffiliateLinkTrackings} />
          <Pagination
            className="mt-6"
            onPaging={(url) => navigate(url)}
            limit={parseInt(limit, 10)}
            page={parseInt(page, 10)}
            total={total}
          />
        </>
      )}
    </DashboardContainer>
  );
}
