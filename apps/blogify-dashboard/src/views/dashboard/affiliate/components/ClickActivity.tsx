// import { MdAdsClick } from 'react-icons/md';

// export default function ClickActivity({
//   count,
// }: {
//   count: { clicks: number; actions: number; cr: number };
// }) {
//   return (
//     <article className="flex w-full flex-col  p-4 gap-3 border-x-0 border border-gray10">
//       <span className="font-medium">Activity</span>
//       <div className="relative flex items-center justify-center self-center w-32 h-32 rounded-full ">
//         <div className="absolute inset-0 rounded-full custom-circle-border3"></div>
//         <div className="absolute w-24 h-24 rounded-full custom-circle-border2 "></div>
//         <div className="absolute w-16 h-16 rounded-full custom-circle-border1"></div>
//         <MdAdsClick className=" text-4xl text-primary" />
//       </div>
//       <span className="flex justify-between gap-2">
//         <div className="flex flex-col gap-2">
//           <span className="text-sm font-medium">Clicks</span>
//           <span className="text-2xl font-semibold">{count.clicks}</span>
//         </div>
//         <div className="flex flex-col gap-2">
//           <span className="text-sm font-medium">Actions</span>
//           <span className="text-2xl font-semibold">{count.actions}</span>
//         </div>
//       </span>
//       <span className="text-center text-gray9">
//         Conversion Rate: {count.cr.toLocaleString(undefined, { style: 'percent' })}
//       </span>
//     </article>
//   );
// }
