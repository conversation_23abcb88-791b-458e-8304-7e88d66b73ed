import type { AffiliateLink } from '@ps/types';

import { HiDotsVertical } from 'react-icons/hi';
import { useState } from 'react';
import { IoImage } from 'react-icons/io5';
import toast from 'react-hot-toast';
import { calculateMetrics } from '../utils';
import ProductDialog from './dialogs/ProductDialog';
import { useToggle } from 'react-use';

export default function LinksTable({ data }: { data: AffiliateLink[] }) {
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  const [dialogOpen, toggleDialogOpen] = useToggle(false);
  const [selectedProduct, setSelectedProduct] = useState<AffiliateLink | null>(null);

  const handleDropdownToggle = (id: string) => {
    setDropdownOpen(dropdownOpen === id ? null : id);
  };

  const handleViewDetails = (datum: AffiliateLink) => {
    setSelectedProduct(datum);
    toggleDialogOpen(true);
    setDropdownOpen(null); // Close the dropdown after clicking
  };

  const handleCopyLink = (datum: AffiliateLink) => {
    navigator.clipboard.writeText(datum.trackingLink);
    toast.success('Affiliate link copied!');
    setDropdownOpen(null); // Close the dropdown after copying the link
  };

  const handleViewProductPage = (datum: AffiliateLink) => {
    window.open(datum.trackingLink, '_blank');
    setDropdownOpen(null); // Close the dropdown after opening the product page
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[600px] table-auto text-sm text-gray-700">
        <thead className="h-6 bg-bg2">
          <tr className="text-left text-xs uppercase text-gray9 *:p-2">
            <th className="px-2 font-medium">Product</th>
            <th className="px-2 font-medium">Link</th>
            <th className="px-2 text-right font-medium">Clicks</th>
            <th className="px-2 text-right font-medium">Actions</th>
            <th className="px-2 text-right font-medium">Earnings</th>
            <th className="px-2 text-right font-medium">EPC</th>
            <th className="px-2 text-right font-medium">EPS</th>
            <th className="px-2 text-center font-medium">CR</th>
            <th className="px-2 text-center font-medium"></th>
          </tr>
        </thead>
        <tbody>
          {data.map((datum) => {
            const { EPC, EPS, CR } = calculateMetrics(datum);
            return (
              <tr key={datum._id} className="even:bg-gray-50">
                <td
                  className="flex cursor-pointer gap-2 p-2 hover:underline"
                  onClick={() => handleViewDetails(datum)}
                >
                  {datum.productImageUrl ? (
                    <img
                      src={datum.productImageUrl}
                      width={20}
                      height={20}
                      className="size-5 rounded-lg"
                      alt={`${datum.productName} icon`}
                    />
                  ) : (
                    <IoImage className="size-5 rounded-lg" />
                  )}
                  <span className="max-w-[150px] truncate font-semibold">{datum.productName}</span>
                </td>
                <td className="max-w-[150px] truncate p-2">{datum._id}</td>
                <td className="px-4 text-right">{datum.totalClickCount}</td>
                <td className="p-2 text-right">{datum.totalActionCount}</td>
                <td className="p-2 text-right">
                  {datum.totalEarnings.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 3,
                  })}
                </td>
                <td className="p-2 text-right">
                  {EPC.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 4,
                  })}
                </td>
                <td className="p-2 text-right">
                  {EPS.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 3,
                  })}
                </td>
                <td className="p-2 text-center">
                  {CR.toLocaleString(undefined, { style: 'percent', minimumFractionDigits: 2 })}
                </td>
                <td className="relative p-2">
                  <HiDotsVertical
                    className="cursor-pointer text-gray-600"
                    onClick={() => handleDropdownToggle(datum._id)}
                  />
                  {dropdownOpen === datum._id && (
                    <div className="absolute right-0 z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
                      <ul className="text-sm text-gray-700">
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleViewDetails(datum)}
                        >
                          View Details
                        </li>
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleViewProductPage(datum)}
                        >
                          View Product Page
                        </li>
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleCopyLink(datum)}
                        >
                          Copy Affiliate Link
                        </li>
                      </ul>
                    </div>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
      <ProductDialog
        isOpen={dialogOpen}
        onClose={toggleDialogOpen}
        selectedProduct={selectedProduct}
      />
    </div>
  );
}
