import { DateSelector } from '@/components/commonActivity';
import { PeriodType } from '@/views/dashboard/analytics/Analytics';
import { LineChart } from '@mui/x-charts/LineChart';
import dayjs from 'dayjs';
import { range } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import './index.css';

export default function SeriesChart() {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(0);
  const [period, setPeriod] = useState<PeriodType>('Today');
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);

  // Adjust chart width when the container resizes
  useEffect(() => {
    const updateWidth = () => {
      if (chartContainerRef.current) {
        setChartWidth(chartContainerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  return (
    <article className="rounded border border-gray-300 ">
      <div className="p-4">
        <span className="mb-4 block text-lg font-semibold ">Performance</span>
        <DateSelector
          selectedRange={period}
          onRangeChange={setPeriod}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </div>
      <div
        ref={chartContainerRef}
        className="w-full overflow-x-hidden"
        style={{ position: 'relative' }}
      >
        {chartWidth > 0 && (
          <LineChart
            width={chartWidth} // Dynamic width
            height={300}
            dataset={range(1, 25).map((x) => ({
              x: x + 1,
              y: Math.floor(Math.random() * 10) + 1,
              z: Math.floor(Math.random() * 50) + 1,
            }))}
            xAxis={[
              {
                dataKey: 'x',
                tickNumber: 12,
                disableLine: true,
                disableTicks: true,
              },
            ]}
            yAxis={[
              { disableLine: true, disableTicks: true }, // Remove y-axis line and ticks
            ]}
            series={[
              {
                curve: 'linear',
                dataKey: 'y',
                color: '#337DFF',
                showMark: false,
              },
              {
                curve: 'linear',
                dataKey: 'z',
                color: '#FF5733',
                showMark: false,
              },
            ]}
            grid={{ vertical: true, horizontal: true }}
          />
        )}
      </div>
      <div className="mt-2 flex flex-wrap justify-start gap-16 px-4">
        {Object.entries({
          clicks: 100,
          actions: 17,
          earnings: '$9.00',
          epc: '$0.08',
          eps: '$0.05',
          'conversion rate': '17%',
        }).map(([name, value]) => (
          <div key={name} className="mb-4 w-1/3 text-center sm:w-auto">
            <span className="text-sm font-medium capitalize">{name}</span>
            <br />
            <span className="text-lg font-semibold">{value}</span>
          </div>
        ))}
      </div>
    </article>
  );
}
