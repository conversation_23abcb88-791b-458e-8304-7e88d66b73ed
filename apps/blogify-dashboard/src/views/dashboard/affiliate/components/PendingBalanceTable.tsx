import type { AffiliateLinkTracking } from '@ps/types';

import { Link } from 'react-router-dom';
import { MdMoreVert } from 'react-icons/md';
import toast from 'react-hot-toast';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { cn } from '@ps/ui/lib/utils';
import { useState } from 'react';
import ProductDialog from './dialogs/ProductDialog';
import { useToggle } from 'react-use';
import { IoImage } from 'react-icons/io5';

const getStatusColors = (status: AffiliateLinkTracking['conversionStatus']): string => {
  switch (status) {
    // REVIEWING
    case 'PENDING':
    case 'PROCESSING':
      return 'text-[#804e9c] bg-[#804e9c]/10';
    // APPROVED
    case 'APPROVED':
      return 'text-[#088c90] bg-[#088c90]/10';
    // PROCESSING
    case 'PAID':
      return 'text-[#879f00] bg-[#879f00]/10';
    // PAID
    case 'CREDITED':
      return 'text-[#093] bg-[#093]/10';
    // DENIED/EXPIRED
    case 'REJECTED':
    case 'REVERSED':
    case 'EXPIRED':
      return 'text-[#c8223d] bg-[#c8223d]/10';
  }
  return '';
};

const getStatusDisplay = (status: AffiliateLinkTracking['conversionStatus']): string => {
  switch (status) {
    case 'PENDING':
    case 'PROCESSING':
      return 'REVIEWING';
    case 'APPROVED':
      return 'APPROVED';
    case 'PAID':
      return 'PROCESSING';
    case 'CREDITED':
      return 'PAID';
    case 'REJECTED':
    case 'REVERSED':
      return 'DENIED';
    case 'EXPIRED':
      return 'EXPIRED';
  }
  return '';
};

export default function PendingBalanceTable({
  affiliateLinkTrackings,
  total,
  isLoading,
  containerClass,
}: {
  affiliateLinkTrackings: AffiliateLinkTracking[];
  total: number;
  isLoading: boolean;
  containerClass?: string;
}) {
  const [selectedProduct, setSelectedProduct] = useState<AffiliateLinkTracking | null>(null);
  const [dialogOpen, toggleDialog] = useToggle(false);

  const copyActionId = (actionId: string) => {
    navigator.clipboard.writeText(actionId);
    toast.success('Action ID copied!');
  };

  const copyTrackingLink = (trackingLink: string) => {
    navigator.clipboard.writeText(trackingLink);
    toast.success('Tracking link copied!');
  };

  const handleViewProductDetails = (datum: AffiliateLinkTracking) => {
    setSelectedProduct(datum);
    toggleDialog(true);
  };

  return (
    <>
      <Table containerClass={containerClass}>
        <TableHeader>
          {total ? (
            <TableRow>
              <TableHead>Blog</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Brand</TableHead>
              <TableHead className="text-right">Payable</TableHead>
              <TableHead className="text-right">Status</TableHead>
              <TableHead></TableHead>
            </TableRow>
          ) : (
            <TableRow>
              <TableHead colSpan={6}></TableHead>
            </TableRow>
          )}
        </TableHeader>

        <TableBody>
          {!total && (
            <TableRow>
              <TableCell colSpan={6} className="text-center text-13 text-gray9">
                {isLoading ? 'Loading...' : `You don't have any pending balance right now.`}
              </TableCell>
            </TableRow>
          )}
          {affiliateLinkTrackings?.map((alt) => (
            <TableRow key={alt._id}>
              <TableCell className="max-w-40 truncate font-medium ">
                <Link
                  to={`/dashboard/blogs/${alt.blogId}`}
                  className="cursor-pointer hover:underline"
                >
                  {alt.blogTitle}
                </Link>
              </TableCell>
              <TableCell
                className="max-w-40 cursor-pointer truncate font-medium"
                onClick={() => handleViewProductDetails(alt)}
              >
                <div className="flex gap-2 hover:underline">
                  {alt.productImageUrl ? (
                    <img
                      src={alt.productImageUrl}
                      width={20}
                      height={20}
                      className="size-5 rounded-lg"
                      alt={`${alt.productName} icon`}
                    />
                  ) : (
                    <IoImage className="size-5 rounded-lg" />
                  )}
                  <span className="max-w-[150px] truncate font-semibold">{alt.productName}</span>
                </div>
              </TableCell>
              <TableCell className="max-w-40 truncate text-sm text-gray9">
                {alt.networkBrandName}
              </TableCell>
              <TableCell className="text-right">${alt.affiliateCommission || '0.00'}</TableCell>

              <TableCell className="text-right">
                <div
                  className={cn(
                    'inline-block h-5 rounded-3xl px-2 py-0.5 !text-11 font-semibold uppercase',
                    getStatusColors(alt.conversionStatus)
                  )}
                >
                  {getStatusDisplay(alt.conversionStatus)}
                </div>
              </TableCell>

              <TableCell align="right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex size-5 flex-center">
                      <MdMoreVert className="text-gray12" size={14} />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => copyActionId(alt._id)}>
                      Copy Action ID
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => copyTrackingLink(alt.trackingLink || alt.affiliateLink)}
                    >
                      Copy Affiliate Link
                    </DropdownMenuItem>
                    <Link to={alt.trackingLink || alt.affiliateLink} target="_blank">
                      <DropdownMenuItem>View Product</DropdownMenuItem>
                    </Link>
                    <Link to={`/dashboard/blogs/${alt.blogId}`}>
                      <DropdownMenuItem>View Blog</DropdownMenuItem>
                    </Link>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <ProductDialog
        isOpen={dialogOpen}
        onClose={toggleDialog}
        selectedProduct={{
          _id: selectedProduct?.productId || '',
          brandLogoUrl: selectedProduct?.networkBrandLogoUrl || '',
          brandName: selectedProduct?.networkBrandName || '',
          productName: selectedProduct?.productName || '',
          productImageUrl: selectedProduct?.productImageUrl || '',
          trackingLink: selectedProduct?.trackingLink || '',
          totalClickCount: selectedProduct?.clickCount || 0,
          totalActionCount: selectedProduct?.actionCount || 0,
          totalEarnings: selectedProduct?.affiliateCommission || 0,
        }}
      />
    </>
  );
}
