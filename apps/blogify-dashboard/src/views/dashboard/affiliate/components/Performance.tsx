import { useEffect, useState } from 'react';
import { DNSActivityType, PeriodType } from '../../analytics/Analytics';
import dayjs from 'dayjs';
import { useQuery } from 'react-query';
import { DateSelector } from '@/components/commonActivity';
import StateHandler from '@/components/misc/StateHandler';
import DnsActivity from '@/components/commonActivity/activity/DnsActivity';

const Performance = () => {
  const [period, setPeriod] = useState<PeriodType>('Today');
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);
  const [isInital, setIsInital] = useState(true);

  const startDate = dateRange[0].toISOString();
  const endDate = dateRange[1].toISOString();

  const {
    data: dnsActivityData,
    isLoading: dnsActivityLoading,
    refetch,
  } = useQuery<DNSActivityType>([`affiliate/chart?start=${startDate}&end=${endDate}`]);
  const isEmptyDnsActivity =
    !dnsActivityData?.sales.length &&
    !dnsActivityData?.clicks.length &&
    !dnsActivityData?.views.length;

  useEffect(() => {
    if (!isInital) return;
    if (!isEmptyDnsActivity || period === '30 Days') {
      setIsInital(false);
      return;
    }
    const nextPeriod = period === 'Today' ? '7 Days' : period === '7 Days' ? '30 Days' : period;
    const nextDateRange =
      nextPeriod === '7 Days'
        ? [dayjs().subtract(7, 'days'), dayjs().subtract(1, 'days')]
        : nextPeriod === '30 Days'
          ? [dayjs().subtract(30, 'days'), dayjs().subtract(1, 'days')]
          : null;
    setPeriod(nextPeriod);

    if (nextDateRange) {
      setDateRange(nextDateRange);
      refetch();
    }
  }, [isEmptyDnsActivity, isInital, period, refetch]);

  return (
    <div className="rounded-lg border border-gray10">
      <div className="p-4">
        <h1 className="text-lg font-semibold">Performance</h1>
        <p className="text-sm">Get an overview of how your blogs are performing.</p>
      </div>

      <div className="flex items-center justify-between px-4">
        <DateSelector
          selectedRange={period}
          onRangeChange={setPeriod}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </div>

      <div>
        {dnsActivityLoading || isEmptyDnsActivity ? (
          <StateHandler
            loading={dnsActivityLoading}
            className="min-h-[40vh]"
            isEmpty={isEmptyDnsActivity}
          />
        ) : (
          <DnsActivity dnsActivityData={dnsActivityData} showTraffic={false} showBlog={false} />
        )}
      </div>
    </div>
  );
};

export default Performance;
