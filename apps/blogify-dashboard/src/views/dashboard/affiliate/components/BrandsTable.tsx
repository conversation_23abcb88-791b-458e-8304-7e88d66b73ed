import type { Affiliate<PERSON><PERSON> } from '@ps/types';

import { HiDotsVertical } from 'react-icons/hi';
import { useState } from 'react';
import { IoImage } from 'react-icons/io5';

import { calculateMetrics } from '../utils';
import BrandDialog from './dialogs/BrandDialog';

export default function BrandsTable({ data }: { data: AffiliateBrand[] }) {
  const [dropdownOpenIndex, setDropdownOpenIndex] = useState<number | null>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [selectedBrand, setSelectedBrand] = useState<AffiliateBrand | null>(null);

  const handleDropdownToggle = (index: number) => {
    // Close the dropdown if it's already open or open the clicked one
    setDropdownOpenIndex(dropdownOpenIndex === index ? null : index);
  };

  const handleViewLinks = (datum: AffiliateBrand) => {
    setSelectedBrand(datum);
    setDialogOpen(true);
    setDropdownOpenIndex(null); // Close the dropdown when a link is viewed
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[600px]">
        <thead className="h-6 bg-bg2">
          <tr className="text-left text-xs uppercase text-gray9 *:p-2">
            <th className="px-2 font-medium">Brand</th>
            <th className="px-2 font-medium">Link</th>
            <th className="px-2 text-right font-medium">Clicks</th>
            <th className="px-2 text-right font-medium">Actions</th>
            <th className="px-2 text-right font-medium">Earnings</th>
            <th className="px-2 text-right font-medium">EPC</th>
            <th className="px-2 text-right font-medium">EPS</th>
            <th className="px-2 text-center font-medium">CR</th>
            <th className="px-2 text-center font-medium"></th>
          </tr>
        </thead>
        <tbody>
          {data.map((datum, index) => {
            const { EPC, EPS, CR } = calculateMetrics(datum);

            return (
              <tr key={`${datum.brandName}-${index}`} className="even:bg-gray-50">
                <td className="flex gap-2 p-2">
                  {datum.brandLogoUrl ? (
                    <img
                      src={datum.brandLogoUrl}
                      width={20}
                      height={20}
                      className="size-5 rounded-lg"
                      alt={`${datum.brandName} icon`}
                    />
                  ) : (
                    <IoImage className="size-5 rounded-lg" />
                  )}
                  <span className="truncate font-semibold">{datum.brandName}</span>
                </td>
                <td className="truncate">
                  <a className="underline" onClick={() => handleViewLinks(datum)}>
                    {datum.linkCount} Links
                  </a>
                </td>
                <td className="px-4 text-right">{datum.totalClickCount}</td>
                <td className="p-2 text-right">{datum.totalActionCount}</td>
                <td className="p-2 text-right">
                  {datum.totalEarnings.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 3,
                  })}
                </td>
                <td className="p-2 text-right">
                  {EPC.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 4,
                  })}
                </td>
                <td className="p-2 text-right">
                  {EPS.toLocaleString(undefined, {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 3,
                  })}
                </td>
                <td className="p-2 text-center">
                  {CR.toLocaleString(undefined, {
                    style: 'percent',
                    minimumFractionDigits: 2,
                  })}
                </td>
                <td className="relative p-2">
                  <HiDotsVertical
                    className="cursor-pointer text-gray-600"
                    onClick={() => handleDropdownToggle(index)}
                  />
                  {dropdownOpenIndex === index && (
                    <div className="absolute right-0 z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
                      <ul className="text-sm text-gray-700">
                        <li
                          className="cursor-pointer p-2 hover:bg-gray-100"
                          onClick={() => handleViewLinks(datum)}
                        >
                          View Active Links
                        </li>
                      </ul>
                    </div>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>

      <BrandDialog
        dialogOpen={dialogOpen}
        setDialogOpen={setDialogOpen}
        selectedBrand={selectedBrand}
      />
    </div>
  );
}
