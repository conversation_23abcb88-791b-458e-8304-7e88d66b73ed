import { useState } from 'react';
import { AnalyticsData, PeriodType } from '../../analytics/Analytics';
import dayjs from 'dayjs';
import { useQuery } from 'react-query';
import { DateSelector } from '@/components/commonActivity';
import BlogAnalytics from '../../analytics/BlogAnalytics';

const BlogAnalyticsWrapper = () => {
  const [period, setPeriod] = useState<PeriodType>('Today');
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);

  const startDate = dateRange[0].toISOString();
  const endDate = dateRange[1].toISOString();

  const { data: analyticsData } = useQuery<AnalyticsData>(['analytics', { startDate, endDate }], {
    enabled: true,
  });

  return (
    <div className="rounded-lg border border-gray10">
      <div className="p-4">
        <h1 className="text-lg font-semibold">Blog Analytics</h1>
        <p>See how your content is performing around the globe.</p>
      </div>

      <div className="flex items-center justify-between px-4 pb-5">
        <DateSelector
          selectedRange={period}
          onRangeChange={setPeriod}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
        />
      </div>

      <div>
        <BlogAnalytics analyticsData={analyticsData} />
      </div>
    </div>
  );
};

export default BlogAnalyticsWrapper;
