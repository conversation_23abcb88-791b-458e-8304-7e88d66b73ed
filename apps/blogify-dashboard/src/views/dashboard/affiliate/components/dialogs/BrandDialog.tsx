import StateHandler from '@/components/misc/StateHandler';
import { copy } from '@/utils/clipboard';
import { AffiliateBrand, AffiliateBrandLink } from '@ps/types';
import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import { BsCopy } from 'react-icons/bs';
import { FiExternalLink } from 'react-icons/fi';
import { IoImage } from 'react-icons/io5';
import { useQuery } from 'react-query';

type PropsType = {
  dialogOpen: boolean;
  setDialogOpen: (open: boolean) => void;
  selectedBrand: AffiliateBrand | null;
};

const BrandDialog = ({ dialogOpen, setDialogOpen, selectedBrand }: PropsType) => {
  const { data, isLoading, isRefetching, error } = useQuery<AffiliateBrandLink[]>(
    [
      `/affiliate-link-trackings/active-links-by-brand?brandName=${selectedBrand?.brandName}`,
      // selectedBrand?.brandName,
    ],
    { enabled: !!selectedBrand?.brandName, refetchOnWindowFocus: false }
  );
  const { networkBrandName, networkBrandLogoUrl } = data ? data[0] : {};

  const handleClose = () => {
    setDialogOpen(false);
  };

  return (
    <Dialog isOpen={dialogOpen} onClose={handleClose} className="max-w-3xl">
      {isLoading || isRefetching || error || !data?.length ? (
        <StateHandler
          loading={isLoading || isRefetching}
          error={error as string}
          isEmpty={!data?.length}
          emptyMsg="No data found."
          className="!min-h-[40vh]"
        />
      ) : (
        <div className="p-4">
          <div className="mb-5 flex flex-col">
            {networkBrandLogoUrl ? (
              <img
                src={networkBrandLogoUrl}
                width={60}
                height={60}
                className="size-12 rounded-lg"
                alt={`${networkBrandName} icon`}
              />
            ) : (
              <IoImage className="size-5 rounded-lg" />
            )}
            <span className="mt-2 truncate font-semibold">{networkBrandName}</span>
          </div>
          <p className="mb-2 text-xs uppercase text-gray9">active links ({data.length})</p>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <tbody>
                {data.map((row, index) => (
                  <tr key={`${row.productName}-${index}`} className="*:p-2 even:bg-gray-50">
                    <td className="max-w-[150px] truncate font-semibold" title={row.productName}>
                      {row.productName}
                    </td>
                    <td className="max-w-[150px] truncate text-gray9" title={row.productCategory}>
                      {row.productCategory}
                    </td>
                    <td className="max-w-[300px] truncate" title={row.affiliateLink}>
                      {row.affiliateLink}
                    </td>

                    <td className="flex gap-2">
                      <button
                        type="button"
                        className="rounded-md border p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] hover:bg-gray-100"
                        onClick={() => window.open(row.trackingLink, '_blank')}
                      >
                        <FiExternalLink color="#a8918a" />
                      </button>

                      <button
                        type="button"
                        className="rounded-md border p-1 shadow-[0_1px_1px_0_rgba(168,145,138,0.3)] hover:bg-gray-100"
                        onClick={() => copy(row.trackingLink)}
                      >
                        <BsCopy color="#a8918a" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </Dialog>
  );
};

export default BrandDialog;
