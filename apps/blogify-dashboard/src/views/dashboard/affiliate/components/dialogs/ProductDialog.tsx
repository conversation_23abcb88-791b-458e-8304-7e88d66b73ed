import { AffiliateProduct } from '@ps/types';
import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import { BiCopy } from 'react-icons/bi';
import { calculateMetrics } from '../../utils';
import { copy } from '@/utils/clipboard';

type PropsType = {
  isOpen: boolean;
  onClose: () => void;
  selectedProduct: AffiliateProduct | null;
};

const ProductDialog = ({ isOpen, onClose, selectedProduct }: PropsType) => (
  <Dialog isOpen={isOpen} onClose={onClose}>
    {selectedProduct && (
      <div className="p-4">
        <img
          className=" h-[260px] w-[464px]"
          src={selectedProduct.productImageUrl}
          alt={selectedProduct.productName}
        />
        <p className="mt-2">
          <strong>{selectedProduct.productName}</strong>
        </p>
        <p>
          <strong className="text-sm text-gray9">Category:</strong>
        </p>
        <div className="mt-2 flex justify-between">
          <div className="flex items-center gap-2">
            {selectedProduct.brandLogoUrl && (
              <img
                src={selectedProduct.brandLogoUrl}
                width={20}
                height={20}
                className="size-5 rounded-full object-contain"
                alt={`${selectedProduct.brandName} icon`}
              />
            )}
            <span className="truncate text-md font-semibold">{selectedProduct.brandName}</span>
          </div>
          <a href={selectedProduct.trackingLink} target="_blank">
            <Button variant={'secondary'}>View Product</Button>
          </a>
        </div>
        <div>
          <p className="text-xs uppercase text-gray9">Affiliate Link</p>
          <p className="mt-2 text-sm">
            {selectedProduct._id.length > 70
              ? `${selectedProduct._id.substring(0, 70)}...`
              : selectedProduct._id}
          </p>
          <button
            className="mt-2 inline-flex cursor-pointer items-center gap-1 rounded-lg border px-1 text-sm shadow"
            onClick={() => copy(selectedProduct.trackingLink)}
          >
            <BiCopy /> copy
          </button>
        </div>

        {(() => {
          const { EPC, EPS, CR } = calculateMetrics(selectedProduct);
          return (
            <div className="mt-4 flex flex-col gap-6">
              <div className="flex items-center justify-between gap-6">
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Clicks</p>
                  <p className="text-xl font-bold">{selectedProduct.totalClickCount}</p>
                </div>
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Actions</p>
                  <p className="text-xl font-bold">{selectedProduct.totalActionCount}</p>
                </div>
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Earnings</p>
                  <p className="text-xl font-bold">
                    {selectedProduct.totalEarnings.toLocaleString(undefined, {
                      style: 'currency',
                      currency: 'USD',
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between gap-6">
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Earn Per Click</p>
                  <p className="text-xl font-bold">
                    {EPC.toLocaleString(undefined, { style: 'currency', currency: 'USD' })}
                  </p>
                </div>
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Earn Per Sale</p>
                  <p className="text-xl font-bold">
                    {EPS.toLocaleString(undefined, { style: 'currency', currency: 'USD' })}
                  </p>
                </div>
                <div className="flex flex-col items-start">
                  <p className="text-xs font-medium uppercase text-gray9">Conversion Rate</p>
                  <p className="text-xl font-bold">
                    {CR.toLocaleString(undefined, {
                      style: 'percent',
                      minimumFractionDigits: 2,
                    })}
                  </p>
                </div>
              </div>
              <Button variant={'secondary'} onClick={onClose}>
                Close
              </Button>
            </div>
          );
        })()}
      </div>
    )}
  </Dialog>
);

export default ProductDialog;
