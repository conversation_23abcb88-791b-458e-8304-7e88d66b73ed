// // بسم الله الرحمن الرحيم

// import { ComponentProps, useMemo } from 'react';
// import SVGWorldMap from 'react-svg-worldmap';
// import { Country } from './constants';

// function WorldMap({ data }: { data: { country: keyof typeof Country; value: number }[] }) {
//   return (
//     <div className="w-full h-auto">
//       <SVGWorldMap
//         strokeOpacity={0}
//         backgroundColor="white"
//         borderColor="#f2470d"
//         color="#f2470d"
//         valueSuffix="clicks"
//         size="xl"
//         richInteraction
//         frame
//         frameColor="white"
//         data={[
//           ...Object.keys(Country).map((s) => ({
//             country: s,
//             value: 0,
//           })),
//           ...data,
//         ]}
//       />
//     </div>
//   );
// }

// export function ClicksWorldMap({ data }: { data: ComponentProps<typeof WorldMap>['data'] }) {
//   return (
//     <section className="flex flex-col md:flex-row justify-around gap-4 p-4">
//       <div className="w-full ">
//         <WorldMap data={data} />
//       </div>
//       <TopClicksByCountry data={data} />
//     </section>
//   );
// }

// function TopClicksByCountry({
//   data,
// }: {
//   data: { country: keyof typeof Country; value: number }[];
// }) {
//   const [percentagesClick] = useMemo(() => {
//     const descending = data.sort((a, b) => b.value - a.value);
//     const total = descending.reduce((prev, current) => prev + current.value, 0);
//     const percentages = descending.map(({ country, value }) => ({
//       country,
//       percentage: value / total,
//       click: value,
//     }));
//     return [percentages];
//   }, [data]);

//   return (
//     <section className="flex flex-col gap-4 md:w-1/2">
//       <section className="flex flex-col gap-2">
//         <span className="text-gray-700 text-lg">Most Clicked</span>
//         <span className="text-2xl font-semibold">{Country[percentagesClick[0]?.country]}</span>
//         <span>
//           {percentagesClick[0]?.click} Clicks | Share:{' '}
//           {percentagesClick[0]?.percentage.toLocaleString(undefined, { style: 'percent' })}
//         </span>
//       </section>
//       <section className="space-y-3">
//         <span className="text-sm text-gray-600">Top Countries</span>
//         <div className="flex flex-col gap-2">
//           {percentagesClick.map(({ country, percentage }) => (
//             <div
//               className="flex justify-between gap-6 rounded-lg p-2  odd:bg-gray-100"
//               key={country}
//             >
//               <span className="truncate">{Country[country]}</span>
//               <span>{percentage.toLocaleString(undefined, { style: 'percent' })}</span>
//             </div>
//           ))}
//         </div>
//       </section>
//     </section>
//   );
// }
