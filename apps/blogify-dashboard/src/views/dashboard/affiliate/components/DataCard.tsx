// بسم الله الرحمن الرحيم

export default function DataCard({
  title,
  value,
  info,
}: {
  title: string;
  value: string;
  info: string;
}) {
  return (
    <article className="flex flex-col gap-4 rounded-lg border border-gray10 bg-gradient-to-b from-white to-bg2 p-3">
      <div className="flex flex-col gap-2">
        <span className="text-sm font-medium">{title}</span>
        <span className="text-2xl font-semibold">{value}</span>
      </div>
      <span className="text-xs text-gray9">{info}</span>
    </article>
  );
}
