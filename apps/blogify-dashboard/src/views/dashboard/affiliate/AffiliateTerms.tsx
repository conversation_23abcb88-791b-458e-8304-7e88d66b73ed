import { useMutation } from 'react-query';

import { useStoreActions } from '@/store';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';

export default function AffiliateTerms() {
  const fetchUser = useStoreActions((a) => a.user.fetch);

  const { mutateAsync: accept, isLoading } = useMutation({
    mutationFn: () => API.patch('business/accept-affiliate-tnc').then(() => fetchUser()),
  });

  return (
    <div className="mx-auto max-w-2xl rounded-xl border border-gray10 p-6">
      <h2 className="text-21 font-semibold">Terms & Conditions</h2>
      <p className="mt-5 text-15">
        Terms & Policy for Blogify.ai Users Utilizing Affiliate Link Automation Powered by AI
      </p>

      <ul className="my-6 flex flex-col gap-5 rounded-lg bg-[#f4f1f0]/50 p-4">
        <li>
          <h3 className="text-15 font-semibold">1. General Overview</h3>
          <p className="mt-1.5">
            Blogify.ai provides an Affiliate Link Automation feature that enables users to monetize
            their blogs seamlessly. By using this feature, users agree to the following terms and
            policies.
          </p>
        </li>

        <li>
          <h3 className="text-15 font-semibold">2. Responsibility for Content</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">
              Users are solely responsible for the content they publish, including compliance with
              copyright and intellectual property laws. Blogify.ai assumes no liability for any
              copyright infringement issues arising from user-generated content.
            </li>
          </ul>
        </li>

        <li>
          <h3 className="text-15 font-semibold">3. Affiliate Link Automation</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">
              Blogify.ai's AI system identifies optimal keywords within user blogs and inserts
              perfectly matched affiliate links to enhance monetization potential.
            </li>
            <li className="ml-5 mt-1">
              When a user's blog is published and a visitor clicks an affiliate link and makes a
              purchase, the user earns a commission.
            </li>
          </ul>
        </li>

        <li>
          <h3 className="text-15 font-semibold">4. Fees</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">
              There is no fixed platform fee for using the Affiliate Link Automation feature.
            </li>
            <li className="ml-5 mt-1">
              A 5% payment processing fee will be applied during withdrawal transactions.
            </li>
          </ul>
        </li>

        <li>
          <h3 className="text-15 font-semibold">5. Payment Terms</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">
              Earnings generated through the Affiliate Link Automation feature will be paid on a{' '}
              <strong>NET 45</strong> basis.
              <ul className="list-disc">
                <li className="ml-5 mt-1">
                  Example: For a sale made on January 1st, the sale will be locked on February 1st
                  (unless voided). The corresponding amount will be available for withdrawal
                  starting March 15th.
                </li>
              </ul>
            </li>
            <li className="ml-5 mt-1">
              Users can view the payment status and details from their Blogify.ai dashboard.
            </li>
          </ul>
        </li>

        <li>
          <h3 className="text-15 font-semibold">6. Withdrawal Options</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">The minimum withdrawal amount is $50.</li>
            <li className="ml-5 mt-1">
              Currently, users can withdraw funds to their bank accounts only.
            </li>
            <li className="ml-5 mt-1">
              Users may provide Wise or Payoneer bank details for withdrawals.
            </li>
            <li className="ml-5 mt-1">
              Blogify.ai is actively working to add additional withdrawal options in the future.
            </li>
          </ul>
        </li>

        <li>
          <h3 className="text-15 font-semibold">7. Disclaimer</h3>
          <ul className="list-disc">
            <li className="ml-5 mt-1.5">
              Blogify.ai does not guarantee specific earning amounts through the Affiliate Link
              Automation feature. Earnings depend on user content performance and visitor
              engagement.
            </li>
            <li className="ml-5 mt-1">
              Blogify.ai reserves the right to modify these terms and policies at any time, with
              updates communicated to users.
            </li>
          </ul>
        </li>
      </ul>

      <p className="text-15">
        Using the Affiliate Link Automation feature, users agree to comply with the above terms and
        acknowledge their understanding of the outlined responsibilities and processes.
      </p>
      <p className="mt-2 text-15 font-semibold">Effective Date: 1 January 2025</p>

      <div className="mt-6 flex gap-3">
        <Button onClick={() => accept()} loading={isLoading}>
          Agree & Continue
        </Button>
        <Button variant="secondary" onClick={() => window.history.back()}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
