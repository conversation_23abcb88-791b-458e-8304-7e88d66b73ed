type ParamsType = {
  totalEarnings: number;
  totalClickCount: number;
  totalActionCount: number;
};

export const calculateMetrics = (params: ParamsType) => {
  const { totalEarnings, totalClickCount, totalActionCount } = params;

  const EPC = totalClickCount > 0 ? totalEarnings / totalClickCount : 0;
  const EPS = totalActionCount > 0 ? totalEarnings / totalActionCount : 0;
  const CR = totalClickCount > 0 ? totalActionCount / totalClickCount : 0;

  return { EPC, EPS, CR };
};
