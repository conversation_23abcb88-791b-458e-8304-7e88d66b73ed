import type { Notification } from '@/types/resources/notification.type';
import type { APIResponse } from '@ps/types';

import { useInfiniteQuery } from 'react-query';
import { useEffect } from 'react';

import { PageContainerMini } from '@/components/layout/PageContainer';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import Loader from '@/components/misc/Loader';

import NotificationCard from './components/NotificationCard';
import Spinner from '@/components/misc/Spinner';

const fetchNotifications = async ({
  pageParam = 0,
}: { pageParam: number; queryKey: string[] } | any): Promise<{
  notifications: Notification[];
  totalPages: number;
  nextPage: number;
}> => {
  const LIMIT = 10;
  const response = await API.fetch<APIResponse<Notification>>(
    `notifications?limit=${LIMIT}&offset=${pageParam * LIMIT}`
  );

  return {
    notifications: response?.data || [],
    totalPages: Math.ceil((response?.total || 0) / LIMIT) || 1,
    nextPage: pageParam + 1,
  };
};

const Notifications = () => {
  const { data, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage } = useInfiniteQuery({
    queryKey: ['notifications'],
    queryFn: fetchNotifications,
    getNextPageParam: (lastPage) =>
      lastPage?.nextPage < lastPage?.totalPages ? lastPage?.nextPage : undefined,
    enabled: true,
  });

  const notifications = data?.pages.flatMap((page) => page.notifications) || [];

  const handleScroll = () => {
    if (window.innerHeight + window.scrollY >= document.documentElement.scrollHeight) {
      if (isFetchingNextPage || !hasNextPage) return;
      fetchNextPage();
    }
  };

  useEffect(() => {
    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasNextPage]);

  return (
    <DashboardContainer title="Notifications">
      <PageContainerMini>
        <Card className="px-4 py-2">
          <div className="text-sm text-primary">All</div>
        </Card>

        {!!notifications.length ? (
          notifications.map((notification) => (
            <Card key={notification._id} className="mt-0.5">
              <NotificationCard notification={notification} />
            </Card>
          ))
        ) : isFetching ? (
          <Card className="mt-0.5">
            <div className="flex flex-center" style={{ minHeight: 'calc(100vh - 225px)' }}>
              <Loader />
            </div>
          </Card>
        ) : (
          <Card className="mt-0.5">
            <div className="flex flex-center" style={{ minHeight: 'calc(100vh - 225px)' }}>
              <div className="text-center text-gray2">
                You do not have any notifications at the moment.
              </div>
            </div>
          </Card>
        )}

        {isFetchingNextPage && (
          <div className="mt-0.5 flex h-20 flex-center">
            <Spinner />
          </div>
        )}
      </PageContainerMini>
    </DashboardContainer>
  );
};

export default Notifications;
