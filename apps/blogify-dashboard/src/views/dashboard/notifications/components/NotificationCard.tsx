import type { Notification } from '@/types/resources/notification.type';

import { MdNotifications } from 'react-icons/md';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';

dayjs.extend(relativeTime);

const NotificationCard = ({
  notification: { title, description, createdAt },
}: {
  notification: Notification;
}) => (
  <div className="flex min-h-20 w-full items-center border-b border-bg2 p-4">
    <div className="flex size-10 !min-w-10 rounded-full bg-primary flex-center">
      <div className="text-white">
        <MdNotifications size={20} />
      </div>
    </div>

    <div className="ml-4 w-full">
      <div className="leading-tight">
        <div className="text-xs font-semibold text-black2">{title}</div>
        <div className="mt-1 text-xs text-gray2">{description}</div>
      </div>
      <div className="mt-0.5 text-right text-xs text-gray2">{dayjs(createdAt).fromNow()}</div>
    </div>
  </div>
);

export default NotificationCard;
