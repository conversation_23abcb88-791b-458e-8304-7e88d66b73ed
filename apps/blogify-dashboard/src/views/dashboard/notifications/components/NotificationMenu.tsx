import type { APIResponseType } from '@/types/resources';
import type { Notification } from '@/types/resources/notification.type';

import { MdNotifications } from 'react-icons/md';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import {
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';
import subscribeForPushNotification from '@/services/push-notification';
import Spinner from '@/components/misc/Spinner';
import Link from '@/components/common/Link';

import NotificationCard from './NotificationCard';

const NotificationMenu = () => {
  const { data: { data: notifications, total } = { data: [] }, isFetching } = useQuery<
    APIResponseType<Notification>
  >('/notifications?limit=10', {
    enabled: true,
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="relative size-9 rounded-full outline-none" variant="icon">
          {/* TODO: Develop actual read status system */}
          {notifications[0] && dayjs().diff(notifications[0]?.createdAt, 'hours') < 12 && (
            <div className="absolute right-2 top-1 size-2 rounded-full border bg-primary" />
          )}
          <div className="flex items-center justify-center">
            <MdNotifications size={18} className="text-gray12" />
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="max-h-[calc(100vh-60px)] w-screen overflow-y-auto rounded-none pb-20 sm:max-h-96 sm:w-80 sm:rounded-md sm:pb-0"
        sideOffset={12}
        align="end"
      >
        {'Notification' in window && Notification.permission === 'default' && (
          <DropdownMenuItem onClick={() => subscribeForPushNotification()} asChild>
            <button className="flex w-full justify-center text-13">Enable Push Notification</button>
          </DropdownMenuItem>
        )}

        {!!notifications.length ? (
          notifications.map((notification) => (
            <Link key={notification._id} to={notification.actionLink || '#'}>
              <DropdownMenuItem className="h-auto p-0">
                <NotificationCard notification={notification} />
              </DropdownMenuItem>
            </Link>
          ))
        ) : isFetching ? (
          <DropdownMenuItem>
            <div className="flex">
              <Spinner />
            </div>
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem>
            <div className="flex h-[49px] flex-center">
              <div className="text-center text-sm text-gray2">
                You do not have any notifications at the moment.
              </div>
            </div>
          </DropdownMenuItem>
        )}

        {Number(total) > notifications.length && (
          <Link to="/dashboard/notifications">
            <DropdownMenuItem className="flex h-auto min-w-0 justify-end p-0 px-4 py-2 !text-13">
              See All
            </DropdownMenuItem>
          </Link>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationMenu;
