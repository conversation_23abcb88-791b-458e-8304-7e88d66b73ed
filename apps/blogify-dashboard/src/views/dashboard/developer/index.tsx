import { Outlet, Link } from 'react-router-dom';

import UserMenu from '../layout/header/components/UserMenu';

const DeveloperPortal = () => (
  <div className="min-h-screen w-full bg-white font-inter text-black4">
    <header className="container sticky top-0 flex h-14 items-center justify-between px-6">
      <Link className="flex items-center gap-2" to="/">
        <img className="size-6" src="/images/blogify.svg" />
        <span className="text-xl font-bold">Blogify</span>
      </Link>

      <UserMenu />
    </header>

    <main className="container mx-auto flex justify-center pt-3 font-inter text-black4">
      <Outlet />
    </main>
  </div>
);

export default DeveloperPortal;
