import { ComponentProps, useState } from 'react';
import { IoMdEyeOff, IoMdEye } from 'react-icons/io';
import { IconType } from 'react-icons';

function PasswordInput({ ...props }: ComponentProps<'input'>) {
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = (previousState: boolean) => {
    setShowPassword(!previousState);
  };
  return (
    <div className={'flex w-full items-center justify-between gap-2'}>
      <input {...props} type={showPassword ? 'text' : 'password'} />
      <button className="text-primary" onClick={() => togglePasswordVisibility(showPassword)}>
        {showPassword ? <IoMdEyeOff /> : <IoMdEye />}
      </button>
    </div>
  );
}

export function Field({
  type,
  label,
  Icon,
  className,
  error,
  ...props
}: {
  type: 'textarea' | ComponentProps<'input'>['type'];
  label: string;
  className?: string;
  Icon: IconType;
  error?: string;
} & ComponentProps<'input'> &
  ComponentProps<'textarea'>) {
  const fieldCSS =
    'hover:shadow-soft-glow focus:shadow-soft-glow w-full bg-bg2 rounded-lg p-2 shadow-inner-glow';
  return (
    <section className={className}>
      <label className="space-y-1">
        <span className="flex items-center gap-2 font-semibold">
          <Icon className="text-primary" />
          {label}
        </span>
        {(() => {
          switch (type) {
            case 'textarea':
              return <textarea className={fieldCSS} {...props} />;
            case 'password':
              return <PasswordInput className={fieldCSS} {...props} />;
            default:
              return <input type={type} className={fieldCSS} {...props} />;
          }
        })()}
      </label>
      <span className="text-sm text-primary-100">{error}</span>
    </section>
  );
}
