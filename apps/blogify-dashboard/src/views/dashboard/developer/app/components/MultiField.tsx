import { ComponentProps } from 'react';
import { MdCancel } from 'react-icons/md';
import { IconType } from 'react-icons';

function Cell({
  value,
  onCancel,
  disabled,
}: {
  value: string;
  onCancel?: (value: string) => void;
  disabled?: boolean;
}) {
  return (
    <section className="flex items-center justify-between gap-2 rounded-lg bg-gray-100 p-2">
      <span>{value}</span>
      <button
        hidden={disabled}
        onClick={() => {
          onCancel?.(value);
        }}
      >
        <MdCancel className="text-primary" />
      </button>
    </section>
  );
}

function MultiInput({
  defaultValues,
  className,
  disabled,
}: {
  defaultValues: Array<ComponentProps<typeof Cell>['value']>;
  className: string;
} & Omit<ComponentProps<typeof Cell>, 'value'>) {
  return (
    <div className={className}>
      <section className={'flex min-h-7 flex-wrap gap-3 rounded-lg p-2'}>
        {defaultValues.map((item, index) => (
          <Cell disabled={disabled} key={`${item}-${index}`} value={item} />
        ))}
      </section>
    </div>
  );
}

export function MultiField({
  label,
  Icon,
  className,
  defaultValues,
  disabled,
}: {
  label: string;
  type: 'text' | 'url';
  name: string;
  Icon: IconType;
  className: string;
} & ComponentProps<typeof MultiInput>) {
  return (
    <section className={className}>
      <label>
        <span className="flex items-center gap-2 font-semibold">
          <Icon className="text-primary" />
          {label}
        </span>
        <MultiInput
          disabled={disabled}
          className={'rounded-lg p-2 hover:shadow-soft-glow focus:shadow-soft-glow'}
          defaultValues={defaultValues}
        />
      </label>
    </section>
  );
}
