// بسم الله الرحمن الرحيم
import type { OauthApp } from '@/types/resources/oauth-app.type';

import { MdInfo, MdLink, MdPalette } from 'react-icons/md';
import { FaFileImage, FaRegSave } from 'react-icons/fa';
import { ChangeEvent, useState } from 'react';
import { ValidationError } from 'class-validator';
import { useNavigate } from 'react-router-dom';
import { TbApi } from 'react-icons/tb';

import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';

import { Field } from './components/Field';

type CreateAppDTO = Omit<OauthApp, '_id' | 'secret' | 'createdAt' | 'updatedAt'>;
function AppForm({
  updateApp,
  errors,
}: {
  errors?: Record<keyof CreateAppDTO, string>;
  updateApp: React.Dispatch<React.SetStateAction<CreateAppDTO>>;
}) {
  function onChangeHandler(event: ChangeEvent<HTMLInputElement>) {
    const { name, value } = event.target;
    switch (name) {
      case 'permissions':
        updateApp((previous) => ({
          ...previous,
          permissions: value
            .split(',')
            .map((permission) => permission.trim())
            .filter((permission) => permission.length > 0),
        }));
        break;
      case 'redirectURIs':
        updateApp((previous) => ({
          ...previous,
          redirectUris: value
            .split(',')
            .map((uri) => uri.trim())
            .filter((uri) => uri.length > 0),
        }));
        break;
      default:
        updateApp((previous) => ({
          ...previous,
          [name]: value,
        }));
    }
  }
  return (
    <article
      className="grid grid-cols-2 gap-4 rounded-lg border border-gray-300 p-6"
      onChange={onChangeHandler}
    >
      <Field
        type="text"
        inputMode="text"
        label="Application Name*"
        name="name"
        Icon={MdInfo}
        placeholder="A unique name for your application not exceeding 3 words."
        minLength={1}
        maxLength={30}
        required
        error={errors?.name}
      />
      <Field
        type="url"
        inputMode="url"
        label="Application Logo URL*"
        name="logoURL"
        Icon={FaFileImage}
        placeholder="A link to your application's logo image."
        required
        error={errors?.logoURL}
      />
      <Field
        type="url"
        inputMode="url"
        label="Application Website*"
        name="website"
        Icon={MdLink}
        placeholder="A link to your application's website."
        required
        error={errors?.website}
      />
      <Field
        type="color"
        inputMode="text"
        label="Application Brand Color*"
        name="theme"
        Icon={MdPalette}
        required
        error={errors?.theme}
      />
      <Field
        className="col-span-2"
        type="textarea"
        inputMode="text"
        label="Application Description*"
        name="description"
        Icon={MdInfo}
        placeholder="A short paragraph explaining how effortlessly your app integrates the functionalities provided by Blogify.ai and maximizes the value users obtain from both platforms."
        minLength={1}
        maxLength={300}
        required
        error={errors?.description}
      />
      <Field
        type="text"
        inputMode="text"
        label="Company Name*"
        name="companyName"
        Icon={MdInfo}
        placeholder="The name of your organization developing this application."
        minLength={1}
        maxLength={30}
        required
        error={errors?.companyName}
      />
      <Field
        type="url"
        inputMode="url"
        label="Company Homepage*"
        name="companyWebsite"
        Icon={MdInfo}
        placeholder="A link to your company's homepage."
        required
        error={errors?.companyWebsite}
      />
      <Field
        type="url"
        inputMode="url"
        label="Documentation Link*"
        name="documentationURL"
        Icon={MdLink}
        placeholder="A URL to the application's documentation page."
        required
        error={errors?.documentationURL}
      />
      <Field
        type="url"
        inputMode="url"
        label="Privacy Policy Link*"
        name="privacyPolicyURL"
        Icon={MdLink}
        placeholder="A URL to the platforms privacy policy page."
        required
        error={errors?.privacyPolicyURL}
      />
      <Field
        type="textarea"
        className="col-span-2"
        inputMode="text"
        label="Redirect URIs*"
        name="redirectURIs"
        Icon={MdLink}
        placeholder="A comma separated list of Redirect URIs for your application."
        required
        error={errors?.redirectUris}
      />
      <Field
        type="textarea"
        className="col-span-2"
        inputMode="text"
        label="Scopes*"
        name="permissions"
        Icon={TbApi}
        placeholder="A comma separated list of scopes required by your application."
        required
        error={errors?.permissions}
      />
    </article>
  );
}

export default function EditApp() {
  const [app, updateApp] = useState<CreateAppDTO>({} as CreateAppDTO);
  const [errors, setErrors] = useState<Record<keyof CreateAppDTO, string>>();
  const navigate = useNavigate();
  const updateErrors = (data: ValidationError[]) => {
    setErrors(
      data
        .map((error) => ({
          [error.property]: Object.values(error.constraints ?? {}).join(';'),
        }))
        .reduce((prev, curr) => ({ ...prev, ...curr }), {}) as Record<keyof CreateAppDTO, string>
    );
  };
  return (
    <DashboardContainer
      title="Create OAuth2 Application"
      cancelUrl="/developer/"
      actions={
        <Button
          className="flex gap-2"
          onClick={async () => {
            await API.post('/oauth2-apps/', app)
              .then(() => navigate('/developer/'))
              .catch(updateErrors);
          }}
        >
          <FaRegSave />
          Save
        </Button>
      }
    >
      <AppForm errors={errors} updateApp={updateApp} />
    </DashboardContainer>
  );
}
