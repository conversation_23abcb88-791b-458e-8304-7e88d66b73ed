// بسم الله الرحمن الرحيم
import type { OauthApp } from '@/types/resources/oauth-app.type';

import { MdEdit, MdInfo, MdLink, MdPalette, MdPublic, MdUpdate } from 'react-icons/md';
import { IoMdEye, IoMdEyeOff } from 'react-icons/io';
import { AiFillPlusCircle } from 'react-icons/ai';
import { FaRegSquarePlus } from 'react-icons/fa6';
import { GrHelpBook } from 'react-icons/gr';
import { GiPadlock } from 'react-icons/gi';
import { IconType } from 'react-icons';
import { useQuery } from 'react-query';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

import { MultiField } from './components/MultiField';
import { Field } from './components/Field';

function requestAppPublish(app: OauthApp) {
  const message = `Hi Blogify!,

This a request to review of our OAuth2 application, ${app.name}, for integration with your platform.

App Details:

App Name: ${app.name}
Short Description: ${app.description}
Company Name: ${app.companyName}
Website: ${app.companyWebsite}
Documentation: ${app.documentationURL}
Privacy Policy: ${app.privacyPolicyURL}
Redirect URI(s): ${app.redirectUris?.join(', ')}
Scopes Requested: ${app.permissions.join('\n')}

We believe that integrating our app with your platform will provide significant value to your users.

We are confident that our app meets your security and functionality requirements. Please let us know if you require any additional information or have any questions. We are eager to discuss this opportunity further and look forward to your response.

Thank you for your time and consideration.

Sincerely,

The ${app.name} Team`;

  //@ts-ignore This is a global variable available in the browser
  $crisp.push(['do', 'message:send', ['text', message]]);
  // @ts-ignore This is a global variable available in the browser
  $crisp.push(['do', 'chat:open']);
}

function Info({ text, Icon }: { text: string; Icon: IconType }) {
  return (
    <span className="flex items-center gap-2">
      <Icon size={24} className="text-primary" title={text} />
      <span className=" font-space text-md font-semibold">{text}</span>
    </span>
  );
}

function App({ data }: { data: OauthApp }) {
  const [showDetails, setShowDetails] = useState(false);
  const toggleShowDetails = () => {
    setShowDetails(!showDetails);
  };

  const displayDate = (dateString: string) => {
    const date = new Date(dateString);
    const options = {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    } as const;
    return date.toLocaleString('en-UK', options).replaceAll(',', '  ');
  };
  function hexToRGBA(hexCode: string, alpha = 1) {
    // Remove the hash if present
    const hex = hexCode.replace(/^#/, '');

    // Parse the hex values
    let r, g, b;

    if (hex.length === 3) {
      // Convert 3-digit hex to 6-digit
      r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
      g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
      b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else {
      throw new Error('Invalid hex color format');
    }

    // Validate the values
    if (isNaN(r) || isNaN(g) || isNaN(b)) {
      throw new Error('Invalid hex color values');
    }

    // Ensure alpha is between 0 and 1
    const opacity = Math.max(0, Math.min(1, alpha));

    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return (
    <article
      id="app"
      className={`flex flex-col rounded-lg border border-gray-300 shadow-inner-glow hover:shadow-soft-glow`}
    >
      <article
        id="banner"
        style={{
          background: hexToRGBA(data.theme, 0.05),
        }}
        className={`flex items-stretch justify-between rounded-lg p-4`}
      >
        <img
          className="max-w-[100px] basis-1/4 rounded-full"
          src={data.logoURL}
          alt={data.name + ' Logo'}
        />
        <h1
          style={{ color: data.theme }}
          className="basis-1/4 self-center text-left text-xl font-semibold"
        >
          {data.name}
        </h1>
        <section className="flex basis-1/4 flex-col items-start gap-2 self-center text-sm">
          {data.approved ? (
            <Info text={'Status: Public'} Icon={MdPublic} />
          ) : (
            <Info text={'Status: Private'} Icon={GiPadlock} />
          )}
          <Info text={`Created: ${displayDate(data.createdAt)}`} Icon={AiFillPlusCircle} />
          <Info text={`Edited  : ${displayDate(data.updatedAt)}`} Icon={MdUpdate} />
        </section>
        <div id="actions" className="flex basis-1/4 flex-row-reverse gap-2 self-start">
          <Button
            variant="secondary"
            key="view"
            onClick={() => {
              toggleShowDetails();
            }}
          >
            {showDetails ? (
              <>
                <IoMdEyeOff /> Hide
              </>
            ) : (
              <>
                <IoMdEye /> View
              </>
            )}
          </Button>
          <Link to={`/developer/apps/${data._id}/edit`}>
            <Button variant="secondary" key="edit">
              <MdEdit /> Edit
            </Button>
          </Link>
          {!data.approved && (
            <Button
              variant="secondary"
              key="publish"
              onClick={() => {
                requestAppPublish(data);
              }}
            >
              <MdPublic /> Publish
            </Button>
          )}
        </div>
      </article>

      <article
        id="details"
        className={'grid grid-cols-2 gap-4 p-6' + ' ' + (!showDetails ? 'hidden' : '')}
      >
        <Field type="text" label="Client ID" Icon={MdInfo} value={data._id} disabled />
        <Field
          type="password"
          label="Client Secret"
          Icon={GiPadlock}
          defaultValue={data.secret}
          disabled
        />
        <Field
          type="url"
          label="Application Website"
          Icon={MdLink}
          defaultValue={data.website}
          disabled
        />
        <Field
          type="color"
          label="Application Brand Color"
          Icon={MdPalette}
          defaultValue={data.theme}
          disabled
        />
        <Field
          type="textarea"
          className="col-span-2"
          label="Application Summary"
          Icon={MdInfo}
          defaultValue={data.description}
          disabled
        />
        <Field type="text" label="Company Name" Icon={MdInfo} value={data.companyName} disabled />
        <Field
          type="url"
          label="Company Website"
          Icon={MdLink}
          defaultValue={data.companyWebsite}
          disabled
        />
        <Field
          type="url"
          label="Documentation URL"
          Icon={MdLink}
          defaultValue={data.documentationURL}
          disabled
        />
        <Field
          type="url"
          label="Privacy Policy URL"
          Icon={MdLink}
          defaultValue={data.privacyPolicyURL}
          disabled
        />
        <MultiField
          className="col-span-2"
          type="text"
          name="permisions"
          label="Permissions"
          Icon={MdInfo}
          defaultValues={data.permissions}
          disabled
        />
        <MultiField
          className="col-span-2"
          type="url"
          name="redirectUris"
          label="Redirect URIs"
          Icon={MdLink}
          defaultValues={data.redirectUris ?? []}
          disabled
        />
      </article>
    </article>
  );
}

export default function ViewApps() {
  const { data: apps } = useQuery<ReadonlyArray<OauthApp>>('oauth2-apps');
  return (
    <DashboardContainer
      title="OAuth2 Applications"
      actions={
        <>
          <Link to="/developer/apps/create">
            <Button className="flex gap-2">
              <FaRegSquarePlus />
              Create
            </Button>
          </Link>
          <a href="/developer/documentation">
            <Button className="flex gap-2" variant="secondary">
              <GrHelpBook />
              Help
            </Button>
          </a>
        </>
      }
    >
      <div className="space-y-2">
        {apps?.map((app: OauthApp) => <App key={app._id} data={app} />)}
      </div>
    </DashboardContainer>
  );
}
