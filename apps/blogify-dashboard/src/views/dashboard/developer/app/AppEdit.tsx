// بسم الله الرحمن الرحيم
import type { OauthApp } from '@/types/resources/oauth-app.type';

import { MdInfo, MdLink, MdPalette } from 'react-icons/md';
import { useNavigate, useParams } from 'react-router-dom';
import { FaFileImage, FaRegSave } from 'react-icons/fa';
import { ChangeEvent, useState } from 'react';
import { ValidationError } from 'class-validator';
import { useQuery } from 'react-query';
import { TbApi } from 'react-icons/tb';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import BlogifyLoader from '@/components/common/BlogifyLoader';

import { Field } from './components/Field';

type CreateAppDTO = Omit<OauthApp, '_id' | 'secret' | 'createdAt' | 'updatedAt'>;
type EditAppDTO = Partial<CreateAppDTO>;
function AppForm({
  defaultApp,
  updateApp,
  errors,
}: {
  defaultApp: OauthApp;
  updateApp: React.Dispatch<React.SetStateAction<EditAppDTO>>;
  errors?: Record<keyof EditAppDTO, string>;
}) {
  function onChangeHandler(event: ChangeEvent<HTMLInputElement>) {
    const { name, value } = event.target;
    switch (name) {
      case 'permissions':
        updateApp((previous) => ({
          ...previous,
          permissions: value
            .split(',')
            .map((permission) => permission.trim())
            .filter((permission) => permission.length > 0),
        }));
        break;
      case 'redirectURIs':
        updateApp((previous) => ({
          ...previous,
          redirectUris: value
            .split(',')
            .map((uri) => uri.trim())
            .filter((uri) => uri.length > 0),
        }));
        break;
      default:
        updateApp((previous) => ({
          ...previous,
          [name]: value,
        }));
    }
  }
  return (
    <article className="grid grid-cols-2 gap-4 p-6" onChange={onChangeHandler}>
      <Field
        type="text"
        inputMode="text"
        label="Application Name"
        name="name"
        Icon={MdInfo}
        defaultValue={defaultApp.name}
        placeholder="A unique name for your application not exceeding 3 words."
        minLength={1}
        maxLength={30}
        required
        error={errors?.name}
      />
      <Field
        type="url"
        inputMode="url"
        label="Application Logo"
        name="logoURL"
        Icon={FaFileImage}
        defaultValue={defaultApp.logoURL}
        placeholder="A link to your application's logo image."
        required
        error={errors?.logoURL}
      />
      <Field
        type="url"
        inputMode="url"
        label="Application Website"
        name="website"
        Icon={MdLink}
        defaultValue={defaultApp.website}
        placeholder="A link to your application's website."
        required
        error={errors?.website}
      />
      <Field
        type="color"
        label="Application Brand Color"
        name="theme"
        Icon={MdPalette}
        defaultValue={defaultApp.theme}
        required
        error={errors?.theme}
      />
      <Field
        className="col-span-2"
        type="textarea"
        inputMode="text"
        label="Application Description"
        name="description"
        Icon={MdInfo}
        defaultValue={defaultApp.description}
        placeholder="A short paragraph explaining how effortlessly your app integrates the functionalities provided by Blogify.ai and maximizes the value users obtain from both platforms."
        minLength={1}
        maxLength={300}
        required
        error={errors?.description}
      />
      <Field
        type="text"
        inputMode="text"
        label="Company Name"
        name="companyName"
        Icon={MdInfo}
        defaultValue={defaultApp.companyName}
        placeholder="The name of your organization developing this application."
        minLength={1}
        maxLength={30}
        required
        error={errors?.companyName}
      />
      <Field
        type="url"
        inputMode="url"
        label="Company Homepage"
        name="companyWebsite"
        Icon={MdInfo}
        defaultValue={defaultApp.companyWebsite}
        placeholder="A link to your company's homepage."
        required
        error={errors?.companyWebsite}
      />
      <Field
        type="url"
        inputMode="url"
        label="Documentation Link"
        name="documentationURL"
        Icon={MdLink}
        defaultValue={defaultApp.documentationURL}
        placeholder="A URL to the application's documentation page."
        required
        error={errors?.documentationURL}
      />
      <Field
        type="url"
        inputMode="url"
        label="Privacy Policy Link"
        name="privacyPolicyURL"
        Icon={MdLink}
        defaultValue={defaultApp.privacyPolicyURL}
        placeholder="A URL to the platforms privacy policy page."
        required
        error={errors?.privacyPolicyURL}
      />
      <Field
        type="textarea"
        className="col-span-2"
        inputMode="text"
        label="Redirect URIs"
        name="redirectURIs"
        Icon={MdLink}
        defaultValue={defaultApp.redirectUris}
        placeholder="A comma separated list of Redirect URIs for your application."
        required
        error={errors?.redirectUris}
      />
      {!defaultApp.approved && (
        <Field
          type="textarea"
          className="col-span-2"
          inputMode="text"
          label="Scopes"
          name="permissions"
          Icon={TbApi}
          defaultValue={defaultApp.permissions}
          placeholder="A comma separated list of scopes required by your application."
          required
          error={errors?.permissions}
        />
      )}
    </article>
  );
}

export default function EditApp() {
  const { id } = useParams();
  const { data: defaultValues, isLoading } = useQuery<OauthApp>(`oauth2-apps/${id}`);
  const [app, updateApp] = useState<EditAppDTO>({});
  const [errors, setErrors] = useState<Record<keyof EditAppDTO, string>>();
  const navigate = useNavigate();
  const updateErrors = (data: ValidationError[]) => {
    setErrors(
      data
        .map((error) => ({
          [error.property]: Object.values(error.constraints ?? {}).join(';'),
        }))
        .reduce((prev, curr) => ({ ...prev, ...curr }), {}) as Record<keyof EditAppDTO, string>
    );
  };

  return isLoading ? (
    <BlogifyLoader />
  ) : (
    <DashboardContainer
      title="Edit OAuth2 Application"
      cancelUrl="/developer/"
      actions={
        <Button
          className="flex gap-2"
          onClick={async () => {
            await API.patch(`/oauth2-apps/${id}`, app)
              .then(() => navigate('/developer/'))
              .catch(updateErrors);
          }}
        >
          <FaRegSave />
          Save
        </Button>
      }
    >
      <AppForm
        errors={errors}
        defaultApp={defaultValues ?? ({} as OauthApp)}
        updateApp={updateApp}
      />
    </DashboardContainer>
  );
}
