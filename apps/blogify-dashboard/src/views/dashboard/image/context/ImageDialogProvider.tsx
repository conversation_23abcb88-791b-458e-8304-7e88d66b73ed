import { createContext, useState } from 'react';

export type ImageSelectionTabs =
  | 'thumbnail'
  | 'library'
  | 'ai-generated'
  | 'unsplash'
  | 'uploaded'
  | '';

interface ImageDialogContextType {
  isOpen: boolean;
  toggleDialog: (open: boolean) => void;

  prompt: string;
  setPrompt: (prompt: string) => void;

  selectImages: (urls: string[]) => void;
  selectImage: (url: string) => void;
  removeImage: (url: string) => void;
  setCurrentTab: (tab: ImageSelectionTabs) => void;

  multiple: boolean;
  selectedImages: string[];
}

const ImageDialogContext = createContext<ImageDialogContextType | undefined>(undefined);

interface ImageDialogProviderProps {
  children: React.ReactNode | ((props: ImageDialogContextType) => React.ReactNode);
  multiple?: boolean;
  max?: number;

  onImageSelect?: (url: string, info: { selectedFrom: ImageSelectionTabs }) => void;
  onImagesSelect?: (urls: string[]) => void;
}

export const ImageDialogProvider: React.FC<ImageDialogProviderProps> = ({
  children,
  multiple,
  max = 12,

  onImageSelect,
  onImagesSelect,
}) => {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [currentTab, setCurrentTab] = useState<ImageSelectionTabs>('library');
  const [isOpen, toggleDialog] = useState<boolean>(false);
  const [prompt, setPrompt] = useState<string>('');

  const selectImage = (url: string) => {
    if (multiple) {
      if (selectedImages.length < max) {
        const imageSet = new Set(selectedImages);
        imageSet.add(url);
        setSelectedImages(Array.from(imageSet));
      }
    } else {
      if (onImageSelect) onImageSelect(url, { selectedFrom: currentTab });
      toggleDialog(false);
    }
  };

  const selectImages = (urls: string[]) => {
    if (onImagesSelect) {
      onImagesSelect(urls);
    }
    toggleDialog(false);
  };

  const removeImage = (url: string) => {
    setSelectedImages(selectedImages.filter((img) => img !== url));
  };

  const value = {
    isOpen,
    toggleDialog,

    prompt,
    setPrompt,

    selectImages,
    selectImage,
    removeImage,
    setCurrentTab,

    multiple: multiple || false,
    selectedImages,
  };

  return (
    <ImageDialogContext.Provider value={value}>
      {typeof children === 'function' ? children(value) : children}
    </ImageDialogContext.Provider>
  );
};

export { ImageDialogContext };
