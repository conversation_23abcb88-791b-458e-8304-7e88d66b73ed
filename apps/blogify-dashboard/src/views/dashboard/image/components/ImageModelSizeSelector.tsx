import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { cn } from '@ps/ui/lib/utils';

import { MODELS } from '../utils';

export default function ImageModelSizeSelector({
  model,
  defaultModel,
  setModel,
  defaultSizeId,
  setSizeId,
}: {
  model?: string;
  defaultModel?: string;
  setModel: (_: string) => void;
  defaultSizeId?: string;
  setSizeId: (_: string) => void;
}) {
  return (
    <RadioGroup
      className="mt-6 gap-0 rounded-lg border border-gray10"
      defaultValue={defaultModel}
      onValueChange={(v) => {
        setModel(v);
        setSizeId(String(MODELS[v as keyof typeof MODELS].resolutions[0].id));
      }}
    >
      {(Object.keys(MODELS) as (keyof typeof MODELS)[]).map((m, i) => (
        <div key={i} className={cn('p-4', { 'border-t border-gray10': i > 0 })}>
          <ModelSelector model={m} />
          {model === m && (
            <ResolutionSelector model={m} defaultValue={defaultSizeId} onChange={setSizeId} />
          )}
        </div>
      ))}
    </RadioGroup>
  );
}

const ModelSelector = ({ model }: { model: keyof typeof MODELS }) => (
  <label className="flex items-center justify-between">
    <div className="flex items-center gap-4">
      <RadioGroupItem value={model} />

      <div className="flex items-center gap-2">
        <img className="size-5 rounded-full" src={MODELS[model].icon} />
        <span
          className="text-md font-medium text-black4"
          dangerouslySetInnerHTML={{ __html: MODELS[model].name }}
        />
      </div>
    </div>

    <div className="text-sm text-gray9">{MODELS[model].costRange}</div>
  </label>
);

const ResolutionSelector = ({
  defaultValue,
  model,
  onChange,
}: {
  defaultValue?: string;
  model: keyof typeof MODELS;
  onChange: (size: string) => void;
}) => (
  <RadioGroup
    className="mt-4 rounded-md bg-cornflowerBlue py-2"
    defaultValue={defaultValue || String(MODELS[model].resolutions[0].id)}
    onValueChange={onChange}
  >
    {MODELS[model].resolutions.map(({ id, text, cost }, i) => (
      <label key={i} className="flex items-center justify-between px-3 py-1">
        <div className="flex items-center gap-4">
          <RadioGroupItem value={String(id)} />
          <span className="text-sm text-gray9">{text}</span>
        </div>

        <div className="text-sm text-gray9">
          {cost} credit{cost > 1 ? 's' : ''}
        </div>
      </label>
    ))}
  </RadioGroup>
);
