import { cn } from '@ps/ui/lib/utils';

import { STYlES } from '../utils';

export default function ImageStyleSelector({
  value,
  onChange,
}: {
  value?: string;
  onChange: (_: string) => void;
}) {
  return (
    <div className="grid grid-cols-4 gap-4 xs:grid-cols-5">
      {STYlES.map((style, i) => (
        <button
          className={cn(
            'relative col-span-1 size-20 overflow-hidden rounded-md border border-transparent',
            { 'ring-2 ring-primary': value === style }
          )}
          onClick={() => onChange(style)}
          title={style}
          type="button"
          key={i}
        >
          <img className="size-full object-cover" src={`/images/image-styles/${style}.png`} />
          <div className="absolute bottom-0 h-5 w-full truncate rounded-b-md bg-black/60 px-2 text-center text-sm font-medium text-white">
            {style}
          </div>
        </button>
      ))}
    </div>
  );
}
