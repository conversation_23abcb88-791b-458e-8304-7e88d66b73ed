'use client';

import { useMutation } from 'react-query';
import { useState } from 'react';

import { useStoreActions } from '@/store';
import { zodResolver, z } from '@ps/ui';
import { DialogFooter } from '@ps/ui/components/dialog';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import FormField from '@ps/ui/form/FormField';

import { imageGenerateCommonSchema, defaultValues, MODELS } from '../utils';
import ImageModelSizeSelector from './ImageModelSizeSelector';
import ImageStyleSelector from './ImageStyleSelector';
import useImageDialog from '../context/useImageDialog';

const imageGenerateSchema = z.object({
  prompt: z.string({ required_error: 'Prompt is required' }),
  ...imageGenerateCommonSchema,
});
type ImageGenerateSchema = z.infer<typeof imageGenerateSchema>;

export default function ImageGenerate() {
  const { selectImage, prompt } = useImageDialog();
  const [error, setError] = useState<string | null>(null);
  const fetchUser = useStoreActions((u) => u.user.fetch);

  const { getInputFields, setValue, handleSubmit, watch, getValues } = useForm<ImageGenerateSchema>(
    {
      resolver: zodResolver(imageGenerateSchema),
      defaultValues: { prompt: prompt || '', ...defaultValues },
    }
  );

  const { mutate: generateImage, isLoading } = useMutation({
    mutationFn: async (data: ImageGenerateSchema) => {
      const _id = parseInt(data.sizeId, 10);
      const modelName = data.model as keyof typeof MODELS;
      const size = MODELS[modelName].resolutions.find((r) => r.id === _id);
      data.provider = MODELS[modelName].provider;
      data.quality = size?.quality || 'standard';
      data.size = size?.size || '1024x1024';
      return API.post<{ url: string }>('images/generate', data);
    },
    onSuccess: (response) => {
      if (response?.url) {
        selectImage(response.url);
        fetchUser();
      }
    },
    onError: (error: string) => {
      setError(error);
    },
  });

  const getSelectedSize = () => {
    const _id = parseInt(watch('sizeId'), 10);
    return MODELS[watch('model') as keyof typeof MODELS].resolutions.find((r) => r.id === _id);
  };

  const onSubmit = (ev: React.FormEvent<HTMLFormElement>) => {
    ev.stopPropagation();
    return handleSubmit((v) => generateImage(v))(ev);
  };

  return (
    <form className="mt-4" onSubmit={onSubmit}>
      <div className="relative [&>img]:focus-within:!grayscale-0">
        <img
          className="absolute left-3.5 top-3.5 z-10 size-3 grayscale transition-all"
          src="/images/icons/stars.svg"
        />

        <FormField
          placeholder="Type what kind image you want"
          className="indent-5 placeholder:indent-5"
          defaultValue={getValues('prompt')}
          type="textarea"
          {...getInputFields('prompt')}
        />
      </div>

      <ImageStyleSelector value={watch('style')} onChange={(s) => setValue('style', s)} />
      <ImageModelSizeSelector
        model={watch('model')}
        defaultModel={getValues('model')}
        setModel={(v) => setValue('model', v)}
        defaultSizeId={getValues('sizeId')}
        setSizeId={(v) => setValue('sizeId', v)}
      />

      <DialogFooter className="mt-6 !flex-col gap-2 !space-x-0">
        {error && <div className="text-sm font-medium text-red4">{error}</div>}
        <Button className="w-full" type="submit" loading={isLoading}>
          Generate Image for {getSelectedSize()?.cost || 0} Credit
        </Button>
      </DialogFooter>
    </form>
  );
}
