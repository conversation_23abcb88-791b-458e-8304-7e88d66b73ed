import ImageCardForDialog from './ImageCardForDialog';
import useImageLibrary from '../hooks/useImageLibrary';
// import ImageSearchBox from './ImageSearchBox';
import ImageGrid from './ImageGrid';

// const CHIPS = ['cover', 'smile', 'beautiful', 'follow', 'portrait', 'photo', 'game'];

export default function ImageLibrary() {
  const { images, isFetching, isFetchingNextPage, handleScroll } = useImageLibrary();

  return (
    <div className="mt-4">
      {/* <ImageSearchBox onSearch={() => {}} /> */}

      {/* <div className="mt-4 flex max-w-[464px] gap-2 overflow-scroll">
        {CHIPS.map((c, i) => (
          <div
            key={i}
            className="flex h-7 items-center rounded-[14px] border border-gray10 px-3.5 text-sm font-medium"
          >
            {c}
          </div>
        ))}
      </div> */}

      <ImageGrid
        className="max-h-[512px] overflow-y-scroll"
        isFetchingMore={isFetchingNextPage}
        isFetching={isFetching}
        images={images}
        onScrollEnd={handleScroll}
        Card={ImageCardForDialog}
      />
    </div>
  );
}
