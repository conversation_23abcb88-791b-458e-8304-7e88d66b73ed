import type { Image } from '@ps/types';

import { MdDownload, MdMoreVert, MdDelete } from 'react-icons/md';
import { FaRegHeart, FaHeart } from 'react-icons/fa';
import { useMutation } from 'react-query';
import { useToggle } from 'react-use';
import { saveAs } from 'file-saver';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';

export default function ImageCard({
  image: { _id, alt, url, thumbnailUrl, downloadUrl, copyright, isFavorite: initialIsFavorite },
  refetch,
}: {
  image: Image;
  refetch?: () => void;
}) {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [isFavorite, toggleIsFavorite] = useToggle(initialIsFavorite || false);
  const { mutate: toggleFavorite } = useMutation({
    mutationFn: () => {
      toggleIsFavorite();
      return API.patch(`images/${_id}`, { isFavorite: !isFavorite });
    },
    onSuccess: () => refetch?.(),
  });

  const {
    mutateAsync: deleteImage,
    isLoading: deleting,
    error,
  } = useMutation({
    mutationFn: () =>
      API.remove(`images/${_id}`).then(() => {
        if (refetch) {
          refetch();
        }
        toggleConfirmationDialogOpen();
      }),
  });

  return (
    <div className="group relative mb-2 overflow-hidden rounded-lg">
      <img className="w-full" src={thumbnailUrl ?? url} />

      <div className="absolute right-3 top-4 z-10 flex gap-2 text-white">
        <button onClick={() => toggleFavorite()}>
          {isFavorite ? (
            <FaHeart size={16} className="hover:cursor-pointer" />
          ) : (
            <FaRegHeart size={16} className="hover:cursor-pointer" />
          )}
        </button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex size-5 rounded bg-black/60 flex-center">
              <MdMoreVert size={16} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={toggleConfirmationDialogOpen} className="text-red4">
              <MdDelete className="-mt-px" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="absolute left-0 top-full flex size-full flex-col justify-between bg-black/50 p-3 text-white opacity-0 transition-all duration-300 ease-in-out group-hover:top-0 group-hover:opacity-100">
        <div className="flex justify-between">
          <div className="flex items-center gap-2">
            <img className="size-3.5" src="/public/images/icons/stars.svg" />
            <p className="line-clamp-2 text-md font-medium">AI Generated</p>
          </div>
        </div>

        <div>
          <p className={cn('text-md font-medium', copyright ? 'line-clamp-1' : 'line-clamp-2')}>
            {alt}
          </p>
          <p className="my-1 text-xs font-medium">{copyright}</p>
          <Button
            size="xs"
            className="w-full"
            onClick={() => saveAs(downloadUrl ?? url, getFileNameFromUrl(url))}
          >
            <MdDownload /> Download
          </Button>
        </div>
      </div>

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to delete this image?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={deleteImage}
        error={error as string}
        confirming={deleting}
      />
    </div>
  );
}

const getFileNameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);

    const pathname = urlObj.pathname;
    const fileName = pathname.substring(pathname.lastIndexOf('/') + 1);

    return fileName || 'unknown';
  } catch (error) {
    console.error('Invalid URL:', error);
    return 'image.jpg';
  }
};
