import type { Image } from '@ps/types';

import { Button } from '@ps/ui/components/button';

import useImageDialog from '../context/useImageDialog';

export default function ImageCardForDialog({
  image: { alt, url, copyright, thumbnailUrl },
}: {
  image: Image;
}) {
  const { selectImage } = useImageDialog();

  return (
    <div className="group relative mb-4 min-h-[126px] overflow-hidden rounded-lg bg-[#F8F7F6]">
      <img className="w-full" src={thumbnailUrl ?? url} />
      <div className="absolute left-0 top-full flex size-full flex-col justify-between bg-black/70 p-3 text-white opacity-0 transition-all duration-300 ease-in-out group-hover:top-0 group-hover:opacity-100">
        <div className="flex flex-col">
          <p className="line-clamp-2 text-md font-medium">{alt}</p>
          <p className="mt-1 truncate text-sm">{copyright}</p>
        </div>
        <Button className="bg-white text-black hover:text-white" onClick={() => selectImage(url)}>
          Insert
        </Button>
      </div>
    </div>
  );
}
