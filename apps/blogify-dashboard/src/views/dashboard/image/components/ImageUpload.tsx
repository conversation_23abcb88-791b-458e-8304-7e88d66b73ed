import type { FileType } from '@/modules/file-upload/useFileUpload';

import { FileUploadProgress, FileUpload } from '@/modules/file-upload/FileUpload';
import { API } from '@/services/api';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';

import useImageDialog from '../context/useImageDialog';

export default function ImageUpload() {
  const { selectImage } = useImageDialog();

  const onUploadComplete = async (files: FileType[]) => {
    const promises = files.map(async (file) => {
      const { url } = file;
      const body = {
        url,
        alt: file.name,
        fileSize: file.size,
        fileType: file.type,
        resolution: file.resolution,
      };
      return API.post('images', body);
    });

    const result = await Promise.all(promises);
    const firstResult = result[0] as { url: string };

    if (firstResult?.url) {
      selectImage(firstResult.url);
    }
  };

  return (
    <FileUploadProvider
      onUploadComplete={onUploadComplete}
      folder="blog/content-images"
      type="image"
    >
      {({ files, isUploading }) => (
        <div className="mt-4 rounded-lg border border-dashed border-gray10">
          {!files.length && (
            <FileUpload
              supportedFormats=".jpeg, .jpg, .png, .gif, .bmp, .tiff, .tif, .webp, .svg, .heic, .heif"
              className="sm:min-h-64"
            />
          )}

          {isUploading && <FileUploadProgress className="sm:min-h-96" />}
        </div>
      )}
    </FileUploadProvider>
  );
}
