import type { Image } from '@ps/types';

import React from 'react';

import { cn } from '@ps/ui/lib/utils';
import Spinner from '@/components/misc/Spinner';

interface ImageGridProps {
  images: Image[];
  isFetching?: boolean;
  isFetchingMore?: boolean;
  isWideView?: boolean;
  Card: React.FC<{ image: Image; refetch?: () => void }>;
  className?: string;
  onScrollEnd?: () => void;
  refetch?: () => void;
}

export default function ImageGrid({
  Card,
  images,
  isFetching,
  isFetchingMore,
  isWideView = false,
  onScrollEnd,
  refetch,
  className,
}: ImageGridProps) {
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    if (onScrollEnd) {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      if (Math.abs(scrollHeight - (scrollTop + clientHeight)) <= 1) {
        onScrollEnd();
      }
    }
  };

  return (
    <div className={cn('mt-6', className)} onScroll={handleScroll}>
      {isFetching && images.length === 0 && (
        <div className="flex py-6 flex-center">
          <Spinner />
        </div>
      )}

      {!isFetching && images.length === 0 && (
        <div className="flex rounded-lg border border-gray10 py-40 flex-center">
          <p className="px-3 text-center text-md text-gray9">
            Looks like you don't have any images on your library yet.
          </p>
        </div>
      )}

      <div
        className={cn('columns-2', { 'gap-2 sm:columns-3 md:columns-4 xl:columns-5': isWideView })}
      >
        {images.map((image, i) => (
          <Card key={i} image={image} refetch={refetch} />
        ))}
      </div>

      {isFetchingMore && (
        <div className="flex py-6 flex-center">
          <Spinner />
        </div>
      )}
    </div>
  );
}
