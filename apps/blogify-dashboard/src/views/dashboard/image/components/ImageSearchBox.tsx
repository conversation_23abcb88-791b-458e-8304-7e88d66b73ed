import { MdSearch } from 'react-icons/md';
import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';

interface ImageSearchBoxProps {
  onSearch: (query: string) => void;
}

export default function ImageSearchBox({ onSearch }: ImageSearchBoxProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = () => {
    onSearch(searchQuery);
  };

  return (
    <div className="flex items-center rounded-lg border border-gray10">
      <div className="flex size-10 shrink-0 border-r border-gray10 flex-center">
        <MdSearch size={16} className="text-primary" />
      </div>
      <Input
        className="border-none placeholder:text-gray9"
        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
        onChange={(e) => setSearchQuery(e.target.value)}
        placeholder="Enter a keyword"
        value={searchQuery}
      />
      <Button size="sm" className="m-1" onClick={handleSearch}>
        Search
      </Button>
    </div>
  );
}
