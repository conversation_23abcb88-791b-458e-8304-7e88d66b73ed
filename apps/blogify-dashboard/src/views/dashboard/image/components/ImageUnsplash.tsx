import type { Image } from '@ps/types';

import { useCallback, useState } from 'react';
import { createApi, OrderBy } from 'unsplash-js';
import { useInfiniteQuery } from 'react-query';

import ImageCardForDialog from './ImageCardForDialog';
import ImageSearchBox from './ImageSearchBox';
import ImageGrid from './ImageGrid';

const accessKey = import.meta.env.VITE_UNSPLASH_ACCESS_KEY as string;
const unsplash = createApi({ accessKey });

const fetchUnsplashImages = async ({
  pageParam = 1,
  queryKey,
}: { pageParam: number; queryKey: string[] } | any): Promise<{
  images: Image[];
  totalPages: number;
  nextPage: number;
}> => {
  const [, query] = queryKey;
  const params = {
    page: pageParam,
    perPage: 12,
  };
  const result = query
    ? await unsplash.search.getPhotos({ query, ...params })
    : await unsplash.photos.list({ orderBy: OrderBy.POPULAR, ...params });

  return {
    images:
      result.response?.results.map((photo) => ({
        alt: photo.description || photo.alt_description || '',
        thumbnail: photo.urls.thumb,
        copyright: photo.user.name,
        url: photo.urls.regular,
        type: 'unsplash',
        _id: photo.id,
      })) || [],
    totalPages: result.response?.total || 1,
    nextPage: pageParam + 1,
  };
};

export default function ImageUnsplash() {
  const [searchQuery, setSearchQuery] = useState('');

  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage } = useInfiniteQuery({
    queryKey: ['unsplash-images', searchQuery],
    queryFn: fetchUnsplashImages,
    getNextPageParam: (lastPage) =>
      lastPage?.nextPage <= lastPage?.totalPages ? lastPage?.nextPage : undefined,
    enabled: true,
  });
  const allImages = data?.pages.flatMap((page) => page.images) || [];

  const handleScroll = useCallback(() => {
    if (isFetchingNextPage || !hasNextPage) return;
    fetchNextPage();
  }, [isFetchingNextPage, fetchNextPage, hasNextPage]);

  return (
    <div className="mt-4">
      <ImageSearchBox onSearch={setSearchQuery} />

      <ImageGrid
        className="max-h-[512px] overflow-y-scroll"
        isFetchingMore={isFetchingNextPage}
        isFetching={isLoading}
        images={allImages}
        onScrollEnd={handleScroll}
        Card={ImageCardForDialog}
      />
    </div>
  );
}
