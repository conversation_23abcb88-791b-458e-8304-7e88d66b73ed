import { FiUploadCloud } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';

import { TabsContent, TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { appendToQuery } from '@/services/api/query';
import { Button } from '@ps/ui/components/button';
import Spinner from '@/components/misc/Spinner';

import { ImageDialogProvider } from './context/ImageDialogProvider';
import DashboardContainer from '../layout/DashboardContainer';
import ImageInsertDialog from './ImageInsertDialog';
import useImageLibrary from './hooks/useImageLibrary';
import ImageGrid from './components/ImageGrid';
import ImageCard from './components/ImageCard';

const TABS: { name: string; query: Record<string, string> }[] = [
  { name: 'All Images', query: {} },
  { name: 'AI Generated', query: { type: 'generated' } },
  { name: 'Uploaded', query: { type: 'uploaded' } },
  { name: 'Favorites', query: { isFavorite: 'true' } },
];

const getDefaultTab = () => {
  if (window.location.search.includes('type=generated')) {
    return 'AI Generated';
  } else if (window.location.search.includes('type=uploaded')) {
    return 'Uploaded';
  } else if (window.location.search.includes('isFavorite=true')) {
    return 'Favorites';
  }
  return 'All Images';
};

export default function ImageList() {
  const {
    refetch: refetchImages,
    isFetchingNextPage,
    handleScroll,
    isFetching,
    images,
  } = useImageLibrary();
  const navigate = useNavigate();

  const defaultBlogCounts = { 'All Images': 0, 'AI Generated': 0, Uploaded: 0, Favorites: 0 };
  const { data: imageCounts = defaultBlogCounts, refetch: refetchCounts } = useQuery<
    Record<string, number>
  >(['images/counts']);

  const refetch = () => {
    setTimeout(() => {
      refetchImages();
      refetchCounts();
    }, 300);
  };

  return (
    <ImageDialogProvider onImageSelect={() => refetch()}>
      {({ toggleDialog }) => (
        <>
          <DashboardContainer
            className="min-h-[unset] pb-0"
            title="Image Library"
            actions={
              <Button variant="secondary" onClick={() => toggleDialog(true)}>
                <FiUploadCloud size={20} />
                Add Image
              </Button>
            }
          >
            <Tabs defaultValue={getDefaultTab()}>
              <TabsList variant="nav">
                {TABS.map((t) => (
                  <TabsTrigger
                    key={t.name}
                    variant="nav"
                    value={t.name}
                    onClick={() => {
                      navigate({ search: appendToQuery(t.query, true) });
                      setTimeout(() => {
                        refetch();
                      }, 100);
                    }}
                  >
                    {t.name}{' '}
                    {imageCounts[t.name as keyof typeof imageCounts]
                      ? `(${imageCounts[t.name as keyof typeof imageCounts]})`
                      : ''}
                  </TabsTrigger>
                ))}
              </TabsList>

              {TABS.map((t) => (
                <TabsContent value={t.name} key={t.name}>
                  {!images.length ? (
                    <ImageNoEntries />
                  ) : (
                    <ImageGrid
                      className="h-[calc(100vh-206px)] overflow-y-scroll"
                      isFetchingMore={isFetchingNextPage}
                      isFetching={isFetching}
                      images={images}
                      isWideView
                      onScrollEnd={handleScroll}
                      refetch={refetch}
                      Card={ImageCard}
                    />
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </DashboardContainer>

          <ImageInsertDialog minimal />
        </>
      )}
    </ImageDialogProvider>
  );
}

const ImageNoEntries = ({ loading, className }: { loading?: boolean; className?: string }) => (
  <div className={`flex min-h-[65vh] flex-col gap-4 p-5 flex-center ${className}`}>
    {loading ? (
      <Spinner />
    ) : (
      <>
        <img src="/images/icons/sad.svg" />
        <div className="text-center text-lg">
          Looks like you don't have any images that matches your filter.
        </div>
      </>
    )}
  </div>
);
