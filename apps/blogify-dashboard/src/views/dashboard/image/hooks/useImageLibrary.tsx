import type { APIResponseType } from '@/types/resources';
import type { Image } from '@ps/types';

import { useInfiniteQuery } from 'react-query';
import { useCallback } from 'react';

import { API } from '@/services/api';

const fetchImages = async ({
  pageParam = 0,
}: { pageParam: number; queryKey: string[] } | any): Promise<{
  images: Image[];
  totalPages: number;
  nextPage: number;
  total: number;
}> => {
  const LIMIT = 20;
  const response = await API.fetch<APIResponseType<Image>>(
    `images${window.location.search ? `${window.location.search}&` : '?'}limit=${LIMIT}&offset=${pageParam * LIMIT}`
  );

  return {
    images: response?.data || [],
    totalPages: Math.ceil((response?.total || 0) / LIMIT) || 1,
    nextPage: pageParam + 1,
    total: response?.total || 0,
  };
};

export default function useImageLibrary() {
  const { data, fetchNextPage, refetch, hasNextPage, isFetching, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ['images'],
      queryFn: fetchImages,
      getNextPageParam: (lastPage) =>
        lastPage?.nextPage < lastPage?.totalPages ? lastPage?.nextPage : undefined,
      enabled: true,
    });

  const images = data?.pages.flatMap((page) => page.images) || [];
  const total = data?.pages[0].total || 0;

  const handleScroll = useCallback(() => {
    if (isFetchingNextPage || !hasNextPage) return;
    fetchNextPage();
  }, [isFetchingNextPage, fetchNextPage, hasNextPage]);

  return {
    isFetchingNextPage,
    isFetching,
    images,
    total,
    refetch,
    handleScroll,
  };
}
