import { z } from '@ps/ui';

type ImageModelSettings = {
  name: string;
  provider: string;
  icon: string;
  costRange: string;
  resolutions: {
    id: number;
    size: string;
    text: string;
    quality: string;
    cost: number;
  }[];
};

export const STYlES = [
  'Anime',
  'Cyberpunk',
  'Fantasy',
  'Fine Art',
  'Horror',
  'Landscape',
  'Photorealistic',
  'Portraits',
  'Sci-Fi',
  'Surrealism',
];

// OpenAI Models
export const OPENAI_IMAGE_MODELS = {
  DALLE_2: 'dall-e-2',
  DALLE_3: 'dall-e-3',
  GPT_IMAGE_1: 'gpt-image-1',
};

// Google Models
export const GOOGLE_IMAGE_MODELS = {
  GEMINI_IMAGE_GENERATION: 'gemini-2.0-flash-preview-image-generation',
  IMAGEN_3: 'imagen-3.0-generate-002',
};

export const MODELS: Record<string, ImageModelSettings> = {
  [OPENAI_IMAGE_MODELS.GPT_IMAGE_1]: {
    name: 'GPT Image 1',
    provider: 'openai',
    icon: '/images/icons/chatgpt.svg',
    costRange: '4 - 6 credits per image',
    resolutions: [
      { id: 8, size: '1024x1024', text: '1024 x 1024px (Square)', quality: 'standard', cost: 2 },
      { id: 9, size: '1024x1536', text: '1024 x 1536px (Portrait)', quality: 'standard', cost: 3 },
      { id: 10, size: '1536x1024', text: '1536 x 1024px (Wide)', quality: 'standard', cost: 3 },
      { id: 11, size: '1024x1024', text: '1024 x 1024px (Square) HD', quality: 'hd', cost: 4 },
      { id: 12, size: '1024x1536', text: '1024 x 1536px (Portrait) HD', quality: 'hd', cost: 5 },
      { id: 13, size: '1536x1024', text: '1536 x 1024px (Wide) HD', quality: 'hd', cost: 5 },
    ],
  },
  [OPENAI_IMAGE_MODELS.DALLE_3]: {
    name: 'Dall&middot;E 3',
    provider: 'dalle',
    icon: '/images/icons/chatgpt.svg',
    costRange: '3 - 8 credits per image',
    resolutions: [
      { id: 2, size: '1024x1024', text: '1024 x 1024px (Square)', quality: 'standard', cost: 3 },
      { id: 3, size: '1024x1024', text: '1024 x 1024px (Square) HD', quality: 'hd', cost: 5 },

      { id: 4, size: '1024x1792', text: '1024 x 1792px (Portrait)', quality: 'standard', cost: 5 },
      { id: 5, size: '1024x1792', text: '1024 x 1792px (Portrait) HD', quality: 'hd', cost: 8 },

      { id: 6, size: '1792x1024', text: '1792 x 1024px (Wide)', quality: 'standard', cost: 5 },
      { id: 7, size: '1792x1024', text: '1792 x 1024px (Wide) HD', quality: 'hd', cost: 8 },
    ],
  },
  [OPENAI_IMAGE_MODELS.DALLE_2]: {
    name: 'Dall&middot;E 2',
    provider: 'dalle',
    icon: '/images/icons/chatgpt.svg',
    costRange: '1 credit per image',
    resolutions: [
      { id: 1, size: '1024x1024', text: '1024 x 1024px (Square)', quality: 'standard', cost: 1 },
    ],
  },
  [GOOGLE_IMAGE_MODELS.GEMINI_IMAGE_GENERATION]: {
    name: 'Gemini Image',
    provider: 'google',
    icon: '/icons/gemini.svg',
    costRange: '3 - 5 credits per image',
    resolutions: [
      { id: 14, size: '1024x1024', text: '1024 x 1024px (Square)', quality: 'standard', cost: 2 },
      { id: 15, size: '1024x1792', text: '1024 x 1792px (Portrait)', quality: 'standard', cost: 3 },
      { id: 16, size: '1792x1024', text: '1792 x 1024px (Wide)', quality: 'standard', cost: 3 },
    ],
  },
  [GOOGLE_IMAGE_MODELS.IMAGEN_3]: {
    name: 'Imagen 3',
    provider: 'google',
    icon: '/icons/imagine.jpg',
    costRange: '3 - 5 credits per image',
    resolutions: [
      { id: 17, size: '1024x1024', text: '1024 x 1024px (Square)', quality: 'standard', cost: 2 },
      { id: 18, size: '1024x1792', text: '1024 x 1792px (Portrait)', quality: 'standard', cost: 3 },
      { id: 19, size: '1792x1024', text: '1792 x 1024px (Wide)', quality: 'standard', cost: 3 },
    ],
  },
  // stablediffusion: {
  //   name: 'Stable Diffusion',
  //   provider: 'stablediffusion',
  //   icon: '/images/icons/stable-diffusion.png',
  //   costRange: '1 credit per image',
  //   resolutions: [{ id: 'stablediffusion', size: '1024x1024', text: '1024 x 1024px', cost: 1 }],
  // },
};

export const imageGenerateCommonSchema = {
  style: z.string(),
  provider: z.string({ required_error: 'Provider is required' }),
  model: z.string(),
  sizeId: z.string(),
  size: z.string(),
  quality: z.string(),
};

export const defaultValues = {
  style: 'Anime',
  provider: 'dalle',
  model: OPENAI_IMAGE_MODELS.DALLE_3,
  sizeId: '2',
  size: '1024x1024',
  quality: 'standard',
};
