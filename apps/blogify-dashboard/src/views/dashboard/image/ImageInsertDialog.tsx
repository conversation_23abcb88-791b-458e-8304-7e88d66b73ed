import type { ImageSelectionTabs } from './context/ImageDialogProvider';

import { useEffect } from 'react';
import { MdDelete } from 'react-icons/md';
import { FaImage } from 'react-icons/fa6';

import { TabsTrigger, TabsContent, TabsList, Tabs } from '@ps/ui/components/tabs';
import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/dialog';

import useImageDialog from './context/useImageDialog';
import ImageGenerate from './components/ImageGenerate';
import ImageUnsplash from './components/ImageUnsplash';
import ImageLibrary from './components/ImageLibrary';
import ImageUpload from './components/ImageUpload';

export default function ImageInsertDialog({
  children,
  prompt,
  minimal = false,
  title,
  description,
  thumbnail,
  defaultTab,
}: {
  children?: React.ReactNode;
  prompt?: string;
  minimal?: boolean;
  title?: string;
  description?: string;
  thumbnail?: string;
  defaultTab?: ImageSelectionTabs;
}) {
  const {
    isOpen,
    multiple,
    selectedImages,
    setPrompt,
    removeImage,
    selectImage,
    selectImages,
    toggleDialog,
    setCurrentTab,
  } = useImageDialog();

  const initialTab: ImageSelectionTabs = defaultTab
    ? defaultTab
    : minimal
      ? 'ai-generated'
      : 'library';

  useEffect(() => {
    if (prompt) {
      setPrompt(prompt);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prompt]);

  useEffect(() => {
    if (isOpen) setCurrentTab(initialTab);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={toggleDialog}
      Icon={FaImage}
      title={title || `${minimal ? 'Add' : 'Insert'} Image`}
      description={description || 'Insert content image into the blog.'}
      trigger={children}
    >
      {multiple && !!selectedImages.length && (
        <div className="mb-6">
          <label className="mb-2 text-15 font-medium">Selected Images</label>
          <div className="flex flex-wrap gap-3">
            {selectedImages.map((img, i) => (
              <div key={i} className="relative overflow-hidden [&>div]:hover:translate-y-0">
                <img className="aspect-video h-20 rounded-lg object-cover" src={img} />
                <Button
                  className="absolute -right-2 bottom-1 h-6 rounded-md text-red5 "
                  onClick={() => removeImage(img)}
                  variant="icon"
                >
                  <span className="rounded-lg bg-white p-1 shadow-md hover:p-1.5">
                    <MdDelete className="size-4" />
                  </span>
                </Button>
              </div>
            ))}
          </div>

          <Button className="mt-3 w-full" onClick={() => selectImages(selectedImages)}>
            Insert These Images
          </Button>
        </div>
      )}

      <Tabs defaultValue={initialTab} onValueChange={(t) => setCurrentTab(t as ImageSelectionTabs)}>
        <TabsList>
          {thumbnail && <TabsTrigger value="thumbnail">Thumbnail</TabsTrigger>}
          {!minimal && <TabsTrigger value="library">Library</TabsTrigger>}
          <TabsTrigger value="ai-generated">AI Image</TabsTrigger>
          {!minimal && <TabsTrigger value="unsplash">Unsplash</TabsTrigger>}
          <TabsTrigger value="uploaded">Upload</TabsTrigger>
        </TabsList>

        <TabsContent value="thumbnail">
          <img className="w-full rounded-lg" src={thumbnail} />
          <Button className="mt-6 w-full" onClick={() => selectImage(thumbnail || '')}>
            Select
          </Button>
        </TabsContent>

        <TabsContent value="library">
          <ImageLibrary />
        </TabsContent>

        <TabsContent value="ai-generated">
          <ImageGenerate />
        </TabsContent>

        <TabsContent value="unsplash">
          <ImageUnsplash />
        </TabsContent>
        <TabsContent value="uploaded">
          <ImageUpload />
        </TabsContent>
      </Tabs>
    </Dialog>
  );
}
