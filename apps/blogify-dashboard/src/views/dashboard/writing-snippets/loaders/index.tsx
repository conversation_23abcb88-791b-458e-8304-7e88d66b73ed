import { redirect } from 'react-router-dom';

import { IWiringSnippetsAndCategories } from '@/types/resources/writing-snippets.type';
import { API } from '@/services/api';
import Store from '@/store';

export const writingSnippetsLoader = ({ params }: { params?: string }) => {
  if (!Store.getState().user.current.hasSnippetsAddon)
    return redirect('/dashboard/writing-snippets/addon');

  return params ? null : redirect('/dashboard/writing-snippets/all');
};

export const generateSnippetsLoader = async ({ params }: { params: string | undefined }) => {
  const snippets = (await API.fetch(`/context/app`, {
    title: params,
  })) as IWiringSnippetsAndCategories;
  const snippet = snippets.snippetTypes.find(
    (item) => item.title.toLowerCase() === params?.toLowerCase()
  );

  // const snippet = snippetTypes.find((item) => item.title.toLowerCase() === params?.toLowerCase());
  return { snippet };
};
