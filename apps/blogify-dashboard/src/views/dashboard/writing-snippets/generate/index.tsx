import type { IWritingSnippetType } from '@/types/resources/writing-snippets.type';

import { useEffect, useState } from 'react';
import { BsFillBookmarkFill } from 'react-icons/bs';
import { useLoaderData } from 'react-router-dom';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';
import { DashboardContainer } from '@/views/dashboard/layout';
import { Button } from '@ps/ui/components/button';
import Link from '@/components/common/Link';

import SnippetsGenerateCardDetails from '../components/SnippetsGenerate/SnippetsGenerateCardDetails';
import SnippetsGenerateProvider from '../components/SnippetsGenerate/SnippetsGenerateProvider';
import SnippetsGenerateResult from '../components/SnippetsGenerate/ResultList/SnippetsGenerateResult';
import SnippetsGenerateForm from '../components/SnippetsGenerate/SnippetsGenerateForm';

const initialBreadcrumb = [
  {
    href: '/dashboard',
    title: 'Dashboard',
  },
  {
    href: '/dashboard/writing-snippets/all',
    title: 'Writing Snippets',
  },
  {
    href: '/dashboard/writing-snippets',
    title: 'Marketing',
  },
];

export default function GenerateWritingSnippets() {
  // load data using useLoaderData hook
  const { snippet } = useLoaderData() as { snippet: IWritingSnippetType };
  // Update breadcrumb
  const [breadcrumb, setBreadCrumb] = useState(initialBreadcrumb);
  useEffect(() => {
    if (!snippet) return;
    setBreadCrumb([
      ...initialBreadcrumb.slice(0, 2),
      {
        href: `/dashboard/writing-snippets/${snippet.category.name}`,
        title: snippet.category.name.replace('_', ' '),
      },
      {
        href: `/dashboard/writing-snippets/${snippet.category.name}/generate`,
        title: 'Generate',
      },
    ]);
  }, [snippet]);

  // Primary action button
  const primaryActionButton = (
    <Link to="/dashboard/writing-snippets/saved">
      <Button className="bg-green">
        <div className="flex items-center gap-1">
          <BsFillBookmarkFill />
          <div className="hidden text-sm md:inline">Saved Snippet</div>
        </div>
      </Button>
    </Link>
  );

  // writing snippets tour guide
  const {
    setState,
    state: { tourActive },
  } = useWritingSnippetsTourGuideContext();

  useEffect(() => {
    if (tourActive) {
      setState({ run: true, stepIndex: 2 });
    }
  }, [setState, tourActive]);

  return (
    <>
      <DashboardContainer
        title="Generate Snippets"
        breadcrumb={breadcrumb}
        primaryAction={primaryActionButton}
      >
        <SnippetsGenerateProvider snippet={snippet}>
          {/* Card */}
          {/* Card top */}
          <SnippetsGenerateCardDetails />
          {/* Form */}
          <div className="mt-0.5 flex flex-wrap gap-0.5 md:flex-nowrap">
            {/* Left */}
            <div
              className="w-full rounded bg-white p-5 lg:w-3/5 xl:w-1/2"
              style={{ boxShadow: '0px 2px 3px 0px rgba(52, 51, 74, 0.06)' }}
            >
              {/* Form */}
              <SnippetsGenerateForm />
            </div>
            {/* Right */}
            <div
              className="min-h-[300px] w-full rounded bg-white p-5 lg:w-2/5 xl:w-1/2"
              style={{ boxShadow: '0px 2px 3px 0px rgba(52, 51, 74, 0.06)' }}
            >
              <SnippetsGenerateResult />
            </div>
          </div>
        </SnippetsGenerateProvider>
      </DashboardContainer>
    </>
  );
}
