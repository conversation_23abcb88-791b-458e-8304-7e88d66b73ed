/* eslint-disable react-refresh/only-export-components */
import type { RouteObject } from 'react-router-dom';

import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { RouteErrorBoundary } from '@/components/error';
import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';

import { generateSnippetsLoader, writingSnippetsLoader } from './loaders';

const WritingSnippetsCategories = safeLazy(() => import('./categories'));
const WritingSnippetsGenerate = safeLazy(() => import('./generate'));
const WritingSnippetsAddon = safeLazy(() => import('./addon'));
const WritingSnippetsSaved = safeLazy(() => import('./saved'));
const SingleSavedSnippet = safeLazy(() => import('./saved/SingleSavedSnippet'));
const WritingSnippets = safeLazy(() => import('./'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);
function WritingSnippetsRoot() {
  return <Outlet />;
}

const getRoutes = (): RouteObject[] => [
  {
    path: 'writing-snippets',
    element: <WritingSnippetsRoot />,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        path: '',
        element: <Lazy as={WritingSnippets} />,
        loader: ({ params }) => writingSnippetsLoader({ params: params.category }),
        children: [{ path: ':category', element: <Lazy as={WritingSnippetsCategories} /> }],
      },
      { path: 'addon', element: <Lazy as={WritingSnippetsAddon} /> },
      {
        path: ':writingSnippet/generate',
        element: <Lazy as={WritingSnippetsGenerate} />,
        loader: ({ params }) => generateSnippetsLoader({ params: params.writingSnippet }),
      },
      { path: 'saved', element: <Lazy as={WritingSnippetsSaved} /> },
      {
        path: 'saved/:snippetId',
        element: <Lazy as={SingleSavedSnippet} />,
      },
    ],
  },
];

export default getRoutes;
