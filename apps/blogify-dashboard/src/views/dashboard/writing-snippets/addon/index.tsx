import type { APIResponseType } from '@/types/resources';
import type { Product } from '@/types/resources/product.type';

import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useQuery } from 'react-query';

import { useStoreState } from '@/store';
import AddonPurchaseLayout from '@/components/layout/AddonPurchaseLayout';

export default function WritingSnippetsAddon() {
  const user = useStoreState((s) => s.user.current);
  const navigate = useNavigate();

  const { data: { data: addons } = { data: [] } } = useQuery<APIResponseType<Product>>(
    'products/addons',
    { enabled: !user.hasSnippetsAddon }
  );

  useEffect(() => {
    if (user.hasSnippetsAddon) {
      navigate('/dashboard/writing-snippets/all');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user.hasSnippetsAddon]);

  const addon = (addons.find((a) => a.name.toLowerCase().includes('snippets')) || {}) as Product;

  return (
    <AddonPurchaseLayout
      imageSrc="/images/writing_snippets.png"
      title="Generate Snippets"
      addon={addon}
    />
  );
}
