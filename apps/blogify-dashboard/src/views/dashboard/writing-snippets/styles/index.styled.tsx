import styled from 'styled-components';

import { cn } from '@ps/ui/lib/utils';
import theme from '@/styles/theme';

export const WritingSnippetsCardGrid = ({
  className,
  ...props
}: React.HTMLProps<HTMLDivElement>) => (
  <div
    className={`mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 ${className}`}
    {...props}
  />
);

export const WritingSnippetsCardHoverStyle2 = ({
  className,
  ...props
}: React.HTMLProps<HTMLInputElement>) => <div className={`${className}`} {...props} />;

export const WritingSnippetsCardHoverStyle = (color?: string) => `
  background-color: ${theme.colors.white};
  background: linear-gradient(
    11deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 1) 75%,
    ${color}78
  );
`;

export const WritingSnippetsCard = styled.div<{
  variant?: string;
  color?: string;
  fixedBg?: boolean;
}>`
  background: ${theme.colors.white};
  border-radius: 3px;
  box-shadow: 0px 2px 3px 0px rgba(52, 51, 74, 0.06);
  padding: 16px;
  height: 100%;
  width: 100%;

  ${({ variant, color, fixedBg }) =>
    variant === 'marketing' && !fixedBg
      ? `
    :hover {
      ${WritingSnippetsCardHoverStyle(color)}
    }
  `
      : variant === 'marketing'
        ? `
      ${WritingSnippetsCardHoverStyle(color)}
  `
        : variant === 'social_media' && !fixedBg
          ? `
    :hover {
      ${WritingSnippetsCardHoverStyle(color)}
    }
  `
          : variant === 'social_media'
            ? `
      ${WritingSnippetsCardHoverStyle(color)}
  `
            : variant === 'blogging' && !fixedBg
              ? `
  :hover {
      ${WritingSnippetsCardHoverStyle(color)}
    }
  `
              : variant === 'blogging'
                ? `
      ${WritingSnippetsCardHoverStyle(color)}
  `
                : variant === 'business' && !fixedBg
                  ? `
  :hover {
      ${WritingSnippetsCardHoverStyle(color)}
    }
  `
                  : variant === 'business'
                    ? `
      ${WritingSnippetsCardHoverStyle(color)}
  `
                    : variant === 'other' && !fixedBg
                      ? `
  :hover {
      ${WritingSnippetsCardHoverStyle(color)}
    }
  `
                      : variant === 'other'
                        ? `
      ${WritingSnippetsCardHoverStyle(color)}
  `
                        : ``}
`;

export interface FilterButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant: 'primary' | '';
}

export const FilterButton = ({ className, variant, ...props }: FilterButtonProps) => (
  <button
    className={cn(
      `h-9 w-max rounded bg-white px-4 text-sm font-semibold uppercase text-gray2 transition-all duration-200 ease-in-out hover:bg-primary hover:text-white ${className}`,
      { 'bg-primary text-white': variant === 'primary' }
    )}
    {...props}
  />
);

export const SearchInput = ({
  className,
  ...props
}: React.InputHTMLAttributes<HTMLInputElement>) => (
  <input
    className={`size-full bg-gray7 text-xs font-normal leading-[18px] text-gray2 ${className}`}
    {...props}
  />
);
