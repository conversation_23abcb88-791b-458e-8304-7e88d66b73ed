import { cn } from '@ps/ui/lib/utils';

import { FilterButtonProps, FilterButton } from './index.styled';

export const ToneButton = ({ className, variant, ...props }: FilterButtonProps) => (
  // @ts-ignore Will Fix Later
  <FilterButton
    className={cn(`!hover:text-white !border !border-[#d7e1f1] !capitalize`, {
      '!text-white': variant === 'primary',
      '!text-black1': variant === 'primary',
    })}
    {...props}
  />
);
