import { BsFillBookmarkFill } from 'react-icons/bs';
import { Outlet, useParams } from 'react-router-dom';
import { useMount } from 'react-use';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';
import { useWritingSnippets } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import WritingSnippetsFilterHeader from './components/SnippetsList/WritingSnippetsFilterHeader';
import WritingSnippetsProvider from './components/SnippetsList/WritingSnippetsProvider';
import { DashboardContainer } from '../layout';

export default function WritingSnippets() {
  const { data: snippets, isLoading } = useWritingSnippets();
  const { category } = useParams();
  const pageTitle = category && category === 'all' ? 'writing' : category?.replace('_', ' ');

  // Primary action button
  const primaryActionButton = (
    <Link to="/dashboard/writing-snippets/saved">
      <Button className="bg-green">
        <div className="flex items-center gap-4">
          <BsFillBookmarkFill />
          <div className="hidden text-sm md:inline">Saved Snippet</div>
        </div>
      </Button>
    </Link>
  );

  // writing snippets tour guide
  const {
    setState,
    state: { tourActive },
  } = useWritingSnippetsTourGuideContext();
  useMount(() => {
    if (tourActive) {
      setState({ run: true });
    }
  });

  return (
    <>
      <DashboardContainer title={`${pageTitle} snippets`} primaryAction={primaryActionButton}>
        {isLoading ? (
          <div className="flex h-full items-center justify-center">
            <Loader />
          </div>
        ) : null}

        {snippets && (
          <WritingSnippetsProvider snippets={snippets}>
            {/* Header */}
            <WritingSnippetsFilterHeader />
            <Outlet />
          </WritingSnippetsProvider>
        )}
      </DashboardContainer>
    </>
  );
}
