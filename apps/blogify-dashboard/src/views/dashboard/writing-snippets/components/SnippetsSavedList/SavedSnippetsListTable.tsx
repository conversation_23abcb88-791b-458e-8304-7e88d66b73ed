import type { ISavedWritingSnippetListItem } from '@/types/resources/writing-snippets.type';

import { BsThreeDotsVertical } from 'react-icons/bs';
import { useToggle } from 'react-use';
import { FiCopy } from 'react-icons/fi';
import dayjs from 'dayjs';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import Loader from '@/components/misc/Loader';
import theme from '@/styles/theme';
import Link from '@/components/common/Link';

import { useSavedWritingSnippetsContext } from './SavedSnippetsListProvider';
import SavedSnippetsDeleteDialog from './SavedSnippetsDeleteDialog';
import SavedSnippetsEditDialog from './SavedSnippetsEditDialog';

export default function SavedSnippetsListTable() {
  const { filteredSavedSnippets, isLoading, isFetching } = useSavedWritingSnippetsContext();

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Create</TableHead>
            <TableHead>Content</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {isLoading || isFetching ? (
            <TableRow>
              <TableCell colSpan={5}>
                <div className="flex flex-col gap-1 flex-center">
                  <Loader />
                </div>
              </TableCell>
            </TableRow>
          ) : filteredSavedSnippets && filteredSavedSnippets.length ? (
            filteredSavedSnippets.map((item, index) => (
              <SavedSnippetListItems key={index} snippet={item} />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5}>
                <div className="flex flex-col gap-1 flex-center">
                  <div className="font-semibold">No saved snippets found</div>
                  <div className="text-xs text-gray2">
                    Make sure you don't have any filters applied
                  </div>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}

const SavedSnippetListItems = ({ snippet }: { snippet: ISavedWritingSnippetListItem }) => {
  const [isEditDialogOpen, toggleEditDialogOpen] = useToggle(false);
  const [isDeleteDialog, toggleDeleteDialog] = useToggle(false);

  return (
    <>
      <TableRow>
        <TableCell>
          <Link
            to={`/dashboard/writing-snippets/saved/${snippet.genId}`}
            color={theme.colors.black2}
          >
            <div className="flex items-center gap-2">
              <img className="size-[30px]" src={snippet.categoryIcon} alt={snippet.category} />

              {snippet.name}
            </div>
          </Link>
        </TableCell>
        <TableCell>
          <Link
            to={`/dashboard/writing-snippets/saved/${snippet.genId}`}
            color={theme.colors.black2}
          >
            {snippet.category}
          </Link>
        </TableCell>
        <TableCell>
          <Link
            to={`/dashboard/writing-snippets/saved/${snippet.genId}`}
            color={theme.colors.black2}
          >
            {dayjs(snippet.createdAt).format('DD - MMM - YYYY')}
          </Link>
        </TableCell>
        <TableCell>
          <Link
            to={`/dashboard/writing-snippets/saved/${snippet.genId}`}
            color={theme.colors.black2}
          >
            <div>{snippet.content.slice(0, 50)}...</div>
          </Link>
        </TableCell>
        <TableCell>
          <div className="flex items-center">
            {/* Copy button */}
            <Button variant="secondary" className="bg-white" onClick={() => copy(snippet.content)}>
              <FiCopy color={theme.colors.gray2} size="16px" />
            </Button>
            {/* Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="bg-white">
                  <BsThreeDotsVertical color={theme.colors.gray2} size="16px" />
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent className="flex w-[100px] flex-col rounded-[3px] border border-gray5 bg-white py-[7px] shadow-[0px_8px_12px_0px_rgba(114,135,171,0.4)]">
                <DropdownMenuItem
                  className="cursor-pointer px-[12px] py-[5px] text-[12px] font-normal leading-[18px] text-black2 hover:bg-gray7 hover:outline-none"
                  onClick={() => copy(snippet.content)}
                >
                  Copy
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer px-[12px] py-[5px] text-[12px] font-normal leading-[18px] text-black2 hover:bg-gray7 hover:outline-none"
                  onClick={toggleEditDialogOpen}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer px-[12px] py-[5px] text-[12px] font-normal leading-[18px] text-black2 hover:bg-gray7 hover:outline-none"
                  onClick={toggleDeleteDialog}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* Delete dialog */}
          <SavedSnippetsDeleteDialog
            isOpen={isDeleteDialog}
            toggleOpen={toggleDeleteDialog}
            snippet={snippet}
          />
          {/* Edit dialog */}
          <SavedSnippetsEditDialog
            isOpen={isEditDialogOpen}
            toggleOpen={toggleEditDialogOpen}
            snippet={snippet}
          />
        </TableCell>
      </TableRow>
    </>
  );
};
