import {
  ISavedWritingSnippetListItem,
  IWiringSnippetsAndCategories,
} from '@/types/resources/writing-snippets.type';
import { useGetSavedSnippetsList } from '@/utils/writingSnippets';
import React, { createContext, useEffect, useState } from 'react';

interface IFilterCheckboxOptions {
  name: string;
  checked: boolean;
}
interface ISavedWritingSnippetsContext {
  filterCheckboxOptions: IFilterCheckboxOptions[] | undefined;
  handleFilterCheckboxChange: (checked: boolean, index: number) => void;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filteredSavedSnippets: ISavedWritingSnippetListItem[] | undefined;
  currentPage: number;
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  totalPages: number;
  setTotalPages: React.Dispatch<React.SetStateAction<number>>;
  isFetching: boolean;
  isLoading: boolean;
}
// Context
const WritingSnippetsContext = createContext<ISavedWritingSnippetsContext | null>(null);
WritingSnippetsContext.displayName = 'WritingSnippetsContext';
// Provider component
export default function SavedWritingSnippetsListProvider({
  children,
  snippets,
}: {
  children: React.ReactNode;
  snippets: IWiringSnippetsAndCategories;
}) {
  // pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const limit = 10;
  // Filter
  const [searchTerm, setSearchTerm] = useState<string>('');
  useEffect(() => {
    // Reset pagination when search term changes
    setCurrentPage(1);
  }, [searchTerm]);
  const [filterCheckboxOptions, setFilterCheckboxOptions] = useState<IFilterCheckboxOptions[]>();
  useEffect(() => {
    // Set filter options from API response
    const filterOptions = snippets?.categories.map((item) => ({
      name: item.name,
      checked: false,
    }));
    setFilterCheckboxOptions(filterOptions);
  }, [snippets]);

  // Filter checkbox change handler
  const handleFilterCheckboxChange = (checked: boolean, index: number) => {
    setCurrentPage(1);
    setFilterCheckboxOptions(
      filterCheckboxOptions?.map((cat, i) => {
        if (index === i) {
          return {
            ...cat,
            checked,
          };
        }
        return cat;
      })
    );
  };
  const filterCategories = filterCheckboxOptions
    ?.filter((item) => item.checked)
    .map((item) => item.name)
    .join(',');

  // fetch saved snippets
  const { savedSnippetsData, isFetching, isSavedSnippetsLoading } = useGetSavedSnippetsList({
    pageNumber: currentPage,
    limit,
    searchTerm,
    category: filterCategories,
  });

  useEffect(() => {
    // Set total pages from API response
    if (savedSnippetsData) {
      setTotalPages(Math.ceil(savedSnippetsData.total / limit));
    }
  }, [savedSnippetsData]);
  // Context values
  const values = {
    filterCheckboxOptions,
    handleFilterCheckboxChange,
    searchTerm,
    setSearchTerm,
    filteredSavedSnippets: savedSnippetsData?.snippets,
    currentPage,
    setCurrentPage,
    totalPages,
    setTotalPages,
    isFetching,
    isLoading: isSavedSnippetsLoading,
  };
  return (
    <WritingSnippetsContext.Provider value={values}>{children}</WritingSnippetsContext.Provider>
  );
}

// Custom hook
// eslint-disable-next-line react-refresh/only-export-components
export const useSavedWritingSnippetsContext = () => {
  const context = React.useContext(WritingSnippetsContext) as ISavedWritingSnippetsContext;
  if (context === null) {
    throw new Error(
      'useSavedWritingSnippetsContext must be used within a SavedWritingSnippetsListProvider'
    );
  }
  return context;
};
