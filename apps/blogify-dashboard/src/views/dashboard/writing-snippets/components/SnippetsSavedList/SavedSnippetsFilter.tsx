import { BsFilter } from 'react-icons/bs';
import { FiSearch } from 'react-icons/fi';
import debounce from 'lodash/debounce';

import {
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { DashboardTitleCard } from '@/views/dashboard/layout';
import { Button } from '@ps/ui/components/button';

import { useSavedWritingSnippetsContext } from './SavedSnippetsListProvider';
import { SearchInput } from '../../styles/index.styled';

export default function SavedSnippetsFilter() {
  const {
    filterCheckboxOptions,
    handleFilterCheckboxChange,
    filteredSavedSnippets,
    setSearchTerm,
  } = useSavedWritingSnippetsContext();
  const totalSnippets = filteredSavedSnippets?.length || 0;
  const totalAppliedFilters = filterCheckboxOptions?.filter((item) => item.checked).length || 0;
  return (
    <DashboardTitleCard
      className="min-h-[170px] flex-col justify-center sm:min-h-[84] sm:flex-row sm:justify-between"
      count={totalSnippets}
      isLoading={false}
      title="Snippets"
    >
      {/* Saved list filter options */}
      <div className="flex h-auto w-full flex-col gap-3 py-4 sm:w-1/2 sm:flex-row sm:py-0 md:w-1/3">
        {/* Search term input */}
        <div className="flex w-full items-center gap-1 overflow-hidden rounded border border-gray5 bg-gray7">
          <Button className="w-9 !rounded-none border-r border-gray5" variant="secondary">
            <div>
              <FiSearch size="16px" className="text-gray2" />
            </div>
          </Button>
          <SearchInput
            placeholder="Type what are you looking..."
            onChange={debounce((e) => setSearchTerm(e.target.value), 500)}
            width="100%"
          />
        </div>
        {/* Filter dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="secondary">
              <div className="flex h-full items-center gap-2">
                <BsFilter className="text-gray2" size="14px" />
                <div className="text-sm font-semibold text-gray2">FILTER</div>
                <div className="text-sm font-semibold text-gray2">
                  {totalAppliedFilters > 0 ? ` (${totalAppliedFilters})` : ''}
                </div>
              </div>
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent
            className="flex w-[200px] flex-col rounded-[3px] border border-gray5 bg-white py-[7px] shadow-[0px_8px_12px_0px_rgba(114,135,171,0.4)]"
            alignOffset={-20}
            sideOffset={10}
          >
            {filterCheckboxOptions && filterCheckboxOptions.length
              ? filterCheckboxOptions.map((item, index) => (
                  <DropdownMenuCheckboxItem
                    className="cursor-pointer px-3 py-1.5 text-xs leading-[18px] text-black2 hover:bg-gray7 hover:outline-none"
                    checked={item.checked}
                    onCheckedChange={(checked) => handleFilterCheckboxChange(checked, index)}
                    key={index}
                  >
                    <div className="flex items-center gap-1">
                      <div className="text-sm font-semibold">{item.name}</div>
                    </div>
                  </DropdownMenuCheckboxItem>
                ))
              : null}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </DashboardTitleCard>
  );
}
