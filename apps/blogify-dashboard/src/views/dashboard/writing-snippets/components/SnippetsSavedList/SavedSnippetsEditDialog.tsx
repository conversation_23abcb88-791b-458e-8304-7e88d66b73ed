import type { ISavedWritingSnippetListItem } from '@/types/resources/writing-snippets.type';

import { useFormik } from 'formik';
import { useEffect } from 'react';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { useUpdateSavedSnippet } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import Spinner from '@/components/misc/Spinner';
import Dialog from '@ps/ui/components/dialog';

const validationSchema = Yup.object({
  name: Yup.string().required('Name is required'),
  content: Yup.string().required('Content is required'),
});

export default function SavedSnippetsEditDialog({
  snippet,
  isOpen,
  toggleOpen,
}: {
  snippet: ISavedWritingSnippetListItem;
  isOpen: boolean;
  toggleOpen: () => void;
}) {
  const { updateSavedSnippet, isUpdateSavedSnippetError } = useUpdateSavedSnippet();

  const { handleSubmit, handleChange, values, setValues, errors, isSubmitting } = useFormik({
    initialValues: {
      name: snippet.name,
      content: snippet.content,
    },
    onSubmit: async (formPayload, actions) => {
      await updateSavedSnippet(
        {
          id: snippet.genId,
          formPayload,
        },
        {
          onSuccess: () => {
            toast.success('Snippet updated successfully');
            toggleOpen();
            actions.resetForm();
          },
        }
      );
    },
    validationSchema,
  });

  useEffect(() => {
    setValues({
      name: snippet.name,
      content: snippet.content,
    });
  }, [setValues, snippet]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={toggleOpen}
      Icon={() => null}
      title={`Edit ${snippet.name}`}
      description=""
      actions={
        <>
          <Button type="submit">
            {isSubmitting ? <Spinner /> : isUpdateSavedSnippetError ? 'Retry' : 'Update'}
          </Button>
          <Button variant="secondary" type="button" onClick={toggleOpen} className="mr-2">
            Cancel
          </Button>
        </>
      }
      error={isUpdateSavedSnippetError ? 'Something went wrong. Please try again.' : null}
      onSubmit={(ev) => handleSubmit(ev as any)}
    >
      <FormField
        name="name"
        label="Snippet Name"
        placeholder="Enter new snippet name"
        value={values.name}
        onChange={handleChange}
        error={errors.name ? errors.name : ''}
      />

      <FormField
        type="textarea"
        name="content"
        label="Text"
        placeholder="Update your snippet description"
        value={values.content}
        onChange={handleChange}
        error={errors.content ? errors.content : ''}
      />
    </Dialog>
  );
}
