import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri';

import { Button } from '@ps/ui/components/button';

import { useSavedWritingSnippetsContext } from './SavedSnippetsListProvider';

export default function SavedSnippetsListPagination() {
  const { totalPages, currentPage, setCurrentPage } = useSavedWritingSnippetsContext();
  // const handleLoadMore = () => {
  //   setCurrentPage(currentPage + 1);
  // };

  const handlePreviousPage = () => {
    setCurrentPage(Math.max(currentPage - 1, 1));
  };
  const handleNextPage = () => {
    setCurrentPage(Math.min(currentPage + 1, totalPages));
  };
  return (
    <>
      <div className="mt-4 flex justify-end gap-3">
        <Button
          variant="secondary"
          onClick={handlePreviousPage}
          disabled={currentPage === 1 || totalPages === 0}
        >
          <div className="flex items-center">
            <RiArrowLeftSLine className="mt-0.5" size={24} />
            Previous
          </div>
        </Button>
        <Button
          variant="secondary"
          onClick={handleNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          <div className="flex items-center">
            Next
            <RiArrowRightSLine className="mt-0.5" size={24} />
          </div>
        </Button>
      </div>
      {/* <div className="flex gap-3 mt-4 justify-center">
        <Button
          variant="secondary"
          onClick={handleLoadMore}
          loading={isFetching}
          disabled={currentPage == totalPages}
        >
          <div className="flex items-center">
            Load more
          </div>
        </Button>
      </div> */}
    </>
  );
}
