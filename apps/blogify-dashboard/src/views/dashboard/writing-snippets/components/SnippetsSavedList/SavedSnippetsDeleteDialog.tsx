import type { ISavedWritingSnippetListItem } from '@/types/resources/writing-snippets.type';

import toast from 'react-hot-toast';

import { useDeleteSavedSnippet } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import Spinner from '@/components/misc/Spinner';
import Dialog from '@ps/ui/components/dialog';

export default function SavedSnippetsDeleteDialog({
  snippet,
  isOpen,
  toggleOpen,
}: {
  snippet: ISavedWritingSnippetListItem;
  isOpen: boolean;
  toggleOpen: () => void;
}) {
  const { deleteSavedSnippet, isDeleteSavedSnippetLoading, isDeleteSavedSnippetError } =
    useDeleteSavedSnippet();
  const handleDeleteSnippet = async () => {
    await deleteSavedSnippet(snippet.genId, {
      onSuccess: () => {
        toast.success('Snippet deleted successfully');
        toggleOpen();
      },
    });
  };
  return (
    <Dialog
      open={isOpen}
      onOpenChange={toggleOpen}
      Icon={() => null}
      title="Delete this snippet?"
      description="Please confirm if you want to delete this snippet. Once deleted it will be removed from this list."
      actions={
        <>
          <Button className="bg-red4" onClick={handleDeleteSnippet}>
            {isDeleteSavedSnippetLoading ? (
              <Spinner />
            ) : isDeleteSavedSnippetError ? (
              'Retry'
            ) : (
              'Delete'
            )}
          </Button>
          <Button className="mr-2" variant="secondary" onClick={toggleOpen}>
            Keep
          </Button>
        </>
      }
      error={isDeleteSavedSnippetError ? 'Something went wrong. Please try again.' : null}
    />
  );
}
