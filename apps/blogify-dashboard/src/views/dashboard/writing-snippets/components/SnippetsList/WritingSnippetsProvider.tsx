/**
 * This is a compound component that provides the context for the WritingSnippets list
 */
import React, { createContext, useContext, useMemo, useState } from 'react';
import type {
  IWiringSnippetsAndCategories,
  IWritingSnippetType,
  IWritingSnippetCategory,
} from '@/types/resources/writing-snippets.type';

// Constants
const defaultWritingSnippetsCategories: IWritingSnippetCategory[] = [
  {
    colorScheme: '',
    iconUrl: '',
    name: 'all',
  },
];

interface IWritingSnippetsContext {
  filterOptions: IWritingSnippetCategory[];
  selectedSnippetCategory: string | undefined;
  setSelectedSnippetCategory: React.Dispatch<React.SetStateAction<string>>;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filteredSnippets: IWritingSnippetType[];
}

const WritingSnippetsContext = createContext<IWritingSnippetsContext | null>(null);
WritingSnippetsContext.displayName = 'WritingSnippetsContext';

// Provider component
export default function WritingSnippetsProvider({
  snippets,
  children,
  ...props
}: {
  snippets: IWiringSnippetsAndCategories;
  children: React.ReactNode;
}) {
  const filterOptions = useMemo(
    () => [...defaultWritingSnippetsCategories, ...snippets?.categories],
    [snippets]
  );
  const [selectedSnippetCategory, setSelectedSnippetCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  // Search filter by searchTerm and selectedSnippetCategory title
  const filteredByCategory = snippets?.snippetTypes?.filter((item) =>
    selectedSnippetCategory.toLowerCase() !== 'all'
      ? item.category.name.toLowerCase() === selectedSnippetCategory.toLowerCase()
      : item
  );
  const filteredSnippets = filteredByCategory?.filter((item) =>
    searchTerm.length ? item.title.toLowerCase().includes(searchTerm.toLowerCase()) : item
  );

  const value = {
    filterOptions,
    selectedSnippetCategory,
    setSelectedSnippetCategory,
    searchTerm,
    setSearchTerm,
    filteredSnippets,
  };

  return (
    <WritingSnippetsContext.Provider value={value} {...props}>
      {children}
    </WritingSnippetsContext.Provider>
  );
}

// Custom hook to use the WritingSnippetsContext
// eslint-disable-next-line react-refresh/only-export-components
export const useWritingSnippetsContext = () => {
  const context = useContext(WritingSnippetsContext) as IWritingSnippetsContext;
  if (context === undefined) {
    throw new Error('useWritingSnippets must be used within a WritingSnippetsProvider');
  }
  return context;
};
