import Link from '@/components/common/Link';

import { WritingSnippetsCard, WritingSnippetsCardGrid } from '../../styles/index.styled';
import { useWritingSnippetsContext } from './WritingSnippetsProvider';

export default function WritingSnippetsList() {
  const { filteredSnippets } = useWritingSnippetsContext();
  return (
    <WritingSnippetsCardGrid>
      {filteredSnippets.length ? (
        [...filteredSnippets].map((item, index) => (
          <Link to={`/dashboard/writing-snippets/${item.title}/generate`} key={index}>
            <WritingSnippetsCard
              variant={item.category.name.toLowerCase().replace(' ', '_')}
              color={item.category.colorScheme}
            >
              <div className="flex flex-col gap-3">
                <img
                  className="size-12"
                  src={item.iconUrl || item.category.iconUrl}
                  alt="marketingLogo"
                />
                <div className="text-xs font-semibold uppercase text-[#7A8EB2]" role="heading">
                  {item.category.name}
                </div>
                <div className="font-semibold text-black1">{item.title}</div>
                <div className="text-sm text-black1">{item.description}</div>
              </div>
            </WritingSnippetsCard>
          </Link>
        ))
      ) : (
        <div>No snippets found</div>
      )}
    </WritingSnippetsCardGrid>
  );
}
