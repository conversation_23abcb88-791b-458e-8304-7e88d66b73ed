import { NavLink, useNavigate, useParams } from 'react-router-dom';
import { useEffect } from 'react';
import { FiSearch } from 'react-icons/fi';

import { Button } from '@ps/ui/components/button';
import theme from '@/styles/theme';

import { FilterButton, SearchInput } from '../../styles/index.styled';
import { useWritingSnippetsContext } from './WritingSnippetsProvider';

export default function WritingSnippetsFilterHeader() {
  const { filterOptions, setSearchTerm, setSelectedSnippetCategory } = useWritingSnippetsContext();
  const navigate = useNavigate();
  // Handle search term
  const handleSearchTerm = (e: React.ChangeEvent<HTMLInputElement>) => {
    navigate(`/dashboard/writing-snippets/all`);
    setSearchTerm(e.target.value);
  };
  // Set selectedSnippetCategory based on the url param
  const { category } = useParams();
  useEffect(() => {
    if (!category) return;
    setSelectedSnippetCategory(category);
  }, [category, setSelectedSnippetCategory]);
  return (
    <>
      <div className="flex flex-col gap-0.5">
        <div className="flex items-center gap-2 rounded bg-white px-4 py-3">
          {/* Search */}
          <div className="w-full">
            <div className="flex items-center gap-1 overflow-hidden rounded border border-gray5 bg-gray7">
              <Button className="w-9 !rounded-none border-r border-gray5" variant="secondary">
                <div>
                  <FiSearch size="16px" color={theme.colors.gray2} />
                </div>
              </Button>
              <SearchInput placeholder="Type what are you looking.." onChange={handleSearchTerm} />
            </div>
          </div>
        </div>
        <div
          className="flex items-center gap-2 rounded bg-white px-4 py-3"
          id="writingSnippetsFilterHeader"
        >
          {/* Filter tab links */}
          <div className="flex w-full items-center gap-4 overflow-scroll">
            {filterOptions.length
              ? filterOptions.map((item, index) => (
                  <NavLink
                    to={`/dashboard/writing-snippets/${item.name.toLowerCase()}`}
                    key={index}
                  >
                    {({ isActive }) => (
                      <FilterButton variant={isActive ? 'primary' : ''} data-testid="filter_button">
                        {item.name.replace('_', ' ')}
                      </FilterButton>
                    )}
                  </NavLink>
                ))
              : null}
          </div>
        </div>
      </div>
    </>
  );
}
