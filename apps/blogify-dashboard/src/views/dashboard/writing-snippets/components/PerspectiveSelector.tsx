import { cn } from '@ps/ui/lib/utils';

// eslint-disable-next-line react-refresh/only-export-components
export const perspectiveOptions = [
  {
    title: 'First Person',
    description: '(I & We)',
    value: 'First Person',
  },
  {
    title: 'Second Person',
    description: '(You)',
    value: 'Second Person',
  },
  {
    title: 'Third Person',
    description: '(<PERSON>, She, They, It)',
    value: 'Third Person',
  },
];

export default function PerspectiveSelector({
  value,
  onChange,
  label,
}: {
  value: string;
  onChange: (_value: string) => void;
  label?: string;
}) {
  return (
    <>
      {label && <div className="mb-2 mt-5 text-sm font-semibold text-black1">{label}</div>}
      <div className="mb-4 mt-2 flex flex-col gap-2 sm:flex-row sm:gap-3 md:gap-6">
        {perspectiveOptions.length
          ? perspectiveOptions.map((option, index) => (
              <div
                className={cn(
                  'w-full cursor-pointer rounded border border-[#d7e1f1] p-3 hover:bg-primary [&>*>*]:hover:text-white',
                  { 'bg-primary [&>*>*]:text-white': value === option.value }
                )}
                key={index}
                onClick={() => onChange(option.value)}
              >
                <input className="hidden" type="hidden" name="model" value={value} />
                <div className="flex flex-col gap-1">
                  <div className="text-sm text-black1">{option.title}</div>
                  <div className="text-xs text-gray2">{option.description}</div>
                </div>
              </div>
            ))
          : null}
      </div>
    </>
  );
}
