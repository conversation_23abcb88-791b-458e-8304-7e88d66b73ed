import { Button } from '@ps/ui/components/button';
import Link from '@/components/common/Link';

import { useSnippetsGenerateContext } from './SnippetsGenerateProvider';
import { WritingSnippetsCard } from '../../styles/index.styled';

export default function SnippetsGenerateCardDetails() {
  const { snippet } = useSnippetsGenerateContext();
  return (
    <>
      <WritingSnippetsCard
        variant={snippet.category.name.toLowerCase().replace(' ', '_')}
        color={snippet.category.colorScheme}
        fixedBg
      >
        <div className="flex flex-wrap justify-between gap-3 sm:flex-nowrap">
          <div className="flex flex-col gap-3">
            <img
              className="size-12"
              src={snippet.iconUrl || snippet.category.iconUrl}
              alt="marketingLogo"
            />
            <div className="text-xs font-semibold uppercase text-[#7A8EB2]">
              {snippet.category.name}
            </div>
            <div className="font-semibold text-black1">{snippet.title}</div>
            <div className="text-sm text-black1">{snippet.description}</div>
          </div>
          <div className="mt-4 flex min-w-max flex-col justify-end sm:mt-0">
            <Link
              className="text-sm text-primary"
              to={`/dashboard/writing-snippets/${snippet.category.name}`}
            >
              <Button variant="secondary">
                <div className="text-sm font-normal capitalize">
                  More {snippet.category.name} snippets
                </div>
              </Button>
            </Link>
          </div>
        </div>
      </WritingSnippetsCard>
    </>
  );
}
