import { useSnippetsGenerateContext } from '../SnippetsGenerateProvider';
import SnippetsResultsListItem from './SnippetsResultsListItem';

export default function SnippetsResultsList() {
  const { generatedSnippetData } = useSnippetsGenerateContext();
  return (
    <>
      {/* result 1 */}
      {generatedSnippetData?.output ? (
        <div className="mt-7">
          <SnippetsResultsListItem selectedKey="output" label="Result - 1" />
        </div>
      ) : null}
      {/* result 2 */}
      {/* {generatedSnippetData?.output1 ? (
        <div className="mt-7">
          <SnippetsResultsListItem selectedKey="output1" label="Result - 2" />
        </div>
      ) : null} */}
    </>
  );
}
