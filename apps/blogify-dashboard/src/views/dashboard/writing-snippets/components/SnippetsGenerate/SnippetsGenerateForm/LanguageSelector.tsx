import type { ReactSelectOptionType } from '@/components/form/SelectOption';
import type { IWritingSnippetType } from '@/types/resources/writing-snippets.type';

import { BLOG_LANGUAGES } from '@/views/dashboard/blog/create/utils/languages';
import SelectOption from '@/components/form/SelectOption';

// eslint-disable-next-line react-refresh/only-export-components
export const outputLanguageOptions: ReactSelectOptionType[] = BLOG_LANGUAGES.map((language) => ({
  value: language.value,
  label: `${language.name} - ${language.translation}`,
}));

export default function LanguageSelector({
  snippet,
  handleChange,
  values,
}: {
  snippet: IWritingSnippetType;
  handleChange: any;
  values: Record<string, any>;
}) {
  return (
    <div className="flex flex-col gap-3 md:flex-row">
      {/* Select Input language */}
      {snippet.displayInputLanguage ? (
        <div className="mb-4 w-full">
          <SelectOption
            label="Input language"
            options={outputLanguageOptions}
            defaultValue={outputLanguageOptions[0]}
            onChange={handleChange}
            value={values.inputLanguage}
            name="inputLanguage"
            // onChange={(option) => {
            //   handleChange({
            //     target: {
            //       name: 'language',
            //       value: option?.value,
            //     },
            //   });
            // }}
          />
        </div>
      ) : null}
      {/* Select output language */}
      {snippet.displayOutputLanguage ? (
        <div className="mb-4 w-full">
          <SelectOption
            label="Output language"
            options={outputLanguageOptions}
            defaultValue={outputLanguageOptions[0]}
            onChange={handleChange}
            value={values.language}
            name="language"
          />
        </div>
      ) : null}
    </div>
  );
}
