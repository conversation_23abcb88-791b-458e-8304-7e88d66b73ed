import { tones } from '@/constants/tones';

import { ToneButton } from '../../../styles/SnippetsGenerate.styled';

export default function ToneSelector({
  value,
  handleChange,
}: {
  value: string;
  handleChange: any;
}) {
  return (
    <div>
      <div className="text-xs font-semibold text-black1">Select Tone</div>
      <div className="mb-4 mt-2 flex flex-wrap gap-3">
        {tones.length &&
          tones.map((item, index) => (
            <ToneButton
              type="button"
              onClick={() => {
                handleChange({
                  target: {
                    name: 'tone',
                    value: item.title,
                  },
                });
              }}
              key={index}
              variant={value === item.title ? 'primary' : ''}
            >
              <input type="hidden" name="tone" value={value} />
              <div className="flex items-center gap-1">
                <img src={item.icon} width="16px" height="16px" />
                <span>{item.title}</span>
              </div>
            </ToneButton>
          ))}
      </div>
    </div>
  );
}
