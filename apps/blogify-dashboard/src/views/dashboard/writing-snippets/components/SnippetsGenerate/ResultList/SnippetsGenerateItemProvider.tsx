import React, { createContext } from 'react';
import { useSnippetsGenerateContext } from '../SnippetsGenerateProvider';
import { IGeneratedWritingSnippet } from '@/types/resources/writing-snippets.type';
import { useToggle } from 'react-use';

export interface SnippetsGenerateItemProviderContextProps {
  isSaveDialogOpen: boolean;
  toggleSaveDialog: () => void;
  isSaved: boolean;
  toggleIsSaved: () => void;
  generatedSnippetData: IGeneratedWritingSnippet | undefined;
  snippetValue: string;
  setSnippetValue: React.Dispatch<React.SetStateAction<string>>;
  isFullScreen: boolean;
  toggleFullScreen: () => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const SnippetsGenerateItemProviderContext =
  createContext<SnippetsGenerateItemProviderContextProps | null>(null);
SnippetsGenerateItemProviderContext.displayName = 'SnippetsGenerateItemProviderContext';

export default function SnippetsGenerateItemProvider({
  children,
  selectedKey,
  ...props
}: {
  children: React.ReactNode;
  selectedKey: keyof IGeneratedWritingSnippet;
}) {
  const { generatedSnippetData } = useSnippetsGenerateContext();
  // Save dialog
  const [isSaveDialogOpen, toggleSaveDialog] = useToggle(false);
  // Saved
  const [isSaved, toggleIsSaved] = useToggle(false);
  // Full screen
  const [isFullScreen, toggleFullScreen] = useToggle(false);
  // Snippet value
  const [snippetValue, setSnippetValue] = React.useState<string>(
    generatedSnippetData?.[selectedKey] || ''
  );

  const value = {
    isSaveDialogOpen,
    toggleSaveDialog,
    isSaved,
    toggleIsSaved,
    generatedSnippetData,
    snippetValue,
    setSnippetValue,
    isFullScreen,
    toggleFullScreen,
  };
  return (
    <SnippetsGenerateItemProviderContext.Provider value={value} {...props}>
      {children}
    </SnippetsGenerateItemProviderContext.Provider>
  );
}

// Custom hook
// eslint-disable-next-line react-refresh/only-export-components
export const useSnippetsGenerateItemContext = () => {
  const context = React.useContext(
    SnippetsGenerateItemProviderContext
  ) as SnippetsGenerateItemProviderContextProps;
  if (context === null) {
    throw new Error('useSnippetsGenerateItem must be used within a SnippetsGenerateItemProvider');
  }
  return context;
};
