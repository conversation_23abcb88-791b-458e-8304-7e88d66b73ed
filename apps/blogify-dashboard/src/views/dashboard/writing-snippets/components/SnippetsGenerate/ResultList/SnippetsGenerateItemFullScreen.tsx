import { Dialog, DialogContent } from '@ps/ui/components/dialog';

import { useSnippetsGenerateItemContext } from './SnippetsGenerateItemProvider';
import { SnippetResultOutput } from './SnippetsResultsListItem';

export default function SnippetsGenerateItemFullScreen({ label }: { label: string }) {
  const { isFullScreen, toggleFullScreen } = useSnippetsGenerateItemContext();
  console.log('label', label);
  return (
    <>
      <Dialog open={isFullScreen} onOpenChange={toggleFullScreen}>
        <DialogContent>
          <SnippetResultOutput label={label} minHeight="600px" />
        </DialogContent>
      </Dialog>
    </>
  );
}
