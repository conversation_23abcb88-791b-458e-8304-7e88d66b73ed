import { useEffect, useMemo } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';
import { useSnippetGenerate } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import { tones } from '@/constants/tones';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import Spinner from '@/components/misc/Spinner';

import PerspectiveSelector, { perspectiveOptions } from '../PerspectiveSelector';
import LanguageSelector, { outputLanguageOptions } from './SnippetsGenerateForm/LanguageSelector';
import { useSnippetsGenerateContext } from './SnippetsGenerateProvider';
import ToneSelector from './SnippetsGenerateForm/ToneSelector';

// Validation schema
const validationSchema = {
  title: yup.string().required('Title is required'),
  category: yup.string().required('Category is required'),
  prompt: yup.string().required('Prompt is required'),
};

// Utils
const isBlogifyPrefix = (fieldName: string) => fieldName.split('_')[0].toLowerCase() === 'blogify';

// Component
export default function SnippetsGenerateForm() {
  const { snippet, setGeneratedSnippetData } = useSnippetsGenerateContext();
  const { generateSnippet, isGenerateSnippetError, generateSnippetError } = useSnippetGenerate();
  // Extra fields
  const extraFields = useMemo(() => snippet.extraFields, [snippet.extraFields]);
  const filteredExtraFields = useMemo(
    () =>
      extraFields
        ? extraFields.filter((field) => field.fieldName.split('_')[0].toLowerCase() !== 'blogify')
        : [],
    [extraFields]
  );
  const extraFieldsInitialValues = useMemo(
    () =>
      extraFields
        ? extraFields.reduce((acc: Record<string, string>, item) => {
            acc[item.fieldName] =
              isBlogifyPrefix(item.fieldName) && item.placeholder ? item.placeholder : '';
            return acc;
          }, {})
        : {},
    [extraFields]
  );
  // Formik
  const {
    values,
    handleChange,
    errors,
    touched,
    handleSubmit,
    isSubmitting,
    setFieldValue,
    submitForm,
  } = useFormik({
    initialValues: {
      title: snippet.title,
      category: snippet.category.name,
      model: snippet.model,
      language: outputLanguageOptions[0].value,
      inputLanguage: outputLanguageOptions[0].value,
      perspective: snippet.displayPerspective ? perspectiveOptions[2].value : '',
      prompt: '',
      tone: snippet.displayTone ? tones[0].title : '',
      ...extraFieldsInitialValues,
    },
    onSubmit: async (value) => {
      setGeneratedSnippetData(undefined);
      await generateSnippet(value, {
        onSuccess: (data) => {
          setGeneratedSnippetData(data);
        },
      });
    },
    validationSchema: yup.object({
      ...validationSchema,
    }),
  });

  // writing snippets tour guide
  const {
    state: { tourActive, stepIndex },
  } = useWritingSnippetsTourGuideContext();
  useEffect(() => {
    if (tourActive)
      setFieldValue(
        'prompt',
        'Create an email announcing the baking competition for the community'
      );
  }, [setFieldValue, tourActive]);
  useEffect(() => {
    if (tourActive && stepIndex === 5) {
      submitForm();
    }
  }, [stepIndex, submitForm, tourActive]);

  return (
    <>
      <div className="font-semibold text-black1">Prompt</div>
      <div className="mt-2 text-sm text-black1">
        Type your prompt or content & play with different settings to get the right result.
      </div>
      {/* Form */}
      <form onSubmit={handleSubmit}>
        <div className="mt-7" id="writingSnippetsGenerateInputForm">
          {/* Prompt text field */}
          <FormField
            name="prompt"
            type="textarea"
            label="Type your prompt"
            placeholder={snippet?.promptHint || 'Type your prompt here...'}
            value={values.prompt}
            onChange={handleChange}
            error={touched.prompt ? errors.prompt : ''}
            maxLength={500}
          />
          {/* Extra fields */}
          {filteredExtraFields && filteredExtraFields.length
            ? filteredExtraFields.map((item, index) => (
                <FormField
                  key={index}
                  name={item.fieldName}
                  type="text"
                  label={item.fieldName.replaceAll('_', ' ')}
                  placeholder={item.placeholder}
                  value={values[item.fieldName as keyof typeof values]}
                  onChange={handleChange}
                  error={
                    touched[item.fieldName as keyof typeof touched]
                      ? errors[item.fieldName as keyof typeof errors]
                      : ''
                  }
                />
              ))
            : null}
        </div>
        <div id="writingSnippetsGenerateOtherInput">
          {/* Select tone */}
          {snippet.displayTone ? (
            <ToneSelector value={values.tone} handleChange={handleChange} />
          ) : null}
          {/* Perspective selector */}
          {snippet.displayPerspective ? (
            <PerspectiveSelector
              label="Point of view"
              value={values.perspective}
              onChange={(value) =>
                handleChange({
                  target: {
                    name: 'perspective',
                    value,
                  },
                })
              }
            />
          ) : null}
          {/* Language selectors */}
          <LanguageSelector snippet={snippet} handleChange={handleChange} values={values} />
        </div>

        {/* Form submit error */}
        {generateSnippetError ? (
          <ErrorMessage showError={isGenerateSnippetError}>
            Something is wrong. Please try again.
          </ErrorMessage>
        ) : null}
        {/* submit button */}
        <Button
          id="writingSnippetsGenerateSubmitButton"
          className="mt-7 w-full"
          disabled={isSubmitting}
          type="submit"
        >
          <div className="flex items-center" style={{ textTransform: 'initial' }}>
            {isGenerateSnippetError ? (
              'Try again'
            ) : isSubmitting ? (
              <>
                Generating your text...
                <Spinner />
              </>
            ) : (
              'Generate'
            )}
          </div>
        </Button>
      </form>
    </>
  );
}
