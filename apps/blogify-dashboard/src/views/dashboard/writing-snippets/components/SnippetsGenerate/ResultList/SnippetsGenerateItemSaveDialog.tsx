import { useFormik } from 'formik';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { useSaveSnippet } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import FormField from '@ps/ui/form/FormField';
import Dialog from '@ps/ui/components/dialog';

import { useSnippetsGenerateItemContext } from './SnippetsGenerateItemProvider';

const validationSchema = Yup.object({
  name: Yup.string().required('Snippet name is required'),
});

export default function SnippetsGenerateItemSaveDialog() {
  const { generatedSnippetData, snippetValue, toggleSaveDialog, toggleIsSaved, isSaveDialogOpen } =
    useSnippetsGenerateItemContext();
  const { saveSnippet, isSaveSnippetError } = useSaveSnippet();

  const { handleSubmit, values, handleChange, touched, errors, isSubmitting } = useFormik({
    initialValues: {
      genId: generatedSnippetData!.genId,
      name: '',
      content: snippetValue,
    },
    onSubmit: async (submitValue, actions) => {
      await saveSnippet(submitValue, {
        onSuccess: () => {
          toast.success('Snippet saved successfully');
          toggleSaveDialog();
          actions.resetForm();
          toggleIsSaved();
        },
      });
    },
    validationSchema,
  });

  return (
    <Dialog
      as="form"
      open={isSaveDialogOpen}
      onOpenChange={toggleSaveDialog}
      Icon={() => null}
      title="Save snippet"
      description="You can save this text as a snippet that you can later easily find and reuse. Just give this snippet a name and hit save."
      onSubmit={(ev) => handleSubmit(ev as any)}
      id="writingSnippetsResultsSavedDialogForm"
      actions={
        <Button type="submit" className="w-full" loading={isSubmitting}>
          {isSaveSnippetError ? 'Try again' : 'Save'}
        </Button>
      }
      error={isSaveSnippetError ? 'Something is wrong' : null}
    >
      <FormField
        name="name"
        type="text"
        label="Snippet Name"
        placeholder="Type your snippet name"
        value={values.name}
        onChange={handleChange}
        error={touched.name && errors.name ? errors.name : undefined}
      />
    </Dialog>
  );
}
