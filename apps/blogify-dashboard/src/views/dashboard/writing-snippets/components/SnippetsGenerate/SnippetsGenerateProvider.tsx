import {
  IGeneratedWritingSnippet,
  IWritingSnippetType,
} from '@/types/resources/writing-snippets.type';
import React, { ReactNode, createContext, useContext, useState } from 'react';

export interface SnippetsGenerateProviderContextProps {
  snippet: IWritingSnippetType;
  generatedSnippetData: IGeneratedWritingSnippet | undefined;
  setGeneratedSnippetData: React.Dispatch<
    React.SetStateAction<IGeneratedWritingSnippet | undefined>
  >;
}

// eslint-disable-next-line react-refresh/only-export-components
export const SnippetsGenerateProviderContext =
  createContext<SnippetsGenerateProviderContextProps | null>(null);

export default function SnippetsGenerateProvider({
  snippet,
  children,
  ...props
}: {
  snippet: IWritingSnippetType;
  children: ReactNode;
}) {
  const [generatedSnippetData, setGeneratedSnippetData] = useState<IGeneratedWritingSnippet>();
  const value = { snippet, generatedSnippetData, setGeneratedSnippetData };
  return (
    <SnippetsGenerateProviderContext.Provider value={value} {...props}>
      {children}
    </SnippetsGenerateProviderContext.Provider>
  );
}

// custom hook
// eslint-disable-next-line react-refresh/only-export-components
export const useSnippetsGenerateContext = () => {
  const context = useContext(
    SnippetsGenerateProviderContext
  ) as SnippetsGenerateProviderContextProps;
  if (context === null) {
    throw new Error('useSnippetsGenerate must be used within a SnippetsGenerateProvider');
  }
  return context;
};
