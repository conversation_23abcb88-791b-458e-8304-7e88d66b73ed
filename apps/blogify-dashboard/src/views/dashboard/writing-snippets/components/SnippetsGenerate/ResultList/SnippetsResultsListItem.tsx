import type { IGeneratedWritingSnippet } from '@/types/resources/writing-snippets.type';

import { BsArrowsFullscreen, BsBookmark, BsBookmarkFill } from 'react-icons/bs';
import { useCallback, useEffect } from 'react';
import { FiCopy } from 'react-icons/fi';
import styled from 'styled-components';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import theme from '@/styles/theme';

import SnippetsGenerateItemProvider, {
  useSnippetsGenerateItemContext,
} from './SnippetsGenerateItemProvider';
import SnippetsGenerateItemSaveDialog from './SnippetsGenerateItemSaveDialog';
import SnippetsGenerateItemFullScreen from './SnippetsGenerateItemFullScreen';

export default function SnippetsResultsListItem({
  selectedKey,
  label,
}: {
  selectedKey: keyof IGeneratedWritingSnippet;
  label: string;
}) {
  return (
    <>
      <SnippetsGenerateItemProvider selectedKey={selectedKey}>
        {/* Save dialog */}
        <SnippetResultOutput label={label} />
        {/* Save dialog */}
        <SnippetsGenerateItemSaveDialog />
        {/* Full screen view */}
        <SnippetsGenerateItemFullScreen label={label} />
      </SnippetsGenerateItemProvider>
    </>
  );
}

export const SnippetResultOutput = ({
  label,
  minHeight = '200px',
}: {
  label: string;
  minHeight?: string;
}) => {
  const {
    snippetValue,
    setSnippetValue,
    isSaved,
    isSaveDialogOpen,
    toggleSaveDialog,
    toggleFullScreen,
    isFullScreen,
  } = useSnippetsGenerateItemContext();
  // Save button click
  const handleSave = useCallback(() => {
    toggleSaveDialog();
    if (isFullScreen) {
      toggleFullScreen();
    }
  }, [isFullScreen, toggleFullScreen, toggleSaveDialog]);
  // writing snippets tour guide
  const {
    state: { tourActive, stepIndex },
    setState,
  } = useWritingSnippetsTourGuideContext();
  useEffect(() => {
    if (tourActive && stepIndex === 7) {
      handleSave();
      setState({ run: true, stepIndex: 8 });
    }
  }, [tourActive, stepIndex, handleSave, toggleSaveDialog, isSaveDialogOpen, setState]);
  return (
    <>
      <div className="mb-2 flex items-center gap-1">
        <div className="text-xs font-semibold text-black1">{label}</div>
        {/* <div className="text-xs text-gray2">
          ∙ Inspirational
        </div> */}
      </div>
      <div className="rounded border border-gray5 bg-gray7">
        {/* Result */}
        <textarea
          className="h-max w-full resize-y border-none bg-transparent p-2 text-sm focus:border-none focus:outline-none"
          style={{ minHeight }}
          value={snippetValue}
          onChange={(e) => setSnippetValue(e.target.value)}
        />
        {/* Actions */}
        <div className="flex justify-between border-t border-gray5">
          <div className="flex">
            {/* Copy button */}
            <SnippetResultActionButton
              onClick={() => copy(snippetValue)}
              id="writingSnippetsResultsCopyButton"
            >
              <div className="flex items-center gap-1.5">
                <FiCopy color={theme.colors.gray3} size="11px" />
                COPY
              </div>
            </SnippetResultActionButton>
            {/* Save button */}
            <SnippetResultActionButton onClick={handleSave} isActive={isSaved}>
              <div className="flex items-center gap-1.5">
                {isSaved ? (
                  <BsBookmarkFill color={theme.colors.gray3} size="11px" />
                ) : (
                  <BsBookmark color={theme.colors.gray3} size="11px" />
                )}
                {isSaved ? 'SAVED' : 'SAVE'}
              </div>
            </SnippetResultActionButton>
          </div>
          {/* Full screen button */}
          <SnippetResultActionButton className="!border-r-0" onClick={toggleFullScreen}>
            <div className="flex items-center gap-1.5">
              <BsArrowsFullscreen color={theme.colors.gray3} size="11px" />
            </div>
          </SnippetResultActionButton>
        </div>
      </div>
    </>
  );
};

export const SnippetResultActionButton = styled(Button)<{ isActive?: boolean }>`
  color: #7a8eb2 !important;
  font-size: 11px !important;
  border: none !important;
  border-right: 1px solid ${theme.colors.gray5} !important;
  margin: 0 !important;
  border-radius: 0 !important;
  ${({ isActive }) =>
    isActive
      ? `
    background-color: ${theme.colors.gray6} !important;
  `
      : `
  background-color: transparent !important;
  `}
  :hover {
    background-color: ${theme.colors.gray6} !important;
  }
`;
