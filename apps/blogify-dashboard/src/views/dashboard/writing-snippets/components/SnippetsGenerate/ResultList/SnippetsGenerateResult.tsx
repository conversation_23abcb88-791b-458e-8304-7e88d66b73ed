import { useEffect } from 'react';

import { useWritingSnippetsTourGuideContext } from '@/context/WritingSnippetsTourGuide';

import { useSnippetsGenerateContext } from '../SnippetsGenerateProvider';
import SnippetsGenerateResultEmptyState from './SnippetsGenerateResultEmptyState';
import SnippetsResultsList from './SnippetsResultsList';

export default function SnippetsGenerateResult() {
  const { generatedSnippetData } = useSnippetsGenerateContext();

  // writing snippets tour guide
  const {
    state: { stepIndex },
    setState,
  } = useWritingSnippetsTourGuideContext();
  useEffect(() => {
    if (stepIndex === 5 && generatedSnippetData) {
      setState({ run: true });
    }
  }, [generatedSnippetData, setState, stepIndex]);
  return (
    <>
      {generatedSnippetData ? (
        <div id="writingSnippetsResults">
          <div className="font-semibold text-black1">Results</div>
          <div className="mt-2 text-sm text-black1">
            All results are in chronological order. Save the result you want to reuse. Once you move
            from this page all results will be gone.
          </div>
          {/* Snippets results */}
          <SnippetsResultsList />
        </div>
      ) : (
        <SnippetsGenerateResultEmptyState />
      )}
    </>
  );
}
