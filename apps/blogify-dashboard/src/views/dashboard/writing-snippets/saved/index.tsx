import { CgFileDocument } from 'react-icons/cg';

import { useWritingSnippets } from '@/utils/writingSnippets';
import { Button } from '@ps/ui/components/button';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { DashboardContainer } from '../../layout';
import SavedWritingSnippetsListProvider from '../components/SnippetsSavedList/SavedSnippetsListProvider';
import SavedSnippetsListPagination from '../components/SnippetsSavedList/SavedSnippetsListPagination';
import SavedSnippetsListTable from '../components/SnippetsSavedList/SavedSnippetsListTable';
import SavedSnippetsFilter from '../components/SnippetsSavedList/SavedSnippetsFilter';

const initialBreadcrumb = [
  {
    href: '/dashboard',
    title: 'Dashboard',
  },
  {
    href: '/dashboard/writing-snippets/saved',
    title: 'Saved Snippets',
  },
];

export default function SavedSnippetsList() {
  const { data: snippets, isLoading } = useWritingSnippets();
  // Primary action button
  const primaryActionButton = (
    <Link to="/dashboard/writing-snippets/all">
      <Button>
        <div className="flex items-center gap-1">
          <CgFileDocument size="16px" />
          <div className="hidden text-sm md:inline">Create New Snippet</div>
        </div>
      </Button>
    </Link>
  );
  return (
    <>
      <DashboardContainer
        title="Saved Snippets"
        breadcrumb={initialBreadcrumb}
        primaryAction={primaryActionButton}
      >
        {isLoading ? (
          <div className="flex h-full flex-center">
            <Loader />
          </div>
        ) : snippets ? (
          <SavedWritingSnippetsListProvider snippets={snippets}>
            <SavedSnippetsFilter />
            {/* Table list */}
            <div className="bg-white p-6">
              <SavedSnippetsListTable />
              <SavedSnippetsListPagination />
            </div>
            {/* Pagination */}
          </SavedWritingSnippetsListProvider>
        ) : null}
      </DashboardContainer>
    </>
  );
}
