import { CgFileDocument } from 'react-icons/cg';
import { useParams } from 'react-router-dom';
import { FiCopy } from 'react-icons/fi';

import { useGetSingleSavedSnippet } from '@/utils/writingSnippets';
import { DashboardContainer } from '@/views/dashboard/layout';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import Loader from '@/components/misc/Loader';
import theme from '@/styles/theme';
import Link from '@/components/common/Link';

import { SnippetResultActionButton } from '../components/SnippetsGenerate/ResultList/SnippetsResultsListItem';
import { WritingSnippetsCard } from '../styles/index.styled';

const initialBreadcrumb = [
  {
    href: '/dashboard',
    title: 'Dashboard',
  },
  {
    href: '/dashboard/writing-snippets/all',
    title: 'Writing Snippets',
  },
];
export default function SingleSavedSnippet() {
  const { snippetId } = useParams() as { snippetId: string };
  const { savedSnippetData, isSavedSnippetLoading, isSavedSnippetError } = useGetSingleSavedSnippet(
    { snippetId }
  );
  const primaryActionButton = (
    <Link to="/dashboard/writing-snippets/all">
      <Button>
        <div className="flex items-center gap-1">
          <CgFileDocument size="16px" />
          <div className="hidden text-sm md:inline">Create New Snippet</div>
        </div>
      </Button>
    </Link>
  );

  return (
    <>
      <DashboardContainer
        title="Snippet"
        breadcrumb={initialBreadcrumb}
        primaryAction={primaryActionButton}
      >
        {isSavedSnippetLoading ? (
          <div className="flex h-full flex-center">
            <Loader />
          </div>
        ) : isSavedSnippetError ? (
          <div className="flex h-full flex-center">
            <div className="font-semibold text-red">Snippet not found</div>
          </div>
        ) : (
          <>
            <WritingSnippetsCard
              variant="marketing"
              color={savedSnippetData?.categoryColor}
              fixedBg
            >
              <div className="flex flex-wrap justify-between sm:flex-nowrap">
                <div className="flex flex-col gap-3">
                  <img
                    className="size-12"
                    src={savedSnippetData?.categoryIcon}
                    alt="marketingLogo"
                  />
                  <div className="text-xs font-semibold uppercase text-[#7A8EB2]">
                    {savedSnippetData?.category}
                  </div>
                  <div className="font-semibold text-black1">{savedSnippetData?.name}</div>
                </div>
              </div>
            </WritingSnippetsCard>
            <div className="bg-white p-4 md:p-6">
              <div className="rounded border border-gray5 bg-gray7">
                {/* Text area */}
                <textarea
                  className="h-max min-h-[300px] w-full resize-y border-none bg-transparent p-2 text-sm focus:border-none focus:outline-none"
                  defaultValue={savedSnippetData?.content}
                />
                {/* Actions */}
                <div className="flex justify-between border-t border-gray5">
                  <SnippetResultActionButton onClick={() => copy(savedSnippetData?.content || '')}>
                    <div className="flex items-center gap-1.5">
                      <FiCopy color={theme.colors.gray3} size="11px" />
                      COPY
                    </div>
                  </SnippetResultActionButton>
                </div>
              </div>
            </div>
          </>
        )}
      </DashboardContainer>
    </>
  );
}
