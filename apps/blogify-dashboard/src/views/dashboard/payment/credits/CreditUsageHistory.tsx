import type { CreditTransaction, APIResponse } from '@ps/types';
import type { StatsAPIResponse } from '@/types/misc/stats.type';

import { FaRegArrowAltCircleDown, FaRegArrowAltCircleUp } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { MdMoreVert } from 'react-icons/md';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { getPackageAndPeriod } from '@/utils';
import { useStoreState } from '@/store';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';
import emptyStats from '@/types/misc/stats.type';

import DashboardContainer from '../../layout/DashboardContainer';
import AddBlogCredit from '../subscription/AddBlogCredit';

const getType = (transaction: CreditTransaction): string => {
  switch (transaction.type) {
    case 'CONTENT_GENERATION_CHARGE': {
      if (transaction.contentType?.includes('blog')) return 'Blog';
      if (['image', 'dall-e'].some((t) => transaction.contentType?.includes(t))) return 'AI Image';
      return 'Content Generation';
    }

    case 'MONTHLY_CREDITS_EXPIRATION':
      return 'Expired';

    case 'CONTENT_GENERATION_CHARGE_REFUND':
      return 'Reimbursed';

    case 'MONTHLY_CREDITS_ALLOCATION':
      return 'Credit Refill';

    case 'ADDITIONAL_CREDIT_PURCHASE':
      return 'Top Up';

    default:
      return transaction.mode === 'credit' ? 'Added' : 'Deducted';
  }
};

const getStatusColor = (status: CreditTransaction['status']): string => {
  switch (status) {
    case 'confirmed':
    case 'refunded':
      return 'bg-[#009933]/10 text-[#009933]';
    case 'unconfirmed':
      return 'bg-[#cc0022]/10 text-[#CC0022]';
    default:
      return 'bg-[#879f00]/10 text-[#879f00]';
  }
};

const getContentUrl = (transaction: CreditTransaction): string => {
  if (
    transaction.type === 'CONTENT_GENERATION_CHARGE' &&
    transaction.contentType?.includes('blog') &&
    (transaction.contentIds?.length || 0) > 0
  ) {
    return `/dashboard/blogs/${transaction.contentIds?.[0]}`;
  }
  return '';
};

export default function CreditTransactionHistory() {
  const { limit = '10', page = '1' } = parseQuery();

  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const navigate = useNavigate();
  const user = useStoreState((s) => s.user.current);

  const { data: { credits } = emptyStats } = useQuery<StatsAPIResponse>(['me/stats']);
  const { data: { data: creditTransactions, total } = emptyAPIResponse } = useQuery<
    APIResponse<CreditTransaction>
  >(['credit-transactions', { page, limit }]);

  return (
    <DashboardContainer title="Blogify Credits">
      <section className="my-6 flex min-h-40 flex-col justify-between rounded-lg border border-gray10 p-4 sm:flex-row">
        <div className="flex flex-col justify-between p-2">
          <div>
            <div className="text-11 font-medium text-primary">AVAILABLE CREDITS</div>
            <div className="mt-3 text-3xl font-semibold">{credits.remaining}</div>
          </div>

          <div className="text-15">
            Subscription:{' '}
            <span className="capitalize">{getPackageAndPeriod(user.subscriptionPlan).plan}</span> |
            Monthly Credits: {credits.total} | Reset at{' '}
            {dayjs().add(1, 'month').startOf('month').format('DD MMM YYYY')}
          </div>
        </div>

        <div className="mt-4 flex items-end justify-between">
          <Button onClick={toggleAddBlogCreditDialog}>Purchase Credits</Button>
        </div>
      </section>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Credit</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Info</TableHead>
            <TableHead className="text-right">Status</TableHead>
            <TableHead />
          </TableRow>
        </TableHeader>

        <TableBody>
          {creditTransactions.map((transaction, i) => (
            <TableRow key={i} className="[&>td]:even:bg-white">
              <TableCell>
                <div className="flex items-center gap-3 font-medium">
                  {transaction.mode === 'debit' ? (
                    <FaRegArrowAltCircleDown className="text-[#c02]" size={20} />
                  ) : (
                    <FaRegArrowAltCircleUp className="text-[#093]" size={20} />
                  )}
                  {transaction.amount}
                </div>
              </TableCell>

              <TableCell>{dayjs(transaction.createdAt).format('DD MMM YYYY')}</TableCell>
              <TableCell>{dayjs(transaction.createdAt).format('HH : mm : ss')}</TableCell>

              <TableCell>
                <Badge className="bg-bg2 text-gray9">{getType(transaction)}</Badge>
              </TableCell>

              <TableCell className="max-w-80 overflow-hidden text-ellipsis first-letter:capitalize">
                {getContentUrl(transaction) ? (
                  <Link className="hover:underline" to={getContentUrl(transaction)} target="_blank">
                    {transaction.description?.replace(/_/g, ' ')}
                  </Link>
                ) : (
                  <>{transaction.description?.replace(/_/g, ' ')}</>
                )}
              </TableCell>

              <TableCell className="text-right">
                <Badge className={cn(getStatusColor(transaction.status))}>
                  {transaction.status === 'confirmed' ? 'Successful' : 'Failed'}
                </Badge>
              </TableCell>

              <TableCell align="right">
                {getContentUrl(transaction) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="flex size-5 flex-center">
                        <MdMoreVert className="text-gray12" size={14} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem className="p-0">
                        <Link
                          className="w-full px-4 py-2.5"
                          to={getContentUrl(transaction)}
                          target="_blank"
                        >
                          View Content
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </TableCell>
            </TableRow>
          ))}

          {!creditTransactions.length && (
            <TableRow>
              <TableCell className="p-0" colSpan={6}>
                <div className="text-center text-sm text-gray2">No credit transactions so far.</div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Pagination
        className="mt-6"
        onPaging={(url) => navigate(url)}
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
      />

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
    </DashboardContainer>
  );
}
