import type { APIResponseType } from '@/types/resources';
import type { Transaction } from '@/types/resources/transaction.type';

import { useNavigate } from 'react-router-dom';
import { MdDownload } from 'react-icons/md';
import { useQuery } from 'react-query';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import { formatDate, formatTime } from '@/utils/date';
import { toFixedWithoutRound } from '@/utils';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';

import DashboardContainer from '../../layout/DashboardContainer';

const itemPerPage = 15;
export default function TransactionHistory() {
  const { limit = '10', page = '1' } = parseQuery();
  const navigate = useNavigate();

  const { data: { data: transactions, total } = emptyAPIResponse } = useQuery<
    APIResponseType<Transaction>
  >(['transactions', { page, limit: itemPerPage }]);

  return (
    <DashboardContainer title="Transaction History">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Description</TableHead>
            <TableHead>Payment Method</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Date Time</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {transactions.map((transaction, i) => (
            <TableRow key={i}>
              <TableCell>{transaction.description}</TableCell>
              <TableCell className="uppercase">{transaction.paymentMethod}</TableCell>
              <TableCell className="uppercase">{transaction.status}</TableCell>
              <TableCell>
                <div className="text-xs text-gray2">
                  {formatDate(transaction.createdAt as string)}{' '}
                  {formatTime(transaction.createdAt as string)}
                </div>
              </TableCell>
              <TableCell>
                <div>${toFixedWithoutRound(transaction.amount / 100, 2)}</div>
              </TableCell>
              <TableCell>
                <a href={transaction.paymentInvoiceUrl}>
                  <Button variant="icon" title="Download Invoice">
                    <div className="text-xl text-gray2">
                      <MdDownload />
                    </div>
                  </Button>
                </a>
              </TableCell>
            </TableRow>
          ))}

          {!transactions.length && (
            <TableRow>
              <TableCell className="p-0" colSpan={6}>
                <div className="text-center text-sm text-gray2">No transactions so far.</div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Pagination
        className="mt-6"
        onPaging={(url) => navigate(url)}
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
      />
    </DashboardContainer>
  );
}
