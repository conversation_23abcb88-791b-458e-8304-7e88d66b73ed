import type { NewPackageType } from '@/constants/packages';
import type { Plan } from '@/types/misc/plan.type';

import { useContext, useEffect, useState } from 'react';
import { useToggle } from 'react-use';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';

import { Tabs<PERSON>ontent, Ta<PERSON><PERSON>rigger, Ta<PERSON>List, Tabs } from '@ps/ui/components/tabs';
import { useStoreActions, useStoreState } from '@/store';
import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { getPackageAndPeriod } from '@/utils';
import { trackSubscription } from '@/services/analytics/google-analytics';
import { AccessDenied } from '@/components/misc/AccessDenied';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import DeactivateFeedbackDialog from '@/components/dialog/DeactivateFeedbackDialog';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';

import DashboardPricingCardContent from './DashboardPricingCardContent';

const Subscription = () => {
  const user = useStoreState((s) => s.user.current);
  const period = ['FREE', 'NOPLAN'].includes(user.subscriptionPlan)
    ? 'monthly'
    : getPackageAndPeriod(user.subscriptionPlan).period;
  const [pricingType, setPricingType] = useState<keyof NewPackageType['pricing']>(period);
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [upgrading, toggleUpgrading] = useToggle(false);
  const [plan, setPlan] = useState<Plan>();

  const fetchUser = useStoreActions((a) => a.user.fetch);
  const fetchPlans = async () => {
    API.fetch<Plan>('payments/plans').then((data: Plan | void) => {
      setPlan(data as Plan);
    });
  };

  const upgradeSubscription = () => {
    toggleUpgrading();
    API.put(`payments/end-trial-and-upgrade`)
      .then(() => {
        fetchUser(); // refresh user info
        if (plan) {
          trackSubscription(plan.subscriptionPrice, plan.subscriptionPlan);
          console.log(plan.subscriptionPrice);
        }
        toast.success('Subscription upgraded successfully!');
      })
      .catch((e) => toast.error(e?.error || e?.message || e))
      .finally(() => {
        toggleUpgrading();
        toggleConfirmationDialogOpen();
      });
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const abilities = useContext(UserAbilitiesContext);
  return !abilities.subscription.view ? (
    <main>
      <AccessDenied />
    </main>
  ) : (
    <>
      <Card className="w-full p-6">
        <div className="flex justify-between">
          <span className="text-xs font-semibold uppercase text-primary">Active Subscription</span>
          {/* <div className="text-gray2 text-sm">
            Account ID: #{user._id}
          </div> */}
        </div>

        <div className="mt-4 flex items-end justify-between">
          <div>
            <span className="text-2xl font-bold">{user.subscriptionPlan.replace('_', ' ')}</span>
            {/* <div className="font-semibold">(end in 21 days)</div> */}
          </div>

          <Link to="/dashboard/transaction-history">
            <Button variant="secondary">Transaction History</Button>
          </Link>
        </div>
      </Card>

      {user.isShopifyUser ? (
        <div className="pt-px">
          <Card className="min-h-[200px] px-2 text-sm font-semibold flex-center md:px-5">
            You can upgrade or cancel your subscription from Shopify.
          </Card>
        </div>
      ) : (
        <>
          <div className="pt-px">
            <div className="bg-white p-2 md:p-5">
              <Tabs
                onValueChange={(value) => setPricingType(value as keyof NewPackageType['pricing'])}
                value={pricingType}
                className="justify-center"
              >
                <div className="flex justify-center">
                  <TabsList className="mb-5">
                    <TabsTrigger value="monthly">Monthly</TabsTrigger>
                    <TabsTrigger value="yearly">Annually</TabsTrigger>
                    {plan?.isLatestPlan ? null : (
                      <TabsTrigger value="lifetime">Lifetime</TabsTrigger>
                    )}
                  </TabsList>
                </div>

                <TabsContent value="monthly">
                  <DashboardPricingCardContent pricingType={pricingType} color="black" />
                </TabsContent>
                <TabsContent value="yearly">
                  <DashboardPricingCardContent pricingType={pricingType} color="black" />
                </TabsContent>
                <TabsContent value="lifetime">
                  <DashboardPricingCardContent pricingType={pricingType} color="black" />
                </TabsContent>
              </Tabs>
            </div>
          </div>

          <div className="flex flex-wrap">
            {/* <div className="w-full p-2 xl:w-1/2">
              <Card className="p-6">
                <div className="font-semibold">Put your subscription on hold</div>
                <div className="mt-3 text-sm">
                  You will not be billed during the hold duration. Your subscription will be
                  reactivated and billed automatically at the end of the hold duration selected
                  below.
                </div>
                <div className="mt-6 flex justify-end">
                  <Button variant="primary">Hold</Button>
                </div>
              </Card>
            </div> */}

            {user.subscriptionStatus === 'trial' && (
              <div className="size-full pr-px pt-px xl:w-1/2">
                <Card className="p-6">
                  <div className="font-semibold">End Trial and Upgrade</div>
                  <div className="mt-3 text-sm">
                    Ready to unlock the full potential of Blogify? Upgrade your trial now to access
                    all features and maximize your blogging experience. Please be aware that your
                    payment method will be charged immediately upon upgrading.
                  </div>
                  <div className="mt-6 flex w-full justify-end">
                    <Button
                      onClick={toggleConfirmationDialogOpen}
                      loading={upgrading}
                      variant="secondary"
                    >
                      Upgrade
                    </Button>
                  </div>
                </Card>
              </div>
            )}

            {['active', 'trial'].includes(user.subscriptionStatus) &&
              user.subscriptionPlan !== 'FREE' && <SubscriptionCancelCard />}
          </div>

          <ConfirmationDialog
            isOpen={isConfirmationDialogOpen}
            confirmationMessage={'Are you sure you want to upgrade your subscription?'}
            onClose={toggleConfirmationDialogOpen}
            onConfirm={upgradeSubscription}
            confirmText="Upgrade"
            cancelText="Later"
            confirming={upgrading}
          />
        </>
      )}
    </>
  );
};

const SubscriptionCancelCard = () => {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [isFeedbackDialog, toggleFeedbackDialog] = useToggle(false);
  const [canceling, toggleCanceling] = useToggle(false);
  const [error, setError] = useState<string>('');

  const fetchUser = useStoreActions((a) => a.user.fetch);

  const cancelSubscription = (comment: string) => {
    toggleCanceling();
    API.remove(`payments/cancel-subscription?comment=${comment}`)
      .then(() => fetchUser())
      .catch((e) => {
        const err = e?.error || e?.message || e;
        toast.error(err);
        setError(err);
      })
      .finally(() => {
        toggleCanceling();
        toggleFeedbackDialog();
      });
  };

  return (
    <>
      <div className="size-full pt-px xl:w-1/2">
        <Card className="p-6">
          <div className="font-semibold">Cancel your subscription</div>
          <div className="mt-3 text-sm">
            We are sad to see you go. We are thriving to achieve our goal and pretty close to it.
            Feel free to cancel your subscription if you want, But your blog credits and Data will
            be still accessible for the next billing cycle.
            {/* Please reach out on our support chat or email us at{' '}
            <a href="mailto:<EMAIL>"><EMAIL></a> to if you have any feedback for
            us. */}
          </div>
          <div className="mt-6 flex w-full justify-end">
            <Button onClick={toggleConfirmationDialogOpen} variant="secondary">
              Cancel Subscription
            </Button>
          </div>
        </Card>
      </div>

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to cancel your subscription?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={toggleFeedbackDialog}
      />

      <DeactivateFeedbackDialog
        open={isFeedbackDialog}
        onOpenChange={toggleFeedbackDialog}
        description="
          Please take a min and tell us more about why you have chosen to deactivate your subscription.
          This wil help us to create better a better product for you.
        "
        onSubmit={cancelSubscription}
        loading={canceling}
        error={error}
      />
    </>
  );
};

export default Subscription;
