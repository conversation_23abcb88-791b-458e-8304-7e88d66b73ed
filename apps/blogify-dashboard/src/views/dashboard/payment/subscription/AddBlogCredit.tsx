import type { StripePaymentElementChangeEvent } from '@stripe/stripe-js';
import type { FormikValues } from 'formik';

import { PaymentElement, useElements, useStripe, Elements } from '@stripe/react-stripe-js';
import { useEffect, useState } from 'react';
import { GiTwoCoins } from 'react-icons/gi';
import { loadStripe } from '@stripe/stripe-js';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { stripePaymentElementOptions, stripeElementsOptionsLight } from '@/utils/stripe';
import { useStoreState, useStoreActions } from '@/store';
import { toFixedWithoutRound } from '@/utils';
import { useToggle } from 'react-use';
import { cacheGet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import Counter from '@/components/misc/Counter';
import useForm from '@/hooks/useForm';
import Dialog from '@ps/ui/components/dialog';
import config from '@/constants/config';

const initialValues = {
  credits: 100,
  amount: 0,
};

const validationSchema = Yup.object().shape({
  credits: Yup.number()
    .min(10, 'Min purchase is 10 Credits')
    .required('Credit amount is required.'),
});

const stripePromise = loadStripe(config.stripeKey);

const PurchaseCreditForm = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const [isSubmitting, setIsSubmitting] = useToggle(false);
  const [stripeEvent, setStripeEvent] = useState<StripePaymentElementChangeEvent | null>(null);
  const [formError, setFormError] = useState('');
  const [stripeError, setStripeError] = useState('');
  const packageByName = useStoreState((s) => s.context.packageByName);
  const user = useStoreState((s) => s.user.current);
  const elements = useElements();
  const stripe = useStripe();

  const fetchUser = useStoreActions((a) => a.user.fetch);

  const userPlan = packageByName[user.subscriptionPlan];
  const creditPrice = userPlan?.limit?.CREDIT_PRICE || 0;

  const submit = async (values: FormikValues) => {
    if (!elements || !stripe) return;

    if (!stripeEvent?.complete) {
      return setStripeError('Your card number is incomplete.');
    }

    toast.loading('Processing payment...', { duration: 3000 });
    setIsSubmitting();
    setFormError('');
    setStripeError('');

    try {
      const resp = await API.post<{ clientSecret: string }>('payments/create-payment-intent', {
        ...values,
        amount: values.amount,
      }).catch((e) => {
        setFormError(e?.error || e?.message || e);
        toast.error(e?.error || e?.message || e);
        setIsSubmitting();
        throw e;
      });

      if (resp?.clientSecret) {
        elements.submit();
        const result = await stripe.confirmPayment({
          elements,
          clientSecret: resp?.clientSecret,
          confirmParams: {
            return_url: `${config.apiUrl}payments/confirm-purchase-credit?token=${cacheGet(
              'token'
            )}`,
          },
        });

        if (result.error?.message) {
          throw new Error(result.error.message);
        }
      }

      await fetchUser();
      setIsSubmitting();
      toast.success('Payment successful!');
      onClose();
    } catch (e: any) {
      elements.getElement('payment')?.clear();
      setStripeError(e?.error || e?.message || e);
      toast.error(e?.error || e?.message || e);
      setIsSubmitting();
    }
  };

  const form = useForm({ initialValues, validationSchema, submit });
  const credits = form.getInputFields('credits').value;

  useEffect(() => {
    setStripeError('');
  }, [isOpen]);

  useEffect(() => {
    if (form && elements) {
      const creditsCost = Math.floor(credits * (creditPrice * 100));
      const amount = Math.max(0, creditsCost);
      form.setFieldValue('amount', amount);
      elements.update({ currency: 'usd', amount });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [credits, elements]);

  return (
    <Dialog
      onOpenChange={onClose}
      open={isOpen}
      Icon={GiTwoCoins}
      title="Purchase Credits"
      description="Credits can be used to generating blog, image and other AI generated content."
      as="form"
      actions={
        <Button className="w-full" disabled={!form.isValid} loading={isSubmitting} type="submit">
          Pay - ${toFixedWithoutRound(form.values.amount / 100, 2).toFixed(2)}
        </Button>
      }
      onSubmit={(ev) => form.submitForm(ev as any)}
      error={(formError || form.formValidationError) as string}
    >
      {/* @ts-ignore For Now */}
      <FormField
        containerClass="min-h-24"
        label={`Credit Amount ($${creditPrice}/credit)`}
        placeholder="Credit Amount"
        type="custom"
        {...form.getInputFields('credits')}
      >
        <Counter
          className="h-10"
          text=" "
          min={0}
          step={10}
          defaultValue={form.values.credits}
          onChange={(c) => form.setFieldValue('credits', c)}
        />
      </FormField>

      <FormField label="Total Cost" type="custom">
        <div className="flex h-10 items-center justify-between rounded-lg border border-gray10 px-3.5">
          <span className="text-17 font-semibold">
            ${toFixedWithoutRound(form.values.amount / 100, 2).toFixed(2)} USD
          </span>
          <span className="text-11 font-medium text-gray9">
            ${creditPrice} x {form.values.credits}
          </span>
        </div>
      </FormField>

      <div
        className={cn('mt-1 overflow-hidden rounded-lg border pb-px', {
          'border-red': stripeError,
          'border-prelude/20': !stripeError,
        })}
      >
        <PaymentElement onChange={setStripeEvent} options={stripePaymentElementOptions} />
      </div>

      {stripeError && <ErrorMessage className="-mb-5 mt-0.5 h-0">{stripeError}</ErrorMessage>}
    </Dialog>
  );
};

const PurchaseCredits = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => (
  <Elements stripe={stripePromise} options={stripeElementsOptionsLight}>
    <PurchaseCreditForm isOpen={isOpen} onClose={onClose} />
  </Elements>
);

export default PurchaseCredits;
