import { useLocation } from 'react-router-dom';

import { PricingItem } from '@/constants/packages';

interface Props {
  items: PricingItem[];
  packageName: string;
}

const CREDIT_PRICE: Record<string, number> = {
  free: 0.08,
  lite: 0.08,
  basic: 0.07,
  premium: 0.06,
  business: 0.05,
  enterprise: 0.04,
  unlimited: 0.04,
};

const MorePricingInfo: React.FC<Props> = ({ items, packageName }) => {
  const location = useLocation();
  const isSubscriptionPage = location.pathname === '/dashboard/subscription';

  return (
    <>
      <div className="flex flex-col gap-4">
        {items
          .filter((item) => !item.isFooter)
          .map((item, index) => (
            <div key={index} className="">
              <div className="flex flex-row justify-between">
                <p
                  className={`text-sm ${
                    isSubscriptionPage ? 'text-black' : 'text-white'
                  } font-medium`}
                >
                  {item.name}
                </p>
                <p
                  className={`text-sm ${
                    isSubscriptionPage ? 'text-black' : 'text-white'
                  } font-medium`}
                >
                  {item.value}
                </p>
              </div>
              <div className="flex flex-row justify-between">
                <p className="text-xs font-normal text-typo-secondary">
                  {item.feature.join(' / ')}
                </p>
              </div>
            </div>
          ))}
      </div>
      {/* footer  */}
      {items.some((item) => item.isFooter) && (
        <div className="mt-4 border-t border-secondary/25 pt-4">
          <p className={`text-sm ${isSubscriptionPage ? 'text-black' : 'text-white'} font-medium`}>
            &copy;{' '}
            {items
              .find((item) => item.isFooter)
              ?.feature[0].replace('CREDIT_PRICE', String(CREDIT_PRICE[packageName]))}
          </p>
        </div>
      )}
    </>
  );
};

export default MorePricingInfo;
