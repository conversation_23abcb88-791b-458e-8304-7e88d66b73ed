import { FaCaretDown, FaCaretUp } from 'react-icons/fa';
import { useToggle } from 'react-use';
import { useMemo } from 'react';
import { FaInfo } from 'react-icons/fa6';
import { Link } from 'react-router-dom';

import { SHOW_MORE_INFO, NewPackageType } from '@/constants/packages';
// import { toFixedWithoutRound } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';

import MorePricingInfo from './MorePricingInfo';

type PricingCardUrlAction = {
  type: 'url';
  title: string;
  url: string;
  external?: boolean;
};
type PricingCardOnAction = {
  type: 'action';
  title: string;
  onAction: () => void;
  disabled?: boolean;
  loading?: boolean;
};
type PricingCardAction = PricingCardUrlAction | PricingCardOnAction;

export default function PricingCard({
  name,
  pricing,
  features,
  // featured,
  pricingType,
  // discount,
  action,
  color = 'white',
}: NewPackageType & {
  pricingType: keyof NewPackageType['pricing'];
  action?: PricingCardAction;
  color?: 'white' | 'black';
}) {
  // const isInfo = features.map((feature) => feature.isInfo);
  // const discountedPrice = pricing[pricingType]
  //   ? (discount[pricingType] / 100) * pricing[pricingType]!
  //   : 0;
  // const newPrice = pricing[pricingType]! - discountedPrice;
  const [isShowMore, toggleShowMore] = useToggle(false);
  const tempFeatures = useMemo(
    () => (isShowMore ? features : features.slice(0, 9)),
    [features, isShowMore]
  );
  const saveBtn =
    pricingType === 'yearly' ? (
      <Button
        variant="glow-secondary"
        className={`w-[90px] text-xxs uppercase ${
          color === 'black' ? 'text-black0' : 'text-black5'
        }`}
      >
        save 20%
      </Button>
    ) : null;

  const [showInfo, setShowInfo] = useToggle(false);

  return (
    <div className="w-full">
      <div className="h-full rounded-xl border border-secondary/25">
        <div className="px-4 py-5">
          <div className="flex items-center gap-2">
            <p className={`text-sm font-semibold capitalize${color}`}>{name}</p>
            {/* {featured && (
              <Button
                as="div"
                className="px-[6px] py-0.5 rounded-[4px] uppercase font-ibx text-[10px] font-medium"
              >
                Popular
              </Button>
            )} */}
            <div className="">{saveBtn}</div>
          </div>
          <div className="mt-4 flex items-center gap-2">
            {/* if you have discount  */}
            <p className={cn(`text-[28px] font-bold${color}`)}>${pricing[pricingType]}</p>
            {/* {discountedPrice ? (
              <>
                <p className="text-primary font-bold text-[28px]">
                  ${toFixedWithoutRound(newPrice, 2)}
                </p>
                <p className="text-primary text-xs self-end mb-1.5">
                  {discount[pricingType]}% OFF
                </p>
              </>
            ) : null} */}
          </div>
          <p className={`mb-4 mt-1 text-xs${color}`}>
            {pricingType === 'yearly'
              ? 'Per Month'
              : pricingType === 'lifetime'
                ? 'Lifetime'
                : pricingType === 'monthly' && pricing[pricingType] === 0
                  ? '(no credit card required)'
                  : 'Per Month'}
          </p>
        </div>
        <div className="h-px w-full bg-secondary/25"></div>

        <div className="flex justify-end pr-3 pt-3">
          <button
            onClick={setShowInfo}
            className={`flex size-5 items-center justify-center rounded-full border border-secondary/25 text-xs text-typo-secondary hover:border-primary-50 hover:bg-primary hover:shadow-button-primary${color} relative z-20 bg-[#C5B8E00F]`}
          >
            {showInfo ? <FaCaretUp size="16px" className="text-typo-secondary" /> : <FaInfo />}
          </button>
        </div>

        <div className="">
          {showInfo && (
            <div className="-mt-8 w-full bg-[#C5B8E00F] px-4 pb-4 pt-10 backdrop-blur-md">
              <MorePricingInfo items={SHOW_MORE_INFO} packageName={name} />
            </div>
          )}
        </div>
        <div className="p-4">
          {tempFeatures.map(({ available, name: fName, value, includes }, i) => (
            <div className="flex items-start justify-between py-4" key={i}>
              <div className="flex flex-col gap-2">
                <p className={`text-sm font-medium${color}`}>{fName}</p>
                {includes?.length
                  ? includes.map((include, index) => (
                      <p key={index} className="text-sm font-medium text-typo-secondary">
                        {include.name}
                      </p>
                    ))
                  : null}
              </div>

              <div className="flex flex-col items-end gap-2">
                {!value ? (
                  <>
                    {!available ||
                    (pricingType === 'lifetime' &&
                      name !== 'enterprise' &&
                      fName === 'Writing Snippets (Addon)') ||
                    (pricingType === 'lifetime' &&
                      name === 'enterprise' &&
                      (fName === 'YouTube Connect Pro (Addon)' ||
                        fName === 'Writing Snippets (Addon)')) ||
                    (pricingType === 'lifetime' &&
                      name === 'business' &&
                      fName === 'YouTube Connect (Addon)') ? (
                      <p className={`text-sm font-semibold${color} ml-8`}>No</p>
                    ) : (
                      available && <p className={`text-sm font-semibold${color} ml-8`}>Yes</p>
                    )}
                  </>
                ) : Array.isArray(value) ? (
                  <div className="flex flex-wrap justify-center gap-2">
                    {value.map((v, vi) => (
                      <img
                        className="size-4"
                        key={vi}
                        src={v.logo}
                        alt={v.name}
                        aria-label={v.name}
                      />
                    ))}
                  </div>
                ) : (
                  <>
                    <p className={`text-sm font-semibold${color} ml-8`}>{value?.toString()}</p>
                    {includes?.length
                      ? includes.map((include, index) => (
                          <p className="text-xs font-semibold text-typo-secondary" key={index}>
                            {include.value}
                          </p>
                        ))
                      : null}
                  </>
                )}
              </div>
            </div>
          ))}

          <div className="mt-5 cursor-pointer" onClick={toggleShowMore}>
            <div className="flex items-center gap-[4.48px]">
              <p className={`text-sm font-semibold ${color}`}>
                {isShowMore ? 'Show less' : 'Show more (4)'}
              </p>
              {isShowMore ? (
                <FaCaretUp size="16px" color={color} />
              ) : (
                <FaCaretDown size="16px" color={color} />
              )}
            </div>
          </div>

          {action?.type == 'url' ? (
            <Link
              to={action.url}
              className="mt-8 block"
              target={action.external ? '_blank' : '_self'}
            >
              <Button variant="glow-secondary" className="w-full">
                {action.title}
              </Button>
            </Link>
          ) : action?.type == 'action' ? (
            <div className="mt-9">
              <Button
                className={`${color === 'black' ? '!text-black0' : 'text-black5'}`}
                variant="glow-secondary"
                onClick={action.onAction}
                disabled={action.disabled}
              >
                {action.title}
              </Button>
            </div>
          ) : null}
          <div className="flex justify-center text-center">
            {pricingType !== 'lifetime' && (
              <p className="mt-2 text-xs font-normal text-typo-secondary">3 Days Free Trial!</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
