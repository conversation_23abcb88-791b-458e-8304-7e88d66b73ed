import type { FormikValues } from 'formik';

import { useEffect, useState } from 'react';
import { useToggle } from 'react-use';
import { GiUpgrade } from 'react-icons/gi';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import {
  insertStripeInput,
  stripeCardElement,
  initStripe,
  elements,
  stripe,
} from '@/services/stripe';
import {
  getDiscountsForCoupon,
  dealCodesPrefixList,
  toFixedWithoutRound,
  getPackageName,
} from '@/utils';
import { useStoreActions, useStoreState } from '@/store';
import { NEW_PACKAGES, NewPackageType } from '@/constants/packages';
import { getStripeReturnRedirect } from '@/utils/stripe';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';
import Dialog from '@ps/ui/components/dialog';

import PricingCard from './components/PricingCard';

const DashboardPricingCardContent = ({
  pricingType,
  color,
}: {
  pricingType: keyof NewPackageType['pricing'];
  color: 'white' | 'black';
}) => (
  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
    {NEW_PACKAGES.map((pkg, i) => (
      <SubscriptionPackageCard pkg={pkg} pricingType={pricingType} key={i} color={color} />
    ))}
  </div>
);

export default DashboardPricingCardContent;

const SubscriptionPackageCard = ({
  pkg,
  pricingType,
  color,
}: {
  pkg: NewPackageType;
  pricingType: keyof NewPackageType['pricing'];
  color: 'white' | 'black';
}) => {
  const [shouldAddPaymentMethodDialog, toggleAddPaymentMethodDialog] = useToggle(false);
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [isUpgradeDialogOpen, toggleUpgradeDialogOpen] = useToggle(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [upgrading, toggleUpgrading] = useToggle(false);
  const [stripeEvent, setStripeEvent] = useState<any>({});
  const [formError, setFormError] = useState('');
  const [discount, applyDiscount] = useState(0);
  const fetchUser = useStoreActions((a) => a.user.fetch);
  const user = useStoreState((s) => s.user.current);

  const packageName = getPackageName(pkg.name, pricingType);
  const { packageByName } = useStoreState((s) => s.context);
  // const navigate = useNavigate();

  const upgradeSubscription = () => {
    toggleUpgrading();
    API.post(`payments/update-subscription`, {
      price_id: packageByName[packageName]?.id,
    })
      .then(async () => {
        await fetchUser();
        toggleUpgrading();
      })
      .catch(toggleUpgradeDialogOpen);
  };

  const submit = async (values: FormikValues) => {
    const coupon = values.coupon.trim();
    const selectedPackage = packageByName[packageName];
    const matchedDealCouponPrefix =
      coupon && dealCodesPrefixList.find((prefix) => coupon.startsWith(prefix));
    if (!matchedDealCouponPrefix && !stripeEvent.complete && selectedPackage?.price !== 0) {
      return;
    }
    setIsSubmitting(true);
    setFormError('');

    try {
      const clientSecret = await API.post<{ clientSecret: string }>(
        'payments/create-subscription?is-upgrade=true',
        { price_id: packageByName[packageName]?.id, coupon }
      )
        .then((resp) => resp?.clientSecret)
        .catch((e) => {
          if (
            e?.message === 'You must pass in a clientSecret when calling stripe.confirmPayment().'
          ) {
            setFormError('verifying...');
          } else {
            setFormError(e?.error || e?.message || e);
          }
          toast.error(e?.error || e?.message || e);
          setIsSubmitting(false);
        });

      if (clientSecret && selectedPackage.price !== 0) {
        elements.submit();
        const result = await stripe.confirmPayment({
          elements,
          clientSecret,
          confirmParams: {
            return_url: getStripeReturnRedirect('', {
              packageName: selectedPackage.name,
              coupon,
            }),
          },
        });
        if (result.error?.message) {
          throw new Error(result.error.message);
        }
        toast.success('Subscription upgraded successfully');
      }

      await fetchUser();

      window.location.reload();
    } catch (e: any) {
      if (e?.message === 'You must pass in a clientSecret when calling stripe.confirmPayment().') {
        stripeCardElement.clear();
        setFormError('');
        toast.success('Subscription upgraded successfully');
        window.location.reload();
      } else {
        setFormError(e?.error || e?.message || e);
        setIsSubmitting(false);
        stripeCardElement.clear();
      }
    }
  };

  const initialValues = {
    package: packageName,
    period: 'monthly',
    coupon: '',
  };

  const LogInSchema = Yup.object().shape({
    package: Yup.string().required(`Package is required`),
    period: Yup.string().required(`Subscription period is required`),
    coupon: Yup.string(),
  });

  const form = useForm({ initialValues, submit, validationSchema: LogInSchema });
  const { coupon: couponCode } = form.values;
  useEffect(() => {
    try {
      if (shouldAddPaymentMethodDialog) {
        const onInit = () => insertStripeInput(setStripeEvent);
        setTimeout(() => {
          initStripe(onInit);
        }, 100);
      }
    } catch (e) {}
  }, [shouldAddPaymentMethodDialog]);

  useEffect(() => {
    if (shouldAddPaymentMethodDialog) {
      if (stripe) {
        const newAmount = packageByName[packageName]?.price || 0;
        elements.update({ mode: 'subscription', currency: 'usd', amount: newAmount });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [packageName, stripe, shouldAddPaymentMethodDialog]);

  useEffect(() => {
    const setDiscount = async () => {
      const totalDiscount = await getDiscountsForCoupon(couponCode.trim());
      applyDiscount(totalDiscount.discount || 0);
    };
    setDiscount();
  }, [couponCode, toggleAddPaymentMethodDialog]);

  const actionTitle =
    user.subscriptionPlan === `${pricingType.toUpperCase()}_${pkg.name.toUpperCase()}`
      ? 'Activated'
      : 'Upgrade';
  const isActionDisabled =
    user.subscriptionPlan === `${pricingType.toUpperCase()}_${pkg.name.toUpperCase()}`;

  return (
    <>
      <PricingCard
        {...pkg}
        pricingType={pricingType}
        color={color}
        action={{
          title: actionTitle,
          type: 'action',
          onAction: () => toggleConfirmationDialogOpen(),
          disabled: isActionDisabled,
          loading: upgrading,
        }}
      />

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to upgrade your subscription?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={upgradeSubscription}
      />
      <ConfirmationDialog
        isOpen={isUpgradeDialogOpen}
        confirmationMessage={`You do not have any active payment method added or you have cancelled your subscription before. Please add payment method to upgrade your subscription. In case of lifetime plan if you have additional coupons then please press confirm to upgrade to higher plan.`}
        onClose={toggleUpgradeDialogOpen}
        onConfirm={toggleAddPaymentMethodDialog}
        confirmationTitle="Add Payment Method"
      />

      <Dialog
        open={shouldAddPaymentMethodDialog}
        onOpenChange={toggleAddPaymentMethodDialog}
        Icon={GiUpgrade}
        title="Upgrade Plan"
        description=""
        as="form"
        actions={
          <Button className="w-full" type="submit" loading={isSubmitting}>
            Upgrade
          </Button>
        }
        onSubmit={(ev) => form.submitForm(ev as any)}
      >
        <div
          className={`mt-7 border px-3 py-[10px] ${
            stripeEvent.error?.message ? 'border-red' : 'border-gray6'
          }`}
        >
          <div id="card-element" />
        </div>

        <ErrorMessage>
          <div id="card-element-errors" role="alert" />
        </ErrorMessage>

        {/* @ts-ignore For Now */}
        <FormField label="Coupon" {...form.getInputFields('coupon')} />
        <ErrorMessage className="mt-2">{formError || form.formValidationError}</ErrorMessage>

        <div className="mb-1 mt-3 justify-between text-sm">
          <div className="flex capitalize">
            Plan:{' '}
            <div className="ml-1 font-semibold">{packageName.toLowerCase().replace('_', ' ')}</div>
          </div>
          <div className="flex">
            Price:
            <div
              className={cn('ml-1 font-semibold text-black1', {
                '!text-gray3 line-through': discount > 0,
              })}
            >
              ${packageByName[packageName]?.price / 100}
            </div>
            {discount > 0 && (
              <div className="ml-1 font-semibold">
                $
                {toFixedWithoutRound(
                  packageByName[packageName]?.price / 100 -
                    (packageByName[packageName]?.price / 100) * (discount / 100),
                  2
                )}
              </div>
            )}
          </div>
        </div>
      </Dialog>
    </>
  );
};
