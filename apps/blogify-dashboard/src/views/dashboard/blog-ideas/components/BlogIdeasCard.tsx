import { FiMoreVertical, FiCalendar } from 'react-icons/fi';
import dayjs from 'dayjs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ps/ui/components/dropdown-menu';
import { MdDelete } from 'react-icons/md';
import { useToggle } from 'react-use';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import { useMutation } from 'react-query';
import { API } from '@/services/api';
import { Project } from '../types/project.type';
import { sanitizeUrl } from '@ps/common/utils/url';
import { Link } from 'react-router-dom';

type PropsType = {
  project: Project;
  refetchProjects: () => void;
};

const BlogIdeasCard = ({ project, refetchProjects }: PropsType) => {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);

  const {
    mutateAsync: deleteProject,
    isLoading: deleting,
    error,
  } = useMutation({
    mutationFn: () =>
      API.remove(`projects/${project._id}`).then(() => {
        refetchProjects();
        toggleConfirmationDialogOpen();
      }),
  });

  return (
    <>
      <Link to={`/dashboard/blog-ideas/${project._id}`} className="block">
        <div className="relative overflow-hidden  rounded-lg  bg-white">
          {/* Folder Top Shape */}
          <svg viewBox="0 0 220 20" width="100%" height="40" className="block">
            <path
              d="M0 8 C0 3 3 0 8 0 H60 C65 0 68 3 70 8 H212 C216 8 220 12 220 16 V40 H0 Z"
              fill="#E9E5E1"
            />
          </svg>

          {/* Folder Body */}
          <div className="rounded-b-lg border border-t-0 border-gray-200 bg-[#f4f1f0] p-4">
            <div className="flex items-start justify-between">
              <h3 className="truncate text-base font-semibold text-black">{project.name}</h3>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex size-5 flex-center">
                    <FiMoreVertical className="size-4" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleConfirmationDialogOpen();
                    }}
                    className="text-red4"
                  >
                    <MdDelete className="-mt-px" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <p className="mt-1 text-sm text-[#8c7e75]">{sanitizeUrl(project.websiteUrl)}</p>
            <div className="mt-3 flex items-center text-sm text-[#8c7e75]">
              <FiCalendar className="mr-2 size-4" />
              <span>{dayjs(project.createdAt).format('DD MMM YYYY')}</span>
            </div>
          </div>
        </div>
      </Link>

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to delete this project?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={deleteProject}
        error={error as string}
        confirming={deleting}
      />
    </>
  );
};

export default BlogIdeasCard;
