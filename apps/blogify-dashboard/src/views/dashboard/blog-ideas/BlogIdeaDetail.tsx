import { Outlet, Link, useParams, useLocation } from 'react-router-dom';
import { LuNotebookPen } from 'react-icons/lu';

import { Tabs, TabsList, TabsTrigger } from '@ps/ui/components/tabs';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '../layout/DashboardContainer';

const TABS = [{ name: 'Overview' }, { name: 'Ideas' }, { name: '<PERSON><PERSON><PERSON>' }];

export default function BlogIdeaDetail() {
  const { id } = useParams() as { id: string };
  const location = useLocation();

  const selectedTab =
    TABS.find((tab) => location.pathname.endsWith(tab.name.toLowerCase()))?.name || 'Overview';

  const showCreateBlogAction = [
    `/dashboard/blog-ideas/${id}`,
    `/dashboard/blog-ideas/${id}/ideas`,
  ].includes(location.pathname);

  return (
    <DashboardContainer
      title={`Project ${id}`}
      actions={
        showCreateBlogAction ? (
          <Link to={`/dashboard/blogs/select-source`}>
            <Button>
              <LuNotebookPen />
              Create Blog
            </Button>
          </Link>
        ) : null
      }
    >
      <Tabs defaultValue={selectedTab}>
        <TabsList variant="nav">
          {TABS.map((tab) => (
            <Link to={`./${tab.name.toLowerCase()}`} key={tab.name}>
              <TabsTrigger value={tab.name} variant="nav">
                {tab.name}
              </TabsTrigger>
            </Link>
          ))}
        </TabsList>

        <Outlet />
      </Tabs>
    </DashboardContainer>
  );
}
