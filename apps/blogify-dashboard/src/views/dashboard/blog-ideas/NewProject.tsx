import { Button } from '@ps/ui/components/button';
import DashboardContainer from '../layout/DashboardContainer';
import { useForm } from '@ps/ui/hooks/useForm';
import { z, zodResolver } from '@ps/ui';
import FormField from '@ps/ui/form/FormField';
import { useNavigate } from 'react-router-dom';
import { useMutation } from 'react-query';
import { API } from '@/services/api';
import { Project } from './types/project.type';
import toast from 'react-hot-toast';

const projectSchema = z.object({
  name: z.string({ required_error: 'Project name is required.' }),
  websiteUrl: z.string({ required_error: 'Website URL is required.' }),
});

export type ProjectSchema = z.infer<typeof projectSchema>;

export default function NewProject() {
  const navigate = useNavigate();

  const { getInputFields, handleSubmit } = useForm<ProjectSchema>({
    resolver: zodResolver(projectSchema),
    defaultValues: {},
  });

  const { mutateAsync: addProject, isLoading } = useMutation({
    mutationFn: (payload: ProjectSchema) => API.post<Project>(`projects`, payload),
  });

  const submit = (values: ProjectSchema) => {
    addProject(values)
      .then((value) => navigate(`/dashboard/blog-ideas/${value?._id}`))
      .catch((err) => {
        toast.error('Something went wrong.');
        console.error(err);
      });
  };

  return (
    <DashboardContainer
      title="New Project"
      actions={
        <>
          <Button variant="outline" size="sm" className="h-8 border bg-white text-red5 shadow">
            <img className=" size-4" src="/images/icons/icon-error.svg" />
            Cancel
          </Button>
        </>
      }
    >
      <form onSubmit={handleSubmit(submit)}>
        <div className="p-6 px-64">
          <h3 className="mb-3 text-lg font-medium">Let's start a new project!</h3>
          <p className="mb-6 text-sm text-gray-600">
            To create a new project, first add a project name. Add main topics of your website.
            Insert your website URL.
          </p>

          <div className="space-y-2">
            <FormField
              label="Project Name"
              placeholder="eg. Travel Switzerland"
              {...getInputFields('name')}
            />

            <FormField
              label="Website"
              placeholder="eg. website.com"
              {...getInputFields('websiteUrl')}
            />

            {/* <div>
            <label htmlFor="topics" className="mb-1 block text-sm font-medium text-gray-700">
              Topics
            </label>
            <Input id="topics" placeholder="eg. Travel, Nature, Hiking, etc" className="w-full" />
          </div> */}

            {/* <div className="flex items-center space-x-2">
            <Checkbox
              id="hasBlogs"
              checked={hasBlogs}
              onCheckedChange={(checked) => setHasBlogs(checked as boolean)}
              className="data-[state=checked]:border-[#EE5B30] data-[state=checked]:bg-[#EE5B30]"
            />
            <label
              htmlFor="hasBlogs"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I already have blogs on this website
            </label>
          </div> */}

            {/* {hasBlogs && (
            <div>
              <label htmlFor="blogsUrl" className="mb-1 block text-sm font-medium text-gray-700">
                Blogs URL
              </label>
              <Input
                id="blogsUrl"
                placeholder="eg. blog.website.com, website.com/blogs, etc"
                className="w-full"
              />
            </div>
          )} */}

            <Button
              className="mt-5 w-full bg-primary hover:bg-[#d24c25]"
              type="submit"
              loading={isLoading}
              disabled={isLoading}
            >
              Start a New Project
            </Button>
          </div>
        </div>
      </form>
    </DashboardContainer>
  );
}
