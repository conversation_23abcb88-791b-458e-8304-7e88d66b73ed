import { useOutletContext } from 'react-router-dom';
import { ImLink } from 'react-icons/im';
import { Project } from '../types/project.type';
import { PiPlugsConnected } from 'react-icons/pi';
import { FaUsers } from 'react-icons/fa';

export default function BlogIdeaWebsiteStatus() {
  const { ProjectData } = useOutletContext<{ ProjectData: Project }>();

  return (
    <div className=" mx-auto p-6 ">
      {/* Title and URL */}
      <div>
        <h2 className="text-2xl font-bold">{ProjectData.name}</h2>
        <a href="#" className="text-xs font-semibold text-[#93766c] hover:underline">
          {ProjectData.websiteUrl}
        </a>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 py-8 md:grid-cols-3">
        {[
          {
            icon: <FaUsers className="size-8 text-primary" />,
            label: 'Organic Traffic',
            value: Math.floor(ProjectData.domainAnalytics?.metrics.organic.etv || 0),
          },
          {
            icon: <PiPlugsConnected className="size-8 text-primary" />,
            label: 'Referring Domains',
            value: Math.floor(ProjectData.domainAnalytics?.backlinks_info.referring_domains || 0),
          },
          {
            icon: <ImLink className="size-8 text-primary" />,
            label: 'Backlinks',
            value: Math.floor(ProjectData.domainAnalytics?.backlinks_info.backlinks || 0),
          },
        ].map((stat, idx) => (
          <div key={idx} className="flex items-center space-x-4 rounded-lg border p-4">
            <div className="rounded-lg bg-orange-100 p-4">{stat.icon}</div>
            <div>
              <p className="text-sm font-medium">{stat.label}</p>
              <p className="text-2xl font-bold">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
