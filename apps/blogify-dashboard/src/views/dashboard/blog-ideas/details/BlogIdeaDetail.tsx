import { Outlet, Link, useParams, useLocation } from 'react-router-dom';
import { Tabs, TabsList, TabsTrigger } from '@ps/ui/components/tabs';
import DashboardContainer from '../../layout/DashboardContainer';
import { useQuery } from 'react-query';
import { Project } from '../types/project.type';
import StateHandler from '@/components/misc/StateHandler';

const TABS = [
  { name: 'Website Status', path: '' },
  { name: 'Blog Ideas', path: 'blog-ideas' },
  { name: 'Keywords', path: 'keywords' },
];

export default function BlogIdeaDetail() {
  const { id } = useParams() as { id: string };
  const location = useLocation();

  const { data: ProjectData, isLoading, error } = useQuery<Project>(`projects/${id}`);

  const selectedTab =
    TABS.find((tab) => location.pathname.endsWith(tab.path))?.name || 'Website Status';

  return isLoading || error || !ProjectData ? (
    <StateHandler
      loading={isLoading}
      error={error as string}
      isEmpty={!ProjectData}
      emptyMsg="No project found."
    />
  ) : (
    <DashboardContainer title={`Project: ${ProjectData.name}`}>
      <Tabs defaultValue={selectedTab}>
        <TabsList variant="nav">
          {TABS.map((tab) => (
            <Link to={`./${tab.path}`} key={tab.name}>
              <TabsTrigger value={tab.name} variant="nav">
                {tab.name}
              </TabsTrigger>
            </Link>
          ))}
        </TabsList>

        <Outlet context={{ ProjectData }} />
      </Tabs>
    </DashboardContainer>
  );
}
