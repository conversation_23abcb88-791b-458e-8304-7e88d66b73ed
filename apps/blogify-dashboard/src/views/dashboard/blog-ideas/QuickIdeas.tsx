'use client';

import { <PERSON><PERSON> } from '@ps/ui/components/button';
import { Popover, PopoverContent, PopoverTrigger } from '@ps/ui/components/popover';
import { useState } from 'react';
import { FaEdit, FaTrash, FaEllipsisV, FaSave } from 'react-icons/fa';
import DashboardContainer from '../layout/DashboardContainer';
import { Checkbox } from '@ps/ui/components/checkbox';
import toast from 'react-hot-toast';

type IdeaItemProps = {
  title: string;
  selected: boolean;
  onSelect: () => void;
  onDelete?: () => void;
};

const IdeaItem = ({ title, selected, onSelect, onDelete }: IdeaItemProps) => (
  <div className="flex flex-wrap items-center justify-between gap-2 border-b border-gray-100 p-3">
    <div className="flex min-w-0 flex-1 items-center gap-3">
      <Checkbox
        checked={selected}
        onCheckedChange={() => onSelect()}
        className="size-4 shrink-0 accent-orange-500"
      />
      <span className={`break-words ${selected ? 'font-medium' : 'text-gray-700'}`}>{title}</span>
    </div>
    <Popover>
      <PopoverTrigger asChild>
        <button className="shrink-0 text-gray-400">
          <FaEllipsisV size={14} />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-40 p-0" side="right" align="start">
        <div className="py-1">
          <button
            onClick={onDelete}
            className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-[#c02] hover:bg-gray-100"
          >
            <FaTrash size={12} />
            <span>Delete</span>
          </button>
        </div>
      </PopoverContent>
    </Popover>
  </div>
);

const QuickIdeas = () => {
  const [selectedIdeas, setSelectedIdeas] = useState<Record<string, boolean>>({
    'Matching Outfits for the Whole Family: Fun and Stylish Ideas.': true,
    'Dressing Up Your Baby: Cute and Cozy Outfits for Infants': false,
    "Keeping Your Kids' Clothes Clean and Stain-Free: Tips and Tricks": true,
    "The Best Colors and Prints for Your Kids' Clothes This Season": true,
    'Fashion Tips: Perfect Summer Outfits for Boys': false,
  });

  const [activeFilters] = useState(['Baby Outfits', 'Fashion', 'Clothes']);

  const toggleIdeaSelection = (idea: string) => {
    setSelectedIdeas((prev) => ({
      ...prev,
      [idea]: !prev[idea],
    }));
  };

  const deleteIdea = (idea: string) => {
    const newIdeas = { ...selectedIdeas };
    delete newIdeas[idea];
    setSelectedIdeas(newIdeas);
    toast.success('Idea deleted');
  };

  const deleteAllIdeas = () => {
    setSelectedIdeas({});
    toast.success('All results deleted');
  };

  const saveAllIdeas = () => {
    toast.success('All ideas saved!');
  };

  const updateIdeas = () => {
    toast.success('Ideas updated!');
  };

  const generateMoreIdeas = () => {
    toast.loading('Generating more ideas...');
    setTimeout(() => {
      toast.dismiss();
      toast.success('More ideas generated!');
    }, 1500);
  };

  const saveBlogIdeas = () => {
    toast.success('Blog ideas saved!');
  };

  const cancelHandler = () => {
    toast('Canceled', { icon: '🚫' });
  };

  return (
    <DashboardContainer
      title="Get Ideas"
      className="flex h-full flex-col"
      actions={
        <Button
          variant="outline"
          size="sm"
          className=" h-8 border bg-white text-red5 shadow"
          onClick={cancelHandler}
        >
          <img className="size-4" src="/images/icons/icon-error.svg" />
          Cancel
        </Button>
      }
    >
      <div className="gap-6 p-4 sm:px-8 md:px-16 lg:px-32 xl:px-64">
        <div>
          <h3 className="mb-2 text-lg font-medium">Generate blog ideas</h3>
          <p className="mb-4 text-sm text-gray-600 sm:text-base">
            Let AI help you come up with blog ideas. Insert what you want to write about. Insert
            your SEO keywords if you want them in your blog.
          </p>
        </div>

        {/* Ideas container */}
        <div className="rounded-lg border bg-gray-50">
          <div className="flex flex-col gap-2 border-b bg-[#f4f1f0]  p-4">
            <div className="flex flex-wrap items-center justify-between gap-2">
              <div className="flex flex-col">
                <div className="font-bold">Quick Ideas ({Object.keys(selectedIdeas).length})</div>
                <div className="text-sm text-[#93766c]">Researched On: 12 Jun 2025</div>
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <button className="text-gray-400">
                    <FaEllipsisV size={14} />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-40 p-0" side="right" align="start">
                  <div className="w-56 rounded-xl border bg-white py-1 shadow-md">
                    <Button
                      variant="ghost"
                      className="w-full justify-start px-3 py-2 font-medium text-black hover:bg-gray-100"
                      onClick={saveAllIdeas}
                    >
                      <FaSave size={16} className="mr-2" />
                      Save All Ideas
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start px-3 py-2 font-medium text-black hover:bg-gray-100"
                      onClick={updateIdeas}
                    >
                      <FaEdit size={16} className="mr-2" />
                      Update Research
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start px-3 py-2 font-medium text-[#c02] hover:bg-gray-100 hover:text-[#c02]"
                      onClick={deleteAllIdeas}
                    >
                      <FaTrash size={16} className="mr-2" />
                      Delete Results
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Filter tags */}
            <div className="flex flex-wrap gap-2 py-2">
              {activeFilters.map((filter) => (
                <span key={filter} className="rounded-lg bg-white px-3 py-1 text-sm">
                  {filter}
                </span>
              ))}
            </div>
          </div>

          {/* Ideas list */}
          <div className="bg-white">
            {Object.entries(selectedIdeas).map(([idea, selected]) => (
              <IdeaItem
                key={idea}
                title={idea}
                selected={selected}
                onSelect={() => toggleIdeaSelection(idea)}
                onDelete={() => deleteIdea(idea)}
              />
            ))}
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 bg-white p-3">
            <Button
              size="xs"
              className="w-full bg-primary hover:bg-orange-600 sm:w-auto"
              onClick={saveBlogIdeas}
            >
              SAVE BLOGS IDEAS
            </Button>
            <Button
              variant="outline"
              size="xs"
              className="w-full sm:w-auto"
              onClick={generateMoreIdeas}
            >
              GENERATE MORE
            </Button>
          </div>
        </div>
      </div>
    </DashboardContainer>
  );
};

export default QuickIdeas;
