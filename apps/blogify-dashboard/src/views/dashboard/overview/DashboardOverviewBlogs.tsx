import type { APIResponseType } from '@/types/resources';
import type { Blog } from '@ps/types';

import { useQuery } from 'react-query';

import { BlogNoEntries } from '@/views/dashboard/blog/components/BlogLayout';
import { useStoreState } from '@/store';
import { UserRole } from '@/types/resources/user.type';
import { Avatar } from '@ps/ui/components/avatar';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';
import SocialIcon from '@/components/misc/SocialIcon';
import Link from '@/components/common/Link';

import useBlog from '../blog/hooks/useBlog';
import Loader from '@/components/misc/Loader';

const DashboardOverviewBlogs = () => {
  const user = useStoreState((s) => s.user.current);
  const { isFetching, data: { data: blogs } = emptyAPIResponse } = useQuery<APIResponseType<Blog>>([
    'blogs',
    { limit: 5, ...(user.role === UserRole.writer && { uid: user._id }) },
  ]);

  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname
    <div className="joyRide-my-blogs mx-6 rounded-lg border border-gray10 p-6">
      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <h2 className="text-19 font-semibold">Recent Blogs</h2>

        <div className="flex gap-2">
          <Link to="/dashboard/blogs/select-source">
            <Button variant="gray" className="text-primary">
              <img className="size-4 " src={`/images/icons/stars.svg`} alt="Stars Icon" />
              Create New Blog
            </Button>
          </Link>
          <Link to="/dashboard/blogs">
            <Button variant="gray">View All Blogs</Button>
          </Link>
        </div>
      </div>

      {!blogs.length && <BlogNoEntries className="!min-h-[40vh] bg-white" loading={isFetching} />}

      <div className="flex flex-col gap-6">
        {blogs.slice(0, 5).map((blog, i) => (
          <DashboardOverviewBlogCard key={i} blog={blog} />
        ))}
      </div>
    </div>
  );
};

const DashboardOverviewBlogCard = ({ blog: propBlog }: { blog: Blog }) => {
  const { getBlogContent, redirect, blog, isFailed } = useBlog(propBlog._id, {
    initialBlog: propBlog,
    isListView: true,
  });

  return (
    <div className="flex flex-wrap items-center gap-4 md:flex-nowrap">
      <Link className="aspect-video w-full shrink-0 md:h-[100px] md:w-44" to={redirect}>
        {isFailed ? (
          <div className="flex size-full rounded-lg bg-gray6 flex-center">
            <Loader status="error" />
          </div>
        ) : (
          <img
            className="size-full rounded-lg object-cover"
            src={propBlog.image || '/images/temp-bg.jpg'}
          />
        )}
      </Link>

      <div className="flex w-full flex-col gap-1">
        <Link to={redirect}>
          <h3 className="text-15 font-semibold">{blog.title}</h3>
        </Link>

        <div className="flex items-center gap-1">
          <Avatar
            className="size-4 object-cover text-[8px] font-semibold"
            alt={`${blog.uid?.name}'s Profile Picture`}
            src={blog.uid?.profilePicture}
            name={blog.uid?.name}
          />
          <p className="text-13 text-gray9">
            {blog.uid?.name}&nbsp; · &nbsp;{blog.wordCount} Words
          </p>
        </div>

        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <p className={`ellipsis r1 text-md ${isFailed ? 'text-red4' : ''}`}>{getBlogContent()}</p>

        <div className="flex gap-1">
          <div
            className={cn('flex h-6 items-center rounded-md px-2 !text-11 font-medium uppercase', {
              'border border-gray10 text-gray9': blog.publishStatus === 'draft',
              'bg-primary/10   text-primary': blog.publishStatus === 'scheduled',
              'bg-primary text-white': blog.publishStatus === 'published',
            })}
          >
            {blog.publishStatus}
          </div>

          {blog.platforms.map((bp, i) => (
            <SocialIcon
              key={i}
              className="rounded-md"
              integration={bp.platform}
              to={blog[`${bp.platform}Link`] || ''}
            />
          ))}
        </div>

        {/* <div className="mt-1 text-sm text-gray2">
          {toLocalizedRelativeTime(blog.publishTime || blog.publishAt).startsWith('in')
            ? `Publish ${toLocalizedRelativeTime(blog.publishTime || blog.publishAt)}`
            : blog.publishTime || blog.publishAt
              ? `Published ${toLocalizedRelativeTime(blog.publishTime || blog.publishAt)}`
              : ''}
        </div> */}
      </div>

      {/* <div className="shrink-1 flex w-full flex-wrap items-center sm:w-8/12 md:w-10/12">

        <div className="ml-0 flex w-full flex-wrap items-center sm:ml-5 lg:ml-0 lg:w-1/2">
          <div className="mt-3 w-1/2 md:w-1/3 lg:mt-0">
            <div className="mb-1 text-sm text-gray2">Blogs</div>
            <div className="flex">
              {blog.platforms.length ? (
                blog.platforms.map((bp, i) => (
                  <SocialIcon
                    key={i}
                    className="mr-2"
                    integration={bp.platform}
                    to={blog[`${bp.platform}Link`] || ''}
                  />
                ))
              ) : (
                <>- -</>
              )}
            </div>
          </div>

          <div className="mt-3 w-1/2 md:w-1/3 lg:mt-0">
            <div className="mb-1 text-sm text-gray2">Socials</div>
            <div className="flex">
              {blog.socials.length ? (
                blog.socials.map((sp, i) => (
                  <SocialIcon
                    key={i}
                    className="mr-2"
                    integration={sp.platform}
                    to={blog[`${sp.platform}Link`] || ''}
                  />
                ))
              ) : (
                <>- -</>
              )}
            </div>
          </div>

          {(['Clicks', ''] as 'Clicks'[]).map((t, i) => (
            <div key={i} className="mt-3 w-1/2 md:w-1/3 lg:mt-0">
              <div className="mb-1 text-sm text-gray2">{t}</div>
              <div className="text-sm">{blog[`total${t}`] ? blog[`total${t}`] : '- -'}</div>
            </div>
          ))}
        </div>
      </div> */}
    </div>
  );
};

export default DashboardOverviewBlogs;
