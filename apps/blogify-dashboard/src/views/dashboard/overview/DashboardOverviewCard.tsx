import { Button } from '@ps/ui/components/button';
import Link from '@/components/common/Link';

type DashboardOverviewCardProps = {
  color: string;
  title: string;
  subTitle?: string;
  whole?: string | number;
  actionTitle: string;
  actionLink: string;
  onAction?: () => void;
  className?: string;
};

const DashboardOverviewCard = ({
  color,
  title,
  subTitle,
  whole,
  actionTitle,
  actionLink,
  onAction,
  className,
}: DashboardOverviewCardProps) => (
  <div
    className={`flex min-h-[164px] w-full flex-col justify-between rounded-md p-4 ${className}`}
    style={{ backgroundColor: color }}
  >
    <div>
      <h3 className="text-15 font-medium">{title}</h3>
      <h2 className="text-[28px] font-semibold">{whole}</h2>
    </div>

    <div>
      <p className="mb-2 text-13">{subTitle}</p>
      <Link
        to={actionLink || './'}
        onClick={() => {
          if (onAction) onAction();
        }}
      >
        <Button className="w-full border-none !text-11" size="xs" variant="secondary">
          {actionTitle}
        </Button>
      </Link>
    </div>
  </div>
);

export default DashboardOverviewCard;
