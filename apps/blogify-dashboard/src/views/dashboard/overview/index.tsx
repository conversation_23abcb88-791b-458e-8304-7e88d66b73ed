import type { StatsAPIResponse } from '@/types/misc/stats.type';

import { useContext } from 'react';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import emptyStats from '@/types/misc/stats.type';

import DashboardOverviewBlogs from './DashboardOverviewBlogs';
import DashboardOverviewCard from './DashboardOverviewCard';
import AddBlogCredit from '../payment/subscription/AddBlogCredit';
import Analytics from '../analytics/Analytics';
import PayoutRequestDialog from '../wallet/components/PayoutRequestDialog';

const CURRENCY: Record<string, string> = {
  USD: '$',
};

const Overview = () => {
  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const [isPayoutRequestDialogOpen, togglePayoutRequestDialog] = useToggle(false);
  const abilities = useContext(UserAbilitiesContext);

  const { data = emptyStats } = useQuery<StatsAPIResponse>(['me/stats']);
  const { blogCount, website, credits, wallet } = data;

  return (
    <>
      <div className="bg-white pb-6">
        <div className="grid grid-cols-1 gap-4 bg-white p-6 sm:pt-2 md:grid-cols-2 lg:grid-cols-4">
          <DashboardOverviewCard
            // eslint-disable-next-line tailwindcss/no-custom-classname
            className="joyRide-blogs-created"
            title="Total Blogs"
            subTitle={`New Blogs : ${blogCount.thisMonth} (This month)`}
            whole={blogCount.lifeTime}
            color="#E8F1F4"
            actionTitle="CREATE NEW BLOG"
            actionLink="/dashboard/blogs/select-source"
          />
          <DashboardOverviewCard
            className=""
            title="Active Sites"
            subTitle={`Published : ${website.blogCount} Blogs`}
            whole={website.count}
            color="#E7F3F0"
            actionTitle="HOST A NEW BLOG SITE"
            actionLink="/dashboard/websites/add"
          />
          {abilities.credits.buy && (
            <DashboardOverviewCard
              // eslint-disable-next-line tailwindcss/no-custom-classname
              className="joyRide-blog-credit"
              title="Available Credits"
              subTitle={`Monthly Credits : ${credits.total}`}
              whole={credits.remaining}
              color="#EEF3E5"
              actionTitle={`PURCHASE CREDITS`}
              actionLink="/dashboard"
              onAction={toggleAddBlogCreditDialog}
            />
          )}
          {abilities.affiliate.view && (
            <DashboardOverviewCard
              // eslint-disable-next-line tailwindcss/no-custom-classname
              className="joyRide-your-earnings"
              title="Wallet"
              subTitle={`Last Withdraw : ${CURRENCY[wallet.currency]}${wallet.lastWithdraw.toFixed(2)}`}
              whole={`${CURRENCY[wallet.currency]}${wallet.balance.toFixed(2)}`}
              color="#FBF1EA"
              actionTitle="MAKE A WITHDRAW"
              actionLink="/dashboard/"
              onAction={togglePayoutRequestDialog}
            />
          )}
        </div>
        <DashboardOverviewBlogs />
      </div>

      <Analytics className="p-6 pt-0" />

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
      <PayoutRequestDialog isOpen={isPayoutRequestDialogOpen} onClose={togglePayoutRequestDialog} />
    </>
  );
};

export default Overview;
