// بسم الله الرحمن الرحيم
import type { IconType } from 'react-icons';
import type { User } from '@/types/resources/user.type';

import { FaRegCircleDot, FaPaperPlane, FaUserCheck, FaRegCircle } from 'react-icons/fa6';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { LuOctagonMinus } from 'react-icons/lu';
import { FaUserEdit } from 'react-icons/fa';
import { useContext } from 'react';
import { MdCancel } from 'react-icons/md';
import { ImBin2 } from 'react-icons/im';
import dayjs from 'dayjs';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { useStoreState } from '@/store';
import { UserStatus } from '@/types/resources/user.type';
import { UserRole } from '@ps/types';
import { Avatar } from '@ps/ui/components/avatar';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';

import { UserRoleBadge } from '.';
import { DialogAction } from '../UserList';

export default function UserTable({
  users,
  dispatch,
}: {
  users: User[];
  dispatch: React.Dispatch<DialogAction>;
}) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {['Name', 'Email', 'Role', 'Added On', 'Status', ''].map((heading) => (
            <TableHead key={heading}>{heading}</TableHead>
          ))}
        </TableRow>
      </TableHeader>

      <TableBody>
        {users.map((user) => (
          <TableRow key={user._id}>
            <TableCell>
              <div className="flex items-center gap-3">
                <Avatar className="size-6" src={user.profilePicture} name={user.name} />
                <span className="font-medium capitalize">{user.name}</span>
              </div>
            </TableCell>

            <TableCell className="text-15">{user.email}</TableCell>

            <TableCell>
              <UserRoleBadge role={user.role} />
            </TableCell>

            <TableCell className="text-13 text-gray9">
              <time>{dayjs(user.createdAt).format('D MMM, YYYY  -  HH:mm:DD')}</time>
            </TableCell>

            <TableCell>
              <Status status={user.status} />
            </TableCell>

            <TableCell className="text-right">
              <UserTableDropdownMenu user={user} dispatch={dispatch} />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}

const UserTableDropdownMenu = ({
  user,
  dispatch,
}: {
  user: User;
  dispatch: React.Dispatch<DialogAction>;
}) => {
  const currentUser = useStoreState((s) => s.user.current);
  const abilities = useContext(UserAbilitiesContext);

  const showDialog = (
    target: 'enable' | 'disable' | 'delete' | 'reinvite' | 'update' | 'change-role'
  ) => {
    dispatch({ type: 'open', target, payload: user });
  };

  if (currentUser.role === UserRole.editor && user.status !== UserStatus.inactive) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="size-8 p-0" variant="ghost">
          <BsThreeDotsVertical className="text-gray9" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {user.status === UserStatus.active ? (
          <>
            <DropdownMenuItem
              onClick={() => showDialog('change-role')}
              disabled={!abilities.user.invite}
            >
              <FaUserEdit />
              Change Role
            </DropdownMenuItem>

            <DropdownMenuItem
              className="font-medium text-red4 hover:!text-red4"
              onClick={() => showDialog('disable')}
              disabled={!abilities.user.deactivate}
            >
              <LuOctagonMinus />
              Disable
            </DropdownMenuItem>
          </>
        ) : (
          <>
            {user.status !== UserStatus.deactivated ? (
              <>
                <DropdownMenuItem
                  onClick={() => showDialog('reinvite')}
                  disabled={!abilities.user.invite}
                >
                  <FaPaperPlane />
                  {user.status === UserStatus.inactive ? 'Invite' : 'Resend'}
                </DropdownMenuItem>

                {user.status === UserStatus.inactive && (
                  <DropdownMenuItem onClick={() => showDialog('update')}>
                    <FaUserEdit />
                    Update Profile
                  </DropdownMenuItem>
                )}
              </>
            ) : (
              <DropdownMenuItem
                onClick={() => showDialog('enable')}
                disabled={!abilities.user.deactivate}
              >
                <FaUserCheck />
                Enable
              </DropdownMenuItem>
            )}

            <DropdownMenuItem
              className="font-medium text-red4 hover:!text-red4"
              onClick={() => showDialog('delete')}
              disabled={!abilities.user.deactivate}
            >
              <ImBin2 />
              Delete
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const statusConfig: Record<
  keyof typeof UserStatus,
  { name: string; color: string; icon: IconType }
> = {
  active: { name: 'Active', color: 'text-primary', icon: FaRegCircleDot },
  invited: { name: 'Invited', color: 'text-success', icon: FaPaperPlane },
  inactive: { name: 'Inactive', color: 'text-gray9', icon: FaRegCircle },
  deactivated: { name: 'Disabled', color: 'text-gray9', icon: MdCancel },
};
const Status = ({ status }: { readonly status: UserStatus }) => {
  const Icon = statusConfig[status].icon;

  return (
    <div
      className={cn(
        'flex items-center gap-1.5 !text-11 font-medium uppercase',
        statusConfig[status].color
      )}
    >
      <Icon size={8} />
      {statusConfig[status].name}
    </div>
  );
};
