import type { User } from '@ps/types';

import { LuOctagonMinus } from 'react-icons/lu';
import { FaUserCheck } from 'react-icons/fa6';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import Dialog from '@ps/ui/components/dialog';

import { UserCard } from '.';

export const UserActiveStatusToggleDialog = ({
  user,
  isOpen,
  variant,
  onConfirm,
  onClose,
}: {
  user: User;
  isOpen: boolean;
  variant: 'enable' | 'disable';
  onConfirm: () => void;
  onClose: () => void;
}) => {
  const toDisable = variant === 'disable';

  const {
    mutateAsync: deactivate,
    isLoading,
    error,
  } = useMutation({
    mutationFn: async () => API.post(`users/${user._id}/toggle-active-status`),
    onSuccess: () => {
      toast.success(`User ${toDisable ? 'disabled' : 'enabled'}.`);
      onConfirm();
      onClose();
    },
  });
  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={toDisable ? () => <LuOctagonMinus className="size-14 text-[#da840b]" /> : FaUserCheck}
      title={`${toDisable ? 'Disable' : 'Enable'} User`}
      description={
        toDisable
          ? 'The following user will be disabled from this Blogify account. The user would not be able to access this account but all of their data / blogs will stay as it is. If enabled again, the user would be able to resume their work from where they left off.'
          : 'The following user will be enabled for this Blogify account. The user will be able to access this account again and all of their data / blogs will be as it was. The user would be able to resume their work from where they left off.'
      }
      actions={
        <>
          <Button
            className={cn('min-w-28', { 'bg-[#da840b] hover:bg-[#da840b]/90': toDisable })}
            onClick={() => deactivate()}
            loading={isLoading}
          >
            {toDisable ? 'Disable' : 'Enable'} User
          </Button>
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
        </>
      }
      error={error as Error}
    >
      <UserCard user={user} />
    </Dialog>
  );
};
