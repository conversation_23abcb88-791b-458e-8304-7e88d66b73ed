// بسم الله الرحمن الرحيم
import type { User } from '@/types/resources/user.type';

import { FaUserEdit } from 'react-icons/fa';
import { useMutation } from 'react-query';
import { useState } from 'react';
import toast from 'react-hot-toast';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Dialog from '@ps/ui/components/dialog';

import { UserRoleSelector } from '../../components/UserRoleSelector';
import { UserCard } from '.';

export const UserEditRoleDialog = ({
  user,
  isOpen,
  onConfirm,
  onClose,
}: {
  user: User;
  isOpen: boolean;
  onConfirm: () => void;
  onClose: () => void;
}) => {
  const [selectedRole, selectRole] = useState<User['role']>(user?.role);

  const {
    mutateAsync: updateRole,
    isLoading: isUpdating,
    error,
  } = useMutation({
    mutationFn: async () =>
      API.post(`users/change-role`, { role: selectedRole, email: user?.email }),
    onSuccess: () => {
      toast.success('User Role Updated.');
      onConfirm();
      onClose();
    },
  });

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={FaUserEdit}
      title="Change Role"
      description="Select a role from among the roles below to update the role of this user."
      actions={
        <Button className="w-full" loading={isUpdating} onClick={() => updateRole()}>
          Update
        </Button>
      }
      error={error as Error}
    >
      <UserCard user={user} />
      <div className="h-6" />
      <UserRoleSelector
        defaultValue={selectedRole}
        onChange={(v) => selectRole(v as User['role'])}
      />
    </Dialog>
  );
};
