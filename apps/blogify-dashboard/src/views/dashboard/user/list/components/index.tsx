import type { User } from '@ps/types';

import { Avatar } from '@ps/ui/components/avatar';
import { Badge } from '@ps/ui/components/badge';
import { cn } from '@ps/ui/lib/utils';

const colorScheme: Record<User['role'], string> = {
  superadmin: 'text-primary bg-primary/10',
  admin: 'text-[#3956ac] bg-[#3956ac]/10',
  editor: 'text-[#008c99] bg-[#008c99]/10',
  writer: 'text-[#568613] bg-[#568613]/10',
};

const UserRoleBadge = ({ role }: { role: User['role'] }) => (
  <Badge className={cn(colorScheme[role] || colorScheme.superadmin)}>
    {role === 'writer' ? 'Author' : role}
  </Badge>
);

const UserCard = ({ user }: { user: User }) => (
  <section className="flex items-center justify-between border-y border-gray10 py-3">
    <div className="flex items-center gap-3">
      <Avatar className="size-10" src={user.profilePicture} name={user.name} />

      <div>
        <div className="text-15 font-medium">{user.name}</div>
        <div className="mt-0.5 text-13 text-gray9">{user.email}</div>
      </div>
    </div>

    <UserRoleBadge role={user.role} />
  </section>
);

export { UserRoleBadge, UserCard };
