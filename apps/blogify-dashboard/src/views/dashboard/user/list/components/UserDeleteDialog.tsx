import type { User } from '@ps/types';

import { useMutation } from 'react-query';
import { ImBin2 } from 'react-icons/im';
import toast from 'react-hot-toast';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Dialog from '@ps/ui/components/dialog';

import { UserCard } from '.';

export const UserDeleteDialog = ({
  user,
  isOpen,
  onConfirm,
  onClose,
}: {
  user: User;
  isOpen: boolean;
  onConfirm: () => void;
  onClose: () => void;
}) => {
  const {
    mutateAsync: deactivate,
    isLoading,
    error,
  } = useMutation({
    mutationFn: async () => API.remove(`users/${user._id}`),
    onSuccess: () => {
      toast.success(`User deleted.`);
      onConfirm();
      onClose();
    },
  });
  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={() => <ImBin2 className="size-14 text-red5" />}
      title="Delete User"
      description="The following user will be deleted from the system. The user would not be able to access to this system but all of their data would be assign to system admin. If invited again the user would get a fresh new account and would not be able to see their previous data."
      actions={
        <>
          <Button
            className="min-w-28 bg-red5 hover:bg-red5/90"
            onClick={() => deactivate()}
            loading={isLoading}
          >
            Delete User
          </Button>
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
        </>
      }
      error={error as Error}
    >
      <UserCard user={user} />
    </Dialog>
  );
};
