// بسم الله الرحمن الرحيم
import type { User } from '@ps/types';

import { FaPaperPlane } from 'react-icons/fa';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';

import { UserStatus } from '@ps/types';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Dialog from '@ps/ui/components/dialog';

import { UserCard } from '.';

export const UserReinviteDialog = ({
  user,
  isOpen,
  onClose,
}: {
  user: User;
  isOpen: boolean;
  onClose: () => void;
}) => {
  const {
    mutateAsync: resendInvite,
    isLoading: isResending,
    error,
  } = useMutation({
    mutationFn: async () =>
      API.post('users/invite/resend', { email: user.email }).then(() => {
        toast.success('Resent invitation.');
        onClose();
      }),
  });

  const isInvite = user.status === UserStatus.inactive;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={FaPaperPlane}
      title={isInvite ? 'Invite User' : 'Resend Invite'}
      description={`
        We will send ${isInvite ? '' : 'another'} an invitation email to the following address.
        If you do not receive it, please allow a few minutes and check your spam or junk folder.
      `}
      actions={
        <Button className="w-full" loading={isResending} onClick={() => resendInvite()}>
          {isInvite ? 'Send' : 'Resend'} Invitation
        </Button>
      }
      error={error as Error}
    >
      <UserCard user={user} />
    </Dialog>
  );
};
