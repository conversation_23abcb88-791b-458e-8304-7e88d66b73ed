import { FaPaperPlane } from 'react-icons/fa';
import { useMutation } from 'react-query';

import { zodResolver, z } from '@ps/ui';
import { emailSchema } from '@ps/ui/utils/validations';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import Dialog from '@ps/ui/components/dialog';

import { UserRoleSelector } from '../../components/UserRoleSelector';

const userInviteSchema = z.object({
  name: z.string({ required_error: 'Name is required' }),
  email: emailSchema,
  role: z.enum(['admin', 'editor', 'writer']),
});
type UserInviteSchema = z.infer<typeof userInviteSchema>;

const UserInviteDialog = ({
  isOpen,
  onConfirm,
  onClose,
}: {
  isOpen: boolean;
  onConfirm: () => void;
  onClose: () => void;
}) => {
  const { getInputFields, handleSubmit, setValue } = useForm<UserInviteSchema>({
    resolver: zodResolver(userInviteSchema),
    defaultValues: { name: '', email: '', role: 'admin' },
  });

  const {
    mutateAsync: inviteUser,
    isLoading,
    error,
  } = useMutation({
    mutationFn: (body: UserInviteSchema) =>
      API.post('users/invite', body).catch((e: string) => {
        if (e.includes('duplicate key error')) {
          throw new Error('An user with this email already exist.');
        }
        throw new Error(e);
      }),
    onSuccess: () => {
      onConfirm();
      onClose();
    },
  });

  const submit = (values: UserInviteSchema) => inviteUser(values);

  return (
    <Dialog
      as="form"
      open={isOpen}
      onOpenChange={onClose}
      Icon={FaPaperPlane}
      title="Send Invite"
      description="User will receive an email invitation to join this Blogify account. Please provide user name, email address & select a role."
      onSubmit={handleSubmit(submit)}
      error={error as Error}
      actions={
        <Button type="submit" className="w-full" loading={isLoading}>
          Send Invitation
        </Button>
      }
    >
      <FormField label="Full Name" {...getInputFields('name')} />
      <FormField label="Email Address" {...getInputFields('email')} type="email" />

      <FormField label="User Role" type="custom" noError>
        <UserRoleSelector onChange={(v) => setValue('role', v)} />
      </FormField>
    </Dialog>
  );
};

export default UserInviteDialog;
