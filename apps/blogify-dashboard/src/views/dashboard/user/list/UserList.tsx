import type { APIResponse, User } from '@ps/types';
import type { Stats } from '@/components/misc/StatsBox';

import { useContext, useReducer, useMemo } from 'react';
import { FaPaperPlane } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { HiUserAdd } from 'react-icons/hi';
import { useQuery } from 'react-query';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { useStoreState } from '@/store';
import { AccessDenied } from '@/components/misc/AccessDenied';
import { parseQuery } from '@/utils';
import { UserRole } from '@ps/types';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import emptyAPIResponse from '@/types/resources';
import BlogifyLoader from '@/components/common/BlogifyLoader';
import Pagination from '@ps/ui/components/pagination';
import StatsBox from '@/components/misc/StatsBox';

import { UserActiveStatusToggleDialog } from './components/UserActiveStatusToggleDialog';
import { UserReinviteDialog } from './components/UserReinviteDialog';
import { UserEditRoleDialog } from './components/UserEditRoleDialog';
import { UserDeleteDialog } from './components/UserDeleteDialog';
import UserInviteDialog from './components/UserInviteDialog';
import UserEditDialog from '../components/UserEditDialog';
import UserAddDialog from '../components/UserAddDialog';
import UserTable from './components/UserTable';

export type OpenedDialog =
  | Record<string, never>
  | { target: 'invite' }
  | {
      target: 'enable' | 'disable' | 'delete' | 'reinvite' | 'update' | 'change-role';
      payload: User;
    };
export type DialogAction =
  | { type: 'close' }
  | { type: 'open'; target: 'invite' }
  | {
      type: 'open';
      target: 'enable' | 'disable' | 'delete' | 'reinvite' | 'update' | 'change-role';
      payload: User;
    };

function reducer(_: OpenedDialog, action: DialogAction): OpenedDialog {
  const { type, ...rest } = action;
  switch (type) {
    case 'close':
      return {};
    case 'open':
      return rest;
  }
}

export default function UserList() {
  const { limit = '10', page = '1' } = parseQuery();

  const [openedDialog, dispatch] = useReducer(reducer, {});
  const currentUser = useStoreState((s) => s.user.current);
  const abilities = useContext(UserAbilitiesContext);
  const navigate = useNavigate();

  const {
    data: { data: users, total } = emptyAPIResponse,
    isLoading,
    refetch,
  } = useQuery<APIResponse<User>>([`users?page=${page}&limit=${limit}`], {
    enabled: abilities.user.view,
  });

  const isEditor = currentUser.role === UserRole.editor;
  const userStats: Stats[] = useMemo(
    () => [
      { name: 'Total User', amount: users.length },
      { name: 'Active User', amount: users.filter((user) => user.status === 'active').length },
      {
        name: 'Inactive User',
        amount: users.filter((user) => ['inactive', 'invited'].includes(user.status)).length,
      },
      {
        name: 'Disabled User',
        amount: users.filter((user) => user.status === 'deactivated').length,
      },
    ],
    [users]
  );

  if (!abilities.user.view) return <AccessDenied />;
  if (isLoading) return <BlogifyLoader />;

  return (
    <DashboardContainer
      title="User List"
      actions={
        <>
          <UserAddDialog variant={isEditor ? 'author' : 'default'} refetchUsers={refetch}>
            <Button variant="secondary">
              <HiUserAdd /> Add {isEditor ? 'Author' : 'User'}
            </Button>
          </UserAddDialog>

          {abilities.user.invite && (
            <Button onClick={() => dispatch({ type: 'open', target: 'invite' })}>
              <FaPaperPlane /> Invite User
            </Button>
          )}
        </>
      }
    >
      <p className="mt-5 text-15">Here you can manage all the members of your team.</p>

      <StatsBox stats={userStats} />
      <UserTable users={users} dispatch={dispatch} />
      <Pagination
        className="mt-4"
        onPaging={(url) => navigate(url)}
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
      />

      <UserInviteDialog
        isOpen={'target' in openedDialog && openedDialog.target === 'invite'}
        onClose={() => dispatch({ type: 'close' })}
        onConfirm={refetch}
      />

      {'payload' in openedDialog && openedDialog.payload && (
        <>
          <UserEditDialog
            isOpen={openedDialog.target === 'update'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload as User}
            refetchUsers={refetch}
          />
          <UserReinviteDialog
            isOpen={openedDialog.target === 'reinvite'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload as User}
          />
          <UserEditRoleDialog
            isOpen={openedDialog.target === 'change-role'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload as User}
            onConfirm={refetch}
          />
          <UserActiveStatusToggleDialog
            isOpen={openedDialog.target === 'enable'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload || undefined}
            onConfirm={refetch}
            variant="enable"
          />
          <UserActiveStatusToggleDialog
            isOpen={openedDialog.target === 'disable'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload || undefined}
            onConfirm={refetch}
            variant="disable"
          />
          <UserDeleteDialog
            isOpen={openedDialog.target === 'delete'}
            onClose={() => dispatch({ type: 'close' })}
            user={openedDialog.payload || undefined}
            onConfirm={refetch}
          />
        </>
      )}
    </DashboardContainer>
  );
}
