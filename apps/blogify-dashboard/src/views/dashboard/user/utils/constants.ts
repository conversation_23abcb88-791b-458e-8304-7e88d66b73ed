import type { UserRole } from '@ps/types';

import { emailSchema } from '@ps/ui/utils/validations';
import { z } from '@ps/ui';

export const INTERESTS = [
  'Animals & Pet',
  'Architecture',
  'Arts & Craft',
  'Automotive',
  'Baking',
  'Beauty',
  'Books',
  'Business & Finance',
  'Food & Drink',
  'Fashion',
  'Family',
  'Environment',
  'Organic Food',
  'Music',
  'Home Interior',
  'Health & Fitness',
  'Wedding',
  'Travel',
  'Technology',
  'Sustainability',
  'Sport',
  'Skincare',
  'Photography',
];

export const UserRoles: Record<Exclude<UserRole, 'superadmin'>, any> = {
  admin: {
    name: 'Admin',
    access: 'Full',
    description: '<PERSON><PERSON> have full permission and they can manage users',
  },
  editor: {
    name: 'Editor',
    access: 'Moderate',
    description:
      'Editors will be able to create, update, publish & share all blogs, manage team, see analytics, connect platform etc.',
  },
  writer: {
    name: 'Author',
    access: 'Limited',
    description:
      'Authors will only be able to create & update their blogs, see analytics, update profile.',
  },
};

export const userFormSchema = z.object({
  profilePicture: z.string().optional(),
  name: z.string({ required_error: 'Name is required' }),
  occupation: z.string().optional(),
  bio: z.string().optional(),
  email: emailSchema,
  socials: z
    .object({
      facebook: z.string().optional(),
      x: z.string().optional(),
      youtube: z.string().optional(),
      instagram: z.string().optional(),
      linkedin: z.string().optional(),
    })
    .default({}),
  role: z.string().default('writer').optional(),
  sendInvite: z.boolean().default(false).optional(),
});
export type UserSchema = z.infer<typeof userFormSchema>;
