import { useToggle } from 'react-use';
import { useState } from 'react';

import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import ErrorMessage from '@/components/form/ErrorMessage';
import TagInput from '@/components/form/TagInput';

import { INTERESTS } from '../../utils/constants';
import UserInterestsDialog from './UserInterestsDialog';

const UserInterests = () => {
  const user = useStoreState((s) => s.user.current);

  const [interests, setInterests] = useState<string[]>(user?.interests || []);
  const [submitting, toggleSubmitting] = useToggle(false);
  const [isDialogOpen, toggleDialog] = useToggle(false);
  const [error, setError] = useState<string>('');

  const submit = () => {
    toggleSubmitting();
    API.patch(`/business/${user.business}/interests`, { interests })
      .then(toggleSubmitting)
      .catch((e) => {
        toggleSubmitting();
        setError(e);
      });
  };

  return (
    <Card className="my-4 py-5">
      <h3 className="mb-5 font-semibold">Interests</h3>

      <TagInput
        suggestions={INTERESTS}
        setTags={setInterests}
        tags={interests}
        placeholder="ie: Animals & Pet, Music, Sport"
        name="interests"
        suggestionsOnly
      />

      <Button className="mt-6" variant="secondary" onClick={toggleDialog}>
        Select
      </Button>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <Button className="mt-5" loading={submitting} onClick={submit}>
        Update
      </Button>

      <UserInterestsDialog
        interests={interests}
        isOpen={isDialogOpen}
        submit={submit}
        onClose={toggleDialog}
        selectInterest={setInterests}
      />
    </Card>
  );
};

export default UserInterests;
