import { Link, useSearchParams } from 'react-router-dom';

import { useContext, useEffect } from 'react';
import toast from 'react-hot-toast';

import { IntegrationDetailsContext } from '@/context/IntegrationDetailsContext';
import { getConnectUrl } from '@/utils/integration';
import { isMobile } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import { useQuery } from 'react-query';
import { FaLinkedinIn, FaSquareXTwitter } from 'react-icons/fa6';
import { capitalize } from '@/utils/string';
import { useStoreState } from '@/store';
import { cacheGet } from '@/utils/localStorageCache';

const disconnect = (id: string) =>
  API.remove('integrations/' + id)
    .then(() => toast.success('Disconnected successfully!'))
    .catch((error) => {
      toast.error('Failed to disconnect');
      console.log(error);
    });

// Base Platform compound component
const Platform = {
  Root: ({ children, ...props }: React.HTMLProps<HTMLDivElement>) => (
    <div
      className="mt-0.5 flex flex-row flex-wrap justify-between gap-3 py-3 sm:flex-nowrap"
      {...props}
    >
      {children}
    </div>
  ),

  Content: ({ children }: { children: React.ReactNode }) => (
    <div className="flex items-center gap-4">{children}</div>
  ),

  Logo: ({ theme, children }: { theme?: string; children: React.ReactNode }) => (
    <div className="shrink-0 rounded-lg p-2" style={{ backgroundColor: theme }}>
      {children}
    </div>
  ),

  Info: ({ title, titleColor }: { title: string; titleColor?: string }) => (
    <div>
      <h2 className="text-15 font-semibold" style={{ color: titleColor }}>
        {title}
      </h2>
      <p className="mt-1 text-11 text-gray9 sm:text-13">Publish blogs via your {title} account</p>
    </div>
  ),

  Actions: ({ children }: { children: React.ReactNode }) => (
    <div className="flex items-center gap-3">{children}</div>
  ),
};

// Integration component using Platform
const Integration = ({
  id,
  name,
  theme,
  logoURL,
  connected,
  tutorial,
}: {
  id: string;
  name: string;
  theme: string;
  logoURL: string;
  connected: boolean;
  tutorial?: string;
}) => (
  <Platform.Root>
    <Platform.Content>
      <Platform.Logo theme={theme}>
        <img
          className="size-12 object-contain"
          alt={`${name} Logo`}
          src={logoURL}
          height={48}
          width={48}
        />
      </Platform.Logo>
      <Platform.Info title={name} titleColor={theme} />
    </Platform.Content>
    <Platform.Actions>
      <Link
        to={tutorial || getConnectUrl(id)}
        target={(tutorial || getConnectUrl(id)).startsWith('https') ? '_blank' : '_self'}
      >
        <Button size={isMobile ? 'sm' : 'default'} variant={connected ? 'secondary' : 'default'}>
          {connected ? 'Connected' : 'Connect'}
        </Button>
      </Link>
      <Button
        size={isMobile ? 'sm' : 'default'}
        onClick={() => disconnect(id)}
        disabled={!connected}
      >
        Disconnect
      </Button>
    </Platform.Actions>
  </Platform.Root>
);

// Social component using Platform
const Social = ({
  name,
  Icon,
  className,
  isActive,
}: {
  name: string;
  Icon: React.ComponentType<{ size: number; className: string }>;
  className: string;
  isActive: boolean | null;
}) => (
  <Platform.Root>
    <Platform.Content>
      <Platform.Logo>
        <Icon size={48} className={className} />
      </Platform.Logo>
      <Platform.Info title={capitalize(name)} />
    </Platform.Content>
    <Platform.Actions>
      <Link to={getConnectUrl(name)} target={getConnectUrl(name)}>
        <Button size={isMobile ? 'sm' : 'default'} variant={isActive ? 'secondary' : 'default'}>
          {isActive ? 'Connected' : 'Connect'}
        </Button>
      </Link>
      <Button
        size={isMobile ? 'sm' : 'default'}
        onClick={() => disconnect(name)}
        disabled={!isActive}
      >
        Disconnect
      </Button>
    </Platform.Actions>
  </Platform.Root>
);

const socials = {
  x: { Icon: FaSquareXTwitter, className: '' },
  linkedin: { Icon: FaLinkedinIn, className: 'text-linkedin' },
} as const;

function Socials() {
  const { data } =
    useQuery<Record<'linkedin' | 'x', { isActive: null | boolean }>>('/integrations/socials');
  return (
    data &&
    Object.entries(data).map(([name, { isActive }]) => {
      const { Icon, className } = socials[name as keyof typeof socials];
      return (
        <Social key={name} name={name} Icon={Icon} className={className} isActive={isActive} />
      );
    })
  );
}

function Integrations() {
  const user = useStoreState((s) => s.user.current);
  const integrations = useContext(IntegrationDetailsContext);
  return Object.entries(integrations)
    .filter(([, { display }]) => user.role === 'superadmin' || display)
    .map(([id, details]) => <Integration key={id} id={id} {...details} />);
}

const UserIntegration = (props: React.HTMLProps<HTMLDivElement>) => {
  const [queryParams] = useSearchParams();

  useEffect(() => {
    if (queryParams.has('shop')) {
      const redirect = new URL('/shopify/auth', import.meta.env.VITE_API_URL);
      redirect.searchParams.append('shop', queryParams.get('shop')!);
      redirect.searchParams.append('token', cacheGet('token'));
      window.open(redirect, '_self');
    }
  }, [queryParams]);

  return (
    <div {...props}>
      <Card className="mt-4 py-6">
        <div className="font-semibold">Connect Platforms</div>
      </Card>
      <Integrations />
      <Socials />
    </div>
  );
};

export default UserIntegration;
