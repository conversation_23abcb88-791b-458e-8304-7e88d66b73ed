import type { Dispatch } from 'react';

import { MdCheckCircleOutline, MdInterests } from 'react-icons/md';

import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import Dialog from '@ps/ui/components/dialog';

import { INTERESTS } from '../../utils/constants';

const UserInterestsDialog = ({
  isOpen,
  interests,
  submit,
  onClose,
  selectInterest,
}: {
  isOpen: boolean;
  submit: () => void;
  onClose: () => void;
  interests: string[];
  selectInterest: Dispatch<string[]>;
}) => {
  const isSelected = (interest: string) => interests.some((int) => int === interest);

  const toggleInterest = (interest: string) => {
    if (isSelected(interest)) {
      selectInterest(interests.filter((int) => int !== interest));
    } else {
      selectInterest([...interests, interest]);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      Icon={MdInterests}
      title="Select Your Interest"
      description="Select at least three. You can change the your interests anytime."
      actions={
        <Button
          className="w-full"
          onClick={() => {
            onClose();
            submit();
          }}
        >
          Update Interests
        </Button>
      }
    >
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
        {INTERESTS.map((interest, i) => (
          <div
            className={`group relative h-24 cursor-pointer md:h-36`}
            onClick={() => toggleInterest(interest)}
            key={i}
          >
            <img
              className="block h-24 w-full rounded-md object-cover md:h-36"
              src={`/images/interests/${interest}.jpg`}
            />

            <div
              className={cn(
                'relative -top-24 left-0 z-10 flex h-24 w-full rounded-md bg-[#3c40577f] transition-colors flex-center group-hover:bg-primary/50 md:-top-36 md:h-36',
                { 'bg-primary/50': isSelected(interest) }
              )}
            >
              <div className="font-bold text-white">{interest}</div>

              <MdCheckCircleOutline
                className={cn('absolute right-2 top-2 hidden rounded-full bg-green text-white', {
                  block: isSelected(interest),
                })}
                size={20}
              />
            </div>
          </div>
        ))}
      </div>
    </Dialog>
  );
};

export default UserInterestsDialog;
