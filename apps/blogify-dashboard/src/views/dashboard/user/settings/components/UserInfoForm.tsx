import type { User } from '@/types/resources/user.type';

import { useState } from 'react';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { useStoreActions, useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';

import BlogPreferencesForm from './BlogPreferencesForm';

const UserInfoForm = ({ className, ...props }: React.HTMLProps<HTMLDivElement>) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState('');
  const user = useStoreState((s) => s.user.current);

  const update = useStoreActions((a) => a.user.update);

  const initialValues: Partial<User> = {
    name: user.name,
    email: user.email,
    currentPassword: '',
    password: '',
    preferredInputLanguage: user.preferredInputLanguage || 'Global English',
    preferredOutputLanguage: user.preferredOutputLanguage || 'english',
  };

  const submit = (values: Partial<User>) => {
    setFormError('');
    setIsSubmitting(true);
    update(values as User)
      .then(() => toast.success('Updated successfully.'))
      .catch((e: unknown) => {
        if (typeof e === 'string') {
          setFormError(e);
        } else if (e instanceof Error) {
          setFormError(e.message);
        }
      })
      .finally(() => setIsSubmitting(false));
  };

  const UserSchema = Yup.object().shape({
    name: Yup.string().required(`Name is required`),
    email: Yup.string().required(`Email is required`).email(`Invalid email`),
    currentPassword: Yup.lazy(() => {
      if (form.getInputFields('password').value) {
        return Yup.string().required(`Current Password is required`);
      }
      return Yup.string();
    }),
    password: Yup.string(),
    preferredInputLanguage: Yup.string(),
    preferredOutputLanguage: Yup.string(),
  });

  const form = useForm({ initialValues, submit, validationSchema: UserSchema });

  return (
    <form onSubmit={form.submitForm}>
      <Card className={`py-6 ${className}`} {...props}>
        <div className="font-semibold">Basic Information</div>

        <ErrorMessage className="pb-2">{formError || form.formValidationError}</ErrorMessage>

        {/* @ts-ignore For */}
        <FormField {...form.getInputFields('name')} />
        {/* @ts-ignore For Now */}
        <FormField type="email" {...form.getInputFields('email')} />
        {/* @ts-ignore For Now */}
        <FormField
          type="password"
          label="Current Password"
          {...form.getInputFields('currentPassword')}
        />
        {/* @ts-ignore For Now */}
        <FormField type="password" label="New Password" {...form.getInputFields('password')} />
      </Card>

      <BlogPreferencesForm form={form as any} />

      <Card className="mt-1 py-6">
        <Button className="w-full" type="submit" loading={isSubmitting}>
          Update
        </Button>
      </Card>
    </form>
  );
};

export default UserInfoForm;
