import { Card } from '@/components/layout';
import Select from '@ps/ui/form/Select';

import { BLOG_LANGUAGES, LANGUAGE_CODES } from '@/views/dashboard/blog/create/utils/languages';

const inputLanguageOptions = LANGUAGE_CODES.map((lang, i) => (
  <option key={lang + i} value={lang}>
    {lang}
  </option>
));

const blogLanguageOptions = BLOG_LANGUAGES.map((language, i) => (
  <option key={language.value + i} value={language.value}>
    {language.name} - {language.translation}
  </option>
));

const BlogPreferencesForm = ({
  form,
  className,
  ...props
}: { form: any } & React.HTMLProps<HTMLDivElement>) => (
  <Card className={`mt-1 py-6 ${className}`} {...props}>
    <div className="font-semibold">Blog Preferences</div>

    <div className="mt-5 flex flex-wrap sm:flex-nowrap">
      <div className="w-full sm:w-1/2">
        <Select
          placeholder="Preferred Input Language"
          {...form.getInputFields('preferredInputLanguage')}
        >
          {inputLanguageOptions}
        </Select>
      </div>
      <div className="ml-0 mt-5 w-full sm:ml-5 sm:mt-0 sm:w-1/2">
        <Select
          placeholder="Preferred Blog Language"
          {...form.getInputFields('preferredOutputLanguage')}
        >
          {blogLanguageOptions}
        </Select>
      </div>
    </div>
  </Card>
);

export default BlogPreferencesForm;
