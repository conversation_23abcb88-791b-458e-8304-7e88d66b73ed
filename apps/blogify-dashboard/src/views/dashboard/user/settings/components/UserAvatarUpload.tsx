import type { ChangeEvent } from 'react';

import { useState } from 'react';
import { useRef } from 'react';

import { useStoreActions, useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';
import ErrorMessage from '@/components/form/ErrorMessage';

const UserAvatarUpload = () => {
  const update = useStoreActions((a) => a.user.update);
  const user = useStoreState((s) => s.user.current);
  const [src, setSrc] = useState(user.profilePicture || '');

  return (
    <FileUploadProvider
      onUploadComplete={(files) => {
        setTimeout(() => {
          const profilePicture = files[0].url;
          setSrc(profilePicture);
          update({ profilePicture });
        }, 1000);
      }}
      folder="user/avatars"
      type="image"
    >
      {({ onChange, progress, error, files }) => (
        <div className="py-6">
          <h2 className="font-semibold">Profile Picture</h2>

          <div className="mt-6 flex flex-wrap gap-6 flex-center">
            {files[0]?.url || src ? (
              <img
                className="size-52 shrink-0 rounded-full object-cover"
                src={files[0]?.url || src}
              />
            ) : (
              <div className="flex size-52 rounded-full bg-gray7 flex-center">
                <p className="text-center text-md text-gray2">
                  <span className="mb-1 text-lg font-semibold">Avatar</span>
                  <br />
                  200 x 200
                </p>
              </div>
            )}

            <div className="flex flex-col flex-center">
              <FileInput loading={progress > 0 && progress < 100} uploadFile={onChange} />
              <p className="mt-6 text-center text-sm leading-6 text-gray2">
                Choose a picture from you local drive
                <br />
                Min 200 x 200px, Max 800 x 800px, Supported format PNG, JPG, GIF
              </p>
            </div>
          </div>
          <ErrorMessage className="!pt-3">{error}</ErrorMessage>
        </div>
      )}
    </FileUploadProvider>
  );
};

const FileInput = ({
  loading,
  uploadFile,
  multiple = false,
}: {
  loading?: boolean;
  uploadFile?: (ev: ChangeEvent<HTMLInputElement>) => void;
  multiple?: boolean;
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <>
      <input
        className="hidden"
        type="file"
        multiple={multiple}
        ref={inputRef}
        accept="image/*"
        onChange={(e) => uploadFile && uploadFile(e)}
      />

      <Button
        className="min-w-36"
        onClick={() => inputRef.current?.click()}
        loading={loading}
        type="button"
      >
        Choose a File
      </Button>
    </>
  );
};

export default UserAvatarUpload;
