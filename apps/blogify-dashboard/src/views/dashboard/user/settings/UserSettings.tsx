import { useContext } from 'react';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { PageContainerMini } from '@/components/layout/PageContainer';
import { useStoreState } from '@/store';
import { parseQuery } from '@/utils';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

import UserAvatarUpload from './components/UserAvatarUpload';
import UserIntegration from './components/UserIntegration';
import UserInterests from './components/UserInterests';
import UserInfoForm from './components/UserInfoForm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ps/ui/components/tabs';

const UserSettings = () => {
  const user = useStoreState((s) => s.user.current);
  const { message } = parseQuery();
  const abilities = useContext(UserAbilitiesContext);

  if (!user._id) return null;

  return (
    <DashboardContainer title="Settings">
      {message && (
        <p className="mb-0.5 p-5 text-md">
          <span className="font-bold text-green">{message}</span>
        </p>
      )}

      <Tabs defaultValue="integrations">
        <TabsList variant="nav">
          <TabsTrigger variant="nav" value="profile">
            Profile
          </TabsTrigger>
          <TabsTrigger variant="nav" value="company">
            Company
          </TabsTrigger>
          <TabsTrigger variant="nav" value="integrations">
            Integrations
          </TabsTrigger>
        </TabsList>

        <PageContainerMini>
          <TabsContent value="profile">
            {abilities.profile.edit && <UserAvatarUpload />}
            {abilities.profile.edit && <UserInfoForm className="mt-0" />}
          </TabsContent>
          <TabsContent value="company">{abilities.interests.edit && <UserInterests />}</TabsContent>

          <TabsContent value="integrations">
            {abilities.integrations.connect && <UserIntegration className="mt-4" />}
          </TabsContent>
        </PageContainerMini>
      </Tabs>
    </DashboardContainer>
  );
};

export default UserSettings;
