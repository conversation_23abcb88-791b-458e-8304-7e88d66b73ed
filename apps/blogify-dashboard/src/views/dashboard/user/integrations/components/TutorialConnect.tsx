// بسم الله الرحمن الرحيم
import { useEffect } from 'react';

import { PageContainerMini } from '@/components/layout/PageContainer';
import { Card } from '@/components/layout';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

export const TutorialConnect = ({
  title,
  Header = (
    <div className="font-semibold text-black1">
      Follow the instructions listed below 👇 carefully:
    </div>
  ),
  tutorialSteps,
}: {
  title: string;
  Header?: React.ReactElement;
  tutorialSteps: Array<{
    summary: string;
    instruction: React.ReactElement;
    image?: { src: string; alt: string };
  }>;
}) => {
  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  return (
    <DashboardContainer title={title}>
      <PageContainerMini>
        <Card className="p-6">
          <div className="flex flex-wrap items-center justify-between gap-2">{Header}</div>
        </Card>
        {tutorialSteps.map((step, i) => (
          <Card key={i} className="mt-0.5 p-6">
            <div>
              <b>
                {i + 1}. {step.summary}:
              </b>
            </div>
            <div className="m-5 flex flex-wrap items-center gap-2">{step.instruction}</div>
            {step.image ? (
              <img className="mt-5 w-full rounded" src={step.image.src} alt={step.image.alt} />
            ) : (
              <></>
            )}
          </Card>
        ))}
      </PageContainerMini>
    </DashboardContainer>
  );
};
