// بسم الله الرحمن الرحيم
import type { Integration } from '@/types/misc/integration.type';

import { useEffect } from 'react';
import { useQuery } from 'react-query';
import Markdown from 'react-markdown';

import { getConnectUrl } from '@/utils/integration';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import Link from '@/components/common/Link';

const GetAccessToken = () => {
  const { data } = useQuery<{ token: string }>('/wordpressorg/token/generate');
  return (
    <>
      <FormField name="WordPress Access Token" readOnly value={data?.token} />
      <Button
        className="w-full"
        onClick={async () => {
          await copy(data?.token ?? '');
        }}
      >
        Copy
      </Button>
    </>
  );
};

const markdown = `
1. Download our plugin by clicking on the **Download Plugin** button above.

2. After successful installation and activation a new item will appear in your sites **Settings Menu called Blogify-AI 📝**

3. **Copy the access token from below** and paste it in that settings page and hit save.

4. If you own **multiple Wordpress sites**, repeat the steps above for each of them.
`;

const WordpressOrgConnect = () => {
  const urlWithSecret = new URLSearchParams(window.location.search).get('wordpressorg');

  useEffect(() => {
    if (urlWithSecret) {
      const url = getConnectUrl('wordpressorg' as Integration) + `&url=${urlWithSecret}`;
      window.location.replace(url);
    }
  }, [urlWithSecret]);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  if (urlWithSecret) {
    return null;
  }
  return (
    <DashboardContainer
      title="Connect one or more of your WordPress sites to Blogify.ai"
      actions={
        <Link
          to={import.meta.env.VITE_WORDPRESS_PLUGIN_DOWNLOAD_LINK}
          download="blogify-plugin.zip"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Button>Download Plugin</Button>
        </Link>
      }
    >
      <Markdown className="prose max-w-none">{markdown}</Markdown>
      <GetAccessToken />
    </DashboardContainer>
  );
};

export default WordpressOrgConnect;
