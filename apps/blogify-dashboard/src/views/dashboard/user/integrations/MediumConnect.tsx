// بسم الله الرحمن الرحيم
import { useEffect, useState } from 'react';

import { PageContainerMini } from '@/components/layout/PageContainer';
import { getConnectUrl } from '@/utils/integration';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import SocialIcon from '@/components/misc/SocialIcon';
import Form<PERSON>ield from '@ps/ui/form/FormField';
import Link from '@/components/common/Link';

import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

async function validateToken(
  token: string
): Promise<string | { username: string; name: string; link: string }> {
  try {
    const response = await API.post<{
      error?: string;
      username?: string;
      name?: string;
      link?: string;
    }>('medium/validate', { token });
    if (response?.error) {
      return response.error;
    } else if (response?.username && response?.name && response?.link) {
      return {
        username: response.username,
        name: response.name,
        link: response.link,
      };
    } else {
      throw new Error('Invalid response from server');
    }
  } catch (error: any | Error) {
    const error_message = 'Error when validating medium token: ' + error?.message;

    throw new Error(error_message);
  }
}

const MediumAccountConfirmation = ({
  username,
  name,
  link,
  token,
}: {
  username: string;
  name: string;
  link: string;
  token: string;
}) => (
  <div className="visible m-6 flex-wrap items-center gap-2">
    <div className="mt-4 w-full text-center font-semibold text-black1">
      Connecting Blogify.ai to your Medium account
    </div>
    <SocialIcon integration="medium" to="https://medium.com/me/settings" />
    <Link to={link}>{link}</Link>
    <FormField name="username" readOnly value={username} />
    <FormField name="name" readOnly value={name} />
    <Button
      className="w-full !rounded-[15px] bg-[#34aa47] !p-[10px]"
      onClick={() => {
        const connectUrl = new URL(getConnectUrl('medium'));
        connectUrl.searchParams.append('medium-integration-token', token);
        window.open(connectUrl.toString(), '_self');
      }}
    >
      Approve
    </Button>
  </div>
);

const IntegrationTokenInput = () => {
  const [token, setToken] = useState('');
  const [error, setError] = useState('');
  const [Extra, setExtra] = useState(<></>);

  const decide = (result: string | { username: string; name: string; link: string }) => {
    if (typeof result === 'string') {
      setError(result);
      setExtra(<></>);
    } else {
      const { username, name, link } = result;
      setError('');
      setExtra(
        <MediumAccountConfirmation token={token} username={username} name={name} link={link} />
      );
    }
  };

  return (
    <>
      <FormField
        name="mediumIntegrationToken"
        label="Medium Integration Token"
        type="password"
        placeholder="Paste your token here"
        value={token}
        onChange={(e) => setToken(e.target.value)}
        error={error}
      />
      <Button
        className="w-full !rounded-[15px] bg-[#34aa47] !p-[10px]"
        disabled={token.length !== 65}
        onClick={async () => {
          try {
            const result = await validateToken(token);
            decide(result);
          } catch (err: any | Error) {
            setError('Failure: ' + err?.message);
          }
        }}
      >
        Save
      </Button>
      {Extra}
    </>
  );
};

const tutorialSteps: {
  instruction: React.ReactElement;
  imageSource?: string;
  imageCaption?: string;
}[] = [
  {
    instruction: (
      <div className="flex flex-wrap items-center gap-2">
        <div className="mb-4 w-full text-center font-semibold">⚠️ Important Notice</div>
        <div className="w-full">
          Unfortunately, Medium has{' '}
          <Link
            to="https://help.medium.com/hc/en-us/articles/213480228-API-Importing"
            className="underline"
          >
            discontinued their public API service
          </Link>
          . This means that automatic publishing to Medium is no longer available - this is not an
          issue with Blogify.ai, but rather a decision made by Medium.
        </div>
        <div className="mt-2 w-full">
          We recommend manually copying and pasting your content to Medium, or exploring our other
          available integration options for online publishing. We apologize for any inconvenience
          this may cause.
        </div>
      </div>
    ),
  },
  {
    instruction: (
      <div className="flex flex-wrap items-center gap-2">
        <div>
          <b>1.</b> Open your Medium Account's
        </div>
        <Link className="underline" to="https://medium.com/me/settings/security" target="_blank">
          Settings
        </Link>
        <div>
          {' '}
          page and click on <b>Security and apps</b>
        </div>
      </div>
    ),
    imageCaption:
      'Head over to the "Security and Apps" tab of your Medium account\'s Settings page',
    imageSource: '/images/guides/medium/1-medium-settings-page.png',
  },
  {
    instruction: (
      <div className="flex flex-wrap items-center gap-2">
        <div>
          <b>2.</b> Scroll down to <em>Integration tokens</em> and click on it.
        </div>
      </div>
    ),
    imageCaption: 'Scroll down to the "Integration Token" section and click on it',
    imageSource: '/images/guides/medium/2-scroll-to-integration-token.png',
  },
  {
    instruction: (
      <div className="flex flex-wrap items-center gap-2">
        <div>
          <b>3.</b> A popup will appear, and in it provide a description of your token so that you
          can easily identify it later on.
        </div>
        <div> Now, click on </div>
        <Button className="!rounded-[15px] !p-[10px]" variant="secondary" color="#34aa47">
          Get token
        </Button>
        <div>and wait for a few moments.</div>
      </div>
    ),
    imageCaption: 'Click on "Get Token" and wait for a few moments',
    imageSource: '/images/guides/medium/3-get-integration-token.png',
  },
  {
    instruction: (
      <div className="flex flex-wrap items-center gap-2">
        <div>
          <b>4.</b> A new integration token will be generated for you. Copy it and paste it here
          below.👇 then click <b>Save</b>
        </div>
        <IntegrationTokenInput />
      </div>
    ),
  },
];

const MediumConnect = () => {
  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, []);

  return (
    <DashboardContainer title="Connect Medium account">
      <PageContainerMini>
        <Card className="p-6">
          <div className="flex flex-wrap items-center gap-2">
            <div className="hidden font-semibold text-black1">
              Follow the instructions listed below 👇 carefully:
            </div>
          </div>
        </Card>

        {tutorialSteps.slice(0, 1).map((step, i) => (
          <Card key={i} className="mt-0.5 p-6">
            <div className="text-sm sm:text-base">{step.instruction}</div>
            {step.imageCaption ? (
              <img className="mt-5 w-full rounded" src={step.imageSource} alt={step.imageCaption} />
            ) : (
              <></>
            )}
          </Card>
        ))}
      </PageContainerMini>
    </DashboardContainer>
  );
};

export default MediumConnect;
