// بسم الله الرحمن الرحيم
import { useEffect, useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { API } from '@/services/api';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import Link from '@/components/common/Link';

import { TutorialConnect } from './components/TutorialConnect';

const GetAPIKey = () => {
  const [apiKey, setApiKey] = useState<string>('');
  const [error, setError] = useState<string>('');
  useEffect(() => {
    API.fetch<{ key: string }>(`zapier/key`)
      .then((response) => {
        if (response) {
          setApiKey(response.key);
          setError('');
        } else {
          setError('Something went wrong! ');
        }
      })
      .catch((e) => {
        setError('Something went wrong! ' + e);
      });
  }, []);
  return (
    <div className="w-full">
      <FormField
        name="Zapier API Key"
        label="Blogify API Key"
        placeholder="Your API Key should appear here"
        readOnly
        value={apiKey}
        error={error}
      />
      <Button
        className="w-full"
        onClick={async () => {
          try {
            await copy(apiKey);
          } catch (e) {
            setError('Something went wrong! ' + e);
          }
        }}
      >
        Copy
      </Button>
    </div>
  );
};
const tutorialSteps = [
  {
    summary: 'Install Blogify on Zapier',
    instruction: (
      <>
        <div>Visit</div>
        <Link className="underline" to="https://zapier.com/app/connections" target="_blank">
          My Apps
        </Link>
        <div>on your Zapier Account and check if Blogify is installed or not.</div>
        <div>If it is not installed you should install the</div>
        Blogify App
        <div>on your Zapier Account.</div>
      </>
    ),
    image: {
      src: '/images/guides/zapier/1-install-blogify.png',
      alt: 'Check if Blogify App is installed or not',
    },
  },
  {
    summary: 'Create a Connection',
    instruction: (
      <>
        <div>From</div>
        <Link className="underline" to="https://zapier.com/app/connections" target="_blank">
          My Apps
        </Link>
        <div>
          click on <b>Blogify</b> and then click on
        </div>
        <Button className="bg-[#3d4592]">Add connection</Button>
      </>
    ),
    image: {
      src: '/images/guides/zapier/2-click-on-add-connection.png',
      alt: 'Click on Add Connection',
    },
  },
  {
    summary: 'Provide API Key',
    instruction: (
      <>
        <div>A new window will open asking for your Blogify API Key.</div>
        <div>Paste the API Key provided to you by Blogify at</div>
        <Link
          className="underline"
          onClick={() => window.scrollTo({ top: 0 })}
          to="/dashboard/settings/zapier-connect"
          target="_self"
        >
          the top of this tutorial.
        </Link>
      </>
    ),
    image: {
      src: '/images/guides/zapier/3-provide-api-key.png',
      alt: 'Paste API Key',
    },
  },
  {
    summary: 'Create a Zap using Blogify',
    instruction: (
      <>
        <div>You are all set!</div>
        <div>Now you can publish your posts via Zaps. To begin, visit</div>
        <Link className="underline" to="https://zapier.com/app/zaps" target="_blank">
          My Zaps
        </Link>
        <div>and create a new Zap using Blogify as the Trigger.</div>
        <div className="font-semibold">
          {' '}
          Note Zapier won't be shown as connected in Blogify until you publish a Zap which uses
          Blogify as the trigger
        </div>
      </>
    ),
    image: {
      src: '/images/guides/zapier/4-zap-using-blogify-as-trigger.png',
      alt: 'Create Zaps with Blogify as the Trigger',
    },
  },
  {
    summary: 'Complete',
    instruction: (
      <>
        <div>You are all set! Click on the button below to continue.</div>
        <Button
          className="w-full"
          onClick={() => {
            window.location.href = '/dashboard/settings?message=Successfully Connected to Zapier';
          }}
        >
          Continue
        </Button>
      </>
    ),
  },
];

const ZapierConnect = () => (
  <TutorialConnect
    title="Connect to Zapier"
    tutorialSteps={tutorialSteps}
    Header={
      <>
        <div>
          Zapier requires an API key to connect to your Blogify account. This API Key is
          confidential and should not be shared with anyone.
        </div>
        <GetAPIKey />
      </>
    }
  />
);

export default ZapierConnect;
