/* eslint-disable react-refresh/only-export-components */
import type { RouteObject } from 'react-router-dom';

import { Suspense } from 'react';

import { RouteErrorBoundary } from '@/components/error';
import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';

// Users & Settings
const UserList = safeLazy(() => import('./list/UserList'));
const UserSettings = safeLazy(() => import('./settings/UserSettings'));
// Integrations
const WordpressOrgConnect = safeLazy(() => import('./integrations/WordpressOrgConnect'));
const MediumConnect = safeLazy(() => import('./integrations/MediumConnect'));
const ZapierConnect = safeLazy(() => import('./integrations/ZapierConnect'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);

const getRoutes = (): RouteObject[] => [
  { path: 'users', element: <Lazy as={UserList} />, errorElement: <RouteErrorBoundary /> },

  { path: 'settings', element: <Lazy as={UserSettings} />, errorElement: <RouteErrorBoundary /> },
  {
    path: 'settings/wordpressorg-connect',
    element: <Lazy as={WordpressOrgConnect} />,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: 'settings/medium-connect',
    element: <Lazy as={MediumConnect} />,
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: 'settings/zapier-connect',
    element: <Lazy as={ZapierConnect} />,
    errorElement: <RouteErrorBoundary />,
  },
];

export default getRoutes;
