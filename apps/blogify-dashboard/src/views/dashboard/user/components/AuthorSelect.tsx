import type { APIResponse, User } from '@ps/types';

import { useState, useMemo } from 'react';
import { HiUserAdd, HiCog } from 'react-icons/hi';
import { useStoreState } from '@/store';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

import { SelectContent, SelectTrigger, SelectItem, Select } from '@ps/ui/components/select';
import { Avatar } from '@ps/ui/components/avatar';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';

import UserAddDialog from './UserAddDialog';

const isAdmin = (user: User) => ['superadmin', 'admin'].includes(user.role);

export default function AuthorSelect({
  selectedUserId: initialSelectedUserId,
  blogId,
}: {
  selectedUserId: string;
  blogId: string;
}) {
  const [selectedUserId, selectUser] = useState(initialSelectedUserId);
  const user = useStoreState((s) => s.user.current);

  const { data: { data: users } = emptyAPIResponse, refetch } = useQuery<APIResponse<User>>(
    ['users'],
    { enabled: !!selectedUserId }
  );

  const selectedUser = useMemo(
    () => users.find((u) => u._id === selectedUserId) || users[0],
    [selectedUserId, users]
  );

  if (!selectedUser || !selectedUserId) return null;

  const canEdit = (u: User) => isAdmin(user) || (user.role === 'editor' && !isAdmin(u));
  const editable = canEdit(selectedUser);

  const updateAuthor = (uid: string) => {
    selectUser(uid);
    API.patch(`blogs/${blogId}`, { uid });
  };

  return (
    <section className="rounded-lg border border-gray10 p-4">
      <div className="mb-3 flex items-center justify-between gap-4">
        <h3 className="text-17 font-semibold">Author</h3>
        {editable && (
          <div className="flex gap-2">
            <UserAddDialog variant="author" refetchUsers={refetch}>
              <Button className="size-6 rounded-md p-0 text-gray12" variant="secondary">
                <HiUserAdd />
              </Button>
            </UserAddDialog>

            <Link to="/dashboard/users">
              <Button className="size-6 rounded-md p-0 text-gray12" variant="secondary">
                <HiCog />
              </Button>
            </Link>
          </div>
        )}
      </div>

      <Select value={selectedUserId} onValueChange={updateAuthor}>
        <SelectTrigger
          className="h-auto rounded-lg border-gray10 p-0"
          dropdownIndicator={editable ? null : <></>}
        >
          <UserInfo user={selectedUser} />
        </SelectTrigger>

        {editable && (
          <SelectContent>
            {users.filter(canEdit).map((u) => (
              <SelectItem className="p-0" key={u._id} value={u._id} showCheckMark={false}>
                <UserInfo user={u} />
              </SelectItem>
            ))}
          </SelectContent>
        )}
      </Select>
    </section>
  );
}

const UserInfo = ({ user, className }: { user: User; className?: string }) => (
  <div className={cn('flex w-full items-center gap-3 p-3', className)}>
    <Avatar className="size-9" src={user.profilePicture} name={user?.name} />

    <div className="text-left">
      <h4 className="text-15 font-semibold">{user.name}</h4>
      <p className="mt-0.5 text-13 capitalize text-gray9">
        {user.role === 'writer' ? 'Author' : user.role}
      </p>
    </div>
  </div>
);
