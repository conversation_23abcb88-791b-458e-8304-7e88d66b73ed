import { FaInstagram, FaFacebook, FaLinkedin, Fa<PERSON>witter, FaYoutube } from 'react-icons/fa6';
import { useContext } from 'react';
import { useSet } from 'react-use';

import { FileUploadProgress, FileUpload } from '@/modules/file-upload/FileUpload';
import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { UseFormReturn } from '@ps/ui/hooks/useForm';
import { Checkbox } from '@ps/ui/components/checkbox';
import { Button } from '@ps/ui/components/button';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';
import FormField from '@ps/ui/form/FormField';

import { UserRoleSelector } from './UserRoleSelector';
import { UserSchema } from '../utils/constants';

const MAX_FILE_SIZE = 5 * 1024 * 1024;

interface UserFormProps {
  form: UseFormReturn<UserSchema>;
  variant: 'default' | 'author';
}
export default function UserForm({ form, variant }: UserFormProps) {
  const abilities = useContext(UserAbilitiesContext);
  const {
    formState: { defaultValues },
    getInputFields,
    setValue,
    watch,
  } = form;

  const profilePicture = watch('profilePicture');
  const isAuthor = variant === 'author';

  return (
    <>
      <FormField label="Avatar" type="custom">
        <FileUploadProvider
          type="image"
          maxSize={MAX_FILE_SIZE}
          folder="user/avatars"
          onUploadComplete={(files) => {
            const url = files?.[0]?.url || '';
            setTimeout(() => {
              setValue('profilePicture', url);
            }, 300);
          }}
        >
          {({ isUploading, files, error, removeFiles }) => (
            <div className="w-full rounded-lg">
              {!files.length && !profilePicture && (
                <FileUpload
                  className="min-h-52 rounded-lg border border-dashed border-gray12"
                  supportedFormats=".png, .jpg, .jpeg"
                  maxFileSize={MAX_FILE_SIZE}
                  error={error}
                />
              )}

              {isUploading && (
                <FileUploadProgress className="min-h-52 rounded-lg border border-dashed border-gray12" />
              )}

              {((!!files.length && !isUploading) || !!profilePicture) && (
                <div className="relative flex min-h-52 rounded-lg border border-gray10 bg-cornflowerBlue flex-center">
                  <img className="h-[206px] object-contain" src={files[0]?.url || profilePicture} />
                  <Button
                    className="absolute bottom-3 right-3"
                    onClick={removeFiles}
                    variant="secondary"
                  >
                    Remove
                  </Button>
                </div>
              )}
            </div>
          )}
        </FileUploadProvider>
      </FormField>

      <FormField label="Full Name" placeholder="eg. Esther Howard" {...getInputFields('name')} />
      <FormField
        label="Occupation"
        placeholder="eg. Technical Writer"
        {...getInputFields('occupation')}
      />
      <FormField
        label="Bio"
        placeholder="Write about yourself"
        type="textarea"
        {...getInputFields('bio')}
      />
      <FormField
        label="E-mail Address"
        placeholder="eg. <EMAIL>"
        type="email"
        {...getInputFields('email')}
      />

      <FormField label="Social Handle" type="custom">
        <SocialHandles form={form} />
      </FormField>

      {abilities.user.invite && (
        <label className="mt-1 flex gap-2.5 border-t border-gray10 pb-2 pt-5">
          <Checkbox onCheckedChange={(c) => setValue('sendInvite', c as boolean)} />

          <div className="-mt-0.5">
            <h5 className="text-15 font-medium">Sent an email invitation to join Blogify</h5>
            {isAuthor ? (
              <p className="mt-2 text-13">
                The author will receive an email invitation to join your team with limited
                permissions. They will be able to update their profile info, create new blogs etc.
              </p>
            ) : (
              <p className="mt-2 text-13">
                The user will receive an email invitation to join your team with the selected role &
                permissions.
              </p>
            )}

            {!isAuthor && (
              <FormField containerClass="mt-4" label="User Role" type="custom" noError>
                <UserRoleSelector
                  defaultValue={defaultValues?.role}
                  onChange={(v) => setValue('role', v)}
                />
              </FormField>
            )}
          </div>
        </label>
      )}
    </>
  );
}

const SOCIALS = {
  facebook: { icon: FaFacebook, className: 'text-facebook' },
  x: { icon: FaXTwitter, className: 'text-black' },
  youtube: { icon: FaYoutube, className: 'text-youtube' },
  instagram: { icon: FaInstagram, className: 'text-instagram' },
  linkedin: { icon: FaLinkedin, className: 'text-linkedin' },
};
type SocialType = keyof typeof SOCIALS;

const getIcon = (social: SocialType, isActive: boolean) => {
  const Icon = SOCIALS[social].icon;
  return <Icon className={isActive ? SOCIALS[social].className : 'text-gray12'} size={20} />;
};

const SocialHandles = ({
  form: { getInputFields, formState },
}: {
  form: UseFormReturn<UserSchema>;
}) => {
  const [activeSocials, activeSocialsSet] = useSet<SocialType>(
    new Set(Object.keys(formState.defaultValues?.socials || {}) as SocialType[])
  );

  return (
    <div className="flex w-full flex-col gap-2">
      <div className="flex gap-2">
        {(Object.keys(SOCIALS) as SocialType[]).map((social) => (
          <Button
            key={social}
            variant="icon"
            className="size-10 rounded-lg border border-gray10 p-0"
            onClick={() => activeSocialsSet.toggle(social)}
          >
            {getIcon(social, activeSocialsSet.has(social))}
          </Button>
        ))}
      </div>

      {Array.from(activeSocials).map((social) => (
        <FormField
          key={social}
          placeholder={`Enter your ${social} username`}
          left={getIcon(social, true)}
          noError
          {...getInputFields(`socials.${social}`)}
        />
      ))}
    </div>
  );
};
