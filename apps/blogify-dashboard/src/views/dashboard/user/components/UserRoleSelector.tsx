import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { cn } from '@ps/ui/lib/utils';

import { UserRoles } from '../utils/constants';

const UserRoleSelector = ({
  defaultValue = 'writer',
  onChange,
}: {
  defaultValue?: string;
  onChange: (v: 'admin' | 'editor' | 'writer') => void;
}) => (
  <RadioGroup
    className="gap-0 rounded-lg border border-gray10"
    onValueChange={(v) => onChange(v as 'admin' | 'editor' | 'writer')}
    defaultValue={defaultValue}
  >
    {Object.entries(UserRoles).map(([role, info], i) => (
      <label key={role} className={cn('flex gap-x-3.5 border-gray10 p-4', { 'border-t': i !== 0 })}>
        <RadioGroupItem value={role} />

        <div className="-mt-1">
          <h5 className="text-15 font-medium">{info.name}</h5>
          <h6 className="mt-1.5 text-11 font-medium uppercase text-gray9">Access: {info.access}</h6>
          <p className="mt-2 text-13">{info.description}</p>
        </div>
      </label>
    ))}
  </RadioGroup>
);

export { UserRoleSelector };
