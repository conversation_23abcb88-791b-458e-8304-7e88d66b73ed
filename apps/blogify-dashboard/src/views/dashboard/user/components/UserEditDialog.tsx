import type { User } from '@ps/types';

import { useMutation } from 'react-query';
import { HiUserAdd } from 'react-icons/hi';
import { useEffect } from 'react';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Dialog from '@ps/ui/components/dialog';

import { zodResolver } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';

import { userFormSchema, UserSchema } from '../utils/constants';
import UserForm from './UserForm';

export default function UserEditDialog({
  user,
  isOpen,
  onClose,
  refetchUsers,
}: {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  refetchUsers: () => void;
}) {
  const form = useForm<UserSchema>({
    resolver: zodResolver(userFormSchema),
    defaultValues: user,
  });

  const {
    mutateAsync: updateUser,
    isLoading: isAddingUser,
    error,
  } = useMutation({
    mutationFn: (body: UserSchema) =>
      API.patch(`users/${user._id}`, body).catch((e: string) => {
        if (e.includes('duplicate key error')) {
          throw new Error('An user with this email already exist.');
        }
        throw new Error(e);
      }),
    onSuccess: () => {
      refetchUsers();
      onClose();
      form.reset();
    },
  });

  const submit = (v: UserSchema) => updateUser(v);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => () => form.reset(), []);

  return (
    <Dialog
      as="form"
      open={isOpen}
      onOpenChange={onClose}
      Icon={HiUserAdd}
      title="Update Profile"
      description="Update user information"
      onSubmit={form.handleSubmit(submit)}
      error={error as Error}
      actions={
        <Button className="w-full" loading={isAddingUser} type="submit">
          Save
        </Button>
      }
    >
      <UserForm variant="default" form={form} />
    </Dialog>
  );
}
