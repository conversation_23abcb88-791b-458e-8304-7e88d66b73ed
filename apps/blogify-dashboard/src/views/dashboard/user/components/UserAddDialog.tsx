import { useEffect, useState } from 'react';
import { useMutation } from 'react-query';
import { HiUserAdd } from 'react-icons/hi';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Dialog from '@ps/ui/components/dialog';

import { zodResolver } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';

import { userFormSchema, UserSchema } from '../utils/constants';
import UserForm from './UserForm';

export default function UserAddDialog({
  variant = 'default',
  children,
  refetchUsers,
}: {
  variant?: 'default' | 'author';
  children: React.ReactNode;
  refetchUsers: () => void;
}) {
  const [isOpen, onOpenChange] = useState(false);

  const form = useForm<UserSchema>({
    resolver: zodResolver(userFormSchema),
    defaultValues: { role: 'writer' },
  });

  const {
    mutateAsync: addUser,
    isLoading: isAddingUser,
    error,
  } = useMutation({
    mutationFn: (body: UserSchema) =>
      API.post('users', body).catch((e: string) => {
        if (e.includes('duplicate key error')) {
          throw new Error('An user with this email already exist.');
        }
        throw new Error(e);
      }),
    onSuccess: () => {
      refetchUsers();
      onOpenChange(false);
      form.reset();
    },
  });

  const isAuthor = variant === 'author';
  const type = isAuthor ? 'author' : 'user';

  const submit = (v: UserSchema) => addUser(v);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => () => form.reset(), []);

  return (
    <Dialog
      as="form"
      open={isOpen}
      onOpenChange={onOpenChange}
      trigger={children}
      Icon={HiUserAdd}
      title={`Add ${isAuthor ? 'Author' : 'User'}`}
      description={`Insert ${type} information to add a new ${type}. You can also invite them to join Blogify and become a part of your team.`}
      onSubmit={form.handleSubmit(submit)}
      error={error as Error}
      actions={
        <Button className="w-full" loading={isAddingUser} type="submit">
          Add New {isAuthor ? 'Author' : 'User'}
        </Button>
      }
    >
      <UserForm variant={variant} form={form} />
    </Dialog>
  );
}
