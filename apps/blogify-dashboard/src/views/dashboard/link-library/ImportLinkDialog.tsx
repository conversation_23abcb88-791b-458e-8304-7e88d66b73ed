import { API } from '@/services/api';
import { Button } from '@ps/ui/components/button';
import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import Select from '@ps/ui/form/Select';
import { DragEvent, useState } from 'react';
import toast from 'react-hot-toast';
import { FaArrowUp, FaLink } from 'react-icons/fa';
import { FiUploadCloud } from 'react-icons/fi';
import { IoMdCloseCircle } from 'react-icons/io';
import { PiFileCsvBold } from 'react-icons/pi';
import { useMutation } from 'react-query';

const PREDEFINED_COLUMNS = {
  name: 'Name',
  category: 'Category',
  affiliateLink: 'Affiliate Link',
  productPage: 'Product Page (Optional)',
} as const;

type PredefinedColumn = keyof typeof PREDEFINED_COLUMNS;
type ColumnMapping = Partial<Record<PredefinedColumn, string>>;

interface ImportLinkDialogProps extends React.ComponentProps<typeof Dialog> {
  refetchLinks: () => void;
}

const ImportLinkDialog: React.FC<ImportLinkDialogProps> = ({
  onClose,
  refetchLinks,
  ...dialogProps
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({});
  const [errors, setErrors] = useState<Partial<Record<PredefinedColumn, string>>>({});
  const [error, setError] = useState<string | null>(null);

  const { mutate: onImportLinks, isLoading } = useMutation({
    mutationFn: (formData: FormData) => API.post(`affiliate/link-library/import`, formData),
    onSuccess: () => {
      toast.success('Links imported successfully');
      refetchLinks();
      handleClose();
    },
    onError: (error: any) => {
      setError(error.response?.data?.message || 'Import failed');
      toast.error(error.response?.data?.message || 'Import failed');
    },
  });

  const readCsvHeaders = async (selectedFile: File) => {
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Please upload a CSV file');
      return;
    }
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError('File size should not exceed 10MB');
      return;
    }

    try {
      const text = await selectedFile.text();
      const headers = text
        .split('\n')[0]
        .split(',')
        .map((h) => h.trim())
        .filter(Boolean);
      if (headers.length === 0) {
        setError('CSV file appears to be empty');
        return;
      }
      setCsvHeaders(headers);
      setError(null);
    } catch (err) {
      setError('Unable to read CSV file');
      toast.error('Failed to read CSV file');
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      readCsvHeaders(selectedFile);
    }
  };
  const handleDrop = (ev: DragEvent<HTMLDivElement>) => {
    ev.preventDefault();
    const droppedFile = ev.dataTransfer.files[0];
    if (droppedFile) {
      setFile(droppedFile);
      readCsvHeaders(droppedFile);
    }
  };

  const isRequiredCollumnError = () => {
    const requiredColumns: PredefinedColumn[] = ['name', 'category', 'affiliateLink'];
    const newErrors: Partial<Record<PredefinedColumn, string>> = {};
    requiredColumns.forEach((col) => {
      if (!columnMapping[col]) {
        newErrors[col] = 'This field is required';
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length > 0;
  };

  const handleImport = async () => {
    if (!file) {
      toast.error('Please select a CSV file');
      return;
    }
    if (isRequiredCollumnError()) return;

    const formData = new FormData();
    if (file) {
      formData.append('file', file);
    }
    Object.entries(columnMapping).forEach(([key, value]) => {
      formData.append(`mapping[${key}]`, value as string);
    });
    onImportLinks(formData);
  };
  const handleClose = () => {
    onClose();
    setFile(null);
    setCsvHeaders([]);
    setColumnMapping({});
    setErrors({});
    setError(null);
  };

  const handleValueChange = (col: PredefinedColumn, value: string) => {
    setColumnMapping((prev) => ({
      ...prev,
      [col]: value,
    }));
  };

  return (
    <Dialog
      onClose={handleClose}
      title="Import Affiliate Links"
      description="Drag & drop your .csv file, then map your columns to import links."
      PrimaryIcon={FaLink}
      SecondaryIcon={FaArrowUp}
      className="max-w-sm"
      {...dialogProps}
    >
      {!file || csvHeaders.length === 0 ? (
        <div
          onDragEnter={(e) => e.preventDefault()}
          onDragOver={(e) => e.preventDefault()}
          onDrop={handleDrop}
        >
          <label className="flex w-full flex-col justify-between rounded-md p-6 text-center hover:bg-gray-50">
            <input accept=".csv" onChange={handleFileChange} className="hidden" type="file" />
            <div className="flex flex-col items-center">
              <FiUploadCloud className="text-primary" size={40} />
              <p className="mt-2 text-md">Drag & drop your file here or browse files</p>
              <Button asChild className="mt-2 min-w-32" variant="secondary">
                <div>Browse Files</div>
              </Button>
              {error && <p className="mt-1 h-4 text-center text-xs text-red4">{error}</p>}
            </div>

            <p className="mt-1 text-sm text-gray-500">Supported Formats: .csv, Max size 10 MB</p>
          </label>
        </div>
      ) : (
        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between rounded-md bg-[#f4f1f0] px-4 py-3">
            <div className="flex items-center space-x-2">
              <PiFileCsvBold className="text-primary" />
              <p className="font-semibold">{file.name}</p>
              <p className="mt-1 text-sm text-[#A38F8A]">
                {(file.size / (1024 * 1024)).toFixed(2)} MB
              </p>
            </div>
            <IoMdCloseCircle
              className="size-5 cursor-pointer text-[#A38F8A]"
              onClick={() => {
                setFile(null);
                setCsvHeaders([]);
                setColumnMapping({});
                setErrors({});
                setError(null);
              }}
            />
          </div>

          {Object.entries(PREDEFINED_COLUMNS).map(([col, label]) => (
            <div key={col} className="space-y-1">
              <p className="text-sm font-semibold">
                {label}
                {col !== 'productPage' && <span className="ml-1 text-red4">*</span>}
              </p>

              <Select
                value={columnMapping[col as PredefinedColumn] || ''}
                onChange={(e) => handleValueChange(col as PredefinedColumn, e.target.value)}
              >
                <option value="" disabled>
                  Select CSV Column
                </option>
                {csvHeaders.map((header) => (
                  <option key={header} value={header}>
                    {header}
                  </option>
                ))}
              </Select>
              {errors[col as PredefinedColumn] && (
                <p className="text-xs text-red4">{errors[col as PredefinedColumn]}</p>
              )}
            </div>
          ))}

          {error && <p className="text-center text-sm text-red4">{error}</p>}

          <div></div>
          <Button onClick={handleImport} className="w-full" loading={isLoading}>
            Import CSV
          </Button>
        </div>
      )}
    </Dialog>
  );
};

export default ImportLinkDialog;
