import { AffiliateLinkLibrary } from '@ps/types';
import { BiSolidEditAlt } from 'react-icons/bi';
import { FaTrash } from 'react-icons/fa';

type LinkLibraryTableProps = {
  data: AffiliateLinkLibrary[];
  onEdit: (link: AffiliateLinkLibrary) => void;
  onDelete: (link: AffiliateLinkLibrary) => void;
};

export default function LinkLibraryTable({ data, onEdit, onDelete }: LinkLibraryTableProps) {
  // const [selectedLinks, setSelectedLinks] = useState<string[]>([]);

  // const handleSelectAll = () => {
  //   if (selectedLinks.length === data.length) {
  //     setSelectedLinks([]);
  //   } else {
  //     setSelectedLinks(data.map((datum) => datum._id));
  //   }
  // };

  // const handleCheckboxChange = (id: string) => {
  //   setSelectedLinks((prev) =>
  //     prev.includes(id) ? prev.filter((linkId) => linkId !== id) : [...prev, id]
  //   );
  // };

  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[600px] table-auto text-sm text-gray-700">
        <thead className="h-6 bg-bg2">
          <tr className="text-left text-xs uppercase text-gray9 *:p-2">
            <th className="flex items-center gap-2 px-2 font-medium">
              {/* <input
                type="checkbox"
                className="text-primary checked:text-primary"
                onChange={handleSelectAll}
                checked={selectedLinks.length === data.length}
              /> */}
              NAME
            </th>
            <th className="px-2 font-medium">CATEGORY</th>
            <th className="px-2 font-medium">AFFILIATE LINK</th>
            <th className="px-2 text-right font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          {data.map((datum) => (
            <tr key={datum._id}>
              <td className="flex gap-2 p-2">
                {/* {datum.brandLogoUrl && (
                  <img
                    src={datum.brandLogoUrl}
                    width={20}
                    height={20}
                    className="w-5 h-5 rounded-lg"
                    alt={`${datum.brandName} logo`}
                  />
                )} */}
                {/* <td className="">
                  <input
                    type="checkbox"
                    className="text-primary checked:text-primary"
                    checked={selectedLinks.includes(datum._id)}
                    onChange={() => handleCheckboxChange(datum._id)}
                  />
                </td> */}
                <span className="truncate font-semibold">{datum.name}</span>
              </td>

              <td className="p-2 text-gray9">{datum.category || 'N/A'}</td>

              <td className="max-w-[150px] truncate p-2">
                <a
                  href={datum.affiliateLink}
                  target="_blank"
                  className="text-blue/60 hover:underline"
                  rel="noopener noreferrer"
                >
                  {datum.affiliateLink}
                </a>
              </td>
              <td className="flex justify-end gap-2 px-4 text-right">
                <BiSolidEditAlt
                  className="cursor-pointer text-gray-500"
                  onClick={() => onEdit(datum)} // Edit functionality triggered
                />

                <FaTrash
                  className="cursor-pointer text-gray-500"
                  onClick={() => onDelete(datum)} // Delete functionality triggered
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
