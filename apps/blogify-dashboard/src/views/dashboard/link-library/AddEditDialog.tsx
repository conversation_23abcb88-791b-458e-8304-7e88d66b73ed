import { AffiliateLinkLibrary } from '@ps/types';
import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import FormField from '@ps/ui/form/FormField';
import { useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FaLink } from 'react-icons/fa';
import { FiPlus } from 'react-icons/fi';
import { MdEdit } from 'react-icons/md';

interface DialogProps extends React.ComponentProps<typeof Dialog> {
  onSubmit: SubmitHandler<AffiliateLinkLibrary>;
  isLoading?: boolean;
  selectedLink?: AffiliateLinkLibrary | null;
  setSelectedLink?: (link: AffiliateLinkLibrary) => void;
}

const AddEditDialog = ({ onSubmit, isLoading, selectedLink, ...props }: DialogProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<AffiliateLinkLibrary>({
    defaultValues: selectedLink ? selectedLink : undefined,
  });

  useEffect(() => {
    if (selectedLink) {
      reset(selectedLink);
    } else {
      reset({ name: '', category: '', affiliateLink: '', productPage: '' });
    }
  }, [selectedLink, reset]);

  return (
    <Dialog
      title={selectedLink ? 'Edit Affiliate Link' : 'Add New Affiliate Link'}
      description={
        selectedLink ? 'Update name and link of this' : 'Add a new affiliate link to your library.'
      }
      PrimaryIcon={FaLink}
      SecondaryIcon={selectedLink ? MdEdit : FiPlus}
      primaryButton={{
        label: selectedLink ? 'Save Link' : 'Add Link',
        onClick: handleSubmit(onSubmit),
        disabled: isLoading,
      }}
      className="max-w-sm"
      {...props}
    >
      <form>
        <FormField
          {...register('name', { required: 'Name is required' })}
          label="Name"
          placeholder="e.g., DJI Mavic Pro"
          error={errors.name?.message}
          variant="secondary"
        />
        <FormField
          {...register('category', { required: 'Category is required' })}
          label="Category"
          placeholder="e.g., Electronics"
          error={errors.category?.message}
          variant="secondary"
        />
        <FormField
          {...register('affiliateLink', { required: 'Affiliate Link is required' })}
          label="Affiliate Link"
          placeholder="Affiliate link here"
          error={errors.affiliateLink?.message}
          variant="secondary"
        />
        <FormField
          {...register('productPage')}
          label="Product Page (Optional)"
          placeholder="Product page link"
          variant="secondary"
        />
      </form>
    </Dialog>
  );
};

export default AddEditDialog;
