import type { AffiliateLinkLibrary } from '@ps/types';
import type { SubmitHandler } from 'react-hook-form';

import { useMutation, useQuery } from 'react-query';
import { Link, useNavigate } from 'react-router-dom';
import { HiDotsHorizontal } from 'react-icons/hi';
import { FaTrash, FaLink } from 'react-icons/fa';
import { useToggle } from 'react-use';
import { useState } from 'react';
import toast from 'react-hot-toast';

import emptyAPIResponse, { APIResponseType } from '@/types/resources';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import StateHandler from '@/components/misc/StateHandler';
import DeleteDialog from '@ps/ui/components/common/dialogs/DeleteDialog';
import Pagination from '@ps/ui/components/pagination';

import ImportLinkDialog from './ImportLinkDialog';
import LinkLibraryTable from './LinkLibraryTable';
import AddEditDialog from './AddEditDialog';

export default function LinkLibrary() {
  const { limit = '20', page = '1' } = parseQuery();

  const [isAddEditDialogOpen, toggleAddEditDialog] = useToggle(false);
  const [isImportDialogOpen, toggleImportDialog] = useToggle(false);
  const [isDeleteDialogOpen, toggleDeleteDialog] = useToggle(false);
  const [selectedLink, setSelectedLink] = useState<AffiliateLinkLibrary | null>(null);
  const navigate = useNavigate();

  const {
    data: { data: result, total } = emptyAPIResponse,
    isLoading,
    error,
    refetch,
  } = useQuery<APIResponseType<AffiliateLinkLibrary>>(
    `/affiliate/link-library?limit=${limit}&page=${page}`
  );

  const onSuccess = () => {
    setSelectedLink(null);
    refetch();
    toggleAddEditDialog();
    toast.success(selectedLink ? 'Link updated successfully' : 'Link added successfully');
  };
  const onError = (error: any) => {
    toast.error(error?.response?.data?.message || 'An error occurred');
  };
  const { mutate: onCreate, isLoading: isMuting } = useMutation({
    mutationFn: async (payload: AffiliateLinkLibrary) =>
      API.post<{ url: string }>('/affiliate/link-library', payload),
    onSuccess: onSuccess,
    onError: onError,
  });
  const { mutate: onUpdate, isLoading: isUpdating } = useMutation({
    mutationFn: async (payload: AffiliateLinkLibrary) =>
      API.patch<{ url: string }>(`/affiliate/link-library/${payload._id}`, payload),
    onSuccess: onSuccess,
    onError: onError,
  });
  const { mutate: onDelete, isLoading: isDeleting } = useMutation({
    mutationFn: async (id: string) => API.remove<{ url: string }>(`/affiliate/link-library/${id}`),
    onError: onError,
    onSuccess: () => {
      setSelectedLink(null);
      refetch();
      toggleDeleteDialog();
      toast.success('Link deleted successfully');
    },
  });

  const handleEditLink = (linkData: AffiliateLinkLibrary) => {
    setSelectedLink(linkData);
    toggleAddEditDialog();
  };
  const onSubmit: SubmitHandler<AffiliateLinkLibrary> = (linkData) => {
    if (selectedLink) {
      onUpdate(linkData);
    } else {
      onCreate(linkData);
    }
  };

  const handleDeleteLink = (linkData: AffiliateLinkLibrary) => {
    setSelectedLink(linkData);
    toggleDeleteDialog();
  };
  const handleConfirmDelete = () => {
    if (selectedLink) {
      onDelete(selectedLink._id);
    }
  };

  return (
    <DashboardContainer
      title="Link Library"
      actions={
        <>
          <Button
            className="border shadow hover:opacity-80"
            variant="outline"
            onClick={toggleImportDialog}
          >
            Import
          </Button>
          <Button
            onClick={() => {
              setSelectedLink(null);
              toggleAddEditDialog();
            }}
          >
            Add Link
          </Button>
          <Link className="rounded-lg border p-1" to="#">
            <HiDotsHorizontal />
          </Link>
        </>
      }
    >
      {isLoading || error || !result?.length ? (
        <StateHandler
          loading={isLoading}
          error={error as string}
          isEmpty={!result?.length}
          emptyMsg="No link found."
        />
      ) : (
        <>
          <LinkLibraryTable data={result} onEdit={handleEditLink} onDelete={handleDeleteLink} />
          <Pagination
            className="mt-6"
            onPaging={(url) => navigate(url)}
            limit={parseInt(limit, 10)}
            page={parseInt(page, 10)}
            total={total}
          />
        </>
      )}

      <AddEditDialog
        onSubmit={onSubmit}
        isOpen={isAddEditDialogOpen}
        onClose={() => {
          toggleAddEditDialog();
          setSelectedLink(null);
        }}
        isLoading={isMuting || isUpdating}
        selectedLink={selectedLink}
      />

      <DeleteDialog
        title="Delete Link"
        description="This link will be deleted from your link library. Links used in your blog will stay unaffected."
        deleteContentTitle={selectedLink?.name}
        deleteContentDescription={selectedLink?.affiliateLink}
        PrimaryIcon={FaLink}
        SecondaryIcon={FaTrash}
        primaryButton={{
          label: 'Delete Link',
          onClick: handleConfirmDelete,
          disabled: isDeleting,
        }}
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          toggleDeleteDialog();
          setSelectedLink(null);
        }}
      />

      <ImportLinkDialog
        isOpen={isImportDialogOpen}
        onClose={toggleImportDialog}
        refetchLinks={refetch}
      />
    </DashboardContainer>
  );
}
