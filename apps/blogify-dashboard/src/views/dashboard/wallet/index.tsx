import type { StatsAPIResponse } from '@/types/misc/stats.type';

import { useContext } from 'react';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

import { UserAbilitiesContext } from '@/context/UserAbilitiesContext';
import { AccessDenied } from '@/components/misc/AccessDenied';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import emptyStats from '@/types/misc/stats.type';

import PayoutRequestDialog from './components/PayoutRequestDialog';
import PayoutHistoryTable from './components/PayoutHistoryTable';

export default function Wallet() {
  const [isPayoutDialogOpen, togglePayoutDialog] = useToggle(false);

  const abilities = useContext(UserAbilitiesContext);

  const { data: { wallet } = emptyStats } = useQuery<StatsAPIResponse>(['me/stats'], {
    enabled: abilities.affiliate.view,
  });

  if (!abilities.affiliate.view) {
    return <AccessDenied />;
  }

  return (
    <DashboardContainer
      title="Wallet"
      actions={
        <Link to="/dashboard/affiliate/pending-balance">
          <Button variant="secondary">Pending Balance</Button>
        </Link>
      }
    >
      <section className="my-6 flex min-h-40 flex-col justify-between rounded-lg border border-gray10 p-4 sm:flex-row">
        <div className="flex flex-col justify-between p-2">
          <div>
            <div className="text-11 font-medium text-primary">AVAILABLE BALANCE</div>
            <div className="mt-3 text-3xl font-semibold">
              ${wallet.balance.toFixed(2)} <span className="text-lg">{wallet.currency}</span>
            </div>
          </div>

          <div className="text-15">Minimum withdrawal amount: $50</div>
        </div>

        <div className="mt-4 flex items-end justify-between">
          <Button onClick={togglePayoutDialog}>Request a Withdrawal</Button>
        </div>
      </section>

      <PayoutHistoryTable />

      <PayoutRequestDialog isOpen={isPayoutDialogOpen} onClose={togglePayoutDialog} />
    </DashboardContainer>
  );
}
