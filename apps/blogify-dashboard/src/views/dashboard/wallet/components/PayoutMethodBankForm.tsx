import type { PayoutMethod } from '@ps/types';

import { useMutation } from 'react-query';
import { FaSave } from 'react-icons/fa';

import { zodResolver, z } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';
import toast from 'react-hot-toast';

import { useStoreState } from '@/store';
import { emailSchema } from '@ps/ui/utils/validations';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import useCountryStateCity from '@/hooks/useCountryStateCity';
import FormField from '@ps/ui/form/FormField';

const payoutMethodBankSchema = z.object({
  // Receiver Info
  fullName: z.string(),
  email: emailSchema,
  // Bank Info
  bankName: z.string(),
  branchName: z.string(),
  accountNumber: z.string(),
  accountType: z.string(),
  swiftCode: z.string(),
  routingNumber: z.string(),
  // Address
  streetAddress: z.string(),
  country: z.string(),
  state: z.string(),
  city: z.string(),
  zipCode: z.string(),
});
type PayoutMethodBankSchema = z.infer<typeof payoutMethodBankSchema>;

export default function PayoutMethodBankForm({
  payoutMethod,
  onSubmit,
}: {
  payoutMethod?: PayoutMethod;
  onSubmit: (shouldRefetch: boolean) => void;
}) {
  const user = useStoreState((s) => s.user.current);

  const { getInputFields, handleSubmit, watch } = useForm<PayoutMethodBankSchema>({
    resolver: zodResolver(payoutMethodBankSchema),
    defaultValues: {
      fullName: user.name,
      email: user.email,
      ...(payoutMethod?.info || {}),
    },
  });

  const { mutateAsync: savePayoutMethod, isLoading: isSaving } = useMutation({
    mutationFn: async (body: Partial<PayoutMethod>) =>
      payoutMethod
        ? API.patch(`wallet/payout-methods/${payoutMethod._id}`, body)
        : API.post('wallet/payout-methods', body),
  });

  const submit = async (info: PayoutMethodBankSchema) => {
    const body: Partial<PayoutMethod> = { type: 'bank', info };

    return savePayoutMethod(body)
      .then(() => toast.success('Payment Method Saved Successfully.'))
      .then(() => onSubmit(true))
      .catch((e) => toast.error(e?.error || e?.message || e));
  };

  const selectedCountry = watch('country');
  const selectedState = watch('state');

  const { countries, states, cities } = useCountryStateCity([selectedCountry, selectedState]);

  return (
    <form onSubmit={handleSubmit(submit)} className="mt-2 rounded-lg border border-gray10 p-4">
      <div>
        <h4 className="mb-4 text-11 font-medium text-gray9">RECEIVER INFORMATION</h4>

        <FormField
          label="Full Name"
          placeholder="Your full name used in the bank"
          {...getInputFields('fullName')}
        />
        <FormField label="Email Address" type="email" {...getInputFields('email')} />
      </div>

      <div>
        <h4 className="mb-4 text-11 font-medium text-gray9">BANK INFORMATION</h4>

        <div className="grid grid-cols-2 gap-x-4">
          <FormField label="Bank Name" {...getInputFields('bankName')} />
          <FormField label="Branch Name" {...getInputFields('branchName')} />
          <FormField label="Account Number" {...getInputFields('accountNumber')} />
          <FormField label="Account Type" type="select" {...getInputFields('accountType')}>
            <option value="">Select Account Type</option>
            <option value="savings">Savings</option>
            <option value="checking">Checking</option>
            <option value="current">Current</option>
          </FormField>
          <FormField label="SWIFT Code" {...getInputFields('swiftCode')} />
          <FormField label="Routing Number / AHC (US Only)" {...getInputFields('routingNumber')} />
        </div>
      </div>

      <div>
        <h4 className="mb-4 text-11 font-medium text-gray9">ADDRESS</h4>
        <FormField label="Street Address" {...getInputFields('streetAddress')} />
        <div className="grid grid-cols-2 gap-x-4">
          <FormField label="Country" type="select" {...getInputFields('country')}>
            <option value="">Select Country</option>
            {countries.map((country) => (
              <option key={country.iso3} value={country.iso3}>
                {country.name}
              </option>
            ))}
          </FormField>

          <FormField label="State" type="select" {...getInputFields('state')}>
            <option value="">Select State</option>
            {states.map((state) => (
              <option key={state.name} value={state.name}>
                {state.name}
              </option>
            ))}
          </FormField>

          <FormField label="City" type="select" {...getInputFields('city')}>
            <option value="">Select City</option>
            {cities.map((city) => (
              <option key={city.name} value={city.name}>
                {city.name}
              </option>
            ))}
          </FormField>

          <FormField label="Zip Code" {...getInputFields('zipCode')} />
        </div>
      </div>

      <div className="mt-2 flex gap-4">
        {payoutMethod && (
          <Button
            type="button"
            className="w-full"
            variant="secondary"
            onClick={() => onSubmit(false)}
          >
            Cancel
          </Button>
        )}
        <Button className="w-full" type="submit" variant="secondary" loading={isSaving}>
          <FaSave />
          Save Bank Details
        </Button>
      </div>
    </form>
  );
}
