import { GrAtm } from 'react-icons/gr';

import Dialog from '@ps/ui/components/common/dialogs/Dialog';

import PayoutRequestForm from './PayoutRequestForm';

export default function PayoutRequestDialog({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  return (
    <Dialog
      className="w-full max-w-2xl"
      PrimaryIcon={GrAtm}
      title="Payout"
      description={
        <>
          <span className="text-primary">Minimum withdrawal amount is $50.</span> <br />
          Enter the payout amount and bank details to request a payout. Payout will take 10-15
          working days. Please check payout history to see the status of your request.
        </>
      }
      isOpen={isOpen}
      onClose={onClose}
    >
      <PayoutRequestForm isOpen={isOpen} onClose={onClose} />
    </Dialog>
  );
}
