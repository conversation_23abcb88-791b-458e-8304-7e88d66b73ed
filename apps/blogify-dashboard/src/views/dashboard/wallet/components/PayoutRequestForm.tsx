import type { APIResponse, PayoutMethod } from '@ps/types';

import { useEffect, useState, Fragment } from 'react';
import { useMutation, useQuery } from 'react-query';
import { BiSolidEditAlt } from 'react-icons/bi';
import { AiFillBank } from 'react-icons/ai';
import toast from 'react-hot-toast';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { zodResolver, z } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import emptyAPIResponse from '@/types/resources';
import Form<PERSON>ield from '@ps/ui/form/FormField';
import Spinner from '@/components/misc/Spinner';

import PayoutMethodBankForm from './PayoutMethodBankForm';

const payoutRequestSchema = z.object({
  amount: z.coerce
    .number({ required_error: 'Amount is required.' })
    .min(50, 'Minimum withdrawal is $50.'),
  payoutMethodId: z.string({ required_error: 'Please select a payout method.' }),
});
type PayoutRequestSchema = z.infer<typeof payoutRequestSchema>;

export default function PayoutRequestForm({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const [isPayoutFormEditOpen, togglePayoutFormEdit] = useState(false);
  const [selectedPayoutMethod, selectPayoutMethod] = useState<PayoutMethod | null>(null);

  const { formState, getInputFields, getValues, setValue, watch } = useForm<PayoutRequestSchema>({
    resolver: zodResolver(payoutRequestSchema),
    defaultValues: {
      amount: 0,
    },
  });

  const {
    data: { data: payoutMethods } = emptyAPIResponse,
    isFetching,
    isLoading,
    refetch,
  } = useQuery<APIResponse<PayoutMethod>>('wallet/payout-methods', {
    onSuccess: (r) => setValue('payoutMethodId', r?.data?.[0]._id),
  });

  const {
    mutateAsync: requestPayout,
    error: requestPayoutError,
    isLoading: isRequesting,
  } = useMutation({
    mutationFn: async () => {
      const body = {
        amount: parseInt(String(getValues('amount')), 10),
        payoutMethodId: getValues('payoutMethodId'),
      };
      return API.post('wallet/payouts', body)
        .then(() => toast.success('Payout Requested Successfully.'))
        .then(() => onClose());
    },
  });

  useEffect(() => {
    if (payoutMethods?.[0]?._id) {
      setValue('payoutMethodId', payoutMethods?.[0]._id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  return (
    <div>
      <AmountInput {...getInputFields('amount')} />

      <FormField containerClass="-mt-1 min-h-0" label="Transfer To" type="custom" noError>
        <div className="flex items-center gap-3 rounded-md border border-gray10 px-3 py-[7px]">
          <AiFillBank className="text-black" size={20} />
          <span className="text-15 font-medium">Bank</span>
        </div>
      </FormField>

      {isLoading ? (
        <div className="mt-2 flex h-12 rounded-lg border border-gray10 flex-center">
          <Spinner />
        </div>
      ) : (
        <>
          {isPayoutFormEditOpen && selectedPayoutMethod?._id ? (
            <PayoutMethodBankForm
              payoutMethod={selectedPayoutMethod}
              onSubmit={(shouldRefetch) => {
                togglePayoutFormEdit(false);
                if (shouldRefetch) {
                  refetch().then(() => selectPayoutMethod(null));
                } else {
                  selectPayoutMethod(null);
                }
              }}
            />
          ) : !!payoutMethods.length ? (
            <RadioGroup
              className="mt-2 flex flex-col gap-3"
              value={watch('payoutMethodId')}
              onValueChange={(v) => setValue('payoutMethodId', v)}
            >
              {payoutMethods.map((payoutMethod) => (
                <Fragment key={payoutMethod._id}>
                  {isFetching && selectedPayoutMethod?._id === payoutMethod._id ? (
                    <div className="flex h-12 rounded-lg border border-gray10 flex-center">
                      <Spinner />
                    </div>
                  ) : (
                    <PaymentMethodRadioItem
                      payoutMethod={payoutMethod}
                      onEdit={() => {
                        togglePayoutFormEdit(true);
                        selectPayoutMethod(payoutMethod);
                      }}
                    />
                  )}
                </Fragment>
              ))}
            </RadioGroup>
          ) : (
            <PayoutMethodBankForm
              onSubmit={(shouldRefetch) => (shouldRefetch ? refetch() : null)}
            />
          )}
        </>
      )}

      <div className="mt-3 pt-0.5">
        <div className="mb-1.5 h-5 text-13 text-red4">{requestPayoutError as string}</div>
        <Button
          className="w-full"
          disabled={!formState.isValid}
          onClick={() => requestPayout()}
          loading={isRequesting}
        >
          Request Payout
        </Button>
      </div>
    </div>
  );
}

const AmountInput = (props: React.ComponentProps<typeof FormField>) => (
  <div className="relative">
    <div className="absolute left-0 top-7 z-10 border-r border-gray10 px-3 py-2 text-15 font-medium">
      $USD
    </div>
    <FormField label="Amount" type="number" className="pl-20" {...props} />
  </div>
);

const PaymentMethodRadioItem = ({
  payoutMethod,
  onEdit,
}: {
  payoutMethod: PayoutMethod;
  onEdit: () => void;
}) => (
  <label className="flex items-center justify-between gap-2 rounded-lg border border-gray10 px-3 py-[11px]">
    <div className="flex items-center gap-3">
      <RadioGroupItem className="ml-0.5" value={payoutMethod._id} />
      <span className="text-15 font-medium">{payoutMethod.info.fullName}</span>
    </div>

    <div className="flex items-center gap-3">
      <span className="text-11 font-medium">ACC: {payoutMethod.info.accountNumber}</span>
      <Button
        className="h-6 rounded-md px-1.5 !text-11 font-medium text-gray9"
        onClick={() => onEdit()}
        variant="secondary"
      >
        <BiSolidEditAlt /> EDIT
      </Button>
    </div>
  </label>
);
