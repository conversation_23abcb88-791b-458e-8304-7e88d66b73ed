import type { APIResponse, Payout } from '@ps/types';

import { useNavigate } from 'react-router-dom';
import { AiFillBank } from 'react-icons/ai';
import { MdMoreVert } from 'react-icons/md';
import { useQuery } from 'react-query';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { parseQuery } from '@/utils';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';

const PayoutTo = (payout: Payout) => {
  switch (payout.type) {
    case 'bank':
      return (
        <div className="flex items-center gap-2.5">
          <AiFillBank className="text-gray12" size={16} />
          <span className="font-medium">{payout.payoutMethodInfo.bankName}</span>
        </div>
      );
    default:
      return null;
  }
};

const getStatusColors = (status: Payout['status']): string =>
  status === 'successful'
    ? 'text-[#093] bg-[#********]'
    : status === 'failed'
      ? 'text-[#c02] bg-[#cc002219;]'
      : status === 'initiated'
        ? 'text-[#879f00] bg-[#879f0019]'
        : '';

export default function PayoutHistoryTable() {
  const { limit = '10', page = '1' } = parseQuery();

  const { data: { data: payouts, total } = emptyAPIResponse, isLoading } = useQuery<
    APIResponse<Payout>
  >([`wallet/payouts?page=${page}&limit=${limit}`]);

  const navigate = useNavigate();

  const copyTrxId = (trxId: string) => {
    navigator.clipboard.writeText(trxId);
    toast.success('Transaction ID copied!');
  };

  const downloadReceipt = (payout: Payout) => {
    const receiptText = `
      Receipt for Payout
      --------------------
      Amount: $${payout.amount}
      Date: ${dayjs(payout.createdAt).format('DD MMM, YYYY')}
      To: ${payout.payoutMethodInfo.bankName}
      ACC. NO: ${payout.payoutMethodInfo.accountNumber}
      TRXID: ${payout.transactionId}
      Status: ${payout.status}
    `;

    const blob = new Blob([receiptText], { type: 'text/plain' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `receipt-${payout.transactionId}.txt`;
    link.click();
  };

  return (
    <>
      <h2 className="pb-4 text-21 font-semibold">Payout History</h2>

      <Table>
        <TableHeader>
          {total ? (
            <TableRow>
              <TableHead>Amount</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>To</TableHead>
              <TableHead>ACC. NO</TableHead>
              <TableHead>TRXID</TableHead>
              <TableHead className="text-right">Status</TableHead>
              <TableHead></TableHead>
            </TableRow>
          ) : (
            <TableRow>
              <TableHead colSpan={7}></TableHead>
            </TableRow>
          )}
        </TableHeader>

        <TableBody>
          {!total && (
            <TableRow>
              <TableCell colSpan={7} className="text-center text-13 text-gray9">
                {isLoading ? 'Loading...' : `You haven't made a payout yet.`}
              </TableCell>
            </TableRow>
          )}
          {payouts.map((payout) => (
            <TableRow key={payout._id}>
              <TableCell className="font-medium">${payout.amount}</TableCell>
              <TableCell className="text-13 text-gray9">
                {dayjs(payout.createdAt).format('DD MMM, YYYY')}
              </TableCell>
              <TableCell>
                <PayoutTo {...payout} />
              </TableCell>
              <TableCell className="font-medium">{payout.payoutMethodInfo.accountNumber}</TableCell>
              <TableCell className="text-13 text-gray9">{payout.transactionId || '- -'}</TableCell>

              <TableCell className="text-right">
                <div
                  className={cn(
                    'inline-block h-5 rounded-3xl px-2 py-0.5 !text-11 font-semibold uppercase',
                    getStatusColors(payout.status)
                  )}
                >
                  {payout.status}
                </div>
              </TableCell>

              <TableCell align="right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="flex size-5 flex-center">
                      <MdMoreVert className="text-gray12" size={14} />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => copyTrxId(payout.transactionId)}>
                      Copy TRXID
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => downloadReceipt(payout)}>
                      Download Receipt
                    </DropdownMenuItem>
                    {/* <DropdownMenuItem onClick={} className="text-red">
                      Report an Issue
                    </DropdownMenuItem> */}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Pagination
        className="mt-6"
        onPaging={(url) => navigate(url)}
        limit={parseInt(limit, 10)}
        page={parseInt(page, 10)}
        total={total}
      />
    </>
  );
}
