import { useToggle } from 'react-use';
import { FaImage } from 'react-icons/fa';

import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { CONTENT_COUNT_MAX_PER_TYPE } from '../utils/constants';
import { useYoutubeContentFavorite } from '../utils/useYoutubeMutation';
import { TextBox, Card } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoThumbnailDialog from '../components/dialogs/YoutubeVideoThumbnailDialog';
import YoutubeVideoContentHeader from '../components/YoutubeVideoContentHeader';
import useYoutubeContent from '../utils/useYoutubeContent';

export default function YoutubeVideoThumbnails() {
  const { video, contents: thumbnails, refetchContents } = useYoutubeContent('thumbnail');
  const { setFavorite } = useYoutubeContentFavorite(video.id);

  const [isThumbnailCreate, toggleThumbnailCreate] = useToggle(false);

  const hasContent =
    (thumbnails || []).filter((t) => t.status !== 'generation_failed').length >=
    CONTENT_COUNT_MAX_PER_TYPE.thumbnail;

  const onThumbnailCreateDialogClose = () => {
    toggleThumbnailCreate();
    refetchContents();
  };

  return (
    <>
      <Card>
        <YoutubeVideoContentHeader
          title="Create a Video Thumbnail"
          description="Create an AI generated video thumbnail for your video. Generating images cost one credit per image."
          hasContent={hasContent}
          primaryAction={{
            children: 'Create a video thumbnail',
            onClick: toggleThumbnailCreate,
            icon: FaImage,
          }}
        />

        {!!thumbnails?.length && (
          <div className="mb-2 mt-6 flex flex-wrap gap-6 sm:gap-10">
            {thumbnails.map(({ _id, status, failReason, isFavorite, contentData }, i) => (
              <div key={i} className="w-full md:w-[calc(50%-20px)]">
                <label className="block h-full">
                  <div className="flex h-full flex-col justify-between rounded border border-gray5 bg-gray7">
                    <TextBox>
                      <div className="flex items-center gap-4">
                        <Input
                          onClick={() => setFavorite(_id)}
                          defaultChecked={isFavorite}
                          name="thumbnail"
                          type="radio"
                          size={16}
                        />

                        <div className="text-sm font-semibold text-black1">
                          {status === 'generating' ? 'Generating...' : `Thumbnail_${i + 1}.png`}
                        </div>
                      </div>
                    </TextBox>

                    <div className="p-3 pb-0">
                      {status === 'generating' ? (
                        <div className="flex flex-center">
                          <Loader />
                        </div>
                      ) : status === 'generation_failed' ? (
                        <div className="text-red">
                          Thumbnail generation failed.
                          <br />
                          {failReason}
                        </div>
                      ) : (
                        <img width="100%" height="auto" src={contentData} />
                      )}
                    </div>

                    <div className="p-3 pt-0">
                      <Link to={contentData} download={`Thumbnail_${i + 1}.png`}>
                        <Button className="mt-3" disabled={status !== 'generated'}>
                          Download
                        </Button>
                      </Link>
                    </div>
                  </div>
                </label>
              </div>
            ))}
          </div>
        )}
      </Card>

      {video?.id && (
        <YoutubeVideoThumbnailDialog
          video={video}
          summary={video.data?.summary || ''}
          isThumbnailCreate={isThumbnailCreate}
          toggleThumbnailCreate={onThumbnailCreateDialogClose}
        />
      )}
    </>
  );
}
