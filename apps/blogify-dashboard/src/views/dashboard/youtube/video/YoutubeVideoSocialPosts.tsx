import { PiTextTBold } from 'react-icons/pi';
import { useToggle } from 'react-use';

import { cn } from '@ps/ui/lib/utils';
import Loader from '@/components/misc/Loader';
import Theme from '@/styles/theme';

import { Copy<PERSON><PERSON>on, TextBox, Card } from '../components/YoutubeVideoContentLayouts';
import { SOCIAL_PLATFORMS } from '../utils/constants';
import YoutubeVideoSocialPostsDialog from '../components/dialogs/YoutubeVideoSocialPostsDialog';
import YoutubeVideoContentHeader from '../components/YoutubeVideoContentHeader';
import useYoutubeContent from '../utils/useYoutubeContent';

export default function YoutubeVideoSocialPosts() {
  const { video, contents: socialPosts, refetchContents } = useYoutubeContent('social-post');
  const [isSocialPost, toggleSocialPost] = useToggle(false);

  const posts =
    socialPosts?.map((sp) => {
      const platform =
        sp.contentType === 'social_post_facebook'
          ? SOCIAL_PLATFORMS[0]
          : sp.contentType === 'social_post_twitter'
            ? SOCIAL_PLATFORMS[1]
            : SOCIAL_PLATFORMS[2];

      return { ...platform, ...sp };
    }) || [];

  const hasContent = posts.length >= 15;

  const onSocialPostDialogClose = () => {
    toggleSocialPost();
    refetchContents();
  };

  return (
    <>
      <Card>
        <YoutubeVideoContentHeader
          title="Social Media Post"
          hasContent={hasContent}
          description="Here are your social media posts. Copy these posts to use on your social media platforms."
          primaryAction={{
            children: 'Create Social Media Post',
            onClick: toggleSocialPost,
            icon: PiTextTBold,
          }}
        />

        {!!posts?.length && (
          <div className={cn('flex flex-col gap-6', { 'mt-2': hasContent, 'mt-6': !hasContent })}>
            {posts.map(({ Icon, title, status, failReason, contentData }, i) => (
              <div key={i} className="rounded border border-gray5 bg-gray7">
                <TextBox>
                  <div className="flex items-center gap-2">
                    <Icon size={16} color={Theme.colors[title]} />
                    <div className="text-sm font-semibold capitalize text-black1">{title}</div>
                  </div>

                  {status === 'generated' && <CopyButton content={contentData || ''} />}
                </TextBox>
                <div className="p-4">
                  {status === 'generation_failed' ? (
                    <div className="text-sm text-black1">{failReason}</div>
                  ) : status === 'generating' ? (
                    <div className="flex flex-center">
                      <Loader />
                    </div>
                  ) : (
                    <div
                      className="text-sm text-black1"
                      dangerouslySetInnerHTML={{
                        __html: (contentData || '').replace(/\n/g, '<br/>'),
                      }}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      <YoutubeVideoSocialPostsDialog
        video={video}
        isSocialPost={isSocialPost}
        toggleSocialPost={onSocialPostDialogClose}
      />
    </>
  );
}
