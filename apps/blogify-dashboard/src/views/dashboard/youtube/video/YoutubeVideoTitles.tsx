import { PiTextTBold } from 'react-icons/pi';

import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';

import { useYoutubeContentFavorite } from '../utils/useYoutubeMutation';
import { CopyButton } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoTitles() {
  const {
    videoContents: { titles },
    refetchContents,
    video,
  } = useYoutubeVideo();

  const { setFavorite } = useYoutubeContentFavorite(video.id, { onSuccess: refetchContents });

  return (
    <YoutubeVideoContentContainer
      contentType="title"
      icon={PiTextTBold}
      contents={titles}
      title="Titles"
    >
      <div className="rounded border border-gray5">
        {titles?.map(({ _id, isFavorite, contentData }, index) => (
          <label key={index}>
            <div
              className={cn(
                'flex justify-between gap-1 px-4 py-3 hover:bg-gray7 [&>.copy]:hover:visible',
                { 'bg-gray7': isFavorite }
              )}
            >
              <div className="flex items-center gap-4">
                <Input
                  onChange={() => setFavorite(_id)}
                  defaultChecked={isFavorite}
                  type="radio"
                  name="title"
                  size={16}
                />

                <div className="text-sm font-semibold text-black1">{contentData}</div>
              </div>

              <CopyButton
                // eslint-disable-next-line tailwindcss/no-custom-classname
                className={cn(`xs copy invisible`, { visible: isFavorite })}
                content={contentData}
              />
            </div>
          </label>
        ))}
      </div>
    </YoutubeVideoContentContainer>
  );
}
