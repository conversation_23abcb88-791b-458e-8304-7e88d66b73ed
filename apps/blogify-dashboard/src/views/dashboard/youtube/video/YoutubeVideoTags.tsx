import { useEffect } from 'react';
import { RiHashtag } from 'react-icons/ri';
import { FiTag } from 'react-icons/fi';

import { copy } from '@/utils/clipboard';

import { useYoutubeContentDelete } from '../utils/useYoutubeMutation';
import { YoutubeVideoTagButton } from '../components/YoutubeVideoTags';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoTagsHashtags() {
  const { refetchContents } = useYoutubeVideo();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => refetchContents, []);

  return (
    <div className="flex flex-col gap-2 md:flex-row">
      <YoutubeVideoTagsCard />
      <YoutubeVideoHashTagsCard />
    </div>
  );
}

const YoutubeVideoTagsCard = () => {
  const {
    videoContents: { tags },
    video: { id },
  } = useYoutubeVideo();

  const { deselectContent } = useYoutubeContentDelete(id);

  return (
    <YoutubeVideoContentContainer
      className="w-full md:w-1/2"
      onCopy={() => copy(tags?.map((t) => t.contentData).join(', ') || '')}
      copyText="Copy All Tags"
      contentType="tag"
      contents={tags}
      title="Tags"
      icon={FiTag}
    >
      <div className="flex flex-wrap gap-2">
        {tags?.map((tag, index) => (
          <YoutubeVideoTagButton key={index} tag={tag} onDelete={() => deselectContent(tag._id)} />
        ))}
      </div>
    </YoutubeVideoContentContainer>
  );
};
const YoutubeVideoHashTagsCard = () => {
  const {
    videoContents: { hashtags },
    video: { id },
  } = useYoutubeVideo();

  const { deselectContent } = useYoutubeContentDelete(id);

  const copyContent =
    hashtags
      ?.map(({ contentData }) => `${!contentData.startsWith('#') ? '#' : ''}${contentData}`)
      .join(' ') || '';

  return (
    <YoutubeVideoContentContainer
      className="w-full md:w-1/2"
      onCopy={() => copy(copyContent)}
      copyText="Copy All Hashtags"
      contentType="hashtag"
      contents={hashtags}
      title="Hashtags"
      icon={RiHashtag}
    >
      <div className="flex flex-wrap gap-2">
        {hashtags?.map((tag, index) => (
          <YoutubeVideoTagButton
            onDelete={() => deselectContent(tag._id)}
            key={index}
            tag={tag}
            isHashtag
          />
        ))}
      </div>
    </YoutubeVideoContentContainer>
  );
};
