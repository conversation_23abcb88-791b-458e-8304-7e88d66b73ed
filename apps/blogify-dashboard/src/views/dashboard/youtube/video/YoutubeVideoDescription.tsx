import { MdLineStyle } from 'react-icons/md';

import { Input } from '@ps/ui/components/input';

import { useYoutubeContentFavorite } from '../utils/useYoutubeMutation';
import { CopyButton, TextBox } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoDescription() {
  const {
    videoContents: { descriptions },
    refetchContents,
    video,
  } = useYoutubeVideo();

  const { setFavorite } = useYoutubeContentFavorite(video.id, { onSuccess: refetchContents });

  return (
    <YoutubeVideoContentContainer
      contentType="description"
      contents={descriptions}
      title="Descriptions"
      icon={MdLineStyle}
    >
      <div className="flex flex-col gap-6">
        {descriptions?.map(({ _id, isFavorite, contentData }, index) => (
          <label key={index}>
            <div className="rounded border border-gray5 bg-gray7">
              <TextBox>
                <div className="flex items-center gap-4">
                  <Input
                    onClick={() => setFavorite(_id)}
                    defaultChecked={isFavorite}
                    name="description"
                    type="radio"
                    size={16}
                  />

                  <div className="text-sm font-semibold text-black1">
                    Description {descriptions.length > 1 ? index + 1 : ''}
                  </div>
                </div>

                <CopyButton content={contentData} />
              </TextBox>
              <div className="p-4">
                <div
                  dangerouslySetInnerHTML={{ __html: contentData.replace(/\n/g, '<br/>') }}
                  className="text-sm text-black1"
                />
              </div>
            </div>
          </label>
        ))}
      </div>
    </YoutubeVideoContentContainer>
  );
}
