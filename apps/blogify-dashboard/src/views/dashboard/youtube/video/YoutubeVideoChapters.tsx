import { ImStack } from 'react-icons/im';

import { copy } from '@/utils/clipboard';

import { formatChaptersForCopy } from '../utils';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import YoutubeVideoChapters from '../components/YoutubeVideoChapters';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoChaptersView() {
  const {
    videoContents: { chapters = [] },
  } = useYoutubeVideo();

  return (
    <YoutubeVideoContentContainer
      onCopy={() => copy(formatChaptersForCopy(chapters))}
      copyText="Copy Chapters"
      contentType="chapters"
      contents={chapters}
      title="Chapters"
      icon={ImStack}
    >
      <YoutubeVideoChapters chapters={chapters} />
    </YoutubeVideoContentContainer>
  );
}
