import type {
  YoutubeVideoContentOverview,
  YoutubeVideoChapter,
} from '@/types/resources/youtube-contents.type';

import { MdLineStyle } from 'react-icons/md';
import { PiTextTBold } from 'react-icons/pi';
import { <PERSON>i<PERSON><PERSON><PERSON> } from 'react-icons/ri';
import { useQuery } from 'react-query';
import { ImStack } from 'react-icons/im';
import { BsStars } from 'react-icons/bs';
import { FaImage } from 'react-icons/fa';
import { FiTag } from 'react-icons/fi';

import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { cn } from '@ps/ui/lib/utils';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { formatChaptersForCopy, isOwnChannelVideo, getThumbnail } from '../utils';
import { CopyButton } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoActionButton from '../components/YoutubeVideoActionButton';
import YoutubeVideoChapters from '../components/YoutubeVideoChapters';
import YoutubeVideoTags from '../components/YoutubeVideoTags';
import useYoutubeVideo from '../utils/useYoutubeVideo';
import useYoutube from '../utils/useYoutube';

export default function YoutubeVideoOverview() {
  const { channel } = useYoutube();
  const { video } = useYoutubeVideo();
  const user = useStoreState((u) => u.user.current);

  const { data: { title, thumbnail, description, tags, hashtags, chapters } = {} } =
    useQuery<YoutubeVideoContentOverview>(`youtube/${video.id}/contents/overview`, {
      enabled: !!video.id,
    });

  const isOwnVideo = isOwnChannelVideo(channel, video) && user.hasYouTubeProAddon;

  return (
    <Card className="mt-0.5 text-black1">
      <div className="flex flex-col gap-3 md:flex-row">
        <div className="w-full p-6 lg:w-[70%]">
          {thumbnail || getThumbnail(video, 'maxres') ? (
            <div className="mb-6">
              <img
                className={cn('aspect-video w-full object-cover', {
                  'rounded-t': isOwnVideo,
                  rounded: !isOwnVideo,
                })}
                src={thumbnail || getThumbnail(video, 'maxres')}
                alt={video.snippet.title}
              />
              {isOwnVideo && (
                <Link to={`/dashboard/youtube/videos/${video.id}/thumbnails`}>
                  <Button className="w-full rounded-b" variant="secondary">
                    <div className="flex w-full items-center justify-end gap-1">
                      <BsStars />
                      Create AI generated thumbnail
                    </div>
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <div className="flex flex-center">
              <Loader />
            </div>
          )}

          <YoutubeOverviewSection title="Title" content={title || video.snippet.title}>
            <div className="text-xl font-semibold">{title || video.snippet.title}</div>
          </YoutubeOverviewSection>

          <YoutubeOverviewSection
            title="Description"
            content={description || video.snippet.description}
          >
            <div
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: (description || video.snippet.description).replace(/\n/g, '<br/>'),
              }}
            />
          </YoutubeOverviewSection>

          <YoutubeOverviewSection title="Chapters" content={chapters}>
            <YoutubeVideoChapters chapters={chapters} />
          </YoutubeOverviewSection>

          <YoutubeOverviewSection title="Tags" content={tags}>
            <YoutubeVideoTags tags={tags} />
          </YoutubeOverviewSection>

          <YoutubeOverviewSection title="Hashtags" content={hashtags}>
            <YoutubeVideoTags tags={hashtags} />
          </YoutubeOverviewSection>
        </div>

        {isOwnVideo && (
          <div className="w-full p-6 lg:w-[30%]">
            <div className="mb-2 font-semibold">Overview</div>
            <div className="mb-5 text-sm">
              Here are your selected video details. Easily copy and use them on YouTube.
            </div>
            <div className="flex flex-col gap-3">
              <Link to="./thumbnails">
                <YoutubeVideoActionButton className="w-full" complete={!!title} icon={FaImage}>
                  Thumbnail
                </YoutubeVideoActionButton>
              </Link>
              <Link to="./title">
                <YoutubeVideoActionButton className="w-full" complete={!!title} icon={PiTextTBold}>
                  Titles
                </YoutubeVideoActionButton>
              </Link>
              <Link to="./description">
                <YoutubeVideoActionButton
                  className="w-full"
                  complete={!!description}
                  icon={MdLineStyle}
                >
                  Descriptions
                </YoutubeVideoActionButton>
              </Link>
              <Link to="./chapters">
                <YoutubeVideoActionButton
                  className="w-full"
                  complete={!!chapters?.length}
                  icon={ImStack}
                >
                  Chapters
                </YoutubeVideoActionButton>
              </Link>
              <Link to="./tags">
                <YoutubeVideoActionButton className="w-full" complete={!!tags?.length} icon={FiTag}>
                  Tags
                </YoutubeVideoActionButton>
              </Link>
              <Link to="./tags">
                <YoutubeVideoActionButton
                  className="w-full"
                  complete={!!hashtags?.length}
                  icon={RiHashtag}
                >
                  Hashtags
                </YoutubeVideoActionButton>
              </Link>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

const YoutubeOverviewSection = ({
  title,
  content,
  children,
}: {
  title: string;
  content?: string | string[] | YoutubeVideoChapter[];
  children: React.ReactNode;
}) => {
  if ((Array.isArray(content) && !content.length) || !content) return null;

  const copyContent =
    typeof content === 'string'
      ? content
      : Array.isArray(content) && typeof content[0] === 'string'
        ? content.join(',')
        : formatChaptersForCopy(content as YoutubeVideoChapter[]);

  return (
    <div className="mb-10">
      <div className="mb-2 text-xs font-semibold text-black1">{title}</div>

      {children}

      <CopyButton className="mt-2" content={copyContent} />
    </div>
  );
};
