import type { APIResponseType } from '@/types/resources';
import type { Blog } from '@ps/types';

import { useNavigate } from 'react-router-dom';
import { RiHashtag } from 'react-icons/ri';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';

import { parseQuery } from '@/utils';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';

import { Card } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoContentHeader from '../components/YoutubeVideoContentHeader';
import YoutubeBlogCreateDialog from '../../youtube/components/dialogs/YoutubeBlogCreateDialog';
import useYoutubeVideo from '../utils/useYoutubeVideo';
import BlogCard from '../../blog/components/BlogCard';

export default function YoutubeVideoBlogs() {
  const { limit = '10', page = '1' } = parseQuery();
  const [isBlogCreate, toggleBlogCreate] = useToggle(false);
  const navigate = useNavigate();

  const { video } = useYoutubeVideo();

  const { data: { data: blogs, total } = emptyAPIResponse, refetch } = useQuery<
    APIResponseType<Blog>
  >([`blogs?identifier=${video.id}`, { page }], {
    enabled: !!video.id,
  });

  const onBlogCreateDialogClose = () => {
    toggleBlogCreate();
    refetch();
  };

  return (
    <>
      {!blogs.length && (
        <Card>
          <YoutubeVideoContentHeader
            title="Blogs"
            primaryAction={{
              onClick: toggleBlogCreate,
              children: 'Create Blog',
              icon: RiHashtag,
            }}
          />
        </Card>
      )}

      {!!blogs.length && (
        <div className="bg-white px-6">
          {blogs.map((blog) => (
            <BlogCard key={blog._id} blog={blog} refetch={refetch} />
          ))}
          {total > blogs.length && (
            <Pagination
              className="mt-6"
              onPaging={(url) => navigate(url)}
              limit={parseInt(limit, 10)}
              page={parseInt(page, 10)}
              total={total}
            />
          )}
        </div>
      )}

      {video.id && (
        <YoutubeBlogCreateDialog
          video={video}
          isBlogCreate={isBlogCreate}
          toggleBlogCreate={onBlogCreateDialogClose}
        />
      )}
    </>
  );
}
