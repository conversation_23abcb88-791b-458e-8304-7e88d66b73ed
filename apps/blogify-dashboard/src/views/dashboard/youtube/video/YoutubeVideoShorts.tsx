import type { YoutubeVideoContent } from '@/types/resources/youtube-contents.type';
import type { YoutubeShort } from '@/types/resources/youtube-contents.type';

import { useEffect, useState } from 'react';
import { IoMdPlayCircle } from 'react-icons/io';
import { useMutation } from 'react-query';
import { useToggle } from 'react-use';
import { GoClock } from 'react-icons/go';
import { FaImage } from 'react-icons/fa';
import { ImStack } from 'react-icons/im';

import { DialogContent, DialogTrigger, Dialog } from '@ps/ui/components/dialog';
import { timeStringToSeconds, formatSeconds } from '@/utils/time';
import { PageContainerMini } from '@/components/layout/PageContainer';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { CONTENT_COUNT_MAX_PER_TYPE } from '../utils/constants';
import { CopyButton, TextBox, Card } from '../components/YoutubeVideoContentLayouts';
import YoutubeLanguageSelectDialog from '../components/dialogs/YoutubeLanguageSelectDialog';
import YoutubeVideoClipperDialog from '../components/dialogs/YoutubeVideoClipperDialog';
import YoutubeVideoContentHeader from '../components/YoutubeVideoContentHeader';
import useYoutubeContent from '../utils/useYoutubeContent';
import YoutubeVideoEmbed from '../components/YoutubeVideoEmbed';
import useYoutubeVideo from '../utils/useYoutubeVideo';

const YoutubeVideoShorts = () => {
  const { contents: shorts, refetchContents } = useYoutubeContent<YoutubeShort>('short');
  const { video } = useYoutubeVideo();

  return (
    <>
      <Card>
        {!shorts.length && video.data?.generateTypesInProgress.includes('short') ? (
          <PageContainerMini className="pb-10">
            <div className="mb-2 font-semibold">Create YouTube Shorts</div>
            <div className="mb-6 text-sm">
              You can create YouTube shorts by selecting a video chapter / cropping a video portion
              or let Blogify AI to automatically suggest shorts for you.
            </div>
            <div className="mb-6 text-sm">Generating shorts from your YouTube video...</div>
            <div className="flex flex-center">
              <Loader />
            </div>
          </PageContainerMini>
        ) : (
          <YoutubeVideoShortsCreate shorts={shorts} refetchShorts={refetchContents} />
        )}

        {!!shorts?.length && (
          <div className="mb-2 mt-10 flex flex-wrap gap-6">
            {video.data?.generateTypesInProgress.includes('short') && (
              <div className="w-full lg:w-[calc(50%-12px)] xl:w-[calc(33.333%-16px)]">
                <YoutubeShortsCard refetchShorts={refetchContents} />
              </div>
            )}
            {shorts.map((short, i) => (
              <div key={i} className="w-full lg:w-[calc(50%-12px)] xl:w-[calc(33.333%-16px)]">
                <YoutubeShortsCard content={short} refetchShorts={refetchContents} />
              </div>
            ))}
          </div>
        )}
      </Card>
    </>
  );
};

const YoutubeVideoShortsCreate = ({
  shorts,
  refetchShorts,
}: {
  shorts: YoutubeVideoContent<YoutubeShort>[];
  refetchShorts: Func;
}) => {
  const [isLanguageSelectOpen, toggleLanguageSelect] = useToggle(false);
  const { video, detectedLanguage, refetchVideo } = useYoutubeVideo();

  const [isClipperDialog, toggleClipperDialog] = useState(false);
  const [language, setLanguage] = useState('');
  const [mode, setMode] = useState<'full' | 'partial'>();

  const { mutateAsync: generateShorts, isLoading } = useMutation({
    mutationFn: async (clipTimeSpan: string | null) => {
      if (!language) return Promise.reject('Language selection is required');
      const body = { clipTimeSpan, language };
      return API.post(`youtube/${video.id}/contents/shorts`, body).then(() => {
        toggleClipperDialog(false);
        refetchShorts();
        refetchVideo();
      });
    },
  });

  const hasContent =
    (shorts || []).filter((t) => t.status !== 'generation_failed').length >=
    CONTENT_COUNT_MAX_PER_TYPE.short;

  const showLanguageSelect = (_mode: 'full' | 'partial') => {
    setMode(_mode);
    toggleLanguageSelect();
  };

  useEffect(() => {
    if (video.data?.language) {
      setLanguage(video.data?.language);
    }
  }, [video.data?.language]);

  return (
    <>
      {!shorts.length ? (
        <PageContainerMini className="pb-10">
          <div className="mb-2 font-semibold">Create YouTube Shorts</div>
          <div className="mb-6 text-sm">
            You can create YouTube shorts by selecting a video chapter / cropping a video portion or
            let Blogify AI to automatically suggest shorts for you.
          </div>

          <YoutubeVideoEmbed video={video} />

          <div className="mt-4 flex flex-wrap gap-4 md:flex-nowrap">
            <Button
              onClick={() => (language ? toggleClipperDialog(true) : showLanguageSelect('partial'))}
              className="w-full xl:w-1/2"
              variant="secondary"
            >
              <ImStack />
              <div className="ml-2">Create Shorts from Video Chapters</div>
            </Button>
            <Button
              onClick={() => (language ? generateShorts(null) : showLanguageSelect('full'))}
              className="w-full xl:w-1/2"
              variant="secondary"
            >
              <img className="size-[14px]" src="/images/starburst.svg" />
              <div className="ml-2">Create Shorts using Blogify AI</div>
            </Button>
          </div>

          {video?.id && (
            <YoutubeLanguageSelectDialog
              onConfirm={(lang) => {
                setLanguage(lang);
                return mode === 'full' ? generateShorts(null) : toggleClipperDialog(true);
              }}
              detectedLanguage={detectedLanguage}
              onClose={toggleLanguageSelect}
              isOpen={isLanguageSelectOpen}
            />
          )}
        </PageContainerMini>
      ) : (
        <YoutubeVideoContentHeader
          title="YouTube Shorts"
          description="Create an AI suggested Short from your YouTube Video or clip one from the chapters. Generating shorts cost one credit per video."
          hasContent={hasContent}
          primaryAction={{
            onClick: () => toggleClipperDialog(true),
            children: 'Create a YouTube Short',
            icon: FaImage,
          }}
        />
      )}

      {video?.id && (
        <YoutubeVideoClipperDialog
          video={video}
          title="Create Shorts from Video Chapters"
          description="Select a chapter below or select a portion of your video that you want to use to create a short"
          saving={isLoading}
          open={isClipperDialog}
          onOpenChange={toggleClipperDialog}
          onSubmit={(ct) => generateShorts(ct)}
        />
      )}
    </>
  );
};

type Props = {
  content: YoutubeVideoContent<YoutubeShort>;
  refetchShorts: Func;
};

const YoutubeShortsCard = ({
  content,
  refetchShorts,
}: {
  content?: YoutubeVideoContent<YoutubeShort>;
  refetchShorts: Func;
}) => {
  const short = content?.contentData;
  const copyContent = `${short?.title}\n\n${short?.description}`;

  return (
    <div className="flex h-full flex-col justify-between rounded border border-gray5 text-black1">
      <div>
        {content ? (
          <YoutubeShortsThumbnail content={content} refetchShorts={refetchShorts} />
        ) : (
          <div className="flex flex-center">
            <Loader />
          </div>
        )}
        <div className="p-4">
          <div className="font-semibold">{short?.title || 'Generating Shorts...'}</div>
          {short && (
            <>
              <div className="mt-1 flex items-center gap-2">
                <GoClock />
                <div className="text-sm leading-normal">
                  {formatSeconds(
                    timeStringToSeconds(short.endTime) - timeStringToSeconds(short.startTime)
                  )}
                </div>
              </div>
              <div className="mb-1 mt-4 text-xs text-gray2">DESCRIPTION</div>
              <div className="text-sm">{short.description}</div>
            </>
          )}
        </div>
      </div>

      {content && (
        <div className="flex flex-col gap-2 p-4 pt-0">
          <Button className="w-full" variant="secondary" onClick={() => copy(copyContent)}>
            Copy Title & Description
          </Button>
          <YoutubeShortsActions content={content} refetchShorts={refetchShorts} />
        </div>
      )}
    </div>
  );
};

const isPreparingShort = (yts: YoutubeVideoContent<YoutubeShort>): boolean =>
  yts.status === 'generating' && !yts.contentData?.downloadUrl && !!yts.contentData?.title;

const YoutubeShortsActions = ({ content, refetchShorts }: Props) => {
  const { video } = useYoutubeVideo();

  const { mutateAsync: prepareVideo, isLoading } = useMutation({
    mutationFn: () =>
      API.put(`youtube/${video.id}/contents/shorts/${content._id}`, {}).then(() => refetchShorts()),
  });

  const short = content.contentData;

  return (
    <>
      {short.downloadUrl ? (
        <Link
          download={`${short.title.toLowerCase().replace(/ /g, '-')}.mp4`}
          to={short.downloadUrl}
          noEncode
        >
          <Button className="w-full">Download Short</Button>
        </Link>
      ) : (
        <Button
          className="w-full"
          variant="secondary"
          loading={isPreparingShort(content) || isLoading}
          onClick={() => prepareVideo()}
        >
          Prepare Video File
        </Button>
      )}
    </>
  );
};

const YoutubeShortsThumbnail = ({ content, refetchShorts }: Props) => {
  const { video } = useYoutubeVideo();
  const short = content.contentData;

  return (
    <div className="relative cursor-pointer">
      <YoutubeVideoEmbed
        start={timeStringToSeconds(short.startTime)}
        id="shorts-thumbnail"
        controls={false}
        video={video}
        pauseOnStart
        autoplay
      />
      <Dialog>
        <DialogTrigger asChild>
          <div>
            <div className="absolute left-0 top-0 size-full bg-[#00000019] text-white flex-center">
              <IoMdPlayCircle size={48} />
            </div>
          </div>
        </DialogTrigger>
        <DialogContent className="max-w-[830px] p-0">
          <div className="flex flex-wrap">
            <div className="w-full lg:w-1/2">
              <YoutubeVideoEmbed
                start={timeStringToSeconds(short.startTime)}
                end={timeStringToSeconds(short.endTime)}
                controls={false}
                video={video}
                autoplay
                isShort
                loop
              />
            </div>

            <div className="flex min-h-full w-full flex-col justify-between p-5 sm:p-10 md:w-1/2">
              <div>
                <YoutubeShortsTextBox title="Title" content={short.title} />
                <YoutubeShortsTextBox title="Description" content={short.description} />
              </div>

              <YoutubeShortsActions content={content} refetchShorts={refetchShorts} />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const YoutubeShortsTextBox = ({ title, content }: { title: string; content: string }) => (
  <div className="mt-6 rounded border border-gray5 bg-gray7">
    <TextBox>
      <div className="flex items-center gap-4">
        <div className="text-sm font-semibold text-black1">{title}</div>
      </div>

      <CopyButton content={content} />
    </TextBox>

    <div className={cn('p-4 text-sm', { 'font-semibold': title === 'Title' })}>{content}</div>
  </div>
);

export default YoutubeVideoShorts;
