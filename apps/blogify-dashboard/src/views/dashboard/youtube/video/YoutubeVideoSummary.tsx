import { MdOutlineSummarize } from 'react-icons/md';

import { CopyButton, TextBox } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoSummary() {
  const {
    videoContents: { summary },
  } = useYoutubeVideo();

  const __html = (summary || '').replace(/\n/g, '<br/>');

  return (
    <YoutubeVideoContentContainer
      icon={MdOutlineSummarize}
      contentType="summary"
      contents={summary}
      title="Summary"
    >
      <div className="rounded border border-gray5 bg-gray7">
        <TextBox>
          <div className="flex items-center gap-4">
            <span className="text-sm font-semibold text-black1">Summary</span>
          </div>
          <CopyButton content={summary || ''} />
        </TextBox>
        <div className="p-4">
          <span className="text-sm text-black1" dangerouslySetInnerHTML={{ __html }} />
        </div>
      </div>
    </YoutubeVideoContentContainer>
  );
}
