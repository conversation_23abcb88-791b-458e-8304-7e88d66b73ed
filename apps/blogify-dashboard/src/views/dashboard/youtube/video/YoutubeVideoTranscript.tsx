import type { YoutubeVideoTranscript as YoutubeVideoTranscriptType } from '@/types/resources/youtube-contents.type';

import { RiListUnordered } from 'react-icons/ri';
import { useToggle } from 'react-use';

import { timeFractionToSeconds, formatSeconds } from '@/utils/time';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';

import { CopyButtonWithIcon } from '../components/YoutubeVideoContentLayouts';
import YoutubeVideoContentContainer from '../components/YoutubeVideoContentContainer';
import useYoutubeVideo from '../utils/useYoutubeVideo';

export default function YoutubeVideoTranscript() {
  const [isShowAll, toggleShowAll] = useToggle(false);
  const {
    videoContents: { transcript },
  } = useYoutubeVideo();

  const endTime = timeFractionToSeconds(transcript?.[transcript?.length - 1]?.endTime || 0);

  const getTimeRange = (t: YoutubeVideoTranscriptType) =>
    `${formatSeconds(timeFractionToSeconds(t.startTime), endTime)} - ${formatSeconds(
      timeFractionToSeconds(t.endTime),
      endTime
    )}`;

  const copyTranscript = () =>
    copy(transcript?.map((t) => `${getTimeRange(t)}: ${t.text}`).join('\n') || '');

  return (
    <YoutubeVideoContentContainer
      contentType="transcript"
      icon={RiListUnordered}
      contents={transcript}
      title="Transcript"
    >
      <div className="rounded border border-gray5 text-sm text-black1">
        {(isShowAll ? transcript : transcript?.slice(0, 50))?.map((t, index) => (
          <div
            className="flex flex-wrap border-b border-gray5 flex-center last:border-b-0 md:flex-nowrap"
            key={index}
          >
            <div className="min-h-10 min-w-[90px] px-4 py-3 sm:min-w-[156px]">
              <span className="whitespace-nowrap text-center">{getTimeRange(t)}</span>
            </div>

            <div className="min-h-10 w-full border-l-0 border-t border-gray5 px-4 py-3 sm:border-l sm:border-t-0">
              <span className="text-center md:text-left">{t.text}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-2 flex gap-6">
        <Button className="p-0" variant="ghost" onClick={toggleShowAll}>
          <span className="text-sm font-semibold text-gray2">
            {isShowAll ? 'Show Less' : 'Show Full Transcript'}
          </span>
        </Button>

        <CopyButtonWithIcon onClick={copyTranscript}>Copy Transcript</CopyButtonWithIcon>
      </div>
    </YoutubeVideoContentContainer>
  );
}
