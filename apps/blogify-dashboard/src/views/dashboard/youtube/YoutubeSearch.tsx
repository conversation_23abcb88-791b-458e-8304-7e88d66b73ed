import type { YoutubeResponse, YoutubeVideo } from '@/types/integrations/youtube.type';

import { useInfiniteQuery } from 'react-query';
import { useEffect } from 'react';

import YoutubeVideoList from './components/YoutubeVideoList';
import useYoutube from './utils/useYoutube';

const YoutubeSearch = () => {
  const { search } = useYoutube();

  const url = `youtube/search/${search}`;
  const { data, isFetchingNextPage, isFetching, isLoading, isError, fetchNextPage } =
    useInfiniteQuery<YoutubeResponse<YoutubeVideo>>(url, {
      enabled: !!search,
      retry: 0,
      getNextPageParam: (lastPage) => ({ pageToken: lastPage.nextPageToken }),
    });

  const videos = (data?.pages || []).reduce((arr, d) => {
    arr.push(...d.items);
    return arr;
  }, [] as YoutubeVideo[]);

  useEffect(() => {
    const onScroll = () => {
      if (window.innerHeight + Math.round(window.scrollY) >= document.body.offsetHeight) {
        fetchNextPage();
      }
    };
    document.addEventListener('scroll', onScroll);
    return () => document.removeEventListener('scroll', onScroll);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <YoutubeVideoList
      videos={videos}
      isFetchingNextPage={isFetchingNextPage}
      isFetching={isFetching}
      isLoading={isLoading}
      isError={isError}
    />
  );
};

export default YoutubeSearch;
