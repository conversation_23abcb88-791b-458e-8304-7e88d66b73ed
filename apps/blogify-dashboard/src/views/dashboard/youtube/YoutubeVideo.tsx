import type { Breadcrumb } from '@/types/components/layout';

import { useLocation, useNavigate, useParams, Outlet } from 'react-router-dom';
import { Dispatch, useEffect, useState } from 'react';
import { MdArrowBack } from 'react-icons/md';

import { DashboardContainer } from '@/views/dashboard/layout';
import { Button } from '@ps/ui/components/button';

import YoutubeVideoProvider from './utils/YoutubeVideoProvider';
import YoutubeVideoCard from './components/YoutubeVideoCard';
import YoutubeVideoNav from './components/YoutubeVideoNav';
import useYoutubeVideo from './utils/useYoutubeVideo';
import YoutubeProvider from './utils/YoutubeProvider';

const initialBreadcrumb: Breadcrumb[] = [
  { href: '/dashboard', title: 'Dashboard' },
  { href: '/dashboard/youtube', title: 'Youtube Channel' },
];

const YoutubeVideo = () => {
  const [breadcrumb, setBreadcrumb] = useState<Breadcrumb[]>(initialBreadcrumb);
  const [lastPage, setLastPage] = useState();
  const { videoId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.state?.referrer && !location.state?.referrer?.includes(videoId)) {
      setLastPage(location.state?.referrer);
    }
  }, [location.state?.referrer, videoId]);

  return (
    <YoutubeProvider>
      <YoutubeVideoProvider>
        <DashboardContainer
          title="Video Details"
          breadcrumb={breadcrumb}
          primaryAction={
            <Button variant="secondary" onClick={() => navigate(lastPage || '/dashboard/youtube')}>
              <MdArrowBack />
              <div className="ml-2 hidden md:block">Back to Video List</div>
            </Button>
          }
        >
          <YoutubeVideoHeader breadcrumb={initialBreadcrumb} setBreadcrumb={setBreadcrumb} />
          <Outlet />
        </DashboardContainer>
      </YoutubeVideoProvider>
    </YoutubeProvider>
  );
};

const YoutubeVideoHeader = ({
  breadcrumb,
  setBreadcrumb,
}: {
  breadcrumb: Breadcrumb[];
  setBreadcrumb: Dispatch<Breadcrumb[]>;
}) => {
  const { video } = useYoutubeVideo();

  return (
    <>
      <YoutubeVideoCard video={video} />
      <YoutubeVideoNav breadcrumb={breadcrumb} setBreadcrumb={setBreadcrumb} />
    </>
  );
};

export default YoutubeVideo;
