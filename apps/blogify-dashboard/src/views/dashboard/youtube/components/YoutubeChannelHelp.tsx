import { MdOutlineSummarize, MdLineStyle } from 'react-icons/md';
import { PiPencilSimpleLineFill } from 'react-icons/pi';
import { FaCaretDown, FaCaretUp } from 'react-icons/fa';
import { RiShareLine, Ri<PERSON>ashtag } from 'react-icons/ri';
import { SiYoutubeshorts } from 'react-icons/si';
import { PiTextTBold } from 'react-icons/pi';
import { useToggle } from 'react-use';
import { useState } from 'react';
import { BsStars } from 'react-icons/bs';
import { FaImage } from 'react-icons/fa';
import { ImStack } from 'react-icons/im';

import { cacheSet, cacheGet } from '@/utils/localStorageCache';
import { useStoreState } from '@/store';
import { cn } from '@ps/ui/lib/utils';
import Theme from '@/styles/theme';
import Link from '@/components/common/Link';

const HELP = [
  {
    Icon: PiPencilSimpleLineFill,
    title: 'Blogs from Video',
    description:
      'Click generate on your uploaded videos, Playlist videos or any video on YouTube to create a blog from it.',
    proRequired: false,
  },
  {
    Icon: SiYoutubeshorts,
    title: 'YouTube Shorts',
    description: 'Click generate on your uploaded videos on YouTube to generate a shorts from it.',
    proRequired: true,
  },
  {
    Icon: FaImage,
    title: 'Thumbnails',
    description:
      'Click generate on your uploaded videos on YouTube to generate a thumbnail for it.',
    proRequired: true,
  },
  {
    Icon: PiTextTBold,
    title: 'Video Title',
    description: 'Click generate on your uploaded videos on YouTube to create a title for it.',
    proRequired: true,
  },
  {
    Icon: MdLineStyle,
    title: 'Video Description',
    description:
      'Click generate on your uploaded videos on YouTube to create a description for it.',
    proRequired: true,
  },
  {
    Icon: ImStack,
    title: 'Video Chapters',
    description: 'Click generate on your uploaded videos on YouTube to generate chapters for it.',
    proRequired: true,
  },
  {
    Icon: RiHashtag,
    title: 'Tags & Hashtags',
    description:
      'Click generate on your uploaded videos on YouTube to create a tags & hashtags for it.',
    proRequired: true,
  },
  {
    Icon: MdOutlineSummarize,
    title: 'Video Summary',
    description: 'Click generate on your uploaded videos on YouTube to summarize it.',
    proRequired: true,
  },
  {
    Icon: RiShareLine,
    title: 'Social Media Post',
    description:
      'Click generate on your uploaded videos on YouTube to create a social media post for it.',
    proRequired: false,
  },
];

const YoutubeChannelHelp = () => {
  const [selectedHelp, selectHelp] = useState(HELP[0]);
  const [isExpanded, toggleExpansion] = useToggle(
    cacheGet('youtubeHelpExpanded') === undefined ? true : cacheGet('youtubeHelpExpanded')
  );

  const user = useStoreState((s) => s.user.current);

  const onToggleExpansion = () => {
    cacheSet('youtubeHelpExpanded', !isExpanded);
    toggleExpansion();
  };

  return (
    <div className="bg-gray7">
      <button className="w-full" onClick={onToggleExpansion}>
        <div className="flex h-8 w-full items-center justify-end gap-2 px-5 text-gray2">
          <BsStars />
          <div className="text-xs">Things you can do with YouTube Connect</div>
          {isExpanded ? <FaCaretUp /> : <FaCaretDown />}
        </div>
      </button>

      {isExpanded && (
        <div className="px-6 py-4">
          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="h-scroll flex justify-between gap-2 sm:gap-5">
            {HELP.map((h, i) => (
              <button key={i} onClick={() => selectHelp(h)}>
                <HelpCard isSelected={h.title === selectedHelp.title} {...h} />
              </button>
            ))}
          </div>
          <div className="-mt-px mb-3 h-px w-full bg-gray6" />

          <div className="text-center text-xs text-gray2 sm:text-sm">
            {selectedHelp.proRequired && !user.hasYouTubeProAddon ? (
              <>
                Upgrade to{' '}
                <Link
                  to="/dashboard/addons?addonId=656466197b9a567255f8edd9"
                  className="underline hover:text-primary"
                  color="blue"
                >
                  YouTube Connect Pro
                </Link>{' '}
                to start creating details for your YouTube video.
              </>
            ) : (
              <>{selectedHelp.description}</>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const HelpCard = ({
  Icon,
  title,
  isSelected,
  proRequired,
}: (typeof HELP)[0] & { isSelected: boolean }) => {
  const user = useStoreState((s) => s.user.current);
  const upgradeRequired = proRequired && !user.hasYouTubeProAddon;

  return (
    <div className="flex flex-col overflow-hidden flex-center lg:w-[108px]">
      <div className={cn('flex flex-col flex-center', { 'opacity-30': upgradeRequired })}>
        <div
          className={cn('flex size-[29px] rounded-lg flex-center sm:size-[40px]', {
            'bg-gray2': isSelected,
            'bg-gray6': !isSelected,
          })}
        >
          <Icon size={18} color={Theme.colors[isSelected ? 'white' : 'gray2']} />
        </div>
        <div className="mb-1 mt-2 hidden text-center text-xs leading-snug text-gray2 md:block">
          Create
          <br />
          <span className="whitespace-nowrap">{title}</span>
        </div>
      </div>
      {isSelected ? (
        <div
          className={`relative flex h-5 justify-center before:absolute before:bottom-0 before:block before:border-[10px] before:border-transparent before:border-b-gray6 after:absolute after:bottom-[-1.5px] after:block after:border-[10px] after:border-transparent after:border-b-gray7`}
        />
      ) : (
        <div className="pb-5"></div>
      )}
    </div>
  );
};

export default YoutubeChannelHelp;
