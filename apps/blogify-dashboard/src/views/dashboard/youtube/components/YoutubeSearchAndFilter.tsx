import { useNavigate, NavLink, useParams } from 'react-router-dom';
import { MdOutlineAccessTime } from 'react-icons/md';
import { FiSearch } from 'react-icons/fi';

import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { cn } from '@ps/ui/lib/utils';
import Theme from '@/styles/theme';

// TODO: Move to /src/components
import { FilterButton, SearchInput } from '../../writing-snippets/styles/index.styled';
import useYoutube from '../utils/useYoutube';

const YoutubeSearchAndFilter = () => {
  const { search, onSearch } = useYoutube();
  const { playlistId } = useParams();
  const navigate = useNavigate();

  const filterOptions = [
    { name: 'Uploads', path: '' },
    { name: 'Playlists', path: `/playlists/${playlistId || ''}` },
    // { name: 'Podcasts', path: '/podcasts' },
    // { name: 'Live', path: '/live' },
    // { name: 'Shorts', path: '/shorts' },
  ];

  return (
    <div className="pt-0.5">
      <Card className="px-4 py-3">
        <div
          id="youtubeSearch"
          className="flex items-center gap-1 overflow-hidden rounded border border-gray5 bg-gray7"
        >
          <Button className="w-9 !rounded-none border-r border-gray5" variant="secondary">
            <div>
              <FiSearch size={16} color={Theme.colors.gray2} />
            </div>
          </Button>
          <SearchInput
            placeholder="Search for any YouTube video"
            defaultValue={search}
            onChange={(ev) => onSearch(ev.target.value)}
            onFocus={() => {
              if (search) {
                return navigate(`/dashboard/youtube/search/${search}`);
              }
            }}
          />
        </div>
      </Card>

      <Card
        id="youtubeFilter"
        // eslint-disable-next-line tailwindcss/no-custom-classname
        className="h-scroll mt-0.5 w-full !flex-row justify-start gap-4 px-4 py-3 sm:justify-between"
      >
        <div className="contents sm:flex">
          {filterOptions.map((item, index) => (
            <NavLink to={`/dashboard/youtube${item.path}`} key={index} end>
              {({ isActive }) => (
                <FilterButton className="mr-0 md:mr-3" variant={isActive ? 'primary' : ''}>
                  {item.name}
                </FilterButton>
              )}
            </NavLink>
          ))}
        </div>

        <NavLink to={`/dashboard/youtube/recent`} end>
          {({ isActive }) => (
            <FilterButton
              className={cn('hover:!text-white', isActive ? '' : '!text-primary')}
              variant={isActive ? 'primary' : ''}
            >
              <div className="flex items-center gap-2 font-semibold">
                <MdOutlineAccessTime />
                RECENT CONTENTS
              </div>
            </FilterButton>
          )}
        </NavLink>
      </Card>
    </div>
  );
};

export default YoutubeSearchAndFilter;
