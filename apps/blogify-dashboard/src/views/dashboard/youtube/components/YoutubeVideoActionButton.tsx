import type { ButtonProps } from '@ps/ui/components/button';
import type { IconType } from 'react-icons';

import { MdDiamond } from 'react-icons/md';

import { Button } from '@ps/ui/components/button';
import Chip from '@/components/layout/Chip';

const YoutubeVideoActionButton = ({
  icon: Icon,
  complete,
  children,
  showPro,
  ...props
}: { icon: IconType; complete?: boolean; showPro?: boolean } & ButtonProps) => (
  <Button
    className={`justify-start opacity-100 ${complete ? 'bg-white shadow-none' : ''}`}
    variant="secondary"
    disabled={showPro}
    {...props}
  >
    <div className="flex w-full items-center justify-between">
      <div className="flex items-center gap-2">
        {Icon && <Icon className="text-black1" size={12} />}
        <div className="text-sm font-normal text-black1">{children}</div>
      </div>
      {complete && <img className="size-3" src="/images/icons/icon-success.svg" />}
      {showPro && (
        <Chip className="!bg-gray2 !px-1 !py-0 !text-xs">
          <div className="flex gap-3 flex-center">
            <MdDiamond />
            PRO
          </div>
        </Chip>
      )}
    </div>
  </Button>
);

export default YoutubeVideoActionButton;
