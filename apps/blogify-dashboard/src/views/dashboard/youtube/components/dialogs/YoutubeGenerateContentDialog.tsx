import type { YoutubeContentTypes } from '@/types/resources/youtube-contents.type';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { RiHashtag, RiListUnordered, RiShareLine } from 'react-icons/ri';
import { MdOutlineSummarize, MdLineStyle } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { PiTextTBold } from 'react-icons/pi';
import { GoClock } from 'react-icons/go';
import { FaImage } from 'react-icons/fa';
import { ImStack } from 'react-icons/im';
import { FiTag } from 'react-icons/fi';

import {
  DialogDescription,
  DialogContent,
  DialogClose,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { BLOG_LANGUAGES } from '@/views/dashboard/blog/create/utils/languages';
import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import useForm from '@/hooks/useForm';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { useYoutubeContentGenerate, allGenerateTypes } from '../../utils/useYoutubeMutation';
import { isOwnChannelVideo, getThumbnail } from '../../utils';
import YoutubeVideoLanguageSelector from '../../components/YoutubeVideoLanguageSelector';
import YoutubeVideoActionButton from '../YoutubeVideoActionButton';
import useYoutube from '../../utils/useYoutube';
import YoutubeBadge from '../YoutubeBadge';

const CONTENT_PAGES: Record<YoutubeContentTypes, string> = {
  title: 'title',
  description: 'description',
  chapters: 'chapters',
  tag: 'tags',
  hashtag: 'tags',

  'social-post': 'social-post',
  thumbnail: 'thumbnails',
  short: 'shorts',
  social_post_facebook: 'posts',
  social_post_twitter: 'posts',
  social_post_linkedin: 'posts',

  summary: 'summary',
  transcript: 'transcript',
};

export default function YoutubeGenerateContentDialog({
  toggleThumbnailCreate,
  toggleBlogFromChapter,
  toggleSocialPost,
  toggleBlogCreate,
  video,
  open,
  onOpenChange,
}: {
  toggleThumbnailCreate: Func;
  toggleBlogFromChapter: Func;
  toggleSocialPost: Func;
  toggleBlogCreate: Func;
  video: YoutubeVideo;
  open: boolean;
  onOpenChange: (_: boolean) => void;
}) {
  const navigate = useNavigate();
  const { generateContent, isGenerating } = useYoutubeContentGenerate(video);

  const language =
    video.data?.language ||
    BLOG_LANGUAGES.find((l) => l.iso && l.iso === video.snippet?.defaultAudioLanguage)?.value ||
    'english';

  const createContents = (generateType?: YoutubeContentTypes) => {
    const onSuccess = () => {
      const suffix = generateType ? `/${CONTENT_PAGES[generateType]}` : '';
      if (onOpenChange) onOpenChange(false);
      return navigate(`/dashboard/youtube/videos/${video.id}${suffix}`);
    };
    if (generateType === 'short') {
      API.post(`youtube/${video.id}/contents/shorts`, {
        language: form.values.language || language,
      }).then(onSuccess);
    } else {
      generateContent({
        generateTypes: generateType ? [generateType] : [],
        language: form.values.language || language,
      }).then(onSuccess);
    }
  };

  const initialValues = { language };
  const form = useForm({ initialValues, submit: () => {} });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-3xl !p-0">
        <div className="flex flex-col text-black1 md:flex-row">
          <div className="w-full bg-gray7 p-5 md:p-10 lg:w-2/5">
            <div className="flex h-full flex-col justify-between md:min-h-[640px]">
              <div>
                <VideoInfoCard video={video} />
                {!isGenerating && (
                  <div className="hidden md:block">
                    <div className="my-5 h-px w-full bg-gray6" />
                    <YoutubeVideoLanguageSelector f={form} />
                  </div>
                )}
              </div>

              <div className="hidden w-full md:block">
                <DialogClose asChild>
                  <Button className="w-full">Close</Button>
                </DialogClose>
              </div>
            </div>
          </div>

          {isGenerating ? (
            <div className="flex w-full flex-col flex-center md:w-3/5">
              <Loader />
              <div className="mt-[-60px] text-xl font-semibold text-black1">
                Creating contents for your youtube video...
              </div>
            </div>
          ) : (
            <div className="w-full p-5 md:p-10 lg:w-3/5">
              <DialogTitle asChild>
                <>
                  <div className="mb-2 text-lg font-semibold">Create Contents</div>
                  <div className="text-sm">Create details for your video or contents from it.</div>
                </>
              </DialogTitle>

              <div className="md:hidden">
                <YoutubeVideoLanguageSelector className="mt-4" f={form} />
              </div>

              <DialogDescription asChild>
                <>
                  <YoutubeCreateContentSection
                    createContents={createContents}
                    onOpenChange={onOpenChange}
                    video={video}
                  />

                  <YoutubeCreateOtherContentSection
                    toggleThumbnailCreate={toggleThumbnailCreate}
                    toggleBlogFromChapter={toggleBlogFromChapter}
                    toggleSocialPost={toggleSocialPost}
                    toggleBlogCreate={toggleBlogCreate}
                    createContents={createContents}
                    onOpenChange={onOpenChange}
                    video={video}
                  />
                </>
              </DialogDescription>

              <div className="md:hidden">
                <DialogClose className="" asChild>
                  <Button className="mt-5 w-full">Close</Button>
                </DialogClose>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

const VideoInfoCard = ({ video }: { video: YoutubeVideo }) => (
  <div className="flex flex-row gap-2 md:flex-col">
    <img
      className="w-[45%] min-w-32 rounded object-cover sm:w-2/5 md:w-full"
      src={getThumbnail(video)}
      alt={video.snippet.title}
    />
    <div className="w-[45%] sm:w-3/5 md:w-full">
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <div className="ellipsis r3 mb-2 text-xs font-semibold sm:text-sm">{video.snippet.title}</div>
      <YoutubeBadge checkMark>{video.snippet.channelTitle}</YoutubeBadge>
    </div>
  </div>
);

const YoutubeCreateContentSection = ({
  createContents,
  onOpenChange,
  video,
}: {
  createContents: (_?: YoutubeContentTypes) => void;

  onOpenChange?: (_: boolean) => void;
  video: YoutubeVideo;
}) => {
  const { channel } = useYoutube();
  const navigate = useNavigate();
  const user = useStoreState((u) => u.user.current);

  const redirectTo = (t: YoutubeContentTypes) => {
    if (onOpenChange) onOpenChange(false);
    return navigate(`/dashboard/youtube/videos/${video.id}/${CONTENT_PAGES[t]}`);
  };

  const hasContent = (t: YoutubeContentTypes) => video.data?.generateTypes.includes(t);
  const generateContentOrRedirect = (t: YoutubeContentTypes) =>
    hasContent(t) ? redirectTo(t) : createContents(t);

  return isOwnChannelVideo(channel, video) ? (
    <div className="mt-8">
      <div className="mb-3 text-xs font-semibold text-gray2">CREATE VIDEO DETAILS</div>

      <div className="grid grid-cols-1 gap-x-3 gap-y-4 md:grid-cols-2">
        <YoutubeVideoActionButton
          onClick={() => generateContentOrRedirect('title')}
          showPro={!user.hasYouTubeProAddon}
          complete={hasContent('title')}
          icon={PiTextTBold}
        >
          Titles
        </YoutubeVideoActionButton>
        <YoutubeVideoActionButton
          onClick={() => generateContentOrRedirect('description')}
          complete={hasContent('description')}
          showPro={!user.hasYouTubeProAddon}
          icon={MdLineStyle}
        >
          Descriptions
        </YoutubeVideoActionButton>
        <YoutubeVideoActionButton
          onClick={() => generateContentOrRedirect('chapters')}
          showPro={!user.hasYouTubeProAddon}
          complete={hasContent('chapters')}
          icon={ImStack}
        >
          Chapters
        </YoutubeVideoActionButton>
        <YoutubeVideoActionButton
          onClick={() => generateContentOrRedirect('tag')}
          showPro={!user.hasYouTubeProAddon}
          complete={hasContent('tag')}
          icon={FiTag}
        >
          Tags
        </YoutubeVideoActionButton>
        <YoutubeVideoActionButton
          onClick={() => generateContentOrRedirect('hashtag')}
          showPro={!user.hasYouTubeProAddon}
          complete={hasContent('hashtag')}
          icon={RiHashtag}
        >
          Hashtags
        </YoutubeVideoActionButton>
      </div>

      {user.hasYouTubeProAddon ? (
        <Button
          className="mt-2 p-0 text-primary"
          disabled={allGenerateTypes.every((gt) => video.data?.generateTypes.includes(gt))}
          onClick={() => createContents()}
          variant="ghost"
        >
          <div className="font-semibold">Create all Video Details</div>
        </Button>
      ) : (
        <div className="mt-2 text-sm text-gray2">
          Upgrade to{' '}
          <Link
            to="/dashboard/addons?addonId=656466197b9a567255f8edd9"
            className="underline hover:text-primary"
            color="blue"
          >
            YouTube Connect Pro
          </Link>{' '}
          to start creating details for your YouTube video.
        </div>
      )}
    </div>
  ) : null;
};

const YoutubeCreateOtherContentSection = ({
  createContents,
  toggleThumbnailCreate,
  toggleBlogFromChapter,
  toggleSocialPost,
  toggleBlogCreate,
  onOpenChange,
  video,
}: {
  createContents: (_: YoutubeContentTypes) => void;
  toggleThumbnailCreate: Func;
  toggleBlogFromChapter: Func;
  toggleSocialPost: Func;
  toggleBlogCreate: Func;
  onOpenChange?: (_: boolean) => void;
  video: YoutubeVideo;
}) => {
  const { channel } = useYoutube();
  const navigate = useNavigate();
  const user = useStoreState((u) => u.user.current);

  const redirectTo = (t: YoutubeContentTypes) => {
    if (onOpenChange) onOpenChange(false);
    return navigate(`/dashboard/youtube/videos/${video.id}/${CONTENT_PAGES[t]}`);
  };

  const hasContent = (t: YoutubeContentTypes) => video.data?.generateTypes.includes(t);
  const isGenerating = (t: YoutubeContentTypes) => video.data?.generateTypesInProgress.includes(t);

  const hasPosts = () =>
    video.data?.generateTypes.some((gt) =>
      ['social_post_facebook', 'social_post_twitter', 'social_post_linkedin'].includes(gt)
    );

  const generateContentOrRedirect = (t: YoutubeContentTypes) =>
    hasContent(t) ? redirectTo(t) : createContents(t);

  return (
    <div className="mt-8">
      <div className="mb-12 text-xs font-semibold text-gray2">CREATE OTHER CONTENTS</div>
      <div className="flex flex-col gap-3">
        <YoutubeVideoActionButton
          onClick={() => {
            toggleBlogCreate();
            if (onOpenChange) onOpenChange(false);
          }}
          icon={RiHashtag}
        >
          Blogs (Autopilot / Co-pilot)
        </YoutubeVideoActionButton>
        <YoutubeVideoActionButton
          onClick={() => {
            toggleBlogFromChapter();
            if (onOpenChange) onOpenChange(false);
          }}
          icon={RiHashtag}
        >
          Blogs from a chapter/section
        </YoutubeVideoActionButton>
        {isOwnChannelVideo(channel, video) && (
          <>
            <YoutubeVideoActionButton
              onClick={() => {
                if (hasContent('thumbnail')) {
                  redirectTo('thumbnail');
                } else {
                  toggleThumbnailCreate();
                  if (onOpenChange) onOpenChange(false);
                }
              }}
              loading={isGenerating('thumbnail')}
              complete={hasContent('thumbnail')}
              showPro={!user.hasYouTubeProAddon}
              icon={FaImage}
            >
              Thumbnail - AI Generated
            </YoutubeVideoActionButton>
            <YoutubeVideoActionButton
              onClick={() => {
                if (hasContent('short')) {
                  redirectTo('short');
                } else {
                  createContents('short');
                  if (onOpenChange) onOpenChange(false);
                }
              }}
              showPro={!user.hasYouTubeProAddon}
              loading={isGenerating('short')}
              complete={hasContent('short')}
              icon={GoClock}
            >
              Create YouTube Shorts
            </YoutubeVideoActionButton>
            <YoutubeVideoActionButton
              onClick={() => generateContentOrRedirect('summary')}
              showPro={!user.hasYouTubeProAddon}
              complete={hasContent('summary')}
              icon={MdOutlineSummarize}
            >
              Summary of this video
            </YoutubeVideoActionButton>
            <YoutubeVideoActionButton
              onClick={() => generateContentOrRedirect('transcript')}
              complete={hasContent('transcript')}
              showPro={!user.hasYouTubeProAddon}
              icon={RiListUnordered}
            >
              Transcription of this video
            </YoutubeVideoActionButton>
          </>
        )}
        <YoutubeVideoActionButton
          onClick={() => {
            if (hasPosts()) {
              redirectTo('social_post_facebook');
            } else {
              toggleSocialPost();
              if (onOpenChange) onOpenChange(false);
            }
          }}
          complete={hasPosts()}
          icon={RiShareLine}
        >
          Social media post
        </YoutubeVideoActionButton>
      </div>
    </div>
  );
};
