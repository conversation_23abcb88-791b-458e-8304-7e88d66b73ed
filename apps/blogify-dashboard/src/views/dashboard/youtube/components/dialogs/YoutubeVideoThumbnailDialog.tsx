import type { YoutubeVideo } from '@/types/integrations/youtube.type';
import type { FormikValues } from 'formik';

import { useEffect, Dispatch, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';

import { DialogContent, DialogTitle, Dialog } from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';
import { Theme } from '@/styles';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';

import { getYoutubeUrlFromId } from '../../utils';

const styles = [
  'Anime',
  'Cyberpunk',
  'Fantasy',
  'Fine Art',
  'Horror',
  'Landscape',
  'Photorealistic',
  'Portraits',
  'Sci-Fi',
  'Surrealism',
];

const YoutubeVideoThumbnailDialog = ({
  video,
  summary,
  isThumbnailCreate,
  toggleThumbnailCreate,
}: {
  video: YoutubeVideo;
  summary?: string;
  isThumbnailCreate: boolean;
  toggleThumbnailCreate: Dispatch<boolean>;
}) => {
  const [isSaving, toggleSaving] = useToggle(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const initialValues = {
    url: getYoutubeUrlFromId(video.id),
    prompt: summary || '',
    style: 'Anime',
  };

  const submit = (values: FormikValues) => {
    toggleSaving();
    setError('');
    API.post(`youtube/${video.id}/contents/thumbnails`, values)
      .then(() => {
        toggleThumbnailCreate(true);
        return navigate(`/dashboard/youtube/videos/${video.id}/thumbnails`);
      })
      .catch((e) => setError(e))
      .finally(toggleSaving);
  };

  const form = useForm({ initialValues, submit });

  useEffect(() => {
    if (summary) {
      form.setFieldValue('prompt', summary);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [summary]);

  return (
    <Dialog onOpenChange={toggleThumbnailCreate} open={isThumbnailCreate}>
      <DialogContent className="w-full max-w-[704px] p-0">
        <div className="p-6 text-black1 md:p-10">
          <DialogTitle asChild>
            <div className="mb-6">
              <div className="mb-2 text-xs font-semibold">Create a Video Thumbnail</div>
              <div className="text-sm">
                Create an AI generated video thumbnail for your video. Generating images will cost
                you credit. 1 credit per image.
              </div>
            </div>
          </DialogTitle>

          <form onSubmit={form.submitForm}>
            {/* @ts-ignore For Now */}
            <FormField
              placeholder="Write what type of image you want to generate."
              label="Type your prompt"
              type="textarea"
              {...form.getInputFields('prompt')}
            />

            <div className="mb-10">
              <div className="mb-2 text-xs font-semibold">Select a style</div>
              <div className="flex flex-wrap gap-2.5">
                {styles.map((s) => {
                  const isSelected = s === form.values.style;
                  return (
                    <div
                      key={s}
                      className="flex w-[calc(25%-8px)] flex-col sm:w-[calc(20%-8px)]"
                      onClick={() => form.getInputFields('style').onChange(s)}
                    >
                      <img
                        className="w-full rounded"
                        style={{
                          boxShadow: isSelected ? `0px 0px 0px 2px ${Theme.colors.blue}` : '',
                        }}
                        src={`/images/image-styles/${s}.png`}
                      />
                      <div
                        className={cn(
                          'mr-2 whitespace-nowrap text-center text-xs font-semibold text-black2 sm:text-sm md:text-md',
                          { '!text-primary': isSelected }
                        )}
                      >
                        {s}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="mt-3">
              {error && <ErrorMessage>{error}</ErrorMessage>}
              <Button type="submit" className="w-full" disabled={isSaving}>
                {isSaving ? 'Generating...' : 'Generate'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default YoutubeVideoThumbnailDialog;
