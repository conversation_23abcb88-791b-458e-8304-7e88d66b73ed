import type { YoutubeVideo } from '@/types/integrations/youtube.type';
import type { FormikValues } from 'formik';

import { useEffect, Dispatch, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToggle } from 'react-use';

import { DialogContent, DialogTitle, Dialog } from '@ps/ui/components/dialog';
import { ToneButton } from '@/views/dashboard/writing-snippets/styles/SnippetsGenerate.styled';
import { Button } from '@ps/ui/components/button';
import { Switch } from '@ps/ui/components/switch';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ErrorMessage from '@/components/form/ErrorMessage';
import useForm from '@/hooks/useForm';

import { YoutubeVideoLanguageInput } from '../YoutubeVideoLanguageSelector';
import { getYoutubeUrlFromId } from '../../utils';
import { SOCIAL_PLATFORMS } from '../../utils/constants';
import useYoutubeVideo from '../../utils/useYoutubeVideo';
import FormField from '@ps/ui/form/FormField';
import { Input } from '@ps/ui/components/input';

const tones = [
  { icon: '🤩', title: 'Engaging' },
  { icon: '🎉', title: 'Promotional' },
  { icon: '💡', title: 'Inspirational' },
  { icon: '💬', title: 'Conversational' },
  { icon: '📜', title: 'Storytelling' },
];

const YoutubeVideoSocialPostsDialog = ({
  video,
  isSocialPost,
  toggleSocialPost,
}: {
  video: YoutubeVideo;
  isSocialPost: boolean;
  toggleSocialPost: Dispatch<boolean>;
}) => {
  const { detectedLanguage } = useYoutubeVideo();
  const navigate = useNavigate();

  const [isSaving, toggleSaving] = useToggle(false);
  const [error, setError] = useState('');

  const selectedLanguage = video.data?.language || detectedLanguage;

  const initialValues = {
    url: '',
    tone: 'Engaging',
    prompt: '',
    platform: 'social_post_facebook',
    language: selectedLanguage,
    includeLink: true,
  };

  const submit = (values: FormikValues) => {
    toggleSaving();
    setError('');
    API.post(`youtube/${video.id}/contents/posts`, values)
      .then(() => {
        toggleSocialPost(true);
        return navigate(`/dashboard/youtube/videos/${video.id}/posts`);
      })
      .catch((e) => setError(e))
      .finally(toggleSaving);
  };

  const form = useForm({ initialValues, submit });

  useEffect(() => {
    if (video.id) {
      form.setFieldValue('url', getYoutubeUrlFromId(video.id, true));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [video.id]);

  return (
    <Dialog onOpenChange={toggleSocialPost} open={isSocialPost}>
      <DialogContent className="w-full max-w-[704px] p-0">
        <div className="p-6 text-black1 md:p-10">
          <DialogTitle asChild>
            <div className="mb-6">
              <Label className="!text-md">Social Media Post</Label>
              <div className="text-sm">
                To create a social media post select your platform first. Then type what type of
                post you want to create, select tone and hit generate.
              </div>
            </div>
          </DialogTitle>

          <form onSubmit={form.submitForm}>
            <div className="mb-8">
              <Label>Select a Platform</Label>
              <div className="flex flex-col gap-2 sm:flex-row">
                {SOCIAL_PLATFORMS.map(({ Icon, title, value }, index) => (
                  <div
                    className={cn(
                      'flex w-full items-center gap-2 rounded border border-gray5 p-4 text-gray2 hover:cursor-pointer hover:border-black1 hover:bg-black1 hover:text-white',
                      { 'border-black1 bg-black1 text-white': form.values.platform === value }
                    )}
                    onClick={() => form.setFieldValue('platform', value)}
                    key={index}
                  >
                    <Icon size={24} />
                    <div className="font-semibold capitalize">{title}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* @ts-ignore For Now */}
            <FormField
              placeholder="Subject lines to welcome a new subscribers"
              label="Type your prompt (Optional)"
              type="textarea"
              {...form.getInputFields('prompt')}
            />

            <div>
              <Label>Select Tone</Label>
              <div className="flex flex-wrap gap-3">
                {tones.length &&
                  tones.map((item, index) => (
                    <ToneButton
                      variant={form.values.tone === item.title ? 'primary' : ''}
                      onClick={() => form.setFieldValue('tone', item.title)}
                      type="button"
                      key={index}
                    >
                      <div className="flex items-center gap-1.5">
                        <div>{item.icon}</div>
                        <div>{item.title}</div>
                      </div>
                    </ToneButton>
                  ))}
              </div>
            </div>

            <div className="mt-6">
              <Label>Include YouTube Link</Label>
              <div className="flex items-center">
                <label>
                  <div className="-mr-px flex min-w-14 rounded-l border border-gray5 bg-gray7 px-2 py-[7px] flex-center">
                    <Switch
                      onCheckedChange={(v) => form.setFieldValue('includeLink', v)}
                      defaultChecked={form.getInputFields('includeLink').value}
                    />
                  </div>
                </label>
                <Input className="rounded-r !bg-gray7" value={form.values.url} disabled />
              </div>
            </div>

            <div className="mt-6">
              <Label>Language</Label>
              <YoutubeVideoLanguageInput form={form} />
            </div>

            <div className={error ? 'mt-7' : 'mt-10'}>
              {error && <ErrorMessage>{error}</ErrorMessage>}
              <Button type="submit" className="w-full" disabled={isSaving}>
                {isSaving ? 'Generating...' : 'Generate'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const Label = ({ className, ...props }: React.HTMLProps<HTMLDivElement>) => (
  <div className={`mb-2 text-xs font-semibold ${className}`} {...props} />
);

export default YoutubeVideoSocialPostsDialog;
