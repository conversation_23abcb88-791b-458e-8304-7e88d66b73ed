import { DialogContent, DialogClose, Dialog } from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';
import useForm from '@/hooks/useForm';

import YoutubeVideoLanguageSelector from '../YoutubeVideoLanguageSelector';

const YoutubeLanguageSelectDialog = ({
  detectedLanguage,
  isOpen,
  onConfirm,
  onClose,
}: {
  detectedLanguage: string;
  isOpen: boolean;
  onConfirm: (_: string) => void;
  onClose: () => void;
}) => {
  const initialValues = { language: detectedLanguage || 'english' };
  const form = useForm({ initialValues, submit: () => {} });

  return (
    <Dialog onOpenChange={onClose} open={isOpen}>
      <DialogContent className="max-w-[666px]">
        <div className="p-5 pt-4 text-black2 md:p-10 md:pt-9">
          <div className="text-xl font-semibold">Language Selection</div>

          <div className="mt-5">
            <YoutubeVideoLanguageSelector f={form} />
          </div>

          <div className="mt-10 flex gap-3">
            <DialogClose asChild>
              <Button variant="secondary">Cancel</Button>
            </DialogClose>
            <Button
              className="w-full"
              onClick={() => {
                onConfirm(form.values.language);
                onClose();
              }}
            >
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default YoutubeLanguageSelectDialog;
