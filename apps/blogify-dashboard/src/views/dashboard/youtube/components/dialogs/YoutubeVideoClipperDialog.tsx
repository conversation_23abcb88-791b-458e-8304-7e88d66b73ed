import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { useEffect, useState, useMemo, useRef } from 'react';
import { IoCutSharp } from 'react-icons/io5';

import { timeStringToSeconds, formatSeconds } from '@/utils/time';
import { Button } from '@ps/ui/components/button';
import { Slider } from '@ps/ui/components/slider';
import asyncScriptLoad from '@/utils/asyncScriptLoad';
import FormField from '@ps/ui/form/FormField';
import Dialog from '@ps/ui/components/dialog';

import YoutubeVideoChapters from '../YoutubeVideoChapters';

const PLAYER_VARS = {
  autoplay: 1,
  controls: 0,
  start: 0,
  loop: 1,
  rel: 0,
};

export default function YoutubeVideoClipperDialog({
  video,
  title,
  description,
  saving,
  onSubmit,
  open,
  onOpenChange,
}: {
  video: YoutubeVideo;
  title: string;
  description: string;
  saving?: boolean;
  onSubmit: (cts: string | null) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const duration = timeStringToSeconds(video.contentDetails?.duration || '');

  const [clipTime, setClipTime] = useState<number[]>([0, duration]);
  const playerRef = useRef<any>(null);

  const clipStart = useMemo(() => formatSeconds(clipTime[0]), [clipTime]);
  const clipEnd = useMemo(() => formatSeconds(clipTime[1]), [clipTime]);

  const hasChapters = !!video.data?.chapters?.length;

  const seekTo = (seconds: number) => {
    if (playerRef.current?.seekTo) {
      playerRef.current.seekTo(seconds, true);
    }
  };

  const onCTAClick = () => {
    const clipTimeSpan = clipTime?.length === 2 ? clipTime.sort((a, b) => a - b).join(':') : null;
    onSubmit(clipTimeSpan);
  };

  useEffect(() => {
    if (!open) return;
    const url = 'https://www.youtube.com/iframe_api';
    asyncScriptLoad('yt-iframe-api', url, 'YT').then(() => {
      playerRef.current = new window.YT.Player(`player-0-${video.id}`, {
        videoId: video.id,
        playerVars: PLAYER_VARS,
      });
    });
  }, [open, video.id]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => seekTo(clipTime[0]), [clipTime[0]]);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => seekTo(clipTime[1]), [clipTime[1]]);

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      Icon={IoCutSharp}
      title={title}
      description={description}
      className="sm:max-w-xl"
      actions={
        <Button className="w-full" loading={saving} onClick={onCTAClick}>
          Generate
        </Button>
      }
    >
      <div className="aspect-video h-auto w-full rounded border-none" id={`player-0-${video.id}`} />

      <FormField
        label={`Select ${hasChapters ? 'a chapter or ' : ''}video section`}
        containerClass="mt-7"
        type="custom"
      >
        <Slider
          className="h-7"
          onValueChange={setClipTime}
          minStepsBetweenThumbs={1}
          value={clipTime}
          max={duration}
          step={1}
          min={0}
        />

        <div className="flex justify-between text-sm text-gray2">
          <div>{clipStart}</div>
          <div>{clipEnd}</div>
        </div>
      </FormField>

      {hasChapters && (
        <YoutubeVideoChapters
          chapters={video.data?.chapters || []}
          onChapterSelect={setClipTime}
          video={video}
        />
      )}
    </Dialog>
  );
}
