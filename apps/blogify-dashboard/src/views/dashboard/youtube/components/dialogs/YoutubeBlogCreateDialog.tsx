import type { BlogCreate, Blog } from '@ps/types';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { useNavigate } from 'react-router-dom';

import { DialogContent, DialogTitle, Dialog } from '@ps/ui/components/dialog';
import { YOUTUBE_SOURCE } from '@/views/dashboard/blog/create/utils/constants';
import { Card } from '@/components/layout';
import BlogCreatePublishSettings from '@/views/dashboard/blog/create/components/BlogCreatePublishSettings';
import BlogCreateSettings from '@/views/dashboard/blog/create/components/BlogCreateSettings';
import BlogCreateProvider from '@/views/dashboard/blog/create/utils/BlogCreateProvider';

import { getYoutubeUrlFromId, getThumbnail } from '../../utils';

const YoutubeBlogCreateDialog = ({
  video,
  videoIds,
  isBlogCreate,
  toggleBlogCreate,
  clipTimeSpan,
}: {
  video: YoutubeVideo;
  videoIds?: string[];
  isBlogCreate: boolean;
  toggleBlogCreate: React.Dispatch<boolean>;
  clipTimeSpan?: string | null;
}) => {
  const navigate = useNavigate();

  const isBulk = (videoIds?.length || 0) > 1;

  const getPayloadOverride = (): Partial<BlogCreate> => ({
    url: `https://www.youtube.com/watch?v=${video.id}`,
    title: video?.snippet?.title,
    image: getThumbnail(video, 'maxres'),
    youtubeVideoId: video.id,
    ...(!!clipTimeSpan ? { clipTimeSpan } : {}),
    ...(isBulk
      ? {
          urls: videoIds?.map((vid) => getYoutubeUrlFromId(vid)),
          isYoutubeBulk: true,
        }
      : {}),
  });

  const onBlogCreateSuccess = (resp: Blog) => {
    navigate(
      isBulk
        ? '/dashboard/blogs'
        : resp.generationMode === 'auto'
          ? `/dashboard/youtube/videos/${video.id}/blogs`
          : `/dashboard/blogs/${resp?._id}/co-pilot`
    );
  };

  return (
    <>
      <Dialog open={isBlogCreate} onOpenChange={toggleBlogCreate}>
        <DialogContent
          className="w-full max-w-3xl font-inter text-black4"
          // TODO: Remove once https://github.com/radix-ui/primitives/issues/1280 closes.
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <DialogTitle asChild>
            <YoutubeBlogCreateDialogHeader
              video={video}
              isBulk={isBulk}
              selectedVideoCount={videoIds?.length || 0}
            />
          </DialogTitle>

          <BlogCreateProvider
            onBlogCreateSuccess={onBlogCreateSuccess}
            payloadOverride={getPayloadOverride()}
            source={YOUTUBE_SOURCE}
            isBulk={isBulk}
          >
            <hr className="mb-5 h-px w-full bg-gray11" />
            <BlogCreateSettings canUseCoPilot={!videoIds?.length} />

            <hr className="mb-3 mt-5 h-px w-full bg-gray11 md:mb-8 md:mt-10" />
            <BlogCreatePublishSettings />
          </BlogCreateProvider>
        </DialogContent>
      </Dialog>
    </>
  );
};

type Props = { video: YoutubeVideo; selectedVideoCount: number; isBulk: boolean };
const YoutubeBlogCreateDialogHeader: React.FC<Props> = ({ video, isBulk, selectedVideoCount }) => (
  <Card className="!flex-row">
    <div className="relative min-w-28 xs:min-w-40">
      <img
        className="h-16 w-full rounded object-cover xs:h-24"
        src={getThumbnail(video, 'standard')}
      />
      {isBulk && (
        <div className="absolute bottom-1 right-1 rounded bg-[#000000bf] px-2 text-sm text-white">
          +{(selectedVideoCount || 0) - 1}
        </div>
      )}
    </div>
    <div className="ml-3">
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <div className="ellipsis r1 text-sm font-semibold">{video.snippet.title}</div>
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <div className="ellipsis r1 mt-1 text-xs">
        {video.snippet.description?.substring(0, 90)}...
      </div>
      {!!selectedVideoCount && (
        <div className="mt-1 text-xs">{selectedVideoCount} videos selected</div>
      )}
    </div>
  </Card>
);

export default YoutubeBlogCreateDialog;
