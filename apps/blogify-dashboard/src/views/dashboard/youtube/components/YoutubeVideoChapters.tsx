import type { YoutubeVideoChapter } from '@/types/resources/youtube-contents.type';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { timeStringToSeconds } from '@/utils/time';
import { cn } from '@ps/ui/lib/utils';

const YoutubeVideoChapters = ({
  onChapterSelect,
  chapters = [],
  video,
  className,
  ...props
}: {
  onChapterSelect?: ([from, to]: [number, number]) => void;
  chapters?: YoutubeVideoChapter[];
  video?: YoutubeVideo;
} & React.HTMLProps<HTMLDivElement>) => (
  <div className={`rounded border border-gray5 bg-gray7 ${className}`} {...props}>
    {chapters.map((chapter, i) => (
      <div
        key={i}
        className={cn(
          'flex min-h-[38px] items-center justify-between gap-4 border-gray5 px-4 py-2',
          {
            'cursor-pointer': !!onChapterSelect,
            'border-t': i !== 0,
          }
        )}
        onClick={() => {
          if (onChapterSelect) {
            onChapterSelect([
              timeStringToSeconds(chapters[i]?.timestamp),
              chapters[i + 1]?.timestamp
                ? timeStringToSeconds(chapters[i + 1]?.timestamp)
                : timeStringToSeconds(video?.contentDetails?.duration || ''),
            ]);
          }
        }}
      >
        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <div className="ellipsis r2 text-xs font-semibold text-black1 sm:text-md">
          {chapter.title}
        </div>

        <div className="min-w-9 text-right text-xs text-gray2">{chapters[i]?.timestamp}</div>
      </div>
    ))}
  </div>
);

export default YoutubeVideoChapters;
