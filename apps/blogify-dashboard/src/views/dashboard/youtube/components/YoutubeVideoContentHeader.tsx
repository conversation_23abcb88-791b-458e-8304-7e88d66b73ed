import type { ButtonProps } from '@ps/ui/components/button';
import type { IconType } from 'react-icons';

import YoutubeVideoActionButton from './YoutubeVideoActionButton';

export default function YoutubeVideoContentHeader({
  title,
  hasContent,
  description = 'Results are listed chronologically. Save the one you wish to reuse; moving from this page will delete all results.',
  primaryAction: { icon, children, ...buttonProps },
}: {
  title: string;
  hasContent?: boolean;
  description?: string;
  primaryAction: { icon: IconType } & ButtonProps;
}) {
  return (
    <>
      <div className="mb-2 font-semibold">{title}</div>
      <div className="mb-4 w-full text-sm md:w-1/2">{description}</div>
      {!hasContent && (
        <div>
          <YoutubeVideoActionButton icon={icon} {...buttonProps}>
            {children}
          </YoutubeVideoActionButton>
        </div>
      )}
    </>
  );
}
