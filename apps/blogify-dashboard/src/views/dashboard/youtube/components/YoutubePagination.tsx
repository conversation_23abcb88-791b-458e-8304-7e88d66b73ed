import { MdChevronLeft, MdChevronRight } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';

import { getQuery } from '@/services/api/query';
import { Button } from '@ps/ui/components/button';
import Select from '@ps/ui/form/Select';

export type PagingProps = {
  total?: number;
  current?: number;
  pageSize?: number;
  prevPageToken?: string;
  nextPageToken?: string;
  className?: string;

  setPageSize?: (_: number) => void;
  setCurrent?: (_: number) => void;
  setPageToken?: (_: string) => void;
};

const YoutubePagination = ({
  total = 0,
  current = 1,
  pageSize = 20,
  prevPageToken,
  nextPageToken,
  className,

  setPageToken,
  setPageSize,
  setCurrent,
}: PagingProps) => {
  const navigate = useNavigate();

  const from = pageSize * (current - 1) + 1;
  const to = Math.min(pageSize * current, total);
  const totalPages = Math.ceil(total / pageSize);
  const canGoPrev = current > 1;
  const canGoNext = current < totalPages;

  const onPaging = ({
    goto,
    resultPerPage,
  }: {
    goto?: 'prev' | 'next';
    resultPerPage?: number;
  }) => {
    const query: Record<string, string> = {
      ...getQuery(),
      pageSize: String(pageSize),
      current: String(current),
    };

    if (resultPerPage) {
      if (setPageSize) setPageSize(resultPerPage);
      query.pageSize = String(resultPerPage);
    }

    const _pageToken =
      goto === 'prev' && prevPageToken
        ? prevPageToken
        : goto === 'next' && nextPageToken
          ? nextPageToken
          : null;

    if (goto && (_pageToken || canGoPrev || canGoNext)) {
      const _current = goto === 'next' ? current + 1 : current - 1;
      if (setCurrent) setCurrent(_current);
      query.current = String(_current);

      if (_pageToken && setPageToken) {
        setPageToken(_pageToken);
        query.pageToken = _pageToken;
      }
    }

    const queryString = new URLSearchParams(query).toString();
    return navigate({ search: queryString });
  };

  return (
    <div className={`flex items-center justify-between gap-4 py-4 ${className}`}>
      <div className="flex flex-wrap gap-3">
        {(!!prevPageToken || canGoPrev) && !!onPaging && (
          <Button className="!gap-1" variant="gray" onClick={() => onPaging({ goto: 'prev' })}>
            <MdChevronLeft /> Prev
          </Button>
        )}

        {(!!nextPageToken || canGoNext) && !!onPaging && (
          <Button className="!gap-1" variant="gray" onClick={() => onPaging({ goto: 'next' })}>
            Next <MdChevronRight />
          </Button>
        )}
      </div>

      <div className="flex items-center">
        <div className="mr-3 hidden whitespace-nowrap text-xs text-gray2 xs:block">
          {total > pageSize
            ? `Showing results ${from} - ${to} of ${total}`
            : `Showing ${total} result${total > 1 ? 's' : ''}`}
        </div>

        {onPaging && (
          <Select
            onChange={(ev: any) => onPaging({ resultPerPage: parseInt(ev.target.value, 10) })}
            defaultValue={String(pageSize)}
            className="h-8 w-24 text-xs"
          >
            <option value="10">10 / Page</option>
            <option value="25">25 / Page</option>
            <option value="50">50 / Page</option>
          </Select>
        )}
      </div>
    </div>
  );
};

export default YoutubePagination;
