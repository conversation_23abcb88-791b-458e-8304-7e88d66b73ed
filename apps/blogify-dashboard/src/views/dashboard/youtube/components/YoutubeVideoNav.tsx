import type { Breadcrumb } from '@/types/components/layout';
import type { Dispatch } from 'react';

import { NavLink } from 'react-router-dom';

import { useStoreState } from '@/store';
import { FilterButton } from '@/views/dashboard/writing-snippets/styles/index.styled';
import { Card } from '@/components/layout';

import { isOwnChannelVideo } from '../utils';
import useYoutubeVideo from '../utils/useYoutubeVideo';
import useYoutube from '../utils/useYoutube';

const YoutubeVideoNav = ({
  breadcrumb,
  setBreadcrumb,
}: {
  breadcrumb: Breadcrumb[];
  setBreadcrumb: Dispatch<Breadcrumb[]>;
}) => {
  const { channel } = useYoutube();
  const { video } = useYoutubeVideo();
  const user = useStoreState((u) => u.user.current);

  const nav = [
    { name: 'Overview', path: '' },
    { name: 'Blogs', path: '/blogs' },
    { name: 'Posts', path: '/posts' },
  ];

  if (isOwnChannelVideo(channel, video) && user.hasYouTubeProAddon) {
    nav.push(
      ...[
        { name: 'Thumbnails', path: '/thumbnails' },
        { name: 'Shorts', path: '/shorts' },
        { name: 'Title', path: '/title' },
        { name: 'Description', path: '/description' },
        { name: 'Chapters', path: '/chapters' },
        { name: 'Tags', path: '/tags' },
        { name: 'Summary', path: '/summary' },
        { name: 'Transcript', path: '/transcript' },
      ]
    );
  }

  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname
    <Card className="h-scroll mt-0.5 !flex-row gap-4 px-4 py-3">
      {nav.map((item, index) => (
        <NavLink
          to={`/dashboard/youtube/videos/${video.id}${item.path}`}
          onClick={() => {
            setBreadcrumb([
              ...breadcrumb,
              { title: item.name, href: `/dashboard/youtube/videos/${video.id}${item.path}` },
            ]);
          }}
          key={index}
          end
        >
          {({ isActive }) => (
            <FilterButton variant={isActive ? 'primary' : ''}>{item.name}</FilterButton>
          )}
        </NavLink>
      ))}
    </Card>
  );
};

export default YoutubeVideoNav;
