import { MdChevronRight, MdInfoOutline } from 'react-icons/md';
import { FaArrowsRotate } from 'react-icons/fa6';
import { useToggle } from 'react-use';
import { useEffect } from 'react';
import { BiUnlink } from 'react-icons/bi';

import { PopoverTrigger, PopoverContent, Popover } from '@ps/ui/components/popover';
import { getDisconnectUrl, getConnectUrl } from '@/utils/integration';
import { useYoutubeTourGuideContext } from '@/context/YoutubeTourGuide';
import { useStoreState } from '@/store';
import { isMobile } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import AddBlogCredit from '@/views/dashboard/payment/subscription/AddBlogCredit';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { getThumbnail } from '../utils';
// import YoutubeChannelSwitcher from './YoutubeChannelSwitcher';
import YoutubeChannelHelp from './YoutubeChannelHelp';
import useYoutube from '../utils/useYoutube';

const YoutubeChannelHeader = () => {
  const [isDisconnectConfirmDialogOpen, toggleDisconnectConfirmDialogOpen] = useToggle(false);
  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const { channel } = useYoutube();

  const user = useStoreState((s) => s.user.current);
  const isConnected = !!user.youtubeConnect;

  // YouTube Connect Tour Guide
  const {
    setState,
    state: { tourActive },
  } = useYoutubeTourGuideContext();

  useEffect(() => {
    if (tourActive && isConnected && channel.snippet.title) {
      setState({ run: true, stepIndex: 2 });
    }
  }, [channel.snippet.title, isConnected, setState, tourActive]);

  return (
    <Card>
      {channel.brandingSettings?.image?.bannerExternalUrl ? (
        <img
          src={channel.brandingSettings?.image?.bannerExternalUrl}
          className="h-[100px] w-full object-cover sm:h-[140px]"
        />
      ) : (
        <div className="h-[100px] bg-black2 md:h-[140px]" />
      )}

      <div className="flex w-full flex-wrap items-end justify-between">
        <div className="flex items-center gap-6 p-4">
          {getThumbnail(channel) ? (
            <img src={getThumbnail(channel)} className="size-[120px] min-w-[120px] rounded-full" />
          ) : (
            <div className="flex size-[120px] rounded-full flex-center">
              <Loader className="!size-[148px]" />
            </div>
          )}

          {channel.snippet.title && (
            <div>
              <div className="text-xl font-semibold text-black1">{channel.snippet.title}</div>
              <div className="ml-1 text-sm text-gray2">{channel.snippet.customUrl}</div>

              <CreditInfo className="mt-4 hidden xs:flex" />
            </div>
          )}
        </div>

        <CreditInfo className="mb-4 px-6 xs:hidden" />

        <div className="flex gap-3 p-6 pt-0">
          <Button variant="secondary" onClick={toggleAddBlogCreditDialog} id="youtubeBuyCredit">
            Buy Credit
          </Button>

          {/* <YoutubeChannelSwitcher /> */}
          <Link ignoreExternal to={getConnectUrl('youtube')}>
            <Button className="outline-none" variant="secondary">
              <div className="flex items-center gap-2">
                <FaArrowsRotate size={16} />
                <span className="hidden xs:inline">Change Channel</span>
              </div>
            </Button>
          </Link>

          <Button
            variant="icon"
            className="!size-9 !bg-red2 text-white"
            onClick={toggleDisconnectConfirmDialogOpen}
          >
            <BiUnlink size={16} />
          </Button>
        </div>
      </div>
      <YoutubeChannelHelp />

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
      <ConfirmationDialog
        isOpen={isDisconnectConfirmDialogOpen}
        confirmationTitle="Disconnect"
        confirmationMessage="Are you sure you want to disconnect your YouTube account? Once disconnected all of
        your data related to your videos eg. Title, Description, Tags etc. will not be
        accessible anymore."
        confirmText="Disconnect"
        cancelText="Stay Connected"
        confirmRedirect={getDisconnectUrl('youtube')}
        onClose={toggleDisconnectConfirmDialogOpen}
      />
    </Card>
  );
};

const CreditInfo = ({ className }: { className: string }) => {
  const packageByName = useStoreState((s) => s.context.packageByName);
  const user = useStoreState((s) => s.user.current);

  const userPlan = packageByName[user.subscriptionPlan];

  return (
    <div className={`flex flex-wrap items-center gap-4 ${className}`}>
      <Popover>
        <PopoverTrigger asChild>
          {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
          <button className="youtubeCreditInfo">
            <div className="mr-2 flex gap-1.5 rounded bg-success py-1 pl-1.5 pr-1 font-semibold">
              <div className="text-xs text-white">Total Credits: {user.credits}</div>
              <div className="flex items-center gap-1 rounded bg-grannyApple px-1.5 text-success">
                <div className="text-xs">LEARN MORE</div>
                <MdInfoOutline size={12} />
              </div>
            </div>
          </button>
        </PopoverTrigger>

        <PopoverContent
          className="max-w-[248px] p-4 text-sm text-black2"
          side={isMobile ? 'bottom' : 'right'}
          sideOffset={4}
        >
          <div className="flex justify-between">
            <div>Total Credits:</div>
            <div>{user.credits}</div>
          </div>
          <div className="my-2 flex justify-between">
            <div>YouTube Connect Credits: </div>
            <div>{user.youtubeAddon?.features?.mediaCredit || 0}</div>
          </div>
          <div className="mt-1 text-xs text-gray2">
            Credits will reset monthly if not used. You can buy additional credit at $
            {userPlan?.limit?.CREDIT_PRICE} per credit.
          </div>
        </PopoverContent>
      </Popover>
      <div className="flex items-center gap-1.5 text-xs text-gray2">
        Buy additional credit at ${userPlan?.limit?.CREDIT_PRICE} per credit{' '}
        <MdChevronRight size={16} />
      </div>
    </div>
  );
};

export default YoutubeChannelHeader;
