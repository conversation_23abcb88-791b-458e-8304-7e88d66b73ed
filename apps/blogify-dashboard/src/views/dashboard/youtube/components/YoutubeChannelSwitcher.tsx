import { FaArrowsRotate } from 'react-icons/fa6';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { Button } from '@ps/ui/components/button';

import { getThumbnail } from '../utils';
import useYoutube from '../utils/useYoutube';

const YoutubeChannelSwitcher = () => {
  const { channel: selectedChannel, channels, onChannelChange } = useYoutube();

  if (channels?.length < 2) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="outline-none" variant="secondary">
          <div className="flex items-center gap-2">
            <FaArrowsRotate size={16} />
            <span className="hidden xs:block">Change Channel</span>
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent sideOffset={-36} align="end">
        {channels.map((channel, i) => (
          <DropdownMenuItem
            className={`border-t ${i !== 0 ? 'border-bg2' : ''}`}
            onSelect={() => onChannelChange(channel)}
            key={channel.id}
          >
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center">
                <img
                  src={getThumbnail(channel, 'standard')}
                  className="mr-3 size-10 rounded-full"
                />
                <div>
                  <div className="text-sm font-semibold text-black1">{channel.snippet.title}</div>
                  <div className="text-xs text-gray2">{channel.snippet.customUrl}</div>
                </div>
              </div>

              {selectedChannel?.id === channel.id && (
                <div className="flex size-4 rounded-full bg-[#41d4904c] flex-center">
                  <div className="size-2 rounded-full bg-green" />
                </div>
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default YoutubeChannelSwitcher;
