import type { YoutubeVideo } from '@/types/integrations/youtube.type';
import type { Dispatch } from 'react';

import { IoCalendarClearOutline, IoEyeOutline } from 'react-icons/io5';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { useEffect, useState } from 'react';
import { BiCommentDetail } from 'react-icons/bi';
import { IoMdPlayCircle } from 'react-icons/io';
import { useToggle } from 'react-use';
import dayjs from 'dayjs';

import {
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { DialogTrigger, DialogContent, Dialog } from '@ps/ui/components/dialog';
import { timeStringToSeconds, formatSeconds } from '@/utils/time';
import { shortenNumber } from '@/utils/number';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { API } from '@/services/api';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import { getYoutubeUrlFromId, getThumbnail } from '../utils';
import YoutubeVideoSocialPostsDialog from './dialogs/YoutubeVideoSocialPostsDialog';
import YoutubeGenerateContentDialog from './dialogs/YoutubeGenerateContentDialog';
import YoutubeVideoThumbnailDialog from './dialogs/YoutubeVideoThumbnailDialog';
import YoutubeVideoClipperDialog from './dialogs/YoutubeVideoClipperDialog';
import YoutubeBlogCreateDialog from './dialogs/YoutubeBlogCreateDialog';
import YoutubeVideoEmbed from './YoutubeVideoEmbed';
import YoutubeBadge from './YoutubeBadge';

type Props = {
  video: YoutubeVideo;
  isSelected?: boolean;
  onSelect?: (videoId: string) => void;
};
const toDetails = (video: YoutubeVideo) =>
  video?.status?.privacyStatus === 'private'
    ? 'https://support.google.com/youtube/answer/157177'
    : `/dashboard/youtube/videos/${video.id}`;

const YoutubeVideoCard = ({ video, isSelected, onSelect }: Props) => {
  const [isThumbnailCreate, toggleThumbnailCreate] = useToggle(false);
  const [isBlogFromChapter, toggleBlogFromChapter] = useToggle(false);
  const [isGenerateContent, toggleGenerateContent] = useToggle(false);
  const [isSocialPost, toggleSocialPost] = useToggle(false);
  const [isBlogCreate, toggleBlogCreate] = useToggle(false);
  const [clipTimeSpan, setClipTimeSpan] = useState<string | null>(null);

  const hasContent = !!video.data?.generateTypes.length;
  const hasChapters = !!video.data?.chapters?.length;
  const isDetailsView = !onSelect;
  const isPrivate = video?.status?.privacyStatus === 'private';

  return (
    <>
      <Card className="mt-0.5 !flex-row flex-wrap items-center justify-between gap-4 p-4 lg:flex-nowrap">
        <div className="flex w-full items-stretch lg:w-4/5">
          {!isDetailsView && (
            <label>
              <div className="flex pl-0 pr-2 sm:pl-1 sm:pr-3">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => !isPrivate && onSelect(video.id)}
                />
              </div>
            </label>
          )}
          <div className="flex flex-col gap-4">
            <div className="flex">
              <YoutubeVideoCardThumbnail video={video} isDetailsView={isDetailsView} />

              {video.snippet.title && (
                <div className="ml-2 sm:ml-4">
                  <Link to={toDetails(video)}>
                    {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
                    <div className="ellipsis r2 text-xs font-semibold text-black1 sm:text-sm">
                      {video.snippet.title}
                    </div>

                    {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
                    <div className="ellipsis r2 mt-1 text-xs sm:text-sm">
                      {isPrivate
                        ? `⚠️ This video is private, please change it to either public or unlisted for
                        operability.`
                        : video.snippet.description.substring(0, 100) || '--'}
                    </div>
                  </Link>

                  <YoutubeVideoStats className="mt-4 hidden sm:flex" video={video} />
                </div>
              )}
            </div>

            <YoutubeVideoStats className="sm:hidden" video={video} />
          </div>
        </div>

        <div className="flex w-full justify-end gap-2 lg:w-auto">
          {!isDetailsView && hasContent ? (
            <div className="flex w-full min-w-[194px] gap-3">
              <Button
                className="w-10 min-w-10 !p-0"
                onClick={toggleGenerateContent}
                variant="secondary"
              >
                <img className="size-[14px]" src="/images/starburst.svg" />
              </Button>

              <Link className="w-full" to={`/dashboard/youtube/videos/${video.id}`}>
                <Button className="w-full" variant="secondary">
                  <div className="ml-2">Show Contents</div>
                </Button>
              </Link>
            </div>
          ) : (
            !isPrivate && (
              <Button
                className="w-full min-w-[120px]"
                variant="secondary"
                onClick={toggleGenerateContent}
              >
                <img className="size-[14px]" src="/images/starburst.svg" />
                <div className="ml-2">Generate</div>
              </Button>
            )
          )}

          {!isPrivate && (
            <YoutubeVideoMenu
              toggleBlogFromChapter={toggleBlogFromChapter}
              toggleSocialPost={toggleSocialPost}
              toggleBlogCreate={toggleBlogCreate}
              video={video}
            />
          )}
        </div>
      </Card>

      {video?.id && (
        <>
          <YoutubeGenerateContentDialog
            open={isGenerateContent}
            onOpenChange={toggleGenerateContent}
            toggleThumbnailCreate={toggleThumbnailCreate}
            toggleBlogFromChapter={toggleBlogFromChapter}
            toggleSocialPost={toggleSocialPost}
            toggleBlogCreate={toggleBlogCreate}
            video={video}
          />
          <YoutubeVideoClipperDialog
            video={video}
            title={`Create Blog from a ${hasChapters ? 'chapter' : 'section'}`}
            description="Select a chapter below or select a portion of your video that you want to use to create a blog"
            open={isBlogFromChapter}
            onOpenChange={toggleBlogFromChapter}
            onSubmit={(ct) => {
              setClipTimeSpan(ct);
              toggleBlogFromChapter();
              toggleBlogCreate();
            }}
          />
          <YoutubeVideoThumbnailDialog
            video={video}
            summary={video.data?.summary}
            isThumbnailCreate={isThumbnailCreate}
            toggleThumbnailCreate={toggleThumbnailCreate}
          />
          <YoutubeVideoSocialPostsDialog
            toggleSocialPost={toggleSocialPost}
            isSocialPost={isSocialPost}
            video={video}
          />
          <YoutubeBlogCreateDialog
            video={video}
            isBlogCreate={isBlogCreate}
            toggleBlogCreate={toggleBlogCreate}
            clipTimeSpan={clipTimeSpan}
          />
        </>
      )}
    </>
  );
};

const YoutubeVideoCardThumbnail = ({
  video,
  isDetailsView,
}: {
  video: YoutubeVideo;
  isDetailsView: boolean;
}) => {
  const [isShort, setIsSort] = useState(false);

  useEffect(() => {
    if (isDetailsView && video.id) {
      API.fetch<{ isShort: boolean }>(`youtube/videos/${video.id}/is-short`).then((resp) =>
        setIsSort(!!resp?.isShort)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [video.id]);

  return (
    <Link to={toDetails(video)}>
      {getThumbnail(video, 'standard') ? (
        <div className="relative">
          <img
            className="h-[70px] w-[120px] min-w-[120px] rounded object-cover sm:h-[90px] sm:w-[160px] sm:min-w-[160px]"
            src={getThumbnail(video, 'standard')}
          />
          {isDetailsView && (
            <Dialog>
              <DialogTrigger asChild>
                <div>
                  <div className="absolute left-0 top-0 flex size-full bg-[#00000066] text-white flex-center">
                    <IoMdPlayCircle size={20} />
                  </div>
                </div>
              </DialogTrigger>
              <DialogContent className={isShort ? 'max-w-[415px]' : 'max-w-screen-lg'}>
                <div className="flex w-full flex-center">
                  <YoutubeVideoEmbed video={video} isShort={isShort} autoplay />
                </div>
              </DialogContent>
            </Dialog>
          )}
          {video.contentDetails?.duration && (
            <div className="absolute bottom-1 right-1 rounded bg-black px-0.5">
              <div className="text-xs font-bold text-white">
                {formatSeconds(timeStringToSeconds(video.contentDetails?.duration))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="mr-2 flex h-[70px] w-[120px] flex-center sm:mr-4 sm:h-[90px] sm:w-40">
          <Loader className="!size-[70px]" />
        </div>
      )}
    </Link>
  );
};

const YoutubeVideoStats = ({ video, className }: { video: YoutubeVideo; className: string }) => (
  <div className={`flex flex-wrap items-center gap-4 ${className}`}>
    <YoutubeBadge checkMark>{video.snippet.channelTitle}</YoutubeBadge>

    <div className="flex items-center gap-4">
      {video.statistics?.viewCount && (
        <div className="flex gap-1 text-xs font-semibold text-gray2">
          <IoEyeOutline size={16} />
          <div>{shortenNumber(video.statistics?.viewCount)}</div>
        </div>
      )}

      {video.statistics?.commentCount && (
        <div className="flex gap-1 text-xs font-semibold text-gray2">
          <BiCommentDetail size={16} />
          <div>{shortenNumber(video.statistics?.commentCount)}</div>
        </div>
      )}

      {video.snippet.publishedAt && (
        <div className="flex gap-1 text-xs font-semibold text-gray2">
          <IoCalendarClearOutline size={16} />
          <div>{dayjs(video.snippet.publishedAt).format('MMM DD, YYYY')}</div>
        </div>
      )}
    </div>
  </div>
);

const YoutubeVideoMenu = ({
  toggleBlogFromChapter,
  toggleSocialPost,
  toggleBlogCreate,
  video,
}: {
  toggleSocialPost: Dispatch<void>;
  toggleBlogFromChapter: Dispatch<void>;
  toggleBlogCreate: Dispatch<void>;
  video: YoutubeVideo;
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button className="min-w-9 outline-none" variant="icon">
        <div className="mt-1 text-gray2">
          <BsThreeDotsVertical size={16} />
        </div>
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent className="w-fit" align="end" sideOffset={-36}>
      <DropdownMenuItem onSelect={(ev) => ev.preventDefault()}>
        <Link to={`/dashboard/youtube/videos/${video.id}`}>Video Details</Link>
      </DropdownMenuItem>
      <DropdownMenuItem onSelect={() => toggleBlogCreate()}>Generate Blog</DropdownMenuItem>
      <DropdownMenuItem onSelect={() => toggleBlogFromChapter()}>
        Generate Blog from {video.data?.generateTypes.includes('chapters') ? 'Chapter' : 'Section'}
      </DropdownMenuItem>
      <DropdownMenuItem onSelect={() => toggleSocialPost()}>
        Create Social Media Post
      </DropdownMenuItem>
      <DropdownMenuItem onSelect={(ev) => ev.preventDefault()}>
        <Link to={getYoutubeUrlFromId(video.id)}>Open in YouTube</Link>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

export default YoutubeVideoCard;
