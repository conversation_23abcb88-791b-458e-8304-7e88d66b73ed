import type { YoutubePlaylist } from '@/types/integrations/youtube.type';
import type { Dispatch } from 'react';

import { cn } from '@ps/ui/lib/utils';

import { getThumbnail } from '../utils';
import YoutubeBadge from './YoutubeBadge';

const YoutubePlaylistCard = ({
  id,
  playlist: {
    contentDetails: { itemCount },
    ...playlist
  },
  selected,
  onSelectPlaylist,
}: {
  id?: string;
  playlist: YoutubePlaylist;
  selected: boolean;
  onSelectPlaylist: Dispatch<string>;
}) => (
  <div
    id={id}
    className="min-w-32 cursor-pointer md:min-w-[194px]"
    onClick={() => onSelectPlaylist(playlist.id)}
  >
    <div
      className={cn('relative rounded-lg border-4', {
        'border-primary': selected,
        'border-transparent': !selected,
      })}
    >
      <img
        className="h-[72px] w-full rounded object-cover"
        src={getThumbnail(playlist, 'high')}
        alt={playlist.title}
      />
      <YoutubeBadge
        className={cn('absolute bottom-[6px] left-[6px] px-1 py-px text-xxs text-black1', {
          'bg-primary text-white': selected,
        })}
      >
        {itemCount || 'No'} Video{itemCount > 1 || itemCount === 0 ? 's' : ''}
      </YoutubeBadge>
    </div>

    {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
    <span className="ellipsis r2 mt-1 h-9 text-sm font-semibold">{playlist.snippet?.title}</span>
  </div>
);

export default YoutubePlaylistCard;
