import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { useEffect, useRef } from 'react';

import { cn } from '@ps/ui/lib/utils';
import asyncScriptLoad from '@/utils/asyncScriptLoad';

const PLAYER_VARS = {
  autoplay: 1,
  controls: 0,
  start: 0,
  loop: 1,
  rel: 0,
};

const YoutubeVideoEmbed = ({
  video,
  isShort = false,
  autoplay = false,
  controls = true,
  pauseOnStart,
  loop = true,
  start = 0,
  end,
  id,
}: {
  video: YoutubeVideo;
  isShort?: boolean;
  autoplay?: boolean;
  controls?: boolean;
  pauseOnStart?: boolean;
  loop?: boolean;
  start?: number;
  end?: number;
  id?: string;
}) => {
  const playerRef = useRef<any>(null);
  const playerId = `player${id ? `-${id}` : ''}-${video.id}`;

  const onStateChange = (event: any) => {
    if (!!loop && event.data === window.YT.PlayerState.ENDED) {
      event.target.seekTo(start || 0, true);
      event.target.playVideo();
    }
    if (pauseOnStart && event.data === window.YT.PlayerState.PLAYING) {
      setTimeout(() => event.target.pauseVideo(), 500);
    }
  };

  useEffect(() => {
    if (video.id) {
      const url = 'https://www.youtube.com/iframe_api';
      asyncScriptLoad('yt-iframe-api', url, 'YT').then(() => {
        const playerVars = {
          ...PLAYER_VARS,
          controls: typeof controls !== undefined ? Number(controls) : PLAYER_VARS.controls,
          autoplay: typeof autoplay !== undefined ? Number(autoplay) : PLAYER_VARS.autoplay,
          loop: typeof loop !== undefined ? Number(loop) : PLAYER_VARS.loop,
          ...(start ? { start } : {}),
          ...(end ? { end } : {}),
          ...(pauseOnStart ? { mute: 1 } : {}),
        };
        new window.YT.Player(playerRef.current || playerId, {
          videoId: video.id,
          playerVars,
          events: {
            onStateChange,
          },
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [video.id]);

  return (
    <div
      className={cn('block h-auto max-h-screen w-full rounded border-none', {
        'aspect-[9/16]': isShort,
        'aspect-video': !isShort,
      })}
      ref={playerRef}
      id={playerId}
    />
  );
};

export default YoutubeVideoEmbed;
