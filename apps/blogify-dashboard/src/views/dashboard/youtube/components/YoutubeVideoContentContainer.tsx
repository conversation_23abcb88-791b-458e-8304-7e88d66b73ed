import type {
  YoutubeVideoTranscript,
  YoutubeContentTypes,
  YoutubeVideoContent,
  YoutubeVideoChapter,
} from '@/types/resources/youtube-contents.type';
import type { IconType } from 'react-icons';

import { useToggle } from 'react-use';

import {
  useYoutubeContentGenerateMore,
  useYoutubeContentGenerate,
} from '../utils/useYoutubeMutation';
import { CopyButtonWithIcon, GenerateButton, Card } from './YoutubeVideoContentLayouts';
import { CONTENT_COUNT_MAX_PER_TYPE } from '../utils/constants';
import YoutubeLanguageSelectDialog from './dialogs/YoutubeLanguageSelectDialog';
import YoutubeVideoContentHeader from './YoutubeVideoContentHeader';
import useYoutubeVideo from '../utils/useYoutubeVideo';

const YoutubeVideoContentContainer = ({
  generateMoreTitle,
  contentType,
  children,
  contents,
  title,
  icon,
  copyText,
  onCopy,
  ...props
}: {
  generateMoreTitle?: string;
  contentType: YoutubeContentTypes;
  contents?: YoutubeVideoTranscript[] | YoutubeVideoContent[] | YoutubeVideoChapter[] | string;
  title: string;
  icon: IconType;
  copyText?: string;
  onCopy?: () => void;
} & React.HTMLProps<HTMLDivElement>) => {
  const { video, detectedLanguage, refetchVideo, refetchContents } = useYoutubeVideo();
  const { generateContent, isGenerating } = useYoutubeContentGenerate(video, {
    onSuccess: () => {
      refetchContents();
      refetchVideo();
    },
  });
  const { generateMoreContent, isGeneratingMore } = useYoutubeContentGenerateMore(video.id, {
    onSuccess: refetchContents,
  });

  const [isLanguageSelectOpen, toggleLanguageSelect] = useToggle(false);

  return (
    <Card {...props}>
      <YoutubeVideoContentHeader
        title={title}
        hasContent={!!contents?.length}
        primaryAction={{
          onClick: video.data?.language
            ? () => generateContent({ generateTypes: [contentType] })
            : () => toggleLanguageSelect(),
          children: `Create Video ${title}`,
          loading: isGenerating,
          icon,
        }}
      />

      {!!contents?.length && (
        <>
          {children}
          <div className="mt-2 flex gap-6">
            {CONTENT_COUNT_MAX_PER_TYPE[contentType] > contents.length && (
              <GenerateButton
                onClick={() => generateMoreContent(contentType)}
                loading={isGeneratingMore}
              >
                {generateMoreTitle || `Generate More ${contentType}`}
              </GenerateButton>
            )}

            {copyText && <CopyButtonWithIcon onClick={onCopy}>{copyText}</CopyButtonWithIcon>}
          </div>
        </>
      )}

      {video?.id && (
        <YoutubeLanguageSelectDialog
          onConfirm={(language) => generateContent({ language, generateTypes: [contentType] })}
          detectedLanguage={detectedLanguage}
          onClose={toggleLanguageSelect}
          isOpen={isLanguageSelectOpen}
        />
      )}
    </Card>
  );
};

export default YoutubeVideoContentContainer;
