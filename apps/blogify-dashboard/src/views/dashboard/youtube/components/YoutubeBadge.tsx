import { IoIosCheckmarkCircle } from 'react-icons/io';

import { cn } from '@ps/ui/lib/utils';

const YoutubeBadge = ({
  title,
  checkMark = false,
  children,
  className,
}: React.HTMLProps<HTMLDivElement> & {
  checkMark?: boolean;
  title?: string;
}) => (
  <div className={cn('w-fit rounded bg-gray2 px-2 py-1 text-xs text-white', className)}>
    <div className="flex items-center gap-1">
      <span className="font-semibold uppercase">{children || title}</span>

      {checkMark && <IoIosCheckmarkCircle className="text-white" size={13} />}
    </div>
  </div>
);

export default YoutubeBadge;
