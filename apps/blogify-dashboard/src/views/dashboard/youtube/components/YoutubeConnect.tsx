import { PageContainer<PERSON>ini } from '@/components/layout/PageContainer';
import { getConnectUrl } from '@/utils/integration';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import Link from '@/components/common/Link';

const YoutubeConnect = () => {
  const error = parseQuery().error;

  return (
    <PageContainerMini className="px-4 py-2">
      <Card className="!flex-row justify-between p-6">
        <div className="flex items-center">
          <img className="size-6 sm:size-8" src="/images/third-party-logo/youtube.png" />
          <div className="ml-4 font-semibold text-black1">YouTube</div>
        </div>

        <div className="flex items-center">
          <div className="hidden max-w-[400px] text-right text-xs text-gray2 md:block">
            Connect with your YouTube account
          </div>

          <Link ignoreExternal to={getConnectUrl('youtube')}>
            <Button className="ml-6" id="youtubeConnectButton">
              Connect
            </Button>
          </Link>
        </div>
      </Card>

      {error && (
        <Card className="mt-0.5 p-6">
          <div className="text-sm">
            <span className="font-bold text-red">Error: </span>
            {error}
          </div>
        </Card>
      )}

      <Card className="mt-0.5 p-6">
        <div className="text-xs text-black2">
          <div>
            Blogify AI's use and transfer to any other app of information received from Google APIs
            will adhere to{' '}
            <a
              className="underline hover:text-primary"
              href="https://developers.google.com/terms/api-services-user-data-policy#additional_requirements_for_specific_api_scopes"
            >
              Google API Services User Data Policy
            </a>
            , including the Limited Use requirements. To learn more please check our{' '}
            <a
              className="underline hover:text-primary"
              href="/privacy/#youtube-connect-feature-policy"
            >
              policy
            </a>{' '}
            on YouTube Connect Feature.
          </div>
        </div>
      </Card>
    </PageContainerMini>
  );
};

export default YoutubeConnect;
