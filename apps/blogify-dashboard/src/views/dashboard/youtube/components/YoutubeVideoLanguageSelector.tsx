import type { ReactSelectOptionType } from '@/components/form/SelectOption';

import { BLOG_LANGUAGES } from '@/views/dashboard/blog/create/utils/languages';
import SelectOption from '@/components/form/SelectOption';

const blogLanguageOptions: ReactSelectOptionType[] = BLOG_LANGUAGES.map((language) => ({
  value: language.value,
  label: `${language.name} - ${language.translation}`,
  iso: language.iso,
}));

const YoutubeVideoLanguageSelector = ({
  f: form,
  ...props
}: { f: any } & React.HTMLProps<HTMLDivElement>) => (
  <div {...props}>
    <div className="mb-1 text-sm font-semibold">Video Language</div>
    <div className="mb-3 text-sm">
      To ensure accurate results, make sure the correct language is selected for the video.
    </div>
    <YoutubeVideoLanguageInput form={form} />
  </div>
);

const YoutubeVideoLanguageInput = ({ form }: { form: any }) => (
  <SelectOption options={blogLanguageOptions} {...form.getInputFields('language')} />
);

export default YoutubeVideoLanguageSelector;
export { YoutubeVideoLanguageInput };
