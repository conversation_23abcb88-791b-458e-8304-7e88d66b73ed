import type { YoutubeVideoContent } from '@/types/resources/youtube-contents.type';
import type { ButtonProps } from '@ps/ui/components/button';

import { MdClose, MdAdd } from 'react-icons/md';
import { useToggle } from 'react-use';

import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';

const YoutubeVideoTags = ({
  tags = [],
  isHashtag = false,
}: {
  tags?: string[];
  isHashtag?: boolean;
}) => (
  <div className="flex flex-wrap gap-2">
    {tags.map((tag, index) => (
      <TagButton key={index} variant="secondary">
        {isHashtag && !tag.startsWith('#') ? '#' : ''}
        {tag}
      </TagButton>
    ))}
  </div>
);

const YoutubeVideoTagButton = ({
  tag,
  onDelete,
  isHashtag = false,
}: {
  tag: YoutubeVideoContent;
  onDelete: () => void;
  isHashtag?: boolean;
}) => {
  const [isDeleted, toggleDelete] = useToggle(tag.isDeleted);

  return (
    <TagButton
      className={cn({ 'text-gray2 opacity-60': isDeleted })}
      variant="secondary"
      onClick={() => {
        toggleDelete();
        onDelete();
      }}
    >
      <div className="flex items-center gap-2">
        <span>
          {isHashtag && !tag.contentData.startsWith('#') ? '#' : ''}
          {tag.contentData}
        </span>
        {isDeleted ? <MdAdd /> : <MdClose />}
      </div>
    </TagButton>
  );
};

const TagButton = ({ className, ...props }: ButtonProps) => (
  <Button
    className={`border border-gray5 bg-gray7 font-normal text-black1 transition-all duration-200 ease-in hover:no-underline ${className}`}
    {...props}
  />
);

export default YoutubeVideoTags;
export { YoutubeVideoTagButton };
