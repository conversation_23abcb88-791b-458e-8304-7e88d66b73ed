import type { ButtonProps } from '@ps/ui/components/button';

import { CiCircleMore } from 'react-icons/ci';
import { GoCopy } from 'react-icons/go';

import { Card as BaseCard } from '@/components/layout';
import { Button } from '@ps/ui/components/button';
import { copy } from '@/utils/clipboard';
import { cn } from '@ps/ui/lib/utils';

export const Card = ({ className, ...props }: React.HTMLProps<HTMLDivElement>) => (
  <BaseCard className={`mt-0.5 px-6 py-5 sm:px-10 sm:py-8 ${className}`} {...props} />
);

export const CopyButton = ({ content, className, ...props }: { content: string } & ButtonProps) => (
  // eslint-disable-next-line tailwindcss/no-custom-classname
  <Button className={cn('xs rounded bg-gray2', className)} onClick={() => copy(content)} {...props}>
    COPY
  </Button>
);

export const CopyButtonWithIcon = ({ children, className, ...props }: ButtonProps) => (
  <Button className={`p-0 ${className}`} variant="ghost" color="primary" {...props}>
    <GoCopy size={18} />
    <div className="ml-2 text-sm font-semibold">{children}</div>
  </Button>
);

export const GenerateButton = ({ children, className, ...props }: ButtonProps) => (
  <Button
    className={`justify-start gap-1 p-0 ${className}`}
    variant="ghost"
    color="primary"
    {...props}
  >
    <CiCircleMore size={16} />
    <div className="font-semibold capitalize">{children}</div>
  </Button>
);

export const TextBox = ({ className }: { children: React.ReactNode; className?: string }) => (
  <div
    className={`flex items-center justify-between gap-1 border-b border-gray5 px-4 py-3 ${className}`}
  />
);
