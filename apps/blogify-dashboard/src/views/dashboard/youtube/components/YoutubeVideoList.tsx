import type { YoutubeVideo } from '@/types/integrations/youtube.type';
import type { PagingProps } from './YoutubePagination';

import { useToggle, useSet } from 'react-use';
import { IoMdCloseCircle } from 'react-icons/io';

import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import { cn } from '@ps/ui/lib/utils';
import PageContainer from '@/components/layout/PageContainer';
import Loader from '@/components/misc/Loader';

import YoutubeBlogCreateDialog from './dialogs/YoutubeBlogCreateDialog';
import YoutubePagination from './YoutubePagination';
import YoutubeVideoCard from './YoutubeVideoCard';

const YoutubeVideoList = ({
  videos,
  isFetchingNextPage,
  isFetching,
  isLoading,
  isError,
  ...paging
}: {
  videos: YoutubeVideo[];
  isFetchingNextPage?: boolean;
  isFetching: boolean;
  isLoading: boolean;
  isError: boolean;
} & PagingProps) => {
  const [selectedVideos, selectedVideosSet] = useSet<string>(new Set());
  const [isBlogCreate, toggleBlogCreate] = useToggle(false);

  return (
    <div className="text-black2">
      {isLoading && !isFetchingNextPage ? (
        <Card className="mt-0.5 h-[126px] p-5 flex-center">
          <Loader />
        </Card>
      ) : isError || !videos.length ? (
        <Card className="mt-0.5 h-[126px] p-5 flex-center">
          <div className="flex flex-col flex-center">
            <img src="/images/icons/sad.svg" width={56} />
            <div className="mt-2 text-sm">Looks like you don't have any videos on this list.</div>
          </div>
        </Card>
      ) : !!videos.length && videos[0].id ? (
        <div className={cn({ 'cursor-wait opacity-50': isFetching })}>
          {videos.map((video, index) => (
            <YoutubeVideoCard
              key={video.id || index}
              video={video}
              onSelect={selectedVideosSet.toggle}
              isSelected={selectedVideosSet.has(video.id)}
            />
          ))}
          {typeof isFetchingNextPage === 'undefined' && (
            <YoutubePagination className="bg-white px-4" {...paging} />
          )}
        </div>
      ) : null}

      {isFetchingNextPage && (
        <Card className="mt-0.5 h-[126px] p-5 flex-center">
          <Loader />
        </Card>
      )}

      {selectedVideos.size > 1 && (
        <div
          className="fixed bottom-0 left-0 flex w-full bg-white text-black1"
          style={{ boxShadow: '0px -2px 8px rgba(122, 142, 178, 0.20)' }}
        >
          <PageContainer className="px-4">
            <div className="flex h-[60px] items-center justify-between gap-2">
              <div className="hidden text-sm font-semibold md:block">Bulk Blog Generation</div>

              <div className="flex items-center gap-4">
                <div className="text-sm">
                  {selectedVideos.size} videos selected.{' '}
                  <span className="hidden lg:inline">Generate blogs for the selected videos.</span>
                </div>
                <Button onClick={toggleBlogCreate}>Generate Blog</Button>
              </div>

              <Button variant="secondary" onClick={selectedVideosSet.reset}>
                <div className="mr-2 hidden xs:block">Clear Selection</div>
                <IoMdCloseCircle size={18} />
              </Button>
            </div>
          </PageContainer>

          <YoutubeBlogCreateDialog
            video={
              videos.find((v) => selectedVideos.values().next().value === v.id) as YoutubeVideo
            }
            videoIds={Array.from(selectedVideos)}
            isBlogCreate={isBlogCreate}
            toggleBlogCreate={toggleBlogCreate}
          />
        </div>
      )}
    </div>
  );
};

export default YoutubeVideoList;
