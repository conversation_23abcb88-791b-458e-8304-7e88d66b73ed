import type {
  YoutubeContentGenerateRequest,
  YoutubeVideoContents,
  YoutubeContentTypes,
} from '@/types/resources/youtube-contents.type';

import { useMutation } from 'react-query';

import { API } from '@/services/api';

export const allGenerateTypes: YoutubeContentTypes[] = [
  'title',
  'description',
  'chapters',
  'tag',
  'hashtag',
];

export const useYoutubeContentGenerate = (
  video: YoutubeVideo,
  { onSuccess }: { onSuccess?: () => void } = {}
) => {
  const {
    mutateAsync: generateContent,
    data,
    error: generateErrorMessage,
    isLoading: isGenerating,
    isError: isGenerateError,
  } = useMutation({
    mutationFn: ({ language, generateTypes, ...reqBody }: YoutubeContentGenerateRequest) =>
      API.post<YoutubeVideoContents>(`youtube/${video.id}/contents`, {
        generateTypes: !!generateTypes.length
          ? generateTypes
          : allGenerateTypes.filter((g) => !video.data?.generateTypes.includes(g)),
        language: language || video.data?.language,
        ...reqBody,
      }),
    onSuccess: () => onSuccess && onSuccess(),
  });

  return {
    generateContent,
    data,
    generateErrorMessage,
    isGenerateError,
    isGenerating,
  };
};

export const useYoutubeContentGenerateMore = (
  videoId: string,
  { onSuccess }: { onSuccess?: () => void } = {}
) => {
  const {
    mutateAsync: generateMoreContent,
    data,
    error: generateErrorMessage,
    isLoading: isGeneratingMore,
    isError: isGenerateError,
  } = useMutation({
    mutationFn: (contentType: YoutubeContentTypes) =>
      API.post<YoutubeVideoContents>(`youtube/${videoId}/contents/generate-more`, { contentType }),
    onSuccess: () => onSuccess && onSuccess(),
  });

  return {
    generateMoreContent,
    data,
    generateErrorMessage,
    isGeneratingMore,
    isGenerateError,
  };
};

export const useYoutubeContentFavorite = (
  videoId: string,
  { onSuccess }: { onSuccess?: () => void } = {}
) => {
  const {
    mutateAsync: setFavorite,
    data,
    error,
    isLoading: isSaving,
    isError,
  } = useMutation({
    mutationFn: (contentId: string) =>
      API.put<{ success: boolean }>(`youtube/${videoId}/contents/${contentId}/favorite`),
    onSuccess: () => onSuccess && onSuccess(),
  });

  return {
    setFavorite,
    data,
    error,
    isError,
    isSaving,
  };
};

export const useYoutubeContentDelete = (
  videoId: string,
  { onSuccess }: { onSuccess?: () => void } = {}
) => {
  const {
    mutateAsync: deselectContent,
    data,
    error,
    isLoading: isSaving,
    isError,
  } = useMutation({
    mutationFn: (contentId: string) =>
      API.remove<{ success: boolean }>(`youtube/${videoId}/contents/${contentId}`),
    onSuccess: () => onSuccess && onSuccess(),
  });

  return {
    deselectContent,
    data,
    error,
    isError,
    isSaving,
  };
};
