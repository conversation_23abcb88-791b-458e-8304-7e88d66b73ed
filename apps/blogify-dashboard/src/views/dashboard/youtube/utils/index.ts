import type {
  YoutubeVideoThumbnailQuality,
  YoutubeChannel,
  YoutubeVideo,
  YoutubeBase,
} from '@/types/integrations/youtube.type';
import type { YoutubeVideoChapter } from '@/types/resources/youtube-contents.type';

export const getYoutubeUrlFromId = (id: string, short: boolean = false) =>
  short ? `https://youtu.be/${id}` : `https://youtube.com/watch?v=${id}`;

export const getThumbnail = (
  item: YoutubeBase,
  preferred: YoutubeVideoThumbnailQuality = 'medium'
) => {
  const thumbnails = item?.snippet?.thumbnails;
  let thumbnail;

  if (preferred) {
    thumbnail = thumbnails?.[preferred]?.url;
  }

  return (
    thumbnail ||
    thumbnails?.high?.url ||
    thumbnails?.medium?.url ||
    thumbnails?.standard?.url ||
    thumbnails?.default?.url
  );
};

export const formatChaptersForCopy = (chapters: YoutubeVideoChapter[]): string =>
  chapters.map((c) => `${c.timestamp} ${c.title}`).join('\n');

export const isOwnChannelVideo = (channel: YoutubeChannel, video: YoutubeVideo) =>
  channel.id === video.snippet.channelId || channel.snippet.title === video.snippet.channelTitle;
