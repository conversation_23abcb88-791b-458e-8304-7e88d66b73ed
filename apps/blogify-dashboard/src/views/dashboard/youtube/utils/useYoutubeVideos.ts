import type { YoutubeResponse, YoutubeVideo } from '@/types/integrations/youtube.type';
import type { PagingProps } from '../components/YoutubePagination';

import { useQuery } from 'react-query';
import { useState } from 'react';

import { CACHE_TIME } from '@/constants';
import { parseQuery } from '@/utils';

const useYoutubeVideos = (
  url: string,
  enabled: boolean
): {
  videos: YoutubeVideo[];
  isFetching: boolean;
  isLoading: boolean;
  isError: boolean;
} & PagingProps => {
  const { pageToken: pt, pageSize: ps, current: cr } = parseQuery();

  const [pageSize, setPageSize] = useState(parseInt(ps, 10) || 10);
  const [current, setCurrent] = useState(parseInt(cr, 10) || 1);
  const [pageToken, setPageToken] = useState(pt || '');

  const {
    isFetching,
    isLoading,
    isError,
    data: { items: videos, nextPageToken, prevPageToken, pageInfo: { totalResults } } = {
      items: [],
      pageInfo: { totalResults: 0 },
    },
  } = useQuery<YoutubeResponse<YoutubeVideo>>(
    `${url}?maxResults=${pageSize}&pageToken=${pageToken}`,
    {
      staleTime: CACHE_TIME.short,
      retry: 0,
      enabled,
      onSuccess: (resp) => resp.items.forEach((v) => window.youtubeVideos.set(v.id, v)),
    }
  );

  return {
    videos,
    isFetching,
    isLoading,
    isError,
    // Paging
    total: totalResults,
    prevPageToken,
    nextPageToken,
    pageSize,
    current,
    setPageToken,
    setPageSize,
    setCurrent,
  };
};

export default useYoutubeVideos;
