import type { YoutubeVideoContent } from '@/types/resources/youtube-contents.type';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { useEffect } from 'react';
import { useQuery } from 'react-query';

import { youtubeEvents } from '@/services/event';

import { SOCIAL_PLATFORMS } from './constants';
import useYoutubeVideo from './useYoutubeVideo';

const useYoutubeContent = <T = string>(
  type: 'thumbnail' | 'short' | 'social-post'
): {
  video: YoutubeVideo;
  contents: YoutubeVideoContent<T>[];
  refetchContents: Func;
} => {
  const { video, refetchVideo } = useYoutubeVideo();

  const { data: contents = [], refetch: refetchContents } = useQuery<YoutubeVideoContent<T>[]>(
    `youtube/${video.id}/contents/${type}`,
    { enabled: !!video.id }
  );

  useEffect(() => {
    const subscription = youtubeEvents.subscribe('YOUTUBE_CONTENT_UPDATE', (content) => {
      if (
        content?.contentType === type ||
        (type === 'social-post' &&
          SOCIAL_PLATFORMS.map((sp) => sp.value).includes(content?.contentType))
      ) {
        refetchContents();
        refetchVideo();
      }
    });

    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    video,
    contents,
    refetchContents,
  };
};

export default useYoutubeContent;
