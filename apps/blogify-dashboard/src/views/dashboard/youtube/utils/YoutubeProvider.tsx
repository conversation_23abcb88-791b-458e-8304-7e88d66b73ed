import type { YoutubeResponse, YoutubeChannel } from '@/types/integrations/youtube.type';

import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import { debounce } from 'lodash';

import { cacheGet, cacheSet } from '@/utils/localStorageCache';
import { useStoreState } from '@/store';
import { emptyChannel } from '@/types/integrations/youtube.type';
import { CACHE_TIME } from '@/constants';

import { YoutubeContextProvider } from './useYoutube';

export default function YoutubeProvider({ children }: { children: React.ReactNode }) {
  const user = useStoreState((s) => s.user.current);

  const { data: { items: channels } = { items: [emptyChannel] } } = useQuery<
    YoutubeResponse<YoutubeChannel>
  >(`youtube/channels`, {
    enabled: !!user.youtubeConnect,
    staleTime: CACHE_TIME.short,
    retry: 0,
    onSuccess: (resp) => {
      const selectedChannelId = cacheGet('youtube_selected_channel_id');
      const _channel = resp.items.find((c) => c.id === selectedChannelId);
      onChannelChange(_channel || resp.items[0]);
    },
  });

  const navigate = useNavigate();
  const { q } = useParams();
  const [channel, setChannel] = useState<YoutubeChannel>(channels?.[0] || emptyChannel);
  const [search, setSearch] = useState(q || '');

  const onSearch = debounce((_q: string) => {
    setSearch(_q);
    navigate(_q ? `/dashboard/youtube/search/${_q}` : `/dashboard/youtube`);
  }, 500);

  const onChannelChange = (_channel: YoutubeChannel) => {
    cacheSet('youtube_selected_channel_id', _channel.id);
    setChannel(_channel);
  };

  const value = {
    channels,
    channel,
    onChannelChange,
    search,
    onSearch,
  };

  return (
    <YoutubeContextProvider.Provider value={value}>{children}</YoutubeContextProvider.Provider>
  );
}
