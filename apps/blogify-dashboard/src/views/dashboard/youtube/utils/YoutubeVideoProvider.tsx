import type { YoutubeVideoContents } from '@/types/resources/youtube-contents.type';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';

import { BLOG_LANGUAGES } from '@/views/dashboard/blog/create/utils/languages';
import { useStoreState } from '@/store';
import { emptyVideo } from '@/types/integrations/youtube.type';
// import { CACHE_TIME } from '@/constants';

import { YoutubeVideoContextProvider } from './useYoutubeVideo';

export default function YoutubeVideoProvider({ children }: { children: React.ReactNode }) {
  const user = useStoreState((s) => s.user.current);
  const { videoId } = useParams();
  const navigate = useNavigate();

  // const videoInCache = videoId && window.youtubeVideos.get(videoId);
  const { data: videoFromAPI = emptyVideo, refetch: refetchVideo } = useQuery<YoutubeVideo>(
    `youtube/videos/${videoId}`,
    {
      enabled: !!(user.youtubeConnect && videoId), //  && !videoInCache
      // staleTime: CACHE_TIME.short,
      retry: 0,
    }
  );

  const video = videoFromAPI; // videoInCache ||
  const { data: videoContents = {}, refetch: refetchContents } = useQuery<YoutubeVideoContents>(
    `youtube/${videoId}/contents`,
    { enabled: !!(user.youtubeConnect && video.id && video?.status?.privacyStatus !== 'private') }
  );

  if (video?.status?.privacyStatus === 'private') navigate('/dashboard/youtube');

  const detectedLanguage =
    BLOG_LANGUAGES.find((l) => l.iso && l.iso === video.snippet?.defaultAudioLanguage)?.value || '';

  const value = {
    detectedLanguage,
    refetchContents,
    videoContents,
    refetchVideo,
    video,
  };

  return (
    <YoutubeVideoContextProvider.Provider value={value}>
      {children}
    </YoutubeVideoContextProvider.Provider>
  );
}
