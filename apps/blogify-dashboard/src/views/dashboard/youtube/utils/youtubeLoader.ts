import type { User } from '@/types/resources/user.type';

import { redirect } from 'react-router-dom';

import Store from '@/store';

const getCurrentUser = async (): Promise<User> => {
  const user = Store.getState().user.current;
  return user._id ? user : Store.getActions().user.fetch();
};

export const youtubeAccessLoader = async () => {
  const user = await getCurrentUser();
  if (!user.hasYouTubeAddon) {
    return redirect('/dashboard/youtube/purchase-addon');
  }

  return null;
};

export const youtubeAddonPurchaseLoader = async () => {
  const user = await getCurrentUser();
  if (user.hasYouTubeAddon) {
    return redirect('/dashboard/youtube');
  }

  return null;
};
