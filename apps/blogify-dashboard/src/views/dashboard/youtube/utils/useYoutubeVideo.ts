import type { YoutubeVideoContents } from '@/types/resources/youtube-contents.type';
import type { YoutubeVideo } from '@/types/integrations/youtube.type';

import { createContext, useContext } from 'react';

import { emptyVideo } from '@/types/integrations/youtube.type';

interface YoutubeVideoContext {
  video: YoutubeVideo;
  refetchVideo: Func;
  videoContents: YoutubeVideoContents;
  refetchContents: Func;
  detectedLanguage: string;
}

const defaultContext: YoutubeVideoContext = {
  video: emptyVideo,
  refetchVideo: () => {},
  videoContents: {},
  refetchContents: () => {},
  detectedLanguage: '',
};

const YoutubeVideoContextProvider = createContext<YoutubeVideoContext>(defaultContext);
YoutubeVideoContextProvider.displayName = 'YoutubeVideoContext';

const useYoutubeVideo = () => {
  const context = useContext(YoutubeVideoContextProvider);

  if (context === undefined) {
    throw new Error('useYoutubeVideo must be used within a YoutubeVideoProvider');
  }

  return context;
};

export default useYoutubeVideo;
export { YoutubeVideoContextProvider };
