import type { YoutubeChannel } from '@/types/integrations/youtube.type';

import { createContext, useContext } from 'react';

import { emptyChannel } from '@/types/integrations/youtube.type';

interface YoutubeContext {
  // Channel
  channels: YoutubeChannel[];
  channel: YoutubeChannel;
  onChannelChange: (yc: YoutubeChannel) => void;
  // Search
  search: string;
  onSearch: (q: string) => void;
}

const defaultFn = () => {};
const defaultContext: YoutubeContext = {
  channels: [emptyChannel],
  channel: emptyChannel,
  onChannelChange: defaultFn,
  search: '',
  onSearch: defaultFn,
};

const YoutubeContextProvider = createContext<YoutubeContext>(defaultContext);
YoutubeContextProvider.displayName = 'YoutubeContext';

const useYoutube = () => {
  const context = useContext(YoutubeContextProvider);

  if (context === undefined) {
    throw new Error('useYoutube must be used within a YoutubeProvider');
  }

  return context;
};

export default useYoutube;
export { YoutubeContextProvider };
