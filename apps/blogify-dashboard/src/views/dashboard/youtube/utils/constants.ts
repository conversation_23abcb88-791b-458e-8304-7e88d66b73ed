import type { YoutubeContentTypes } from '@/types/resources/youtube-contents.type';
import type { IconType } from 'react-icons';

import { FaLinkedin, FaFacebook } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';

export const CONTENT_COUNT_MAX_PER_TYPE: Record<YoutubeContentTypes, number> = {
  title: 10,
  description: 5,
  tag: 20,
  hashtag: 20,
  summary: 1,
  chapters: 1,
  thumbnail: 6,
  short: 30,
  transcript: 1,
  'social-post': 15,
  social_post_facebook: 5,
  social_post_twitter: 5,
  social_post_linkedin: 5,
};

export const SOCIAL_PLATFORMS: {
  Icon: IconType;
  title: 'facebook' | 'twitter' | 'linkedin';
  value: YoutubeContentTypes;
  text: string;
}[] = [
  { Icon: FaFacebook, title: 'facebook', value: 'social_post_facebook', text: '' },
  { Icon: FaXTwitter, title: 'twitter', value: 'social_post_twitter', text: '' },
  { Icon: FaLinkedin, title: 'linkedin', value: 'social_post_linkedin', text: '' },
];
