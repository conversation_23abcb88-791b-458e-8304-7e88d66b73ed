import { useParams } from 'react-router-dom';

import YoutubeVideoList from '../components/YoutubeVideoList';
import useYoutubeVideos from '../utils/useYoutubeVideos';

const YoutubePlaylistVideos = () => {
  const { playlistId } = useParams();

  const url = `youtube/playlists/${playlistId}/videos`;
  const youtubeData = useYoutubeVideos(url, !!playlistId);

  return <YoutubeVideoList {...youtubeData} />;
};

export default YoutubePlaylistVideos;
