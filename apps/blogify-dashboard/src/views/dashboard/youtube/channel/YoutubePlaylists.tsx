import type { YoutubeResponse, YoutubePlaylist } from '@/types/integrations/youtube.type';

import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useParams, Outlet } from 'react-router-dom';
import { MdChevronLeft, MdChevronRight } from 'react-icons/md';
import { useInfiniteQuery } from 'react-query';
import { useToggle } from 'react-use';

import { CACHE_TIME } from '@/constants';
import { Button } from '@ps/ui/components/button';
import { Card } from '@/components/layout';
import Loader from '@/components/misc/Loader';

import YoutubePlaylistCard from '../components/YoutubePlaylistCard';
import useYoutube from '../utils/useYoutube';

const YoutubePlaylists = () => {
  const [selectedPlaylistId, setSelectedPlaylistId] = useState('');
  const [isExpandedView, toggleExpandedView] = useToggle(false);
  const { playlistId } = useParams();
  const { channel } = useYoutube();
  const navigate = useNavigate();

  const url = `youtube/channels/${channel?.id}/playlists`;
  const { data, isFetchingNextPage, isLoading, isError, fetchNextPage } = useInfiniteQuery<
    YoutubeResponse<YoutubePlaylist>
  >(url, {
    enabled: !!channel?.id,
    staleTime: CACHE_TIME.short,
    retry: 0,
    getNextPageParam: (lastPage) => ({ pageToken: lastPage.nextPageToken }),
  });

  const hasMorePage = !!data?.pages?.[data?.pages.length - 1]?.nextPageToken;
  const playlists = (data?.pages || []).reduce((arr, d) => {
    arr.push(...d.items);
    return arr;
  }, [] as YoutubePlaylist[]);

  const onSelectPlaylist = (_playlistId: string) => {
    setSelectedPlaylistId(_playlistId);
    if (isExpandedView) toggleExpandedView();
    setTimeout(() => {
      const playlistCard = document.querySelector(`#playlist-${_playlistId}`);
      if (playlistCard) {
        playlistCard.scrollIntoView({ inline: 'center', block: 'nearest' });
      }
    }, 300);
    return navigate(`/dashboard/youtube/playlists/${_playlistId}`);
  };

  useEffect(() => {
    if (playlistId || playlists?.[0]?.id) {
      onSelectPlaylist(playlistId || playlists?.[0]?.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playlists?.[0]?.id]);

  return (
    <>
      <Card className="mt-0.5 py-3">
        {isLoading ? (
          <div className="flex flex-center">
            <Loader className="!size-[148px]" />
          </div>
        ) : isError || !playlists.length ? (
          <div className="flex h-[148px] p-5 flex-center">
            <div className="flex flex-col flex-center">
              <img src="/images/icons/sad.svg" width={72} />
              <div className="mt-2 text-sm">Looks like you don't have any playlist.</div>
            </div>
          </div>
        ) : !!playlists.length && playlists[0].id ? (
          <>
            {isExpandedView ? (
              <div className="px-4 xl:px-11">
                <div className="grid grid-cols-2 gap-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-3 xl:grid-cols-5 2xl:grid-cols-7">
                  {playlists.map((playlist, index) => (
                    <YoutubePlaylistCard
                      key={index}
                      playlist={playlist}
                      onSelectPlaylist={onSelectPlaylist}
                      selected={playlist.id === selectedPlaylistId}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <YoutubePlaylistSlider>
                {playlists.map((playlist, index) => (
                  <YoutubePlaylistCard
                    id={`playlist-${playlist.id}`}
                    key={index}
                    playlist={playlist}
                    onSelectPlaylist={onSelectPlaylist}
                    selected={playlist.id === selectedPlaylistId}
                  />
                ))}
              </YoutubePlaylistSlider>
            )}

            <div className="flex gap-3 flex-center">
              <Button size="sm" color="blue" variant="ghost" onClick={toggleExpandedView}>
                {isExpandedView ? 'Show Less' : 'Expand'}
              </Button>
              {isExpandedView && hasMorePage && (
                <Button
                  size="sm"
                  onClick={() => fetchNextPage()}
                  loading={isFetchingNextPage}
                  variant="ghost"
                  color="blue"
                >
                  View More
                </Button>
              )}
            </div>
          </>
        ) : null}
      </Card>

      <Outlet />
    </>
  );
};

const YoutubePlaylistSlider = ({ children }: { children: React.ReactNode }) => {
  const scrollContainer = useRef<HTMLDivElement>(null);

  const onScroll = (to: 'right' | 'left') => {
    if (scrollContainer.current) {
      const { offsetWidth } = scrollContainer.current;
      scrollContainer.current.scrollBy({
        left: to === 'right' ? offsetWidth : -offsetWidth,
        behavior: 'smooth',
      });
    }
  };

  return (
    <div className="flex px-4 lg:px-2">
      <div className="hidden lg:block">
        <Button className="mt-6 min-w-9" onClick={() => onScroll('left')} variant="icon">
          <div className="mt-1 text-gray2">
            <MdChevronLeft size={24} />
          </div>
        </Button>
      </div>

      <div className="flex w-full gap-1 overflow-x-auto" ref={scrollContainer}>
        {children}
      </div>

      <div className="hidden lg:block">
        <Button className="mt-6 min-w-9" onClick={() => onScroll('right')} variant="icon">
          <div className="mt-1 text-gray2">
            <MdChevronRight size={24} />
          </div>
        </Button>
      </div>
    </div>
  );
};

export default YoutubePlaylists;
