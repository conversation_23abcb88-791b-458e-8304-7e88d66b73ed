import YoutubeVideoLists from '../components/YoutubeVideoList';
import useYoutubeVideos from '../utils/useYoutubeVideos';
import useYoutube from '../utils/useYoutube';

const YoutubeUploads = () => {
  const { channel } = useYoutube();

  const playlistId = channel?.contentDetails?.relatedPlaylists?.uploads;
  const url = `youtube/playlists/${playlistId}/videos`;

  const youtubeData = useYoutubeVideos(url, !!(channel?.id && playlistId));

  return <YoutubeVideoLists {...youtubeData} />;
};

export default YoutubeUploads;
