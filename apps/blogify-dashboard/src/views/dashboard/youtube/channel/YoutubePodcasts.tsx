import YoutubeVideoLists from '../components/YoutubeVideoList';
import useYoutubeVideos from '../utils/useYoutubeVideos';
import useYoutube from '../utils/useYoutube';

const YoutubePodcasts = () => {
  const { channel } = useYoutube();

  const url = `youtube/channels/${channel?.id}/podcasts`;
  const youtubeData = useYoutubeVideos(url, !!channel?.id);

  return <YoutubeVideoLists {...youtubeData} />;
};

export default YoutubePodcasts;
