import YoutubeVideoLists from '../components/YoutubeVideoList';
import useYoutubeVideos from '../utils/useYoutubeVideos';
import useYoutube from '../utils/useYoutube';

const YoutubeShorts = () => {
  const { channel } = useYoutube();

  const url = `youtube/channels/${channel?.id}/shorts`;
  const youtubeData = useYoutubeVideos(url, !!channel?.id);

  return <YoutubeVideoLists {...youtubeData} />;
};

export default YoutubeShorts;
