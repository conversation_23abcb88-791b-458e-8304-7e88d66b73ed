/* eslint-disable react-refresh/only-export-components */
import type { RouteObject } from 'react-router-dom';

import { Suspense } from 'react';

import { RouteErrorBoundary } from '@/components/error';
import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';

import { youtubeAddonPurchaseLoader, youtubeAccessLoader } from './utils/youtubeLoader';

const YoutubeAddonPurchase = safeLazy(() => import('../youtube/YoutubeAddonPurchase'));

// YouTube Channel
const YoutubePlaylistVideos = safeLazy(() => import('./channel/YoutubePlaylistVideos'));
const YoutubeRecentContents = safeLazy(() => import('./YoutubeRecentContents'));
const YoutubePlaylists = safeLazy(() => import('./channel/YoutubePlaylists'));
const YoutubePodcasts = safeLazy(() => import('./channel/YoutubePodcasts'));
const YoutubeChannel = safeLazy(() => import('./YoutubeChannel'));
const YoutubeUploads = safeLazy(() => import('./channel/YoutubeUploads'));
const YoutubeSearch = safeLazy(() => import('./YoutubeSearch'));
const YoutubeShorts = safeLazy(() => import('./channel/YoutubeShorts'));
const YoutubeLives = safeLazy(() => import('./channel/YoutubeLives'));

// YouTube Video
const YoutubeVideoDescription = safeLazy(() => import('./video/YoutubeVideoDescription'));
const YoutubeVideoSocialPosts = safeLazy(() => import('./video/YoutubeVideoSocialPosts'));
const YoutubeVideoThumbnails = safeLazy(() => import('./video/YoutubeVideoThumbnails'));
const YoutubeVideoTranscript = safeLazy(() => import('./video/YoutubeVideoTranscript'));
const YoutubeVideoChapters = safeLazy(() => import('./video/YoutubeVideoChapters'));
const YoutubeVideoOverview = safeLazy(() => import('./video/YoutubeVideoOverview'));
const YoutubeVideoSummary = safeLazy(() => import('./video/YoutubeVideoSummary'));
const YoutubeVideoShorts = safeLazy(() => import('./video/YoutubeVideoShorts'));
const YoutubeVideoTitles = safeLazy(() => import('./video/YoutubeVideoTitles'));
const YoutubeVideoBlogs = safeLazy(() => import('./video/YoutubeVideoBlogs'));
const YoutubeVideoTags = safeLazy(() => import('./video/YoutubeVideoTags'));
const YoutubeVideo = safeLazy(() => import('./YoutubeVideo'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);

const getRoutes = (): RouteObject[] => [
  {
    path: 'youtube/purchase-addon',
    element: <Lazy as={YoutubeAddonPurchase} />,
    loader: () => youtubeAddonPurchaseLoader(),
    errorElement: <RouteErrorBoundary />,
  },
  {
    path: 'youtube',
    element: <Lazy as={YoutubeChannel} />,
    loader: () => youtubeAccessLoader(),
    errorElement: <RouteErrorBoundary />,
    children: [
      { path: '', element: <Lazy as={YoutubeUploads} /> },
      {
        path: 'playlists',
        element: <Lazy as={YoutubePlaylists} />,
        children: [{ path: ':playlistId', element: <Lazy as={YoutubePlaylistVideos} /> }],
      },
      { path: 'podcasts', element: <Lazy as={YoutubePodcasts} /> },
      { path: 'live', element: <Lazy as={YoutubeLives} /> },
      { path: 'shorts', element: <Lazy as={YoutubeShorts} /> },
      { path: 'search/:q', element: <Lazy as={YoutubeSearch} /> },
      { path: 'recent', element: <Lazy as={YoutubeRecentContents} /> },
    ],
  },
  {
    path: 'youtube/videos/:videoId',
    element: <Lazy as={YoutubeVideo} />,
    loader: () => youtubeAccessLoader(),
    errorElement: <RouteErrorBoundary />,
    children: [
      { path: '', element: <Lazy as={YoutubeVideoOverview} /> },
      { path: 'title', element: <Lazy as={YoutubeVideoTitles} /> },
      { path: 'description', element: <Lazy as={YoutubeVideoDescription} /> },
      { path: 'chapters', element: <Lazy as={YoutubeVideoChapters} /> },
      { path: 'tags', element: <Lazy as={YoutubeVideoTags} /> },
      { path: 'thumbnails', element: <Lazy as={YoutubeVideoThumbnails} /> },
      { path: 'shorts', element: <Lazy as={YoutubeVideoShorts} /> },
      { path: 'summary', element: <Lazy as={YoutubeVideoSummary} /> },
      { path: 'transcript', element: <Lazy as={YoutubeVideoTranscript} /> },
      { path: 'blogs', element: <Lazy as={YoutubeVideoBlogs} /> },
      { path: 'posts', element: <Lazy as={YoutubeVideoSocialPosts} /> },
    ],
  },
];

export default getRoutes;
