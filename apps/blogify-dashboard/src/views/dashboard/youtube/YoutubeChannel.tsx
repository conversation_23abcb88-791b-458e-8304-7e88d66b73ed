import { Outlet } from 'react-router-dom';

import { useStoreState } from '@/store';

import YoutubeSearchAndFilter from './components/YoutubeSearchAndFilter';
import YoutubeChannelHeader from './components/YoutubeChannelHeader';
import YoutubeProvider from './utils/YoutubeProvider';
import YoutubeConnect from './components/YoutubeConnect';
import { useYoutubeTourGuideContext } from '@/context/YoutubeTourGuide';
import { useMount } from 'react-use';

const YoutubeChannel = () => {
  const user = useStoreState((s) => s.user.current);
  const isConnected = !!user.youtubeConnect;

  // youtube connect tour guide
  const {
    setState,
    state: { tourActive },
  } = useYoutubeTourGuideContext();
  useMount(() => {
    if (tourActive && !isConnected) {
      setState({ run: true });
    }
  });

  return (
    <YoutubeProvider>
      {isConnected ? (
        <>
          <YoutubeChannelHeader />
          <YoutubeSearchAndFilter />
          <Outlet />
        </>
      ) : (
        <YoutubeConnect />
      )}
    </YoutubeProvider>
  );
};

export default YoutubeChannel;
