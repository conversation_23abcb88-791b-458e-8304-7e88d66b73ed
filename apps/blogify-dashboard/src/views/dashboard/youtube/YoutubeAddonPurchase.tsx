import type { APIResponseType } from '@/types/resources';
import type { Product } from '@/types/resources/product.type';

import { useQuery } from 'react-query';

import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';
import AddonCard from '../addons/components/AddonCard';

export default function YoutubePurchaseAddon() {
  const { data: { data: addons } = { data: [] } } =
    useQuery<APIResponseType<Product>>('products/addons');

  const youtubeAddons = addons.filter((a) => a.name.toLowerCase().includes('youtube')) || [];

  return (
    <DashboardContainer title="YouTube Connect Addons">
      <div className="flex flex-col gap-6 md:flex-row">
        {youtubeAddons.map((addon) => (
          <div key={addon._id} className="order-1 w-full md:order-2 md:w-1/2">
            <AddonCard className="p-6" imgHeight={[176, 276, 276, 320]} addon={addon as Product} />
          </div>
        ))}
      </div>
    </DashboardContainer>
  );
}
