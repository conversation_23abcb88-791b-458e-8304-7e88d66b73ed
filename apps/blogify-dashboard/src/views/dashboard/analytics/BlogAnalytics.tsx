import {
  ClickActivity,
  ClickPerDevice,
  ClicksByCountry,
  DeviceClickActivity,
} from '@/components/commonActivity';
import { Country } from '@/components/commonActivity/activity/constants';
import { Card } from '@/components/layout';
import { AnalyticsData } from './Analytics';

type AnalyticsProps = {
  analyticsData?: AnalyticsData;
};

const BlogAnalytics = ({ analyticsData }: AnalyticsProps) => {
  const totalClicks = Object.values(analyticsData?.click?.platform || {}).reduce(
    (a, b) => a + b,
    0
  );

  const totalActions = analyticsData?.actions.reduce((a, b) => a + b[1], 0) || 0;

  const conversionRate = totalActions ? totalActions / totalClicks : 0;

  const deviceData = {
    mobile: analyticsData?.click?.platform?.mobile || 0,
    laptop: analyticsData?.click?.platform?.laptop || 0,
    desktop: analyticsData?.click?.platform?.desktop || 0,
    other: Object.entries(analyticsData?.click?.platform || {})
      .filter(([key]) => !['mobile', 'laptop', 'desktop'].includes(key))
      .reduce((sum, [, value]) => sum + value, 0),
  };

  const countryData = Object.entries(analyticsData?.click?.country || {})
    .map(([country, value]) => ({
      country: country as keyof typeof Country,
      value: value as number,
      percentage: totalClicks ? (value / totalClicks) * 100 : 0,
    }))
    .filter((item) => item.country in Country)
    .sort((a, b) => b.value - a.value) // Sort by highest value first
    .slice(0, 4); // Take top 4 countries

  return (
    <>
      <div className="flex flex-col border-t-2 sm:flex-row">
        {/* First Column */}
        <div className="order-1 w-full p-2 sm:w-1/2 lg:w-1/3">
          <ClickActivity
            count={{
              clicks: totalClicks,
              actions: totalActions,
              cr: conversionRate,
            }}
            labels={{
              clicks: 'Blogs Affiliate Link Clicked',
              actions: 'Projected Affiliate Sales',
              conversionRate: 'Conversion Rate',
            }}
          />
        </div>

        {/* Second Column */}
        <div className="order-2  w-full  p-2 sm:w-1/2 md:border-x-2 lg:w-1/3">
          <DeviceClickActivity counts={deviceData} />
        </div>

        {/* Third Column */}
        <div className="order-3 w-full p-2 sm:w-1/2 lg:w-1/3">
          <Card className="h-full">
            <ClickPerDevice counts={deviceData} />
          </Card>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="flex flex-col border-t-2">
        <ClicksByCountry data={countryData} />
      </div>
    </>
  );
};

export default BlogAnalytics;
