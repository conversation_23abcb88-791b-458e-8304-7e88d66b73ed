import { MdAdsClick } from 'react-icons/md';

import AnalyticsCard from './AnalyticsCard';

const AnalyticsTotalClick = ({ totalClicks }: { totalClicks: number }) => (
  <AnalyticsCard title="Total Clicks">
    <div className="my-10 flex flex-col text-primary flex-center">
      <MdAdsClick size={64} />

      <div className="mt-3 text-3xl font-bold">
        {totalClicks ? totalClicks.toLocaleString() : '- -'}
      </div>
    </div>
  </AnalyticsCard>
);

export default AnalyticsTotalClick;
