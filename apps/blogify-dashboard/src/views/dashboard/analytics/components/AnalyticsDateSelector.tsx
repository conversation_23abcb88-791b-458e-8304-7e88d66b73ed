import type { Dayjs } from 'dayjs';

import { MdCalendarMonth } from 'react-icons/md';
import { useState } from 'react';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import dayjs from 'dayjs';

import { PopoverContent, PopoverTrigger, PopoverClose, Popover } from '@ps/ui/components/popover';
import { TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import FormField from '@ps/ui/form/FormField';

dayjs.extend(localizedFormat);
dayjs.extend(advancedFormat);

type RangeLabel = 'Today' | '7 Days' | '30 Days' | '3 Months';
type Range = [Dayjs, Dayjs];

export default function AnalyticsDateSelector({
  onChange,
  className,
}: {
  onChange: React.Dispatch<Range>;
  className?: string;
}) {
  const predefinedRanges: Record<RangeLabel, Range> = {
    Today: [dayjs().startOf('day'), dayjs().endOf('day')],
    '7 Days': [dayjs().subtract(7, 'day').startOf('day'), dayjs().endOf('day')],
    '30 Days': [dayjs().subtract(30, 'day').startOf('day'), dayjs().endOf('day')],
    '3 Months': [dayjs().subtract(3, 'month').startOf('day'), dayjs().endOf('day')],
  };

  const [predefinedRange, setPredefinedRange] = useState<RangeLabel | ''>('Today');
  const [dateRange, setDateRange] = useState<Range>(predefinedRanges.Today);

  const handleRangeChange = (label: RangeLabel) => {
    setPredefinedRange(label);
    const range = predefinedRanges[label];
    if (range) {
      setDateRange(range);
      onChange(range);
    }
  };

  return (
    <div className={cn('flex flex-wrap gap-4', className)}>
      <Tabs value={predefinedRange} onValueChange={(v) => handleRangeChange(v as RangeLabel)}>
        <TabsList>
          {Object.keys(predefinedRanges).map((k, i) => (
            <TabsTrigger className="py-[5px] text-13" key={i} value={k}>
              {k}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="secondary">
            <MdCalendarMonth />

            <span className="text-13">
              {dayjs(dateRange[0]).format('MMM D, YYYY')} -{' '}
              {dayjs(dateRange[1]).format('MMM D, YYYY')}
            </span>
          </Button>
        </PopoverTrigger>

        <PopoverContent
          className="w-fit rounded-lg border bg-white p-4 shadow-lg"
          sideOffset={8}
          align="end"
        >
          <div className="flex gap-4">
            <FormField
              label="Start Date"
              onChange={(e) => setDateRange([dayjs(e.target.value), dateRange[1]])}
              value={dayjs(dateRange[0]).format('YYYY-MM-DD')}
              type="date"
            />
            <FormField
              label="End Date"
              onChange={(e) => setDateRange([dateRange[0], dayjs(e.target.value)])}
              value={dayjs(dateRange[1]).format('YYYY-MM-DD')}
              type="date"
            />
          </div>
          <div className="flex justify-end ">
            <PopoverClose>
              <Button
                onClick={() => {
                  const range: Range = [dateRange[0], dateRange[1]];
                  setPredefinedRange('');
                  onChange(range);
                }}
              >
                Apply
              </Button>
            </PopoverClose>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
