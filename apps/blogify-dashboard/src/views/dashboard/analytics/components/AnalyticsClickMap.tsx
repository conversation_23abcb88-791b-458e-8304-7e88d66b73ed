import WorldMap from '@/components/chart/WorldMap';

import AnalyticsCard from './AnalyticsCard';

const AnalyticsClickMap = ({ clickByCountry }: { clickByCountry: Record<string, number> }) => (
  <div className="flex flex-center">
    <WorldMap clickByCountry={clickByCountry} />
  </div>
);

const AnalyticsClickMapCard = ({ clickByCountry }: { clickByCountry: Record<string, number> }) => (
  <AnalyticsCard title="Clicked From">
    <AnalyticsClickMap clickByCountry={clickByCountry} />
  </AnalyticsCard>
);

export { AnalyticsClickMap };
export default AnalyticsClickMapCard;
