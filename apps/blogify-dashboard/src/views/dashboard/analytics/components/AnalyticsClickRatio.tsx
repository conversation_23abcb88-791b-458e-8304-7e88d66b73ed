import type { DeviceAnalytics } from '@/types/resources/analytics.type';

import Donut<PERSON>hart from '@/components/chart/DonutChart';
import Theme from '@/styles/theme';

import AnalyticsCard from './AnalyticsCard';

const DOMAIN = ['Mobile', 'Laptop', 'Desktop', 'Other'];
const COLORS = [Theme.colors.green, Theme.colors.cyan, Theme.colors.blue, Theme.colors.gold];

const AnalyticsClickRatio = ({ clickPerDevice }: { clickPerDevice: DeviceAnalytics[] }) => {
  const hasAnalytics = !!clickPerDevice.some((d) => !!d.clicks);

  return (
    <div className="flex flex-col flex-center">
      <DonutChart
        data={
          hasAnalytics
            ? clickPerDevice.map((d) => ({ label: d.device, value: d.clicks }))
            : [{ label: '- -', value: 100 }]
        }
        domain={hasAnalytics ? DOMAIN : ['- -']}
        colors={hasAnalytics ? COLORS : [Theme.colors.gray7]}
      />

      <div className="flex w-full flex-wrap justify-between flex-center">
        {DOMAIN.map((d, i) => (
          <div key={i} className="flex w-1/2 items-center flex-center lg:w-1/4">
            <div className="mr-2 size-4 rounded" style={{ backgroundColor: COLORS[i] }} />
            <div className="text-xs">{d}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const AnalyticsClickRatioCard = ({ clickPerDevice }: { clickPerDevice: DeviceAnalytics[] }) => (
  <AnalyticsCard title="Click Ratio">
    <AnalyticsClickRatio clickPerDevice={clickPerDevice} />
  </AnalyticsCard>
);

export default AnalyticsClickRatioCard;
export { AnalyticsClickRatio };
