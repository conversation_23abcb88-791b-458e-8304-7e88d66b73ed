import { DeviceAnalytics, DeviceType } from '@/types/resources/analytics.type';

import AnalyticsCard from './AnalyticsCard';

const COLORS: Record<DeviceType, string> = {
  mobile: 'green',
  laptop: 'cyan',
  desktop: 'blue',
  other: 'gold',
};

const AnalyticsClickPerDevice = ({ clickPerDevice }: { clickPerDevice: DeviceAnalytics[] }) => (
  <>
    <div className="-mb-3 mt-6 flex text-xs font-semibold uppercase text-gray2">
      <div className="w-2/4">Platform</div>
      <div className="w-1/4">Clicks</div>
      <div className="w-1/4">Ratio</div>
    </div>

    {clickPerDevice.map(({ device, clicks, ratio }, i) => (
      <div key={i} className="mt-6 flex text-sm text-black2">
        <div className="flex w-2/4 items-center">
          <div className="mr-2 size-4 rounded" style={{ backgroundColor: COLORS[device] }} />
          <div className="capitalize">{device}</div>
        </div>
        <div className="w-1/4">{clicks ? clicks.toLocaleString() : '- -'}</div>
        <div className="w-1/4">{ratio ? `${ratio.toFixed(2)}%` || 0 : '- -'}</div>
      </div>
    ))}
  </>
);

const AnalyticsClickPerDeviceCard = ({ clickPerDevice }: { clickPerDevice: DeviceAnalytics[] }) => (
  <AnalyticsCard title="Click Per Device">
    <AnalyticsClickPerDevice clickPerDevice={clickPerDevice} />
  </AnalyticsCard>
);

export default AnalyticsClickPerDeviceCard;
export { AnalyticsClickPerDevice };
