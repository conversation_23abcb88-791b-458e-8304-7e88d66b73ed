import type { CountryAnalytics } from '@/types/resources/analytics.type';

import { Md<PERSON>erson } from 'react-icons/md';

import { cn } from '@ps/ui/lib/utils';

import AnalyticsCard from './AnalyticsCard';

const AnalyticsClickTopCountries = ({
  countryAnalytics,
  showTitle = false,
}: {
  countryAnalytics: CountryAnalytics[];
  showTitle?: boolean;
}) => (
  <>
    <div className="flex items-center">
      <img
        className="w-20 object-contain"
        src={`/images/maps/${countryAnalytics.length ? countryAnalytics[0].country : 'Other'}.png`}
      />
      <div className="ml-4">
        {showTitle && (
          <div className="text-xs font-semibold uppercase text-primary">Most Clicked</div>
        )}
        <div className="font-semibold capitalize">
          {countryAnalytics.length ? countryAnalytics[0].country : '- -'}
        </div>
        <div className="mt-1 flex items-center">
          <MdPerson size={20} />
          <div className="ml-2 font-semibold">
            {countryAnalytics.length && countryAnalytics[0].clicks
              ? countryAnalytics[0].clicks.toLocaleString()
              : '- -'}
          </div>
        </div>
      </div>
    </div>

    <div className="mb-2 mt-7 text-xs font-semibold uppercase">Top Countries</div>
    {(countryAnalytics.length ? countryAnalytics : [{ country: '- -', clicks: 0, ratio: 0 }]).map(
      (d, i) => (
        <div
          key={i}
          className={cn('items-center justify-between px-3 py-2 text-sm', {
            'bg-gray7': i % 2 === 0,
            'bg-white': i % 2 !== 0,
          })}
        >
          <div className="capitalize">{d.country}</div>
          <div>{(d.ratio || 0).toFixed(0)}%</div>
        </div>
      )
    )}
  </>
);

const AnalyticsClickTopCountriesCard = ({
  countryAnalytics,
}: {
  countryAnalytics: CountryAnalytics[];
}) => (
  <AnalyticsCard>
    <AnalyticsClickTopCountries countryAnalytics={countryAnalytics} showTitle />
  </AnalyticsCard>
);

export default AnalyticsClickTopCountriesCard;
export { AnalyticsClickTopCountries };
