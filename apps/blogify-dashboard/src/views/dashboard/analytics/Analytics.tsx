import { useQuery } from 'react-query';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';

import SiteSelector, { SiteValue } from '@/components/commonActivity/activity/SiteSelector';
import { DateSelector } from '@/components/commonActivity';
import { cn } from '@ps/ui/lib/utils';
import StateHandler from '@/components/misc/StateHandler';
import DnsActivity from '@/components/commonActivity/activity/DnsActivity';

import BlogAnalytics from './BlogAnalytics';

export interface AnalyticsData {
  view: DeviceAnalytics;
  click: DeviceAnalytics;
  actions: [string, number][];
}
interface DeviceAnalytics {
  platform: Record<string, number>;
  vendor: Record<string, number>;
  browser: Record<string, number>;
  os: Record<string, number>;
  country: Record<string, number>;
  city: Record<string, number>;
}
export interface DNSActivityType {
  views: [string, number][];
  clicks: [string, number][];
  sales: [string, number][];
}
export interface BlogDataType {
  _id: string;
  title: string;
  uid: {
    _id: string;
    name: string;
    profilePicture: string;
  };
  blogifyPublishTime: Date;
}

export type PeriodType = 'Today' | '7 Days' | '30 Days' | '3 Months' | 'custom';

type AnalyticsProps = {
  className?: string;
};

const Analytics = ({ className }: AnalyticsProps) => {
  const [period, setPeriod] = useState<PeriodType>('Today');
  const [dateRange, setDateRange] = useState([dayjs(), dayjs()]);
  const [selectedSite, setSelectedSite] = useState<SiteValue>('all');
  const [isInital, setIsInital] = useState(true);

  const startDate = dateRange[0].toISOString();
  const endDate = dateRange[1].toISOString();

  const { data: analyticsData, refetch } = useQuery<AnalyticsData>(
    ['analytics', { period, startDate, endDate }],
    {
      enabled: true,
    }
  );
  const { data: dnsActivityData, isLoading: dnsActivityLoading } = useQuery<DNSActivityType>([
    `affiliate/chart?start=${startDate}&end=${endDate}`,
  ]);
  const { data: blogsData, isLoading: blogLoading } = useQuery<BlogDataType[]>([
    `blogify/blogs?start=${startDate}&end=${endDate}`,
  ]);
  const isEmptyDnsActivity =
    !dnsActivityData?.sales.length &&
    !dnsActivityData?.clicks.length &&
    !dnsActivityData?.views.length;

  useEffect(() => {
    if (!isInital) return;
    if (!isEmptyDnsActivity || period === '30 Days') {
      setIsInital(false);
      return;
    }
    const nextPeriod = period === 'Today' ? '7 Days' : period === '7 Days' ? '30 Days' : period;
    const nextDateRange =
      nextPeriod === '7 Days'
        ? [dayjs().subtract(7, 'days'), dayjs().subtract(1, 'days')]
        : nextPeriod === '30 Days'
          ? [dayjs().subtract(30, 'days'), dayjs().subtract(1, 'days')]
          : null;
    setPeriod(nextPeriod);

    if (nextDateRange) {
      setDateRange(nextDateRange);
      refetch();
    }
  }, [isEmptyDnsActivity, isInital, period, refetch]);

  return (
    <div className={cn('bg-white', className)}>
      <div className="rounded-lg border border-gray10">
        <h1 className="p-4 text-lg font-semibold">Blog Activity</h1>

        <div className="flex flex-col items-center gap-4 px-4 sm:flex-row sm:justify-between sm:gap-2">
          <div className="w-full sm:w-auto">
            <SiteSelector value={selectedSite} onValueChange={setSelectedSite} />
          </div>

          <div className="w-full sm:w-auto">
            <DateSelector
              selectedRange={period}
              onRangeChange={setPeriod}
              dateRange={dateRange}
              onDateRangeChange={setDateRange}
              showRangePicker={false}
            />
          </div>
        </div>

        <div className="flex flex-col">
          {dnsActivityLoading || isEmptyDnsActivity ? (
            <StateHandler
              loading={dnsActivityLoading || blogLoading}
              className="min-h-[40vh]"
              isEmpty={isEmptyDnsActivity}
            />
          ) : (
            <DnsActivity dnsActivityData={dnsActivityData} blogsData={blogsData} />
          )}

          <BlogAnalytics analyticsData={analyticsData} />
        </div>
      </div>
    </div>
  );
};

export default Analytics;
