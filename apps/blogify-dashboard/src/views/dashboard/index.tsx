import type { Notification } from '@/types/resources/notification.type';

import { useNavigate, Outlet } from 'react-router-dom';
import { useToggle, useMount } from 'react-use';
import { useEffect, useState } from 'react';

import { IntegrationDetailsContextProvider } from '@/context/IntegrationDetailsContext';
import { useStoreActions, useStoreState } from '@/store';
import { useInitialUserTourGuideContext } from '@/context/InitialUserTourGuide';
import { ToastContext, useToastContext } from '@/hooks/useToast';
import { UserAbilitiesContextProvider } from '@/context/UserAbilitiesContext';
import { BLOG_FAILED_STATUSES } from '@/constants/blog';
import { parseQuery, isPhone } from '@/utils';
import { blogEvents, events } from '@/services/event';
import { normalizeNumber } from '@/utils/number';
import subscribeForPushNotification from '@/services/push-notification';
import WritingSnippetsTour from '@/components/tour-guide/WritingSnippetsTour';
import defaultNotification from '@/types/resources/notification.type';
import InitialUserTour from '@/components/tour-guide/InitialUserTour';
import usePageTracking from '@/hooks/usePageTracking';
import YoutubeTour from '@/components/tour-guide/YoutubeTour';
import Toast from '@/components/primitives/Toast';
import Seo from '@/components/misc/Seo';
import * as Track from '@/services/analytics';

import DashboardSidebar from './layout/DashboardSidebar';
import DashboardHeader from './layout/header';

const Dashboard = () => {
  const toastContext = useToastContext();
  usePageTracking();

  const [notificationToastProps, setNotificationToastProps] =
    useState<Omit<Notification, '_id'>>(defaultNotification);
  const [showNotification, toggleNotification] = useState(false);
  const [isSidebarVisible, toggleSidebar] = useToggle(false);
  const navigate = useNavigate();

  const user = useStoreState((s) => s.user.current);
  const fetchUser = useStoreActions((a) => a.user.fetch);

  const usp = new URLSearchParams(window.location.search);
  const transaction = usp.get('transaction');
  const redirectTo = usp.get('redirectTo');
  const plan = usp.get('plan');

  useEffect(() => {
    if (
      window.location.search.includes('success=subscription-success') &&
      window.location.pathname.startsWith('/dashboard') &&
      transaction &&
      user._id &&
      plan
    ) {
      const { coupon, price } = parseQuery();
      const amount = normalizeNumber(parseInt(price, 10) / 100, 2);
      if (amount) {
        Track.subscription({ _id: transaction, amount, coupon, description: plan }, user);
      }
      if (redirectTo) {
        return navigate(decodeURIComponent(redirectTo));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plan, user, transaction, redirectTo]);

  useEffect(() => {
    if (!isPhone()) subscribeForPushNotification();

    const eventsSubscription = events.subscribe('NOTIFICATION', (notification) => {
      if (notification.actionLink !== window.location.pathname) {
        setNotificationToastProps(notification);
        toggleNotification(true);
      }
    });

    const blogEventsSubscription = blogEvents.subscribe('BLOG_STATUS_UPDATE', (updates) => {
      const blogCreateLink = '/dashboard/blogs/select-source';
      const blogLink = `/dashboard/blogs/${updates._id}`;
      // if (updates.status === 'blog_published') {
      //   setNotificationToastProps({
      //     title: 'Blog Published Successfully',
      //     description: 'The blog has been successfully published to the publishing platform',
      //   });
      //   toggleNotification(true);
      // }
      if (![, blogLink].includes(window.location.pathname)) {
        if (updates.status === 'outline_generated' && updates.generationMode === 'assisted') {
          setNotificationToastProps({
            title: 'Blog outline generated 😇',
            description: 'Continue with co-pilot to generate blog content.',
            actionTitle: 'Continue',
            actionLink: `/dashboard/blogs/${updates._id}/co-pilot`,
          });
          toggleNotification(true);
        }
        if (updates.status === 'content_generated') {
          setNotificationToastProps({
            title: 'Blog generation successful  😇',
            description: 'Go to your blog list to see your new blog.',
            actionTitle: 'View',
            actionLink: blogLink,
          });
          toggleNotification(true);
          fetchUser();
        }
        if (BLOG_FAILED_STATUSES.includes(updates.status)) {
          setNotificationToastProps({
            title: 'Blog generation failed 😥',
            description: `Sorry we couldn't generate your blog. Try creating again.`,
            actionTitle: 'Create',
            actionLink: blogCreateLink,
          });
          toggleNotification(true);
        }
      }
    });

    return () => {
      eventsSubscription.unsubscribe();
      blogEventsSubscription.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (user.subscriptionStatus === 'inactive') {
      navigate('/payment/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user._id]);

  // initial user tour guide
  const {
    setState,
    state: { tourActive },
  } = useInitialUserTourGuideContext();

  useMount(() => {
    if (tourActive) {
      setState({ run: true });
    }
  });

  return (
    <>
      <Seo
        title="Dashboard | Blogify"
        description="Quickly transcribe video to text & convert it into high quality blog & monetize it. You can also turn your podcasts to text using our  ."
        keywords="transcribe video to text, transcribe audio to text, how to transcribe audio to text,  "
      />
      <ToastContext.Provider value={toastContext}>
        <div className="mx-auto max-w-[1920px] font-inter text-black4">
          <InitialUserTour isSidebarVisible={isSidebarVisible} toggleSidebar={toggleSidebar} />
          <WritingSnippetsTour />
          <YoutubeTour />

          <div className="flex">
            <DashboardSidebar isSidebarVisible={isSidebarVisible} toggleSidebar={toggleSidebar} />
            <div className="!mx-auto w-full lg:max-w-[calc(100vw-256px)]">
              <DashboardHeader toggleSidebar={toggleSidebar} />
              <div className="w-full">
                <IntegrationDetailsContextProvider>
                  <UserAbilitiesContextProvider role={user.role}>
                    <Outlet />
                  </UserAbilitiesContextProvider>
                </IntegrationDetailsContextProvider>
              </div>
            </div>
          </div>
        </div>

        <Toast
          {...notificationToastProps}
          open={showNotification}
          onOpenChange={toggleNotification}
        />

        <Toast
          title="Generating your blog..."
          description="We'll notify you once your blog is ready."
          open={toastContext.isToastOpen.blogLoaderUnload}
          onOpenChange={(open) => toastContext.toggleToast('blogLoaderUnload', open)}
        />
      </ToastContext.Provider>
    </>
  );
};

export default Dashboard;
