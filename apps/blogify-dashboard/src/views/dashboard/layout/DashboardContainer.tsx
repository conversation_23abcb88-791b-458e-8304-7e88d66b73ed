import { MdCancel } from 'react-icons/md';

import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import Link from '@/components/common/Link';

interface DashboardContainerProps {
  children: React.ReactNode;
  title?: string;
  cancelUrl?: string;
  actions?: React.ReactNode;
  className?: string;
}
const DashboardContainer: React.FC<DashboardContainerProps> = ({
  title,
  children,
  cancelUrl,
  actions,
  className,
}) => (
  <div
    className={cn('min-h-[93.1vh] w-full bg-white px-6 pb-28 font-inter text-black4', className)}
  >
    {(title || actions) && (
      <div className="flex min-h-16 flex-wrap items-center justify-between gap-4 py-3">
        {title && <h1 className="text-21 font-semibold">{title}</h1>}

        <div className="flex flex-wrap items-center justify-end gap-3">
          {actions}

          {cancelUrl && (
            <Link to={cancelUrl}>
              <Button variant="secondary" className="text-red4">
                <MdCancel />
                <span className="font-semibold">Cancel</span>
              </Button>
            </Link>
          )}
        </div>
      </div>
    )}

    {children}
  </div>
);

export default DashboardContainer;
