import type { StatsAPIResponse } from '@/types/misc/stats.type';
import type { HTMLProps } from 'react';

import { FaClockRotateLeft } from 'react-icons/fa6';
import { useToggle } from 'react-use';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

import { PopoverTrigger, PopoverContent, Popover, PopoverClose } from '@ps/ui/components/popover';
import { useStoreState } from '@/store';
import { Progress } from '@ps/ui/components/progress';
import AddBlogCredit from '@/views/dashboard/payment/subscription/AddBlogCredit';
import emptyStats from '@/types/misc/stats.type';

const CreditsMenu = ({ className }: HTMLProps<HTMLElement>) => {
  const [isAddBlogCreditDialogOpen, toggleAddBlogCreditDialog] = useToggle(false);
  const user = useStoreState((s) => s.user.current);

  const { data: { credits } = emptyStats } = useQuery<StatsAPIResponse>(['me/stats']);

  //NOTE: find subscription plan name
  const formattedString = user.subscriptionPlan.toLowerCase().replace(/_/g, ' ');
  const wordsArray = formattedString.split(' ');
  const planName = wordsArray[wordsArray.length - 1];

  const progressPercentage =
    credits.total !== undefined ? Math.round((user.credits / credits.total) * 100) : undefined;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          className={`flex h-5 items-center rounded-xl bg-primary pl-1 pr-2 font-semibold ${className}`}
        >
          <img className="size-[14px]" src="/images/icons/credits.svg" />
          <span className="ml-1 text-xs text-white">Credits: {user.credits}</span>
        </button>
      </PopoverTrigger>

      <PopoverContent className="w-[220px] rounded-lg" sideOffset={8}>
        <div className="px-4 py-2 pb-3">
          <span className="mr-2 text-xl font-bold text-black1">{user.credits}</span>
          <span className="text-sm text-black1">Credits Left</span>
          <Progress className="mt-4" value={progressPercentage || 0} />
        </div>

        <div className="flex items-center justify-between px-4 py-2 text-sm text-black1">
          <span>Subscription</span>
          <span className="font-bold capitalize">{planName}</span>
        </div>

        <div className="flex items-center justify-between px-4 py-2 text-sm text-black1">
          <span>Monthly Credits</span>
          <span className="font-bold">{credits.total}</span>
        </div>

        <PopoverClose asChild>
          <Link
            className="flex items-center gap-1 bg-cornflowerBlue px-4 py-2 text-13 text-gray9 hover:underline"
            to="/dashboard/credits-usage-history"
          >
            <FaClockRotateLeft size={12} />
            Credit Usage History
          </Link>
        </PopoverClose>

        <PopoverClose asChild>
          <button
            className="flex w-full items-center gap-1 rounded-b-lg bg-primary px-4 py-2 text-13 text-white hover:underline"
            onClick={toggleAddBlogCreditDialog}
          >
            <img className="size-[14px]" src="/images/icons/credits.svg" />
            Purchase Credits
          </button>
        </PopoverClose>
      </PopoverContent>

      <AddBlogCredit isOpen={isAddBlogCreditDialogOpen} onClose={toggleAddBlogCreditDialog} />
    </Popover>
  );
};

export default CreditsMenu;
