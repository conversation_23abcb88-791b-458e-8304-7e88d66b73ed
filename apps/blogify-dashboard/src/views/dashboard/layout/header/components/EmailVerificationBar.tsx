import { useMutation } from 'react-query';
import toast from 'react-hot-toast';

import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';

const EmailVerificationBar = () => {
  const user = useStoreState((s) => s.user.current);

  const { mutateAsync: resend, isLoading: isResending } = useMutation({
    mutationFn: async () => {
      const tRef = toast.loading('Resending verification email...', { duration: 3000 });
      return API.post('users/verify/resend')
        .then(() => toast.success('Verification email sent.'))
        .catch((e) => toast.error(e?.error || e?.message || e))
        .finally(() => toast.dismiss(tRef));
    },
  });

  if (!user._id || user.verified) return null;

  return (
    <div className="mb-px flex flex-wrap items-center justify-between gap-3 bg-white px-6 py-3">
      <span className="text-xs font-semibold">
        Verify your email address to receive <span className="font-bold text-blue">15 credits</span>{' '}
        for free
      </span>
      <div className="flex items-center gap-3">
        <span className="text-xs">Didn't receive verification mail?</span>
        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <Button className="sm" variant="secondary" onClick={() => resend()} loading={isResending}>
          Resend
        </Button>
      </div>
    </div>
  );
};

export default EmailVerificationBar;
