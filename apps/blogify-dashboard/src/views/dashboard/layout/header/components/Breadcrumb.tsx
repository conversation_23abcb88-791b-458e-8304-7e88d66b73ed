import { FaAngleRight, FaAngleLeft } from 'react-icons/fa6';
import { useLocation, Location } from 'react-router-dom';
import { IoIosArrowForward } from 'react-icons/io';
import React, { useEffect, useRef } from 'react';

import Link from '@/components/common/Link';

interface BreadcrumbItemType {
  label: string;
  link?: string;
}
interface BreadcrumbProps {
  items?: BreadcrumbItemType[];
}

const generateBreadcrumbItems = (url: Location<any>): { label: string; link: string }[] => {
  const pathSegments = decodeURI(url.pathname)
    .split('/')
    .filter((segment) => segment !== '');
  const breadcrumb = pathSegments.map((segment, index) => {
    const label = segment.replace(/-/g, ' ');
    const link = '/' + pathSegments.slice(0, index + 1).join('/');
    return { label, link };
  });
  return breadcrumb;
};

const Breadcrumb: React.FC<BreadcrumbProps> = () => {
  const ref = useRef<HTMLOListElement>(null);
  const location = useLocation();

  const items = generateBreadcrumbItems(location);
  const canGoForward = window.navigation?.canGoForward !== false;
  const canGoBack = window.navigation?.canGoBack !== false;

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollBy({ left: ref.current.offsetWidth });
    }
  }, []);

  return (
    <div className="flex items-center">
      <div className="mr-4 flex items-center gap-1">
        <button
          className="rounded-md border-2 border-gray11 p-0.5 hover:text-primary-200 disabled:text-gray12"
          onClick={() => history.back()}
          disabled={!canGoBack}
        >
          <FaAngleLeft size={16} />
        </button>
        <button
          className="rounded-md border-2 border-gray11 p-0.5 hover:text-primary-200 disabled:text-gray12"
          onClick={() => history.forward()}
          disabled={!canGoForward}
        >
          <FaAngleRight size={16} />
        </button>
      </div>

      <nav aria-label="breadcrumb" className="overflow-x-hidden">
        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <ol ref={ref} className="h-scroll flex h-10">
          {items?.map((item, index) => (
            <BreadcrumbItem key={index} item={item} isLast={index === items.length - 1} />
          ))}
        </ol>
      </nav>
    </div>
  );
};

interface BreadcrumbItemType {
  label: string;
  link?: string;
}
interface BreadcrumbItemProps {
  item: BreadcrumbItemType;
  isLast: boolean;
}
const BreadcrumbItem: React.FC<BreadcrumbItemProps> = ({ item, isLast }) => {
  const label = item.label.length > 20 ? item.label.substring(0, 20) + '...' : item.label;

  return (
    <li className="flex items-center whitespace-nowrap text-xs capitalize text-gray9 lg:text-sm">
      <Link
        className={isLast ? 'cursor-default text-black4' : 'hover:text-primary'}
        to={item.link || ''}
      >
        {label}
      </Link>
      {!isLast && <IoIosArrowForward className="mx-1 text-gray9 lg:mx-2" />}
    </li>
  );
};

export default Breadcrumb;
