import {
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import { useStoreState } from '@/store';
import { Avatar } from '@ps/ui/components/avatar';
import useAuth from '@/hooks/useAuth';
import Link from '@/components/common/Link';

const UserMenu = () => {
  const user = useStoreState((s) => s.user.current);
  const { logoutUser } = useAuth();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Avatar className="size-7" src={user.profilePicture} name={user.name} />
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-max min-w-[180px]" sideOffset={8} align="end">
        <DropdownMenuItem className="h-auto cursor-default flex-col items-start gap-0 px-4 py-3 text-sm font-bold text-gray12 hover:!bg-white hover:!text-gray12">
          {user.name}
          <span className="text-xs">{user.email}</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="flex p-0 text-xs text-black1 hover:bg-gray-200 hover:text-primary">
          <Link className="w-full px-4 py-2" to="/dashboard/settings">
            Profile Settings
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="flex px-4 py-2 text-xs text-black1 hover:bg-gray-200 hover:text-primary"
          onClick={logoutUser}
        >
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenu;
