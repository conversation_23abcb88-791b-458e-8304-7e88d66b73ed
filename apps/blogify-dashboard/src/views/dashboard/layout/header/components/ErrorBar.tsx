import { MdClose } from 'react-icons/md';

import { useToggle } from 'react-use';

import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';

const ErrorBar = () => {
  const { error } = parseQuery();
  const [show, setShow] = useToggle(!!error);

  if (!error || !show) return null;

  return (
    <div className="mb-px flex items-center justify-between bg-white px-6 py-2">
      <span className="text-sm font-bold text-primary">{error}</span>
      <Button variant="icon" onClick={() => setShow()}>
        <MdClose size={20} className="text-gray2" />
      </Button>
    </div>
  );
};

export default ErrorBar;
