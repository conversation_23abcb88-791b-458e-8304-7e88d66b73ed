import { IoSettingsSharp } from 'react-icons/io5';
import { MdMenu } from 'react-icons/md';

import { Button } from '@ps/ui/components/button';
import Link from '@/components/common/Link';

import EmailVerificationBar from './components/EmailVerificationBar';
import NotificationMenu from '../../notifications/components/NotificationMenu';
import CreditsMenu from './components/CreditsMenu';
import Breadcrumb from './components/Breadcrumb';
import UserMenu from './components/UserMenu';
import ErrorBar from './components/ErrorBar';

const DashboardHeader = ({ toggleSidebar }: { toggleSidebar: () => void }) => (
  <>
    <header className="sticky top-0 z-40 flex h-[60px] items-center justify-between bg-white px-6">
      <div className="flex items-center">
        <div className="-ml-2 lg:hidden">
          <Button className="size-9" variant="icon" onClick={toggleSidebar}>
            <MdMenu className="text-gray9" size={20} />
          </Button>
        </div>
        <div className="hidden lg:block">
          <Breadcrumb />
        </div>
      </div>

      <div className="flex items-center gap-1">
        <CreditsMenu className="mr-2" />
        <NotificationMenu />
        <Link className="mr-2" to="/dashboard/settings">
          <Button className="size-9" variant="icon">
            <div className="flex items-center justify-center">
              <IoSettingsSharp size={16} className="mx-2 text-gray12" />
            </div>
          </Button>
        </Link>
        <UserMenu />
      </div>
    </header>

    <div className="sticky top-[59px] z-30 block border-b border-bg2 bg-white px-6 lg:hidden">
      <Breadcrumb />
    </div>
    <EmailVerificationBar />
    <ErrorBar />
  </>
);

export default DashboardHeader;
