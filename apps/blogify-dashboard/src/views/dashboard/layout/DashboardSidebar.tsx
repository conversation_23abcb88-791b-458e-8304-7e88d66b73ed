import {
  MdOutlineFeatured<PERSON>layList,
  MdEditDocument,
  MdDashboard,
  MdExtension,
  MdSettings,
  MdPayment,
  MdWallet,
  MdPhoto,
} from 'react-icons/md';
import { FaYoutubeSquare, FaLink } from 'react-icons/fa';
import { TbAffiliateFilled } from 'react-icons/tb';
import { FaChildReaching } from 'react-icons/fa6';
import { HiUserGroup } from 'react-icons/hi2';
import { VscGlobe } from 'react-icons/vsc';
import styled from 'styled-components';
import React from 'react';

import { getPackageAndPeriod } from '@/utils';
import { mediaQuery, Theme } from '@/styles';
import { useStoreState } from '@/store';
import Link from '@/components/common/Link';

const DASHBOARD_MENU_CONFIG: {
  name: string;
  domId: string;
  link: string;
  icon: React.ReactNode;
}[] = [
  { name: 'Overview', domId: 'overview', link: '.', icon: <MdDashboard size={20} /> },

  { name: 'My Blogs', domId: 'blogs', link: './blogs', icon: <MdEditDocument size={20} /> },
  { name: 'My Blog Sites', domId: 'websites', link: './websites', icon: <VscGlobe size={20} /> },

  { name: 'Images', domId: 'images', link: './images', icon: <MdPhoto size={20} /> },
  {
    name: 'Affiliate',
    domId: 'affiliate',
    link: './affiliate',
    icon: <TbAffiliateFilled size={20} />,
  },
  {
    name: 'Link Library',
    domId: 'link-library',
    link: './link-library',
    icon: <FaLink size={20} />,
  },
  { name: 'My Wallet', domId: '#wallet', link: './wallet', icon: <MdWallet size={20} /> },
  {
    name: 'Subscription',
    domId: 'subscription',
    link: './subscription',
    icon: <MdPayment size={20} />,
  },
  {
    name: 'Users',
    domId: 'user-settings',
    link: './users',
    icon: <HiUserGroup size={20} />,
  },
  { name: 'Settings', domId: 'settings', link: './settings', icon: <MdSettings size={20} /> },
] as const;

const DASHBOARD_ADDONS_MENU_CONFIG = [
  {
    name: 'YouTube Connect',
    domId: 'channels',
    link: './youtube',
    icon: <FaYoutubeSquare size={20} />,
  },
  {
    name: 'Writing Snippets',
    domId: 'writingSnippets',
    link: './writing-snippets/all',
    icon: <MdEditDocument size={20} />,
  },
  {
    name: 'Addons',
    domId: 'addons',
    link: './addons',
    icon: <MdExtension size={20} />,
  },
];

const DashboardSidebar = ({
  isSidebarVisible,
  toggleSidebar,
}: {
  isSidebarVisible: boolean;
  toggleSidebar: () => void;
}) => {
  const user = useStoreState((u) => u.user.current);

  DASHBOARD_ADDONS_MENU_CONFIG[0].name = user.hasYouTubeProAddon
    ? 'YouTube Connect Pro'
    : 'YouTube Connect';

  if (
    user?.email?.endsWith('@blogify.ai') &&
    DASHBOARD_MENU_CONFIG.every((m) => m.name !== 'Blog Ideas')
  ) {
    DASHBOARD_MENU_CONFIG.splice(1, 0, {
      name: 'Blog Ideas',
      domId: 'blog-ideas',
      link: './blog-ideas',
      icon: <FaChildReaching size={20} />,
    });
  }

  return (
    <div className="font-inter text-black4">
      {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
      <DashboardSidebarStyled className={`sidebar h-screen ${isSidebarVisible ? 'visible' : ''}`}>
        <div className="flex flex-row items-center justify-start gap-3 pb-8">
          <a href="/">
            <img src="/images/blogify.svg" height={40} />
          </a>
          <div className="">
            <h2 className="text-md font-semibold leading-[18px]">Blogify.ai</h2>
            <h4 className="text-sm font-normal capitalize leading-4 text-gray9">
              {getPackageAndPeriod(user.subscriptionPlan).plan}
            </h4>
          </div>
        </div>
        {DASHBOARD_MENU_CONFIG.map((config, i) => (
          <React.Fragment key={i}>
            <DashboardSidebarItem {...config} onClick={toggleSidebar} />
          </React.Fragment>
        ))}
        <div className="pb-2 pt-5 text-xs font-semibold uppercase text-gray9">Addons</div>
        {DASHBOARD_ADDONS_MENU_CONFIG.map((config, i) => (
          <DashboardSidebarItem key={i} {...config} onClick={toggleSidebar} />
        ))}

        <div className="mt-10 border-t border-gray11">
          <DashboardSidebarItem
            {...{
              name: 'Upcoming Features',
              link: './upcoming-features',
              icon: <MdOutlineFeaturedPlayList size={20} />,
            }}
            onClick={toggleSidebar}
          />
        </div>
      </DashboardSidebarStyled>

      {isSidebarVisible && <DashboardOverlay onClick={toggleSidebar} />}
    </div>
  );
};

const DashboardSidebarStyled = styled.aside`
  padding: 10px 12px;
  overflow-y: auto;
  position: sticky;
  width: 256px;
  background-color: ${Theme.colors.bg2};
  top: 0;
  a {
    /* color: ${Theme.colors.black4}; */
    /* display: flex; */
    svg {
      color: ${Theme.colors.gray9};
    }
    &:hover {
      color: ${Theme.colors.blue};
    }
  }
  a.active {
    box-shadow: 1px 1px 3px -1px ${Theme.colors.gray4};
    background-color: ${Theme.colors.white};
    color: ${Theme.colors.blue};
    border-radius: 6px;
    display: block;
    svg {
      color: ${Theme.colors.blue};
    }
  }
  ${mediaQuery.md`
    transition: transform 0.2s ease-in-out 0s;
    background: ${Theme.colors.bg2};
    transform: translateX(-80vw);
    height: calc(100vh - 0px);
    position: fixed;
    z-index: 350;
    &.visible {
      transform: translateX(0);
    }
  `}
`;

const DashboardOverlay = styled.div`
  ${mediaQuery.md`
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    height: 100vh;
    z-index: 100;
    width: 100vw;
    right: 0;
    top: 0;
  `}
`;

const DashboardSidebarItem = ({
  name,
  link,
  icon,
  domId,
  onClick,
}: {
  name: string;
  link: string;
  icon: React.ReactNode;
  domId?: string;
  onClick: () => void;
}) => (
  <div className="mb-1 rounded-md hover:bg-bg2">
    <Link to={link} onClick={onClick} id={domId} end>
      <div className="flex w-full items-center gap-3 px-[10px] py-2">
        {icon}
        <div className="font-medium leading-5">{name}</div>
      </div>
    </Link>
  </div>
);

export default DashboardSidebar;
