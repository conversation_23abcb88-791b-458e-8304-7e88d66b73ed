import type { DashboardBreadcrumbProps } from '@/types/components/base';

import { Fragment } from 'react';

import { Card } from '@/components/layout';
import Spinner from '@/components/misc/Spinner';
import Link from '@/components/common/Link';

const DashboardTitle = ({
  title,
  className,
  ...props
}: { title: string } & React.HTMLProps<HTMLDivElement>) => (
  <div
    className={`text-xl font-semibold capitalize text-black1 ${className}`}
    role="heading"
    {...props}
  >
    {title}
  </div>
);

const DashboardTitleCard = ({
  title,
  count,
  isLoading,
  children,
  className,
  ...props
}: {
  title: string;
  count: number;
  isLoading?: boolean;
} & React.HTMLProps<HTMLDivElement>) => (
  <Card
    className={`min-h-[84px] !flex-row items-center justify-between px-6 ${className}`}
    {...props}
  >
    <div className="flex items-center">
      {!isLoading && (
        <div className="mr-2 text-2xl font-semibold" role="count">
          {count === 0 ? 'No' : count}
        </div>
      )}
      <div className="mt-1 font-semibold">{isLoading ? <Spinner /> : title}</div>
    </div>

    {children}
  </Card>
);

const DashboardBreadcrumb = ({ breadcrumb }: { breadcrumb: DashboardBreadcrumbProps[] }) => (
  <div id="breadCrumb" className="mt-0.5 flex gap-2 text-xs text-gray2">
    {breadcrumb.map((item, index) => (
      <Fragment key={index}>
        <Link to={item.href}>
          <div className="capitalize">{item.title}</div>
        </Link>
        {index < breadcrumb.length - 1 && <div>/</div>}
      </Fragment>
    ))}
  </div>
);

const DashboardContainer = ({
  title,
  breadcrumb,
  primaryAction,
  children,
  ...props
}: {
  title?: string;
  breadcrumb?: DashboardBreadcrumbProps[];
  primaryAction?: React.ReactNode;
} & React.HTMLProps<HTMLDivElement>) => (
  <div {...props}>
    {(title || !!breadcrumb?.length) && (
      <Card className="mb-px !flex-row items-start justify-between px-6 sm:items-center">
        <div className="py-4">
          {breadcrumb?.length && <DashboardBreadcrumb breadcrumb={breadcrumb} />}
          {title && <DashboardTitle title={title} />}
        </div>
        {primaryAction && primaryAction}
      </Card>
    )}
    {children}
  </div>
);

export { DashboardTitle, DashboardTitleCard, DashboardContainer };
