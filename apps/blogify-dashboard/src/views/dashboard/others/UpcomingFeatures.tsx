import { IoMdCheckmarkCircle } from 'react-icons/io';

import { PageContainerMini } from '@/components/layout/PageContainer';
import { Card } from '@/components/layout';
import DashboardContainer from '@/views/dashboard/layout/DashboardContainer';

const FeaturesSection = () => (
  <DashboardContainer title="Upcoming Features">
    <PageContainerMini>
      <Card>
        <img className="rounded-t" src="/images/upcoming-features.png" alt="Upcoming Features" />
        <div className="p-5">
          <div className="text-sm">
            We are working on making <b>blogfy.ai</b> rich with features. Here are a few features we
            are currently working on to bring to you as soon as possible.
          </div>

          <div className="text-sm">
            <ol className="my-4 list-decimal p-0 pl-5 [&>li]:py-0.5">
              <li>Upload any media files, doc or pdf to generate blogs</li>
              <li>Facebook signup & publish</li>
              <li>Multiple Users</li>
              <li>
                Mailchimp & Aweber Integration: Seamlessly integrate your email marketing with your
                content creation.
              </li>
              <li>Wix Integration</li>
              <li>Medium Connect</li>
              <li>Shopify App: Easily integrate your blogs with your Shopify store.</li>
              <li>
                Woocommerce Plug-ins: Bring your WordPress-based eCommerce to life with engaging
                blogs.
              </li>
              <li>BigCommerce Plugin</li>
              <li>Hubspot Integration</li>
              <li>Personalised writing styles by training models only for enterprise</li>
            </ol>
          </div>

          <div className="text-sm">
            If you have any requests please feel free to drop a feature request via our support.
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-5">
          <div className="text-sm">
            Here are the features we have already completed and are available in <b>blogfy.ai</b>:
          </div>

          <ol className="my-4 p-0 pl-5 [&>*>li]:py-0.5 [&>ul]:pl-5">
            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-green" />
              Blogify Version 1.0 (Completed)
            </h4>
            <ul className="list-disc">
              <li>Transform YouTube Video into a blog</li>
              <li>Generate Blog From Text prompt</li>
              <li>Analytics</li>
              <li>Schedule Blog for later publish</li>
              <li>LinkedIn Connect & publish</li>
              <li>Twitter Connect & publish</li>
              <li>WordPress connect & publish</li>
              <li>Blogger.com connect & publish</li>
              <li>Affiliate Link Automation (Beta)</li>
            </ul>

            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-green" />
              Blogify Version 2.0 (Completed)
            </h4>
            <ul className="list-disc">
              <li>Blog generation improvements to generate upto 4000 words blog</li>
              <li>Facebook Connect and publish</li>
              <li>SEO optimized post with AI-generated Meta Tags</li>
              <li>AI-generated #</li>
              <li>Automatic Blog Summary</li>
              <li>Blog Tags (Auto publish)</li>
              <li>Redesign User Interface</li>
              <li>Review Before Publish</li>
              <li>Retry option if Blogify failed to generate Blog</li>
            </ul>

            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-green" />
              Blogify Version 3.0 (Completed)
            </h4>
            <ul className="list-disc">
              <li>Upload multiple image for blog content</li>
              <li>Upload any audio or video file to generate blogs</li>
              <li>Upload any doc, docx, txt or pdf file to generate blogs</li>
              <li>Sign up with Google</li>
              <li>Option to give Video credit to the original creator</li>
              <li>More language support</li>
            </ul>

            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-green" />
              Blogify Version 4.0 (Completed)
            </h4>
            <ul className="list-disc">
              <li>YouTube Channels (Addon)</li>
              <li>Writing Snippets (Addon)</li>
              <li>Wordpress(org) Plugin</li>
              <li>Medium Integration</li>
              <li>Mailchimp Integration</li>
              <li>Invite User</li>
            </ul>

            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-green" />
              Blogify Version 5.0 (Completed)
            </h4>
            <ul className="list-disc">
              <li>SEO Trends Analysis & Improvements</li>
              <li>Social Media Content Generation & Scheduling</li>
              <li>Agency Feature</li>
              <li>Blogify Publication - A complete blogging platform</li>
              <li>Affiliate Expansion - Affiliate network for bloggers</li>
              <li>Bulk Blog Generation</li>
            </ul>

            <h4 className="flex items-center gap-2 font-bold">
              <IoMdCheckmarkCircle className="mr-1 text-gray4" />
              Blogify Version 5.5 (to be announced)
            </h4>
            <ul className="list-disc">
              <li>
                Personalized Tone (AI will adapt your writing style and follow that style) (For
                Enterprise)
              </li>
              <li>Facebook Integration</li>
              <li>Aweber Integration</li>
              <li>Shopify Plugin</li>
              <li>Wix Integration</li>
              <li>Substack Integration</li>
            </ul>
          </ol>
        </div>
      </Card>
    </PageContainerMini>
  </DashboardContainer>
);

export default FeaturesSection;
