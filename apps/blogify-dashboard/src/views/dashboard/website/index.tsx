/* eslint-disable react-refresh/only-export-components */
import type { RouteObject } from 'react-router-dom';

import { Outlet } from 'react-router-dom';

import { Suspense } from 'react';

import { RouteErrorBoundary } from '@/components/error';
import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';

const WebsiteSubscribers = safeLazy(() => import('./details/WebsiteSubscribers'));
const WebsiteAnalytics = safeLazy(() => import('./details/WebsiteAnalytics'));
const WebsiteSettings = safeLazy(() => import('./details/WebsiteSettings'));
const WebsiteDetails = safeLazy(() => import('./details/WebsiteDetails'));
const WebsiteCreate = safeLazy(() => import('./WebsiteCreate'));
const WebsiteTheme = safeLazy(() => import('./details/theme/WebsiteTheme'));
const WebsiteBlogs = safeLazy(() => import('./details/WebsiteBlogs'));
const WebsiteList = safeLazy(() => import('./WebsiteList'));
const WebsiteEdit = safeLazy(() => import('./WebsiteEdit'));

const BlogCreate = safeLazy(() => import('./blog/BlogCreate'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);

function WebsiteRoot() {
  return <Outlet />;
}

const getRoutes = (): RouteObject[] => [
  {
    path: 'websites',
    element: <WebsiteRoot />,
    errorElement: <RouteErrorBoundary />,
    children: [
      { path: '', element: <Lazy as={WebsiteList} /> },
      { path: 'add', element: <Lazy as={WebsiteCreate} /> },
      { path: 'update/:id', element: <Lazy as={WebsiteEdit} /> },
      { path: ':id/blogs/create', element: <Lazy as={BlogCreate} /> },
      {
        path: ':id',
        element: <Lazy as={WebsiteDetails} />,
        children: [
          { path: '', element: <Lazy as={WebsiteBlogs} /> },
          { path: 'blogs', element: <Lazy as={WebsiteBlogs} /> },
          { path: 'analytics', element: <Lazy as={WebsiteAnalytics} /> },
          { path: 'subscribers', element: <Lazy as={WebsiteSubscribers} /> },
          { path: 'theme', element: <Lazy as={WebsiteTheme} /> },
          { path: 'settings', element: <Lazy as={WebsiteSettings} /> },
        ],
      },
    ],
  },
];

export default getRoutes;
