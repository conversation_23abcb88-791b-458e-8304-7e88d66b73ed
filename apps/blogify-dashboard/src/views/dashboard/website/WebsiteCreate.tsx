import type { WebsiteSchema } from './components/WebsiteForm';

import { useNavigate } from 'react-router-dom';
import { useMutation } from 'react-query';

import { API } from '@/services/api';

import DashboardContainer from '../layout/DashboardContainer';
import WebsiteForm from './components/WebsiteForm';

export default function WebsiteCreate() {
  const {
    mutateAsync: addWebsite,
    isLoading,
    error,
  } = useMutation({
    mutationFn: (payload: WebsiteSchema) => API.post(`websites`, payload),
  });
  const navigate = useNavigate();

  const submit = (values: WebsiteSchema) => {
    values.url = values.url.toLowerCase();
    addWebsite(values).then(() => navigate('/dashboard/websites'));
  };

  return (
    <DashboardContainer title="Host a Blog Site">
      <WebsiteForm submit={submit} isSaving={isLoading} error={error as string} />
    </DashboardContainer>
  );
}
