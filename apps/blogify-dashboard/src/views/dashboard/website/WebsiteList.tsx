import type { APIResponse, Website } from '@ps/types';

import { FaCircleInfo, FaYoutube } from 'react-icons/fa6';
import { VscGlobe } from 'react-icons/vsc';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';

import { Button } from '@ps/ui/components/button';
import YouTubeEmbedDialog from '@/components/dialog/YouTubeEmbedDialog';
import Spinner from '@/components/misc/Spinner';

import DashboardContainer from '../layout/DashboardContainer';
import WebsiteCard from './components/WebsiteCard';

export default function WebsiteList() {
  const {
    data: { data: websites } = { data: [] },
    isLoading,
    refetch,
  } = useQuery<APIResponse<Website>>('websites');

  return (
    <DashboardContainer
      title="My Blog Sites"
      actions={
        <>
          <YouTubeEmbedDialog
            title="Step-by-step guide to Learn About Google Index"
            url="https://www.youtube.com/embed/KOZ_BX15DCA"
          >
            <Button variant="outline" type="button">
              <FaCircleInfo />
              Learn About Google Indexing
            </Button>
          </YouTubeEmbedDialog>

          <Link to="./add">
            <Button>
              <VscGlobe size={20} />
              Host a Blog Site
            </Button>
          </Link>
        </>
      }
    >
      {!websites.length && (
        <div className="flex h-[calc(100vh-240px)] text-lg font-medium text-gray9 flex-center">
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="flex flex-col text-center text-black4 flex-center">
              <img src="/images/icons/empty-websites.svg" />
              <h3 className="mt-4 text-17 font-semibold">No Blog Site Hosted Yet</h3>
              <p className="mx-auto mb-4 mt-2 max-w-2xl text-15">
                Don't have a blog site yet? No worries, Blogify has you covered! Use your own domain
                or subdomain to launch a fully powered blog site in minutes.
              </p>
              <div className="flex justify-between gap-3">
                <Link to="./add">
                  <Button>
                    <VscGlobe size={20} />
                    Host a Blog Site
                  </Button>
                </Link>
                <YouTubeEmbedDialog
                  title="Step-by-step guide to setting up a domain for blogging using Blogify"
                  url="https://www.youtube.com/embed/ngGiBsB1krs?si=dd222qQ1aJPDDkvR"
                >
                  <Button variant="secondary" className="text-youtube" type="button">
                    <FaYoutube size={20} />
                    Watch Tutorial
                  </Button>
                </YouTubeEmbedDialog>
              </div>
            </div>
          )}
        </div>
      )}

      {!!websites.length && (
        <div className="grid grid-cols-1 items-start gap-5 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {websites.map((website, i) => (
            <WebsiteCard key={i} website={website} refetchWebsites={refetch} />
          ))}
        </div>
      )}
    </DashboardContainer>
  );
}
