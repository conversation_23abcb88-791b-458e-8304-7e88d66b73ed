import { zod<PERSON><PERSON><PERSON><PERSON>, z } from '@ps/ui';
import { useParams } from 'react-router-dom';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';

import { FileUploadProgress, FileUpload } from '@/modules/file-upload/FileUpload';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';
import RichEditor from '@/components/form/RichEditor';
import Form<PERSON>ield from '@ps/ui/form/FormField';
import TagInput from '@/components/form/TagInput';

const blogSchema = z.object({
  title: z.string({ required_error: 'A blog title is required.' }),
  content: z
    .string({ required_error: 'Content is required.' })
    .min(500, 'Please enter at least 500 characters'),
  image: z.string({ required_error: 'Cover image is required.' }).optional(),
  metaDescription: z.string({ required_error: 'Meta description is required.' }),
  keywords: z.array(z.string({ required_error: 'Keyboards are required.' })).min(1),
  platforms: z
    .array(
      z.object({
        draft: z.boolean().optional(),
        platform: z.string().optional(),
        siteIDs: z.array(z.string()).optional(),
        timestamp: z.string().optional(),
      })
    )
    .optional(),
});
export type BlogSchema = z.infer<typeof blogSchema>;

export default function BlogForm({
  submit,
  defaultValues = {} as BlogSchema,
  isSaving,
  error,
}: {
  submit: (values: BlogSchema) => void;
  defaultValues?: BlogSchema;
  isSaving: boolean;
  error?: string;
}) {
  const { id } = useParams() as { id: string };
  const { getInputFields, handleSubmit, setValue, setError, watch } = useForm<BlogSchema>({
    resolver: zodResolver(blogSchema),
    defaultValues: {
      platforms: [
        {
          draft: false,
          platform: 'blogify',
          siteIDs: [id],
          timestamp: new Date().toISOString(),
        },
      ],
      ...defaultValues,
    },
  });

  const image = watch('image');

  return (
    <form className="mx-auto max-w-5xl py-10" onSubmit={handleSubmit(submit)}>
      <FileUploadProvider
        onUploadComplete={(files) => {
          if (files[0]) {
            setTimeout(() => {
              const { url } = files[0];
              setValue('image', url);
            }, 500);
          }
        }}
        folder="blog/covers"
        type="image"
      >
        {({ isUploading }) => (
          <FormField label="Cover Image" type="custom" {...getInputFields('image')}>
            {image && <img className="mb-5 w-full rounded-lg" src={image} />}
            <div className="mt-2 rounded-lg border border-dashed border-gray12">
              {!isUploading && (
                <FileUpload
                  supportedFormats=".jpeg, .jpg, .png, .gif, .bmp, .tiff, .tif, .webp, .svg, .heic, .heif"
                  className="sm:min-h-52"
                />
              )}

              {isUploading && <FileUploadProgress className="sm:min-h-52" />}
            </div>
          </FormField>
        )}
      </FileUploadProvider>

      <FormField label="Title" {...getInputFields('title')} />
      <FormField label="Meta Description" type="textarea" {...getInputFields('metaDescription')} />

      <FormField label="Keywords" type="custom" {...getInputFields('keywords')}>
        <TagInput
          tags={(watch('keywords') || []) as string[]}
          setError={(message) => {
            if (message) setError('keywords', { message });
          }}
          placeholder="Comma (,) separated keywords for SEO"
          setTags={(tags) => setValue('keywords', tags)}
          name="keywords"
          type="tags"
        />
      </FormField>

      <FormField type="custom" label="Content" {...getInputFields('content')}>
        <RichEditor blogLanguage="english" value="" onChange={(v) => setValue('content', v)} />
      </FormField>

      <div className="mt-10">
        {error && (
          <div className="mb-1 text-sm text-red">
            {error.includes('duplicate')
              ? 'Another blog already existing with the same URL'
              : error}
          </div>
        )}
        <Button type="submit" className="w-full" loading={isSaving}>
          Submit
        </Button>
      </div>
    </form>
  );
}
