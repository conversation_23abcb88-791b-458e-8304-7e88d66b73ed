import type { BlogSchema } from './BlogForm';

import { useNavigate, useParams } from 'react-router-dom';
import { useMutation } from 'react-query';

import { API } from '@/services/api';

import DashboardContainer from '../../layout/DashboardContainer';
import WebsiteProvider from '../details/context/WebsiteProvider';
import BlogForm from './BlogForm';

export default function BlogCreate() {
  const {
    mutateAsync: addBlog,
    isLoading,
    error,
  } = useMutation({
    mutationFn: (payload: BlogSchema) => API.post(`blogs/create`, payload),
  });
  const { id } = useParams() as { id: string };
  const navigate = useNavigate();

  const submit = (values: BlogSchema) => {
    addBlog(values).then(() => navigate(`/dashboard/websites/${id}/blogs`));
  };

  return (
    <WebsiteProvider id={id}>
      <DashboardContainer title="Create a Blog">
        <BlogForm submit={submit} isSaving={isLoading} error={error as string} />
      </DashboardContainer>
    </WebsiteProvider>
  );
}
