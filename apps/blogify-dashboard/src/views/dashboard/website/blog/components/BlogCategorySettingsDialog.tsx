'use client';

import type { BlogCategory } from '@ps/types';

import { MdFormatListBulletedAdd } from 'react-icons/md';
import { useMutation } from 'react-query';
import { useState } from 'react';
import { FiEdit3 } from 'react-icons/fi';
import { FaTrash } from 'react-icons/fa';
import { FaCog } from 'react-icons/fa';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { zodResolver, z } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import FormField from '@ps/ui/form/FormField';

const blogCategorySchema = z.object({
  _id: z.string().optional(),
  name: z.string({ required_error: 'Name is required' }),
});
type BlogCategorySchema = z.infer<typeof blogCategorySchema>;

export default function BlogCategorySettingsDialog({
  categories,
  refetch,
}: {
  categories: BlogCategory[];
  refetch: () => void;
}) {
  const [categoryId, setCategoryId] = useState('');
  const [error, setError] = useState('');
  const [open, setOpen] = useState(false);

  const { getInputFields, handleSubmit, setValue, watch, reset } = useForm<BlogCategorySchema>({
    resolver: zodResolver(blogCategorySchema),
  });
  const { mutateAsync: updateCategory, isLoading } = useMutation({
    mutationFn: (payload: BlogCategorySchema) =>
      API.patch<BlogCategorySchema>(`blog-categories/${payload._id}`, payload),
  });
  const { mutateAsync: deleteCategory, isLoading: deleting } = useMutation({
    mutationFn: () =>
      API.remove<BlogCategorySchema>(`blog-categories/${categoryId}`).then(() => {
        setCategoryId('');
        refetch();
      }),
  });

  const toggleEdit = (category: BlogCategory) => {
    reset();
    setError('');
    setCategoryId('');
    setValue('_id', category._id);
    setValue('name', category.name);
  };

  const toggleDelete = (id: string) => {
    reset();
    setError('');
    setCategoryId(id);
  };

  const update = async (values: BlogCategorySchema) => {
    setError('');
    await updateCategory(values)
      .then(() => {
        refetch();
        reset();
      })
      .catch((e: Error) => setError(e.message));
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(o) => {
        setError('');
        reset();
        setOpen(o);
      }}
    >
      <DialogTrigger asChild>
        <Button
          className="size-6 !px-2 !text-11 text-gray9"
          onClick={() => setOpen(true)}
          variant="secondary"
          size="xs"
        >
          <div>
            <FaCog size={14} />
          </div>
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-xl border-none px-6 font-inter">
        <DialogHeader>
          <MdFormatListBulletedAdd className="text-primary" size={48} />
          <DialogTitle className="!mt-6 text-17 font-semibold">Blog Category Settings</DialogTitle>
          <DialogDescription className="mt-2 !text-15 text-black">
            Rename or remove your blog categories.
          </DialogDescription>
        </DialogHeader>

        <div>
          <h3 className="text-15 font-medium">Blog Categories</h3>
          <ul className="mt-2 flex flex-col gap-2">
            {categories.map((category) => (
              <li
                className="flex h-10 items-center justify-between rounded-lg border border-gray10 pl-4 pr-2"
                key={category._id}
              >
                <span className="text-15 font-medium">{category.name}</span>

                <div className="flex items-center gap-2">
                  <Button
                    className="size-6 !px-2 !text-11 text-gray9"
                    onClick={() => toggleEdit(category)}
                    variant="secondary"
                    size="xs"
                  >
                    <div>
                      <FiEdit3 size={14} />
                    </div>
                  </Button>

                  <Button
                    className="size-6 !px-2 !text-11 text-gray9"
                    onClick={() => toggleDelete(category._id)}
                    variant="secondary"
                    size="xs"
                  >
                    <div>
                      <FaTrash size={14} />
                    </div>
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        </div>

        {!!watch('_id') && (
          <form
            className="mt-3 rounded-lg border border-gray10 px-6 py-5"
            onSubmit={handleSubmit(update)}
          >
            <FormField label="Category Name" {...getInputFields('name')} />

            {error && <div className="min-h-6 pt-1 text-xs font-medium text-red">{error}</div>}

            <div className="-mt-3 flex gap-3">
              <Button type="submit" loading={isLoading}>
                Save
              </Button>
              <Button variant="secondary" onClick={() => setCategoryId('')}>
                Cancel
              </Button>
            </div>
          </form>
        )}

        {!!categoryId && (
          <div className="rounded-lg bg-[#cc0022]/5 px-6 py-5">
            <h3 className="text-17 font-semibold">
              Delete {categories.find((c) => categoryId === c._id)?.name}
            </h3>
            <p className="mt-1.5 text-15">
              Only the category will be deleted. Your blogs are ok. Blogs under this category is
              available in your all blogs.
            </p>

            <div className="mt-5 flex gap-3">
              <Button
                className="min-w-20 bg-red4 hover:bg-red4/80"
                onClick={() => deleteCategory()}
                loading={deleting}
              >
                Delete
              </Button>
              <Button variant="secondary" onClick={() => setCategoryId('')}>
                Cancel
              </Button>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button
            onClick={() => setOpen(false)}
            className="mt-6 w-full"
            variant="secondary"
            size="lg"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
