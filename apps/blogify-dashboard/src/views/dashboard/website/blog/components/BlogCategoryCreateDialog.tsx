'use client';

import type { Website } from '@ps/types';

import { MdFormatListBulletedAdd } from 'react-icons/md';
import { useMutation } from 'react-query';
import { useState } from 'react';
import { FaPlus } from 'react-icons/fa6';
import toast from 'react-hot-toast';

import {
  DialogDescription,
  DialogContent,
  DialogTrigger,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { zodResolver, z } from '@ps/ui';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Form<PERSON>ield from '@ps/ui/form/FormField';

const blogCategorySchema = z.object({
  name: z.string({ required_error: 'Name is required' }),
  website: z.string(),
});
type BlogCategorySchema = z.infer<typeof blogCategorySchema>;

export default function BlogCategoryCreateDialog({
  website,
  refetch,
}: {
  website: Website;
  refetch: () => void;
}) {
  const [error, setError] = useState('');
  const [open, setOpen] = useState(false);

  const { getInputFields, handleSubmit, reset } = useForm<BlogCategorySchema>({
    resolver: zodResolver(blogCategorySchema),
    defaultValues: { website: website._id },
  });
  const { mutateAsync: createCategory, isLoading } = useMutation({
    mutationFn: (payload: BlogCategorySchema) =>
      API.post<BlogCategorySchema>(`blog-categories`, payload),
  });

  const submit = async (values: BlogCategorySchema) => {
    setError('');
    await createCategory(values)
      .then(() => {
        toast.success(`Added category ${values.name}.`);
        setOpen(false);
        refetch();
      })
      .catch((e: Error) => setError(e.message));
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(o) => {
        setError('');
        reset();
        setOpen(o);
      }}
    >
      <DialogTrigger asChild>
        <Button
          className="h-6 !px-2 !text-11 text-gray9"
          onClick={() => setOpen(true)}
          variant="secondary"
          size="xs"
        >
          <FaPlus size={8} />
          New Category
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-xl border-none px-6 font-inter">
        <DialogHeader>
          <MdFormatListBulletedAdd className="text-primary" size={48} />
          <DialogTitle className="!mt-6 text-17 font-semibold">New Blog Category</DialogTitle>
          <DialogDescription className="mt-2 !text-15 text-black">
            To create a new blog category simply type the category name and create new blog category
          </DialogDescription>
        </DialogHeader>

        <form className="mt-3" onSubmit={handleSubmit(submit)}>
          <FormField label="Category Name" {...getInputFields('name')} />

          {error && <div className="min-h-6 pt-1 text-xs font-medium text-red">{error}</div>}
          <DialogFooter>
            <Button className="w-full" size="lg" type="submit" loading={isLoading}>
              Create new blog category
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
