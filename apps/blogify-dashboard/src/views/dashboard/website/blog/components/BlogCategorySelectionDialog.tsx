'use client';

import type { Blog, BlogCategory } from '@ps/types';

import { MdFormatListBulletedAdd } from 'react-icons/md';
import { useMutation } from 'react-query';
import { useState } from 'react';

import {
  DialogDescription,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogFooter,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';

export default function BlogCategorySelectionDialog({
  open,
  setOpen,
  blog,
  categories,
  refetch,
}: {
  open: boolean;
  setOpen: (_: boolean) => void;
  blog: Blog;
  categories: BlogCategory[];
  refetch: () => void;
}) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    (blog.categories as string[]) || []
  );

  const { mutateAsync: updateBlog, isLoading } = useMutation({
    mutationFn: () =>
      API.patch<Blog>(`blogs/${blog._id}`, { categories: selectedCategories }).then(() => {
        setOpen(false);
        refetch();
      }),
  });

  const toggleCategorySelection = (id: string) => {
    if (selectedCategories.includes(id)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== id));
    } else {
      setSelectedCategories([...selectedCategories, id]);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-xl border-none px-6 font-inter">
        <DialogHeader>
          <MdFormatListBulletedAdd className="text-primary" size={48} />
          <DialogTitle className="!mt-6 text-17 font-semibold">Add Blog to Categories</DialogTitle>
          <DialogDescription className="mt-2 !text-15 text-black">
            You can assign your blog to one or multiple categories.
          </DialogDescription>
        </DialogHeader>

        <div>
          <h3 className="text-15 font-medium">Blog Categories</h3>
          <ul className="mt-2 flex flex-col gap-2">
            {categories.map((category) => (
              <li
                className={cn(
                  'flex h-10 cursor-pointer items-center justify-between rounded-lg border border-gray10 pl-4 pr-2',
                  { 'border-primary': selectedCategories.includes(category._id) }
                )}
                onClick={() => toggleCategorySelection(category._id)}
                key={category._id}
              >
                <span className="text-15 font-medium">{category.name}</span>
              </li>
            ))}
          </ul>
        </div>

        <DialogFooter>
          <Button
            onClick={() => updateBlog()}
            className="mt-6 w-full"
            loading={isLoading}
            size="lg"
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
