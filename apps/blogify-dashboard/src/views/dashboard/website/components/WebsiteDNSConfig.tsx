import { useEffect, useState } from 'react';
import { useCopyToClipboard } from 'react-use';
import { GoCopy } from 'react-icons/go';
import toast from 'react-hot-toast';

import { Button } from '@ps/ui/components/button';

import { getDomainType } from '../utils';

type DomainType = 'empty' | 'invalid' | 'sub' | 'apex';
type DNSRecord = { type: 'A' | 'TXT' | 'CNAME'; name: string; value: string };

const DNS_CONFIG: Record<Exclude<DomainType, 'empty' | 'invalid'>, DNSRecord> = {
  apex: {
    type: 'A',
    name: '@',
    value: '***********',
  },
  sub: {
    type: 'CNAME',
    name: '',
    value: 'cname.vercel-dns.com',
  },
};

export default function WebsiteDNSConfig({ domain }: { domain: string }) {
  const [subDomainName, setSubDomainName] = useState<string>('');
  const [domainType, setDomainType] = useState<DomainType>('empty');

  useEffect(() => {
    setDomainType(getDomainType(domain));
    if (domain) {
      const sub = domain.split('.');
      sub.pop();
      sub.pop();
      setSubDomainName(sub.join('.'));
    }
  }, [domain]);

  return (
    <div className="mb-10">
      <h4 className="mb-2 text-15 font-medium">DNS Info</h4>
      {['empty', 'invalid'].includes(domainType) && (
        <div className="flex h-[103px] rounded-lg border border-gray10 bg-cornflowerBlue flex-center">
          <p className="text-13 text-gray9">
            Please add a valid domain or subdomain to get DNS info
          </p>
        </div>
      )}

      {(domainType === 'sub' || domainType === 'apex') && (
        <WebsiteDNSRecord
          record={{ ...DNS_CONFIG[domainType], name: DNS_CONFIG[domainType].name || subDomainName }}
        />
      )}
    </div>
  );
}

export const WebsiteDNSRecord = ({ record }: { record: DNSRecord }) => {
  const [, copy] = useCopyToClipboard();

  const copyText = (text: string) => {
    copy(text);
    toast.success('Copied');
  };

  return (
    <div className="flex rounded-lg border border-gray10 bg-cornflowerBlue">
      <div className="border-r border-gray10 p-3 pr-6">
        <h4 className="text-11 font-medium text-gray9">TYPE</h4>
        <h3 className="mb-2 mt-1 text-15 font-medium">{record.type}</h3>
        <Button
          className="h-6 rounded-md px-1.5 text-11 text-gray9"
          onClick={() => copyText(record.type)}
          variant="secondary"
        >
          <GoCopy />
          COPY
        </Button>
      </div>

      <div className="border-r border-gray10 p-3 pr-6">
        <h4 className="text-11 font-medium text-gray9">NAME</h4>
        <h3 className="mb-2 mt-1 text-15 font-medium">{record.name}</h3>
        <Button
          onClick={() => copyText(record.name)}
          className="h-6 rounded-md px-1.5 text-11 text-gray9"
          variant="secondary"
        >
          <GoCopy />
          COPY
        </Button>
      </div>

      <div className="overflow-hidden p-3">
        <h4 className="text-11 font-medium text-gray9">VALUE</h4>
        {/* eslint-disable-next-line tailwindcss/no-custom-classname */}
        <h3 className="h-scroll mb-2 mt-1 whitespace-nowrap text-15 font-medium">{record.value}</h3>
        <Button
          className="h-6 rounded-md px-1.5 text-11 text-gray9"
          onClick={() => copyText(record.value)}
          variant="secondary"
        >
          <GoCopy />
          COPY
        </Button>
      </div>
    </div>
  );
};
