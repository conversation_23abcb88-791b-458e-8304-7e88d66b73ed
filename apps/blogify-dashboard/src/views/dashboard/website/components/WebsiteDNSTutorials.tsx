import { HiOutlineLightBulb } from 'react-icons/hi2';
import { getDomainType } from '../utils';

const TUTORIALS = {
  sub: [
    {
      title: 'How to setup sub Domain with Blogify and Create your own Blog site!',
      duration: '1:23 Min',
      description: `In this tutorial, I'll guide you on how to add a CNAME record to your domain in Blogify.`,
      thumbnail: 'https://img.youtube.com/vi/r6ei3b7YzaE/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=r6ei3b7YzaE',
    },
    {
      title: 'How To Add CNAME Record in Cloudflare (Step By Step)',
      duration: '1:05 Min',
      description:
        'In this video, I show you how to add CNAME Record in Cloudflare. Did this video help? Comment below and let me know. Thanks!',
      thumbnail: 'https://img.youtube.com/vi/DwA8OGBx2to/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=DwA8OGBx2to',
    },
    {
      title: 'How to Add CNAME Record on GoDaddy Tutorial (2024)',
      duration: '1:08 Min',
      description: `In this tutorial, I'll guide you on how to add a CNAME record to your GoDaddy domain.`,
      thumbnail: 'https://img.youtube.com/vi/RGSlWIpTrWI/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=RGSlWIpTrWI',
    },
  ],
  apex: [
    {
      title: 'How to setup Blogify main domain and publish your own Blog site!',
      duration: '2:03 Min',
      description: `In this tutorial, I'll guide you on how to add a 'A' record to your domain in Blogify`,
      thumbnail: 'https://img.youtube.com/vi/ngGiBsB1krs/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=ngGiBsB1krs',
    },
    {
      title: 'How To Add A DNS Record To Cloudflare Domain - Update DNS Records in Cloudflare',
      duration: '1:12 Min',
      description:
        'Learn how to add a DNS record to Cloudflare domain in this step by step tutorial.',
      thumbnail: 'https://img.youtube.com/vi/F1q76vBkJFs/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=F1q76vBkJFs',
    },
    {
      title: 'How To Add DNS Record In Godaddy (2024)',
      duration: '1:10 Min',
      description: `In this tutorial, I'll guide you on how to add a 'A' record to your GoDaddy domain.`,
      thumbnail: 'https://img.youtube.com/vi/BUPRQaZ7rYA/hqdefault.jpg',
      url: 'https://www.youtube.com/watch?v=BUPRQaZ7rYA',
    },
  ],
};

export default function WebsiteDNSTutorials({ domain }: { domain: string }) {
  if (!domain) return null;

  const domainType = getDomainType(domain) as 'sub' | 'apex';

  if (!['sub', 'apex'].includes(domainType)) return null;

  return (
    <div className="mb-10 rounded-lg border border-gray10 p-4">
      <header className="flex justify-between">
        <div>
          <h2 className="text-17 font-semibold">Tutorials</h2>
          <p className="mt-1 text-15">
            Unsure about how to add DNS record? Watch the following tutorials.
          </p>
        </div>

        <HiOutlineLightBulb size={42} className="text-primary" />
      </header>

      {TUTORIALS[domainType].map((tutorial, i) => (
        <a
          className="mt-4 flex flex-wrap items-start gap-4 xs:flex-nowrap"
          href={tutorial.url}
          target="_blank"
          key={i}
        >
          <img
            className="aspect-video w-full rounded-lg object-cover xs:w-[160px]"
            src={tutorial.thumbnail}
          />

          <div>
            <h3 className="text-15 font-medium">{tutorial.title}</h3>
            <div className="mt-1 text-13 text-gray9">{tutorial.duration}</div>
            <p className="mt-1 text-13">{tutorial.description}</p>
          </div>
        </a>
      ))}
    </div>
  );
}
