import { zod<PERSON><PERSON><PERSON><PERSON>, z } from '@ps/ui';
import { LOCALES } from '@ps/common/constants/locale';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { Switch } from '@ps/ui/components/switch';
import FormField from '@ps/ui/form/FormField';
import TagInput from '@/components/form/TagInput';

// import { fetchUrlMeta } from '../../blog/create/utils/UrlService';
import { domainRegex } from '../utils';
import WebsiteDNSTutorials from './WebsiteDNSTutorials';
import WebsiteDNSConfig from './WebsiteDNSConfig';

const BLOCKED_DOMAINS = ['blogspot.com', 'wordpress.com'];

const extractDomain = (_url: string): string => {
  try {
    let url = _url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`;
    }

    const parsedUrl = new URL(url);
    return parsedUrl.hostname;
  } catch (error) {
    return '';
  }
};

const isBlockedDomain = (domain: string): boolean =>
  BLOCKED_DOMAINS.some(
    (blockedDomain) => domain.endsWith(`.${blockedDomain}`) || domain === blockedDomain
  );

const websiteSchema = z.object({
  name: z.string({ required_error: 'Website name is required.' }),
  url: z
    .string({ required_error: 'Website domain or subdomain is required.' })
    .refine((value) => domainRegex.test(value), {
      message: 'Invalid domain. It must be in the format of "example.com" or "sub.example.com".',
    })
    .refine((value) => !isBlockedDomain(value), {
      message: 'This domain is not supported. Please use a different domain.',
    }),
  // TODO: Find a better way handle this
  // .refine(async (url) => !(await fetchUrlMeta(`https://${url}`, 'Web Link'))?.title, {
  //   message:
  //     'This domain is currently hosting content. Please enter a domain that is not currently in use to host your blog.',
  // }),

  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  metaKeywords: z.array(z.string()).optional(),

  googleAnalyticsId: z.string().optional(),
  googleTagManagerId: z.string().optional(),

  defaultLanguage: z.string().default('en'),
  privacyPolicyUrl: z.string().url().optional(),
  commentsEnabled: z.boolean().optional(),
  ratingsEnabled: z.boolean().optional(),
  shareEnabled: z.boolean().optional(),
  tableOfContentsEnabled: z.boolean().optional(),
});
export type WebsiteSchema = z.infer<typeof websiteSchema>;

export default function WebsiteForm({
  submit,
  defaultValues = {} as WebsiteSchema,
  isSaving,
  error,
}: {
  submit: (values: WebsiteSchema) => void;
  defaultValues?: Partial<WebsiteSchema>;
  isSaving: boolean;
  error?: string;
}) {
  const { getInputFields, handleSubmit, getValues, setValue, setError, watch } =
    useForm<WebsiteSchema>({
      resolver: zodResolver(websiteSchema),
      defaultValues: {
        defaultLanguage: 'en',
        commentsEnabled: true,
        ratingsEnabled: true,
        shareEnabled: true,
        tableOfContentsEnabled: true,
        ...defaultValues,
      },
    });

  const url = watch('url');

  return (
    <form className="mx-auto max-w-3xl py-10" onSubmit={handleSubmit(submit)}>
      <h1 className="text-lg font-semibold">Configure</h1>
      <p className="mb-4 mt-1.5 text-md">
        Update your domains DNS setting to show blogs from Blogify. Once connected you will be able
        to customize your site & publish blogs.
      </p>

      <WebsiteDNSConfigSteps />

      <FormField label="Website Name" placeholder="eg: My Blog Site" {...getInputFields('name')} />
      <FormField
        containerClass="min-h-9"
        label="Domain or Subdomain"
        placeholder="eg: example.com or sub.example.com"
        {...getInputFields('url')}
        onBlur={(ev) => {
          setValue('url', extractDomain(url));
          getInputFields('url').onBlur(ev);
        }}
      />
      <div className="mb-10 flex items-center gap-2 rounded-lg bg-[#fef4e5] px-3 py-2.5">
        <img src="/images/icons/warn.svg" className="size-8" />
        <p className="text-13 text-[#da840b]">
          If you already have an active website then add a new subdomain. Make sure you are using a
          domain or subdomain that is currently empty & not showing any other content.
        </p>
      </div>

      <WebsiteDNSConfig domain={watch('url')} />
      <WebsiteDNSTutorials domain={watch('url')} />

      <div className="mb-10">
        <h3 className="mb-2 text-17 font-semibold">Search Engine Optimization</h3>
        <FormField
          label="Meta Title"
          placeholder="Give your new website a title"
          {...getInputFields('metaTitle')}
        />
        <FormField
          label="Meta Description"
          placeholder="Write something about your new website"
          type="textarea"
          {...getInputFields('metaDescription')}
        />
        <FormField className="min-h-8" label="Meta Keywords" type="custom">
          <TagInput
            setTags={(tags) => setValue('metaKeywords', tags)}
            setError={(message) => {
              if (message) setError('metaKeywords', { message });
            }}
            tags={watch('metaKeywords') || []}
            placeholder="Comma separated keywords"
            name="metaKeywords"
            type="tags"
          />
        </FormField>
      </div>

      <div className="mb-10">
        <h3 className="mb-2 text-17 font-semibold">Scripts</h3>
        <FormField
          label="Google Analytics ID"
          placeholder="ie: G-XXXXXXXXXX"
          {...getInputFields('googleAnalyticsId')}
        />
        <FormField
          label="Google Tag Manager ID"
          placeholder="ie: GTM-XXXXXXX"
          {...getInputFields('googleTagManagerId')}
        />
      </div>

      <div className="mb-10">
        <h3 className="mb-2 text-17 font-semibold">Site Settings</h3>

        <FormField
          className="mb-2"
          label="Default Language"
          type="select"
          {...getInputFields('defaultLanguage')}
        >
          {LOCALES.map((lang) => (
            <option key={lang.code} value={lang.code}>
              {lang.name}
            </option>
          ))}
        </FormField>

        <FormField
          className="mb-2"
          label="Privacy Policy URL"
          {...getInputFields('privacyPolicyUrl')}
        />

        <div>
          <label className="flex items-center gap-3 pb-2">
            <Switch
              defaultChecked={getValues('commentsEnabled')}
              onCheckedChange={(v) => setValue('commentsEnabled', v)}
            />
            <span className="text-md font-bold">Comments</span>
          </label>
          <p className="mb-6 text-md">
            If active people would be able to comment in your blog. You can turn it off. If turned
            off comments would not be visible in any blog.
          </p>
        </div>

        <div>
          <label className="flex cursor-pointer items-center gap-3 pb-2">
            <Switch
              defaultChecked={getValues('ratingsEnabled')}
              onCheckedChange={(v) => setValue('ratingsEnabled', v)}
            />
            <span className="text-md font-bold">Rating</span>
          </label>
          <p className="mb-6 text-md">
            If active people would be able to rate your blog. You can turn it off. If turned off
            blog ratings would not be visible and readers can not rate your blog.
          </p>
        </div>

        <div>
          <label className="flex cursor-pointer items-center gap-3 pb-2">
            <Switch
              defaultChecked={getValues('shareEnabled')}
              onCheckedChange={(v) => setValue('shareEnabled', v)}
            />
            <span className="text-md font-bold">Share</span>
          </label>
          <p className="mb-6 text-md">
            If active people would be able to share your blog. You can turn it off. If turned off
            blog sharing would not possible.
          </p>
        </div>

        <div>
          <label className="flex cursor-pointer items-center gap-3 pb-2">
            <Switch
              defaultChecked={getValues('tableOfContentsEnabled')}
              onCheckedChange={(v) => setValue('tableOfContentsEnabled', v)}
            />
            <span className="text-md font-bold">Show Table of Content</span>
          </label>
          <p className="mb-6 text-md">
            If active all blogs will show a table of content based on your blog content. Turn it off
            if you don't want to show table of content on your blog.
          </p>
        </div>
      </div>

      <div className="mt-4">
        {error && (
          <div className="mb-1 text-sm text-red">
            {error.includes('duplicate')
              ? 'Another website already existing with the same URL'
              : error}
          </div>
        )}
        <Button type="submit" className="w-full" loading={isSaving}>
          Save Settings
        </Button>
      </div>
    </form>
  );
}

const STEPS = [
  'Add your empty domain or subdomain link where you want to show your blogs.',
  `Go to your domain name provider's dashboard or cloudflare.`,
  `Find your DNS setting. Copy the below 'DNS info' and add as a new record.`,
  `Once DNS record is added click 'Save Settings' to connect your site with Blogify.`,
];
const WebsiteDNSConfigSteps = () => (
  <div className="mb-10 grid grid-cols-2 gap-4 sm:grid-cols-4">
    {STEPS.map((step, i) => (
      <div key={i} className="rounded-lg border border-gray10 p-3">
        <h6 className="text-11 font-medium text-primary">STEP - {i + 1}</h6>
        <p className="mt-2 text-13">{step}</p>
      </div>
    ))}
  </div>
);
