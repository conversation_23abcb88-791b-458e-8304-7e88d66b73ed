import type { APIResponse, Blog, Website, WebsiteSubscriber } from '@ps/types';

import { MdColor<PERSON><PERSON>, MdMoreVert, MdSettings, MdDelete } from 'react-icons/md';
import { MdOutlineShowChart, MdOutlineVerified } from 'react-icons/md';
import { useCopyToClipboard, useToggle } from 'react-use';
import { useMutation, useQuery } from 'react-query';
import { FaSitemap, FaLink } from 'react-icons/fa';
import { IoMdListBox } from 'react-icons/io';
import { TbUsers } from 'react-icons/tb';
import { BiCopy } from 'react-icons/bi';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';

import {
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenu,
} from '@ps/ui/components/dropdown-menu';
import Dialog, { DialogClose } from '@ps/ui/components/dialog';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import emptyAPIResponse from '@/types/resources';

import { WebsiteDNSRecord } from './WebsiteDNSConfig';

export default function WebsiteCard({
  website,
  refetchWebsites,
}: {
  website: Website;
  refetchWebsites: () => void;
}) {
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [, copy] = useCopyToClipboard();

  const { data: { total: totalBlogs } = emptyAPIResponse } = useQuery<APIResponse<Blog>>([
    `websites/${website._id}/blogs?page=1&limit=0`,
  ]);

  const { data: { total: totalSubscribers } = emptyAPIResponse } = useQuery<
    APIResponse<WebsiteSubscriber>
  >([`websites/${website._id}/subscribers?page=1&limit=1`]);

  const {
    mutateAsync: deleteWebsite,
    isLoading: deleting,
    error,
  } = useMutation({
    mutationFn: () =>
      API.remove(`websites/${website._id}`).then(() => {
        refetchWebsites();
        toggleConfirmationDialogOpen();
      }),
  });

  const { mutateAsync: updateWebsiteStatus, isLoading: updating } = useMutation({
    mutationFn: () =>
      API.patch(`websites/${website._id}`, { status: 'checking' }).then(refetchWebsites),
  });

  return (
    <div className="w-full rounded-xl border border-gray10 p-5">
      <div className="flex justify-between">
        {website.logo ? (
          <img className="h-9" src={website.logo} />
        ) : (
          <DefaultLogo website={website} />
        )}

        <div className="flex gap-2">
          {website.status === 'active' && (
            <Link to={`/dashboard/websites/${website._id}/theme`}>
              <button className="flex size-5 flex-center">
                <MdColorLens className="text-gray12" size={14} />
              </button>
            </Link>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex size-5 flex-center">
                <MdMoreVert className="text-gray12" size={14} />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {website.status === 'active' && (
                <>
                  <Link target="_blank" to={`https://${website.url}/sitemap.xml`}>
                    <DropdownMenuItem>
                      <FaSitemap className="-mt-px" />
                      View Sitemap
                    </DropdownMenuItem>
                  </Link>
                  <Link to={`/dashboard/websites/${website._id}/settings`}>
                    <DropdownMenuItem>
                      <MdSettings className="-mt-px" />
                      Settings
                    </DropdownMenuItem>
                  </Link>
                </>
              )}
              <DropdownMenuItem onClick={toggleConfirmationDialogOpen} className="text-red4">
                <MdDelete className="-mt-px" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <h2 className="mt-3 text-lg font-semibold">{website.name}</h2>

      <div className="flex items-center justify-between">
        <a
          className="flex w-11/12 shrink hover:underline"
          href={`https://${website.url}`}
          target="_blank"
        >
          <p className="mt-1.5 w-11/12 overflow-hidden text-ellipsis text-md text-gray9">
            https://{website.url}
          </p>
        </a>
        <button
          className="mt-0.5 flex size-5 w-1/12 flex-center"
          onClick={() => {
            copy(`https://${website.url}`);
            toast.success('Copied.');
          }}
        >
          <BiCopy className="text-gray12" size={14} />
        </button>
      </div>

      <ul className="mt-4 flex flex-col gap-2.5 rounded-lg bg-bg2 px-3 py-2.5 text-xs font-medium uppercase [&>li]:flex [&>li]:justify-between">
        <li>
          <div className="flex items-center gap-2">
            <FaLink />
            <span>DNS</span>
          </div>
          <div
            className={cn('h-4 rounded px-1.5 text-11 font-medium uppercase !text-white', {
              'bg-[#093]': website.status === 'active',
              'bg-[#e69019]': website.status === 'checking',
              'bg-red4': website.status === 'error',
            })}
          >
            {website.status}
          </div>
        </li>
        <li>
          <div className="flex items-center gap-2">
            <IoMdListBox />
            <span>Blogs</span>
          </div>
          <span>{totalBlogs}</span>
        </li>
        <li>
          <div className="flex items-center gap-2">
            <TbUsers />
            <span>Subscribers</span>
          </div>
          <span>{totalSubscribers}</span>
        </li>
        <li className="opacity-50">
          <div className="flex items-center gap-2">
            <MdOutlineShowChart />
            <span>Traffic</span>
          </div>
          <span>Coming Soon</span>
        </li>
      </ul>

      {website.status === 'checking' && (
        <p className="mt-2 text-13">
          Checking your DNS record... <br />
          This could take from 5 to 20 minute.
        </p>
      )}

      {website.status === 'error' && website.error && (
        <p className="mt-2 text-13 text-red">{website.error}</p>
      )}

      {website.status === 'active' ? (
        <Link to={`./${website._id}`}>
          <Button className="mt-3 w-full">View Details</Button>
        </Link>
      ) : website.verificationRecordValue ? (
        <Dialog
          trigger={
            <Button className="mt-3 w-full" variant="secondary">
              Verify Domain
            </Button>
          }
          Icon={MdOutlineVerified}
          title="Verification Info"
          description="Please add the following DNS record in order to verify your domain. Once added, please wait 5-20 minutes for your domain to be activated."
          actions={
            <DialogClose className="w-full">
              <Button className="w-full" loading={updating} onClick={() => updateWebsiteStatus()}>
                I've Added the DNS Record
              </Button>
            </DialogClose>
          }
        >
          <WebsiteDNSRecord
            record={{
              type: 'TXT',
              name: '_vercel',
              value: website.verificationRecordValue,
            }}
          />
        </Dialog>
      ) : (
        <Link to={`./update/${website._id}`}>
          <Button variant="secondary" className="mt-3 w-full">
            Update Settings
          </Button>
        </Link>
      )}

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        confirmationMessage={'Are you sure you want to delete this website?'}
        onClose={toggleConfirmationDialogOpen}
        onConfirm={deleteWebsite}
        error={error as string}
        confirming={deleting}
      />
    </div>
  );
}

const DefaultLogo = ({ website }: { website: Website }) => (
  <svg width="54" height="36" viewBox="0 0 54 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M0 23.849V7H3.89463V23.849H0ZM2.32226 23.849V20.2867H11.1033V23.849H2.32226Z"
      fill={website.brandColor}
    />
    <path
      d="M18.7456 24.1379C17.4715 24.1379 16.3427 23.8731 15.3589 23.3435C14.3913 22.798 13.6253 22.0518 13.0608 21.105C12.5125 20.1583 12.2384 19.0671 12.2384 17.8315C12.2384 16.5959 12.5125 15.5047 13.0608 14.558C13.6092 13.6112 14.3671 12.8731 15.3347 12.3435C16.3185 11.798 17.4312 11.5252 18.673 11.5252C19.947 11.5252 21.0678 11.798 22.0354 12.3435C23.0192 12.8731 23.7852 13.6112 24.3335 14.558C24.8818 15.5047 25.156 16.5959 25.156 17.8315C25.156 19.0671 24.8818 20.1583 24.3335 21.105C23.7852 22.0518 23.0272 22.798 22.0596 23.3435C21.1081 23.8731 20.0034 24.1379 18.7456 24.1379ZM18.7214 20.5755C19.2213 20.5755 19.6487 20.4632 20.0034 20.2385C20.3744 19.9978 20.6646 19.6769 20.8743 19.2757C21.0839 18.8585 21.1888 18.3771 21.1888 17.8315C21.1888 17.2859 21.0759 16.8125 20.8501 16.4114C20.6405 15.9942 20.3502 15.6652 19.9793 15.4245C19.6083 15.1838 19.181 15.0635 18.6972 15.0635C18.2134 15.0635 17.786 15.1838 17.4151 15.4245C17.0442 15.6652 16.7539 15.9942 16.5442 16.4114C16.3346 16.8125 16.2298 17.2859 16.2298 17.8315C16.2298 18.3611 16.3346 18.8344 16.5442 19.2516C16.7539 19.6689 17.0442 19.9978 17.4151 20.2385C17.8021 20.4632 18.2376 20.5755 18.7214 20.5755Z"
      fill={website.brandColor}
    />
    <path
      d="M32.7952 29C31.8114 29 30.9486 28.9358 30.2068 28.8074C29.4811 28.6791 28.8602 28.5106 28.3442 28.302C27.8281 28.0934 27.4007 27.8607 27.0621 27.6039L28.5377 24.6193C28.7957 24.7316 29.1021 24.868 29.4569 25.0284C29.8117 25.205 30.2471 25.3574 30.7632 25.4858C31.2792 25.6142 31.9001 25.6783 32.6258 25.6783C33.1903 25.6783 33.6902 25.566 34.1256 25.3414C34.5772 25.1328 34.9239 24.7958 35.1658 24.3304C35.4238 23.8811 35.5529 23.3115 35.5529 22.6214V11.814H39.3265V22.477C39.3265 23.8249 39.0685 24.9883 38.5525 25.9672C38.0364 26.946 37.2946 27.6922 36.3269 28.2057C35.3593 28.7352 34.1821 29 32.7952 29ZM31.9243 23.3917C30.7954 23.3917 29.8198 23.167 28.9973 22.7177C28.191 22.2524 27.562 21.5945 27.1105 20.744C26.675 19.8935 26.4573 18.8826 26.4573 17.7112C26.4573 16.4274 26.675 15.3282 27.1105 14.4136C27.562 13.4989 28.191 12.7929 28.9973 12.2954C29.8198 11.7819 30.7954 11.5252 31.9243 11.5252C32.8597 11.5252 33.666 11.7819 34.3433 12.2954C35.0368 12.7929 35.569 13.5069 35.9399 14.4376C36.3108 15.3683 36.4963 16.4836 36.4963 17.7834C36.4963 18.9387 36.3108 19.9336 35.9399 20.7681C35.569 21.6025 35.0368 22.2524 34.3433 22.7177C33.666 23.167 32.8597 23.3917 31.9243 23.3917ZM33.0371 20.2626C33.5209 20.2626 33.9321 20.1503 34.2708 19.9256C34.6256 19.6849 34.8917 19.364 35.0691 18.9628C35.2626 18.5616 35.3593 18.1043 35.3593 17.5908C35.3593 17.0452 35.2626 16.5718 35.0691 16.1707C34.8755 15.7695 34.6014 15.4566 34.2466 15.2319C33.9079 15.0073 33.4967 14.895 33.0129 14.895C32.5452 14.895 32.1259 15.0073 31.755 15.2319C31.4002 15.4566 31.118 15.7695 30.9083 16.1707C30.7148 16.5718 30.61 17.0452 30.5939 17.5908C30.61 18.1043 30.7148 18.5616 30.9083 18.9628C31.118 19.364 31.4083 19.6849 31.7792 19.9256C32.1501 20.1503 32.5694 20.2626 33.0371 20.2626Z"
      fill={website.brandColor}
    />
    <path
      d="M47.5896 24.1379C46.3156 24.1379 45.1867 23.8731 44.203 23.3435C43.2353 22.798 42.4693 22.0518 41.9049 21.105C41.3566 20.1583 41.0824 19.0671 41.0824 17.8315C41.0824 16.5959 41.3566 15.5047 41.9049 14.558C42.4532 13.6112 43.2112 12.8731 44.1788 12.3435C45.1625 11.798 46.2753 11.5252 47.517 11.5252C48.791 11.5252 49.9118 11.798 50.8795 12.3435C51.8632 12.8731 52.6292 13.6112 53.1775 14.558C53.7258 15.5047 54 16.5959 54 17.8315C54 19.0671 53.7258 20.1583 53.1775 21.105C52.6292 22.0518 51.8713 22.798 50.9037 23.3435C49.9522 23.8731 48.8475 24.1379 47.5896 24.1379ZM47.5654 20.5755C48.0653 20.5755 48.4927 20.4632 48.8475 20.2385C49.2184 19.9978 49.5087 19.6769 49.7183 19.2757C49.928 18.8585 50.0328 18.3771 50.0328 17.8315C50.0328 17.2859 49.9199 16.8125 49.6941 16.4114C49.4845 15.9942 49.1942 15.6652 48.8233 15.4245C48.4524 15.1838 48.025 15.0635 47.5412 15.0635C47.0574 15.0635 46.63 15.1838 46.2591 15.4245C45.8882 15.6652 45.5979 15.9942 45.3883 16.4114C45.1786 16.8125 45.0738 17.2859 45.0738 17.8315C45.0738 18.3611 45.1786 18.8344 45.3883 19.2516C45.5979 19.6689 45.8882 19.9978 46.2591 20.2385C46.6462 20.4632 47.0816 20.5755 47.5654 20.5755Z"
      fill={website.brandColor}
    />
  </svg>
);
