import { useParams } from 'react-router-dom';

import DashboardContainer from '../layout/DashboardContainer';
import WebsiteProvider from './details/context/WebsiteProvider';
import WebsiteForm from './components/WebsiteForm';

export default function WebsiteEdit() {
  const { id } = useParams() as { id: string };

  return (
    <DashboardContainer title="Update Settings">
      <WebsiteProvider id={id}>
        {({ website, isUpdating, updateError, update }) => (
          <>
            {website._id && (
              <WebsiteForm
                submit={update}
                isSaving={isUpdating}
                error={updateError}
                defaultValues={website}
              />
            )}
          </>
        )}
      </WebsiteProvider>
    </DashboardContainer>
  );
}
