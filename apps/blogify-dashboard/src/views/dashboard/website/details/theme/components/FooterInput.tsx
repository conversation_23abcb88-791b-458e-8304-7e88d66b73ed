import { debounce } from 'lodash';
import { useState } from 'react';

import { Input } from '@ps/ui/components/input';

import useWebsite from '../../context';

export default function FooterInput() {
  const { website, update } = useWebsite();
  const [footer, setFooter] = useState<string>(
    website.footerText || `© ${new Date().getFullYear()} All rights reserved.`
  );

  const onChange = (f: string) => {
    setFooter(f);
    update({ footerText: f });
  };

  return (
    <section className="mt-10">
      <h3 className="text-md font-medium">Footer</h3>

      <div className="mt-2 flex flex-col items-start gap-6 md:flex-row">
        <div className="w-full md:w-2/3">
          <div className="flex h-14 items-center rounded-lg border border-gray10 bg-bg2 px-6">
            <span className="text-sm text-gray9">{footer}</span>
          </div>

          <Input
            className="mt-2"
            defaultValue={footer}
            onChange={debounce((ev) => onChange(ev.target.value), 500)}
          />
        </div>

        <p className="w-full text-sm text-gray9 md:w-1/3">
          ** This info will be visible in your blog footer.
        </p>
      </div>
    </section>
  );
}
