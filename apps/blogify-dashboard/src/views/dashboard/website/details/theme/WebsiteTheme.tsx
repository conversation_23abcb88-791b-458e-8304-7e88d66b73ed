import { FaAsterisk } from 'react-icons/fa';

import { getPackageAndPeriod } from '@/utils';
import { useStoreState } from '@/store';
import { Switch } from '@ps/ui/components/switch';

import { SectionHeader } from './components';
import BrandColorSelection from './components/BrandColorSelection';
import ThemeSelection from './components/ThemeSelection';
import FontSelection from './components/FontSelection';
import FavIconUpload from './components/FavIconUpload';
import FooterInput from './components/FooterInput';
import LinksInput from './components/LinksInput';
import LogoUpload from './components/LogoUpload';
import useWebsite from '../context';

export default function WebsiteTheme() {
  return (
    <div className="mx-auto max-w-[1036px] py-6">
      <ThemeSelection />
      <FontSelection />

      <SectionHeader
        title="Personalization"
        description="Personalize your website with logo, brand color, nav links, and footer text."
      >
        <FaAsterisk className="text-primary" size={40} />
      </SectionHeader>

      <LogoUpload />
      <FavIconUpload />
      <BrandColorSelection />
      <LinksInput type="header" />
      <LinksInput type="footer" />
      <FooterInput />
      <BlogifyBrandingToggle />
    </div>
  );
}

const BlogifyBrandingToggle = () => {
  const { website, update } = useWebsite();
  const user = useStoreState((s) => s.user.current);
  const { plan } = getPackageAndPeriod(user.subscriptionPlan);

  if (!['premium', 'business', 'enterprise', 'unlimited'].includes(plan)) return;

  return (
    <div className="mt-10">
      <label className="flex cursor-pointer items-center gap-3 pb-2">
        <Switch
          defaultChecked={
            typeof website.showBlogifyBranding === 'undefined' ? true : website.showBlogifyBranding
          }
          onCheckedChange={(v) => update({ showBlogifyBranding: v })}
        />
        <span className="text-md font-bold">Show Powered By Blogify</span>
      </label>
      <p className="mb-6 text-md">
        If active your blog site will show Powered by Blogify logo on the footer .
      </p>
    </div>
  );
};
