import { useState } from 'react';

import { hexToRgba } from '@ps/common/utils/color';

import useWebsite from '../../context/index';

export default function BrandColorSelection() {
  const { website, update } = useWebsite();
  const [color, setColor] = useState<string>(website.brandColor || '#F2470D');

  const onChange = (c: string) => {
    setColor(c);
    update({ brandColor: c });
  };

  return (
    <section className="mt-10">
      <h3 className="text-md font-medium">Brand Color</h3>

      <div className="mt-2 grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
        <div className="flex h-10 items-center gap-3 rounded-lg border border-gray10 px-1.5">
          <input
            className="size-7 rounded-md border-none p-0 hover:cursor-pointer"
            onChange={(ev) => onChange(ev.target.value)}
            value={color}
            type="color"
          />
          <span className="text-md font-medium">{color}</span>
        </div>

        <div className="flex text-md font-medium">
          <div
            className="flex h-10 w-1/2 rounded-l-lg text-black flex-center"
            style={{ backgroundColor: hexToRgba(color, 5) }}
          >
            Background
          </div>
          <div
            className="flex h-10 w-1/2 rounded-r-lg text-white flex-center"
            style={{ backgroundColor: color }}
          >
            Action
          </div>
        </div>

        <p className="text-sm text-gray9">
          ** When choosing color make sure it's accessible & readable.
        </p>
      </div>
    </section>
  );
}
