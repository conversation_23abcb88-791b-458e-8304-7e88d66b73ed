import { FaPalette } from 'react-icons/fa6';

import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';

import { SectionHeader } from '.';

export default function ThemeSelection() {
  return (
    <section>
      <SectionHeader
        title="Blog Theme"
        description="Select a theme that best suits with your content."
        className="pt-0"
      >
        <FaPalette className="text-primary" size={40} />
      </SectionHeader>

      <RadioGroup
        className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3"
        defaultValue="default"
      >
        <label className="rounded-xl border border-gray10 p-4">
          <div className="flex items-center gap-2.5">
            <RadioGroupItem value="default" />
            <span className="text-md font-semibold">Default</span>
          </div>
          <img className="mt-4 rounded-lg" src="/images/page-assets/website/theme-default.png" />
        </label>

        <label className="rounded-xl border border-gray10 p-4">
          <div className="flex items-center gap-2.5">
            <RadioGroupItem value="personal" disabled />
            <span className="text-md font-semibold">Personal</span>
          </div>
          <img
            className="mt-4 rounded-lg"
            src="/images/page-assets/website/theme-coming-soon.png"
          />
        </label>

        <label className="rounded-xl border border-gray10 p-4">
          <div className="flex items-center gap-2.5">
            <RadioGroupItem value="news" disabled />
            <span className="text-md font-semibold">News</span>
          </div>
          <img
            className="mt-4 rounded-lg"
            src="/images/page-assets/website/theme-coming-soon.png"
          />
        </label>
      </RadioGroup>
    </section>
  );
}
