import { useState } from 'react';

import { FileUpload, FileUploadProgress } from '@/modules/file-upload/FileUpload';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';

import useWebsite from '../../context';

export default function LogoUpload() {
  const { website, update } = useWebsite();
  const [logo, setLogo] = useState(website.logo || '/images/page-assets/website/logo-default.png');

  return (
    <FileUploadProvider
      onUploadComplete={(files) => {
        if (files[0]) {
          setTimeout(() => {
            const { url } = files[0];
            setLogo(url);
            update({ logo: url });
          }, 500);
        }
      }}
      folder="publish/logo"
      type="image"
    >
      {({ isUploading }) => (
        <section>
          <h3 className="text-md font-medium">Brand Logo</h3>

          <div className="mt-2 flex flex-col items-start gap-6 md:flex-row">
            <div className="w-full md:w-2/3">
              <div className="flex h-14 items-center rounded-lg border border-gray10 px-6">
                <img className="h-8" src={logo} />
              </div>

              <div className="mt-2 rounded-lg border border-dashed border-gray12">
                {!isUploading && (
                  <FileUpload
                    supportedFormats=".jpeg, .jpg, .png, .gif, .bmp, .tiff, .tif, .webp, .svg, .heic, .heif"
                    className="sm:min-h-52"
                  />
                )}

                {isUploading && <FileUploadProgress className="sm:min-h-52" />}
              </div>
            </div>

            <p className="w-full text-sm text-gray9 md:w-1/3">
              ** Upload your brand logo and wait for it to upload. You can upload again to replace
              the current logo.
            </p>
          </div>
        </section>
      )}
    </FileUploadProvider>
  );
}
