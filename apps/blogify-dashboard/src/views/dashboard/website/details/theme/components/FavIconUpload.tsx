import { useState } from 'react';

import { FileUpload, FileUploadProgress } from '@/modules/file-upload/FileUpload';
import FileUploadProvider from '@/modules/file-upload/FileUploadProvider';

import useWebsite from '../../context';

export default function FavIconUpload() {
  const { website, update } = useWebsite();
  const [favicon, setFavIcon] = useState(website.favicon || 'https://blogify.ai/favicon.ico');

  return (
    <FileUploadProvider
      onUploadComplete={(files) => {
        if (files[0]) {
          setTimeout(() => {
            const { url } = files[0];
            setFavIcon(url);
            update({ favicon: url });
          }, 500);
        }
      }}
      folder="publish/favicon"
      type="image"
    >
      {({ isUploading }) => (
        <section className="mt-10">
          <h3 className="text-md font-medium">FavIcon</h3>

          <div className="mt-2 flex flex-col items-start gap-6 md:flex-row">
            <div className="w-full md:w-2/3">
              <div className="flex h-14 items-center rounded-lg border border-gray10 px-6">
                <img className="h-8" src={favicon} />
              </div>

              <div className="mt-2 rounded-lg border border-dashed border-gray12">
                {!isUploading && (
                  <FileUpload
                    maxFileSize={0.5 * 1024 * 1024}
                    supportedFormats=".ico"
                    className="sm:min-h-52"
                  />
                )}

                {isUploading && <FileUploadProgress className="sm:min-h-52" />}
              </div>
            </div>

            <p className="w-full text-sm text-gray9 md:w-1/3">
              ** Upload your brand favicon and wait for it to upload. You can upload again to
              replace the current favicon.
            </p>
          </div>
        </section>
      )}
    </FileUploadProvider>
  );
}
