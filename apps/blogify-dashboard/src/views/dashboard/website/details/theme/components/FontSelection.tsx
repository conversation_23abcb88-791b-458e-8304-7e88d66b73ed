import { RiFontSize } from 'react-icons/ri';

import { RadioGroup, RadioGroupItem } from '@ps/ui/components/radio-group';

import { SectionHeader } from '.';

export default function FontSelection() {
  return (
    <section>
      <SectionHeader
        title="Font Style"
        description="Select a font that best suits with your content."
      >
        <RiFontSize className="text-primary" size={40} />
      </SectionHeader>

      <div className="flex flex-col justify-between rounded-xl border border-gray10 md:flex-row">
        <div>
          <RadioGroup defaultValue="font-inter">
            <label className="flex items-center gap-4 p-4">
              <RadioGroupItem value="font-inter" />
              <div>
                <h1 className="text-3xl font-semibold">This is a blog title</h1>
                <p className="mt-1 line-clamp-1 text-lg">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua.
                </p>
              </div>
            </label>
          </RadioGroup>
          <img
            className="md:rounded-bl-xl xl:max-w-[634px]"
            src="/images/page-assets/website/font-coming-soon.png"
          />
        </div>

        <div className="border-t border-gray10 p-4 md:border-l md:border-t-0">
          <img className="rounded-lg" src="/images/page-assets/website/font-inter-preview.png" />
        </div>
      </div>
    </section>
  );
}
