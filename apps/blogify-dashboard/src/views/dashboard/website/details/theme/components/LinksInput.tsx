import type { Website } from '@ps/types';

import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { MdOutlineRemoveCircleOutline } from 'react-icons/md';
import { RiDraggable } from 'react-icons/ri';
import { useDebounce } from 'react-use';
import { useState } from 'react';

import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { cn } from '@ps/ui/lib/utils';

import useWebsite from '../../context/index';

const reorder = (list: Website['links'], startIndex: number, endIndex: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

export default function LinksInput({ type }: { type: 'header' | 'footer' }) {
  const { website, update } = useWebsite();
  const [links, setLinks] = useState<Website['links']>(
    website[type === 'footer' ? 'footerLinks' : 'links']
  );

  const addLink = () => {
    if (links.length === 7) return;
    setLinks([...links, { title: '', url: '' }]);
  };

  const removeLink = (index: number) => {
    setLinks(links.filter((_, i) => index !== i));
  };

  const onChange = (
    field: 'title' | 'url',
    index: number,
    ev: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = ev.target;
    const _links = [...links];
    _links[index][field] = value;
    setLinks(_links);
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = reorder(links, result.source.index, result.destination.index);
    setLinks(items);
  };

  useDebounce(() => update({ [type === 'footer' ? 'footerLinks' : 'links']: links }), 500, [links]);

  return (
    <section className="mt-10">
      <h3 className="text-md font-medium">{type === 'header' ? 'Navigation' : 'Footer'} Links</h3>

      <div className="mt-2 flex flex-col items-start gap-6 md:flex-row">
        <div className="w-full md:w-2/3">
          <div className="flex h-14 items-center justify-center gap-10 rounded-lg border border-gray10 px-6 text-md font-medium">
            {links.map((l, i) => (
              <span key={i}>{l.title}</span>
            ))}
          </div>

          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="list">
              {(provided) => (
                <ul className="mt-2" {...provided.droppableProps} ref={provided.innerRef}>
                  {links.map((l, i) => (
                    <Draggable key={i} index={i} draggableId={String(i)}>
                      {(draggableProvided, snapshot) => (
                        <li
                          className="flex items-center [&>div]:first:rounded-t-lg [&>div]:last:rounded-b-lg"
                          {...draggableProvided.dragHandleProps}
                          {...draggableProvided.draggableProps}
                          ref={draggableProvided.innerRef}
                        >
                          <Button variant="icon" className="pl-0 pr-2">
                            <RiDraggable className="rotate-90" />
                          </Button>
                          <div
                            className={cn('-mt-px flex w-full border border-gray10', {
                              '!rounded-none': snapshot.isDragging,
                            })}
                          >
                            <Input
                              className="w-1/2 border-none text-md font-medium"
                              onChange={(ev) => onChange('title', i, ev)}
                              placeholder="Title"
                              value={l.title}
                            />

                            <div className="border-l border-gray10" />

                            <Input
                              className="w-1/2 border-none text-sm font-medium text-gray9"
                              onChange={(ev) => onChange('url', i, ev)}
                              placeholder="Url"
                              value={l.url}
                            />

                            <Button
                              className="size-10 rounded-none border-l border-gray10"
                              onClick={() => removeLink(i)}
                              variant="icon"
                            >
                              <div>
                                <MdOutlineRemoveCircleOutline size={20} className="text-gray9" />
                              </div>
                            </Button>
                          </div>

                          <Button variant="icon" className="pl-2 pr-0">
                            <RiDraggable className="rotate-90" />
                          </Button>
                        </li>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </ul>
              )}
            </Droppable>
          </DragDropContext>

          <Button
            className="ml-5 mt-2 h-6"
            disabled={links.length === 7}
            variant="secondary"
            onClick={addLink}
            size="xxs"
          >
            <span className="text-gray9">Add Link</span>
          </Button>
        </div>

        <p className="w-full text-sm text-gray9 md:w-1/3">
          ** Add navigation title & links to your blog. You can drag & drop navigation links to
          quickly sort them to your need. You can add upto 7 navigation links.
        </p>
      </div>
    </section>
  );
}
