import type { WebsiteSchema } from '../components/WebsiteForm';

import WebsiteForm from '../components/WebsiteForm';
import useWebsite from './context';

export default function WebsiteSettings() {
  const { website, isUpdating, updateError, update } = useWebsite();

  const submit = (values: WebsiteSchema) => {
    values.url = values.url.toLowerCase();
    update(values);
  };

  return (
    <WebsiteForm
      submit={submit}
      isSaving={isUpdating}
      error={updateError}
      defaultValues={website}
    />
  );
}
