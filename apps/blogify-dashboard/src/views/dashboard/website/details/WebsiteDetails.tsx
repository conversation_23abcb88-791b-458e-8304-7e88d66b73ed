import { Outlet, <PERSON>, usePara<PERSON>, useLocation } from 'react-router-dom';
import { LuNotebookPen } from 'react-icons/lu';

import { Tabs<PERSON><PERSON>ger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { useStoreState } from '@/store';
import { Button } from '@ps/ui/components/button';
import DashboardContainer from '../../layout/DashboardContainer';
import WebsiteProvider from './context/WebsiteProvider';

const TABS: { name: string }[] = [
  { name: 'Blogs' },
  { name: 'Analytics' },
  { name: 'Subscribers' },
  { name: 'Theme' },
  { name: 'Setting<PERSON>' },
];

export default function WebsiteDetails() {
  const user = useStoreState((s) => s.user.current);
  const { id } = useParams() as { id: string };
  const location = useLocation();

  const selectedTab = TABS.find((t) => location.pathname.endsWith(t.name.toLowerCase()))?.name;

  const showCreateBlogAction = [
    `/dashboard/websites/${id}`,
    `/dashboard/websites/${id}/blogs`,
  ].includes(location.pathname);

  return (
    <WebsiteProvider id={id}>
      {({ website, isLoading }) => (
        <DashboardContainer
          title={isLoading ? 'Loading...' : website.name}
          actions={
            <>
              {showCreateBlogAction && (
                <Link
                  to={
                    user.email.endsWith('@blogify.ai')
                      ? `/dashboard/websites/${id}/blogs/create`
                      : '/dashboard/blogs/select-source'
                  }
                >
                  <Button>
                    <LuNotebookPen />
                    Create New Blog
                  </Button>
                </Link>
              )}
            </>
          }
        >
          <Tabs defaultValue={selectedTab || 'Blogs'}>
            <TabsList variant="nav">
              {TABS.map((t) => (
                <Link to={`./${t.name.toLowerCase()}`} key={t.name}>
                  <TabsTrigger value={t.name} variant="nav">
                    {t.name}
                  </TabsTrigger>
                </Link>
              ))}
            </TabsList>

            {website._id && <Outlet />}
          </Tabs>
        </DashboardContainer>
      )}
    </WebsiteProvider>
  );
}
