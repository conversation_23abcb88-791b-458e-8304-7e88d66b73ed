import type {
  DNSActivityType,
  AnalyticsData,
  BlogDataType,
} from '@/views/dashboard/analytics/Analytics';

import { useQuery } from 'react-query';
import { useState } from 'react';
import dayjs from 'dayjs';

import AnalyticsDateSelector from '@/views/dashboard/analytics/components/AnalyticsDateSelector';
import StateHandler from '@/components/misc/StateHandler';
import DnsActivity from '@/components/commonActivity/activity/DnsActivity';

import BlogAnalytics from '../../analytics/BlogAnalytics';
import useWebsite from './context';

export default function WebsiteAnalytics() {
  const [dateRange, setDateRange] = useState([dayjs().startOf('day'), dayjs().endOf('day')]);
  const { website } = useWebsite();

  const [from, to] = [dateRange[0].toISOString(), dateRange[1].toISOString()];

  const { data: analytics } = useQuery<AnalyticsData>([
    `websites/${website._id}/analytics?from=${from}&to=${to}`,
  ]);

  const { data: activity, isLoading: activityLoading } = useQuery<
    DNSActivityType & { blogs: BlogDataType[] }
  >([`websites/${website._id}/analytics/activity?from=${from}&to=${to}`]);

  const isActivityEmpty =
    !activity?.sales.length && !activity?.clicks.length && !activity?.views.length;

  return (
    <>
      <div className="py-6">
        <h3 className="text-17 font-semibold">Website Analytics</h3>
        <p className="mt-1 text-15">See how your content is preforming around the globe.</p>
      </div>

      <AnalyticsDateSelector className="mb-4" onChange={setDateRange} />

      <div className="rounded-lg border border-gray10">
        <div className="pr-4">
          <h4 className="p-4 text-15 font-semibold">Blog Activity</h4>

          {activityLoading || isActivityEmpty ? (
            <StateHandler
              loading={activityLoading}
              className="min-h-[40vh]"
              isEmpty={isActivityEmpty}
            />
          ) : (
            <DnsActivity dnsActivityData={activity} blogsData={activity?.blogs} showBlog />
          )}
        </div>

        <BlogAnalytics analyticsData={analytics} />
      </div>
    </>
  );
}
