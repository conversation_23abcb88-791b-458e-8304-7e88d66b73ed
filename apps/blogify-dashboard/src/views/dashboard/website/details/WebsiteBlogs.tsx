import type { APIResponse, Blog, BlogCategory } from '@ps/types';
import type { Blog as BlogType } from '@ps/types';

import { useMutation, useQuery } from 'react-query';
import { TbCategoryPlus } from 'react-icons/tb';
import { LuNotebookPen } from 'react-icons/lu';
import { IoMdListBox } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { IoMdEyeOff } from 'react-icons/io';
import { useState } from 'react';
import { IoEye } from 'react-icons/io5';

import { TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { DropdownMenuItem } from '@ps/ui/components/dropdown-menu';
import { parseQuery } from '@/utils';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';
import Loader from '@/components/misc/Loader';
import Link from '@/components/common/Link';

import BlogCategorySelectionDialog from '../blog/components/BlogCategorySelectionDialog';
import BlogCategorySettingsDialog from '../blog/components/BlogCategorySettingsDialog';
import BlogCategoryCreateDialog from '../blog/components/BlogCategoryCreateDialog';
import useWebsite from './context';
import BlogCard from '../../blog/components/BlogCard';

export default function WebsiteBlogs() {
  const { limit = '10', page = '1' } = parseQuery();

  const [selectedCategory, selectCategory] = useState('');
  const [blog, setBlog] = useState<Blog>({} as Blog);
  const { website } = useWebsite();
  const navigate = useNavigate();

  const { data: { data: categories } = emptyAPIResponse, refetch: refetchCategories } = useQuery<
    APIResponse<BlogCategory>
  >([`blog-categories?s={"$and":[{"website":{"$eq":"${website._id}"}}]}`]);

  const {
    data: { data: blogs, total } = emptyAPIResponse,
    isFetching,
    refetch,
  } = useQuery<APIResponse<Blog>>([
    `websites/${website._id}/blogs?page=${page}&limit=${limit}${selectedCategory === '' ? '' : `&category=${selectedCategory}`}`,
  ]);

  const { mutateAsync: toggleVisibility } = useMutation({
    mutationFn: ({ siteId, blogId }: { siteId: string; blogId: string }) =>
      API.post(`blogify/${siteId}/blogs/${blogId}/toggle-visibility`).then(() => refetch()),
  });

  return (
    <div>
      <Tabs defaultValue={selectedCategory || 'All Blogs'}>
        <div className="relative flex justify-between gap-4">
          <TabsList variant="badge">
            {[{ _id: '', bid: '', name: 'All Blogs' }, ...categories].map((category) => (
              <TabsTrigger
                onClick={() => selectCategory(category._id)}
                value={category.name}
                key={category._id}
                variant="badge"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="flex items-center gap-2">
            <BlogCategoryCreateDialog website={website} refetch={refetchCategories} />
            <BlogCategorySettingsDialog categories={categories} refetch={refetchCategories} />
          </div>
        </div>
      </Tabs>

      {blogs.map((b, i) => {
        const visibility = b.publishResult?.find(
          (pr) => pr.integration === 'blogify' && pr.siteID === website._id
        )?.visibility;
        const isHidden = visibility === 'hidden';

        return (
          <BlogCard
            key={i}
            blog={b as BlogType}
            siteId={website._id}
            refetch={refetch}
            visibility={visibility}
            variant="website-blog-list"
            className={cn({ 'opacity-90': isFetching })}
            menu={
              <>
                <DropdownMenuItem onClick={() => setBlog(b)}>
                  <TbCategoryPlus />
                  Assign Categories
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => toggleVisibility({ siteId: website._id, blogId: b._id })}
                >
                  {isHidden ? <IoEye /> : <IoMdEyeOff />}
                  {isHidden ? 'Publish Again' : 'Hide'}
                </DropdownMenuItem>
              </>
            }
          />
        );
      })}

      {blogs.length === 0 && isFetching && (
        <div className="flex min-h-96 flex-center">
          <Loader />
        </div>
      )}

      {blogs.length === 0 && !isFetching && (
        <div className="flex h-[calc(100vh-380px)] flex-col text-center text-black4 flex-center">
          <img src="/images/icons/empty-blogs.svg" />
          <h3 className="mt-4 text-17 font-semibold">No Published Blogs</h3>
          <p className="mb-4 mt-2 text-15">
            To show blogs in this website first create or select old blogs, then publish, select
            Blogify and then select this website.
          </p>
          <div className="flex gap-3">
            <Link to="/dashboard/blogs">
              <Button variant="secondary">
                <IoMdListBox />
                Go to My Blogs
              </Button>
            </Link>

            <Link to="/dashboard/blogs/select-source">
              <Button>
                <LuNotebookPen />
                Create New Blog
              </Button>
            </Link>
          </div>
        </div>
      )}

      {!!blogs.length && (
        <Pagination
          className="mt-6"
          onPaging={(url) => navigate(url)}
          limit={parseInt(limit, 10)}
          page={parseInt(page, 10)}
          total={total}
        />
      )}

      {blog._id && (
        <BlogCategorySelectionDialog
          setOpen={() => setBlog({} as Blog)}
          categories={categories}
          open={!!blog._id}
          refetch={refetch}
          blog={blog}
        />
      )}
    </div>
  );
}
