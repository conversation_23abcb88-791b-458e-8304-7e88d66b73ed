import type { WebsiteContextType } from './index';
import type { Website } from '@ps/types';

import { useMutation, useQuery } from 'react-query';
import { API } from '@/services/api';

import { WebsiteContext } from './index';

const WebsiteProvider = ({
  children,
  id,
}: {
  children: React.ReactNode | ((_: WebsiteContextType) => React.ReactNode);
  id: string;
}) => {
  const { data: website = {} as Website, isLoading, refetch } = useQuery<Website>(`websites/${id}`);

  const {
    mutateAsync: update,
    isLoading: isUpdating,
    error: updateError,
  } = useMutation({
    mutationFn: (data: Partial<Website>) => {
      data.url = data.url?.toLowerCase();
      return API.patch(`websites/${id}`, data).then((r) => {
        refetch();
        return r;
      }) as Promise<Website>;
    },
  });

  const value = {
    isLoading,
    website,

    updateError: updateError as string,
    isUpdating,
    update,
  };

  return (
    <WebsiteContext.Provider value={value}>
      <>{typeof children === 'function' ? children(value) : children}</>
    </WebsiteContext.Provider>
  );
};

export default WebsiteProvider;
