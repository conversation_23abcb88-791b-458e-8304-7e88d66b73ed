import type { Website } from '@ps/types';

import { createContext, useContext } from 'react';

export interface WebsiteContextType {
  website: Website;
  isLoading: boolean;

  updateError: string;
  isUpdating: boolean;
  update: (website: Partial<Website>) => Promise<Website>;
}

const WebsiteContext = createContext<WebsiteContextType>({
  website: {} as Website,
  isLoading: false,

  updateError: '',
  isUpdating: false,
  update: () => ({}) as Promise<Website>,
});

const useWebsite = () => {
  const context = useContext(WebsiteContext);

  if (context === undefined) {
    throw new Error('useWebsite must be used within a WebsiteProvider');
  }

  return context;
};

export default useWebsite;
export { WebsiteContext };
