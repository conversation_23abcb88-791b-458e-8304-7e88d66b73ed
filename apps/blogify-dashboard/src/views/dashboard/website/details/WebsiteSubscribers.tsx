import type { APIResponse, WebsiteSubscriber } from '@ps/types';

import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { MdDelete } from 'react-icons/md';
import { useState } from 'react';
import dayjs from 'dayjs';

import {
  TableHeader,
  TableHead,
  TableBody,
  TableCell,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import { parseQuery } from '@/utils';
import { cacheGet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import emptyAPIResponse from '@/types/resources';
import Pagination from '@ps/ui/components/pagination';
import config from '@/constants/config';

import useWebsite from './context';

export default function WebsiteSubscribers() {
  const { limit = '10', page = '1' } = parseQuery();
  const [toDelete, setToDelete] = useState('');
  const [open, setOpen] = useState(false);
  const { website } = useWebsite();
  const navigate = useNavigate();

  const {
    data: { data: subscribers, total } = emptyAPIResponse,
    isFetching,
    refetch,
  } = useQuery<APIResponse<WebsiteSubscriber>>([
    `websites/${website._id}/subscribers?page=${page}&limit=${limit}`,
  ]);

  const { mutateAsync: remove, isLoading: deleting } = useMutation({
    mutationFn: () =>
      API.remove(`websites/${website._id}/subscribers/${toDelete}`).then(() => {
        refetch();
        setOpen(false);
      }),
  });

  return (
    <div className="py-6">
      <header className="mb-6 flex items-end justify-between">
        <div>
          <h2 className="text-lg font-semibold">Subscriber List</h2>
          <p className="mt-1.5 text-15">List of the subscribers, subscribed for this blog.</p>
        </div>

        <a
          href={`${config.apiUrl}websites/${website._id}/subscribers/export?token=${cacheGet('token')}`}
          download
        >
          <Button variant="secondary">Export as CSV</Button>
        </a>
      </header>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Joined</TableHead>
            <TableHead className="text-right">Action</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {!subscribers.length && (
            <TableRow>
              <TableCell colSpan={4} className="text-center">
                {isFetching ? 'Loading...' : 'No subscribers yet'}
              </TableCell>
            </TableRow>
          )}
          {subscribers.map((subscriber) => (
            <TableRow key={subscriber._id}>
              <TableCell className="font-medium">{subscriber.name}</TableCell>
              <TableCell>{subscriber.email}</TableCell>
              <TableCell>{dayjs(subscriber.createdAt).format('YYYY-MM-DD HH:mm:ss')}</TableCell>
              <TableCell className="flex items-center justify-end">
                <Button
                  variant="icon"
                  className="p-0"
                  onClick={() => {
                    setToDelete(subscriber._id);
                    setOpen(true);
                  }}
                >
                  <MdDelete size={20} className="text-gray9" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {!!subscribers.length && (
        <Pagination
          className="mt-6"
          onPaging={(url) => navigate(url)}
          limit={parseInt(limit, 10)}
          page={parseInt(page, 10)}
          total={total}
        />
      )}

      <ConfirmationDialog
        isOpen={open}
        confirmationTitle={'Are you sure?'}
        confirmationMessage={'Are you sure you want to remove this subscriber from your list?'}
        confirmText="Remove"
        cancelText="Cancel"
        onClose={() => {
          setOpen(false);
          setToDelete('');
        }}
        confirming={deleting}
        onConfirm={() => remove()}
      />
    </div>
  );
}
