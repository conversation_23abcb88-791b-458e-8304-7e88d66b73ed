import { useLocalStorage, useEffectOnce } from 'react-use';
import { ScrollRestoration, Outlet } from 'react-router-dom';

import usePageTracking from '@/hooks/usePageTracking';
import Announcement from '@/components/misc/Announcement';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import * as GoogleAnalytics from '@/services/analytics/google-analytics';

const Root = () => {
  const [siteBrowsed, setSiteBrowsed] = useLocalStorage('ga-site-browse');

  usePageTracking();

  useEffectOnce(() => {
    const timeout = setTimeout(() => {
      if (!siteBrowsed) {
        GoogleAnalytics.trackSiteBrowse(10);
        setSiteBrowsed(true);
      }
    }, 10 * 1000);

    return () => clearTimeout(timeout);
  });

  return (
    <div className="bg-dark font-space">
      <Announcement />
      <Header />
      <ScrollRestoration />
      <Outlet />
      <Footer />
    </div>
  );
};

export default Root;
