// بسم الله الرحمن الرحيم
import { API } from '@/services/api';
import { ReactNode, useEffect, useState } from 'react';
import { BiLoaderCircle } from 'react-icons/bi';

export default function Recaptcha({ action, children }: { action: string; children: ReactNode }) {
  const [isHuman, setIsHuman] = useState<boolean>();
  const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
  useEffect(() => {
    //@ts-ignore Originating from recaptcha script
    grecaptcha.enterprise.ready(() => {
      //@ts-ignore Originating from recaptcha script
      grecaptcha.enterprise.execute(siteKey, { action }).then((token: string) => {
        API.fetch<{ score: number }>(`auth/recaptcha?token=${token}&action=${action}`)
          .then((response) => {
            if (response) setIsHuman(response.score > 0.5);
            else throw new Error('Failed to get reCAPTCHA score');
          })
          .finally(() => setIsHuman(true));
      });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action]);

  return (
    <>
      {undefined === isHuman ? (
        <span className="flex items-center justify-center gap-2">
          <BiLoaderCircle className="size-20 animate-spin text-primary" />
          <h1 className="text-white">Verifying you're a human...</h1>
        </span>
      ) : false === isHuman ? (
        <div>
          <h1 className="text-primary">You've been identified as a bot! </h1>
          <h2 className="text-white">
            You can message via the Chat us if you think this is a mistake.
          </h2>
        </div>
      ) : (
        children
      )}
    </>
  );
}
