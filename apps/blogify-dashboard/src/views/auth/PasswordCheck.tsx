import { FaCheck, FaTimes } from 'react-icons/fa';

const CHECKS = [
  { id: 1, match: /^.{8,256}$/, text: 'min 8 characters' },
  { id: 2, match: /.*[A-Z].*/, text: 'At least 1 capital letter' },
  { id: 3, match: /.*[a-z].*/, text: '1 lowercase letter' },
  { id: 4, match: /.*\d.*/, text: '1 number' },
];

const PasswordCheck = ({ password }: { password: string }) => (
  <div className="flex flex-wrap items-center justify-start gap-1 text-black5">
    {CHECKS.map((check) => (
      <div className="flex" key={check.id}>
        {check.match.test(password) ? (
          <FaCheck className="text-green" />
        ) : (
          <FaTimes className="text-red4" />
        )}
        <div className="pl-2 text-xs text-gray1">{check.text}</div>
      </div>
    ))}
  </div>
);

export default PasswordCheck;
