// بسم الله الرحمن الرحيم
import { MdOutlineCancel } from 'react-icons/md';
import { useQuery } from 'react-query';
import { FaCheck } from 'react-icons/fa';
import { Link } from 'react-router-dom';

import { cacheGet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import usePageTracking from '@/hooks/usePageTracking';
import UserMenu from '@/views/dashboard/layout/header/components/UserMenu';

type AppInfo = {
  appName: string;
  appDescription: string;
  documentationURL: string;
  logoURL: string;
  privacyPolicyURL: string;
  companyName: string;
  companyWebsite: string;
};

const OAuth2ConsentCard = () => {
  const params = new URLSearchParams(window.location.search);
  const requestedPermissions: string[] =
    decodeURIComponent(params.get('scope') ?? '')?.split(' ') ?? [];

  usePageTracking();

  const { data: permissionExplanations } = useQuery<Record<string, string>>(
    'oauth2/screen/permissions'
  );
  const { data: appInfo } = useQuery<AppInfo>(
    `oauth2/screen/appData?client_id=${params.get('client_id')}`
  );

  const appendSearchParams = (url: URL) => {
    const allSearchParams = new URLSearchParams(window.location.search);
    allSearchParams.append('token', cacheGet('token'));
    url.search = allSearchParams.toString();
    return url;
  };

  return !Boolean(appInfo) ? (
    <img
      className="flex h-full animate-pulse items-center justify-center"
      src="favicon.ico"
      alt="Loading..."
    />
  ) : (
    <div className="mb-40 mt-9 flex w-full max-w-screen-md flex-wrap rounded-lg border border-primary bg-white">
      <section className="flex w-full flex-col items-center justify-start gap-5 border-r border-primary px-4 py-7 sm:w-1/3">
        <div className="flex size-20 w-full items-center justify-center gap-5 flex-center">
          <img
            className="size-20 rounded-full bg-white"
            src={appInfo?.logoURL}
            alt={appInfo?.appName + ' Logo'}
          />
          <div className="flex flex-col">
            <h2 className="text-md font-semibold">{appInfo?.appName}</h2>
            <h4 className="text-sm font-medium text-gray9">
              by
              <a
                className="mt-3 text-sm text-primary"
                href={appInfo?.companyWebsite}
                target="_blank"
              >
                {' ' + appInfo?.companyName}
              </a>
            </h4>
          </div>
        </div>
        <p className="mt-3 text-justify text-sm">{appInfo?.appDescription}</p>
      </section>

      <section className="w-full rounded-lg bg-white p-4 sm:w-2/3 sm:p-10">
        <h1 className="text-2xl font-semibold">
          Allow
          <a className="text-primary" href={appInfo?.documentationURL ?? ''} target="_blank">
            {' ' + appInfo?.appName + ' '}
          </a>
          access to your Blogify account?
        </h1>
        <h3 className="mt-8 text-md font-semibold">
          This application is requesting permission to:
        </h3>

        <ul className="ml-4 mt-2 list-disc text-sm leading-6">
          {requestedPermissions.sort().map((scope: string) => (
            <li key={scope}>{permissionExplanations?.[scope] ?? scope}</li>
          ))}
        </ul>

        <div className="mt-8 flex gap-4">
          <Button
            variant="secondary"
            className="w-full"
            onClick={() => {
              window.location.assign(
                appendSearchParams(new URL('/oauth2/v1/cancel', import.meta.env.VITE_API_URL))
              );
            }}
          >
            <MdOutlineCancel size="20" className="text-primary" />
            Cancel
          </Button>
          <Button
            className="w-full"
            onClick={() => {
              window.location.assign(
                appendSearchParams(new URL('/oauth2/v1/authorize', import.meta.env.VITE_API_URL))
              );
            }}
          >
            <FaCheck />
            Allow Access
          </Button>
        </div>

        <p className="mt-14 text-xs font-bold">
          View
          <a href={appInfo?.privacyPolicyURL} target="_blank" className="text-primary">
            {' '}
            Privacy Policy{' '}
          </a>
          of {' ' + appInfo?.appName + '.'}
        </p>
      </section>
    </div>
  );
};

const OAuth2Consent = () => (
  <div className="min-h-screen w-full bg-white font-inter text-black4">
    <header className="container flex h-14 items-center justify-between px-6">
      <Link className="flex items-center gap-2" to="/">
        <img className="size-6" src="/images/blogify.svg" />
        <span className="text-xl font-bold">Blogify</span>
      </Link>

      <UserMenu />
    </header>

    <main className="flex justify-center px-6">
      <OAuth2ConsentCard />
    </main>
  </div>
);

export default OAuth2Consent;
