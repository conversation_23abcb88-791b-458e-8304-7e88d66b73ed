import type { StripePaymentElementChangeEvent } from '@stripe/stripe-js';
import type { SubscriptionType } from '@/types/misc/subscription.type';
import type { FormikValues } from 'formik';
import type { Package } from '@/types/misc/package.type';

import { PaymentElement, useElements, useStripe, Elements } from '@stripe/react-stripe-js';
import { TabsTrigger, TabsList, Tabs } from '@ps/ui/components/tabs';
import { useEffect, useState } from 'react';
import { BiSolidCoupon } from 'react-icons/bi';
import { useNavigate } from 'react-router-dom';
import { loadStripe } from '@stripe/stripe-js';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import {
  getPriceAfterDiscount,
  getDiscountsForCoupon,
  dealCodesPrefixList,
  getPackageName,
  parseQuery,
} from '@/utils';
import {
  stripePaymentElementOptions,
  getStripeReturnRedirect,
  stripeElementsOptions,
} from '@/utils/stripe';
import { trackPaymentClick, trackSubscription } from '@/services/analytics/google-analytics';
import { useStoreActions, useStoreState } from '@/store';
import { RadioGroupItem, RadioGroup } from '@ps/ui/components/radio-group';
import { cacheRemove, cacheGet } from '@/utils/localStorageCache';
import { normalizeNumber } from '@/utils/number';
import { Button } from '@ps/ui/components/button';
import { Input } from '@ps/ui/components/input';
import { API } from '@/services/api';
import { cn } from '@ps/ui/lib/utils';
import ErrorMessage from '@/components/form/ErrorMessage';
import useForm from '@/hooks/useForm';
import config from '@/constants/config';
import Seo from '@/components/misc/Seo';

type CreateSubscriptionResponse = {
  clientSecret: string;
  subscriptionRef: string;
};

const PaymentSchema = Yup.object().shape({
  plan: Yup.string().required(`Package is required`),
  period: Yup.string().required(`Subscription period is required`),
  coupon: Yup.string(),
});

const stripePromise = loadStripe(config.stripeKey);

const plans: { name: SubscriptionType; suitableFor: string }[] = [
  { name: 'lite', suitableFor: 'For Bloggers' },
  { name: 'basic', suitableFor: 'For Agencies' },
  { name: 'premium', suitableFor: 'For Small Businesses' },
  { name: 'business', suitableFor: 'For Large Businesses' },
  { name: 'enterprise', suitableFor: 'For Enterprises' },
  { name: 'unlimited', suitableFor: 'For Limitless Potential' },
];

const PaymentInfo = () => {
  const { SUBSCRIPTION_TRIAL_PERIOD = 0, FREE_PLAN_ENABLED = false } = useStoreState(
    (s) => s.context.settings
  );
  const { packageByName } = useStoreState((s) => s.context);

  const [selectedPackage, setSelectedPackage] = useState<Package>(packageByName.LIFETIME_BASIC);
  const [discountedPrice, setDiscountedPrice] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stripeEvent, setStripeEvent] = useState<StripePaymentElementChangeEvent | null>(null);
  const [stripeError, setStripeError] = useState('');
  const [formError, setFormError] = useState('');
  const [discount, applyDiscount] = useState(0);
  const navigate = useNavigate();
  const elements = useElements();
  const stripe = useStripe();

  if (FREE_PLAN_ENABLED && plans[0].name !== 'free') {
    plans.unshift({ name: 'free', suitableFor: 'For Individual' });
  }

  const fetchUser = useStoreActions((a) => a.user.fetch);

  const submit = async (values: FormikValues) => {
    trackPaymentClick('stripe_form');
    if (!elements || !stripe) return;

    const _coupon = values.coupon.trim();
    const matchedDealCouponPrefix =
      _coupon && dealCodesPrefixList.find((prefix) => _coupon.startsWith(prefix));

    if (!matchedDealCouponPrefix && !stripeEvent?.complete && selectedPackage.price !== 0) {
      return setStripeError(`Your card number is incomplete.`);
    }

    toast.loading('Subscribing to the plan ...', { duration: 3000 });
    setIsSubmitting(true);
    setStripeError('');
    setFormError('');

    const resp = await API.post<CreateSubscriptionResponse>('subscriptions', {
      priceId: selectedPackage.id,
      coupon: _coupon,
      period,
      plan,
    }).catch((e) => {
      setFormError(e?.error || e?.message || e);
      toast.error(e?.error || e?.message || e);
      setIsSubmitting(false);
    });

    try {
      if (resp?.clientSecret && selectedPackage.price !== 0 && !matchedDealCouponPrefix) {
        const trialPeriod = SUBSCRIPTION_TRIAL_PERIOD;
        const isTrial = !!trialPeriod && period !== 'lifetime' && plan !== 'free';

        elements.submit();
        const result = await stripe[isTrial ? 'confirmSetup' : 'confirmPayment']({
          elements,
          clientSecret: resp?.clientSecret,
          confirmParams: {
            return_url: getStripeReturnRedirect(resp?.subscriptionRef, {
              packageName: selectedPackage.name,
              coupon: _coupon,
            }),
          },
        });

        if (result.error?.message) {
          const paymentRef = result.error[isTrial ? 'setup_intent' : 'payment_intent']?.id;
          if (paymentRef && resp?.subscriptionRef) {
            await API.patch(`products/addons/purchase/cancel`, {
              paymentRef,
              subscriptionRef: resp?.subscriptionRef,
            });
          }
          throw new Error(result.error.message);
        }
      } else if (resp?.clientSecret && plan === 'free') {
        const intent = await stripe.retrieveSetupIntent(resp?.clientSecret);
        const setupIntent = intent.setupIntent?.id;
        window.location.href = `${getStripeReturnRedirect(resp?.subscriptionRef, {
          packageName: selectedPackage.name,
          coupon: _coupon,
        })}&setup_intent=${setupIntent}&setup_intent_client_secret=FREE&redirect_status=succeeded`;
      }
      await fetchUser();
      cacheRemove('selectedPeriod');
      cacheRemove('selectedPlan');
      trackSubscription(selectedPackage.price, selectedPackage.name);
      setIsSubmitting(false);
      toast.success('Successfully subscribed to the plan. Redirecting to dashboard...');
      return navigate('/dashboard');
    } catch (e: any) {
      elements.getElement('payment')?.clear();
      setStripeError(e?.error || e?.message || e);
      toast.error(e?.error || e?.message || e);
      setIsSubmitting(false);
    }
  };

  const query = parseQuery();

  const queryPlan = ['mini', 'starter'].includes(query.plan) ? 'lite' : query.plan;
  const initialValues = {
    plan: cacheGet('selectedPlan') || queryPlan || 'basic',
    period: cacheGet('selectedPeriod') || query.period || 'yearly',
    coupon: query.coupon || '',
  };
  const form = useForm<typeof initialValues>({
    validationSchema: PaymentSchema,
    initialValues,
    submit,
  });
  const { plan, period, coupon } = form.values;

  useEffect(() => {
    if (packageByName.MONTHLY_BASIC?.id) {
      const _plan = ['mini', 'starter'].includes(plan) && period !== 'lifetime' ? 'lite' : plan;
      form.setFieldValue('plan', _plan);
      const packageName = getPackageName(_plan, period);
      setSelectedPackage(packageByName[packageName]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [packageByName]);

  useEffect(() => {
    if (plan && period && elements) {
      if (plan === 'lite' && period === 'monthly') {
        form.setFieldValue('coupon', '');
      }
      const _plan = ['mini', 'starter'].includes(plan) && period !== 'lifetime' ? 'lite' : plan;
      const packageName = getPackageName(_plan, period);
      const _selectedPackage = packageByName[packageName] || packageByName.MONTHLY_BASIC;
      setSelectedPackage(_selectedPackage);
      elements.update({ mode: 'subscription', currency: 'usd', amount: _selectedPackage.price });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plan, period, elements]);

  useEffect(() => {
    async function fetchDiscount() {
      if (!coupon) {
        setFormError('');
        return applyDiscount(0);
      }
      const { discount: discountPercentage, valid } = await getDiscountsForCoupon(coupon);
      if (!valid) {
        return setFormError('Invalid coupon code');
      }
      if (coupon.startsWith('BLOGIFY_APPSUMO') && period !== 'lifetime') {
        return setFormError('This coupon is only valid for lifetime plans.');
      }
      setFormError('');
      applyDiscount(discountPercentage);
    }

    fetchDiscount();
  }, [coupon, discount, period]);

  useEffect(() => {
    setStripeError('');
    // Only fetch the discounted price if there's a discount
    if (discount > 0) {
      const fetchDiscountedPrice = async () => {
        const priceAfterDiscount = await getPriceAfterDiscount(selectedPackage.price, coupon);
        setDiscountedPrice(priceAfterDiscount);
      };

      fetchDiscountedPrice();
    }
  }, [selectedPackage, coupon, discount]);

  useEffect(() => {
    if (form) {
      form.setFieldValue('plan', period === 'lifetime' ? 'lite' : 'basic');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [period]);

  return (
    <form
      className="w-full max-w-[480px] rounded-2xl border border-prelude/20 p-5 shadow-inner-glow backdrop-blur-xl xs:p-8"
      onSubmit={form.submitForm}
    >
      <h1 className="text-xl font-bold">Subscription</h1>
      <p className="text-md font-medium text-black5">Select a plan and start blogging...</p>
      <div className="mt-2">
        {plan !== 'free' && (
          <>
            {!!SUBSCRIPTION_TRIAL_PERIOD && (
              <p className="text-sm font-semibold text-black5">
                Your card will be charged after the {SUBSCRIPTION_TRIAL_PERIOD}-day trial.
              </p>
            )}
            <p className="text-sm font-semibold text-black5">Subscribe now, cancel anytime.</p>
          </>
        )}
      </div>

      {/* Stripe resets the first input for some reason, this prevents required fields from getting reset */}
      <input hidden />

      <Tabs
        onValueChange={(value) => form.setFieldValue('period', value)}
        defaultValue={period || 'yearly'}
        className="mt-8"
      >
        <TabsList variant="dark">
          <TabsTrigger value="monthly" variant="dark">
            Monthly
          </TabsTrigger>

          <TabsTrigger
            className="px-1.5 data-[state=active]:shadow-none [&>div>div]:data-[state=active]:bg-white [&>div>div]:data-[state=active]:text-primary [&>div>div]:data-[state=active]:shadow-none"
            variant="dark"
            value="yearly"
          >
            <div className="flex gap-1 flex-center">
              Annually
              <div className="whitespace-nowrap rounded bg-primary px-1 py-0.5 text-xs shadow-button-primary lg:text-sm">
                20% Off
              </div>
            </div>
          </TabsTrigger>

          <TabsTrigger value="lifetime" variant="dark">
            Lifetime
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <RadioGroup
        onValueChange={(value) => form.setFieldValue('plan', value)}
        className="mt-4 rounded-lg border border-prelude/20"
        aria-label="Select Plan"
        value={plan}
      >
        {plans.map((p, i) => (
          <label
            className={cn(
              'flex w-full items-center justify-between border-prelude/20 px-4 py-2.5',
              { 'border-t': i !== 0 }
            )}
            key={p.name}
          >
            <div className="flex items-center gap-4">
              <RadioGroupItem value={p.name} />

              <div className="text-left">
                <div className="text-md font-medium capitalize">{p.name}</div>
                <div className="mt-0.5 text-sm font-medium text-black5">{p.suitableFor}</div>
              </div>
            </div>

            <div className="text-right">
              <div className="text-md font-medium">
                {/* <span className="text-black5 line-through">
                  ${packageByName[getPackageName(p.name, period)]?.price / 100}
                </span> */}
                <span>
                  $
                  {normalizeNumber(packageByName[getPackageName(p.name, period)]?.price / 100, 2) ||
                    0}
                </span>
              </div>
              <div className="mt-0.5 text-sm font-medium text-black5">
                {period === 'lifetime' ? 'for ' : '/'}
                {period.replace('ly', '')}
              </div>
            </div>
          </label>
        ))}
      </RadioGroup>

      {selectedPackage?.price !== 0 && (
        <div className="mt-8 rounded-lg border border-prelude/20 p-4 shadow-inner-glow backdrop-blur-xl">
          <label className="flex items-center gap-2">
            <BiSolidCoupon className="text-primary" size={20} />
            <span className="text-sm font-medium">Coupon</span>
          </label>
          <p className="mt-2 text-md text-black5">If you have any coupon code insert it below.</p>
          <Input
            variant="dark"
            className="mt-4"
            name={form.getInputFields('coupon').name}
            value={form.getInputFields('coupon').value}
            onChange={form.getInputFields('coupon').onChange}
          />
          <span className="text-sm text-black5">{discount > 0 ? 'Coupon Applied' : ''}</span>
        </div>
      )}

      {plan !== 'free' && (
        <>
          <div
            className={cn(`mt-7 rounded-lg border pb-px`, {
              'border-red': stripeError,
              'border-prelude/20': !stripeError,
            })}
          >
            <PaymentElement onChange={setStripeEvent} options={stripePaymentElementOptions} />
          </div>

          {stripeError && <ErrorMessage className="mt-2">{stripeError}</ErrorMessage>}
        </>
      )}

      <div className="mt-8">
        <p className="text-sm font-bold text-white">
          {SUBSCRIPTION_TRIAL_PERIOD}-day trial, cancel anytime.
        </p>

        {formError && <ErrorMessage>{formError}</ErrorMessage>}

        <Button
          className="mt-4 h-11 w-full shadow-glow"
          loading={isSubmitting}
          disabled={!!formError}
          type="submit"
        >
          Subscribe for -{' '}
          <span className={cn({ 'line-through': discount > 0 })}>
            ${selectedPackage?.price / 100}
          </span>
          {discount > 0 && <span> ${discountedPrice}</span>}
        </Button>
      </div>
    </form>
  );
};

const PaymentInfoContainer = () => (
  <>
    <Seo
      title="Payment"
      description="Quickly transcribe video to text & convert it into high quality blog & monetize it. You can also turn your podcasts to text using our  ."
      keywords="transcribe video to text, transcribe audio to text, how to transcribe audio to text,  "
    />
    <Elements stripe={stripePromise} options={stripeElementsOptions}>
      <div className="relative isolate flex flex-col px-4 pb-28 pt-14 font-space text-white flex-center">
        <img
          className="absolute inset-x-0 -top-16 -z-10 w-full"
          src="/images/backgrounds/main-bg.svg"
          alt="hero bg effect"
        />
        <PaymentInfo />
      </div>
    </Elements>
  </>
);

export default PaymentInfoContainer;
