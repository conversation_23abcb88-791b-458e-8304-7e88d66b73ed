import type { FormikValues } from 'formik';

import { useEffect, useState } from 'react';
import { FaGoogle } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { useStoreActions, useStoreState } from '@/store';
import { getSocialAuthUrlWithRedirect } from '@/utils/auth';
import { checkPassword } from '@/utils/validations';
import { trackSignup } from '@/services/analytics/google-analytics';
import { cacheSet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import ErrorMessage from '@/components/form/ErrorMessage';
import FormField from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';
import useAuth from '@/hooks/useAuth';
import Select from '@ps/ui/form/Select';

import PasswordCheck from '../PasswordCheck';

const initialValues = {
  name: '',
  email: '',
  password: '',
  businessName: '',
  foundUsFrom: '',
  foundUsFromType: '',
};

const SignUp = () => {
  const [showPasswordCheck, setShowPasswordCheck] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState('');
  const [errorShown, setErrorShown] = useState(false);
  const { redirectAfterAuth } = useAuth();

  const isAuthenticated = useStoreState((a) => a.user.isAuthenticated);
  const onAuthorize = useStoreActions((actions) => actions.user.onAuthorize);
  const signUp = useStoreActions((a) => a.user.signUp);

  const queryString = window.location.search;
  const urlParams = new URLSearchParams(queryString);
  const errorMessage = urlParams.get('error');
  const token = urlParams.get('token');
  const selectedPeriod = urlParams.get('period');
  const selectedPlan = urlParams.get('plan');

  if (selectedPeriod && selectedPlan) {
    cacheSet('selectedPeriod', selectedPeriod);
    cacheSet('selectedPlan', selectedPlan);
  }

  useEffect(() => {
    const bodyClass = document.querySelector('.grecaptcha-badge')?.classList;
    bodyClass?.add('visible');
    return () => bodyClass?.remove('visible');
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      redirectAfterAuth();
    }

    // Check if there's a token in the query string
    if (token) {
      onAuthorize({ token });
    }

    // Check if there's an error message in the query string
    if (errorMessage && !errorShown) {
      setErrorShown(true);
      toast.error(errorMessage);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, errorShown]);

  const submit = ({ foundUsFromType, ...values }: FormikValues) => {
    setIsSubmitting(true);
    if (foundUsFromType !== 'Other') {
      values.foundUsFrom = foundUsFromType;
    }
    signUp(values as typeof initialValues)
      .then(() => {
        setIsSubmitting(false);
        trackSignup();
      })
      .catch((e: string) => {
        setIsSubmitting(false);
        setFormError(e);
      });
  };

  const SignUpSchema = Yup.object().shape({
    name: Yup.string().required(`Name is required`),
    email: Yup.string().required(`Email is required`).email(`Invalid email`),
    password: Yup.string()
      .required('Password is required')
      .test({
        name: 'passwordStrengthCheck',
        message: 'Password is too weak',
        test: (value: string) => !checkPassword(value, {}).password,
      }),
    businessName: Yup.string().required(`Business name is required`),
    foundUsFromType: Yup.string().required('We would love to know where did you find us'),
    foundUsFrom: Yup.lazy(() => {
      if (form.getInputFields('foundUsFromType').value === 'Other') {
        return Yup.string().required(`We would love to know where did you find us`);
      }
      return Yup.string();
    }),
  });

  useEffect(() => {
    if (isAuthenticated) {
      redirectAfterAuth(`/payment/${window.location.search}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  const form = useForm({ initialValues, submit, validationSchema: SignUpSchema });

  const password = form.getInputFields('password').value as string;

  return (
    <>
      <div className="mx-auto flex w-full flex-col items-start justify-between gap-6 rounded-2xl border border-secondary/15 p-8 text-white shadow-button-secondary lg:w-[844px] lg:flex-row">
        <div className="w-full flex-1">
          <div className="mb-8">
            <h1 className="mb-[8px] text-xl font-bold text-white">Welcome to Blogify!</h1>
            <h3 className="text-base font-medium text-black5">
              Sign up to create your Blogify account.
            </h3>
          </div>

          {/* google login button  */}
          <a href={getSocialAuthUrlWithRedirect('google')}>
            <div className="mx-auto flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-secondary/25 bg-button-secondary px-[14px] py-[8px] text-base font-medium text-white shadow-button-secondary hover:bg-secondary/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-secondary/15">
              <span>
                <FaGoogle size={15} />
              </span>
              <span>Signup with Google</span>
            </div>
          </a>
          <form onSubmit={form.submitForm}>
            <div className="relative my-3 before:absolute before:left-0 before:top-3 before:h-px before:w-[45%] before:bg-[#C5B8E040] after:absolute after:right-0 after:top-3 after:h-px after:w-[45%] after:bg-[#C5B8E040]">
              <div className="relative z-10 text-center text-[14px] text-black5">or</div>
            </div>

            <ErrorMessage className="pb-1">{formError || form.formValidationError}</ErrorMessage>
            {/* @ts-ignore For Now */}
            <FormField
              label="Name"
              variant="dark"
              {...form.getInputFields('name')}
              placeholder="ie: John Doe"
            />

            {/* @ts-ignore For Now */}
            <FormField
              type="email"
              label="Email"
              variant="dark"
              {...form.getInputFields('email')}
              placeholder="ie: <EMAIL>"
            />

            {/* @ts-ignore For Now */}
            <FormField
              type="password"
              label="Password"
              variant="dark"
              {...form.getInputFields('password')}
              placeholder="********"
              onFocus={() => setShowPasswordCheck(true)}
            />

            {showPasswordCheck && <PasswordCheck password={password} />}

            {/* for agency */}
            {/* @ts-ignore For Now */}
            <FormField
              variant="dark"
              {...form.getInputFields('businessName')}
              label="Business Name"
              placeholder="ie: Awesome Ltd."
            />

            {/* <div className="relative my-3 before:absolute before:left-0 before:top-3 before:h-[1px] before:w-[40%] before:bg-[#C5B8E040] after:absolute after:right-0 after:top-3 after:h-[1px] after:w-[40%] after:bg-[#C5B8E040]">
              <div className="relative z-10 px-4 text-center text-[14px] text-black5">Optional</div>
            </div> */}

            {/* find place */}
            <div className="">
              <div className="mb-[10px] text-[14px] font-medium text-white">
                Where did you find us?
              </div>
              {/* @ts-ignore For Now */}
              <Select {...form.getInputFields('foundUsFromType')} variant="dark">
                <option value="">Select</option>
                {['Google', 'Facebook', 'Twitter', 'LinkedIn', 'YouTube', 'Other'].map((from) => (
                  <option key={from} value={from}>
                    {from}
                  </option>
                ))}
              </Select>
              {form.getInputFields('foundUsFromType').value === 'Other' && (
                // @ts-ignore For Now
                <FormField
                  variant="dark"
                  {...form.getInputFields('foundUsFrom')}
                  label=" "
                  placeholder="Where did you find us"
                ></FormField>
              )}
              {form.getInputFields('foundUsFromType').touched &&
                !form.getInputFields('foundUsFromType').value && (
                  <span className="text-sm text-red4">Please select one</span>
                )}
            </div>

            {/*remember checkbox  */}
            {/*
              <div className="py-[17px] flex flex-row gap-2 items-center justify-start">
                <Input
                  id="remember"
                  type="checkbox"
                  value=""
                  className="appearance-none shadow-button-secondary relative peer rounded shrink-0 w-5 h-5 border-2 border-blue-500 bg-dark checked:bg-primary checked:border-0"
                  onChange={(e) => console.log('checkbox', e)}
                />
                <label htmlFor="remember" className="text-[14px] text-black5 font-medium">
                  Remember this device
                </label>
                <svg
                  className="absolute w-5 h-5  hidden peer-checked:block"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="4"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div> */}

            {/* submit button  */}
            <div className="my-[28px]">
              <Button variant="glow" type="submit" className="w-full" loading={isSubmitting}>
                Sign up
              </Button>

              <p className="mt-3 text-center text-xs font-medium text-black5">
                By clicking Sign Up, you agree to accept Blogify's
                <a href="/terms/" className="ml-2 font-medium text-primary hover:underline">
                  Terms & Conditions
                </a>
              </p>
            </div>
          </form>
          <div className="flex flex-col items-center justify-center">
            <p className="mb-[6px] text-[14px] font-medium text-white">Already have an account?</p>
            <Link
              className="text-[14px] font-medium text-primary hover:underline"
              to={`/login/${window.location.search || ''}`}
            >
              Log in
            </Link>
          </div>
        </div>
        {/* blogify video  */}
        <div className="w-full shrink-0 lg:w-2/5">
          <div className="w-full rounded-lg p-2 shadow-button-secondary">
            <iframe
              className="min-h-[200px]"
              src="https://www.youtube.com/embed/Nzzu0rx0M1Y"
              title="Demo Video"
              allowFullScreen
              width="100%"
            ></iframe>
          </div>
          <div className="mt-5 font-inter text-sm font-normal text-black5">
            <p>Blogify is your ultimate AI powered productivity tool for Blogging</p>
            <br />
            <p>
              Discover the ✨ magic ✨ of AI with our platform, turning YouTube videos into blogs
              with a snap. Experience the revolution of blogging in our engaging demo video, filled
              with amazing features. Step into the future of blogging 🚀
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SignUp;
