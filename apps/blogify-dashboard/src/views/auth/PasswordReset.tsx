import type { FormikValues } from 'formik';

import { useNavigate, useParams } from 'react-router-dom';
import { useState } from 'react';
import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import ErrorMessage from '@/components/form/ErrorMessage';
import useForm from '@/hooks/useForm';

import { AuthContainer } from './Auth';
import Auth from './Auth';
import FormField from '@ps/ui/form/FormField';

const PasswordResetSchema = Yup.object().shape({
  password: Yup.string().required(`Password is required`),
  passwordRepeat: Yup.string().required(`Password repeat is required`),
  token: Yup.string().required(`Token is required`),
});

const PasswordReset = () => {
  const { token } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState('');
  const navigate = useNavigate();
  const isSetup = window.location.pathname.startsWith('/accept-invitation');

  const initialValues = {
    password: '',
    passwordRepeat: '',
    token,
  };

  const submit = (values: FormikValues) => {
    if (values.password !== values.passwordRepeat) {
      return setFormError(`Password and password repeat doesn't match.`);
    }
    setIsSubmitting(true);
    API.post<{ success: boolean }>(
      `auth/${isSetup ? 'accept-invitation' : 'password-reset'}`,
      values
    )
      .then((resp) => {
        setIsSubmitting(false);
        if (resp?.success) {
          toast.success(
            `${
              isSetup ? 'Password setup complete.' : 'Password reset successful.'
            } Please login using the new password.`
          );
        }
        return navigate('/login');
      })
      .catch((e: string) => {
        setIsSubmitting(false);
        return setFormError(e);
      });
  };

  const form = useForm({ initialValues, submit, validationSchema: PasswordResetSchema });

  return (
    <Auth>
      <AuthContainer title={isSetup ? 'Setup Password' : 'Reset Password'}>
        <form onSubmit={form.submitForm}>
          {/* @ts-ignore For Now */}
          <FormField
            variant="dark"
            type="password"
            label="New Password"
            {...form.getInputFields('password')}
          />
          {/* @ts-ignore For Now */}
          <FormField
            variant="dark"
            type="password"
            label="Repeat New Password"
            placeholder="Repeat Password"
            {...form.getInputFields('passwordRepeat')}
          />

          <ErrorMessage className="pb-2">{formError || form.formValidationError}</ErrorMessage>

          <Button variant="glow" className="w-full" type="submit" loading={isSubmitting}>
            Save
          </Button>
        </form>
      </AuthContainer>
    </Auth>
  );
};

export default PasswordReset;
