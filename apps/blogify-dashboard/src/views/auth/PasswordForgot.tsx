import type { FormikValues } from 'formik';

import { useState } from 'react';

import toast from 'react-hot-toast';
import * as Yup from 'yup';

import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import ErrorMessage from '@/components/form/ErrorMessage';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import useForm from '@/hooks/useForm';
import Seo from '@/components/misc/Seo';

import Auth from './Auth';

const initialValues = {
  email: '',
};

const PasswordForgotSchema = Yup.object().shape({
  email: Yup.string().required(`Email is required`).email(`Invalid email`),
});

const PasswordForgot = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState('');

  const submit = (values: FormikValues) => {
    setIsSubmitting(true);
    API.post<{ success: boolean }>('auth/password-forgot', values)
      .then((resp) => {
        setIsSubmitting(false);
        if (resp?.success) {
          toast.success('Password reset link sent. Please check your email.');
        }
      })
      .catch((e: string) => {
        setIsSubmitting(false);
        return setFormError(e);
      });
  };

  const form = useForm({ initialValues, submit, validationSchema: PasswordForgotSchema });

  return (
    <>
      <Seo
        title="Turn anything into a blog with the AI"
        description="Quickly transcribe video to text & convert it into high quality blog & monetize it. You can also turn your podcasts to text using our."
        keywords="transcribe video to text, transcribe audio to text, how to transcribe audio to text,  "
      />
      <Auth>
        <div className="mx-auto w-[320px] rounded-2xl border border-secondary/15 p-8 shadow-button-secondary md:w-[480px]">
          <div className="mb-4">
            <h1 className="mb-[8px] text-xl font-bold text-white">Forgot Password</h1>
          </div>

          <form onSubmit={form.submitForm}>
            <ErrorMessage className="pb-2">{formError || form.formValidationError}</ErrorMessage>
            {/* @ts-ignore For Now */}
            <FormField
              variant="dark"
              label="Email"
              type="email"
              {...form.getInputFields('email')}
            />

            {/* submit button  */}
            <div className="pt-8">
              <Button variant="glow" type="submit" className="w-full" loading={isSubmitting}>
                Send Password Reset Link
              </Button>
            </div>
          </form>
        </div>
      </Auth>
    </>
  );
};

export default PasswordForgot;
