import PageContainer from '@/components/layout/PageContainer';

const AuthContainer = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <PageContainer className="px-2 py-16 text-white">
    <div className="flex justify-center">
      <div className="mx-auto w-[320px] shrink-0 rounded-2xl border border-secondary/15 p-8 shadow-button-secondary md:w-[480px]">
        <div className="mb-6 text-xl font-bold text-white">{title}</div>
        <div className="flex flex-center">
          <div className="w-full p-0">{children}</div>
        </div>
      </div>
    </div>
  </PageContainer>
);

const Auth = ({ children }: { children: React.ReactNode }) => (
  <section aria-label="login to Blogify" className="relative isolate text-white">
    <img
      src="/images/backgrounds/main-bg.svg"
      alt="hero bg effect"
      className="absolute inset-x-0 -top-16 -z-10 w-full"
    />
    <div className="relative isolate z-30 py-12 lg:py-[96px]">
      <div className="container mx-auto">{children}</div>
    </div>
  </section>
);

export default Auth;
export { AuthContainer };
