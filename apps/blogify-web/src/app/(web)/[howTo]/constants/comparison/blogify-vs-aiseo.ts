import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import AISeoPost from '@img/page-assets/comparison/aiseopost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import AISeoLogo from '@img/page-assets/comparison/aiseologo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Ease of Use',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'AI Content Enhancement',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Collaboration & Integration',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Pricing Structure',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Analytics & Performance Tracking',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'green-check', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Content Publishing & Hosting',
      'AI generated cover & content image',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      description: 'Limited to text inputs; does not support multi-format content creation.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai offers an intuitive interface designed for creators of all levels. With just a few clicks, users can convert video/audio into blogs.',
    },
    competitor: {
      description:
        'AISEO.ai is user-friendly, but leans more toward experienced SEO users, making it slightly less intuitive for beginners.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: 'Does not support direct video or audio uploads.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Not applicable due to no support for media uploads.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description: 'Requires step-by-step manual input; no auto-pilot mode.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description: 'Offers suggestions but no interactive co-creation environment.',
    },
  },
  {
    blogify: {
      description:
        'Enhances blogs with AI-generated titles, summaries, images, and SEO insights. Also offers readability optimization.',
    },
    competitor: {
      description:
        'Strong SEO enhancement through keyword suggestions and optimization scoring, but lacks visual and creative add-ons.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: 'SEO tools available, but no real-time scoring.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description: 'Limited customization of word count.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Supports multiple languages with fewer settings.',
    },
  },
  {
    blogify: {
      description:
        'Integrates with WordPress, Blogger, and other CMSs; while not collaboration-focused, it supports easy sharing/exporting.',
    },
    competitor: {
      description:
        'Has CMS integrations but lacks team collaboration features and manual content sharing flexibility.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'No custom domain or DNS publishing options.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'No built-in blog scheduling feature.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'No affiliate monetization functionality.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Basic performance metrics only.',
    },
  },
  {
    blogify: {
      description:
        'Offers flexible pricing tiers based on features, with free trial access and cost-effective plans for solopreneurs and teams.',
    },
    competitor: {
      description:
        'Competitively priced for SEO-specific users, but advanced features require higher-tier plans.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description: 'Provides basic snippet creation using templates.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'No YouTube integration.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'Not available.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Limited support; slower response times.',
    },
  },
  {
    blogify: {
      description:
        'Built-in analytics include SEO scores, word counts, and blog performance metrics directly within the dashboard.',
    },
    competitor: {
      description:
        'SEO-based analytics like keyword ranks and optimization reports, but less focused on user engagement or blog-specific metrics.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts.Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Primarily supports repurposing from plain text.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Manual process with some AI suggestions.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'No source embedding feature.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Basic templates with fewer customization options.',
    },
  },
  {
    blogify: {
      description:
        'Allows publishing on custom domain or sub-domain with DNS integration, essentially functioning as a blog host.',
    },
    competitor: {
      description:
        'Supports CMS publishing but lacks direct hosting or domain-level DNS publishing capabilities.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'Limited AI image generation with fewer features.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description: 'No chart or graph generation feature.',
    },
  },
];

const BLOGIFY_VS_AISEO: ComparePageData = {
  metadata: {
    title: 'Blogify vs AISEO.ai: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and AISEO.ai features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs aiseo', 'ai writing tools comparison', 'aiseo alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'AISEO.ai Alternative',
    description:
      'Blogify.ai surpasses AISEO.ai by turning videos, audio, PDFs, and URLs into SEO-optimized blog posts automatically. While AISEO.ai focuses on traditional AI writing, Blogify offers powerful content repurposing, scheduling, monetization, and publishing features — all tailored to help creators automate their blogging workflow with ease. 🚀',
    images: {
      competitorImage: AISeoPost, // Use actual AISEO.ai image here
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs AISEO.ai',
  description:
    'Unlike AISEO.ai, Blogify.ai brings powerful automation, SEO scoring, media-to-blog conversion, and domain publishing to the table. It’s built for modern creators and marketers who want more than just text generation — they want results.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: AISeoLogo, // Use actual AISEO.ai logo here
  },
};

export default BLOGIFY_VS_AISEO;
