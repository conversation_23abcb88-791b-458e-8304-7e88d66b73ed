import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import WriteseedPost from '@img/page-assets/comparison/writeseedpost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import Writeseedlogo from '@img/page-assets/comparison/writeseedlogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Source File Size',
      'Content Rephrasing & Paraphrasing',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'Tone & Style Customization',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Blog Outline Generator',
      'Affiliate Monetization with AI',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Content Scoring & Feedback',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Web Page Summarizer',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'Cold Email Generator',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      description:
        'Writeseed focuses primarily on text-based input and does not support audio/video or external source transformation.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai offers advanced content personalization options such as tone, style, and structure adjustments, allowing users to fine-tune the generated blog content.',
    },
    competitor: {
      description:
        'Writeseed.com provides basic content personalization, but the options are more limited in comparison to Blogify.ai. Customizing the tone and style is not as flexible.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: 'Primarily handles text; no advanced file upload handling is highlighted.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Duration-based source handling not supported, as Writeseed is text-focused.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Writeseed offers AI-assisted writing, but lacks one-click full blog automation.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai includes an integrated content quality control system that helps to ensure the blog meets SEO standards, readability, and keyword optimization.',
    },
    competitor: {
      description:
        'Writeseed.com does not offer any specific content quality control features, so users need to manually review and optimize content for quality and SEO.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description: 'Manual editing is available, but no co-pilot-like guided assistance.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: 'Includes SEO optimization tools but lacks dynamic scoring features.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai offers API access for businesses and developers, enabling seamless integration with other platforms and systems to automate content generation.',
    },
    competitor: {
      description:
        'Writeseed.com does not provide API access, making it less suitable for developers or businesses looking to integrate content creation with their workflows.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description: 'Word count control exists but without smart suggestions.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Supports multiple languages but with fewer localization options.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'No native support for publishing on personal domains.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai supports a wide variety of content formats including blog posts, articles, social media posts, and more, allowing for flexibility in content creation.',
    },
    competitor: {
      description:
        'Writeseed.com mainly focuses on generating blog posts and articles, with limited options for other content formats like social media posts or product descriptions.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Does not provide scheduling or queueing features.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'No built-in monetization or affiliate marketing features.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Does not offer integrated analytics for performance tracking.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai has a built-in plagiarism checker to ensure that all generated content is unique and free from copied material.',
    },
    competitor: {
      description:
        'Writeseed.com does not provide a plagiarism checker, meaning users must manually verify the content for originality.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description: 'Snippet generation available but not as an add-on or modular feature.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Does not offer direct YouTube integration.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'No such advanced content extraction from video.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Provides support during limited hours or via email only.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai allows users to export content in various formats like text, Word, or HTML, making it easy to integrate into other platforms or publish on websites.',
    },
    competitor: {
      description:
        'Writeseed.com offers basic export options but may not support as many file types or integration methods as Blogify.ai. It typically supports text-based export like plain text or Word.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts.Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Mainly repurposes from text and limited sources.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Focuses more on manual AI-assisted blog writing.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'Embedding not available within blog editor.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Provides general templates, but less tailored options.',
    },
  },
];

const BLOGIFY_VS_WRITESEED: ComparePageData = {
  metadata: {
    title: 'Blogify vs Writeseed: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Writeseed features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs writeseed', 'ai writing tools comparison', 'writeseed alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Writeseed Alternative',
    description:
      'Blogify.ai surpasses Writeseed by turning videos, audio, PDFs, and URLs into SEO-optimized blog posts automatically. While Writeseed focuses on fast AI writing templates, Blogify offers powerful content repurposing, scheduling, monetization, and publishing features — all tailored to help creators automate their blogging workflow with ease. 🚀',
    images: {
      competitorImage: WriteseedPost, // Use actual Writeseed image here
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Writeseed',
  description:
    'Unlike Writeseed, Blogify.ai brings powerful automation, SEO scoring, media-to-blog conversion, and domain publishing to the table. It’s built for modern creators and marketers who want more than just text generation — they want results.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: Writeseedlogo, // Use actual Writeseed logo here
  },
};

export default BLOGIFY_VS_WRITESEED;
