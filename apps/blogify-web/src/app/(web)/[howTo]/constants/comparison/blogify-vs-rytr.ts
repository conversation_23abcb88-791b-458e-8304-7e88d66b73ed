import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import Rytr from '@img/page-assets/comparison/rytr.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import RytrLogo from '@img/page-assets/comparison/rytrlogo.jpg';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Onboarding Experience',
      'Custom Use Case Creation',
      'Source File Size',
      'Source File Duration',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'Content Output Variations',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Auto-Pilot Blog Creation',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Mobile Responsiveness',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Analytics',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Browser Extension',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Content Organization',
      'AI generated cover & content image',
      'AI generated graphs & charts',
      'Command Mode (Power Writing)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'red-cross'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      image: Rytr,
      description:
        'Only supports manually entered or prompted text. No support for creating blogs from media or file content.',
    },
  },
  // 2) Onboarding Experience
  {
    blogify: {
      description:
        'Blogify.ai provides a guided onboarding focused on converting audio, video, and documents into blogs. The dashboard walks users through uploading source content and customizing blog preferences.',
    },
    competitor: {
      description:
        'Rytr.ai offers a quick-start interface with minimal onboarding. Users can immediately choose a use case and start generating content, which is ideal for those who prefer instant results.',
    },
  },
  // 3) Custom Use Cases
  {
    blogify: {
      description:
        'Blogify.ai does not currently support creating custom use cases or templates. It focuses on automated blog creation from various source formats using predefined flows.',
    },
    competitor: {
      description:
        'Rytr.ai allows users to create their own use cases, offering flexibility for unique content formats. This is especially useful for marketers and freelancers working with diverse content types.',
    },
  },
  // 4) Source File Size
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: 'Does not support file uploads; cannot process or analyze large source files.',
    },
  },
  // 5) Source File Duration
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Cannot interpret time-based content from videos or audios.',
    },
  },
  // 6) Co-Pilot Blog Creation
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description: 'Does not provide co-creation tools or collaborative blog-building features.',
    },
  },
  // 7) Content Output Quality
  {
    blogify: {
      description:
        'Blogify.ai delivers comprehensive blog posts that are SEO-optimized and structured with headings, images, and graphs based on the original media content.',
    },
    competitor: {
      description:
        'Rytr.ai offers several versions of content outputs per request, allowing users to choose and refine short-form or long-form content like ads, emails, and articles.',
    },
  },
  // 8) SEO Score & Optimization
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: 'Does not offer built-in SEO tools or keyword scoring capabilities.',
    },
  },
  // 9) Word Count
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Can write long-form content but lacks structured formatting for full blog delivery.',
    },
  },
  // 10) Language Support In Blogs
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Offers around 30+ languages, with limited localization support.',
    },
  },
  // 11) Auto-Pilot Blog Creation
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description: 'Requires users to generate sections individually through manual prompts.',
    },
  },
  // 12) Publish on Your Own Site
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description:
        'No publishing feature; you must copy and paste content into your site manually.',
    },
  },
  // 13) Schedule Blog Post
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Lacks any scheduling tools; relies on external platforms for posting.',
    },
  },
  // 14) Affiliate Monetization with AI
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'No integration with affiliate systems or tools for monetization.',
    },
  },
  // 15) Mobile Experience
  {
    blogify: {
      description:
        "Blogify.ai features a mobile-friendly dashboard that supports media uploads and content editing on mobile, although it's optimized for desktop workflows.",
    },
    competitor: {
      description:
        'Rytr.ai provides a smooth and responsive experience on mobile devices, making it easy to generate and edit content on the go.',
    },
  },
  // 16) Analytics
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'No analytics support; users must track performance externally.',
    },
  },
  // 17) Writing Snippet (Addon)
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description: 'Provides general copy snippets, not tailored to blog-specific needs.',
    },
  },
  // 18) YouTube Connect (Addon)
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'No YouTube integration or support for video-to-blog conversion.',
    },
  },
  // 19) YouTube Connect Pro (Addon)
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'Does not handle metadata or video-specific content breakdowns.',
    },
  },
  // 20) Support
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Support is available but limited to email-based with standard hours.',
    },
  },
  // 21) Browser Extension
  {
    blogify: {
      description:
        'Blogify.ai currently does not provide a browser extension. It is fully web-based, requiring login for blog automation workflows.',
    },
    competitor: {
      description:
        'Rytr.ai also does not offer a browser extension, though its web interface is lightweight and easily accessible through browsers.',
    },
  },
  // 22) Repurpose from other content
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Can only generate content from user-written prompts.',
    },
  },
  // 23) Automate & manual blog creation
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Only allows manual creation via repeated prompt writing.',
    },
  },
  // 24) Embed blog source
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'No functionality to include source or attribution within the content.',
    },
  },
  // 25) Blog templates
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Does not include templates or layout guides for blog structuring.',
    },
  },
  // 26) Content Organization
  {
    blogify: {
      description:
        'Blogify.ai organizes blogs based on source files and project categories, making it ideal for handling content in bulk from different formats like video, audio, and docs.',
    },
    competitor: {
      description:
        "Rytr.ai uses a traditional document and folder system for organizing drafts. It's efficient for smaller content pieces and single-author workflows.",
    },
  },
  // 27) AI generated cover & content image
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'No image generation or visual content support.',
    },
  },
  // 28) AI generated graphs & charts
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description: 'Does not support graph or chart generation within content.',
    },
  },
  // 29) Command Input Mode
  {
    blogify: {
      description:
        'Blogify.ai focuses on automation and does not feature a command input mode. Users rely on its smart processing of uploaded content.',
    },
    competitor: {
      description:
        'Rytr.ai includes a command mode where users can enter prompts to guide AI writing, making it suitable for power users and advanced writers.',
    },
  },
];

const BLOGIFY_VS_RYTR: ComparePageData = {
  metadata: {
    title: 'Blogify vs Rytr: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Rytr features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs rytr', 'ai writing tools comparison', 'rytr alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Rytr Alternative',
    description:
      'Blogify.ai outshines Rytr when it comes to automated blog creation from multimedia sources, delivering superior SEO tools and structured formatting. While Rytr is effective for short-form, prompt-based writing, Blogify is built to turn videos, audio, and documents into high-quality blog posts ready for publishing with zero hassle. 🚀',
    images: {
      competitorImage: Rytr,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Rytr',
  description:
    'Blogify.ai distinguishes itself from Rytr with advanced multimedia-to-blog capabilities, integrated SEO optimization, and seamless publishing workflows. Ideal for creators aiming to turn existing content into full-length, optimized blog posts — without relying on manual input.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: RytrLogo,
  },
};

export default BLOGIFY_VS_RYTR;
