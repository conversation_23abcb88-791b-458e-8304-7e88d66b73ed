import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import Writesonic from '@img/page-assets/comparison/Writesonic.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import WritesonicLogo from '@img/page-assets/comparison/writesoniclogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Article Summarizer',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
      'Fact Checker Tool',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'red-cross'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Analytics',
      'Writing Assistant Tone Modes',
      'Writing Snippet (Addon)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Real-time Search Data',
      'Embed blog source',
      'Blog templates',
      'Source File Size',
      'AI generated cover & content image',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: ['AI generated graphs & charts', 'Voice Command Features'],
    comparisons: {
      blogify: ['green-check', 'red-cross'],
      competitor: ['red-cross', 'green-check'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      image: Writesonic,
      description:
        'Only supports text-based input. Users must type or paste prompts to generate blog content. No media input options.',
    },
  },
  // 2) Article Summarizer
  {
    blogify: {
      description:
        'Blogify specializes in extracting core ideas from large content formats like videos and webinars and summarizing them into blog form.',
    },
    competitor: {
      description:
        'Writesonic also includes article summarization features, letting users condense content into short, digestible summaries or outlines.',
    },
  },
  // 3) Source File Duration
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description:
        'Cannot process timed media content like videos or audios. No media parsing support.',
    },
  },
  // 4) Auto-Pilot Blog Creation
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Manual prompt entry required for every section. No fully automated blog writing system available.',
    },
  },
  // 5) Fact Checker Tool
  {
    blogify: {
      description:
        'Blogify does not have a built-in fact-checking system, as it works from uploaded source material or user input.',
    },
    competitor: {
      description:
        'Chatsonic in Writesonic integrates with real-time search engines for validating facts and delivering up-to-date content.',
    },
  },
  // 6) Co-Pilot Blog Creation
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Lacks a structured co-pilot editor. Content must be edited and managed manually after generation.',
    },
  },
  // 7) SEO Score & Optimization
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        'Offers keyword-based suggestions, but lacks full SEO scoring and content optimization feedback.',
    },
  },
  // 8) Word Count
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Can generate long content but lacks automated formatting and structure suitable for full blogs.',
    },
  },
  // 9) Language Support In Blogs
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description:
        'Offers around 25–30 language options. Covers major languages but not as extensive.',
    },
  },
  // 10) Publish on Your Own Site
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description:
        'Does not offer domain publishing. Users need to manually copy and paste into external sites.',
    },
  },
  // 11) Schedule Blog Post
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description:
        'No built-in scheduling support. Users must rely on external tools or CMS platforms.',
    },
  },
  // 12) Affiliate Monetization with AI
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'No native affiliate tools or integrations for monetizing blog posts.',
    },
  },
  // 13) Analytics
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Lacks any built-in analytics for performance or reader insights.',
    },
  },
  // 14) Writing Assistant Tone Modes
  {
    blogify: {
      description:
        "Blogify has a built-in tone engine that mimics the user's voice or adapts tone based on preferred writing style, ideal for brand consistency.",
    },
    competitor: {
      description:
        'Writesonic allows users to pick from various tones such as witty, professional, casual, etc., while generating content in different formats.',
    },
  },
  // 15) Writing Snippet (Addon)
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description:
        'Provides general AI-generated snippets, not specifically optimized for structured blogging.',
    },
  },
  // 16) YouTube Connect (Addon)
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Does not support video-to-blog conversion. YouTube input is not accepted.',
    },
  },
  // 17) YouTube Connect Pro (Addon)
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'No tools for processing or analyzing video metadata or timelines.',
    },
  },
  // 18) Round the clock e-mail & chat support
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description:
        'Offers standard live support during business hours. May experience delays during peak times.',
    },
  },
  // 19) Repurpose from other content
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description:
        'Limited to generating content from scratch using typed input. No content repurposing capabilities.',
    },
  },
  // 20) Automate & manual blog creation
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description:
        'Fully manual. Users must prompt for each blog section separately. No automation.',
    },
  },
  // 21) Real-time Search Data
  {
    blogify: {
      description:
        "Blogify relies on uploaded content and doesn't integrate real-time data or search engine results in its AI process.",
    },
    competitor: {
      description:
        'Through Chatsonic, Writesonic offers real-time search data from Google, allowing users to generate factually accurate and trending content.',
    },
  },
  // 22) Embed blog source
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description:
        'Embedding sources is not supported. Output is plain text unless manually edited post-generation.',
    },
  },
  // 23) Blog templates
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description:
        'Offers basic templates for blog intros and sections but lacks full blog layout templates.',
    },
  },
  // 24) Source File Size
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Does not allow file uploads; users rely solely on prompt input. Not designed for large source content.',
    },
  },
  // 25) AI generated cover & content image
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description:
        'No image generation built-in. Users must manually add visuals from external tools.',
    },
  },
  // 26) AI generated graphs & charts
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description: 'No chart or graph generation capabilities available at this time.',
    },
  },
  // 27) Voice Command Features
  {
    blogify: {
      description:
        "Voice input or command support isn't a feature of Blogify. Users interact primarily through uploading files or manual prompts.",
    },
    competitor: {
      description:
        'Chatsonic supports voice-based prompts, making it convenient for users to create content via voice commands or dictation.',
    },
  },
];

const BLOGIFY_VS_WRITESONIC: ComparePageData = {
  metadata: {
    title: 'Blogify vs Writesonic: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Writesonic features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs writesonic', 'ai writing tools comparison', 'writesonic alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Writesonic Alternative',
    description:
      'Blogify.ai outperforms Writesonic for automated blog creation from multimedia sources, offering superior SEO optimization and structured formatting. While Writesonic excels at prompt-based content and copywriting, Blogify specializes in transforming videos, audio, and documents into ready-to-publish blog posts with minimal effort. 🚀',
    images: {
      competitorImage: Writesonic,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Writesonic',
  description:
    'Blogify.ai stands out from Writesonic with its multimedia-to-blog conversion, built-in SEO tools, and automated publishing. Perfect for creators who want to repurpose existing content into high-quality blogs without manual prompting.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: WritesonicLogo,
  },
};

export default BLOGIFY_VS_WRITESONIC;
