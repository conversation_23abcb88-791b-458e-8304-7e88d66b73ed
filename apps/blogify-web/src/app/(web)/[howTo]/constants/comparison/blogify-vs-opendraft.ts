import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import Opendraftpost from '@img/page-assets/comparison/opendraftpost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import Opendraftlogo from '@img/page-assets/comparison/opendraftlogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Ease of Use',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Content Generation',
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Text Editing',
      'Word Count',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Language Support In Blogs',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'AI Model Type',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Analytics',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'User Interface',
      'Automate & manual blog creation',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'Content Quality',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      image: Opendraftpost,
      description:
        'Focuses on writing from scratch or using user input prompts, lacking multimedia-to-text conversion.',
    },
  },
  // 2) Onboarding Experience
  {
    blogify: {
      description:
        'Blogify.ai is designed with simplicity in mind, making it easy for marketing teams to quickly generate content without a steep learning curve.',
    },
    competitor: {
      description:
        'OpenDraft.io offers a simple, straightforward interface that’s user-friendly for solo writers or small teams.',
    },
  },
  // 3) Custom Use Cases
  {
    blogify: {
      description:
        'Blogify.ai provides tailored AI models for SEO-focused marketing content creation.',
    },
    competitor: {
      description:
        'OpenDraft.io’s AI is focused on creative writing and narrative generation, with no marketing use-case focus.',
    },
  },
  // 4) Source File Size
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Does not support external file uploads, hence no file size handling capabilities.',
    },
  },
  // 5) Source File Duration
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Not applicable, as there is no support for media source integration.',
    },
  },
  // 6) Co-Pilot Blog Creation
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Helps with guided writing, but lacks dynamic editing features from external media inputs.',
    },
  },
  // 7) Content Output Quality
  {
    blogify: {
      description:
        'Excels in producing high-quality, SEO-optimized content tailored for marketing and engagement.',
    },
    competitor: {
      description: 'Delivers high-quality narrative content but lacks SEO and marketing depth.',
    },
  },
  // 8) SEO Score & Optimization
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: 'No integrated SEO tools or scoring available for live content optimization.',
    },
  },
  // 9) Word Count
  {
    blogify: {
      description:
        'Can create complete blog posts from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Word count depends on prompt input; generally optimized for medium-length articles.',
    },
  },
  // 10) Language Support In Blogs
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description:
        'Limited to English and a few major languages; no emphasis on multilingual generation.',
    },
  },
  // 11) Auto-Pilot Blog Creation
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description: 'Relies on manual creation and content drafting using prompt suggestions.',
    },
  },
  // 12) Publish on Your Own Site
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'No publishing functionality; content must be copied and pasted elsewhere.',
    },
  },
  // 13) Schedule Blog Post
  {
    blogify: {
      description: 'Includes scheduling tools to automate blog post publication.',
    },
    competitor: {
      description: 'No post scheduling; all publishing must be done manually.',
    },
  },
  // 14) Affiliate Monetization with AI
  {
    blogify: {
      description: 'Helps monetize blogs with affiliate link suggestions and product embedding.',
    },
    competitor: {
      description: 'No affiliate monetization features or integrations available.',
    },
  },
  // 15) Mobile Experience
  {
    blogify: {
      description: 'Features a clean, modern UI optimized for marketers.',
    },
    competitor: {
      description:
        'Minimalist UI for individual writers or small teams with fewer advanced options.',
    },
  },
  // 16) Analytics
  {
    blogify: {
      description: 'Tracks blog performance with built-in analytics (views, engagement, SEO).',
    },
    competitor: {
      description: 'No analytics or tracking dashboard available.',
    },
  },
  // 17) Writing Snippet (Addon)
  {
    blogify: {
      description: 'Quickly generates intros, CTAs, and summaries customized for blog structure.',
    },
    competitor: {
      description: 'Generates paragraphs but lacks reusable snippet or templating features.',
    },
  },
  // 18) YouTube Connect (Addon)
  {
    blogify: {
      description: 'Extracts content directly from YouTube and converts it into blog format.',
    },
    competitor: {
      description: 'No support for video integration or YouTube extraction.',
    },
  },
  // 19) YouTube Connect Pro (Addon)
  {
    blogify: {
      description:
        'Enhances YouTube content with timestamp extraction, metadata analysis, and SEO tools.',
    },
    competitor: {
      description: 'No YouTube-related tools or video script generation features.',
    },
  },
  // 20) Support
  {
    blogify: {
      description: 'Offers dedicated email and live chat support.',
    },
    competitor: {
      description: 'Email-only support with limited availability.',
    },
  },
  // 21) Browser Extension
  {
    blogify: {
      description: 'No browser extension; fully web-based platform.',
    },
    competitor: {
      description: 'No browser extension; accessed via a lightweight web interface.',
    },
  },
  // 22) Repurpose from other content
  {
    blogify: {
      description:
        'Converts multiple content types (videos, audio, documents, webpages, eCommerce listings) into blogs.',
    },
    competitor: {
      description: 'Only supports manual text input or AI prompt-driven creation.',
    },
  },
  // 23) Automate & manual blog creation
  {
    blogify: {
      description: 'Lets users auto-generate full blogs or manually edit section-by-section.',
    },
    competitor: {
      description: 'Fully manual experience with basic guidance.',
    },
  },
  // 24) Embed blog source
  {
    blogify: {
      description: 'Supports embedding original content sources (e.g., YouTube, webpage) in blogs.',
    },
    competitor: {
      description: 'No support for embedded content.',
    },
  },
  // 25) Blog templates
  {
    blogify: {
      description: 'Provides pre-designed blog layout templates for formatting and publishing.',
    },
    competitor: {
      description: 'No template system; all blogs must be formatted manually.',
    },
  },
  // 26) Content Organization
  {
    blogify: {
      description:
        'Organizes content by source types and categories, ideal for large-scale blog projects.',
    },
    competitor: {
      description: 'Content organization is basic, designed for solo or small-team writing.',
    },
  },
  // 27) AI generated cover & content image
  {
    blogify: {
      description: 'Automatically generates featured images and visuals based on blog topics.',
    },
    competitor: {
      description: 'No visual generation or media integration capabilities.',
    },
  },
  // 28) AI generated graphs & charts
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations within blog posts.',
    },
    competitor: {
      description: 'No visual/chart generation or data rendering features.',
    },
  },
  // 29) Command Input Mode
  {
    blogify: {
      description:
        'Focuses on automation and smart processing of uploaded content; no prompt command input required.',
    },
    competitor: {
      description: 'Primarily driven by user prompt commands for AI-generated content.',
    },
  },
];

const BLOGIFY_VS_OPENDRAFT: ComparePageData = {
  metadata: {
    title: 'Blogify vs Opendraft: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Opendraft features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs opendraft', 'ai writing tools comparison', 'opendraft alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Opendraft Alternative',
    description:
      'While Opendraft is designed to help writers stay in flow with minimalist distraction-free editing, Blogify.ai focuses on automating full-length blog creation from videos, audio, and documents. 🚀 Blogify eliminates manual writing with built-in SEO tools and a seamless publishing pipeline — perfect for content creators who value speed and scale.',
    images: {
      competitorImage: Opendraftpost,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Opendraft',
  description:
    'Blogify.ai differentiates itself from Opendraft by automating content generation from multimedia files, offering SEO enhancements, and enabling direct publishing workflows. For users looking to repurpose long-form content or scale up blog production without typing, Blogify is a clear winner.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: Opendraftlogo,
  },
};

export default BLOGIFY_VS_OPENDRAFT;
