import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import Satellitorpost from '@img/page-assets/comparison/satellitorpost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import Satellitorlogo from '@img/page-assets/comparison/satellitorlogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'SERP Gap Analysis',
      'Source File Size',
      'Use Case Customization',
      'Source File Duration',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Auto-Pilot Blog Creation',
      'Content Trend Monitoring',
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Word Count',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Keyword Clustering',
      'Language Support In Blogs',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Analytics',
      'Competitor Monitoring',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Round the clock e-mail & chat support',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Content Export Options',
      'AI generated cover & content image',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      image: Satellitorpost,
      description:
        "Satellitor is built mainly for planning and writing content from scratch. It doesn't support media-based or file-based blog generation.",
    },
  },
  {
    blogify: {
      description:
        'Blogify doesn’t focus on SEO gap analysis. Its strength lies in transforming content into SEO blogs.',
    },
    competitor: {
      description:
        'Satellitor specializes in identifying keyword/content gaps in search results to help boost SEO strategies.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Satellitor doesn’t allow uploading external source files, so size limitations don’t apply.',
    },
  },
  {
    blogify: {
      description:
        'Blogify supports multiple content input types (video, audio, PDF) for blog creation with AI assistance.',
    },
    competitor: {
      description:
        'Satellitor’s use cases are limited to SEO analysis, keyword tracking, and competitor research.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description:
        'Satellitor has no media file processing capabilities, so duration-based conversion is not supported.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        "Satellitor provides assistance with blog planning but doesn't generate content autonomously from media.",
    },
  },
  {
    blogify: {
      description: 'Blogify doesn’t currently provide trend analysis features.',
    },
    competitor: {
      description:
        'Satellitor tracks evolving content trends and keyword performance to guide users in creating relevant content.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Satellitor focuses more on planning outlines and strategies than collaborative writing with AI.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        'Satellitor emphasizes SEO keyword planning but lacks integrated live SEO scoring or optimization tools.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Satellitor helps draft detailed blogs but doesn’t focus on content structuring automatically.',
    },
  },
  {
    blogify: {
      description: 'Keyword grouping isn’t a primary feature in Blogify.',
    },
    competitor: {
      description:
        'Satellitor clusters related keywords for optimized topic targeting and SEO-rich content structuring.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description:
        'Satellitor supports limited language options primarily suited for English-focused markets.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'Satellitor doesn’t support blog publishing or domain-level integrations.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Satellitor does not offer scheduling capabilities for content delivery.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'Monetization tools are not available within Satellitor’s system.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Satellitor does not offer in-built content analytics features.',
    },
  },
  {
    blogify: {
      description:
        'Blogify doesn’t include tools for tracking competitors or SEO ranking positions.',
    },
    competitor: {
      description:
        'Satellitor actively monitors competitor content strategies and keyword usage for strategic SEO planning.',
    },
  },
  // 2) SEO Gap Analysis
  {
    blogify: {
      description:
        'Blogify doesn’t focus on SEO gap analysis. Its strength lies in transforming content into SEO blogs.',
    },
    competitor: {
      description:
        'Satellitor specializes in identifying keyword/content gaps in search results to help boost SEO strategies.',
    },
  },
  // 3) Snippet Generation
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description:
        'Satellitor provides writing tips but no modular snippet system tailored for blogs.',
    },
  },
  // 4) YouTube to Blog
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'No integration with YouTube or any external video platforms.',
    },
  },
  // 5) YouTube Metadata Tools
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'Not applicable as video integration isn’t available.',
    },
  },
  // 6) Support
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description:
        'Satellitor support is limited to email responses and documentation; no live chat support is available.',
    },
  },
  // 7) Content Repurposing
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Satellitor only supports manual input or prompt-based writing.',
    },
  },
  // 8) Blog Creation Mode
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description:
        'Satellitor leans more towards manual creation with guided outlines and frameworks.',
    },
  },
  // 9) Embed Source Content
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'This feature is not available on Satellitor’s platform.',
    },
  },
  // 10) Blog Layout Templates
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'No built-in templates for blog creation are offered by Satellitor.',
    },
  },
  // 11) Blog Export Options
  {
    blogify: {
      description:
        'Blogify allows exporting full blogs with multimedia elements, ready for CMS publishing.',
    },
    competitor: {
      description:
        'Satellitor enables exporting of detailed SEO reports and keyword data for analysis and integration.',
    },
  },
  // 12) Image Generation
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'Satellitor doesn’t generate visuals or images for content.',
    },
  },
  // 13) Chart & Graph Generation
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description:
        'Satellitor does not provide visual content creation tools like graphs or charts.',
    },
  },
];

const BLOGIFY_VS_SATELLITOR: ComparePageData = {
  metadata: {
    title: 'Blogify vs Satellitor: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Satellitor features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs satellitor', 'ai writing tools comparison', 'satellitor alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Satellitor Alternative',
    description:
      'Blogify.ai outshines Satellitor in transforming diverse content formats into ready-to-publish blog posts. While Satellitor supports AI-generated content, Blogify leads with its automation, SEO features, and multimedia integration — enabling creators to repurpose videos, documents, and audio files effortlessly into high-quality blog articles. 🚀',
    images: {
      competitorImage: Satellitorpost, // Make sure you have imported the correct image component
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Satellitor',
  description:
    'Blogify.ai distinguishes itself from Satellitor with advanced capabilities like AI-generated images, auto-blog creation from any source file, SEO scoring, and seamless publishing workflows. Ideal for creators who want to scale content production without writing from scratch.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: Satellitorlogo, // Make sure this is correctly imported
  },
};

export default BLOGIFY_VS_SATELLITOR;
