import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import JasperDashboard from '@img/page-assets/comparison/jasper.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import Jasper<PERSON>ogo from '@img/page-assets/comparison/jasperlogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Long-form Blog Writer',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'Product Description Generator',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Bulk Content Generation',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'red-cross', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'red-cross', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Automate & manual blog creation',
      'Project Folders & Organization',
      'Embed blog source',
      'Blog templates',
      'Image Generator (AI Art)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'AI generated graphs & charts',
      'API Access',
      'Team Collaboration',
      'Plagiarism Checker',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      image: JasperDashboard,
      description:
        'Jasper requires users to manually enter prompts. It does not support blog creation from external media or document files.',
    },
  },
  // 2) Free Plan Available – description only
  {
    blogify: {
      description:
        'Blogify.ai offers a limited free tier to test core functionality like content conversion and manual creation.',
    },
    competitor: {
      description:
        'Jasper.ai offers a free trial but no permanent free plan, allowing users to test features before committing.',
    },
  },
  // 3) Source File Size – description only
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Jasper does not accept file uploads for content creation, so file size support is irrelevant.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description:
        'Does not support time-based files or interpret video/audio duration for content extraction.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Content creation is prompt-driven, requiring users to generate and format each part manually.',
    },
  },
  {
    blogify: {
      description:
        'Currently focuses on a platform-based experience with plans to release API for advanced use cases and integrations.',
    },
    competitor: {
      description:
        'Jasper offers API access on business plans, enabling integration with your existing apps or content systems.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Offers content templates but lacks an end-to-end co-pilot experience focused on full blog creation.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        "Integrates with SurferSEO (external tool) for optimization, but it's not built-in and requires a separate subscription.",
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Can generate long-form text but relies on user prompts and lacks automatic structuring for SEO blogs.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai does not include an in-built plagiarism checker but encourages content rewriting and enhancement for originality.',
    },
    competitor: {
      description:
        'Jasper does not feature an internal plagiarism checker either, and users must rely on external tools to verify originality.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description:
        'Offers content generation in approximately 30+ languages with decent coverage but less expansive.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description:
        'Jasper does not offer built-in blog publishing. Users must copy and paste content to external platforms.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai offers a task-oriented dashboard focused on automated blog generation, ideal for marketers and creators who want minimal friction.',
    },
    competitor: {
      description:
        'Jasper has a sleek and beginner-friendly interface optimized for prompt-based content generation across various formats.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description:
        'Jasper lacks native scheduling features and relies on third-party platforms for post scheduling.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description:
        'Does not offer monetization or affiliate integration features within the platform.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Jasper does not track content performance or provide usage analytics directly.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description:
        'Provides general AI templates for snippets, not tailored specifically for structured blog writing.',
    },
  },
  {
    blogify: {
      description:
        "Blogify.ai doesn't currently include a real-time conversational AI, but it guides users with smart blog flows and assistant prompts.",
    },
    competitor: {
      description:
        'Jasper features a chat interface allowing dynamic ideation and real-time writing assistance like ChatGPT.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Jasper cannot process or convert YouTube videos into blog content.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description:
        'Jasper does not include any advanced video analysis or YouTube integration features.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai is primarily built for solo creators and SMEs, with role-based content workflow in development.',
    },
    competitor: {
      description:
        'Jasper includes a robust workspace and project folder system, ideal for teams and content agencies.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description:
        'Support is available but usually within business hours, and may not include chat depending on plan.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description:
        'Content is created from scratch via text input only; no repurposing from external media or files.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai includes automatic AI-generated cover and in-content images, tailored to the blog topic or media input.',
    },
    competitor: {
      description:
        "Jasper integrates with third-party tools like DALL·E, but doesn't provide native AI-generated visual content.",
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description:
        'Relies heavily on manual input. Automation is limited to content snippets and templates.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description:
        'Jasper does not allow embedding or referencing external sources directly in the content.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai allows direct export to your own site through custom domain/subdomain setup. It also supports scheduling posts, making it great for full blog publishing workflows.',
    },
    competitor: {
      description:
        "Jasper provides export via copy-paste or manual download. It doesn't support direct publishing to external platforms.",
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description:
        'Includes writing templates but lacks layout-specific templates designed for blog publishing.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description:
        'Jasper does not provide native AI-generated visual content; users must use external image tools.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description:
        'Jasper does not generate or visualize any charts or data elements within its platform.',
    },
  },
];

const BLOGIFY_VS_JASPERAI: ComparePageData = {
  metadata: {
    title: 'Blogify vs Jasper.ai: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Jasper.ai features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs jasper', 'ai writing tools comparison', 'jasper alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Jasper.ai Alternative',
    description:
      'Blogify.ai outperforms Jasper.ai for automated blog creation from multimedia sources, offering superior SEO optimization and structured formatting. While Jasper excels at prompt-based content generation, Blogify specializes in transforming videos, audio, and documents into ready-to-publish blog posts with minimal effort. 🚀',
    images: {
      competitorImage: JasperDashboard,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Jasper.ai',
  description:
    'Blogify.ai stands out from Jasper.ai with its multimedia-to-blog conversion, built-in SEO tools, and automated publishing. Perfect for creators who want to repurpose existing content into high-quality blogs without manual prompting.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: JasperLogo,
  },
};

export default BLOGIFY_VS_JASPERAI;
