import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import CopyaiLogo from '@img/page-assets/comparison/copyai.png';
import Copypost from '@img/page-assets/comparison/copydashboard.jpg';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Free Plan Available',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'API Access',
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Word Count',
      'Plagiarism Checker',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'red-cross'],
      competitor: ['green-check', 'red-cross', 'green-check', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Language Support In Blogs',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'User Interface',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Analytics',
      'Writing Snippet (Addon)',
      'ChatGPT-like Assistant',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Team Collaboration',
      'Round the clock e-mail & chat support',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Image Generation',
      'Automate & manual blog creation',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Embed blog source',
      'Content Export Options',
      'Blog templates',
      'AI generated cover & content image',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      image: Copypost,
      description:
        'Focused on AI-generated copy through prompt inputs; does not support multimedia or file-based blog creation.',
    },
  },
  // 2) Free Plan Available – description only
  {
    blogify: {
      description:
        'Blogify.ai offers a limited free tier to test core functionality like content conversion and manual creation.',
    },
    competitor: {
      description:
        'Copy.ai has a free plan with monthly word limits, great for new users exploring content generation.',
    },
  },
  // 3) Source File Size – description only
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: 'Does not accept file uploads; limited to typed or pasted text input only.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description:
        'Does not handle media files; cannot process content duration or extract from time-based media.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Requires manual prompting and input at every stage to create content; no one-click blog generation workflow.',
    },
  },
  {
    blogify: {
      description:
        'Currently focuses on a platform-based experience with plans to release API for advanced use cases and integrations.',
    },
    competitor: {
      description:
        'Copy.ai offers API access on business plans, enabling integration with your existing apps or content systems.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Works through individual prompt generation; lacks a dedicated co-pilot-style collaborative blog editor.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        'Limited SEO assistance through keyword prompts; lacks integrated SEO scoring or analytics.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Supports long-form generation via repeated prompting but lacks a unified long-form blog writing flow.',
    },
  },
  // …and so on for the remaining items…
  {
    blogify: {
      description:
        'Blogify.ai does not include an in-built plagiarism checker but encourages content rewriting and enhancement for originality.',
    },
    competitor: {
      description:
        'Copy.ai does not feature an internal plagiarism checker either, and users must rely on external tools to verify originality.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Offers multilingual support but covers fewer than 30 major languages.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration. ',
    },
    competitor: {
      description: 'Does not offer built-in publishing or site connection features.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai offers a task-oriented dashboard focused on automated blog generation, ideal for marketers and creators who want minimal friction.',
    },
    competitor: {
      description:
        'Copy.ai has a sleek and beginner-friendly interface optimized for quick prompt-based content generation. It’s intuitive and clean for all users.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description:
        'Lacks a built-in scheduling feature; requires exporting content for manual publishing elsewhere.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description:
        'Not focused on monetization; doesn’t support affiliate systems or recommendations.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'No built-in analytics dashboard or tracking features for generated content.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.        ',
    },
    competitor: {
      description:
        'Offers a wide range of content snippets, but not specialized for blog-specific segments.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai doesn’t currently include a real-time conversational AI, but it guides users with smart blog flows and assistant prompts.  ',
    },
    competitor: {
      description:
        'Copy.ai features Copy.ai Chat, allowing dynamic ideation and real-time writing assistance like ChatGPT, best for brainstorming.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'No functionality for extracting or using YouTube video content in writing.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description:
        'No features related to video metadata, timestamps, or structured blog extraction from YouTube.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai is primarily built for solo creators and SMEs, with role-based content workflow in development.',
    },
    competitor: {
      description:
        'Copy.ai supports team workspaces and content collaboration in its higher-tier plans, suitable for agency workflows.',
    },
  },

  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description:
        'Provides standard email/chat support for general queries, not tailored to blogging workflows.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts.Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description:
        'Built solely for generating text from prompts; does not support content repurposing from external sources.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai includes automatic AI-generated cover and in-content images, tailored to the blog topic or media input.        ',
    },
    competitor: {
      description:
        'Copy.ai does not support AI image generation directly. Users must rely on third-party tools for visuals.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description:
        'All content must be generated through manual input and prompt tweaking; no full automation.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post. ',
    },
    competitor: {
      description:
        'No feature to embed or reference original content sources inside generated text.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai allows direct export to your own site through custom domain/subdomain setup. It also supports scheduling posts, making it great for full blog publishing workflows.',
    },
    competitor: {
      description:
        'Copy.ai provides export via copy-paste or manual download. It doesn’t support direct publishing to external platforms, which requires extra steps.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.        ',
    },
    competitor: {
      description: 'Does not include blog-specific templates or layout guidance.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.        ',
    },
    competitor: {
      description: 'Requires external tools for visuals; does not generate or suggest media.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.        ',
    },
    competitor: {
      description:
        'No ability to generate visuals or graphs within content; requires manual chart creation externally.',
    },
  },
];

const BLOGIFY_VS_COPYAI: ComparePageData = {
  metadata: {
    title: 'Blogify vs Copy.ai: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Copy.ai features, pricing, and capabilities for content creation in 2025.',
    keywords: ['blogify vs copy.ai', 'ai writing tools comparison', 'blogify alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Copy.ai Alternative',
    description:
      'Writers prefer Blogify.ai over Copy.ai for its seamless video-to-blog conversion, SEO-optimized content, and structured formatting. Unlike Copy.ai, Blogify specializes in transforming YouTube videos into high-quality, ready-to-publish blog posts effortlessly. 🚀',
    images: {
      competitorImage: Copypost,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Copy.ai',
  description:
    'Writers prefer Blogify.ai for its easy video-to-blog conversion, SEO optimization, and structured formatting. Unlike Copy.ai, it specializes in transforming YouTube videos into high-quality blog posts effortlessly. 🚀',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: CopyaiLogo,
  },
};

export default BLOGIFY_VS_COPYAI;
