import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import TypeAI from '@img/page-assets/comparison/typeaipost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import TypeAILogo from '@img/page-assets/comparison/typeailogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Real-Time Editing & Collaboration',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Conversation-Based Brainstorming',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Use-Case Agnostic',
      'Affiliate Monetization with AI',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
      'Minimalist Writing Experience',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'red-cross'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Embed blog source',
      'Branding & Voice Training',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'AI generated graphs & charts',
      'Code-Free Integration Philosophy',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  // 1) Create Blog From Anything – includes images
  {
    blogify: {
      image: Blogifypost,
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      image: TypeAI,
      description:
        "Type.ai relies on written input and doesn't support multimedia-based blog creation.",
    },
  },
  // 3) Source File Size
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: "Type.ai doesn't support file uploads for content extraction.",
    },
  },
  // 4) Source File Duration
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Does not support source duration or video/audio file processing.',
    },
  },
  // 5) Auto-Pilot Blog Creation
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Content creation is manual via text prompts without full automation for blog creation.',
    },
  },
  // 6) Co-Pilot Blog Creation
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Provides prompt-based suggestions but lacks a structured co-pilot blog builder.',
    },
  },
  // 7) SEO Score & Optimization
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: "Doesn't offer built-in SEO optimization or scoring tools.",
    },
  },
  // 9) Word Count
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Allows for long-form writing but requires careful prompt control to maintain structure.',
    },
  },
  // 10) Language Support In Blogs
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Limited language support depending on the prompt input and AI model.',
    },
  },
  // 11) Publish on Your Own Site
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'No built-in support for direct publishing to websites or domains.',
    },
  },
  // 12) Schedule Blog Post
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'No scheduling feature available.',
    },
  },
  // 14) Affiliate Monetization with AI
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'Monetization tools are not integrated into the platform.',
    },
  },
  // 15) Analytics
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Analytics features are not included in Type.ai.',
    },
  },
  // 16) Writing Snippet (Addon)
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description:
        'Includes generic templates, but they are not optimized for full blog formatting.',
    },
  },
  // 17) YouTube Connect (Addon)
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Does not support YouTube content conversion.',
    },
  },
  // 18) YouTube Connect Pro (Addon)
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'No advanced video-related features are available.',
    },
  },
  // 19) Support
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Limited to email or ticket support, typically during business hours.',
    },
  },
  // 21) Repurpose from other content
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Does not support content repurposing from external formats.',
    },
  },
  // 22) Automate & manual blog creation
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Content creation is manual, and automation is limited to prompt output.',
    },
  },
  // 23) Embed blog source
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'Embedding sources is not a supported feature.',
    },
  },
  // 25) Blog templates
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Blog-specific templates are not available.',
    },
  },
  // 26) AI generated cover & content image
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'No native support for AI-generated images.',
    },
  },
  // 27) AI generated graphs & charts
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description: 'Visualization tools like graphs and charts are not offered.',
    },
  },
];

const BLOGIFY_VS_TYPEAI: ComparePageData = {
  metadata: {
    title: 'Blogify vs Type.ai: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Type.ai features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs type.ai', 'ai writing tools comparison', 'type.ai alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Type.ai Alternative',
    description:
      'Blogify.ai surpasses Type.ai in automating blog creation from multimedia sources like videos, audio, and documents — with built-in SEO and ready-to-publish formatting. While Type.ai focuses on collaborative document editing and prompt-driven writing, Blogify is built for hands-free, content repurposing into high-quality blogs. 🚀',
    images: {
      competitorImage: TypeAI,
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Type.ai',
  description:
    'Blogify.ai stands apart from Type.ai with its automated multimedia-to-blog conversion, robust SEO toolkit, and effortless publishing pipeline. Ideal for creators looking to transform existing content into long-form, optimized blog posts without manual writing.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: TypeAILogo,
  },
};

export default BLOGIFY_VS_TYPEAI;
