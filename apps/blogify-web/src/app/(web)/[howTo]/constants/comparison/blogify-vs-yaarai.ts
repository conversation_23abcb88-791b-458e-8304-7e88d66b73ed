import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import YaaraPost from '@img/page-assets/comparison/yaarapost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import YaaraLogo from '@img/page-assets/comparison/yaaralogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Source File Size',
      'Content Rephrasing & Paraphrasing',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Co-Pilot Blog Creation',
      'Tone & Style Customization',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Blog Outline Generator',
      'Affiliate Monetization with AI',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Content Scoring & Feedback',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Web Page Summarizer',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'Cold Email Generator',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      description:
        "Yaara requires text-based prompts and doesn't support repurposing content from videos or documents.",
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Yaara does not offer content extraction from uploaded files, regardless of size.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai is focused on creating original content from external sources like videos, podcasts, or webpages. It doesn’t offer direct paraphrasing functionality.',
    },
    competitor: {
      description:
        'Yaara.ai offers a strong paraphrasing and rewriting tool, making it useful for improving existing text or avoiding plagiarism.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'Yaara doesn’t support source duration or media uploads for content conversion.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description: 'Yaara requires manual effort for structuring and editing long-form content.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Yaara allows prompt refinement but lacks a dedicated co-pilot workflow for blogs.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai comes with a Personalized Tone Engine that adapts to the user’s writing style, ensuring consistency across blog content.',
    },
    competitor: {
      description:
        'Yaara.ai lets users choose from different tones like formal, casual, professional, etc., giving flexibility for different audiences and platforms.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description: 'Yaara doesn’t provide SEO scoring tools or built-in optimization.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description: 'Yaara can generate long content, but managing structure requires manual input.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Yaara supports multilingual output but is limited to around 25+ languages.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description: 'Yaara does not offer blog publishing or domain management features.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Yaara lacks scheduling or calendar-based publishing tools.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai automatically creates structured blog posts with outlines when transforming content from videos or audio, saving time for creators.',
    },
    competitor: {
      description:
        'Yaara.ai generates outlines for blogs based on input prompts or topics, helping writers organize their ideas before drafting full articles.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'Yaara does not offer affiliate integration or monetization features.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Yaara does not include analytics or blog tracking capabilities.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai currently does not offer built-in scoring or quality feedback but focuses on optimized blog delivery through its AI engine and SEO scoring.',
    },
    competitor: {
      description:
        'Yaara.ai provides real-time content scoring and suggestions, helping improve grammar, readability, and tone before publishing.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description: 'Yaara offers general-purpose snippets but not blog-focused ones.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Yaara lacks any YouTube integration or video content conversion.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'No such tool exists within Yaara.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description:
        'Yaara generally offers email or ticket-based support with slower response times.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description:
        'Yaara focuses on creating content from user prompts, not repurposing existing material.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Yaara’s workflow is manual and prompt-based, with limited automation.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai can convert full webpages into SEO-optimized blog articles, allowing creators to repurpose existing online content into monetizable formats.',
    },
    competitor: {
      description:
        'Yaara.ai summarizes webpages into concise overviews, ideal for quick content digestion or turning web info into shorter summaries.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'Yaara does not allow content embedding or source linking.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Yaara does not offer structured blog templates or formatting assistance.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'Yaara doesn’t support AI-generated imagery.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai does not include tools for email marketing or cold email generation as its primary focus is long-form content creation.',
    },
    competitor: {
      description:
        'Yaara.ai provides cold email templates and automated generators that help professionals craft outreach emails quickly and effectively.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description:
        'Yaara lacks any support for visual content creation like charts or infographics.',
    },
  },
];
const BLOGIFY_VS_YAARAAI: ComparePageData = {
  metadata: {
    title: 'Blogify vs Yaara.ai: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Yaara.ai features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs yaara.ai', 'ai writing tools comparison', 'yaara.ai alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Yaara.ai Alternative',
    description:
      'Blogify.ai outperforms Yaara.ai by automating blog creation from videos, audio, documents, and URLs — complete with SEO optimization and publishing-ready formatting. While Yaara.ai emphasizes AI-powered text generation and templates, Blogify is tailored for content repurposing into structured, long-form blogs with minimal effort. 🚀',
    images: {
      competitorImage: YaaraPost, // Replace with actual Yaara.ai image if available
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Yaara.ai',
  description:
    'Blogify.ai stands apart from Yaara.ai with its multimedia-to-blog automation, built-in SEO tools, and seamless publishing system. It’s the perfect tool for creators who want to convert content into fully optimized blog posts — without the need to start from scratch.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: YaaraLogo, // Replace with actual Yaara.ai logo if available
  },
};

export default BLOGIFY_VS_YAARAAI;
