import Blogify from '@img/page-assets/comparison/blogify-blog-post.png';
import Image from 'next/image';

import { GlowBox } from '@/app/(web)/partner-program/components';

export default function UniqueFeatures() {
  return (
    <section className="mt-16 px-4">
      <h1 className="mx-auto max-w-2xl text-center text-3xl font-bold sm:text-4xl lg:text-4xl">
        Unique Blogify Features / Specific Features That Rytr is Missing
      </h1>

      {/* First Row: 3 items */}
      <div className="mt-8 flex flex-col items-center gap-6 sm:flex-row sm:justify-center">
        <GlowBox className="flex max-w-xs flex-col p-4">
          <Image className="rounded-lg" src={Blogify} alt="Blogify-Features-Image" />
          <h2 className="mt-4 text-center font-bold sm:text-left">Blog Publishing</h2>
          <p className="mt-4 text-sm text-typo-secondary">
            Lorem ipsum dolor sit amet consectetur. Neque cras egestas ut diam enim. Lacus tristique
            cras laoreet euismod eget eros sed ac. Tellus lectus nec cras ut. Urna tellus sapien
            donec semper.
          </p>
        </GlowBox>

        <GlowBox className="flex max-w-xs flex-col p-4">
          <Image className="rounded-lg" src={Blogify} alt="Blogify-Features-Image" />
          <h2 className="mt-4 text-center font-bold sm:text-left">Content Optimization</h2>
          <p className="mt-4 text-sm text-typo-secondary">
            Lorem ipsum dolor sit amet consectetur. Neque cras egestas ut diam enim. Lacus tristique
            cras laoreet euismod eget eros sed ac. Tellus lectus nec cras ut. Urna tellus sapien
            donec semper.
          </p>
        </GlowBox>

        <GlowBox className="flex max-w-xs flex-col p-4">
          <Image className="rounded-lg" src={Blogify} alt="Blogify-Features-Image" />
          <h2 className="mt-4 text-center font-bold sm:text-left">AI Suggestions</h2>
          <p className="mt-4 text-sm text-typo-secondary">
            Lorem ipsum dolor sit amet consectetur. Neque cras egestas ut diam enim. Lacus tristique
            cras laoreet euismod eget eros sed ac. Tellus lectus nec cras ut. Urna tellus sapien
            donec semper.
          </p>
        </GlowBox>
      </div>

      {/* Second Row: 2 items */}
      <div className="mt-8 flex flex-col items-center gap-6 sm:flex-row sm:justify-center">
        <GlowBox className="flex max-w-xs flex-col p-4">
          <Image className="rounded-lg" src={Blogify} alt="Blogify-Features-Image" />
          <h2 className="mt-4 text-center font-bold sm:text-left">Advanced Analytics</h2>
          <p className="mt-4 text-sm text-typo-secondary">
            Lorem ipsum dolor sit amet consectetur. Neque cras egestas ut diam enim. Lacus tristique
            cras laoreet euismod eget eros sed ac. Tellus lectus nec cras ut. Urna tellus sapien
            donec semper.
          </p>
        </GlowBox>

        <GlowBox className="flex max-w-xs flex-col p-4">
          <Image className="rounded-lg" src={Blogify} alt="Blogify-Features-Image" />
          <h2 className="mt-4 text-center font-bold sm:text-left">Seamless Integrations</h2>
          <p className="mt-4 text-sm text-typo-secondary">
            Lorem ipsum dolor sit amet consectetur. Neque cras egestas ut diam enim. Lacus tristique
            cras laoreet euismod eget eros sed ac. Tellus lectus nec cras ut. Urna tellus sapien
            donec semper.
          </p>
        </GlowBox>
      </div>

      <p className="mx-auto mt-6 max-w-2xl text-center text-sm text-typo-secondary">
        Discover more useful features that Blogify offers. Sign up today to explore all the powerful
        tools and functionalities tailored for you!
      </p>
    </section>
  );
}
