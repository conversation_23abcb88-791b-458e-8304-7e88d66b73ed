import Image from 'next/image';
import { ComparePageData } from '../../types';

export default function ComparisonIntro({ intro }: ComparePageData) {
  const customLoader = ({ src }: { src: string }) => src;

  return (
    <section className="flex flex-col items-center gap-6 p-4 pt-24 md:flex-row md:justify-center md:gap-8 lg:gap-12">
      {/* Text Content */}
      <div className="text-center md:text-left">
        <span className="text-sm font-medium text-primary">{intro.comparisonTitle}</span>
        <h1 className="mx-auto mt-4 max-w-full text-3xl font-bold sm:text-4xl lg:text-5xl">
          {intro.mainTitle}
        </h1>
        <p className="my-6 max-w-full text-base text-typo-secondary sm:w-3/4">
          {intro.description}
        </p>
      </div>

      {/* Images */}
      <div className="relative mt-4 w-full">
        {/* Competitor Image */}
        {typeof intro.images.competitorImage === 'string' ? (
          <Image
            loader={customLoader}
            src={intro.images.competitorImage}
            alt="Competitor product screenshot"
            width={280}
            height={280}
            className="absolute -left-8 -top-12 z-0 rounded-lg sm:-left-12 sm:-top-20 md:-left-40 md:-top-28"
            unoptimized={true}
          />
        ) : (
          <Image
            src={intro.images.competitorImage}
            alt="Competitor product screenshot"
            width={280}
            height={280}
            className="absolute -left-8 -top-12 z-0 rounded-lg sm:-left-12 sm:-top-20 md:-left-40 md:-top-28"
            quality={90}
          />
        )}

        {/* Blogify Image */}
        {typeof intro.images.blogifyImage === 'string' ? (
          <Image
            loader={customLoader}
            src={intro.images.blogifyImage}
            alt="Blogify product screenshot"
            width={280}
            height={280}
            className="relative z-10 rounded-lg"
            unoptimized={true}
          />
        ) : (
          <Image
            src={intro.images.blogifyImage}
            alt="Blogify product screenshot"
            width={280}
            height={280}
            className="relative z-10 rounded-lg"
            quality={90}
          />
        )}
      </div>
    </section>
  );
}
