import Image from 'next/image';
import { GlowBox } from '@/app/(web)/partner-program/components';
import { ComparePageData } from '../../types';

export default function CompareBetween({
  title,
  description,
  comparisonData = [],
  images,
}: ComparePageData) {
  const IMG_W = 450;
  const IMG_H = 150;

  return (
    <div className="mt-32">
      <section>
        <h1 className="text-center text-4xl font-bold">{title}</h1>
        <p className="mt-6 text-center text-gray-500">{description}</p>
      </section>

      <GlowBox className="mt-16">
        <table className="w-full">
          <thead className="rounded-t-xl text-left">
            <tr className="grid grid-cols-2">
              <th className="flex flex-col items-center border-secondary/25 p-5 text-center">
                {images?.blogifyLogo && (
                  <Image
                    className="rounded-lg"
                    src={images.blogifyLogo}
                    alt="Blogify Logo"
                    width={80}
                    height={80}
                    priority
                  />
                )}
              </th>
              <th className="flex flex-col items-center border-l border-secondary/25 p-5 text-center">
                {images?.competitorLogo && (
                  <Image
                    className="rounded-lg"
                    src={images.competitorLogo}
                    alt="Competitor Logo"
                    width={80}
                    height={80}
                    priority
                  />
                )}
              </th>
            </tr>
          </thead>
          <tbody className="rounded-b-xl">
            {comparisonData.map((item, idx) => (
              <tr key={idx} className="grid grid-cols-2 border-t border-secondary/25">
                <td className="flex flex-col space-y-2 border-secondary/25 p-5">
                  {item.blogify.image && (
                    <Image
                      className="rounded-md"
                      src={item.blogify.image}
                      alt={`Blogify Feature ${idx + 1}`}
                      width={IMG_W}
                      height={IMG_H}
                    />
                  )}
                  <p className="text-sm text-typo-secondary">{item.blogify.description}</p>
                </td>
                <td className="flex flex-col space-y-2 border-l border-secondary/25 p-5">
                  {item.competitor.image && (
                    <Image
                      className="rounded-md"
                      src={item.competitor.image}
                      alt={`Competitor Feature ${idx + 1}`}
                      width={IMG_W}
                      height={IMG_H}
                    />
                  )}
                  <p className="text-sm text-typo-secondary">{item.competitor.description}</p>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </GlowBox>
    </div>
  );
}
