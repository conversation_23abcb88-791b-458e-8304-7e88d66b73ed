import HomeContactUsShare from '@/app/(web)/(home)/components/HomeContactUs';
import HomeRatingWidgets from '@/app/(web)/(home)/components/HomeRatingWidgets';
import HomeReviews from '@/app/(web)/(home)/components/HomeReviews';
import HomeTrustBadge from '@/app/(web)/(home)/components/HomeTrustBadge';
import { Button } from '@ps/ui/components/button';
import Link from 'next/link';

export default function TryBlogify() {
  return (
    <section className="mt-24">
      <h1 className="mx-auto  max-w-2xl px-4 text-center text-4xl font-bold lg:text-5xl">
        Try Blogify for FREE{' '}
      </h1>

      <p className="mx-auto max-w-2xl p-4 text-center text-sm text-typo-secondary">
        Create high-quality blog posts in minutes using the power of AI. Blogify helps you turn
        ideas, videos, or outlines into engaging content effortlessly.{' '}
      </p>

      <div className="mx-auto mb-8 mt-4 gap-5 text-center ">
        <Link href="/#pricing-table">
          <Button variant="glow" className="h-10 whitespace-nowrap">
            Start 3-Days Free Trial
          </Button>
        </Link>
      </div>
      <HomeRatingWidgets />
      <HomeTrustBadge />
      <HomeReviews />
      <HomeContactUsShare />
    </section>
  );
}
