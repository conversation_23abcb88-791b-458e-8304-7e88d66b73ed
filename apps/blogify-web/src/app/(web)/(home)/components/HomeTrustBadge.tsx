'use client';
import type { StaticImageData } from 'next/image';

import useEmblaCarousel from 'embla-carousel-react';
import AutoScroll from 'embla-carousel-auto-scroll';
import Image from 'next/image';

import TheChronicleJournal from '@img/partners/the-chronicle-journal.svg';
import StarkvilleDailyNews from '@img/partners/starkville-daily-news.svg';
import TheGlobeAndMail from '@img/partners/the-globe-and-mail.svg';
import Minyanville from '@img/partners/minyanville.svg';
import Barchart from '@img/partners/barchart.svg';
import Benzinga from '@img/partners/benzinga.svg';
import Fox from '@img/partners/fox.svg';
import Ncn from '@img/partners/ncn.svg';

const link1 =
  'https://www.theglobeandmail.com/investing/markets/markets-news/GetNews/26891352/pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-aipowered-blogging-solution/';
const link2 =
  'https://www.benzinga.com/pressreleases/24/06/ab39382245/pixelshadow-inc-announces-the-release-of-blogify-4-0-the-ultimate-ai-powered-blogging-solution';
const link3 = '#';
const link4 =
  'https://www.newschannelnebraska.com/story/50917056/pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-aipowered-blogging-solution';
const link5 =
  'https://www.barchart.com/story/news/26891346/pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-aipowered-blogging-solution';
const link6 =
  'https://finance.minyanville.com/minyanville/article/getnews-2024-6-18-pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-ai-powered-blogging-solution';
const link7 =
  'https://business.starkvilledailynews.com/starkvilledailynews/markets/article/getnews-2024-6-18-pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-ai-powered-blogging-solution/';
const link8 =
  'https://markets.chroniclejournal.com/chroniclejournal/article/getnews-2024-6-18-pixelshadow-inc-announces-the-release-of-blogify-40-the-ultimate-ai-powered-blogging-solution/';

const badges: { name: string; logo: StaticImageData; link: string }[] = [
  { name: 'The Globe & Mail', logo: TheGlobeAndMail, link: link1 },
  { name: 'Benzinga', logo: Benzinga, link: link2 },
  { name: 'Fox 40', logo: Fox, link: link3 },
  { name: 'New Channel Nebraska', logo: Ncn, link: link4 },
  { name: 'Barchart', logo: Barchart, link: link5 },
  { name: 'Minyanville', logo: Minyanville, link: link6 },
  { name: 'Starkville Daily News', logo: StarkvilleDailyNews, link: link7 },
  { name: 'The Chronicle Journal', logo: TheChronicleJournal, link: link8 },
];

export default function HomeTrustBadge() {
  const [emblaRef] = useEmblaCarousel({ loop: true, align: 'start' }, [AutoScroll({ speed: 1 })]);

  return (
    <section
      id="blogify-trust-badge"
      aria-label="Blogify Trust Badge"
      className="mx-auto max-w-7xl px-6 pb-40 pt-10"
    >
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex items-center gap-8">
          {badges.map((badge, index) => {
            const content = (
              <Image
                src={badge.logo}
                alt={`${badge.name} Logo`}
                height={40}
                loading="lazy"
                className="brightness-0 invert"
              />
            );

            return (
              <div
                className="shrink-0 px-8"
                key={index}
                // style={{ flexBasis: '160px' }} // Approx. ~5 logos on large screens
              >
                {badge.link === '#' ? (
                  <div className="flex cursor-default items-center justify-center">{content}</div>
                ) : (
                  <a
                    href={badge.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center"
                  >
                    {content}
                  </a>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
