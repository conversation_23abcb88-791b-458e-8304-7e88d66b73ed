'use client';

import type { StaticImageData } from 'next/image';

import { useState, useMemo } from 'react';
import { TypeAnimation } from 'react-type-animation';
import { BsFire } from 'react-icons/bs';
import { FaPlay } from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';

import {
  DialogDescription,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Dialog,
} from '@ps/ui/components/dialog';
import { VisuallyHidden } from '@ps/ui';
import { INTEGRATIONS } from '@/constants/integrations';
import { Button } from '@ps/ui/components/button';
import useSettings from '@/providers/settings';

import VideoPreview from '@img/page-assets/home/<USER>';
import Stars from '@img/icons/stars.svg';

export default function HomeIntro() {
  const { subscriptionTrialPeriod, isFreePlanEnabled } = useSettings();

  return (
    <section
      className="mx-auto flex max-w-7xl flex-wrap justify-between gap-16 px-6 py-16 lg:flex-nowrap lg:py-32"
      aria-label="Introduction to Blogify"
    >
      <div className="w-full lg:w-1/2">
        <div className="flex w-max items-center gap-2 rounded-lg bg-blur px-3 py-1.5 backdrop-blur-2xl">
          <BsFire />{' '}
          <span className="text-xs font-medium">What&apos;s New: YouTube Connect Pro!</span>
        </div>

        <HeroSectionTitle />

        <p className="mt-4 text-typo-secondary">
          Create SEO-optimized blogs in minutes with Blogify, the best AI for writing blogs. Easily
          convert YouTube videos, podcasts, or files into blogs and boost productivity using the
          best AI for content writing.
        </p>

        <div className="mt-10 flex items-center gap-5">
          <Link href={isFreePlanEnabled ? 'signup?plan=free&period=monthly' : '#pricing-table'}>
            <Button variant="glow" className="h-10 whitespace-nowrap">
              {isFreePlanEnabled
                ? 'Get Started for FREE!'
                : subscriptionTrialPeriod
                  ? `Start ${subscriptionTrialPeriod}-Days Free Trial.`
                  : 'View Pricing'}
            </Button>
          </Link>

          {(isFreePlanEnabled || !!subscriptionTrialPeriod) && (
            <p className="text-sm font-medium text-typo-secondary">
              {isFreePlanEnabled ? '**No Credit Card Required' : `**Cancel Anytime.`}
            </p>
          )}
        </div>
      </div>

      <div className="w-full lg:w-1/2">
        <Dialog>
          <DialogTrigger asChild>
            <button className="relative w-full cursor-pointer">
              <Image
                alt="Blogify demo video preview"
                className="w-full"
                src={VideoPreview}
                priority
              />
              <div className="absolute left-[calc(50%-44px)] top-[calc(50%-44px)] flex flex-center">
                <div className="flex size-[88px] animate-ping rounded-full bg-primary/20 flex-center" />
                <div className="absolute flex flex-center">
                  <div className="absolute flex size-[64px] animate-ping rounded-full bg-primary/15 flex-center" />
                  <div className="absolute flex size-[60px] rounded-full bg-primary flex-center">
                    <FaPlay className="ml-1" />
                  </div>
                </div>
              </div>
            </button>
          </DialogTrigger>

          <DialogContent className="max-w-7xl border-none p-0">
            <VisuallyHidden>
              <DialogHeader>
                <DialogTitle>Blogify Demo Video</DialogTitle>
                <DialogDescription>
                  Watch the full demo video to learn more about blogify.
                </DialogDescription>
              </DialogHeader>
            </VisuallyHidden>

            <iframe
              className="aspect-video"
              src="https://www.youtube.com/embed/Nzzu0rx0M1Y?autoplay=1"
              title="Demo Video"
              allow="autoplay *"
              allowFullScreen
              width="100%"
            ></iframe>
          </DialogContent>
        </Dialog>
      </div>
    </section>
  );
}

const texts: { name: string; logo: StaticImageData }[] = [
  ...INTEGRATIONS,
  { name: 'Text Prompt', logo: Stars },
];
const HeroSectionTitle = () => {
  const [current, setCurrent] = useState(texts.length - 1);

  const sequence = useMemo(
    () =>
      texts
        .map((t, i) => [
          t.name,
          1000,
          () => {
            setTimeout(() => setCurrent(i), 500);
          },
        ])
        .flat(),
    [],
  );

  return (
    <h1 className="mt-8 text-3xl font-bold lg:text-5xl">
      <span>Turn </span>
      <div className="inline-flex items-center gap-2">
        <Image
          className="size-5 object-contain lg:size-8"
          alt={`${texts[current === texts.length - 1 ? 0 : current + 1]?.name} Icon`}
          src={texts[current === texts.length - 1 ? 0 : current + 1]?.logo || ''}
          height={20}
          width={20}
        />
        <TypeAnimation
          className="inline-block"
          preRenderFirstString
          sequence={sequence}
          repeat={Infinity}
          cursor={false}
          wrapper="span"
          speed={20}
        />
      </div>
      <div>into a blog</div>
    </h1>
  );
};
