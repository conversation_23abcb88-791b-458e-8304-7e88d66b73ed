'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

import { Button } from '@ps/ui/components/button';
import useSettings from '@/providers/settings';

import SplashPublish from '@img/page-assets/home/<USER>';
import ApplePodcast from '@img/page-assets/home/<USER>/Amazon.svg';
import iHeartRadio from '@img/page-assets/home/<USER>/iHeartradio.svg';
import SplashMoney from '@img/page-assets/home/<USER>';
import SplashArrow from '@img/page-assets/home/<USER>';
import YoutubeIcon from '@img/page-assets/home/<USER>/YouTube.svg';
import BgDownGrad from '@img/page-assets/home/<USER>';
import SplashBlog from '@img/page-assets/home/<USER>';
import Facebook from '@img/page-assets/home/<USER>/Facebook.svg';
import Shopify from '@img/page-assets/home/<USER>/Shopify.svg';
import YouTube from '@img/page-assets/home/<USER>/YouTube.svg';
import Reddit from '@img/page-assets/home/<USER>/Reddit.svg';
import TikTok from '@img/page-assets/home/<USER>/TikTok.svg';
import Quora from '@img/page-assets/home/<USER>/Quora.svg';
import Fire from '@img/page-assets/home/<USER>';
import Nike from '@img/page-assets/home/<USER>/Nike.svg';
import Doc from '@img/page-assets/home/<USER>/Doc.svg';
import TED from '@img/page-assets/home/<USER>/TED.svg';

const texts = [
  'Earn money with affiliate monetization',
  'DNS powered blog publishing',
  'SEO score & optimization',
  'AI generated charts & graph',
];

const icons = [
  YoutubeIcon,
  ApplePodcast,
  Doc,
  Facebook,
  iHeartRadio,
  Nike,
  YouTube,
  Quora,
  Reddit,
  Shopify,
  TED,
  TikTok,
];

export default function HomeIntro2() {
  const { subscriptionTrialPeriod, isFreePlanEnabled } = useSettings();
  const [shouldPlayVideo, playVideo] = useState(false);
  const [currentText, setCurrentText] = useState(0);
  const [currentIcon, setCurrentIcon] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const [fade, setFade] = useState(true);

  useEffect(() => {
    const iconInterval = setInterval(() => {
      setTimeout(() => {
        setCurrentIcon((prev) => (prev + 1) % icons.length);
        setFade(true);
      }, 500);
    }, 1000);

    return () => clearInterval(iconInterval);
  }, []);

  useEffect(() => {
    const textInterval = setInterval(() => {
      setTimeout(() => {
        setCurrentText((prev) => (prev + 1) % texts.length);
        setFade(true);
      }, 500);
    }, 2000);

    return () => clearInterval(textInterval);
  }, []);

  useEffect(() => {
    let timeout: ReturnType<typeof setTimeout>;

    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => playVideo(true));
    } else {
      timeout = setTimeout(() => playVideo(true), 2000);
    }

    return () => timeout && clearTimeout(timeout);
  }, []);

  return (
    <section
      className="mx-auto flex max-w-7xl flex-col gap-16 px-6 py-16 lg:py-16"
      aria-label="Introduction to Blogify"
    >
      <div className="relative mb-8 w-full px-0 sm:px-6 lg:px-0">
        <div className="flex w-full items-center gap-2 rounded-lg bg-blur px-3 py-1.5 backdrop-blur-2xl sm:w-max">
          <Image src={Fire} alt="arrow-icon" className="w-4 sm:w-4" />
          <span className="text-xs font-medium">
            <span>What&apos;s New :</span>{' '}
            <span
              className={`transition-opacity duration-500 ${fade ? 'opacity-100' : 'opacity-0'}`}
            >
              {texts[currentText]}
            </span>
          </span>
        </div>

        <div className="relative text-center sm:text-left">
          <h1 className="mt-6 text-2xl font-bold leading-tight sm:text-3xl md:text-4xl lg:text-5xl">
            Automate your <br />
            <span className="inline-flex items-center gap-2">
              blog creation
              <Image
                src={icons[currentIcon]}
                alt="dynamic-icon"
                className={`w-6 transition-opacity duration-500 sm:w-16 ${fade ? 'opacity-100' : 'opacity-0'}`}
              />
              <Image src={SplashArrow} alt="arrow-icon" className="w-5 sm:w-7" />
              <Image src={SplashBlog} alt="blog-icon" className="w-5 sm:w-16" />
            </span>
            <span className="inline-flex items-center gap-2">, publication</span>
            <br />
            <span className="inline-flex items-center gap-1 text-primary">
              {' '}
              & affiliate-monetization{' '}
              <Image src={SplashMoney} alt="blog-icon" className="w-5 sm:w-16" />
            </span>
          </h1>
          <Image
            src={SplashPublish}
            alt="rocket-icon"
            className="absolute right-96 top-4 hidden size-16 sm:block sm:size-24"
          />
        </div>
        <p className="mt-4 max-w-full text-center text-sm text-white sm:max-w-3xl sm:text-left sm:text-base">
          Automatically repurpose 40+ sources into blogs in 150+ languages, publish on any platform,
          <br className="hidden sm:block" />
          and earn money using our advanced AI-powered affiliate monetization.
        </p>

        <div className="mt-10 inline-flex w-full flex-col items-center gap-5 sm:flex-row">
          <Link href={isFreePlanEnabled ? 'signup?plan=free&period=monthly' : '#pricing-table'}>
            <Button variant="glow" className="h-10 whitespace-nowrap">
              {isFreePlanEnabled
                ? 'Get Started for FREE!'
                : subscriptionTrialPeriod
                  ? `Start ${subscriptionTrialPeriod}-Days Free Trial.`
                  : 'View Pricing'}
            </Button>
          </Link>
          {(isFreePlanEnabled || !!subscriptionTrialPeriod) && (
            <p className="mt-2 text-center text-sm font-medium text-typo-secondary sm:mt-0">
              {isFreePlanEnabled ? '**No Credit Card Required' : `**Cancel Anytime.`}
            </p>
          )}
        </div>
      </div>

      {/* Video Section */}
      <div className="relative z-30 max-w-7xl rounded-[12px] p-[10px]">
        <div className="aspect-video w-full rounded-2xl bg-[#282828]">
          {shouldPlayVideo && (
            <iframe
              className="aspect-video w-full rounded-2xl"
              src={`https://www.youtube-nocookie.com/embed/beOFeg0BArU?autoplay=1&mute=${isMuted ? 1 : 0}&controls=0&modestbranding=1&rel=0&playsinline=1`}
              title="Demo Video"
              allow="autoplay"
              allowFullScreen
              loading="lazy"
            />
          )}

          {isMuted && (
            <button
              className="absolute right-5 top-5 rounded-md bg-black/70 px-4 py-2 text-sm font-semibold text-white"
              onClick={() => setIsMuted(false)}
            >
              🔊 Tap to Unmute
            </button>
          )}
        </div>

        <div className="absolute bottom-[-72px] left-0 hidden w-full justify-start p-6 md:flex">
          <Image src={BgDownGrad} alt="bg-down" className="size-auto" />
        </div>
      </div>
    </section>
  );
}
