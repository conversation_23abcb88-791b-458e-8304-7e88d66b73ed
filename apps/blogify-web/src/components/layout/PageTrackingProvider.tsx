'use client';

import { useEffect } from 'react';

import { trackUTMCampaign } from '@/services/analytics/google-analytics';

export default function PageTrackingProvider() {
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const utm_source = params.get('utm_source');
    const utm_campaign = params.get('utm_campaign');

    console.log('UTM Params:', { utm_source, utm_campaign }); // <-- Add this log

    if (utm_source || utm_campaign) {
      trackUTMCampaign(utm_source ?? undefined, utm_campaign ?? undefined);
      console.log('trackUTMCampaign called'); // <-- And this one
    }
  }, []);

  return null;
}
