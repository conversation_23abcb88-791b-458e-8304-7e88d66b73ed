# Blogify Server

[![Build and Deploy to GKE](https://github.com/Blogify-ai/blogify/actions/workflows/build-and-deploy.yml/badge.svg)](https://github.com/Blogify-ai/blogify/actions/workflows/build-and-deploy.yml)

![Blogify](https://github.com/user-attachments/assets/52188060-7be0-4a4c-8d3a-37670d99b1b3)

## Table of Contents

- [Introduction](#introduction)
- [Architecture](#architecture)
- [Libraries and Frameworks](#libraries-and-frameworks)
- [Models](#models)
- [Installation](#installation)
- [Running the Server](#running-the-server)
- [Blog Generation Pipeline](#blog-generation-pipeline)
- [NestJS REPL](#nestjs-repl)
- [NestJS Debug](#nestjs-debug)
- [NestJS Logging](#nestjs-logging)

## Introduction

Welcome to the Blogify Server codebase! This README is designed to help you get started with the project, understand its architecture, and learn about the important libraries and frameworks used.

## Architecture

This project follows the [12-factor app](https://12factor.net/) design and [Service-Oriented Architecture (SOA)](https://en.wikipedia.org/wiki/Service-oriented_architecture) principles. It is built using [NestJS](https://nestjs.com/) and [TypeScript](https://www.typescriptlang.org/) and hosted on [Kubernetes](https://cloud.google.com/kubernetes-engine).

## Libraries and Frameworks

- **[pnpm](https://pnpm.io/)**: A fast, disk space-efficient package manager.
- **[NestJS](https://nestjs.com/)**: A progressive Node.js framework for building efficient, reliable, and scalable server-side applications.
- **[MongoDB](https://www.mongodb.com/)**: A source-available cross-platform document-oriented NoSQL database program.
- **[Mongoose](https://mongoosejs.com/)**: An elegant MongoDB object modeling tool for Node.js.
- **[Redis](https://redis.io/)**: An in-memory data structure store, used as a database, cache, and message broker.
- **[BullMQ](https://docs.bullmq.io/)**: A Node.js library for handling jobs and messages in a queue.
- **[ESLint](https://eslint.org/)**: A tool for identifying and reporting on patterns found in ECMAScript/JavaScript code, with the goal of making code more consistent and avoiding bugs.

## Models

### [Blog Model](https://github.com/Blogify-ai/blogify/blob/develop/src/blog/blog.model.ts)

A MongoDB document collection which houses important information regarding blogs such as their input configuration, LLm generated text as well as publish information.

### [Business Model](https://github.com/Blogify-ai/blogify/blob/develop/src/business/business.model.ts)

Each user belongs to an organization/team/company (aka `business` in the codebase) and a single business can be composed of multiple users. This MongoDB collection contains important company data such as credits and access tokens for various external platforms.

### [User Model](https://github.com/Blogify-ai/blogify/blob/develop/src/user/user.model.ts)

A MongoDB collection which houses user-specific information such as name, avatar etc alongside their Blogify credentials (i.e. login email and password) etc.

## Installation

To install the necessary packages, use the following command:

```sh
pnpm install
```

## Running the Server

### Pre-requisites

- `.env` file
- _pnpm_ installation (latest)
- _Node.js_ installation (latest)
- _Locally_ running Redis Server
- _Locallly_ running MongoDB Server

### Execution

Use the following command to start the server:

```sh
pnpm dev
```

Thereafter, you can visit `http://localhost:7777/` to check if your server is running locally.

## Blog Generation Pipeline

Blogify uses a series of BullMQ queues to generate blogs. The journey begins at the `BLOG_REQUEST` queue and proceeds thereforth. Each queue carries out a specific task and upon completion activates the next queue until completion.

```mermaid
graph TD;
    BlogReq[BLOG_REQUEST] -->|audio/video content| Trans[TRANSCRIBE_MEDIA]
    Trans --> TransSum[GENERATE_TRANSCRIPT_SUMMARY]
    TransSum --> Outline[GENERATE_BLOG_OUTLINE]
    BlogReq -->|text content| Outline
    
    Outline --> SeoSearch[SEO_SEARCH_RELEVANT_CONTENT]
    SeoSearch --> KwAnalysis[SEO_ANALYZE_KEYWORD]
    KwAnalysis --> Content[GENERATE_BLOG_CONTENT]
    Content --> Keywords[GENERATE_BLOG_KEYWORDS]
    Keywords --> SeoScore[SEO_SCORING]

    SeoScore -->|affiliate enabled| AffKw[GENERATE_AFFILIATE_KEYWORDS]
    AffKw --> AffSearch[AFFILIATE_PRODUCT_SEARCH]
    AffSearch --> ImpSearch[IMPACT_PRODUCT_SEARCH]
    ImpSearch --> AffLinks[GENERATE_AFFILIATE_LINKS]
    
    SeoScore -->|no affiliates| Dispatch[DISPATCH_TO_PUBLISHERS]
    AffLinks --> Dispatch

    Dispatch --> Publish[PUBLISH_BLOG]

    subgraph Background Processes
        Credits[BLOG_CREDITS]
        AffMonitor[AFFILIATE_SALE_MONITORING]
    end
```

Note: The diagram shows the main blog generation pipeline. There are also background processors like `BLOG_CREDITS` for managing credit allocation and `AFFILIATE_SALE_MONITORING` for tracking affiliate sales conversions.

## NestJS REPL

[NestJS REPL](https://docs.nestjs.com/recipes/repl) is an interactive Read-Eval-Print Loop for NestJS applications. It allows you to interact with your NestJS application in real-time. To start the REPL, use the following command:

```sh
pnpm start:repl
```

## NestJS Debug

NestJS Debug is a tool for debugging NestJS applications. It provides detailed information about the application's execution. You can set breakpoints at various points among others and observe the values of different runtime variables easily. To start debugging, use the following command:

```sh
pnpm start:debug
```

## NestJS Logging

This codebase uses a custom implementation of NestJS's built-in Logger that provides structured logging compatible with Google Cloud Logging. The logger supports colored output in development and proper severity levels in production.

```ts
import { CustomNestLogger } from '@common/logger/logger';

// Create a logger instance with context
const logger = new CustomNestLogger('..Context');

// Available logging methods
logger.log('Info level message');
logger.error('Error message', stackTrace);
logger.warn('Warning message');
logger.debug('Debug message');
logger.verbose('Verbose message');

// Logging objects
logger.log({ key: 'value' }, 'Optional message');

// The logs will be formatted for both:
// 1. Local development (colored, human-readable)
// 2. Google Cloud Logging (structured JSON with proper severity levels)
```

Log levels and their use cases:

- `error`: For errors that need immediate attention
- `warn`: For potentially harmful situations
- `log`: For general information (alias for 'info')
- `debug`: For debugging information
- `verbose`: For detailed information about application flow

In production, logs are automatically formatted for Google Cloud Logging with proper severity levels and metadata.
