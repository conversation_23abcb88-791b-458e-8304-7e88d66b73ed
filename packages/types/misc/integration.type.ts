type MediaIntegration = 'spotify';

type BlogIntegration =
  | 'blogify'
  | 'wordpress'
  | 'wordpressorg'
  | 'blogger'
  | 'wix'
  | 'medium'
  | 'mailchimp'
  | 'zapier';

type SocialIntegration = 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'youtube';

type Integration = MediaIntegration | BlogIntegration | SocialIntegration;

type IntegrationSite = {
  name: string;
  url: string;
  id: string;
  author?: Record<'name' | 'id', string>[];
  category?: Record<'name' | 'id', string>[];
};

export type IntegrationDetail = Readonly<{
  kind: 'blog' | 'social' | 'automation';
  name: string;
  website: string;
  description: string;
  theme: string;
  logoURL: string;
  drafts: boolean;
  sites?: IntegrationSite[];
  tutorial?: string;
  connected: boolean;
  display: boolean;
}>;

export type { MediaIntegration, BlogIntegration, SocialIntegration, Integration, IntegrationSite };
