import type { Base } from './base.type';

export interface Product extends Base {
  subscriptionId: string;
  name: string;
  description: string;
  imageUrl: string;
  videoUrl: string;
  price: number;
  trialPeriod: number;
  productUrl: string;
  learnMoreUrl: string;

  // Client Only
  isActive: boolean;
  href: string;
}

const emptyProduct: Product = {
  _id: '',
  subscriptionId: '',
  name: '',
  description: '',
  imageUrl: 'https://placehold.co/312x176',
  videoUrl: '',
  price: 0,
  trialPeriod: 0,
  productUrl: '',
  learnMoreUrl: '',

  // Client Only
  isActive: false,
  href: '',
};

export enum Addon {
  Snippet = 'Writing Snippets',
  YouTube = 'YouTube Connect',
  YouTubePro = 'YouTube Connect Pro',
}

export default emptyProduct;
