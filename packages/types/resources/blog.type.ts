import type { BlogIntegration } from '../misc/integration.type';
import type { Base } from './base.type';
import type { BlogCategory } from './blog-category';
import type { ImageRequest } from './image.type';
import type { User } from './user.type';

export type BlogSourceType =
  | 'image'
  | 'audio'
  | 'video'
  | 'document'
  | 'prompt'
  | 'e-commerce'
  | 'webLink';

export type BlogSourceName =
  // Video
  | 'YouTube'
  | 'Vimeo'
  | 'Rumble'
  | 'TikTok'
  | 'Facebook'
  | 'X'
  | 'DailyMotion'
  | 'TED'
  | 'Video File'
  | 'Video Link'

  // Audio
  | 'Apple Podcasts'
  | 'CastBox'
  | 'Podchaser'
  | 'PodBean'
  | 'PodcastAddict'
  | 'SoundCloud'
  | 'IHeart'
  | 'TuneIn'
  | 'Audio File'
  | 'Audio Link'

  // Image
  | 'Instagram'
  | 'Pinterest'
  | 'Flickr'
  | 'Getty Images'
  | 'Image File'
  | 'Image Link'

  // Prompt
  | 'Prompt'

  // Document
  | 'Google Docs'
  | 'Notion'
  | 'Document File'
  | 'Document Link'

  // Web Link - eCommerce
  | 'Amazon'
  | 'Shopify'
  | 'Etsy'
  | 'eBay'
  | 'Ikea'
  | 'Nike'
  | 'FlipCart'
  | 'Walmart'
  | 'e-commerce page'
  | 'Web Link'

  // Web Link - Webpage
  | 'Blogger'
  | 'BBC'
  | 'Verge'
  | 'Apple'
  | 'Reddit'
  | 'Quora'
  | 'StackOverflow'
  | 'XDA';

export type BlogStatus =
  | 'queued'
  | 'request_dispatched'
  | 'request_dispatching'
  | 'failed'
  | 'transcribing'
  | 'transcribed'
  | 'transcription_failed'
  | 'summarizing'
  | 'summarized'
  | 'summarization_failed'
  | 'outline_generating'
  | 'outline_generated'
  | 'outline_generation_failed'
  | 'content_generating'
  | 'content_generated'
  | 'content_generation_failed'
  | 'keywords_generating'
  | 'keywords_generated'
  | 'keywords_generation_failed'
  | 'publisher_dispatching'
  | 'publisher_dispatched'
  | 'publisher_dispatching_failed'
  | 'blog_publishing'
  | 'blog_published'
  | 'blog_publication_failed'
  | 'social_publishing'
  | 'social_published'
  | 'social_publication_failed'
  | 'completed';

export type BlogSeoAnalysisStatus =
  | 'seo_search_content_inprogress'
  | 'seo_search_content_completed'
  | 'seo_search_content_failed'
  | 'seo_keyword_analysis_inprogress'
  | 'seo_keyword_analysis_completed'
  | 'seo_keyword_analysis_failed';

export type BlogAffiliateLinkGenerationStatus =
  | 'affiliate_keywords_generating'
  | 'affiliate_keywords_generated'
  | 'affiliate_keywords_failed'
  | 'affiliate_product_matching'
  | 'affiliate_product_matched'
  | 'affiliate_product_matching_failed'
  | 'affiliate_link_generating'
  | 'affiliate_link_generated'
  | 'affiliate_link_generation_failed';

export interface ThirdPartyIntegration<T> {
  platform: T;
  timestamp: string;
  draft: boolean;
  sites?: Array<{ site: string; author?: string; category?: string[] }>;
  blogPlatform?: string;
}

export interface BlogHeading {
  id: string;
  text: string;
  level: string;
}

export interface AffiliateKeyword extends Base {
  keyword: string;
  beforeWord: string;
  afterWord: string;
  confidence: number;
  productName: string;
  productId: string;
  productCatalogId: string;
  productCampaignId: string;
  productCatalogItemId: string;
  productImageUrl: string;
  productCurrentPrice: number;
  productOriginalPrice: number;
  productCurrency: string;
  productCategory: string;
  productItemGroupId: string;
  affiliateLink: string;
  searchTerms: string[];
  status: string;
  matchedAt: string;
  replacementCount: number;
}

export interface Blog extends Base {
  uid?: { _id: string; name: string; profilePicture: string };
  bid: string;
  url: string;
  prompt: string;
  customInstruction?: string;
  title: string;
  slug: string;
  image: string;
  thumbnail?: string;
  sourceType: BlogSourceType;
  sourceName: BlogSourceName;
  tldrPosition: 'start' | 'end' | '';
  generationMode: 'auto' | 'assisted';
  contentImages: string[];
  contentImagesPlaceholder?: string[];
  content: string;
  cta: {
    text: string;
    bgColor: string;
    borderRadius: number;
    link: string;
  };
  analytics?: {
    actions: number;
    views: number;
    clicks: number;
    earnings: number;
  };
  publishAt: string | Date;
  publishTime: string | Date;
  platforms: ThirdPartyIntegration<BlogIntegration>[];
  publishRatings: Record<'bad' | 'ok' | 'nice' | 'great' | 'awesome', number>;
  publishResult?: {
    integration: string;
    siteID?: string;
    visibility?: 'visible' | 'hidden';
    outcome: { link: string; time: string } | { error: string; time: string };
  }[];
  socials: Array<{
    platform: 'x' | 'linkedin';
    timestamp: string;
    text: string;
    outcome?: { link: string; time: string } | { error: string; time: string };
  }>;

  embedSource: boolean;
  embedSourceAsCover: boolean;
  affiliateCommissionOptIn: boolean;
  affiliateTargetLocation: string;
  includeQuotation?: boolean;

  authorsToGiveCredit?: string[];

  blogSize: 'small' | 'medium' | 'large' | 'x-large' | string;
  blogTone:
    | 'Neutral'
    | 'Engaging'
    | 'Inspirational'
    | 'Informative'
    | 'Professional'
    | 'Conversational'
    | 'Promotional'
    | 'Storytelling'
    | 'Educational'
    | 'News'
    | string;

  pov: 'First Person' | 'Second Person' | 'Third Person' | string;

  seoOption: 'ACTIVE' | 'PASSIVE';
  seoInputKeywords: string[];
  wordCountApprox: number;
  sourceSimilarity: number;
  inputLanguage: string;
  blogLanguage: string;

  tableOption: string;
  chartOption: string;

  rating: number;
  ratingFeedback?: string;

  publishStatus: 'draft' | 'scheduled' | 'published';

  categories: string[] | BlogCategory[];

  blogifyLink: string;
  wordpressLink: string;
  wordpressorgLink: string;
  zapierLink: string;
  bloggerLink: string;
  mailchimpLink: string;
  wixLink: string;
  mediumLink: string;
  facebookLink: string;
  instagramLink: string;
  twitterLink: string;
  linkedinLink: string;
  youtubeLink: string;
  spotifyLink: string;

  blogifyPublishTime: string;
  wordpressPublishTime: string;
  wordpressorgPublishTime: string;
  zapierPublishTime: string;
  bloggerPublishTime: string;
  mailchimpPublishTime: string;
  wixPublishTime: string;
  mediumPublishTime: string;
  facebookPublishTime: string;
  instagramPublishTime: string;
  twitterPublishTime: string;
  linkedinPublishTime: string;
  youtubePublishTime: string;
  spotifyPublishTime: string;

  blogOutline: {
    title: string;
    summary?: string;
    introduction: string;
    tldr?: string;
    sections: {
      heading: string;
      bulletPoints: string[];
    }[];
  };

  totalViews: number;
  totalClicks: number;

  transcription?: string;
  transcriptionSummary?: string;
  metaDescription?: string;
  keywords?: string[];
  hashtags?: string[];
  status: BlogStatus;
  seoAnalysisStatus?: BlogSeoAnalysisStatus;
  affiliateLinkGenerationStatus?: BlogAffiliateLinkGenerationStatus;
  failReason: string;
  errorCode: string;
  wordCount: number;
  isPartOfBulkGeneration?: boolean;
  tableOfContents?: BlogHeading[];

  // Relation
  user?: User;

  // Client Only
  publishDate: string;
  mediaFile?: string;
}

export type BlogCreate = Pick<
  Blog,
  | 'prompt'
  | 'customInstruction'
  | 'sourceType'
  | 'sourceName'
  | 'generationMode'
  | 'pov'
  | 'blogTone'
  | 'blogSize'
  | 'seoOption'
  | 'seoInputKeywords'
  | 'wordCountApprox'
  | 'sourceSimilarity'
  | 'inputLanguage'
  | 'blogLanguage'
  | 'tldrPosition'
  | 'cta'
  | 'embedSource'
  | 'embedSourceAsCover'
  | 'affiliateCommissionOptIn'
  | 'affiliateTargetLocation'
  | 'authorsToGiveCredit'
  | 'tableOption'
  | 'chartOption'
  | 'image'
  | 'contentImages'
  | 'platforms'
  | 'publishAt'
  | 'isPartOfBulkGeneration'
> &
  Partial<Pick<Blog, 'url' | 'title'>> & {
    isBulk: false;
    youtubeVideoId?: string;
    isYoutubeBulk?: boolean;
    clipTimeSpan?: string;
    urls?: string[];
    includeQuotation?: boolean;
    coverImageType?: 'thumbnail' | 'ai-generated';
    aiGeneratedCoverImageConfig?: Omit<ImageRequest, 'prompt'> & { count: number; cost: number };
    aiGeneratedContentImagesConfig?: Omit<ImageRequest, 'prompt'> & { count: number; cost: number };
  };

export type BlogBulkCreate = {
  isBulk: true;
  sources: (Pick<
    Blog,
    | 'url'
    | 'title'
    | 'image'
    | 'sourceType'
    | 'sourceName'
    | 'inputLanguage'
    | 'embedSource'
    | 'authorsToGiveCredit'
    | 'platforms'
  > & {
    urlState?: 'validating' | 'valid' | 'invalid' | undefined;
  })[];
  coverImageType?: 'thumbnail' | 'ai-generated';
  aiGeneratedCoverImageConfig?: Omit<ImageRequest, 'prompt'> & { count: number; cost: number };
  aiGeneratedContentImagesConfig?: Omit<ImageRequest, 'prompt'> & { count: number; cost: number };
} & Pick<
  Blog,
  | 'generationMode'
  | 'pov'
  | 'blogTone'
  | 'blogSize'
  | 'wordCountApprox'
  | 'sourceSimilarity'
  | 'blogLanguage'
  | 'tldrPosition'
  | 'cta'
  | 'tableOption'
  | 'chartOption'
  | 'affiliateCommissionOptIn'
  | 'affiliateTargetLocation'
  | 'publishAt'
  | 'platforms'
>;

export type BlogSource = {
  iconUrl: string;
  name: BlogSourceName;
  displayName: BlogSourceName | string;
  title: string;
  description: string;
  footer: string;
  type: BlogSourceType;
  inputType: 'url' | 'textarea' | 'upload';
  status: 'active' | 'inactive' | 'unlisted';
  // Flags
  isInactive?: boolean;
  isEmbeddable?: boolean;
  availableOnTrial?: boolean;
  availableOnFree?: boolean;
};

export type BlogEvents =
  | 'BLOG_STATUS_UPDATE'
  | 'BLOG_SEO_ANALYSIS_STATUS_UPDATE'
  | 'BLOG_AFFILIATE_LINK_GENERATION_STATUS_UPDATE';

export const emptyBlog: Blog = {
  _id: '',
  uid: { _id: '', name: '', profilePicture: '' },
  bid: '',
  url: '',
  prompt: '',
  title: '',
  slug: '',
  image: '',
  sourceType: 'video',
  sourceName: 'YouTube',
  generationMode: 'auto',
  tldrPosition: 'end',
  contentImages: [],
  contentImagesPlaceholder: [],
  content: '',
  cta: {
    text: '',
    bgColor: '',
    borderRadius: 0,
    link: '',
  },
  publishAt: '',
  publishTime: '',
  platforms: [],
  socials: [],
  publishRatings: {
    bad: 0,
    ok: 0,
    nice: 0,
    great: 0,
    awesome: 0,
  },
  rating: 0,

  embedSource: false,
  embedSourceAsCover: false,
  affiliateCommissionOptIn: false,
  affiliateTargetLocation: 'USA',
  categories: [],

  blogSize: 'medium',
  seoOption: 'ACTIVE',
  seoInputKeywords: [],
  wordCountApprox: 2000,
  sourceSimilarity: 50,
  blogTone: 'Neutral',
  inputLanguage: 'en',
  blogLanguage: 'en',
  pov: 'First Person',

  authorsToGiveCredit: [],

  publishStatus: 'draft',

  blogifyLink: '',
  wordpressLink: '',
  wordpressorgLink: '',
  zapierLink: '',
  mailchimpLink: '',
  bloggerLink: '',
  wixLink: '',
  mediumLink: '',
  facebookLink: '',
  instagramLink: '',
  twitterLink: '',
  linkedinLink: '',
  youtubeLink: '',
  spotifyLink: '',

  blogifyPublishTime: '',
  wordpressPublishTime: '',
  wordpressorgPublishTime: '',
  zapierPublishTime: '',
  bloggerPublishTime: '',
  mailchimpPublishTime: '',
  wixPublishTime: '',
  mediumPublishTime: '',
  facebookPublishTime: '',
  instagramPublishTime: '',
  twitterPublishTime: '',
  linkedinPublishTime: '',
  youtubePublishTime: '',
  spotifyPublishTime: '',

  status: 'queued',
  failReason: '',
  errorCode: '',
  blogOutline: {
    title: '',
    introduction: '',
    sections: [],
  },

  totalViews: 0,
  totalClicks: 0,
  wordCount: 0,
  isPartOfBulkGeneration: false,
  tableOfContents: [],

  // Client Only
  publishDate: '',

  tableOption: 'No Table',
  chartOption: 'No Chart',
};
