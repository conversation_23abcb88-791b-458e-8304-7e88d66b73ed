import type { Base } from './base.type';

type TransactionType =
  // Debits
  | 'CONTENT_GENERATION_CHARGE'
  | 'MONTHLY_CREDITS_EXPIRATION'

  // Credits
  | 'CONTENT_GENERATION_CHARGE_REFUND'
  | 'MONTHLY_CREDITS_ALLOCATION'
  | 'ADDITIONAL_CREDIT_PURCHASE';

export interface CreditTransaction extends Base {
  amount: number;
  mode: 'debit' | 'credit';
  type: TransactionType;
  status: 'unconfirmed' | 'confirmed' | 'refunded';
  description?: string;
  contentType?: string;
  contentIds?: string[];
  monthlyCreditsUsed?: boolean;
  // Relations
  business: string;
  user?: string;
}
