import type { Product } from './product.type';
import type { Base } from './base.type';
import type { User } from './user.type';

import anonymousUser from './user.type';
import emptyProduct from './product.type';

export interface Transaction extends Base {
  amount: number;
  description: string;
  status: 'unconfirmed' | 'confirmed';
  paymentProvider: 'stripe';
  paymentMethod: 'card';
  paymentRef: string;
  paymentInvoiceUrl: string;
  subscriptionRef: string;
  subscriptionExpirationDate: string;
  // Relations
  product: Product;
  business: any;
  user: User;
  // Client Only
  coupon?: string;
}

const emptyTransaction: Transaction = {
  _id: '',
  amount: 0,
  description: '',
  status: 'unconfirmed',
  paymentProvider: 'stripe',
  paymentMethod: 'card',
  paymentRef: '',
  paymentInvoiceUrl: '',
  subscriptionRef: '',
  subscriptionExpirationDate: '',
  // Relations
  product: emptyProduct,
  business: {},
  user: anonymousUser,
  // Client Only
  coupon: '',
};

export default emptyTransaction;
