// بسم الله الرحمن الرحيم
/**
 * Decodes and parses a JSON Web Token (JWT).
 *
 * This function takes a JWT string and returns the decoded payload as an object.
 * It performs base64url decoding on the payload portion of the token and converts
 * it back to a JavaScript object.
 *
 * Source: {@link https://stackoverflow.com/a/38552302}
 * @param token - The JWT string to parse. Should be in the format 'header.payload.signature'
 * @returns The decoded payload as an object
 * @throws {Error} If the token is malformed or cannot be decoded
 *
 * @example
 * const token = "eyJhbG..."; // JWT token
 * const payload = parseJWT(token);
 * console.log(payload); // { sub: "1234567890", name: "<PERSON>", ... }
 */
export function decodeJWT(token: string) {
  var base64Url = token.split('.')[1];
  var base64 = base64Url!.replace(/-/g, '+').replace(/_/g, '/');
  var jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      })
      .join(''),
  );

  return JSON.parse(jsonPayload);
}
