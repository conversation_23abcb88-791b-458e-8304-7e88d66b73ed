export function getVideoIdFromYouTubeUrl(url: string): string {
  const youtubeUrl = url.replace(/^(?:https?:\/\/)?(?:www\.)?/i, '');

  const patterns = [
    /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([\w-]{11})/i,
    /youtube\.com\/live\/([\w-]{11})/i,
  ];

  const match = patterns.map((pattern) => youtubeUrl.match(pattern)).find(Boolean);
  return match?.[1] ? match[1] : '';
}

export function getVideoIdFromVimeoUrl(url: string): string | null {
  const regex =
    /(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com\/)(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|video\/|)(\d+)(?:$|\/|\?)/;

  const match = url.match(regex);
  return match && match[2] ? match[2] : null;
}

export function sanitizeUrl(url: string): string {
  return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
}
