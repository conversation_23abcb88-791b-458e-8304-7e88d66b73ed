import { getVideoIdFromYouTubeUrl, getVideoIdFromVimeoUrl } from '@ps/common/utils/url';

function getYouTubeEmbedCode(url: string, title: string, autoPlay?: boolean) {
  const videoId = getVideoIdFromYouTubeUrl(url);
  if (!videoId) return null;

  return (
    <iframe
      className="aspect-video w-full rounded-lg border-none"
      src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
      allow={`${autoPlay ? 'autoplay; ' : ''}accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture`}
      allowFullScreen
      title={title}
    />
  );
}

function getVimeoEmbedCode(url: string, title: string, autoPlay?: boolean) {
  const videoId = getVideoIdFromVimeoUrl(url);
  if (!videoId) return null;

  return (
    <iframe
      className="aspect-video w-full rounded-lg border-none"
      src={`https://player.vimeo.com/video/${videoId}?autoplay=1&title=0&byline=0&portrait=0`}
      allow={`${autoPlay ? 'autoplay; ' : ''}fullscreen; picture-in-picture`}
      title={title}
      allowFullScreen
    />
  );
}

function getRumbleEmbedCode(url: string, title: string, autoPlay?: boolean) {
  if (!url.includes('/embed/')) return null;

  return (
    <iframe
      className="aspect-video w-full rounded-lg border-none"
      allow={`${autoPlay ? 'autoplay; ' : ''}accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture`}
      allowFullScreen
      title={title}
      src={url}
    />
  );
}

export default function Embed({
  platform,
  url,
  title,
  autoPlay,
}: {
  platform: 'YouTube' | 'Vimeo' | 'Rumble';
  url: string;
  title: string;
  autoPlay?: boolean;
}) {
  switch (platform) {
    case 'YouTube':
      return getYouTubeEmbedCode(url, title, autoPlay);
    case 'Vimeo':
      return getVimeoEmbedCode(url, title, autoPlay);
    case 'Rumble':
      return getRumbleEmbedCode(url, title, autoPlay);
    default:
      return null;
  }
}
